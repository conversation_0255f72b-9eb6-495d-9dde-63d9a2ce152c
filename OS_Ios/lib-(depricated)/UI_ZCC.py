
################################################################
#                    UI_ZCC.py                                 #
#                                                              #
#      AUTHOR : SHRUTI KALRA(<EMAIL>)               #
#                                                              #
#                                                              #
################################################################
import os
import sys
from appium.webdriver.webdriver import WebDriver as AppiumDriver
#from appium import webdriver
import copy
import time
from datetime import datetime
from functools import wraps
import pyautogui
import allure
from common_lib.Constants import *
from common_lib.UI_Common import *
from appium.webdriver.common.mobileby import MobileBy


class ios_elements:
    APP="com.zscaler.zscaler"
    SAFARI_APP="com.apple.mobilesafari"
    USERNAME = "//XCUIElementTypeTextField[@value='Username']"
    PASSWORD = "//XCUIElementTypeSecureTextField[@value='Password']"
    LOGIN_BUTTON = "//XCUIElementTypeStaticText[@name='Login']"
    ACCEPT_BUTTON = "a//XCUIElementTypeStaticText[@name='Accept']"
    DECLINE_BUTTON = "//XCUIElementTypeStaticText[@name='Decline']"
    CANCEL_BUTTON = "//XCUIElementTypeStaticText[@name='Cancel']"
    ICON_BACK = "//XCUIElementTypeButton[@name='icon back']"
    HAMBURGER= "//XCUIElementTypeButton[@name='menu icon hamburger']"
    AUTHENTICATING_VIA_BROWSER = "//XCUIElementTypeStaticText[@name='Authenticating User via Browser'']"
    VPN_ALERT = "//XCUIElementTypeStaticText[@name='“Zscaler” Would Like to Add VPN Configurations']"
    VPN_ALERT_ALLOW = "//XCUIElementTypeButton[@name='Allow']"
    VPN_ALERT_DO_NOT_ALLOW = "//XCUIElementTypeButton[@name='Don’t Allow']"
    LOGOUT_BUTTON = "//XCUIElementTypeButton[@name='logoutNew']"
    LOGOUT_OK = "//XCUIElementTypeButton[@name='OK']"
    TURN_OFF_TAB = "//XCUIElementTypeStaticText[@name='TURN OFF']"
    TURN_ON_TAB = "//XCUIElementTypeStaticText[@name='TURN ON']"
    TURN_ON_STATUS ="//XCUIElementTypeStaticText[@name='ON']"
    TURN_OFF_STATUS ="//XCUIElementTypeStaticText[@name='OFF']"
    ZPA_ON_STATUS = "//XCUIElementTypeStaticText[@name='ON']"
    PRIVATE_ACCESS = "//XCUIElementTypeButton[@name='Private Access']"
    INTERNET_ACCESS = "//XCUIElementTypeButton[@name='Internet Security']"
    TURN_OFF_INTERNET_SECURITY = "//XCUIElementTypeStaticText[@name='Are you sure you want to turn off Internet Security?']"
    DISABLE_ZIA_PASSWORD = "//XCUIElementTypeSecureTextField[@value='Enter Password']"
    DISABLE_REASON = "//XCUIElementTypeStaticText[@name='Disable Reason(1-200)']"
    OK = "//XCUIElementTypeStaticText[@name='OK']"
    DISABLE_OK ="//XCUIElementTypeButton[@name='OK']"
    DISABLE_ZPA_REASON = "//XCUIElementTypeStaticText[@name='Are you sure you want to turn off private access?']"
    Authentication_Required="//XCUIElementTypeStaticText[@name='Authentication Required']"
    REAUTHENTICATE = "//XCUIElementTypeStaticText[@name='Reauthenticate']"
    NOTIFICATION = "//XCUIElementTypeButton[@name='Notifications']"
    ZIA_ON_NOTIFICATION = "//XCUIElementTypeStaticText[@name='Zscaler Client Connector Internet Security Turned On.'']"
    ZPA_ON_NOTIFICATION="//XCUIElementTypeStaticText[@name='Zscaler Client Connector Private Access Turned On.'']"
    MORE = "//XCUIElementTypeButton[@name='More']"
    TRUSTED_NETWORK = "//XCUIElementTypeStaticText[@name='Trusted Network']"
    OFF_TRUSTED_NETWORK = "//XCUIElementTypeStaticText[@name='Off Trusted Network']"
    PACKET_CAPTURE = "//XCUIElementTypeStaticText[@name='Start Packet Capture']"
    STOP_PACKET_CAPTURE = "//XCUIElementTypeStaticText[@name='Stop Packet Capture']"
    REPORT_AN_ISSUE = "//XCUIElementTypeStaticText[@name='Report An Issue']"
    NAME = "//XCUIElementTypeTextField[@value='Name']"
    SEND = "//XCUIElementTypeStaticText[@name='Send']"
    CANCEL = "//XCUIElementTypeStaticText[@name='Cancel']"
    RESTART_SERVICE = "//XCUIElementTypeStaticText[@name='Restart Service']"
    CONFIRM = "//XCUIElementTypeStaticText[@name='Confirm']"
    CONTINUE = "//XCUIElementTypeButton[@name='Continue']"
    EXPORT_LOGS = "//XCUIElementTypeStaticText[@name='Export Logs']"
    CLEAR_LOGS = "//XCUIElementTypeStaticText[@name='Clear Logs']"
    UPDATE_POLICY = "//XCUIElementTypeStaticText[@name='Update Policy']"
    LICENSE_AGREEMENT = "//XCUIElementTypeStaticText[@name='License Agreement']"
    CLOSE = "//XCUIElementTypeStaticText[@name='Close']"
    LOG_LEVEL = "//XCUIElementTypeStaticText[@name='Log Level']"
    DEBUG = "//XCUIElementTypeButton[@name='Debug']"
    INFO = "//XCUIElementTypeButton[@name='Info']"
    WARN = "//XCUIElementTypeButton[@name='Warn']"
    ERROR = "//XCUIElementTypeButton[@name='Error']"
    CANCEL = "//XCUIElementTypeButton[@name='Cancel']"
    BYTES_SENT = "//XCUIElementTypeStaticText[@name='Bytes Sent']"
    BYTES_RECEIVED = "//XCUIElementTypeStaticText[@name='Bytes Received']"
    TUNNEL_VERSION = "//XCUIElementTypeStaticText[@name='Tunnel Version']"
    CLIENT_IP = "//XCUIElementTypeStaticText[@name='Client IP']"
    SERVER_IP = "//XCUIElementTypeStaticText[@name='Server IP']"
    #SERVER_IP_ADDRESS="//XCUIElementTypeStaticText[contains(@name, ":")]/@name"
    CAPTURING = "//XCUIElementTypeStaticText[@name='Capturing...']"
    OKTA_USERNAME = "//XCUIElementTypeOther[@name=\"main\"]/XCUIElementTypeOther[6]/XCUIElementTypeOther/XCUIElementTypeTextField"
    OKTA_PASSWORD ="//XCUIElementTypeOther[@name=\"main\"]/XCUIElementTypeOther[8]/XCUIElementTypeOther/XCUIElementTypeSecureTextField"
    SIGN_IN_TEXT = "//XCUIElementTypeStaticText[@name='Sign In']"
    OKTA_SIGN_IN ="//XCUIElementTypeButton[@name='Sign In']"
    #SIGN_IN_OKTA_PAGE="//XCUIElementTypeStaticText[@name='Signing in to beta']"
    SAFARI_SEARCH_BAR ="//XCUIElementTypeTextField[@name='TabBarItemTitle']"
    SAFARI_SEARCH_BAR_TEXT="//XCUIElementTypeApplication[@name='Safari']/XCUIElementTypeWindow[3]/XCUIElementTypeOther/XCUIElementTypeOther"
    SAFARI_TAB_OVERVIEW = "//XCUIElementTypeButton[@name='TabOverviewButton']"
    SAFARI_OPEN_NEW_TAB="//XCUIElementTypeOther[@name='CapsuleViewController']/XCUIElementTypeOther[1]"
    PROXY_VIA_ZSCALER="//XCUIElementTypeStaticText[@name='You are accessing the Internet via a Zscaler BETA proxy hosted Singapore IV in the zscalerbeta.net cloud.']"
    PROXY_NOT_VIA_ZSCALER ="//XCUIElementTypeStaticText[@name='The request received from you didn't come from a Zscaler IP therefore you are not going through the Zscaler proxy service.']"
    GO = "//XCUIElementTypeButton[@name='Go']"
    GOOGLE_SEARCH= "(//XCUIElementTypeStaticText[@name='Search or type URL'])[1]"
    CHROME_SEARCH_BAR="//XCUIElementTypeTextField[@name='Address']"
    #OPEN_NEW_TAB = "//XCUIElementTypeWindow[@name='0']/XCUIElementTypeOther[11]"
    #SEARCH_TOOL_BAR="(//XCUIElementTypeToolbar[@name='Toolbar'])[1]/XCUIElementTypeOther[1]/XCUIElementTypeOther[2]/XCUIElementTypeOther"
    CHROME_GO = "//XCUIElementTypeButton[@name='Go']"


class ZCC():
        def __init__(self,
                     handle: str ="easy",
                     startApp: bool =False,
                     logger: bool =None
                     ):
            self.logger = (logger if logger else CustomLogger.Initialize_Logger(Log_File_Name="ZCC.log", Log_Level="INFO"))
            
            
            self.UI = GUI(handle=handle,logger=self.logger)
            
            self.operating_system = self.UI.operating_system
            
            self.logger.info("Initialized ZCC GUI")
            #self.dynamic_element_check = dynamic_element_check
            self.startApp=startApp
            self.sleep_factor = 1
            self.initialize_elements()
            self.startTimeForLogSearch=""
            try:
                from common_lib.log_ops import log_ops
            except Exception as E:
                self.logger.error(f"ERROR :: Unable to import log ops - {E}")
                raise Exception(f"ERROR :: Unable to import log ops - {E}")
            try:
                from OS_Ios.lib.OPS_System import Sys_Ops
            except Exception as E:
                self.logger.error(f"ERROR :: Unable to import sys ops - {E}")
                raise Exception(f"ERROR :: Unable to import sys ops - {E}")
            self.SysOps = Sys_Ops(self.logger)
            self.logOps = log_ops(console_handle_needed=False)
            #try:
                #self.zccBuild = self.logOps.get_current_zcc_version()
            #except Exception as E:
                #self.logger.error(f"Unable to get zcc version :: {E}\n\n")
                #self.zccBuild = ''

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
        def initialize_elements(self):  # initialize elements for win/mac
            if self.startApp:
                self.UI.start_app(app=ios_elements.APP, sleep_time=30)
                self.elements = copy.deepcopy(ios_elements)
                self.UI.app=self.elements.APP
                return
        
        
        def calculateFuncExecuteTime(func):
            '''
        Decorator used for calculating time taken for execution of a function
        Flow: ------------------------------
            1. Func is passed to calculateFuncExecuteTime which calls wrapper func
            2. Wrapper func then first gets the current time(start time) and then make call to the given func as arg in calculateFuncExecuteTime
            3. After func is executed, we then get the current time(end time) and then calculate endtime-starttime and get the time difference
            '''
            @wraps(func)
            def wrapper(*args,**kwargs):
                startTime = time.time()
                #print("START TIME : {}".format(startTime))
                func(*args,**kwargs)
                endTime = time.time()
                #print("END TIME : {}".format(endTime))
                args[0].FileTransferTime = int(endTime-startTime)
                print("\n")
                args[0].logger.critical(f"{'*'*50}")
                args[0].logger.critical(f"TIME TAKEN FOR {func.__name__} : {args[0].FileTransferTime} Seconds")
                args[0].logger.critical(f"{'*'*50}")
            return wrapper

        def validate_zcc_logged_in(self):
            i=0
            zccLoggedInValidated = False
            while i<=3:
            
                try:
                    zccLoggedInValidated=self.UI.check_if_exists(self.elements.NOTIFICATION)
                    time.sleep(5)
                except:
                    i+=1
                    time.sleep(5)
                else:
                    break
            if zccLoggedInValidated:self.logger.info(f"Logout button exists, zcc logged in")
            else:
                self.logger.error("Logout button does not exists, please check logs and zcc state")
                raise Exception("Logout button does not exists, please check logs and zcc state")
        
        def bring_zcc_to_focus(self,
                        number_of_trails: int = 2,      # number of times to try to open Zcc before raising exception
                        sleep_before_focus: int = 0,    # int seconds to sleep before bringing zcc to focus
                           ):  
            time.sleep(sleep_before_focus)
            try:
                self.UI.driver.activate_app("com.zscaler.zscaler")
                return
            except Exception as e:
                self.logger.info("Warning :: trying to bring ZCC to Focus again! ; {}\n".format(e))
        
            self.logger.error("Failed to bring ZCC to Focus")
            raise Exception("Error :: Failed to bring ZCC to Focus")
    
     
    # ---------------------------------------------------------- #
    #               HELPER FUNCTIONS FOR ZCC LOGIN               #   
    # ---------------------------------------------------------- #
        def Do_Okta_Login(self,     
                      user_id : str,                    # okta username
                      user_password : str,              # okta password
                      search_log_time : str,            # time to be used for log search - datetime.now() should be passed
                      partial_login : bool,             # defines whether to terminate login or do complete login
                      search_file: str = "latest"       # defines whether to search latest file or oldest file
                      ):
                if partial_login:
                #self.Bring_Zcc_To_Focus()
                    self.logger.info("Aborting Okta Login , hitting back button on ZCC")
                    self.UI.click(self.elements.ZCC_BACK_BUTTON_AT_OKTA,sleep_time=1, AEFC="Cannot click ZCC Back Button")
                    self.startTimeForLogSearch = datetime.now()         
                    return

                #if self.operating_system=='Ios':
                
                okta_username=self.UI.check_if_exists(self.elements.OKTA_USERNAME)
                
                if okta_username:

                #self.explicit_wait(self.elements.OKTA_USERNAME)
                    self.UI.click(self.elements.OKTA_USERNAME)
                    self.UI.click(self.elements.OKTA_USERNAME,retry_frequency=5,max_threshold=10,sleep_time=1, AEFC="Cannot click okta username")
                #self.UI.click(self.elements.OKTA_USERNAME)
                    self.UI.type(self.elements.OKTA_USERNAME, text=user_id, sleep_time=1, AEFC="Something wrong with username")
                    self.UI.click(self.elements.SIGN_IN_TEXT)
                    self.UI.click(self.elements.OKTA_PASSWORD)
                    self.UI.type(self.elements.OKTA_PASSWORD, text=user_password, sleep_time=1, AEFC="Something wrong with passwd")
                    okta_sign_in=self.UI.check_if_exists(self.elements.OKTA_SIGN_IN)
                    if okta_sign_in:
                        self.UI.click(self.elements.OKTA_SIGN_IN)
                    else:
                        self.UI.click(self.elements.SIGN_IN_TEXT)
                            #self.explicit_wait(self.elements.OKTA_SIGN_IN)
                        self.UI.click(self.elements.OKTA_SIGN_IN)
            
                    try:
                            #self.explicit_wait(self.elements.VPN_ALERT)
                        self.UI.click(self.elements.VPN_ALERT_ALLOW)
                    except:
                        pass
                            #self.explicit_wait(self.elements.TURN_ON_STATUS)

                else:
                    try:
                        #self.explicit_wait(self.elements.VPN_ALERT)
                        self.UI.click(self.elements.VPN_ALERT_ALLOW)
                    except:
                        pass
                
                self.startTimeForLogSearch = datetime.now()    

        def Initial_ZCC_Login_Window(self,      # helper function for initial userfield at zcc login page
                                 USERNAME: str):
            try:
                #self.bring_zcc_to_focus()

                self.UI.type(self.elements.USERNAME, text=USERNAME, sleep_time=1, AEFC="Something wrong with username")
                self.UI.click(self.elements.LOGIN_BUTTON, sleep_time=0, AEFC="Unable to Click Login Button")
                self.startTimeForLogSearch = datetime.now()         
            except Exception as e:
                self.logger.error(f"Error :: Login Failed at USER_ID Page :: {e}")
                raise Exception(f"Error :: Login Failed at USER_ID Page :: {e}")
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------   
        def Handle_Aup(self,                    # helper function for handling zcc aup login
                   TIME_FOR_LOG_SEARCH,     # datetime.now()
                   ZIA_SAML_LOGIN: bool = False,
                   CANCEL_AUP: bool = False,
                   ):
            try:
                self.bring_zcc_to_focus()
                self.UI.click(self.elements.EULA_DECLINE_BUTTON if CANCEL_AUP else self.elements.EULA_ACCEPT_BUTTON, sleep_time=1, AEFC="Cannot click AUP Decline" if CANCEL_AUP else "Cannot click AUP Accept")
                self.startTimeForLogSearch = datetime.now()         
            except Exception as e:
                self.logger.error(f"Error :: Login Failed at AUP Page :: {e}")
                raise Exception(f"Error :: Login Failed at AUP Page :: {e}")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------      
        def Handle_Zia_Saml(self,               # helper function for zia saml login
                        USERNAME: str,
                        PASSWORD : str,
                        TIME_FOR_LOG_SEARCH,        #datetime.now() object
                        CANCEL_ZIA_SAML_LOGIN : bool
                        ):
            try:
            #time.sleep(15)
                self.Do_Okta_Login(user_id=USERNAME,user_password=PASSWORD,search_log_time=TIME_FOR_LOG_SEARCH,partial_login=CANCEL_ZIA_SAML_LOGIN)
                if CANCEL_ZIA_SAML_LOGIN:
                    self.logger.info("Login aborted at ZIA SAML")
                    return
            except Exception as e:
                self.logger.error(f"Login failed at ZIA SAML OKTA Page :: {e}")
                raise Exception(f"Login failed at ZIA SAML OKTA Page :: {e}")

        def Handle_Zia_Login(self,              # helper function for zia login
                        PASSWORD: str,
                        TIME_FOR_LOG_SEARCH,    #datetime.now() object
                        CANCEL_ZIA_LOGIN: bool):

            try:
                if CANCEL_ZIA_LOGIN:
                    self.logger.info("Aborting Login at ZIA, hitting back button on ZCC")
                    self.UI.click(self.elements.ICON_BACK,sleep_time=1, AEFC="Cannot click ZCC Back Button")
                    return
                else:
                    time.sleep(20)
                    isPasswordBoxUp = False
                    isPasswordBoxUp=self.UI.check_if_exists((self.elements.PASSWORD))
                    if isPasswordBoxUp:
                        self.UI.type(self.elements.PASSWORD, text=PASSWORD, sleep_time=1, AEFC="Password button not found")
                        #self.explicit_wait(self.elements.LOGIN_BUTTON)
                        self.UI.click(self.elements.LOGIN_BUTTON, sleep_time=0, AEFC="Unable to click Login Button")
                        time.sleep(10)
                    #try:
                        #self.explicit_wait(self.elements.VPN_ALERT)
                        
                        #self.UI.click(self.elements.VPN_ALERT_ALLOW)
                            #self.explicit_wait(self.elements.TURN_ON_STATUS)
                    #except:
                           # pass
            except Exception as e:
                self.logger.error(f"Error :: Login Failed at Form PASSWORD Page :: {e}")
                raise Exception(f"Error :: Login Failed at Form PASSWORD Page :: {e}")
        
        def Handle_Zia_One_Id_Login(self): # place holder for zia one id login
            pass

    # ------------------------------------------------------------------------------------------------------------------------------------------------------   
        def Handle_Zpa_Login(self,              # helper function for zpa login
                        USERNAME: str,
                        PASSWORD : str,
                        TIME_FOR_LOG_SEARCH,        #datetime.now() object
                        CANCEL_ZPA_LOGIN : bool
                        ):
            try:
                self.Do_Okta_Login(user_id=USERNAME,user_password=PASSWORD,search_log_time=TIME_FOR_LOG_SEARCH,partial_login=CANCEL_ZPA_LOGIN)
                if CANCEL_ZPA_LOGIN:
                    self.logger.info("Login aborted at ZPA SAML")
                    return
            except Exception as e:
                self.logger.error(f"Login failed at ZPA OKTA Page :: {e}")
                raise Exception(f"Login failed at ZPA OKTA Page :: {e}")
        
        @allure.step("zcc login")       
        @calculateFuncExecuteTime
        def Login(self,                              # PARENT FUNCTION FOR ZCC LOGIN
              ZIA_USER: str = None,              # zia user id
              ZIA_PASSWORD: str = None,          # zia user password
              ZPA_USER: str = None,              # zpa user id
              ZPA_PASSWORD: str = None,          # zpa user password
              AUP: bool= False,                  # Defines AUP enabled or not 
              ZIA_SAML_LOGIN: bool= False,       # Defines ZIA SAML enabled or not
              ZIA_ONE_ID_LOGIN: bool= False,       # Defines ZIA SAML enabled or not
              CANCEL_LOGIN_AT_ZIA: bool= False,  # Defines whether to cancel login at ZIA login page 
              CANCEL_LOGIN_AT_ZPA: bool= False,  # Defines whether to cancel login at ZPA login page
              CANCEL_AUP: bool= False,           # Defines whether to cancel or decline AUP
              sleep_time: int = 10               # Defines sleep time after login is done
              ):        
        
            ZIA,ZPA = False,False

            if ZIA_USER==ZIA_PASSWORD==ZPA_USER==ZPA_PASSWORD==None:
            # no zia user is given, check for zpa user
                self.logger.error("Neither ZIA or ZPA user given to be logged in, cannot login")
                raise Exception("Neither ZIA or ZPA user given to be logged in, cannot login")
        
            if ZIA_USER and ZIA_PASSWORD:ZIA=True
            if ZPA_USER and ZPA_PASSWORD: ZPA=True
            if ZIA and ZPA:self.logger.info("Given user details are ZIA+ZPA")
            if not ZIA:self.logger.info("Given user is ZPA only user")
       
       # Intial Username field -------------------------
            if ZIA or ZPA:
                self.Initial_ZCC_Login_Window(USERNAME=ZIA_USER if ZIA else ZPA_USER)
        
        # AUP -------------------------
            if AUP:
                self.Handle_Aup(TIME_FOR_LOG_SEARCH=self.startTimeForLogSearch,ZIA_SAML_LOGIN=ZIA_SAML_LOGIN,CANCEL_AUP=CANCEL_AUP)
        
        # ZIA -------------------------
            if ZIA:
                if ZIA_SAML_LOGIN:
                    self.Handle_Zia_Saml(USERNAME=ZIA_USER,PASSWORD=ZIA_PASSWORD,TIME_FOR_LOG_SEARCH=self.startTimeForLogSearch,CANCEL_ZIA_SAML_LOGIN=CANCEL_LOGIN_AT_ZIA)
                elif ZIA_ONE_ID_LOGIN:
                    self.Handle_Zia_One_Id_Login()
                else:
                    self.Handle_Zia_Login(PASSWORD=ZIA_PASSWORD,TIME_FOR_LOG_SEARCH=self.startTimeForLogSearch,CANCEL_ZIA_LOGIN=CANCEL_LOGIN_AT_ZIA)

        #ZIA and ZPA
            

        # ZPA -------------------------
            if ZPA:
                self.Handle_Zpa_Login(USERNAME=ZPA_USER,PASSWORD=ZPA_PASSWORD,TIME_FOR_LOG_SEARCH=self.startTimeForLogSearch,CANCEL_ZPA_LOGIN=CANCEL_LOGIN_AT_ZPA)
            self.logger.info("Success :: Logged into ZCC Succesfully!")
            if sleep_time>0:
                print(f'Sleeping for {sleep_time}')
                time.sleep(sleep_time)


        @calculateFuncExecuteTime
        @allure.step("Logout ZCC")
        def Logout( self,
                password: str =None,            # Passowrd/OTP to be used for logout
                sleep_time: int =180,           # number of seconds to sleep when executed
                failure_expected: bool=False,   # Defines whether logout should fail - used with incorrect password
                cancel_Logout: bool = False     # Defines whether to cancel logout operation
               ):
    
            if sleep_time==0:sleep_time=30
            try:
                #self.bring_zcc_to_focus()
                self.UI.click(self.elements.LOGOUT_BUTTON, sleep_time=1 * self.sleep_factor,AEFC="Unable to click Logout Button")

                if cancel_Logout:
                    self.logger.info("Aborting Logout")
                    self.UI.click(self.elements.LOGOUT_PASSWORD_CANCEL_BUTTON, sleep_time=0, AEFC="Unable to confirm Logout disable")
                    return
                if password: 
                    self.UI.type(self.elements.LOGOUT_PASSWORD, text=password, sleep_time=1*self.sleep_factor, AEFC="Unable to enter password")
                    self.UI.click(self.elements.LOGOUT_PASSWORD_LOGOUT_BUTTON, sleep_time=0*self.sleep_factor, AEFC="Unable to confirm Logout")
                    if failure_expected:    # mostly used when incorrect password is given intentionally 
                        if self.operating_system != "mac":
                            for i in range(4):
                                try:
                                    self.UI.click(self.elements.LOGOUT_PASSWORD_FAILED_OK_BUTTON, sleep_time=0, AEFC="Unable to confirm Logout disable")
                                    return
                                except:
                                    print("Wait for the operation to complete")
                        else:
                            self.UI.click(self.elements.LOGOUT_PASSWORD_CANCEL_BUTTON, sleep_time=0, AEFC="Unable to confirm Logout disable")
                            return

                else:
                    self.UI.click(self.elements.LOGOUT_OK,sleep_time=1,AEFC="unable to logout prompt")
                    self.logger.info("logged confirm")
    
            except Exception as e:
                print("adding for code modify") 
                self.logger.error("Logout Failed at ZSTray Page ; {}\n".format(e))
                raise Exception("Error :: Logout Failed at ZSTray Page ; {}\n".format(e))
            assert not self.UI.check_if_exists(self.elements.LOGOUT_OK, boolean=True, AESC="Logout Button not accepted", AEFC="Logged out well")
            self.logger.info("Success :: Logged out!")

        @calculateFuncExecuteTime
        @allure.step("Validate_tunnel_status")
        def Validate_tunnel_status(self,zia_only_company=False,zpa_only_company=False,zia_on=False,zpa_on=False):
            if zia_only_company:
                if zia_on:
                    self.UI.click(self.elements.INTERNET_ACCESS)
                    #self.explicit_wait(self.elements.TURN_ON_STATUS)
                    zia_status=self.UI.check_if_exists(self.elements.TURN_ON_STATUS)
                    if zia_status:
                        self.logger.info("ZIA Status is ON")
                    else:
                        raise Exception("Failure:: ZIA Status is not On")
                else:
                    self.UI.click(self.elements.INTERNET_ACCESS)
                    #self.explicit_wait(self.elements.TURN_OFF_STATUS)
                    zia_off_status=self.UI.check_if_exists(self.elements.TURN_OFF_STATUS)
                    if zia_off_status:
                        self.logger.info("ZIA state is OFF")
                    else:
                        raise Exception("Failure:: ZIA state is not OFF")
            if zpa_only_company:

                if zpa_on:
                    self.UI.click(self.elements.PRIVATE_ACCESS)
                    zpa_status=self.UI.check_if_exists(self.elements.TURN_ON_STATUS)
                    if zpa_status:
                        self.logger.info("ZPA status is On")
                    else:
                        raise Exception("Failure:: ZPA Status is not On")
                else:
                    self.UI.click(By.XPATH,self.elements.PRIVATE_ACCESS)
                    zpa_off_status=self.UI.check_if_exists(self.elements.TURN_OFF_STATUS)
                    if zpa_off_status:
                        self.logger.info("ZpA state is OFF")
                    else:
                        raise Exception("Failure:: ZPA state is not OFF")
            if zia_only_company and zpa_only_company:
                if zia_on:
                    self.UI.click(self.elements.INTERNET_ACCESS)
                    zia_status=self.UI.check_if_exists(self.elements.TURN_ON_STATUS)
                    if zia_status:
                        self.logger.info("ZIA Status is ON")
                    else:
                        raise Exception("Failure:: ZIA Status is not On")
                else:
                    self.UI.click(self.elements.INTERNET_ACCESS)
                    ia_off_status=self.UI.check_if_exists(self.elements.TURN_OFF_STATUS)
                    if zia_off_status:
                        self.logger.info("ZIA state is OFF")
                    else:
                        raise Exception("Failure:: ZIA state is not OFF")
                if zpa_on:
                    self.UI.click(self.elements.PRIVATE_ACCESS)
                    zpa_status=self.UI.check_if_exists(self.elements.TURN_ON_STATUS)
                    if zpa_status:
                        self.logger.info("ZPA status is On")
                    else:
                        raise Exception("Failure:: ZPA Status is not On")
                else:
                    self.UI.click(self.elements.PRIVATE_ACCESS)
                    zpa_off_status=self.UI.check_if_exists(self.elements.TURN_OFF_STATUS)
                    if zpa_off_status:
                        self.logger.info("ZpA state is OFF")
                    else:
                        raise Exception("Failure:: ZPA state is not OFF")


        @calculateFuncExecuteTime
        @allure.step("toggle")
        def toggle_zia(self,disable_zia=False,enable_zia=False,disable_zia_with_password=False):
            if disable_zia:
                self.UI.click(self.elements.INTERNET_ACCESS)
                #self.explicit_wait(self.elements.TURN_OFF_TAB)
                self.UI.click(self.elements.TURN_OFF_TAB)
                #self.explicit_wait(self.elements.DISABLE_OK)
                self.UI.click(self.elements.DISABLE_OK)
                time.sleep(5)
                print("zia is turned off")
                self.Validate_tunnel_status(zia_only_company=True,zpa_only_company=False,zia_on=False,zpa_on=False)
                # self.Validate_tunnel_status(zia_on=False,zpa_on=True)
            if enable_zia:
                self.UI.click(self.elements.INTERNET_ACCESS)
                #self.explicit_wait(self.elements.TURN_ON_TAB)
                self.UI.click(self.elements.TURN_ON_TAB)
                time.sleep(10)
                self.Validate_tunnel_status(zia_only_company=True,zpa_only_company=False,zia_on=True,zpa_on=False)
                #self.Validate_tunnel_status(zia_on=True,zpa_on=True)
            if disable_zia_with_password:
                self.UI.click(self.elements.INTERNET_ACCESS)
                self.UI.click(self.elements.TURN_OFF_TAB)
                self.UI.click(self.elements.DISABLE_OK)
                self.Validate_tunnel_status(zia_only_company=True,zpa_only_company=False,zia_on=False,zpa_on=False)

        
        
        def Validate_traffic_going_through_tunnel(self,is_zia_enable=False,is_zia_disable=False):
            domain="ip.zscaler.com"
        #for Safari
            #self.UI.driver.activate_app("com.apple.mobilesafari")
            #self.explicit_wait(self.elements.SAFARI_SEARCH_BAR)
            #self.UI.click(self.elements.SAFARI_SEARCH_BAR)
            #self.explicit_wait(self.elements.SAFARI_SEARCH_BAR_TEXT)
            #self.UI.type(self.elements.SAFARI_SEARCH_BAR_TEXT,text=domain, sleep_time=1, AEFC="Password button not found")
            
            #time.sleep(40)
            #self.explicit_wait(self.elements.GO)
            #self.UI.click(By.XPATH,self.elements.GO)
            #time.sleep(20)
        #for chrome
            self.UI.driver.activate_app("com.google.chrome.ios")

            #self.explicit_wait(self.elements.GOOGLE_SEARCH)
            self.UI.click(self.elements.GOOGLE_SEARCH)
            #self.explicit_wait(self.elements.CHROME_SEARCH_BAR)
            self.UI.type(self.elements.CHROME_SEARCH_BAR,text=domain, sleep_time=1, AEFC="Password button not found")
            #self.explicit_wait(self.elements.CHROME_GO)
            self.UI.driver.find_element(MobileBy.ACCESSIBILITY_ID, "Go").click()        



            if is_zia_disable:
            
                #self.explicit_wait(self.elements.PROXY_NOT_VIA_ZSCALER)
                x=self.UI.check_if_exists(self.elements.PROXY_NOT_VIA_ZSCALER)
                #print x
                if x:
                    
                    self.UI.check_if_exists(self.elements.PROXY_NOT_VIA_ZSCALER, do_click=False)
                    self.logger.info("Traffic is not going through cloud")
                else:
                    xx=self.UI.check_if_exists(self.elements.PROXY_VIA_ZSCALER)
                    if xx:
                        raise Exception("Failure:: traffic still going through cloud")
            if is_zia_enable:
                #self.explicit_wait(self.elements.PROXY_VIA_ZSCALER)
                y=self.UI.check_if_exists(self.elements.PROXY_VIA_ZSCALER )
                #print y
                if y:
                    self.UI.click(self.elements.PROXY_VIA_ZSCALER, do_click=False)
                else:
                    yy=self.UI.check_if_exists(self.elements.PROXY_NOT_VIA_ZSCALER)
                    if yy:
                        raise Exception("Failure")
              
            self.UI.driver.terminate_app("com.google.chrome.ios")
            self.bring_zcc_to_focus()
            #self.UI.driver.activate_app("com.zscaler.zscaler")
            #self.explicit_wait(self.elements.LOGOUT_BUTTON)

            
            
            #self.UI.driver.delete_all_cookies()  
            
            
        
        
        @calculateFuncExecuteTime
        @allure.step("Restart Service")
        def Restart_Service(self,
                        sleep_time: int =60,                    # Number of seconds to sleep after execution
                             
                        ):
        
            try:
            #self.bring_zcc_to_focus()
                self.UI.click(self.elements.NOTIFICATION, sleep_time=0, AEFC="Unable to click More button on ZCC")
                self.UI.click(self.elements.RESTART_SERVICE, sleep_time=3,AEFC="Unable to click Restart Service button on ZCC")
            
                self.UI.click(self.elements.CONFIRM, sleep_time=0,AEFC="Unable to confirm ZCC Restart")
                time.sleep(sleep_time)
                self.UI.click(self.elements.INTERNET_ACCESS, sleep_time=0,AEFC="Unable to click on Internet Security")
                self.UI.click(self.elements.TURN_ON_STATUS,sleep_time=0,AEFC="Unable to Find ON",do_click=False)
            except Exception as e:
                self.logger.error("Restart Service Failed ; {}\n".format(e))
                raise Exception("Error :: Restart Service Failed ; {}\n".format(e))
        
        
            self.logger.info("Success :: Restarted Service!")

        @calculateFuncExecuteTime
        def Export_Logs(self, 
                    after_login: bool = True,
                    sleep_time=30):     
            try:
            
            
                if after_login:

                    self.UI.click(self.elements.NOTIFICATION, sleep_time=0, AEFC="Unable to click More button on ZCC")
                    self.UI.click(self.elements.EXPORT_LOGS, sleep_time=1, AEFC="Unable to click on export logs")
                    self.UI.click(self.elements.GMAIL_EXPORT_LOGS, sleep_time=1, AEFC="Unable to click on export logs")
                    self.UI.type(self.elements.ANDROID_GMAIL_TO,text=Utils.export_email_id,AEFC="Unable to enter email id")
                    self.UI.click(self.elements.ANDROID_GMAIL_SEND, sleep_time=1, AEFC="Unable to click on send  button")
                    time.sleep(sleep_time)
                else:
                    self.UI.click(self.elements.HAMBURGER, sleep_time=1, AEFC="Unable to click on export logs")
                    self.UI.click(self.elements.WITHOUT_LOGIN_EXPORT_LOGS, sleep_time=1, AEFC="Unable to click on export logs")
                    self.UI.click(self.elements.GMAIL_EXPORT_LOGS, sleep_time=1, AEFC="Unable to click on export logs")
                    self.UI.type(self.elements.ANDROID_GMAIL_TO,text=Utils.export_email_id,AEFC="Unable to enter email id")
                    self.UI.click(self.elements.ANDROID_GMAIL_SEND, sleep_time=1, AEFC="Unable to click on send  button")
                    time.sleep(sleep_time)


                
                
            except Exception as e: 
                self.logger.error("Export Logs Failed ; {}\n".format(e))
                raise Exception("Error :: Export Logs Failed ; {}\n".format(e))
             
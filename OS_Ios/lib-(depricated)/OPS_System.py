################################################################
#                       SYSTEM_OPS.PY                          #
#                                                              #
# AUTHOR : SAHIL KUMAR (<EMAIL>)               #
# CREDITS : SRINIVASA BHASWANTH, ABHIMANYU SHARMA              #
#                                                              #
################################################################
'''
Last Changes Made : 25/05/2022 by <PERSON>hil
Details : Added Installed Zscaler Root CA cert support

'''

import subprocess,os,time
import socket
import OpenSSL,ssl
from datetime import date
import shutil 
from zipfile import ZipFile
import os.path
from threading import Thread
import sys
import logging
import psutil
#import Constants
#from log_ops import log_ops
from common_lib import Constants
from common_lib.log_ops import log_ops
import allure
import json
import platform
import dns.resolver
from datetime import datetime
import pytz



class ThreadWithReturnValue(Thread):
        '''
        This class is a subclass created to get return value from threads
        By default threads dont send return value
        '''
        def __init__(self, group=None, target=None, name=None,
                 args=(), kwargs={}, Verbose=None):
            Thread.__init__(self, group, target, name, args, kwargs)
            self._return = None
        def run(self):
            print(type(self._target))
            if self._target is not None:
                self._return = self._target(*self._args,
                                                    **self._kwargs)
        def join(self, *args):
            Thread.join(self, *args)
            return self._return

class Sys_Ops:
    def __init__(self,logger=None, cloud=None):
        self._logger = (logger if logger else self.Initialize_Logger("Sys_Ops.log", Log_Level="DEBUG"))
    


    def Initialize_Logger(self, 
    Log_File_Name,                      # (str)     Name of the Log file to be created 
    Log_Level,                          # (str)     Level of logging (INFO/DEBUG/WARNING/CRICITCAL etc)
    console_handle_needed=True          # (bool)    print on command line if True only
    ):
        """
        Target: Create Logger object.
        Work Flow
        ----------
            1. Create a logger object.
            2. Create a console handler if required
                - console handler is used to print required info on command line window.
            3. Create a file handler -> mandatory
                - Make sure 'Logs' folder exists, as this file handler will be created in this folder only.
                - file handler is used to send all information to a log file in specified folder.
            4. Set the debug level (INFO/DEBUG/WARNING/CRITICAL etc). Default: INFO
            5. Specify how the logging must happen (with/without datetime/loglevel/username)
            6. Add all created handlers to the logger instance created in step 1.
            7. return the logger instance.
        """
        # 1, 2 & 3
        self.Logs_Directory = os.path.join(os.path.abspath(os.path.dirname(os.path.abspath("Automation.py"))), "Logs")
        logger = logging.getLogger(Log_File_Name)
        if console_handle_needed: console_handler = logging.StreamHandler()
        if not os.path.exists(self.Logs_Directory): os.mkdir(self.Logs_Directory)
        file_handler = logging.FileHandler(os.path.join(self.Logs_Directory, Log_File_Name))

        # 4
        if Log_Level:
            logger.setLevel(eval("logging.{}".format(Log_Level)))
            if console_handle_needed: console_handler.setLevel(eval("logging.{}".format(Log_Level)))
            file_handler.setLevel(eval("logging.{}".format(Log_Level)))

        # 5
        if console_handle_needed: console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))

        # 6
        if console_handle_needed: logger.addHandler(console_handler)
        logger.addHandler(file_handler)
        # 7
        return logger
        

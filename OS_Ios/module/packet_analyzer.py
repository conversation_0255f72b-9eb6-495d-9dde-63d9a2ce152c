# Written By : <PERSON><PERSON><PERSON>

import time
from common_lib.common.logger import <PERSON><PERSON>
import logging
import threading
from OS_Ios.library import ops_system
from OS_Ios.library import ops_packet

class PacketAnalyzer:
    def __init__(self, log_handle:logging.Logger=None, driver=None):
        self.logger = (log_handle if log_handle else Logger.initialize_logger("Packet_Analyzer.log", log_level="DEBUG"))
        self.sys_ops = ops_system.SysOps(driver=driver)
        self.ops_pkt = ops_packet.OpsPacket()
        self.ios_elements = ops_system.IosElements()
        
    def pktcapture_accessapp_delhistory(self,pkt_capture_file_path:str,pkt_capture_duration:int,url:str,pkt_capture_interface:list[str],zpa_app:bool, browser:str) -> tuple[bool, str, None]:
        """
        Starts packet capture and accesses a ZPA app in separate threads.
        Returns a tuple containing a boolean value indicating success, a string message, and None.
        """
        try:
            # Define the packet capture thread function
            def start_packet_capture_thread():
                start_packet_capture = self.ops_pkt.start_packet_capture(pkt_capture_output_file=pkt_capture_file_path, pkt_capture_interface=pkt_capture_interface, pkt_capture_duration=pkt_capture_duration)
                assert start_packet_capture[0], self.logger.error(start_packet_capture[1])
                time.sleep(5)
            self.logger.info("defined start packet capture thread")
            # Define the ZPA app access thread function
            def access_zpa_app_thread():
                access_zpa_app = self.sys_ops.access_application(url=url, browser=browser, zpa_app=zpa_app)
                assert access_zpa_app[0], self.logger.error(access_zpa_app[1])
            self.logger.info("defined access application thread")
            # Create threads
            pkt_capture_thread = threading.Thread(target=start_packet_capture_thread)
            zpa_app_thread = threading.Thread(target=access_zpa_app_thread)

            # Start threads
            self.logger.info("Starting packet Capture")
            pkt_capture_thread.start()
            self.logger.info("Starting app access")
            zpa_app_thread.start()

            # Wait for threads to finish
            pkt_capture_thread.join()
            zpa_app_thread.join()

            if browser=="chrome" : 
                time.sleep(6)
                self.sys_ops.ui.click(path=self.ios_elements.CHROME_MENU_BUTTON)
                time.sleep(2)
                self.sys_ops.ui.click(path=self.ios_elements.CHROME_HISTORY_BUTTON)
                time.sleep(2)
                self.sys_ops.ui.click(path=self.ios_elements.CHROME_DELETE_BROWSING_DATA)
                time.sleep(2)
                self.sys_ops.ui.click(path=self.ios_elements.CHROME_DELETE_DATA_BUTTON)
                time.sleep(2)
    
        except AssertionError as e:
            self.logger.error(f"Assertion error occurred: {e}")
            return False, f"Assertion error occurred: {e}", None
        except Exception as e:
            self.logger.error(f"An error occurred: {e}", exc_info=True)
            return False, f"An error occurred: {e}", None
        else:
            self.logger.info("Packet capture and ZPA app access completed successfully and History Deleted")
            return True, "Packet capture and ZPA app access completed successfully and History Deleted", None

    def extract_dns_response_from_packet_capture_file(self, pkt_capture_output_file: str, url: str) -> tuple[bool, str, str | None]:
        """
        Reads a packet capture file, filters for DNS packets, and extracts the DNS response.
        Returns a tuple containing a boolean value indicating success, a string message, and the extracted DNS response (or None).
        """
        try:
            # Read packet capture file and filter for DNS packets
            success, message, packets = self.ops_pkt.read_packet_from_pcapng_file(pkt_capture_output_file, filter='dns')
            if not success:
                self.logger.error(message)
                return False, message, None

            # Extract the DNS response
            dns_response = ""
            for packet in packets:
                dns = packet.dns
                if dns.qry_name == url:
                    if hasattr(dns, 'a'):
                        if isinstance(dns.a, str):
                            if dns.a.startswith('100.64' + "."):
                                dns_response = dns.a
                                break

        except Exception as e:
            self.logger.error(f"An error occurred: {e}", exc_info=True)
            return False, f"An error occurred: {e}", None
        else:
            self.logger.info(f"DNS response extracted successfully: {dns_response}")
            return True, f"DNS response extracted successfully: {dns_response}", dns_response
    
    def check_domain_inaccessibility_using_pcap(self, pcap_file_path: str, target_domain: str) -> tuple:
        try:
            # Filtering out all dns response packet only
            dns_filter = f"dns and dns.flags.response == '1' and dns.qry_name == {target_domain} "
            success, message, packets = self.ops_pkt.read_packet_from_pcapng_file(pcap_file_path, filter=dns_filter)
            
            # Checking if all the packet should contains dns flag response code as 3
            for packet in packets:
                dns = packet.dns
                flags_hex = dns.flags
                flags_int = int(flags_hex, 16)
                rcode = flags_int & 0xF
                if rcode != 3:
                    self.logger.info("DNS response packet not containing the flag response code as {3}")
                    return (False, f"DNS response packet for {target_domain} does not contain the expected flag response code.", 0)
            
            # If all packets have the expected response code, the domain is inaccessible
            return (True, f"{target_domain} is inaccessible.", 1)
        
        except Exception as e:
            print(f"Error during domain Accessibility check for {target_domain}: {e}")
            return (False, f"An error occurred while checking {target_domain}: {str(e)}", 0)


    def check_ip_accessibility_using_pcap(self, pcap_file_path: str, target_ip: str) -> tuple:
        try:
            # Filter for HTTP traffic to/from the IP on port 80
            http_filter = f'(ip.addr == {target_ip}) and tcp.port == 80 and http'
            success, message, packets = self.ops_pkt.read_packet_from_pcapng_file(pcap_file_path, filter=http_filter)
            for pkt in packets:
                try:
                    # Looking for HTTP Response packets with success/redirect codes
                    if hasattr(pkt.http, 'response_code'):
                        status_code = int(pkt.http.response_code)
                        # Consider 2xx (Success) and 3xx (Redirection) as accessible
                        if 200 <= status_code < 400:
                            print(f"[+] Found successful HTTP interaction (Status {status_code}) with {target_ip} (Packet #{pkt.number})")
                            return (True, f"Found successful HTTP interaction with {target_ip}.", 1)
                except (AttributeError, KeyError, ValueError):
                    # Skip packets without the necessary HTTP fields or invalid code
                    continue

        except Exception as e:
            print(f"Warning: Error during HTTP check for {target_ip}: {e}")
            return (False, f"An error occurred while checking {target_ip}: {str(e)}", 0)

        # No successful interaction found for any resolved IP ---
        print(f"[*] No clear evidence of successful HTTP/HTTPS access to '{target_ip}' found for the resolved IPs.")
        return (False, f"No clear evidence of successful HTTP/HTTPS access to '{target_ip}' found.", 0)
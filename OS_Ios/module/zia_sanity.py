from collections import defaultdict
import ipaddress
import subprocess
import threading
import time
from OS_Ios.library.ops_system import SysOps
from OS_Ios.library.ui_zcc import Zcc
import pyshark
# from selenium.webdriver.common.by import By
from common_lib.common.logger import Logger
from OS_Ios.module.zcc_sanity import Zcc<PERSON>anity
from OS_Ios.library.ui_zcc import Zcc
from OS_Ios.library.ops_system import SysOps
# from OS_Ios.library import ops_packet
# import os
# import tkinter as tk
# from tkinter import messagebox

class ZiaSanity(ZccSanity):
    """
    Provides method to capture packets & read packets
    """
    def __init__(self, cloud, config, log_handle=None, zcc_object=None):
        super().__init__(cloud=cloud, config=config, log_handle=None, zcc_object=zcc_object)
        self.logger = (log_handle if log_handle else Logger.initialize_logger("ZiaSanity.log", log_level="DEBUG"))
        self.zcc = Zcc(log_handle=self.logger, start_app=True)
        self.sys_ops = SysOps(logger=self.logger, driver=self.zcc.ui.driver)


    def fetch_client_ipv4(self) -> tuple[bool, str, str]:
        """
        Fetches the client IP address from the UI of the Internet Security tab of ZCC.

        Returns:
            tuple[bool, str, str]: A tuple where:
                - The first value is a boolean indicating success or failure.
                - The second value is a string message indicating success or failure reason.
                - The third value is the fetched client IPv4 address (or an empty string in case of failure).
        """
        try:
            self.logger.info("Bringing ZCC application to focus.")
            self.zcc.bring_zcc_to_focus()

            self.logger.info("Navigating to Internet Security tab.")
            self.zcc.ui.click(path=self.zcc.elements.INTERNET_ACCESS)

            self.logger.info("Fetching the client IP address from the UI.")
            client_ipv4 = self.zcc.ui.fetch_attribute(xpath=self.zcc.elements.CLIENT_IP_VALUE, fetch_text=True)

            if not client_ipv4:
                error_message = "Failed to fetch client IP address - the fetched value is empty."
                self.logger.error(error_message)
                return (False, error_message, "")

            success_message = f"Successfully fetched client IPv4 address: {client_ipv4}"
            self.logger.info(success_message)
            return (True, success_message, client_ipv4)

        except Exception as e:
            error_message = f"Error occurred while fetching client IPv4 address: {str(e)}"
            self.logger.error(error_message, exc_info=True)
            return (False, error_message, "")
        
    def fetch_sme_ip(self) -> tuple[bool, str, str]:
        """
        Fetches the SME server IP address from the UI of the Internet Security tab of ZCC.

        Returns:
            tuple[bool, str, str]: A tuple where:
                - The first value is a boolean indicating success or failure.
                - The second value is a string message indicating success or failure reason.
                - The third value is the fetched SME IP address (or an empty string in case of failure).
        """
        try:
            self.logger.info("Bringing ZCC application to focus.")
            self.zcc.bring_zcc_to_focus()

            self.logger.info("Navigating to Internet Security tab.")
            self.zcc.ui.click(path=self.zcc.elements.INTERNET_ACCESS)

            self.logger.info("Fetching the SME IP address from the UI.")
            sme_ip = self.zcc.ui.fetch_attribute(xpath=self.zcc.elements.SERVER_IP_VALUE, fetch_text=True)

            # Split and extract the IP address before a colon (if applicable)
            sme_ip = sme_ip.split(":")[0] if sme_ip else ""

            if not sme_ip:
                error_message = "Failed to fetch SME IP address - the fetched value is empty."
                self.logger.error(error_message)
                return (False, error_message, "")

            success_message = f"Successfully fetched SME IP address: {sme_ip}"
            self.logger.info(success_message)
            return (True, success_message, sme_ip)

        except Exception as e:
            error_message = f"Error occurred while fetching SME IP address: {str(e)}"
            self.logger.error(error_message, exc_info=True)
            return (False, error_message, "")


    def validate_dns(self, pcap_file:str, ipv6:bool = False, dns_server_ip:set = None, client_ip:str = None, domain:str = None) -> tuple[bool, str]:
        """
        Validate DNS packets for the specified domain.

        Args:
        - packet: A Pyshark packet object.
        - domain: The domain whose DNS traffic is being validated.

        Returns:
        - bool: True if DNS validation is successful, False otherwise.
        """

        """
        Validate dns inclusion by ensuring none of the dns packets contain system dns ip either in ip.src or ip.dst in the pcap file.

        Args:
            - pcap_file (str): Path to the pcapng file for parsing packets.
            - dns_server_ip (set): The IP address of the system DNS server.
            - domain (str): domain name which we have accessed and will be used as a display filter.

        Returns:
            - tuple[bool, str]: A tuple where:
                - The first value is True if dns inclusion validation passes, False otherwise.
                - The second value contains a message describing success or failure.
        """

        ipver = 6 if ipv6 else 4
 
        try:
            # Parse the pcap file and apply a DNS filter
            self.logger.info(f"Parsing pcap file: {pcap_file}")
            success, message, capture = self.ops_packet.read_packet_from_pcapng_file(pcap_file, filter=f'dns and frame contains "{domain}"')
            if not success:
                self.logger.error(message)
                return False, message
            # try:
            #     capture = pyshark.FileCapture(
            #         pcap_file, 
            #         display_filter=f'dns and frame contains "{domain}"'  # DNS packets only
            #     )
            # except Exception as e:
            #     self.logger.error(f"Failed to parse pcap file: {pcap_file}.", exc_info=True)
            #     return False, f"Failed to parse pcap file: {str(e)}"

            packet_count = len(capture)  # Count the number of packets in the capture
            if packet_count == 0:
                self.logger.error("No packets found in the capture file.")
                return False, "No DNS packets found in the capture file for validation."


            # Initialize a flag for validation
            system_dns = True

            # Iterate through packets to search for system dns in the dns packets
            self.logger.info(f"Validating packets with system DNS server IP: {dns_server_ip}")
            try:
                for packet in capture:
                    if 'DNS' in packet:
                        try:
                            if hasattr(packet, "ip"):
                                source_ip = packet.ip.src
                                destination_ip = packet.ip.dst
                            elif hasattr(packet, "ipv6"):
                                source_ip = packet.ipv6.src
                                destination_ip = packet.ipv6.dst
                            else :
                                self.logger.info("No IP attribute matched for the packet.")
                                continue
                            # source_ip = packet.ip.src  # Source IP of the DNS request
                            # destination_ip = packet.ip.dst  # Destination IP (DNS server IP)

                            # Log each detected DNS request
                            self.logger.debug(f"DNS Request - Source: {source_ip}, Destination: {destination_ip}")
                            self.logger.info(f"client ip : {client_ip}")
                            # print(f"d ip : {client_ip}")
                            # Check if the request matches client IP -> DNS server IP validation
                            if ((source_ip == client_ip and destination_ip in dns_server_ip) or (source_ip in dns_server_ip and destination_ip == client_ip)):
                                ip_obj1 = ipaddress.ip_address(source_ip) # make the variable name more generic like source_ip_obj
                                ip_obj2 = ipaddress.ip_address(destination_ip)
                                if ip_obj1.version == ipver and ip_obj2.version == ipver:
                                    pass
                                else :  # Check if the address is IPv6
                                    return False, f"DNS server has IPv{ip_obj1.version} address"
                            else:
                                self.logger.info("System DNS server IP not found in the packets.")
                                system_dns = False
                                break  # Validation successful, no need to check further.
                        except AttributeError:
                            self.logger.warning("Packet missing required attributes - Source or Destination IP.")
                            continue
            except Exception as e:
                self.logger.error("Error occurred while processing packets.", exc_info=True)
                return False, f"Error occurred while processing packets: {str(e)}"

            # Validate final result
            if system_dns:
                self.logger.info("Dns validation passed successfully.")
                return True, "Dns validation passed successfully."
            else:
                self.logger.error("System DNS server IP not found in packets.")
                return False, "Dns validation failed: System DNS server IP not found in packets."

        except Exception as e:
            # Log unexpected errors
            self.logger.error(f"An unexpected error occurred: {str(e)}", exc_info=True)
            return False, f"An unexpected error occurred: {str(e)}"


    def validate_http_connect(self, pcap_file:str, domain:str) -> tuple[bool, str]:
        """
        Validate HTTP packets to detect HTTP CONNECT method.

        Args:
            - pcap_file (str): Path to the pcap file containing captured packets.
            - domain (str): The domain whose HTTP CONNECT traffic is being validated.

        Returns:
            - tuple[bool, str]: A tuple where:
                - The first value is True if HTTP CONNECT validation is successful, False otherwise.
                - The second value is a message describing the success or failure of validation.
        """
        try:
            # Initialize packet capture for HTTP packets containing the domain
            self.logger.info(f"Starting packet validation for domain: {domain} in pcap file: {pcap_file}.")
            # Parse the pcap file and apply a display filter
            success, message, capture = self.ops_packet.read_packet_from_pcapng_file(pcap_file, filter=f'frame contains "{domain}" && http')
            if not success:
                self.logger.error(message)
                return False, message

            packet_count = len(capture)  # Count the number of packets in the capture
            if packet_count == 0:
                self.logger.error("No packets found in the capture file.")
                return False, f"No http packets found for {domain} in the capture file for validation."

            # try:
            #     capture = pyshark.FileCapture(
            #         pcap_file, 
            #         display_filter=f'frame contains "{domain}" && http',
            #     )
            #     self.logger.info(f"Packet capture initialized successfully for domain: {domain}.")
            # except Exception as e:
            #     self.logger.error(f"Failed to initialize packet capture for pcap file: {pcap_file}.", exc_info=True)
            #     return False, f"Failed to initialize packet capture: {str(e)}"
            
            # Iterate through packets to validate HTTP CONNECT method
            try:
                for packet in capture:
                    if 'HTTP' in packet:
                        self.logger.debug("HTTP packet detected. Processing packet...")
                        try:
                            # Extract HTTP request method
                            http_request_method = packet.http.request_method
                            self.logger.info(f"HTTP Request Method: {http_request_method}")
                            
                            # Check if the request method is CONNECT
                            if http_request_method.upper() == "CONNECT":
                                self.logger.info(f"HTTP CONNECT method detected for domain: {domain}.")
                                return True, "HTTP CONNECT method detected for domain."
                        except AttributeError:
                            self.logger.warning("HTTP request method not found in packet.", exc_info=True)
                            continue
            except Exception as e:
                self.logger.error("Error occurred while processing packets.", exc_info=True)
                return False, f"Error occurred while processing packets: {str(e)}"

            # No HTTP CONNECT method found
            self.logger.info(f"No HTTP CONNECT method detected for domain: {domain}.")
            return False, "No HTTP CONNECT method found for the domain."

        except Exception as e:
            # General exception handler for unexpected errors
            error_message = f"An unexpected error occurred: {str(e)}"
            self.logger.error(error_message, exc_info=True)
            return False, error_message

    def validate_sme_ip(self, pcap_file, domain: str, client_ip:str, sme_ip:str) -> tuple[bool, str]:
        """
        Validate http packets based on connection from client ip to sme server ip.

        Args:
        - pcap_file: Location of the pcapng file.
        - domain: domain to be used as a display filter.
        - client_ip: system's client ipv4 address.
        - sme_ip: sme server ipv4 address.

        Returns:
        - tuple[bool, bool]: A tuple where the first value whether validation is successful or not, and the second value indicates success or failure message.
        """

        # Parse the pcap file and apply a display filter
        success, message, capture = self.ops_packet.read_packet_from_pcapng_file(pcap_file, filter=f'frame contains \"{domain}\"')
        if not success:
            self.logger.error(message)
            return False, message

        packet_count = len(capture)  # Count the number of packets in the capture
        if packet_count == 0:
            self.logger.error("No packets found in the capture file.")
            return False, f"No packets related to {domain} found in the capture file for validation."
        # capture = pyshark.FileCapture(
        #             pcap_file, 
        #             display_filter=f'frame contains \"{domain}\"',
        #         )
        try:
            for packet in capture :
                try :
                    if 'IP' in packet :
                        source_ip = packet.ip.src
                        destination_ip = packet.ip.dst
                        self.logger.info(f"Traffic Source: {source_ip}, Destination: {destination_ip}")
                        if source_ip == client_ip and destination_ip == sme_ip:  
                            self.logger.info(f"Direct connection Found from {client_ip} to {sme_ip}") 
                            return True, f"Direct Connection found from {client_ip} to {sme_ip}"                    
                        else:
                            self.logger.info(f"No connection found from {client_ip} to {sme_ip}")
                except AttributeError:
                    self.logger.warning("Source or destination IP not found in packet. Skipping packet.")
                    continue


        except Exception as e:
                self.logger.error("Error occurred while processing packets.", exc_info=True)
                return False, f"Error occurred while processing packets: {str(e)}"
        
        return False, f"No connection found from {client_ip} to {sme_ip}"
        # return True, f"All HTTP Connection were from {client_ip} to {sme_ip}"        


    def validate_ip(self, pcap_file, domain:str, ipVer6: bool = False) -> tuple[bool, str]:
        """
        Validate packets on the basis of any source ip is ********** or fc00::6440:1 i.e. IPv4 based or IPv6 based.

        Args:
        - pcap_file: Location of the pcapng file.
        - domain: Domain to be used as a display filter.
        - ipVer6: A boolean indicating whether to validate IPv6 traffic or not.

        Returns:
        - tuple[bool, bool]: A tuple where the first value indicates whether validation was success or failed, and the second value indicates success or failure message.
        """
        valid_ipv4 = False
        valid_ipv6 = False

        try:
            # Initialize packet capture for IP packets (IPv4 or IPv6)
            self.logger.info(f"Starting packet validation using pcap file: {pcap_file}.")

            # Parse the pcap file and apply a display filter
            success, message, capture = self.ops_packet.read_packet_from_pcapng_file(pcap_file, filter=f'tls and frame contains "{domain}"')
            if not success:
                self.logger.error(message)
                return False, message

            packet_count = len(capture)  # Count the number of packets in the capture
            if packet_count == 0:
                self.logger.error("No packets found in the capture file.")
                return False, f"No tls packets related to {domain} found in the capture file for validation."

            # try:
                
                # capture = pyshark.FileCapture(
                #     pcap_file, 
                #     display_filter=f'tls and frame contains "{domain}"',
                # )
                # self.logger.info(f"Packet capture initialized successfully for file: {pcap_file}.")
            # except Exception as e:
            #     self.logger.error(f"Failed to initialize packet capture for pcap file: {pcap_file}.", exc_info=True)
            #     return False, f"Failed to initialize packet capture: {str(e)}"

            # Iterate through packets to validate IP traffic
            try:
                for packet in capture:
                    if 'IP' in packet or 'IPV6' in packet:  # Handle IPv4 and IPv6
                        self.logger.debug("Processing packet with IP or IPv6 layer.")
                        try:
                            if 'IP' in packet:  # For IPv4 packets
                                source_ip = packet.ip.src
                                destination_ip = packet.ip.dst
                                self.logger.info(f"IPv4 Traffic Source: {source_ip}, Destination: {destination_ip}")
                                if source_ip == "**********":  # Specific validation for IPv4
                                    self.logger.info(f"Valid IPv4 traffic detected. Source: {source_ip}")
                                    valid_ipv4 = True

                            elif 'IPV6' in packet:  # For IPv6 packets
                                source_ip = packet.ipv6.src
                                destination_ip = packet.ipv6.dst
                                self.logger.info(f"IPv6 Traffic Source: {source_ip}, Destination: {destination_ip}")
                                if ipVer6 and source_ip == "fc00::6440:1":  # Specific validation for IPv6
                                    self.logger.info(f"Valid IPv6 traffic detected. Source: {source_ip}")
                                    valid_ipv6 = True

                        except AttributeError:
                            self.logger.warning("Source or destination IP not found in packet. Skipping packet.")
                            continue
            except Exception as e:
                self.logger.error("Error occurred while processing packets.", exc_info=True)
                return False, f"Error occurred while processing packets: {str(e)}"

            # Validation Results
            if ipVer6 and valid_ipv6:
                self.logger.info("IPv6 validation passed successfully.")
                return True, "IPv6 validation passed."
            elif ipVer6 and not valid_ipv6:
                self.logger.warning("IPv6 validation failed. No valid IPv6 traffic detected.")
                return False, "IPv6 validation failed."
            elif valid_ipv4:
                self.logger.info("IPv4 validation passed successfully.")
                return True, "IPv4 validation passed."
            elif not valid_ipv4:
                self.logger.warning("IPv4 validation failed. No valid IPv4 traffic detected.")
                return False, "IPv4 validation failed."

        except Exception as e:
            # General exception handler for unexpected errors
            error_message = f"An unexpected error occurred: {str(e)}"
            self.logger.error(error_message, exc_info=True)
            return False, error_message


    def validate_direct_connection(self, pcap_file: str, domain: str, client_ip:str) -> tuple[bool, str]:
        """
        Validate if there's a direct connection from the client IP to one of the destination IPs in the domain_ip set.

        Args:
            pcap_file (str): Path to the pcap file to capture packets.
            domain (str): The domain name to look for in packets.
            domain_ip (set): A set of destination IPs to check against.

        Returns:
            tuple[bool, str]: A tuple with:
                - A boolean indicating whether a direct connection was found.
                - A message with the result details.
        """
        try:
            result = self.sys_ops.fetch_application_resolved_ip(pcap_file_url=pcap_file, domain=domain)
            if not result[0]:
                self.logger.error("Failed to fetch application resolved IP's", exc_info=True)
                return result[0], result[1]
            else :
                domain_ip = result[2]
                self.logger.info(f"Fetched all IP's that the application resolved to :- {domain_ip}")
        except Exception as e:
            self.logger.error("Failed to fetch application resolved IP's", exc_info=True)
            return False, f"Failed to fetch application resolved IP's, error : {str(e)}"
        try:
            # Check if the given domain_ip is valid
            if not isinstance(domain_ip, set):
                self.logger.error(f"Invalid argument: domain_ip must be a set, but got {type(domain_ip)}.")
                return False, "Invalid argument: domain_ip must be a set of IP addresses."

            if not domain_ip:
                self.logger.warning("The domain_ip set is empty.")
                return False, "Domain IP set is empty. Cannot validate direct connection."

            # Capture packets from the provided pcap file
            self.logger.info(f"Capturing packets from pcap file: {pcap_file}. Filtering for domain: {domain}.")
            # Parse the pcap file and apply a display filter
            success, message, capture = self.ops_packet.read_packet_from_pcapng_file(pcap_file, filter=f'tls and frame contains "{domain}"')
            if not success:
                self.logger.error(message)
                return False, message

            packet_count = len(capture)  # Count the number of packets in the capture
            if packet_count == 0:
                self.logger.error("No packets found in the capture file.")
                return False, f"No tls packets related to {domain} found in the capture file for validation."
            # capture = pyshark.FileCapture(
            #     pcap_file, 
            #     display_filter=f'tls and frame contains "{domain}"'
            # )

            # Iterate through packets to validate direct connection
            for packet in capture:
                try:
                    # Handle IPv4 packets
                    if 'IP' in packet:  
                        source_ip = packet.ip.src
                        destination_ip = packet.ip.dst
                        self.logger.debug(f"Processing IPv4 packet - Source IP: {source_ip}, Destination IP: {destination_ip}")

                        if source_ip == client_ip and destination_ip in domain_ip:
                            self.logger.info(f"Direct connection found from {client_ip} to {destination_ip}.")
                            return True, f"Found a direct connection from client IP {client_ip} to destination IP {destination_ip}."

                    # Handle IPv6 packets
                    elif 'IPV6' in packet:
                        source_ip = packet.ipv6.src
                        destination_ip = packet.ipv6.dst
                        self.logger.debug(f"Processing IPv6 packet - Source IP: {source_ip}, Destination IP: {destination_ip}")

                        if source_ip == client_ip and destination_ip in domain_ip:
                            self.logger.info(f"Direct connection found from {client_ip} to {destination_ip}.")
                            return True, f"Found a direct connection from client IP {client_ip} to destination IP {destination_ip}."

                    else:
                        self.logger.warning("Packet does not contain IP or IPv6 layer.")
                
                except AttributeError as e:
                    # Handle packets without required attributes (ignore them)
                    self.logger.warning(f"Packet missing expected attributes: {str(e)}", exc_info=True)
                    continue
                except Exception as e:
                    # Log any unexpected errors while processing packets
                    self.logger.error(f"Error processing packet: {str(e)}", exc_info=True)
                    continue

            # If no direct connection is found after processing all packets
            self.logger.info(f"No direct connection found from client IP {client_ip} to any destination IP in the domain_ip set.")
            return False, f"No direct connection found from client IP {client_ip} to any destination IP in the set."

        except FileNotFoundError:
            self.logger.error(f"Pcap file not found: {pcap_file}.", exc_info=True)
            return False, f"Pcap file not found: {pcap_file}."

        except Exception as e:
            # Log unexpected errors
            self.logger.error(f"An unexpected error occurred: {str(e)}", exc_info=True)
            return False, f"An unexpected error occurred: {str(e)}."

    def run_packet_capture_and_access_application(
        self,
        output_file: str,
        capture_duration: int,
        browser: str,
        url: str,
        ios_udid: str,
    ) -> tuple[bool, str]:
        """
        Function to run packet capture on iOS and access an application, ensuring both processes run successfully.

        Args:
            - output_file (str): File path where captured packets will be saved.
            - capture_duration (int): Duration for which packets will be captured, in seconds.
            - browser (str): Browser to launch for accessing the domain (e.g., Safari).
            - url (str): URL to access during the packet capture.
            - ios_udid (str): Unique Device Identifier (UDID) of the iOS device.
            
        Returns:
            - tuple[bool, str]: A tuple where:
                - The first value is True if successful, False if an error occurred.
                - The second value is a message describing the success or failure.
        """
        try:
            # Activate the mirrored RVI interface for the iOS device
            self.logger.info(f"Activating device with UDID: {ios_udid}.")
            try:
                ios_interface = self.ops_packet.activate_device(udid=ios_udid)
                self.logger.info(f"Successfully activated mirrored interface: {ios_interface}.")
            except Exception as e:
                self.logger.error(f"Failed to activate device with UDID {ios_udid}.", exc_info=True)
                return False, f"Failed to activate device with UDID {ios_udid}: {str(e)}"

            # Fetch list of available interfaces
            self.logger.info("Fetching capturable interfaces from the system.")
            try:
                list_of_capturable_interfaces = self.ops_packet.fetch_capturable_interfaces()[-1]
                self.logger.info(f"Available interfaces: {list_of_capturable_interfaces}.")
            except Exception as e:
                self.logger.error("Failed to fetch capturable interfaces.", exc_info=True)
                return False, f"Failed to fetch capturable interfaces: {str(e)}"

            # Validate that the required interface is available
            if ios_interface not in list_of_capturable_interfaces:
                error_message = f"Interface not available: {ios_interface}. Capturable interfaces: {list_of_capturable_interfaces}."
                self.logger.error(error_message)
                return False, error_message

            # Initialize Packet Capture Thread
            self.logger.info(f"Initializing packet capture for {capture_duration} seconds on interface {ios_interface}.")
            try:
                pkt_thread = threading.Thread(
                    target=self.ops_packet.start_packet_capture,
                    args=(
                        output_file,
                        [ios_interface],
                        capture_duration
                    )
                )
            except Exception as e:
                self.logger.error("Failed to initialize packet capture.", exc_info=True)
                return False, f"Failed to initialize packet capture: {str(e)}"

            # Initialize Access Application Thread
            self.logger.info(f"Initializing browser access to URL: {url} using {browser}.")
            try:
                pkt_thread2 = threading.Thread(
                    target=self.sys_ops.access_application,
                    kwargs={
                        "browser": browser,
                        "url": url,
                        "close_tabs": True,
                    }
                )
            except Exception as e:
                self.logger.error("Failed to initialize application access thread.", exc_info=True)
                return False, f"Failed to initialize application access thread: {str(e)}"

            # Start Threads
            self.logger.info("Starting packet capture thread.")
            try:
                pkt_thread.start()
            except Exception as e:
                self.logger.error("Failed to start packet capture thread.", exc_info=True)
                return False, f"Failed to start packet capture thread: {str(e)}"

            time.sleep(2)  # Allow packet capture initialization
            self.logger.info("Starting application access thread.")
            try:
                pkt_thread2.start()
            except Exception as e:
                self.logger.error("Failed to start application access thread.", exc_info=True)
                return False, f"Failed to start application access thread: {str(e)}"

            # Wait for Both Threads to Complete
            self.logger.info("Waiting for both threads to complete.")
            try:
                pkt_thread.join()
                pkt_thread2.join()
                self.logger.info("Both threads completed successfully.")
            except Exception as e:
                self.logger.error("Failed while waiting for threads to complete.", exc_info=True)
                return False, f"Failed while waiting for threads to complete: {str(e)}"
            
            # Change file permissions for the output file
            self.logger.info(f"Changing file permissions for {output_file}.")
            try:
                command = ["sudo", "chmod", "644", output_file]
                subprocess.run(command, check=True, text=True, stderr=subprocess.PIPE)
                self.logger.info(f"File permissions changed successfully for {output_file}.")
            except subprocess.SubprocessError as e:
                self.logger.error(f"Failed to change permissions for {output_file}.", exc_info=True)
                return False, f"Failed to change permissions for {output_file}: {str(e)}"

            # If everything succeeds
            self.logger.info("Packet capture and application access completed successfully.")
            return True, "Packet capture and application access completed successfully."

        except Exception as e:
            # Catch any unexpected exceptions
            error_message = f"Unexpected error occurred: {str(e)}"
            self.logger.error(error_message, exc_info=True)
            return False, error_message

    

    def analyze_packets(self, pkt_file_url: str, ios_udid:str, duration:int = 30, browser:str="safari", urlv4:str = None, urlv6:str=None,  domainv4_filter:str = None, bypass:bool = False, ipv6:bool = False, tunnel_ver:int = 1) -> tuple[bool, str]:
        
        res = self.fetch_client_ipv4()
        if not res[0]:
            return res[0], res[1]
        else:
            client_ip = res[2]
        res = self.fetch_sme_ip()
        if not res[0]:
            return res[0], res[1]
        else:
            sme_ip = res[2]

        self.sys_ops.delete_pcap_file(path=pkt_file_url)

        if tunnel_ver == 1:

            if not ipv6:
                result = self.run_packet_capture_and_access_application(output_file=pkt_file_url, capture_duration=duration, browser=browser, url=urlv4, ios_udid=ios_udid)
                if not result[0]:
                    return result

                if not bypass:
                    result = self.validate_http_connect(pcap_file=pkt_file_url, domain=domainv4_filter)
                    if not result[0]:
                        return result

                    result = self.validate_sme_ip(pcap_file=pkt_file_url, domain=domainv4_filter, client_ip=client_ip, sme_ip=sme_ip)
                    if not result[0]:
                        return result

                    result = self.validate_ip(pcap_file=pkt_file_url, domain=domainv4_filter)
                    if not result[0]:
                        return result
                
                else:
                    result = self.validate_http_connect(pcap_file=pkt_file_url, domain=domainv4_filter)
                    if result[0]:
                        return False, result[1]
                    
                    result = self.validate_sme_ip(pcap_file=pkt_file_url, domain=domainv4_filter, client_ip=client_ip, sme_ip=sme_ip)
                    if result[0]:
                        return False, result[1]

                    result = self.validate_direct_connection(pcap_file=pkt_file_url, domain=domainv4_filter, client_ip=client_ip)
                    if not result[0]:
                        return result

            
            self.sys_ops.delete_pcap_file(path=pkt_file_url)
            return True, "Analyzed packets successfully, and passed conditions successfully."
        
        else:
            if not ipv6:
                result = self.run_packet_capture_and_access_application(output_file=pkt_file_url, capture_duration=duration, browser=browser, url=urlv4, ios_udid=ios_udid)
                if not result[0]:
                    return result

                if not bypass:
                    result = self.validate_http_connect(pcap_file=pkt_file_url, domain=domainv4_filter)
                    if result[0]:
                        return False, result[1]

                    result = self.validate_sme_ip(pcap_file=pkt_file_url, domain=domainv4_filter, client_ip=client_ip, sme_ip=sme_ip)
                    if result[0]:
                        return False, result[1]

                    result = self.validate_direct_connection(pcap_file=pkt_file_url, domain=domainv4_filter, client_ip=client_ip)
                    if result[0]:
                        return False, result[1]

                    result = self.validate_ip(pcap_file=pkt_file_url, domain=domainv4_filter)
                    if not result[0]:
                        return result
                
                else:
                    result = self.validate_http_connect(pcap_file=pkt_file_url, domain=domainv4_filter)
                    if result[0]:
                        return False, result[1]

                    result = self.validate_sme_ip(pcap_file=pkt_file_url, domain=domainv4_filter, client_ip=client_ip, sme_ip=sme_ip)
                    if result[0]:
                        return False, result[1]

                    result = self.validate_direct_connection(pcap_file=pkt_file_url, domain=domainv4_filter, client_ip=client_ip)
                    if not result[0]:
                        return result


            self.sys_ops.delete_pcap_file(path=pkt_file_url)
            return True, "Analyzed packets successfully, and passed conditions successfully."

    def concatenate_strings(self, application_ip:set) -> str:
        """
        Concatenates strings from a set, separating each item with a comma.
        
        Args:
            input_set (set): A set of strings to concatenate.
        
        Returns:
            str: A single string with all items concatenated and separated by commas.
        """
        input_set = application_ip
        if not input_set:
            return ""
        
        try:
            # Join the set elements into a single string using a comma as a delimiter
            concatenated_string = ", ".join(input_set)
            self.logger.info(f"Final Concatenated String is : {concatenated_string}")
            return concatenated_string
        except Exception as e:
            # Handle any unexpected errors
            raise ValueError(f"Error concatenating strings from the set: {str(e)}")
    
    def validate_dns_inclusion(self, pcap_file: str, domain_urlv4: str, ipv6:bool = False) -> tuple[bool, str]:
        """
        Validate dns inclusion by ensuring none of the dns packets contain system dns ip either in ip.src or ip.dst in the pcap file.

        Args:
            - pcap_file (str): Path to the pcapng file for parsing packets.
            - dns_server_ip (set): The IP address of the system DNS server.
            - domain (str): domain name which we have accessed and will be used as a display filter.

        Returns:
            - tuple[bool, str]: A tuple where:
                - The first value is True if dns inclusion validation passes, False otherwise.
                - The second value contains a message describing success or failure.
        """
        domain_urlv4 = domain_urlv4.replace("https://", "")
        if not ipv6 :
            zcc_interface_ip = "**********"
        else :
            zcc_interface_ip = "fc00::6440:1"

        try:
            # Parse the pcap file and apply a DNS filter
            self.logger.info(f"Parsing pcap file: {pcap_file}")

            # Parse the pcap file and apply a display filter
            success, message, capture = self.ops_packet.read_packet_from_pcapng_file(pcap_file, filter=f'dns.qry.name == "{domain_urlv4}"')
            if not success:
                self.logger.error(message)
                return False, message

            packet_count = len(capture)  # Count the number of packets in the capture
            if packet_count == 0:
                self.logger.error("No packets found in the capture file.")
                return False, f"No dns packets related to {domain_urlv4} found in the capture file for validation."

            # try:
            #     capture = pyshark.FileCapture(
            #         pcap_file, 
            #         display_filter=f'dns.qry.name == "{domain_urlv4}"'  # DNS packets only
            #     )
            # except Exception as e:
            #     self.logger.error(f"Failed to parse pcap file: {pcap_file}.", exc_info=True)
            #     return False, f"Failed to parse pcap file: {str(e)}"

            # packet_count = sum(1 for _ in capture)  # Count the number of packets in the capture
            # if packet_count == 0:
            #     self.logger.error("No packets found in the capture file.")
            #     return False, "No DNS packets found in the capture file for validation."


            # Initialize a flag for validation
            not_system_dns = True

            try:
                for packet in capture:
                    if 'DNS' in packet:
                        try:
                            source_ip = packet.ip.src  # Source IP of the DNS request
                            destination_ip = packet.ip.dst  # Destination IP (DNS server IP)
                            # Extract DNS transaction ID
                            dns_transaction_id = packet.dns.id  # Transaction ID field

                            # Log each detected DNS request
                            self.logger.debug(f"DNS Request - Source: {source_ip}, Destination: {destination_ip}")

                            # Check if the request matches client IP -> DNS server IP validation
                            if source_ip != zcc_interface_ip and destination_ip != zcc_interface_ip :
                                self.logger.error("DNS packets are not being intercepted by ZCC.")
                                not_system_dns = False
                                break
                        except AttributeError:
                            self.logger.warning("Packet missing required attributes - Source or Destination IP.")
                            continue
            except Exception as e:
                self.logger.error("Error occurred while processing packets.", exc_info=True)
                return False, f"Error occurred while processing packets: {str(e)}"

            # Validate final result
            if not_system_dns:
       
                self.logger.info("Dns inclusion validation passed successfully.")
                return True, "Dns inclusion validation passed successfully."
            else:
                self.logger.error("ZCC Interface IP was not involved in DNS packets i.e. no DNS interception.")
                return False, "Dns inclusion validation failed: ZCC Interface IP was not involved in DNS packets i.e. no DNS interception."

        except Exception as e:
            # Log unexpected errors
            self.logger.error(f"An unexpected error occurred: {str(e)}", exc_info=True)
            return False, f"An unexpected error occurred: {str(e)}"
        
        # self, pcap_file:str, ipv6:bool = False, dns_server_ip:set = None, client_ip:str = None, domain:str = None
    def validate_dns_exclusion(self, pcap_file: str, domain_urlv4: str, ipv6:bool = False) -> tuple[bool, str]:
        """
        Validate dns exclusion by ensuring none of the dns packets contain zcc interface ip both in ip.src and ip.dst in the pcap file.

        Args:
            - pcap_file (str): Path to the pcapng file for parsing packets.
            - domain_urlv4 (str): domain url
            - ipv6 (bool): flag for ipv6

        Returns:
            - tuple[bool, str]: A tuple where:
                - The first value is True if dns exclusion validation passes, False otherwise.
                - The second value contains a message describing success or failure.
        """
        domain_urlv4 = domain_urlv4.replace("https://", "")
        if not ipv6 :
            zcc_interface_ip = "**********"
        else :
            zcc_interface_ip = "fc00::6440:1"

        try:
            # Parse the pcap file and apply a DNS filter
            self.logger.info(f"Parsing pcap file: {pcap_file}")
            # Parse the pcap file and apply a display filter
            success, message, capture = self.ops_packet.read_packet_from_pcapng_file(pcap_file, filter=f'dns.qry.name == "{domain_urlv4}"')
            if not success:
                self.logger.error(message)
                return False, message

            packet_count = len(capture)  # Count the number of packets in the capture
            if packet_count == 0:
                self.logger.error("No packets found in the capture file.")
                return False, f"No dns packets related to {domain_urlv4} found in the capture file for validation."
            # try:
            #     capture = pyshark.FileCapture(
            #         pcap_file, 
            #         display_filter=f'dns.qry.name == "{domain_urlv4}"'  # DNS packets only
            #     )
            # except Exception as e:
            #     self.logger.error(f"Failed to parse pcap file: {pcap_file}.", exc_info=True)
            #     return False, f"Failed to parse pcap file: {str(e)}"

            # packet_count = sum(1 for _ in capture)  # Count the number of packets in the capture
            # if packet_count == 0:
            #     self.logger.error("No packets found in the capture file.")
            #     return False, "No DNS packets found in the capture file for validation."


            # Initialize a flag for validation
            no_dns_interception = False

            try:
                for packet in capture:
                    if 'DNS' in packet:
                        try:
                            source_ip = packet.ip.src  # Source IP of the DNS request
                            destination_ip = packet.ip.dst  # Destination IP (DNS server IP)

                            # Log each detected DNS request
                            self.logger.debug(f"DNS Request - Source: {source_ip}, Destination: {destination_ip}")

                            # Check if the request matches client IP -> DNS server IP validation
                            if source_ip != zcc_interface_ip and destination_ip != zcc_interface_ip :
                                self.logger.error("DNS packets are not being intercepted by ZCC.")
                                no_dns_interception = True
                                break
                        except AttributeError:
                            self.logger.warning("Packet missing required attributes - Source or Destination IP.")
                            continue
            except Exception as e:
                self.logger.error("Error occurred while processing packets.", exc_info=True)
                return False, f"Error occurred while processing packets: {str(e)}"

            # Validate final result
            if no_dns_interception:
                self.logger.info("Dns Exclusion validation passed successfully.")
                return True, "Dns Exclusion validation passed successfully."
            else:
                self.logger.error("ZCC Interface IP was involved in DNS packets i.e. DNS interception by ZCC.")
                return False, "Dns Exclusion validation failed: ZCC Interface IP was involved in DNS packets i.e. DNS interception by ZCC."

        except Exception as e:
            # Log unexpected errors
            self.logger.error(f"An unexpected error occurred: {str(e)}", exc_info=True)
            return False, f"An unexpected error occurred: {str(e)}"
        
        
    def validate_bypass(self, pcap_file, domain_filter, client_ip, sme_ip, bypass):
        """
        Validates the bypass logic using multiple validators methods written above.
        
        Returns:
            (bool, str) - Tuple of success status and message.
        """
        if not bypass:
            result = self.validate_http_connect(pcap_file=pcap_file, domain=domain_filter)
            if not result[0]:
                return result

            result = self.validate_sme_ip(pcap_file=pcap_file, domain=domain_filter, client_ip=client_ip, sme_ip=sme_ip)
            if not result[0]:
                return result
            
            result = self.validate_ip(pcap_file=pcap_file, domain=domain_filter)
            if not result[0]:
                return result
            
            return True, "Validation pass"
                
        else:

            result = self.validate_http_connect(pcap_file=pcap_file, domain=domain_filter)
            if result[0]:
                return False, result[1]
            
            result = self.validate_sme_ip(pcap_file=pcap_file, domain=domain_filter, client_ip=client_ip, sme_ip=sme_ip)
            if result[0]:
                return False, result[1]

            return True, "Bypass Validation passed"


import copy, json, os, re
import time
import socket
import wget
from OS_Ios.library.ops_packet import OpsPacket
# from OS_Ios.library.ops_system_network import SysOpsNetworkSetup
from common_lib.adminzia import webinsights,ziah<PERSON>per,samlzia,sslpolicy, ziapac
from common_lib.common import constants
from common_lib.common.logger import Logger
from common_lib.common.log_ops import LogOps
from common_lib.mobileadmin import appprofile, serviceentitlement, clientconnectornotifications, clientconnectorappstore, trustednetwork, deviceoverview, serviceentitlement, deviceposture, clientconnectorsupport,platformsettings
from common_lib.adminzpa import zpapolicy
from OS_Ios.library.ops_system import SysOps
from OS_Ios.library.ui_zcc import Zcc
from OS_Ios.library.ops_fetchfromlogs import FetchFromLogs
from packaging.version import Version
import ipaddress

class ZccSanity:
    def __init__(self, cloud, config, log_handle=None, zcc_object=None):
        self.cloud = cloud
        self.const = constants.ZccSanityConst()
        self.logger = log_handle if log_handle != None else Logger.initialize_logger("ZCC_Sanity.log", log_level="DEBUG")
        self.log_ops = LogOps(log_handle=self.logger)
        # self.zcc = zcc_object if zcc_object!=None else Zcc(log_handle=self.logger, start_app=True)
        self.fetch_from_logs = FetchFromLogs()
        self.ma_client_connector_app_store =  clientconnectorappstore.ClientConnectorAppStore(cloud=cloud, config=config, log_handle=self.logger)
        self.ma_client_connector_support =  clientconnectorsupport.ClientConnectorSupport(cloud=cloud, config=config, log_handle=self.logger)
        self.ma_trusted_network = trustednetwork.TrustedNetwork(cloud=cloud, config=config, log_handle=self.logger)
        self.ma_app_profile = appprofile.AppProfile(cloud=cloud, config=config, log_handle=self.logger)
        self.ma_clientconnectornotifications = clientconnectornotifications.ClientConnectorNotifications(cloud=cloud, config=config, log_handle=self.logger)
        self.ma_serviceentitlement = serviceentitlement.ServiceEntitlement(cloud=cloud, config=config, log_handle=self.logger)
        self.ma_device_overview = deviceoverview.DeviceOverview(cloud=cloud, config=config, log_handle=self.logger)
        self.ma_device_posture = deviceposture.DevicePosture(cloud=cloud, config=config, log_handle=self.logger)
        self.zpa_policy = zpapolicy.ZpaPolicy(cloud=cloud, config=config, log_handle=self.logger)
        self.zia = webinsights.WebInsights(cloud=cloud, config=config, log_handle=self.logger)
        # self.sys_ops = self.zcc.sys_ops
        self.const_tunnel_sanity=constants.TunnelSanity()
        self.zia_helper=ziahelper.ZiaHelper(cloud=cloud, config=config, log_handle=self.logger)
        self.saml_zia=samlzia.SamlZia(cloud=cloud, config=config, log_handle=self.logger)
        self.ma_platform_settings=platformsettings.PlatformSettings(cloud=cloud, config=config, log_handle=self.logger)
        self.ssl_policy=sslpolicy.SslPolicy(cloud=cloud, config=config, log_handle=self.logger)

        self.ops_packet = OpsPacket(log_handle=self.logger)
        self.zia_pac=ziapac.ZiaPac(cloud=cloud, config=config, log_handle=self.logger)

        self.logger.info("ZCC Sanity Initialized !\n")

    def log_search_trusted_network(
        self, 
        fail_expected:bool=False,       # Expect failure       
        split_vpn:bool=False            # Defines if we log search should fail or not
    ):
        """
        Work Flow
        ----------
            1. Get created trusted networks name from admin_ops file
            2. Search for the trusted networks in the logs
            3. If failExcpected variable is false, then we expect logs not to be present
            4. split_vpn variable defines if we are looking for split vpn trusted network
        """
        try:
            self.logger.info("CREATED NETWORKS ARE : {}".format(self.ma_trusted_network.created_trusted_networks))
            created_trusted_networks = ["Trusted Networks: "]
            created_trusted_networks.extend(self.ma_trusted_network.created_trusted_networks)
            if split_vpn:
                self.logger.info("SEARCHING :: {}".format(created_trusted_networks))
                created_trusted_networks.append("INF TRUSTED_SPLIT_VPN detected")
            criteria_matched = False

            for network in created_trusted_networks:
                print("Searching for : " + network)
                self.log_ops.search_log_file(
                    file="ZSATunnel",
                    words_to_find_in_line=[network],
                    search_mode=None,
                    start_line_number=self.log_ops.recent_log_line_number,
                )
                if (
                    "INF detectCurrentNetworkType on net criteria matched for network !: " + network
                    in self.log_ops.recent_log_line
                ):
                    criteria_matched = True
                    log_line = self.log_ops.recent_log_line.split("INF")[-1]

            if fail_expected:
                if criteria_matched:
                    raise Exception("Failuer was expected but criteria still matched")
                else:
                    self.logger.info(
                        "\nSUCCESS :: Failuer was expected, logs were found, but criteria for trusted network not matched\n"
                    )
            else:
                if criteria_matched:
                    print("\nTrusted Network Criteria Matched Found in logs\n")
                    self.logger.info(log_line)
                else:
                    self.logger.info("\nLogs were found, but criteria didnt matched !!\n")
                    raise Exception("Trusted Network criteria not matched in logs")
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,"Success :: Failuer was expected, logs were found, but criteria for trusted network not matched" if fail_expected else "Success :: Trusted Network Criteria Matched Found in logs",None)

    def verify_app_added_to_bypass_list(
        self,               # Function to make sure app added to bypasses list in tunnel logs
        domain:str=None,    # (str)     domain that is added to App Profile in MA
        ip:str=None,        # (str)     ip that is added to App Profile in MA
    ):
        """
        Work Flow
        ----------
            1. Make Sure domain or ip is provided
            2. if domain is Provided, look for a certain set of keywords and lines for validation
            3. if ip is provided, look for a different set of keywords and lines for validation
        """
        try:
            if domain == None and ip == None:
                raise Exception("Error :: Please Provide ip/domain")
            threshold_line_number = copy.deepcopy(self.log_ops.recent_log_line_number)
            current_zcc_version = self.zcc.sys_ops.fetch_application_version()
            current_zcc_version = current_zcc_version[-1]
            if ip:
                subnet = False if str(ipaddress.IPv4Network(ip,strict=False).netmask)=="***************" else True
            if Version(current_zcc_version) <= Version("*******"): 
                if domain:
                    keyword_pairs = [
                        ["addHostToProxyBypassList", domain]
                    ]
                else:
                    keyword_pairs = [
                        ["Adding ip to exclude list", ip]
                    ]
            else:
                if domain:
                    keyword_pairs = [
                            ["addHostToProxyBypassList", domain]
                        ]
                else:
                    if subnet:
                        keyword_pairs = [
                                ["ZSVpnBypassRouteManager", ip]
                            ]
                    else :
                        keyword_pairs = [
                                ["addHostToProxyBypassList", ip]
                            ]
            for every_set in keyword_pairs:
                try:
                    self.log_ops.search_log_file(
                        file="ZSATunnel",
                        search_mode=None,
                        start_line_number=threshold_line_number,
                        words_to_find_in_line=every_set,
                    )
                except Exception as e:
                    raise Exception(
                        e, "Search parameters: {} ; {} ; {}".format(None, threshold_line_number, str(every_set))
                    )
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,"Success :: App is added to bypass list",None)

    def verify_app_added_to_include_exclude_list(
        self,                   # Function to verify if an App is added to Include/Exclude List
        ip:str=None,            #  ip is added to App Profile in MA
        port=None,              # (str)     port number/range for the ip    (ex: '80'/'80 - 443')
        protocol=None,          # (str)     protocol for the ip ('TCP'/'UDP')
    ):  
        """
        Work Flow
        ----------
            1. Make sure ip is provided
            2. search through the log file for keywords and lines for validation
        """
        try:
            if ip == None:
                raise Exception("Error :: Please Provide ip")
            threshold_line_number = copy.deepcopy(self.log_ops.recent_log_line_number)
            ip = ip.split(":")[0]
            network_ip = str(ipaddress.IPv4Network(ip,strict=False).network_address)
            mask = str(ipaddress.IPv4Network(ip,strict=False).netmask)
            log_list=[
                    [network_ip, mask,"Protocol: {}".format(protocol if protocol else "Any"),"DST_PORT", port if port else "0 - 0"]
            ]
            for every_word_list in log_list:
                try:
                    self.log_ops.search_log_file(
                        file="ZSATunnel",
                        search_mode=None,
                        start_line_number=threshold_line_number,
                        words_to_find_in_line=every_word_list,
                    )
                except Exception as e:
                    raise Exception(e, "Search Parameters: {} ; {} ; {}".format(None, threshold_line_number, str(every_word_list)))
        except Exception as e:
            return False,f"Error :: {e}",None
        return True,"Success :: App is added to Include/Exclude List",None

    def validate_tunnel_mode_from_logs(
        self, 
        tunnel_version:str=None,  # Takes values such as tunnel_1, twlp, tunnel_2_dtls, tunnel_2_tls
    ):
        """
            Validate tunnel mode from logs.
            This function validates the tunnel mode from logs based on the provided tunnel version.
            Args:
                tunnel_version (str): Tunnel version to search for in logs.
            Returns:
                tuple: A tuple containing a boolean status, a message, and an exception (if any).
        """
        try:
            if tunnel_version == None:
                line = "No tunnel Version provided to search!"
                self.logger.error(line)
                raise Exception(line)

            file = "ZSATunnel"
            search_for = constants.ZccSanityConst.TUNNEL_LOGS_LIST[tunnel_version] 
            for each_index in range(0, len(search_for)):
                if ":UP" in search_for[each_index]:
                    search_for[each_index] = search_for[each_index].replace(":UP", ": UP")

            print("Search list is {}".format(search_for))
            self.log_ops.search_log_file(file=file, words_to_find_in_line=search_for, wait_time=10, start_timestamp=self.log_ops.start_time)
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,"Success :: Tunnel mode validated",None)
    
    def validate_certificate(
        self, 
        tunnel_version:str=None,                # Takes values such as tunnel_1, twlp, tunnel_2_dtls, tunnel_2_tls
        application_to_visit:str = "ndtv.com",  # What url should be accessed
        is_zcert_expected:bool = True,          # Should the application go via zscaler
    ):
        """
            Validate certificate based on the tunnel version.

            :param tunnel_version: (str) takes values such as tunnel_1, twlp, tunnel_2_dtls, tunnel_2_tls, enforce_proxy
            :return: (tuple) status, message, exception (if any)
        """
        try:
            if tunnel_version == None:
                line = "No tunnel Version provided to search!"
                self.logger.error(line)
                raise Exception(line)

            line = "Checking for {} mode".format(tunnel_version)
            self.logger.info(line)

            if tunnel_version == "tunnel_1" or tunnel_version == "tunnel_2_dtls" or tunnel_version == "tunnel_2_tls":
                app_access = self.sys_ops.access_application(application_to_visit, is_zcert=is_zcert_expected)
                if app_access[0]==False:
                    raise Exception(app_access[2])

            else:
                app_access =self.sys_ops.access_application(application_to_visit, is_zcert=False)
                if app_access[0]==False:
                    raise Exception(app_access[2])
                app_access =self.sys_ops.access_application(application_to_visit, proxy=True, is_zcert=is_zcert_expected)
                if app_access[0]==False:
                    raise Exception(app_access[2])
        except Exception as e:
            return False,f"Error while validating zscaler cert: {e}",None
        return True,f"Success, Certificate validated, Should traffic go via zscaler: {is_zcert_expected}",None 

    def get_cloud_and_policy_token(self):
        """
            This function gets the cloud and policy token.

            Returns:
                tuple: A tuple containing a boolean status, a message, and a tuple of policy token and get cloud.
        """
        try:
            time.sleep(10)
            result_list = self.ma_app_profile.get_app_profile_details(operating_system='Mac')
            if result_list[0]:
                raise Exception(result_list[1])
            result_list = result_list[2]
            self.policy_token = result_list[1]
            self.get_cloud = result_list[2]

            print("Policy Token is {}, getCloud is {}".format(self.policy_token, self.get_cloud))
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,"Success :: Policy Token is {}, getCloud is {}".format(self.policy_token, self.get_cloud),(self.policy_token, self.get_cloud))

    def validate_zcc_port(
        self, orignal_port=True  # (bool) to check for orignal port for ZCC (true) or custostom port for ZCC (false)
    ):
        """
            Validate ZCC port.

            :param self: Instance of the class.
            :type self: object
            :param orignal_port: Boolean to check for original port for ZCC (true) or custom port for ZCC (false), defaults to True.
            :type orignal_port: bool, optional
            :return: Tuple containing status, message, and exception (if any).
            :rtype: tuple
        """
        try:
            file = "ZSATunnel"
            lines = (
                constants.ZccSanityConst.LOGS_FOR_ZCC_PORT_CHANGE["orignal"]
                if orignal_port == True
                else constants.ZccSanityConst.LOGS_FOR_ZCC_PORT_CHANGE["changed"]
            )

            for line in lines:
                self.logger.info("Searching for {}".format(line))
                self.log_ops.search_log_file(file=file, words_to_find_in_line=line)
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,"Success :: Validated zcc port",None)

    def validate_driver_logs(self, driver="tap"):  # (str) which reiver to validate tap/lwf
        """
            Validate driver logs.

            This function validates the driver logs for a given driver, either tap or lwf.
            It searches for specific logs in the ZSATunnel file.

            Args:
                driver (str): The driver to validate, either "tap" or "lwf". Default is "tap".

            Returns:
                tuple: A tuple containing a boolean indicating success or failure, a message, and an exception if applicable.
        """
        try:
            logs_to_search = constants.ZccSanityConst.DRIVER_LOGS[driver]

            file = "ZSATunnel"

            self.log_ops.search_log_file(file=file, words_to_find_in_line=logs_to_search)
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,"Success :: Validated driver logs",None)

    def validate_remote_fetch_logs_ma(
        self,
        verification=True,  # (bool) This will enable the function to validate Remote Fetch Logs if True else it will jus return the device ID
    ):
        """
            This function validates remote fetch logs from MA.

            :param self: Instance of the class
            :param verification: (bool) This will enable the function to validate Remote Fetch Logs if True else it will jus return the device ID
            :return: (tuple) A tuple containing a boolean status, a message, and the enrolled device ID (if available)
        """
        try:
            validated = False
            count = 0
            self.log_url = None
            self.enrolled_device_id = None

            found = {"Waiting for KeepAlive": False, "Extraction in progress": False, "Completed": False}

            while validated == False and count <= 4:
                self.logger.info("Request sent {} :: Getting Data for Perticular Device".format(count + 1))
                data = self.ma_device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
                if not data[0]:
                    raise Exception(data[1])
                data = data[2]
                print(json.dumps(data, indent=4))

                if self.enrolled_device_id == None:
                    self.enrolled_device_id = data["id"]

                if verification == False:
                    return (True,"Success :: Returning the Device ID", self.enrolled_device_id)

                data = data["logFetchInfo"]

                if count == 0 and data["logFetchStatus"] == "Waiting for KeepAlive":
                    found["Waiting for KeepAlive"] = True
                else:
                    if found["Waiting for KeepAlive"] == False:
                        self.logger.info("MINOR ERROR :: Waiting for KeepAlive not found with zero keepAlive")

                if data["logFetchStatus"] == "Extraction in progress":
                    found["Extraction in progress"] = True

                if data["logFetchStatus"] == "Completed":
                    if "logUrl" in data:
                        self.log_url = data["logUrl"]
                        found["Completed"] = True
                        self.logger.info(
                            "Fetched Logs are at URL {}\nValues on MA are found are :: Waiting for KeepAlive {}, Extraction in progress {}, Completed {}".format(
                                self.log_url,
                                found["Waiting for KeepAlive"],
                                found["Extraction in progress"],
                                found["Completed"],
                            )
                        )
                        validated = True
                        break
                    else:
                        line = "logFetchStatus is Completed but no URL is present!"
                        self.logger.error(line)
                        raise Exception(line)

                self.logger.info("Updating Policy from remote Fetch logs :: {}".format(count))
                policy_updated = self.zcc.update_policy()
                if not policy_updated[0]:
                    raise Exception(policy_updated[1])

                count += 1
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,"Success :: Validated remote fetch logs from ma",None)

    def validate_download_and_size_fetch_logs(self, URL):
        """
        This function:
            1. Downloads the remoteFetchLogs from the URL fetched from MA as remoteFetchLogs.enc2validates
            2. The size and validate if it is not empty
            3. Delete the file for cleanup
        """


        file_name = "remoteFetchLogs.enc2"

        try:
            if os.path.exists(file_name):
                os.remove(file_name)

            response = wget.download(URL, file_name)
            print("\n\n")

            self.logger.info("remoteFetchLogs downloaded from {} to folder {}".format(URL, os.getcwd()))

            filesize = os.stat(file_name)
            filesize = filesize.st_size

            if filesize == 0:
                raise Exception("Size of file download is 0")
            else:
                self.logger.info("Size of Remote Fetch logs is {}".format(filesize))

            os.remove(file_name)  # Cleanup

            self.logger.info("validate_download_and_size_fetch_logs Comnpleted!")

        except Exception as e:
            line = "Request to URL:: {} for download not made.\nThe Error is {}".format(URL, e)
            self.logger.error(line)
            return (False,f"Error :: {line} :: {e}",None)
        return (True,"Success :: validate_download_and_size_fetch_logs Comnpleted!",None)

    def verify_included(self, hostname):
        """
        Verify if the hostname is included.

        :param hostname: Hostname to verify
        :return: Tuple containing status, message, and exception (if any)
        """
        try:        
            self.log_ops.search_log_file(
                file="ZSATunnel",
                search_mode=None,
                words_to_find_in_line=["DBG Parse tunnel2 domains", "isInclude : 1", hostname],
                start_line=self.log_ops.recent_log_line,
            )
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,"Success :: Validated verify_included",None)

    def verify_excluded(self, hostname):
        """
        Verify if the hostname is excluded.

        :param hostname: Hostname to verify
        :return: Tuple containing status, message, and exception (if any)
        """
        try:
            self.log_ops.search_log_file(
                file="ZSATunnel",
                search_mode=None,
                words_to_find_in_line=["DBG Parse tunnel2 domains", "isInclude : 0", hostname],
                start_line=self.log_ops.recent_log_line,
            )
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,"Success :: Validated verify_excluded",None)

    def verify_app_included(self, hostname):
        """
        Verify if the application is included.

        :param hostname: Hostname to verify
        :return: Tuple containing status, message, and exception (if any)
        """
        try:
            self.log_ops.search_log_file(
                file="ZSATunnel",
                search_mode=None,
                words_to_find_in_line=["isTunnel2Domain:", hostname, ": 1"],
                start_line=self.log_ops.recent_log_line,
            )
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,"Success :: Validated verify_app_included",None)

    def verify_app_excluded(self, hostname):
        """
        Verify if the application is excluded.

        :param hostname: Hostname to verify
        :return: Tuple containing status, message, and exception (if any)
        """
        try:
            self.log_ops.search_log_file(
                file="ZSATunnel",
                search_mode=None,
                words_to_find_in_line=["isTunnel2Domain:", hostname, ": 0"],
                start_line=self.log_ops.recent_log_line,
            )
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,"Success :: Validated verify_app_excluded",None)

    def get_all_ips(self, domain):
        """
        Target: Fetch all available IPs of a domain
        Work Flow:
        ----------
            1. list of IPs for a given domain
            2. if it has more than 1 ip, return list of IPs, else return the ip
        """
        try:
            ip_list = list({addr[-1][0] for addr in socket.getaddrinfo(domain, 0, 0, 0, 0)})
            if len(ip_list) == 1:
                ip_list = ip_list[0]
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,"Success :: Got the IP's for given domain",ip_list)

    def find_traffic_in_web_insights(self, domain=None, ip=None, look_for=600, refresh=True):
        """
        Target: Find Traffic logs in Web Insights using Search Web insights Function from admin_ops.py
        Work Flow:
        ----------
            1. make sure either domain or ip is provided
            2. create a dictionary that contains all key-value pairs expected in a web insight log dictionary
            3. send it to Search_Web_Insights in admin_ops.py.
        """
        try:
            if domain == None and ip == None:
                raise Exception("Error :: Please Provide domain/ip")

            search_dict = {}
            if domain:
                search_dict["urlDomain"] = domain
            if ip:
                search_dict["clientIp"] = ip

            results = self.zia.search_web_insights(
                url_to_be_searched=domain,
                search_dict=search_dict
            )
            if not results[0]:
                raise Exception(results[1])
            results = results[2]
            print(results)
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,"Success :: Got the IP's for given domain",results)


    def validate_export_logs(self, is_via_tray:bool=True)->tuple[bool, str, None]:
        """
        Validate export logs
        1. Perform export logs
        2. Validate if the number of files has increased in Documents or not
        3. Validate file naming
        """
        try:
            # Disable support access to export logs will be available in tray
            action_in_ma = False if is_via_tray == True else True
            result = self.ma_client_connector_support.toggle_report_an_issue(action=action_in_ma)
            if result[0] == False:
                raise Exception(result[1])

            # Update policy
            result = self.zcc.update_policy()
            if result[0] == False:
                raise Exception(result[1])
            
            # Make a note, how many files are present in ~/Documents since by default, Zcc exports it to ~/Documents
            current_user = self.zcc.sys_ops.get_current_logged_in_user()[-1]
            documents_path = os.path.join('/','Users',f'{current_user}','Documents')
            list_of_files_at_documents_before_export = os.listdir(path=documents_path)

            # Create a list of file that contains .zip in them
            list_of_files_at_documents_before_export = [os.path.join(documents_path, each_file) for each_file in list_of_files_at_documents_before_export if ".zip" in each_file]

            # Sort the files based on its creation time
            list_of_files_at_documents_before_export.sort(key=os.path.getctime, reverse=True)

            # Perform export logs
            result = self.zcc.export_logs(use_zcc_tray=is_via_tray)
            if result[0] == False:
                raise Exception(result[1])
            
            # Find the list of .zip files in documents
            list_of_files_at_documents_after_export = os.listdir(path=documents_path)
            list_of_files_at_documents_after_export = [os.path.join(documents_path, each_file) for each_file in list_of_files_at_documents_after_export if ".zip" in each_file]
            
            # Wait until export logs if finished by checking the generation of a new zip file or wait for 60 seconds in total
            start_time = round(time.time())
            end_time = start_time + 60
            self.logger.info("Waiting for export logs to finish")
            while( len(list_of_files_at_documents_after_export) <= len(list_of_files_at_documents_before_export) and start_time < end_time):
                print(f"Time Elapsed: {end_time - start_time} seconds")
                time.sleep(1)
                list_of_files_at_documents_after_export = os.listdir(path=documents_path)
                list_of_files_at_documents_after_export = [os.path.join(documents_path, each_file) for each_file in list_of_files_at_documents_after_export if ".zip" in each_file]
                start_time = round(time.time())
            
            # Sort the files based on its creation time
            list_of_files_at_documents_before_export.sort(key=os.path.getctime, reverse=True)

            # Compare the number of files before & after, if a new file has been generated validate that the generated file has the expected name
            if len(list_of_files_at_documents_before_export) < len(list_of_files_at_documents_after_export):
                self.logger.info(f"The number of files before: {len(list_of_files_at_documents_before_export)} & after: {len(list_of_files_at_documents_after_export)} is as expected")
                regex_exported_file_name = r'Zscaler-[0-9]{10}\.[0-9]{6}.zip'
                if re.match(regex_exported_file_name, list_of_files_at_documents_after_export[0].split('/')[-1]):
                    self.logger.info(f"Required exported log file has been found at documents with the path: {list_of_files_at_documents_after_export[0]}")
                else:
                    self.logger.error(f"The latest zip file found at documents folder doesnt have the required naming: {regex_exported_file_name}, Zip file: {list_of_files_at_documents_after_export[0]}")
                    raise Exception(f"The latest zip file found at documents folder doesnt have the required naming: {regex_exported_file_name}")
            else:
                raise Exception(f"The number of files before: {len(list_of_files_at_documents_before_export)} & after: {len(list_of_files_at_documents_after_export)} is not as expected")
        except Exception as e:
            self.logger.error(f"Error while performing export logs: {e}")
            return False, f"Error while performing export logs: {e}", None
        else:
            self.logger.info("Successfully validated export logs")
            return True, "Successfully validated export logs", None
                

from common_lib.common.log_ops import LogOps
from common_lib.adminzia import urlpolicy
from OS_Ios.library.ops_system import SysOps
from OS_Ios.library.ui_zcc import Zcc
from common_lib.common import constants
from common_lib.mobileadmin import deviceoverview
from common_lib.mobileadmin import deviceposture
from common_lib.mobileadmin import ziaposture
import os,time
import allure
from common_lib.common.logger import Logger

class ZccZiaPosture:
    """
    A class used to manage ZCC ZIA Posture.

    Attributes:
    ----------
    cloud : str
        The cloud environment.
    os_version : str
        The operating system version.
    logger : logger
        The logger handle.
    log_ops : LogOps
        The log operations object.
    zia_posture : ZiaPosture
        The ZIA Posture object.
    zcc : Zcc
        The ZCC object.
    sys_ops : SysOps
        The system operations object.
    const : ZiaPostureConst
        The ZIA Posture constants.
    zia_url_policy : UrlPolicy
        The ZIA URL policy object.
    ma_device_overview : DeviceOverview
        The MA device overview object.
    device_posture : DevicePosture
        The device posture object.

    Methods:
    -------
    validate_trust_level_from_logs(trust_level)
        Validates the trust level from logs.
    validate_policy_download_type_tray_manager_logs(policy_download_type)
        Validates the policy download type log in the ZSATray log file.
    validate_if_url_is_accessible(url, accessed)
        Validates if a URL is accessible.
    create_delete_url_policy(action, policy_name, policy_rule)
        Creates or deletes a URL policy.
    validate_device_trust_level(user, trust_level)
        Validates the trust level of an enrolled device.
    validate_tray_logs(log_to_be_searched)
        Validates if a specific log message exists in the ZSATray log file.
    """
    def __init__(self, cloud, config, log_handle=None):
        """
        Initializes the ZccZiaPosture object.

        Parameters:
        ----------
        cloud : str
            The cloud environment.
        config : dict
            The configuration settings.
        log_handle : logger, optional
            The logger handle (default is None).
        """
        self.cloud = cloud
        self.os_version = constants.os_version
        self.logger = (log_handle if log_handle != None else Logger.initialize_logger("ZCC_ZIA_Posture.log", log_level="DEBUG"))
        self.log_ops = LogOps(log_handle=self.logger)
        self.zia_posture = ziaposture.ZiaPosture(self.cloud, config, self.logger)
        self.zcc = Zcc(start_app=True, log_handle=self.logger)
        self.sys_ops = SysOps(driver=self.zcc.ui.driver, logger=self.logger)
        self.const = constants.ZiaPostureConst
        self.zia_url_policy = urlpolicy.UrlPolicy(self.cloud, config, self.logger)
        self.ma_device_overview = deviceoverview.DeviceOverview(self.cloud, config, self.logger)
        self.device_posture = deviceposture.DevicePosture(self.cloud, config, self.logger)
        self.logger.info("ZCC ZIA Posture !\n")

    # -------------------------------------------------------------------------------------------------------------------------
    
    @allure.step("validate Trust Levle From logs")
    def validate_trust_level_from_logs(self, trust_level:int=None)->tuple[bool,str]:
        """
        Validate the trust level from logs.

        This function validates the trust level from logs by searching for the specific log line that contains the trust level.
        It first escapes the trust level string to avoid any regex special characters and then searches for the log line.
        If the log line is found, it returns True and "Success". If not, it returns False and an error message.

        :param trust_level: The trust level to validate, defaults to None
        :type trust_level: int, optional
        :return: A tuple containing a boolean indicating success and a message
        :rtype: tuple[bool, str]
        """
        logs_to_be_searched = f"Evaluated trust level: {trust_level}"
        if trust_level==0:
            logs_to_be_searched = "Skip zia posture evaluation as it is not configured"

        self.log_ops.recent_log_line_number = None
        try:
            self.log_ops.search_log_file(file="ZSATray", words_to_find_in_line=logs_to_be_searched, search_mode=None,
                                            wait_time=10, start_timestamp=self.log_ops.start_time)
        except:
            try:
                self.log_ops.search_log_file(file="ZSATray", words_to_find_in_line=logs_to_be_searched, search_mode=None,
                                            wait_time=10, start_timestamp=self.log_ops.start_time)
            except:
                return (False,"ERROR :: ZIA Posture trust level evaluation logs not found",None)
        return (True,f"Success :: {logs_to_be_searched} Found ",None)
    # ---------------------------------------------------------------------------------------------------------------------------

    def validate_policy_download_type_tray_manager_logs(self, policy_download_type:int=None)->tuple[bool,str]:
        """
        This function validates if the policy download type log is present in the ZSATray log file.

        Args:
            policy_download_type (int, optional): The policy download type to search for in the log file. Defaults to None.

        Returns:
            tuple[bool, str]: A tuple containing a boolean value indicating the success or failure of the operation,
            and a string message describing the result.
        """
        logs_to_be_searched = f"Auth::Library::apiMobilePolicyDownloadV2: DeviceType: 1 , policyDownloadType: {policy_download_type}"
        self.log_ops.recent_log_line_number = None
        try:
            self.log_ops.search_log_file(file="ZSATray", words_to_find_in_line=logs_to_be_searched,
                                             start_line_number=self.log_ops.recent_log_line_number)
        except:
            time.sleep(10)
            try:
                self.log_ops.search_log_file(file="ZSATray", words_to_find_in_line=logs_to_be_searched,
                                                 start_line_number=self.log_ops.recent_log_line_number)
            except:
                return (False,"ERROR :: Policy Download Type log not found",None)
        return (True,f"Success :: {logs_to_be_searched} Found",None)
    # ---------------------------------------------------------------------------------------------------------------------------

    @allure.step('validate if url is accessible')
    def validate_if_url_is_accessible(self, url:str="https://edition.cnn.com", accessed:bool=True):
        return self.sys_ops.access_application(url=url, browser="safari", assertion_element='(//XCUIElementTypeLink[@name="CNN logo"])', failure_expected=not accessed, dynamic_wait_search_element=10)

    # ---------------------------------------------------------------------------------------------------------------------------

    @allure.step("create delete url policy")
    def create_delete_url_policy(self, action:str, policy_name:str, policy_rule:str=None)->tuple[bool,str]:
        """
        This function creates or deletes a URL policy based on the given action.

        Args:
            action (str): The action to perform, either 'create' or 'delete'.
            policy_name (str): The name of the policy.
            policy_rule (str, optional): The policy rule, either 'allow', 'caution', or 'block'. Defaults to None.

        Returns:
            tuple[bool, str]: A tuple containing a boolean indicating success or failure and a message describing the result.
        """
        created_url_policy_data=None
        if action.lower()=='create':
            if policy_rule==None or policy_name==None:raise Exception("PolicyRule or PolicyName not given, please give policyRule :: allow,caution,block and/or provide policy name")
            policy_input = self.const.URL_POLICY_DETAILS[policy_name]
            if 'deviceTrustLevels' in policy_input:
                res,message,created_url_policy_data = self.zia_url_policy.create_url_filter_policy(action=policy_rule,
                                                                         url_categories=policy_input['urlCategories'],
                                                                         device_groups=policy_input['deviceGroups'],
                                                                         name=policy_input['name'],
                                                                         device_trust_levels=policy_input[
                                                                                'deviceTrustLevels'],
                                                                         order=policy_input['order'],
                                                                         return_data=True)
                if not res:
                    return (False,message,None)
            else:
                res,message,created_url_policy_data = self.zia_url_policy.create_url_filter_policy(action=policy_rule, url_categories=policy_input['urlCategories'], device_groups=policy_input['deviceGroups'], name=policy_input['name'], order=policy_input['order'], return_data=True)
                if not res:
                    return (False, message,None)
        elif action.lower()=='delete':
            res,message,_ = self.zia_url_policy.delete_url_filter(name=policy_name)
            if not res:
                return (False,message,None)
        else:return(False,f"Invalid action given :: {action}, give create or delete",None)
        res,message,_ = self.zia_url_policy.zia_helper.zia_activate()
        if not res:
            return (False, message,None)
        return (True,f"Success :: {action} url policy done ",created_url_policy_data)

    # ---------------------------------------------------------------------------------------------------------------------------
    def validate_device_trust_level(self, user:str, trust_level:int=0)->tuple[bool,str]:
        """
        Validate the trust level of an enrolled device.

        This function validates the trust level of an enrolled device by comparing it to the input trust_level.
        It first checks if the trust_level is within the valid range (0-4), and then retrieves the device details using the user information.
        If the device trust level matches the input trust_level, it logs the successful validation and returns True.
        Otherwise, it returns False and an error message.

        Args:
            self (object): The instance of the class.
            user (str): The user information used to retrieve the device details.
            trust_level (int, optional): The trust level to validate against. Defaults to 0.

        Returns:
            tuple[bool, str]: A tuple containing a boolean indicating the success of the validation and a string with an error message if the validation failed.
        """
        ##trust levels: NOT APPLICABLE = 0, UNKNOWN = 1, LOW = 2, MEDIUM = 3, HIGH = 4
        try:
            if trust_level not in range(0,5): raise Exception("Trust level defined is incorrect")
            self.logger.info(f"Validate Enrolled Device Trust Level")

            res,message,get_enrolled_device = self.ma_device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
            if res:
                raise Exception(message)
            device_trust = get_enrolled_device.json()['deviceTrust']
            self.logger.info(f"device trust level is {device_trust}")
            self.logger.info(f"Input value is {trust_level}")
            # return True if deviceTrust == trust_level else False
            if device_trust == trust_level:
                self.logger.info("ZCC returned correct device trust level to MA")
            else:
                return (False,"Error :: Either ZCC sent incorrect device trust level to MA or MA displayed incorrect device trust level in Enrolled device details",None)
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,"Success :: Device Trust Level validated",None)
    # ---------------------------------------------------------------------------------------------------------------------------

    def validate_tray_logs(self, log_to_be_searched:str=None)->tuple[bool,str]:
        """
        This function validates if a specific log message exists in the ZSATray log file.

        Args:
            log_to_be_searched (str): The log message to search for.

        Returns:
            tuple[bool, str]: A tuple containing a boolean indicating the success or failure of the operation,
            and a string message describing the result.
        """
        if log_to_be_searched is None:
            return (False,"Error :: Log message to be searched is not provided",None)
        self.log_ops.recent_log_line_number = None
        try:
            self.log_ops.search_log_file(file="ZSATray", words_to_find_in_line=log_to_be_searched,
                                         start_line_number=self.log_ops.recent_log_line_number)
        except:
            time.sleep(10)
            try:
                self.log_ops.search_log_file(file="ZSATray", words_to_find_in_line=log_to_be_searched,
                                             start_line_number=self.log_ops.recent_log_line_number)
            except:
                return (False,f"ERROR :: Required log message '{log_to_be_searched}' not found",None)
        return (True,f"Success :: {log_to_be_searched} Found ",None)
    
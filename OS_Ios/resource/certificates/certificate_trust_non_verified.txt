-----BEGIN CERTIFICATE-----
MIIDvzCCAqegAwIBAgIUVZadh4UyloCCmauaeFa2uHdxHiIwDQYJKoZIhvcNAQEL
BQAwbzELMAkGA1UEBhMCSU4xCzAJBgNVBAgMAktBMQwwCgYDVQQHDANCTFIxDDAK
BgNVBAoMA1pDQzERMA8GA1UECwwITUFDWkNDUUExJDAiBgNVBAMMG0NlcnRpZmlj
YXRlVHJ1c3ROb25WZXJpZmllZDAeFw0yNDA0MTcxMTE1MzlaFw0zNDA0MTUxMTE1
MzlaMG8xCzAJBgNVBAYTAklOMQswCQYDVQQIDAJLQTEMMAoGA1UEBwwDQkxSMQww
CgYDVQQKDANaQ0MxETAPBgNVBAsMCE1BQ1pDQ1FBMSQwIgYDVQQDDBtDZXJ0aWZp
Y2F0ZVRydXN0Tm9uVmVyaWZpZWQwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC2AUc9+QWMJyA4Dhr0i4JEth2G+maGKIAvebJPSmPC9pubBufplaG7dWu/
X1DBqRmubHfRmeaZUw/cajC7RtpbdUg3Avs55O4OeH03mlak2KZgj7gWW5qJj9Bn
MDH0QWT5K1PqCbdwwIkbtb4i1hQwvTRJb5yRE8CyuviUD8h4XLdN1WKMQUuDrfuN
5ozHqRrCBLZuT1ljFlSlw/Scch6ODFZFucCoJfCWK2DPvpFVPTbMhGAsZmhyeTv5
FA1IchBQn+TMQXRog4MlCwphJvUQA0QBJuEmrN+LytrYmone19/HWqjEL7xOUYF6
f4KBHRfhpsmL0lPaU52DHI0QB+yBAgMBAAGjUzBRMB0GA1UdDgQWBBQMGA6BQDyC
jOqx/bVQGovtPYXaYDAfBgNVHSMEGDAWgBQMGA6BQDyCjOqx/bVQGovtPYXaYDAP
BgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBHcPJYDP/YVaTFYC0A
xAQVCPUbY4iLMRChUJLyEW7MAxA1WLAji6nnXhV79ZQKZosxKB+1y5DStLQCnzpk
MB3HOU4pT3QoKqCqOtftn7Tsw4cfbIAH1Snovu0ahGr93VJXOpIKYtSM7nJfDw9r
he2HcyPbCbwsqKierGZin4thYJcDkmMyvHmtJVG7IoaAfG9h7F9Bomal0LkBU6WL
tPYloLJB2lrmB6aNsD9fHqOPKE2VfO2WFgFetqBDNvdAIOCa8QBdWNyKyJ1xzbiH
WxIH2ESQEWyKVpir/1XQh0wK4tlJXvY0cUhRy3NpnvCHzZUSZmWmxqeVbhRY4kEi
m6+1
-----END CERTIFICATE-----

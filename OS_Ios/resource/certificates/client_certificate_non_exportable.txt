-----BEGIN CERTIFICATE-----
MIIDxTCCAq2gAwIBAgIUK0LM985kaR4dQVhBJPliw5qDxXcwDQYJKoZIhvcNAQEL
BQAwcjELMAkGA1UEBhMCSU4xCzAJBgNVBAgMAktBMQwwCgYDVQQHDANCTFIxDDAK
BgNVBAoMA1pDQzERMA8GA1UECwwITUFDWkNDUUExJzAlBgNVBAMMHkNsaWVudENl
cnRpZmljYXRlTm9uRXhwb3J0YWJsZTAeFw0yNDA0MTgwNjEyMzZaFw0zNDA0MTYw
NjEyMzZaMHIxCzAJBgNVBAYTAklOMQswCQYDVQQIDAJLQTEMMAoGA1UEBwwDQkxS
MQwwCgYDVQQKDANaQ0MxETAPBgNVBAsMCE1BQ1pDQ1FBMScwJQYDVQQDDB5DbGll
bnRDZXJ0aWZpY2F0ZU5vbkV4cG9ydGFibGUwggEiMA0GCSqGSIb3DQEBAQUAA4IB
DwAwggEKAoIBAQDNlwsAMt0sLf3aSxTX36o6noVk4ahZEKViwDeZpjGbOmtDENXj
iZ7ea/Z88Woi9QaoAl90pX6IsEaHC47ZFiMpFA9gga/ihssUlepYJevDJXVjiTv3
pUXIHIo0BNtElYJV2w+Sr3ewNgtfyUYSgJHEEnbnPT5zVh5GlKB0Z9W/uf+WzCtR
A71AsLqndPkTRWWzzCJ7XTEeuhoI2a5rsDJtCX9zr3CaVBQ826J2n/MyBKdW9e4F
HToWTdvaCX2DYeEhCFSnJlFVfQ+iKVk6G0cLHUfcDjRjrDMPjAcxlaN4xpxCIkty
Hd7VDLf3nts+xz+E4O94r3UxaV+i52KdcIelAgMBAAGjUzBRMB0GA1UdDgQWBBS+
yjYNPfijIgtNzlMEeaGNp8Oc4TAfBgNVHSMEGDAWgBS+yjYNPfijIgtNzlMEeaGN
p8Oc4TAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQCh+UxQXZCp
mpg/iZM4ppEapB633pCChEjZ/5EFktx5XEd61ETh1qSxR1XJwdqMnMp3gSTAcZBA
PqKGklWV8g5gSZ+5jx5gq1zc5kj5iM8PbwlhwuhWDVw30jlgCpVUgIo3l0gON5MN
4MN0NinUA9gImehdK8gLhVXdq5HaDP1fizHzcOaHQ9ei7WpAhrCX00ivodM+4hGe
ua+20pweB0pNkGTho5qERqrVAtWx/LiOa5W4TTeiBjrfrVIUoqFefy0A07LDgw8u
4RlHiPFgwz+48udi07a7l+2iwO4wWeZa1E8ftJZjtivdgVWNjRO0BIm0SjFHPRnr
c5h4M8qRnsE9
-----END CERTIFICATE-----

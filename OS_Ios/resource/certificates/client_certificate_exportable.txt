-----BEGIN CERTIFICATE-----
MIIDqzCCApOgAwIBAgIUZ+Ld0ZqfPD8mrD22GcjfnXSGAw0wDQYJKoZIhvcNAQEL
BQAwZTELMAkGA1UEBhMCSU4xCzAJBgNVBAgMAktBMQwwCgYDVQQHDANCTFIxDDAK
BgNVBAoMA1pDQzERMA8GA1UECwwITUFDWkNDUUExGjAYBgNVBAMMEUNsaWVudENl
cnRpZmljYXRlMB4XDTI0MDQxNzEwMDU0MFoXDTM0MDQxNTEwMDU0MFowZTELMAkG
A1UEBhMCSU4xCzAJBgNVBAgMAktBMQwwCgYDVQQHDANCTFIxDDAKBgNVBAoMA1pD
QzERMA8GA1UECwwITUFDWkNDUUExGjAYBgNVBAMMEUNsaWVudENlcnRpZmljYXRl
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwrfdbtN34b0mBWs6ln2j
4SIrM0+n5denCgKKgac67oo49dbjTjQJKtloE5/lqyS9lUGOFRsh2iaStTquUqvZ
fX9MWGyr80rdeSHBjkURhytK8p5/20QVv7LAWhxVJNU0stfTJoF8Sn5Oa0eDpBja
B/DxY84esnMMCFGNMkWe5KC5K2scjKs4mThO/dAlk2XiyrblsB8mMVVJZBY4lVZN
FzTZnGKITa1fFPz3VPYRUnRQ+ExbImd8L3ZZctGuZnWmbDjfclfeiaD2sXrZWpPO
PT2ujStOI4WYyZhKnqmjUfFN6/MpOJaDKhL4amQe1aPIjJgAkx965siTkmXKqwbo
GQIDAQABo1MwUTAdBgNVHQ4EFgQUvVo3HuFzealY+QkBjIWUp0t5tKYwHwYDVR0j
BBgwFoAUvVo3HuFzealY+QkBjIWUp0t5tKYwDwYDVR0TAQH/BAUwAwEB/zANBgkq
hkiG9w0BAQsFAAOCAQEApF6yYXHUTErowDkqu2SDEq2kqQTw+D7OjaxQkfM8qp/R
tR1x5+DBU3uox2OkycTyevCgbe9e/iFiH6VmIQGjs3X/YCbBK3gW0NM4/lNEuF1/
uFDo6bZbbv6c3CWzWjkpWM+qKPdRpYP/9TcPYpPpSBhodEAaxRCRsqCi3f63Y3GR
e8227yOp1gHmpnPFipEAeCLRJTquGnR+gv2vaZK63HErbhWTkT4ov7G+MMAo4vqD
13WQFNQViR/Xc+gRlEfZlxc3nI3B1w9ICCR9wTf/cWgy9IFX6EATT9R5BGRBBPNr
8tpEaokQ+qCl+lh+haE5kmQoGGa9YOrQBEO5rqV/aA==
-----END CERTIFICATE-----

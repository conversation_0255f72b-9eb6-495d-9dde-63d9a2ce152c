
function FindProxyForURL(url, host) {

    var privateIP = /^(0|10|127|192\.168|172\.1[6789]|172\.2[0-9]|172\.3[01]|169\.254|192\.88\.99)\.[0-9.]+$/;
    var resolved_ip = dnsResolve(host);
   // var country = "${COUNTRY}";

    /* Don't send non-FQDN or private IP auths to us */
    if (isPlainHostName(host) || isInNet(resolved_ip, "*********","*************") || privateIP.test(resolved_ip))
        return "DIRECT";

    /* FTP goes directly */
    if (url.substring(0,4) == "ftp:")
        return "DIRECT";
			
    /* test with ZPA */
    if (isInNet(resolved_ip, "**********","***********"))
        return "DIRECT";
			
    /* Updates are directly accessible */
    if (((localHostOrDomainIs(host, "trust.zscaler.com")) ||
            (localHostOrDomainIs(host, "trust.zscaler.net")) ||
            (localHostOrDomainIs(host, "trust.zscalerone.net")) ||
            (localHostOrDomainIs(host, "trust.zscalertwo.net")) ||
            (localHostOrDomainIs(host, "trust.zscalerthree.net")) ||
            (localHostOrDomainIs(host, "trust.zscalergov.net")) ||
            (localHostOrDomainIs(host, "trust.zsdemo.net")) ||
            (localHostOrDomainIs(host, "trust.zscloud.net")) ||
            (localHostOrDomainIs(host, "trust.zsfalcon.net")) ||
            (localHostOrDomainIs(host, "trust.zdxcloud.net")) ||
            (localHostOrDomainIs(host, "trust.zdxpreview.net")) ||
            (localHostOrDomainIs(host, "trust.zdxbeta.net")) ||
            (localHostOrDomainIs(host, "trust.zsdevel.net")) ||
            (localHostOrDomainIs(host, "trust.zsbetagov.net")) ||
			(localHostOrDomainIs(host, "trust.zspreview.net")) ||
			(localHostOrDomainIs(host, "trust.zscalerten.net")) || 
			(localHostOrDomainIs(host, "trust.zdxten.net")) ) &&
            (url.substring(0,5) == "http:" || url.substring(0,6) == "https:"))
        return "DIRECT";
    
    /* if (shExpMatch(url, "*<UPDATE_BYPASS_DOMAIN_HERE>*") ||
        shExpMatch(url, "*cnn*") ||
        shExpMatch(url, "*instagram*") ||
        shExpMatch(url, "*javatpoint*") ||
        shExpMatch(url, "*snapchat*")
        ){
        return "DIRECT";} */
        
        if (shExpMatch(url, "*<UPDATE_BYPASS_DOMAINv4_HERE>*") || 
            shExpMatch(url, "*<UPDATE_BYPASS_DOMAINv6_HERE>*")
            ){
        return "DIRECT";}
    
	/* for users of Canada if you want to direct traffic to only canada gateways*/
//	if (shExpMatch(country, "Canada")) {
//	 	return "PROXY ${COUNTRY_GATEWAY_FX}:80; PROXY ${COUNTRY_SECONDARY_GATEWAY_FX};DIRECT";
//	}

	/* for all users if you want to direct traffic to country gateways by default */
//	return "PROXY ${COUNTRY_GATEWAY_FX}:80; PROXY ${COUNTRY_SECONDARY_GATEWAY_FX};DIRECT";


    /* Default Traffic Forwarding. Forwarding to Zen on port 80, but you can use port 9400 also */
    return "PROXY ${GATEWAY_FX}:80; PROXY ${SECONDARY_GATEWAY_FX}:80; DIRECT";
}
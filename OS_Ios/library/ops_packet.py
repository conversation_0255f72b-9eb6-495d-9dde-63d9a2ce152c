import subprocess
import re
import time
import os
import time
import subprocess
import pyshark
from common_lib.common.logger import Logger
import logging
import threading
from OS_Ios.library.ops_system import SysOps
from OS_Mac.library.ops_packet import OpsPacket as BaseOpsPacket
import pytest


class OpsPacket(BaseOpsPacket):
    """
    Provides method to capture packets & read packets
    """
    def __init__(self, log_handle:logging.Logger=None):
        super().__init__(log_handle)
        
    capture_process = None
    def get_active_devices(self):
        """Returns a list of active device UDIDs from rvictl -l output."""
        try:
            result = subprocess.run(["rvictl", "-l"], capture_output=True, text=True)
            output = result.stdout.strip()
            
            if "Could not get list of devices" in output:
                return []
            
            return re.findall(r'\[\d+\] ([0-9A-Fa-f-]+) with interface', output)
        except Exception as e:
            print(f"Error retrieving device list: {e}")
            return []

    def remove_active_devices(self):
        """Removes all active devices by executing rvictl -x <udid> for each device."""
        try:
            while True:
                devices = self.get_active_devices()
                if not devices:
                    print("No active devices remaining.")
                    break
                
                for udid in devices:
                    print(f"Removing device: {udid}")
                    subprocess.run(["rvictl", "-x", udid])
        except Exception as e:
            print(f"Error removing active devices: {e}")

    def activate_device(self, udid):
        """Ensures all devices are inactive and then activates the given device. Returns the activated interface."""
        try:
            print(f"Processing UDID: {udid}")
            self.remove_active_devices()
            print(f"Activating device: {udid}")
            result = subprocess.run(["rvictl", "-s", udid], capture_output=True, text=True)
            match = re.search(r'with interface (rvi\d+)', result.stdout)
            return match.group(1) if match else None
        except Exception as e:
            print(f"Error activating device {udid}: {e}")
            return None
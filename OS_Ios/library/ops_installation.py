
################################################################
#               DOCUMENTATION FOR GUI.PY                       #
#                                                              #
# AUTHOR : SRINIVASA BHASWANTH (<EMAIL>)        #
################################################################
__author__ = '{srinivasa bhaswanth}'
__credits__ = ['{srinivasa bhaswanth, abhimanyu sharma, sahil kumar}']
__email__ = '{<EMAIL>}'

######  Module ##############################################################################################################################################
#from concurrent.futures import process
from common_lib.common.constants import Utils
from common_lib.common.logger import Logger

#os_version = "Ios"
##############################################################################################################################################################

class InstallUninstall:
    """
    Base class to be used for MA/ZIA related actions
    any class can import this for performing its own set of MA actions
    Environment
    ------------
        1. operating_system     (str)   name of operating system ('windows'/'mac')
        2. CONST                (class) a class of variables used through out this script
    Work Flow
    ----------
        1. There are 2 important Central Functions
            a. install_zcc()
            b. uninstall_zcc()
        2. these functions can be used with various parameters to install/uninstall zcc on windows/mac
    """
    def __init__(self, 
                 verify_installers_exist=False, 
                 logger=None
                 ):
        self.CONST = Utils
        if logger==None:
            self.logger = Logger.initialize_logger("Install_Uninstall.log", log_level="DEBUG")
        else:
            self.logger = logger
    

    
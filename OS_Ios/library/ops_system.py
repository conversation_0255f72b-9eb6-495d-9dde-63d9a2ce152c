################################################################
#                       SYSTEM_OPS.PY                          #
#                                                              #
# AUTHOR : SAHIL KUMAR (<EMAIL>)               #
# CREDITS : SRINIVASA BHASWANTH, ABHIMANYU SHARMA              #
#                                                              #
################################################################
'''
Last Changes Made : 25/05/2022 by Sahil
Details : Added Installed Zscaler Root CA cert support

'''
from threading import Thread
import pyshark
from common_lib.common.logger import Logger
from common_lib.common.common_ui import Gui
import time
import copy
import allure
import subprocess
import os, dns.resolver, socket


class ThreadWithReturnValue(Thread):
        '''
        This class is a subclass created to get return value from threads
        By default threads dont send return value
        '''
        def __init__(self, 
                     group=None,
                     target=None, 
                     name=None,
                     args=(), 
                     kwargs={}, 
                     verbose=None
                     ):
            Thread.__init__(self, group, target, name, args, kwargs)
            self._return = None
        def run(self):
            print(type(self._target))
            if self._target is not None:
                self._return = self._target(*self._args,
                                                    **self._kwargs)
        def join(self, *args):
            Thread.join(self, *args)
            return self._return

class IosElements:
    # App
    SAFARI_APP="com.apple.mobilesafari"
    CHROME = "com.google.chrome.ios"
    EDGE = "com.microsoft.msedge"
    SETTINGS = 'com.apple.Preferences'

    #Elements

    #safari
    SAFARI_SSL_ERROR = '//XCUIElementTypeStaticText[@name="Safari can’t open the page because it couldn’t establish a secure connection to the server."]'
    SAFARI_RELOAD_BUTTON = '//XCUIElementTypeButton[@name="ReloadButton"]'
    SAFARI_TAB_OVERVIEW = '//XCUIElementTypeButton[@name="TabOverviewButton"]'
    SAFARI_CLOSE = '//XCUIElementTypeButton[contains(@name, "Close")]'
    SAFARI_CLOSEALL = '//XCUIElementTypeButton[@name="CloseAllTabsButton"]'
    SAFARI_STOP_BUTTON = '//XCUIElementTypeButton[@name="StopButton"]'
    
    #chrome
    CHROME_MENU_BUTTON = '//XCUIElementTypeButton[@name="kToolbarToolsMenuButtonIdentifier"]'
    CHROME_RELOAD_BUTTON = '//XCUIElementTypeButton[@name="kToolsMenuReload"]'
    CHROME_TAB_STACK = '//XCUIElementTypeButton[@name="kToolbarStackButtonIdentifier"]'
    CHROME_EDIT = '//XCUIElementTypeButton[@name="kTabGridEditButtonIdentifier"]'
    CHROME_CLOSE_ALL_TABS  = '//XCUIElementTypeButton[@name="Close All Tabs"]'
    CHROME_HISTORY_BUTTON = '//XCUIElementTypeStaticText[@name="History"]'
    CHROME_DELETE_BROWSING_DATA = '//XCUIElementTypeButton[@name="kHistoryToolbarClearBrowsingButtonIdentifier"]'
    CHROME_DELETE_DATA_BUTTON = '//XCUIElementTypeStaticText[@name="Delete data"]'

    #edge
    EDGE_RELOAD_BUTTON = '//XCUIElementTypeButton[@name="kEdgeOmniboxReloadButtonIdentifier"]'
    EDGE_TAB_STACK = '//XCUIElementTypeButton[@name="kToolbarStackButtonIdentifier"]'
    EDGE_CLOSE_ALL = '//XCUIElementTypeStaticText[@name="Close All"]'
    EDGE_CLOSE_BUTTON  = '//XCUIElementTypeButton[@name="Close"]'

    #Keys
    GO = '//XCUIElementTypeButton[@name="Go"]'

    #browser ui
    ERR_SSL_PROTOCOL_ERROR = '//XCUIElementTypeStaticText[@name="ERR_SSL_PROTOCOL_ERROR"]'
    BLOCK_RULE_MESSAGE = '//XCUIElementTypeStaticText[@name="Not allowed to browse"]'

class SysOps:
    def __init__(self,
                 driver=None,
                 logger=None,
                 cloud=None
                 ):
        
        '''   SysOps class '''
        self.logger = (logger if logger else Logger.initialize_logger("Sys_Ops.log", log_level="DEBUG"))
        self.ui = Gui(log_handle=self.logger) 
        self.initialize_elements()
        self.ui.driver = driver

    def get_current_logged_in_user(self)->tuple[bool, str, str|None]:
        """
        Jenkins launchd process always runs as a root user, hence any module used to get current loggedin user returns the user as root which is not right
        Automation triggered via jenkins looks in root directory for zscaler related logs
        Fetch the first value returned by the command 'who'
        """

        try:
            self.logger.info("Fetching current logged in user")
            command = "who"
            process = os.popen(cmd=command)
            process_output = process.read()
            loggedin_username = process_output.split(" ")[0]
        except Exception as e:
            self.logger.error(f"Error while fetching current logged in user: {e}")
            return False, f"Error while fetching current logged in user: {e}", None
        else:
            self.logger.info(f"Current logged in user: {loggedin_username}")
            return True, f"Current logged in user: {loggedin_username}", loggedin_username

    # -----------------------------------------------------------------------------------------------------------------------------------------------------

    @allure.step("Initialize iOS device elements")
    def initialize_elements(self):
        """Initialize the ios elements i.e xpaths"""
        try:
            self.elements = copy.deepcopy(IosElements)
            return (True, "Initialized Ios Elements Successfully", self.elements)
        except Exception as e:
            return False, str(e), None

     # -----------------------------------------------------------------------------------------------------------------------------------------------------
   
    @allure.step("Open App")
    def open_app(self, 
                 app_bundle_id:str = None):
        """
        Function to Open desired app on device 

        Args:
        app_bundle_id:str --> Bundle Id of iOS application to open

        Returns:
        A tuple (status, msg, None)

        """
        try:
            if not self.ui.driver.is_app_installed(bundle_id=app_bundle_id):
                raise Exception
            self.logger.info(f"Terminating previous session for :: {app_bundle_id}")
            self.ui.driver.terminate_app(app_id=app_bundle_id)
            time.sleep(1)
            self.logger.info(f"Activating :: {app_bundle_id}")
            self.ui.driver.activate_app(app_id=app_bundle_id)
            print("App state -->"+str(self.ui.driver.query_app_state(app_id=app_bundle_id)))
            # print(self.ui.driver.context)
            # print(self.ui.driver.contexts)
            # print(self.ui.driver.page_source)
            self.logger.info("App opened successfully")
            return True, "App opened successfully", None
        except Exception as e:
            self.logger.error(f"Error :: {e}")
            return False, f"Error occured while opening app:: {e}", None 

    # -----------------------------------------------------------------------------------------------------------------------------------------------------

    def execute_command(
        self,      
        command:str,        # Command to be ran like curl or ping
        application=None,   # Application to be accessed via command
        fetch_ip=False,     # Decides to fetch ip of app or not
    ):
        '''
        Flow:  ------------------------------
            1. For now it does curl or ping to the given application
            2. Then it fetches the ip of the given application
            3. Returns the result of curl or ping command and ip of the application
        '''
        self.logger.info('COMMAND : {}'.format(command))
        try:
            p1 = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            result = p1.communicate()
            result = list(result)
            for i in range(len(result)):
                result[i] = result[i].decode('ascii')
        except Exception as e:
            self.logger.error("ERROR WITH TERMINAL COMMAND :: {}".format(e))
            raise Exception("ERROR WITH TERMINAL COMMAND :: {}".format(e))
        if not fetch_ip:
            self.logger.info(f"Returned Result")
            return result
        else:
            ip = self.fetch_ip(application)[2]

            self.logger.info("Returned Result & ip")
            return result, ip
        
    @allure.step("Access Application")
    def access_application(self,
                           sleep_time:int = 10,                 #time to sleep after accessing application
                           zpa_app:bool = False,                #True if url is of zpa app else False
                           browser:str = "chrome",              #Preferred browser for app access default is Chrome (Safari/Chrome/Edge) 
                           close_tabs:bool = False,             #True to close tabs after app access else False
                           url:str = None,                      #url/app to access
                           assertion_element = None,            #assertion element in browser to verify app access
                           failure_expected:bool = False,       #True if failure is expected else False
                           dynamic_wait_search_element:int = 40 #Wait for searching an element existence with Explicit_wait function
                           ):  
        """
        Function to access application on browser

        Args:
        sleep_time --> time to sleep after accessing application
        zpa_app:bool --> True if given app is a ZPA app else False
        browser:str = "chrome" --> Preferred browser for app access default is Chrome (Safari/Chrome/Edge) 
        close_tabs --> True to close tabs after app access else False
        url:str = None --> app/url to access in browser
        assertion_element:str = None --> Element required for assertion on UI after acccessing application , list of elements or element can be passed

        Returns:
        A tuple (status, msg, None)

        """
        try:


            skip_assertion = zpa_app
            self.logger.info(f"Given browser :: {browser} --> Opening {url} in {browser} browser...")
            if browser.lower() == "safari":
                if not self.ui.driver.is_app_installed(bundle_id=self.elements.SAFARI_APP):
                    self.logger.error(f"{browser} is not installed on device")
                    raise Exception
                # app_access=subprocess.Popen(f"xcrun devicectl device process launch --device {os.environ.get("ios_udid")} --payload-url {url} {IosElements.SAFARI_APP} ", shell=True, stdout=subprocess.PIPE).stdout.read().decode('utf-8')
                app_access = subprocess.Popen(
                                "xcrun devicectl device process launch --device {} --payload-url {} {}".format(
                                    os.environ.get("ios_udid"),
                                    url,
                                    IosElements.SAFARI_APP
                                ),
                                shell=True,
                                stdout=subprocess.PIPE
                            ).stdout.read().decode('utf-8')
                self.logger.info(f"App access Output :: {app_access}")
                self.logger.info(f"Sleeping for :: {sleep_time/2} before refreshing application")
                time.sleep(sleep_time/2)
                try:
                    self.ui.click(path=self.elements.SAFARI_RELOAD_BUTTON, dynamic_wait_search_element=30)
                except:
                    if self.ui.check_if_exists(path=self.elements.SAFARI_STOP_BUTTON):
                        try:
                            self.ui.click(path=self.elements.SAFARI_STOP_BUTTON)
                            self.ui.click(path=self.elements.SAFARI_RELOAD_BUTTON)
                        except:
                            pass

                self.logger.info(f"Sleeping for :: {sleep_time/2} after accessing application")
                time.sleep(sleep_time/2)
                if not skip_assertion: 
                    if assertion_element is not None:
                        print(f"***** Provided assertion elements on site {url} :: {assertion_element}")
                        element_found = False
                        if type(assertion_element)==type([]):
                            for i in assertion_element:
                                if self.ui.check_if_exists(path=i, dynamic_wait_search_element=10):
                                    element_found=True
                                    break
                        else:
                            if self.ui.check_if_exists(path=assertion_element, dynamic_wait_search_element=dynamic_wait_search_element):
                                element_found=True
                        if element_found and failure_expected:
                            self.logger.error(f"Element :: {assertion_element} appeared which is not expected")
                            raise Exception(f"Element :: {assertion_element} appeared which is not expected")
                        if not element_found:
                            if failure_expected:
                                self.logger.info(f"Element :: {assertion_element} didn't appear as expected")
                            else:
                                raise Exception 
                    
                if close_tabs:
                    try:
                        res = self.close_all_safari_tabs()
                        if not res[0]:
                            raise Exception
                        self.logger.info("***** Closed safari tabs *****")
                    except Exception as e:
                        self.logger.error(f"***** Unable to Close safari tabs :: Error --> {e} *****")

            elif browser.lower() == "chrome":
                if not self.ui.driver.is_app_installed(bundle_id=self.elements.CHROME):
                    self.logger.error(f"{browser} is not installed on device")
                    raise Exception
                # app_access_command = f"xcrun devicectl device process launch --device {os.environ.get("ios_udid")} --payload-url {url} {IosElements.CHROME} "
                app_access_command = subprocess.Popen(
                                "xcrun devicectl device process launch --device {} --payload-url {} {}".format(
                                    os.environ.get("ios_udid"),
                                    url,
                                    IosElements.CHROME
                                ),
                                shell=True,
                                stdout=subprocess.PIPE
                            ).stdout.read().decode('utf-8')
                print("App access command --> ", app_access_command)
                app_access=subprocess.Popen(app_access_command, shell=True, stdout=subprocess.PIPE).stdout.read().decode('utf-8')
                self.logger.info(f"App access Output :: {app_access_command}")
                self.logger.info(f"Sleeping for :: {sleep_time/2} before refreshing application")
                time.sleep(sleep_time/2)
                self.ui.click(path=self.elements.CHROME_MENU_BUTTON)
                self.ui.click(path=self.elements.CHROME_RELOAD_BUTTON)
                self.logger.info(f"Sleeping for :: {sleep_time/2} after accessing application")
                time.sleep(sleep_time/2)

                if not skip_assertion:
                        if assertion_element is not None:
                            print(f"***** Provided assertion elements on site {url} :: {assertion_element}")
                            element_found = False
                            if type(assertion_element)==type([]):
                                for i in assertion_element:
                                    if self.ui.check_if_exists(path=i, dynamic_wait_search_element=10):
                                        element_found=True
                                        break
                            else:
                                if self.ui.check_if_exists(path=assertion_element, dynamic_wait_search_element=dynamic_wait_search_element):
                                    element_found=True
                            if element_found and failure_expected:
                                self.logger.error(f"Element :: {assertion_element} appeared which is not expected")
                                raise Exception(f"Element :: {assertion_element} appeared which is not expected")
                            if not element_found:
                                if failure_expected:
                                    self.logger.info(f"Element :: {assertion_element} didn't appear as expected")
                                else:
                                    raise Exception
                    
                if close_tabs:
                    try:
                        self.logger.info("***** Closing tabs *****")
                        #closing tabs
                        self.ui.click(path=self.elements.CHROME_TAB_STACK)
                        self.ui.click(path=self.elements.CHROME_EDIT)
                        self.ui.click(path=self.elements.CHROME_CLOSE_ALL_TABS)
                    except Exception as e:
                        self.logger.warning(f"***** Unable to close tabs in Chrome browser :: Error --> {e} *****")
            elif browser.lower() == "edge":
                if not self.ui.driver.is_app_installed(bundle_id=self.elements.EDGE):
                    self.logger.error(f"{browser} is not installed on device")
                    raise Exception
                # app_access=subprocess.Popen(f"xcrun devicectl device process launch --device {os.environ.get("ios_udid")} --payload-url {url} {IosElements.EDGE} ", shell=True, stdout=subprocess.PIPE).stdout.read().decode('utf-8')
                app_access = subprocess.Popen(
                                "xcrun devicectl device process launch --device {} --payload-url {} {}".format(
                                    os.environ.get("ios_udid"),
                                    url,
                                    IosElements.EDGE
                                ),
                                shell=True,
                                stdout=subprocess.PIPE
                            ).stdout.read().decode('utf-8')
                self.logger.info(f"App access Output :: {app_access}")
                self.logger.info(f"Sleeping for :: {sleep_time/2} before refreshing application")
                time.sleep(sleep_time/2)
                self.ui.click(path=self.elements.EDGE_RELOAD_BUTTON)
                self.logger.info(f"Sleeping for :: {sleep_time/2} after accessing application")
                time.sleep(sleep_time/2)

                if not skip_assertion:
                    if assertion_element is not None:
                        print(f"***** Provided assertion elements on site {url} :: {assertion_element}")
                        element_found = False
                        if type(assertion_element)==type([]):
                            for i in assertion_element:
                                if self.ui.check_if_exists(path=i, dynamic_wait_search_element=10):
                                    element_found=True
                                    break
                        else:
                            if self.ui.check_if_exists(path=assertion_element, dynamic_wait_search_element=dynamic_wait_search_element):
                                element_found=True
                        if element_found and failure_expected:
                            self.logger.error(f"Element :: {assertion_element} appeared which is not expected")
                            raise Exception(f"Element :: {assertion_element} appeared which is not expected")
                        if not element_found:
                            if failure_expected:
                                self.logger.info(f"Element :: {assertion_element} didn't appear as expected")
                            else:
                                raise Exception

                if close_tabs:
                    try:
                        self.logger.info("***** Closing tabs *****")
                        #closing tabs
                        self.ui.click(path=self.elements.EDGE_TAB_STACK)
                        self.ui.click(path=self.elements.EDGE_CLOSE_ALL)
                        self.ui.click(path=self.elements.EDGE_CLOSE_BUTTON)
                    except:
                        self.logger.error(f"***** Unable to close tabs in Edge browser :: Error --> {e} *****")
            self.logger.info(f"Successfully accessed {url} in {browser}")
            return True, "App access successful", None
        except Exception as e:
            self.logger.info(f"Failed to access {url} in {browser}")
            self.logger.error(f"Error while accessing app :: {url} --> {e}")
            return False, f"Error while accessing app:: {url} --> {e}", None 
    
    @allure.step("Close Safari tabs")
    def close_all_safari_tabs(self):
        """
        This function clear Safari browser data from Settings app
        """
        try:
            self.ui.driver.activate_app(app_id=self.elements.SAFARI_APP)
            time.sleep(2)
            self.ui.long_press_element(xpath=self.elements.SAFARI_TAB_OVERVIEW,duration=2)
            self.ui.click(path=self.elements.SAFARI_CLOSE)
            try :
                self.ui.click(path=self.elements.SAFARI_CLOSEALL, dynamic_wait_search_element=2)
            except:
                pass
            return (True, f"Success:: Closed all tabs on Safari", None)
        except Exception as e:
            return (False, f"Error occured while closing safari tabs:: {e}", None)
        
    def fetch_system_dns_server_ips(self)->tuple[bool, str, list|None]:
        """
        Fetches the list of dns server list for current resolver
        """
        try:
            self.logger.info("Fetching system DNS servers")
            dns_resolver = dns.resolver.Resolver()
            dns_server_list = dns_resolver.nameservers
        except Exception as e:
            self.logger.error(f"Exception during fetching system DNS server list, Error: {e}")
            return False, f"Exception during fetching system DNS server list, Error: {e}", None
        else:
            self.logger.info(f"Fetched system DNS servers, Result: {dns_server_list}")
            return True, f"Fetched system DNS servers, Result: {dns_server_list}", dns_server_list

    def fetch_all_resolved_ips(self, application:str)->tuple[bool, str, list|None]:
        """
        Fetches the list of all the ips an application can resolve to.
        """
        try:
            self.logger.info(f"Fetching IP list for {application}")
            ip_list_result = socket.gethostbyname_ex(application)
        except Exception as e:
            self.logger.error(f"An error occured while fetching the IP list for {application}, Error: {e}")
            return False, f"An error occured while fetching the IP list for {application}, Error: {e}", None
        else:
            ip_list = ip_list_result[2]
            self.logger.info(f"IP list fetch success, fetched result: {ip_list_result}")
            return True, f"IP list fetch success, fetched result: {ip_list_result}", ip_list
        
    def fetch_ip(self,
                 application):
        """
        Fetches the IP address for a given application URL.

        Args:
            application (str): The URL of the application for which the IP address is to be fetched.

        Returns: a Tuple with 3 values ,
            bool : whether the function execution was succes or not
            str : Error or success message
            str: The IP address of the application, or None if the IP address could not be fetched.
        """
        ip = ''
        self.logger.info(f"Fetching ip for  : {application}")
        if 'https://' in application:
            application = application.split('//')[1]
        try:
            ip = socket.gethostbyname(application)
        except socket.gaierror:
            ip = None
            return (False,"Error : gai error while fetching IP",ip)
        except socket.getaddrinfo:
            ip = None
            return (False,"Error : get address info error while fetching IP",ip)
        except Exception as e:
            return (False,f"Error : No idea what happened :{e}",None)
        return (True,"Success : IP fetched successfully",ip)
    
    def fetch_network_dns_search_domains(self) -> tuple[bool, str, list|None]:
        '''
        Fetches the DNS search domains on current network.
        Uses cmd: 'scutil --dns' to find dns details.
        Returns a list of all DNS Search Domains on the network.
        '''
        try:
            cmd = 'scutil --dns | grep "search domain"'

            self.logger.info(f"Exuting command '{cmd}' to fetch DNS Search Domains")
            search_domains_raw = self.execute_command(command=cmd)
            search_domains_raw = list(map(str.strip, search_domains_raw[0].strip().split('\n')))

            dns_search_domains = set()
            for domain in search_domains_raw:
                if domain:
                    dns_search_domains.add(domain.split(':')[1].strip())

            dns_search_domains = list(dns_search_domains)
        except Exception as e:
            self.logger.error(f"Unable to fetch the network DNS search domains, Error: {e}")
            return False, f"Unable to fetch the network DNS search domains, Error: {e}", None
        else:
            self.logger.info(f"Successfully fetched the network DNS search domains, Result: {dns_search_domains}")
            return True, f"Successfully fetched the network DNS search domains, Result: {dns_search_domains}", dns_search_domains

    def fetch_ios_dns_servers(self, pcap_file_url: str) -> tuple[bool, str, set]:
        """
        Analyze captured DNS packets from the iOS device via rvictl and extract destination DNS servers.

        Args:
        - pcap_file_url (str): Path of the pcap file to be analyzed.

        Returns:
        - tuple[bool, str, set]: A tuple where:
            - The first value is a bool indicating success or failure.
            - The second value is the success or failure message.
            - The third value is a set of unique DNS server IPs used by the iOS device.
        """
        dns_servers = set()

        try:
            # Attempt to parse the pcap file
            self.logger.info(f"Analyzing pcap file: {pcap_file_url}")

            # Open and process the pcap file
            with pyshark.FileCapture(
                pcap_file_url,
                display_filter="dns",  # Apply DNS-specific filter
            ) as capture:
                self.logger.info("Fetching DNS server IP list...")
                for packet in capture:
                    try:
                        # Check if DNS layer is present in the packet
                        if "DNS" in packet:
                            # Handle IPv4 packets
                            if hasattr(packet, "ip"):
                                if packet.dns.flags_response == "False":  # Request
                                    dns_servers.add(packet.ip.dst)
                                elif packet.dns.flags_response == "True":  # Response
                                    dns_servers.add(packet.ip.src)
                                else:
                                    self.logger.info("IPv4: No response matched.")

                            # Handle IPv6 packets
                            elif hasattr(packet, "ipv6"):
                                if packet.dns.flags_response == "False":  # Request
                                    dns_servers.add(packet.ipv6.dst)
                                elif packet.dns.flags_response == "True":  # Response
                                    dns_servers.add(packet.ipv6.src)
                                else:
                                    self.logger.info("IPv6: No response matched.")
                            else:
                                self.logger.info("No IP attribute matched for the packet.")
                    except AttributeError:
                        self.logger.info("Packet missing required attributes. Skipping...")
                        continue
            
            # Remove specific unwanted entries from dns_servers if they exist
            dns_servers.discard("**********")  # Remove IPv4 address
            dns_servers.discard("fc00::6440:1")  # Remove IPv6 address

            # Return success with message and results
            if dns_servers:
                self.logger.info(f"DNS servers fetched successfully : {dns_servers}")
                return True, "DNS servers fetched successfully.", dns_servers
            else:
                return False, "No DNS servers found in the pcap file.", dns_servers

        except Exception as e:
            # Log the error and return failure
            self.logger.error(f"An error occurred while analyzing the pcap file: {str(e)}", exc_info=True)
            return False, f"Failed to analyze pcap file: {str(e)}", dns_servers
    
    def fetch_ios_IP_Address(self, pcap_file_url:str) -> tuple[bool, str, set, set]:
        """
        Captured DNS packets from the iOS device via rvictl and extract client ipv4 and client ipv6 address.

        Args:
        - pcap_file_url (str) : path of the pcap file to be analyzed.

        Returns:
        - tuple[bool, str, set, set]: A tuple with first value as success or failure, then message related to success or failure, then set for client_ipv4 address, then set for client_ipv6 address.
        """

        client_ipv4 = set()
        client_ipv6 = set()

        # Capture DNS packets on the RVI interface

        capture = pyshark.FileCapture(
                    pcap_file_url, 
                    display_filter="dns",
                )

        for packet in capture:
            try:
                # Check if the packet has DNS layer
                if "DNS" in packet:
                    # Handle IPv4 packets
                    if hasattr(packet, "ip"):
                        if packet.dns.flags_response == "False":  # Request
                            client_ipv4.add(packet.ip.src)
                            # return True, packet.ip.src
                        else :
                            pass

                    # Handle IPv6 packets
                    elif hasattr(packet, "ipv6"):
                        if packet.dns.flags_response == "False":  # Request
                            client_ipv6.add(packet.ip.src)
                            # return True, packet.ipv6.src
                        else :
                            pass   
                    else :
                        self.logger.info("For this packet neither IPv4 nor IPv6 matched.")
            except AttributeError:
                # Ignore packets that don't have required attributes
                continue
        
        if len(client_ipv4) == 0 and len(client_ipv6) == 0:
            return False, "Neither IPv4 nor IPv6 matched.", client_ipv4, client_ipv6
        return True, "Got client ip, either v4 or v6 or both", client_ipv4, client_ipv6
    
    def fetch_application_resolved_ip(self, pcap_file_url: str, domain: str, application: str = None) -> tuple[bool, str, set]:
        """
        Fetch resolved IP addresses for a given domain from DNS packets in the pcap file.

        Args:
        - pcap_file_url (str): Path of the pcap file to be analyzed.
        - domain (str): The domain whose resolved IP addresses need to be validated.

        Returns:
        - tuple[bool, str, set]: A tuple where:
            - The first value is a bool indicating success or failure.
            - The second value is the success or error message.
            - The third value is a set of resolved IP addresses.
        """
        ip_addresses = set()

        try:
            # Parse the pcap file and apply a DNS filter
            self.logger.info(f"Analyzing pcap file: {pcap_file_url}")
            capture = pyshark.FileCapture(
                pcap_file_url,
                display_filter=f'frame contains "{domain}" and dns',  # Filter by domain and DNS packets
            )

            try:
                for packet in capture:
                    try:
                        # Check if DNS layer is present in the packet
                        if "DNS" in packet:
                            # Fetch both IPv4 (A) and IPv6 (AAAA) records using getattr
                            if packet.dns.flags_response == "True":
                                ipv4_address = getattr(packet.dns, 'a', None)
                                ipv6_address = getattr(packet.dns, 'aaaa', None)
                            else:
                                continue

                            # Add IPv4 (A) records to the set
                            if ipv4_address is not None:
                                self.logger.info(f"Adding IPv4 address: {ipv4_address}")
                                # Handle multiple IPv4 records (comma-separated values)
                                for ip in ipv4_address.split(','):
                                    ip_addresses.add(ip.strip())

                            # Add IPv6 (AAAA) records to the set
                            if ipv6_address is not None:
                                self.logger.info(f"Adding IPv6 address: {ipv6_address}")
                                # Handle multiple IPv6 records (comma-separated values)
                                for ip in ipv6_address.split(','):
                                    ip_addresses.add(ip.strip())

                    except AttributeError:
                        # Handle packets missing attributes gracefully
                        self.logger.info("DNS packet missing required attributes. Skipping...")
                        continue

                capture.close()  # Close the capture explicitly after processing packets

                app_ip = self.fetch_all_resolved_ips(application=application)
                if app_ip[0] and app_ip[2]:
                    ip_addresses.update(app_ip[2])

                # Determine success or failure based on extracted IPs
                if ip_addresses:
                    self.logger.info(f"Resolved IPs fetched successfully : {ip_addresses}")
                    return True, "Resolved IPs fetched successfully.", ip_addresses
                else:
                    return False, f"No resolved IPs found for the domain '{domain}'.", ip_addresses

            except Exception as e:
                capture.close()  # Ensure capture is closed in case of error
                self.logger.error(f"An error occurred while processing packets: {str(e)}", exc_info=True)
                return False, f"Error occurred while processing packets: {str(e)}", ip_addresses

        except Exception as e:
            # Handle errors during file parsing and initialization
            self.logger.error(f"Failed to analyze pcap file '{pcap_file_url}': {str(e)}", exc_info=True)
            return False, f"Failed to analyze pcap file: {str(e)}", ip_addresses
    
    def delete_pcap_file(self, path:str):
        if os.path.exists(path=path):
            os.remove(path=path)

    def delete_pcap_folder(self, folder_path_respective_cwd:str):
        if os.path.exists(folder_path_respective_cwd):
            files_within = os.listdir(folder_path_respective_cwd)
            for each_file in files_within:
                each_file_path = os.path.join(folder_path_respective_cwd,each_file)
                self.delete_pcap_file(path=each_file_path)
            os.rmdir(folder_path_respective_cwd)
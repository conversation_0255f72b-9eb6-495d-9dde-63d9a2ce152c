################################################################
#                    ui_ZCC.py                                 #
#                                                              #
#      AUTHOR : SHRUTI KALRA(<EMAIL>)               #
#                                                              #
#                                                              #
################################################################

from datetime import datetime
from functools import wraps
import copy
import time
import allure
from appium.webdriver.common.mobileby import MobileBy
from common_lib.common.constants import *
from common_lib.common.common_ui import *
from common_lib.common.log_ops import LogOps
from OS_Ios.library.ops_system import SysOps
import os, shutil
import zipfile


class ZccElementsIos:

    # APP
    APP="com.zscaler.zscaler"
    SAFARI_APP="com.apple.mobilesafari"

    # LOGIN SCREEN 
    EMAILID = '//XCUIElementTypeTextField[@value="Email ID"]'
    USERNAME = '//XCUIElementTypeTextField[@value="Username"]'
    PASSWORD = "//XCUIElementTypeSecureTextField[@value='Password']"
    LOGIN_BUTTON = "//XCUIElementTypeStaticText[@name='Login']"
    ACCEPT_BUTTON = '//XCUIElementTypeButton[@name="Accept"]'
    DECLINE_BUTTON = '//XCUIElementTypeButton[@name="Decline"]'
    CANCEL = "//XCUIElementTypeStaticText[@name='Cancel']"
    ICON_BACK = "//XCUIElementTypeButton[@name='icon back']"
    HAMBURGER= "//XCUIElementTypeButton[@name='menu icon hamburger']"
    AUTHENTICATING_VIA_BROWSER = "//XCUIElementTypeStaticText[@name='Authenticating User via Browser'']"
    VPN_ALERT = "///XCUIElementTypeAlert[@name='“Zscaler” Would Like to Add VPN Configurations']/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeScrollView[1]/XCUIElementTypeOther[1]"
    VPN_ALERT_ALLOW = ("Allow", "//XCUIElementTypeButton[@name='Allow']")
    VPN_ALERT_DO_NOT_ALLOW = "//XCUIElementTypeButton[@name='Don’t Allow']"
    REGISTERING_DEVICE = '//XCUIElementTypeStaticText[@name="Registering Device..."]'
    
    # LOGOUT
    LOGOUT_BUTTON = "//XCUIElementTypeButton[@name='logoutNew']"
    LOGOUT_OK = "//XCUIElementTypeButton[@name='OK']"
    LOGOUT_PASSWORD = '//XCUIElementTypeSecureTextField[@value="Enter Password"]'
    LOGOUT_PASSWORD_OK_BUTTON = '//XCUIElementTypeButton[@name="OK"]'

    # INTERNET SECURITY TAB
    INTERNET_ACCESS = '//XCUIElementTypeButton[@name="Internet Security"]'
    TURN_OFF_INTERNET_SECURITY = "//XCUIElementTypeStaticText[@name='Are you sure you want to turn off Internet Security?']"
    TUNNEL_VERSION_LABEL = '//XCUIElementTypeStaticText[@name="Tunnel Version"]'
    TUNNEL_VERSION_VALUE = TUNNEL_VERSION_LABEL+'//following-sibling::XCUIElementTypeStaticText'
    TUNNEL_SDK_VERSION_LABEL_ZIA = "//XCUIElementTypeStaticText[@name=\"Tunnel SDK Version\"]"
    TUNNEL_SDK_VERSION_VALUE_ZIA = TUNNEL_SDK_VERSION_LABEL_ZIA+"//following-sibling::XCUIElementTypeStaticText"

    # PRIVATE ACCESS TAB
    ZPA_ON_STATUS = "//XCUIElementTypeStaticText[@name='ON']"
    PRIVATE_ACCESS = "//XCUIElementTypeButton[@name='Private Access']"
    AUTHENTICATION_STATUS_LABEL = '//XCUIElementTypeStaticText[@name="Authentication Status"]'
    AUTHENTICATION_STATUS_VALUE = AUTHENTICATION_STATUS_LABEL+'//following-sibling::XCUIElementTypeStaticText'
    BROKER_LABEL = '//XCUIElementTypeStaticText[@name="Broker"]'
    BROKER_VALUE = BROKER_LABEL+'//following-sibling::XCUIElementTypeStaticText'
    CLIENT_LABEL = '//XCUIElementTypeStaticText[@name="Client"]'
    CLIENT_VALUE = CLIENT_LABEL+'//XCUIElementTypeStaticText[@name="Client"]'
    Authentication_Required="//XCUIElementTypeStaticText[@name='Authentication Required']"
    REAUTHENTICATE = "//XCUIElementTypeStaticText[@name='Reauthenticate']"
    TUNNEL_PROTOCOL_LABEL = '//XCUIElementTypeStaticText[@name="Tunnel Protocol"]'
    TUNNEL_PROTOCOL_VALUE = TUNNEL_PROTOCOL_LABEL+'//following-sibling::XCUIElementTypeStaticText'
    DATA_SENT_LABEL = '//XCUIElementTypeStaticText[@name="Data Sent"]'
    DATA_SENT_VALUE = DATA_SENT_LABEL+'//following-sibling::XCUIElementTypeStaticText'
    DATA_RECEIVED_LABEL = '//XCUIElementTypeStaticText[@name="Data Received"]'
    DATA_RECEIVED_VALUE = DATA_RECEIVED_LABEL+'//following-sibling::XCUIElementTypeStaticText'
    TUNNEL_SDK_VERSION_LABEL_ZPA = '//XCUIElementTypeStaticText[@name="Tunnel SDK Version"]'
    TUNNEL_SDK_VERSION_VALUE_ZPA = TUNNEL_SDK_VERSION_LABEL_ZPA+'//following-sibling::XCUIElementTypeStaticText'

    # COMMON ELEMENTS
    USERNAME_LABEL = '//XCUIElementTypeStaticText[@name="Username"]'
    USERNAME_VALUE = USERNAME_LABEL+'//following-sibling::XCUIElementTypeStaticText'
    SERVICE_STATUS_LABEL = '//XCUIElementTypeStaticText[@name="Service Status"]'
    SERVICE_STATUS_VALUE = SERVICE_STATUS_LABEL+'//following-sibling::XCUIElementTypeStaticText'
    TOGGLE_VALUE = SERVICE_STATUS_LABEL+'//following-sibling::XCUIElementTypeButton/XCUIElementTypeStaticText'
    NETWORK_STATUS_LABEL = '//XCUIElementTypeStaticText[@name="Network Status"]'
    NETWORK_STATUS_VALUE = NETWORK_STATUS_LABEL+"//following-sibling::XCUIElementTypeStaticText"
    TURN_OFF_TAB = "//XCUIElementTypeStaticText[@name='TURN OFF']"
    TURN_ON_TAB = "//XCUIElementTypeStaticText[@name='TURN ON']"
    TURN_ON_STATUS ="//XCUIElementTypeStaticText[@name='ON']"
    TURN_OFF_STATUS ="//XCUIElementTypeStaticText[@name='OFF']"
    TRUSTED_NETWORK = '//XCUIElementTypeStaticText[@name="Trusted Network"]'
    OFF_TRUSTED_NETWORK = '//XCUIElementTypeStaticText[@name="Off-Trusted Network"]'
    BYTES_SENT_LABEL = "//XCUIElementTypeStaticText[@name='Bytes Sent']"
    BYTES_SENT_VALUE = BYTES_SENT_LABEL+"//following-sibling::XCUIElementTypeStaticText"
    BYTES_RECEIVED_LABEL = "//XCUIElementTypeStaticText[@name='Bytes Received']"
    BYTES_RECEIVED_VALUE = BYTES_RECEIVED_LABEL+"//following-sibling::XCUIElementTypeStaticText"
    TUNNEL_VERSION = "//XCUIElementTypeStaticText[@name='Tunnel Version']"
    CLIENT_IP_LABEL = "//XCUIElementTypeStaticText[@name='Client IP']"
    CLIENT_IP_VALUE = CLIENT_IP_LABEL+"//following-sibling::XCUIElementTypeStaticText"
    SERVER_IP_LABEL = "//XCUIElementTypeStaticText[@name='Server IP']"
    SERVER_IP_VALUE = SERVER_IP_LABEL+"//following-sibling::XCUIElementTypeStaticText"
    TIME_CONNECTED_LABEL = '//XCUIElementTypeStaticText[@name="Time Connected"]'
    TIME_CONNECTED_VALUE = TIME_CONNECTED_LABEL+"//following-sibling::XCUIElementTypeStaticText"
    STATISTICS = '//XCUIElementTypeOther[@name="STATISTICS" and @label="STATISTICS"]'
    OK = "//XCUIElementTypeStaticText[@name='OK']"
    OK_BUTTON ="//XCUIElementTypeButton[@name='OK']"

    # NOTIFICATION
    NOTIFICATION = "//XCUIElementTypeButton[@name='Notifications']"
    ZIA_ON_NOTIFICATION = "//XCUIElementTypeStaticText[@name='Zscaler Client Connector Internet Security Turned On.'']"
    ZPA_ON_NOTIFICATION="//XCUIElementTypeStaticText[@name='Zscaler Client Connector Private Access Turned On.'']" 
    NO_NOTIFICATION_FOUND =  '//XCUIElementTypeStaticText[@name="No notification found"]'
    BIN = '//XCUIElementTypeButton[@name="bin"]'


    # DISABLE SERVICE
    DISABLE_SERVICE_PASSWORD_FIELD = "//XCUIElementTypeSecureTextField[@value='Enter Password']"
    DISABLE_REASON_LABEL = "//XCUIElementTypeStaticText[@name='Disable Reason(1-200)']"
    DISABLE_REASON_FIELD = '//XCUIElementTypeTextView'
    DISABLE_ZPA_REASON = "//XCUIElementTypeStaticText[@name='Are you sure you want to turn off private access?']"

    # APP MORE
    MORE = "//XCUIElementTypeButton[@name='More']"
    ZCC_NOTIFICATION_LABEL = ('Show Notifications on app icon','//XCUIElementTypeStaticText[@name="Show Notifications on app icon"]')
    ZCC_NOTIFICATION_TOGGLE = ZCC_NOTIFICATION_LABEL[1]+'//following-sibling::XCUIElementTypeButton/XCUIElementTypeStaticText'
    ZCC_NOTIFICATION_STATE = ZCC_NOTIFICATION_LABEL[1]+'//following-sibling::XCUIElementTypeStaticText'
    ZPA_NOTIFICATION_LABEL = ('ZPA Reauthentication Notification', '//XCUIElementTypeStaticText[@name="ZPA Reauthentication Notification"]')
    ZPA_NOTIFICATION_TOGGLE = ZPA_NOTIFICATION_LABEL[1]+'//following-sibling::XCUIElementTypeButton/XCUIElementTypeStaticText'
    ZPA_NOTIFICATION_STATE = ZPA_NOTIFICATION_LABEL[1]+'//following-sibling::XCUIElementTypeStaticText'
    PACKET_CAPTURE = "//XCUIElementTypeStaticText[@name='Start Packet Capture']"
    STOP_PACKET_CAPTURE = "//XCUIElementTypeStaticText[@name='Stop Packet Capture']"
    REPORT_AN_ISSUE = "//XCUIElementTypeStaticText[@name='Report An Issue']"
    NAME = "//XCUIElementTypeTextField[@value='Name']"
    COMMENTS_BOX = '//XCUIElementTypeTextView[@value="Enter comments here"]'
    SEND = "//XCUIElementTypeStaticText[@name='Send']"
    CANCEL = "//XCUIElementTypeStaticText[@name='Cancel']"
    RAI_SUCCESS = '//XCUIElementTypeStaticText[@name="Success!"]'
    RESTART_SERVICE = "//XCUIElementTypeStaticText[@name='Restart Service']"
    CONFIRM_RESTART_SERVICE_ALERT = ('Click continue to restart tunnel', '//XCUIElementTypeStaticText[@name="Click continue to restart tunnel"]')
    CONFIRM = "//XCUIElementTypeStaticText[@name='Confirm']"
    CONTINUE = "//XCUIElementTypeButton[@name='Continue']"
    EXPORT_LOGS = "//XCUIElementTypeStaticText[@name='Export Logs']"
    CONFIRM_EXPORT_LOGS_ALERT = ('Click continue to export logs','//XCUIElementTypeStaticText[@name="Click continue to export logs"]')
    CLEAR_LOGS = "//XCUIElementTypeStaticText[@name='Clear Logs']"
    ALERT_CLEAR_LOGS = ('Logs Cleared', '//XCUIElementTypeStaticText[@name="Logs Cleared"]')
    UPDATE_POLICY = "//XCUIElementTypeStaticText[@name='Update Policy']"
    UPDATE_POLICY_SUCCESS = '//XCUIElementTypeStaticText[@name="Successfully updated policy"]'
    UPDATE_POLICY_FAILED = '//XCUIElementTypeStaticText[@name="Failed to update policy"]'
    LICENSE_AGREEMENT = '//XCUIElementTypeStaticText[@name="License Agreement and Third Party Software Notice"]'
    CLOSE = "//XCUIElementTypeStaticText[@name='Close']"
    LOG_LEVEL = "//XCUIElementTypeStaticText[@name='Log Level']"
    SELECT_LOG_MODE = ('Select Log Mode', '//XCUIElementTypeStaticText[@name="Select Log Mode"]')
    DEBUG = "//XCUIElementTypeButton[@name='Debug']"
    INFO = "//XCUIElementTypeButton[@name='Info']"
    WARN = "//XCUIElementTypeButton[@name='Warn']"
    ERROR = "//XCUIElementTypeButton[@name='Error']"
    CANCEL_BUTTON = '//XCUIElementTypeButton[@name="Cancel"]'
    CAPTURING = "//XCUIElementTypeStaticText[@name='Capturing...']"
    APP_VERSION = ('App Version', '//XCUIElementTypeStaticText[@name="App Version"]')
    APP_VERSION_VALUE = APP_VERSION[1]+'//following-sibling::XCUIElementTypeStaticText'
    APP_POLICY = ("App Policy",'//XCUIElementTypeStaticText[@name="App Policy"]')
    APP_POLICY_VALUE = APP_POLICY[1]+'//following-sibling::XCUIElementTypeStaticText'
    LICENSE_AGREEMENT_BUTTON = ('License Agreement','//XCUIElementTypeButton[@name="License Agreement"]')
    THIRD_PARTY_SOFTWARE = ('Third Party Software','//XCUIElementTypeButton[@name="Third Party Software"]')


    # SAFARI 
    SAFARI_SEARCH_BAR ="//XCUIElementTypeTextField[@name='TabBarItemTitle']"
    SAFARI_SEARCH_BAR_TEXT="//XCUIElementTypeApplication[@name='Safari']/XCUIElementTypeWindow[3]/XCUIElementTypeOther/XCUIElementTypeOther"
    SAFARI_TAB_OVERVIEW = "//XCUIElementTypeButton[@name='TabOverviewButton']"
    SAFARI_OPEN_NEW_TAB="//XCUIElementTypeOther[@name='CapsuleViewController']/XCUIElementTypeOther[1]"
    PROXY_VIA_ZSCALER="//XCUIElementTypeStaticText[@name='You are accessing the Internet via a Zscaler BETA proxy hosted Singapore IV in the zscalerbeta.net cloud.']"
    PROXY_NOT_VIA_ZSCALER ="//XCUIElementTypeStaticText[@name='The request received from you didn't come from a Zscaler IP therefore you are not going through the Zscaler proxy service.']"
    GO = "//XCUIElementTypeButton[@name='Go']"
    SAFARI_OPEN_BUTTON = '//XCUIElementTypeStaticText[@name="Open"]'

    # CHROME
    GOOGLE_SEARCH= "(//XCUIElementTypeStaticText[@name='Search or type URL'])[1]"
    CHROME_SEARCH_BAR="//XCUIElementTypeTextField[@name='Address']"
    CHROME_GO = "//XCUIElementTypeButton[@name='Go']"

    # LOGIN ZPA/ZIA OKTA
    OKTA_SIGNIN_PAGE = ''
    OKTA_SIGNIN_PAGE_2 = ''
    OKTA_USERNAME = '//XCUIElementTypeTextField'
    OKTA_PASSWORD_BOX = '//XCUIElementTypeSecureTextField'
    OKTA_KEEP_ME_SIGNED_IN = "//XCUIElementTypeStaticText[@name='Keep me signed in']"
    REAUTH_OKTA_USERNAME = ''
    REAUTH_OKTA_PASSWORD_BOX = ''
    OKTA_SIGN_IN_BUTTON = '//XCUIElementTypeButton[@name="Sign in"]'
    ZCC_BACK_BUTTON_AT_OKTA = ""
    OKTA_SIGN_IN_TEXT = "//XCUIElementTypeStaticText[@name='Sign In']"

    #System 
    AIRDROP = '//XCUIElementTypeCell[@name="AirDrop"]/XCUIElementTypeOther/XCUIElementTypeImage'
    SAVE_TO_FILES = '//XCUIElementTypeCell[@name="Save to Files"]'
    SAVE_BUTTON = '//XCUIElementTypeButton[@name="Save"]'


class Zcc():
    """ ZCC class """
    def __init__(self,
                 handle: str ="easy",
                 start_app: bool =False,
                 log_handle: bool =None
                 ):
        """
        initial function 
        """

        self.logger = (log_handle if log_handle else Logger.initialize_logger(log_file_name="ZCC.log", log_level="INFO"))     
        self.ui = Gui(handle=handle,log_handle=self.logger)
        self.operating_system = self.ui.operating_system  
        self.logger.info("Initialized ZCC Gui")
        #self.dynamic_element_check = dynamic_element_check
        self.start_app=start_app
        self.sleep_factor = 1
        self.initialize_elements()
        self.start_time_for_log_search=""
        self.log_ops = LogOps(console_handle_needed=False)

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    
    def initialize_elements(self)->tuple[bool,str,None]:
        """Initialize the ZCC elements i.e xpaths"""
        try:
            if self.start_app:
                self.ui.start_app(app=ZccElementsIos.APP, sleep_time=20)
                self.elements = copy.deepcopy(ZccElementsIos)
                self.ui.app=self.elements.APP    
                # self.ui.driver.background_app(1)
                return (True, "App started successfully", self.elements)
            else:
                return False, "App not started", None
        except Exception as e:
            return False, str(e), None
    
    def get_driver(self):
        return(self.ui.driver)
    
    # -----------------------------------------------------------------------------------------------------------------------------------------------------

    def calculate_func_execute_time(func):
        """
        Decorator used for calculating time taken for execution of a function
        Flow: ------------------------------
            1. Func is passed to calculate_func_execute_time which calls wrapper func
            2. Wrapper func then first gets the current time(start time) and then make call to the given func as arg in calculate_func_execute_time
            3. After func is executed, we then get the current time(end time) and then calculate endtime-starttime and get the time difference
        """
        @wraps(func)
        def wrapper(*args,**kwargs):
            start_time = time.time()
            #print("START TIME : None".format(startTime))
            res=func(*args,**kwargs)
            end_time = time.time()
            #print("END TIME : None".format(endTime))
            args[0].FileTransferTime = int(end_time-start_time)
            print("\n")
            args[0].logger.critical(f"{'*'*50}")
            args[0].logger.critical(f"TIME TAKEN FOR {func.__name__} : {args[0].FileTransferTime} Seconds")
            args[0].logger.critical(f"{'*'*50}")
            return res
        return wrapper

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    
    def check_for_alerts_after_login(self):
        """
        This function checks for alert after login
        """
        self.logger.info("###### Checking for alerts after login if user logged in for the first time ######")
        ui_alerts = ["VPN", "Notification"]
        for i in range(2):
            try:
                self.logger.info(f"###### Checking if ZCC is asking to enable {ui_alerts[i]} ######")
                WebDriverWait(self.ui.driver, timeout=int(10/(i+1))).until(ec.alert_is_present())
                is_alert_present = self.is_alert_present()[0]
                if is_alert_present:
                    self.handle_alerts(allow=True)
            except Exception as e:
                print(e)

    # -----------------------------------------------------------------------------------------------------------------------------------------------------

    @allure.step("Validate ZCC login")       
    @calculate_func_execute_time
    def validate_zcc_logged_in(self,
                               check_alerts_after_login:bool = True,    #True if need to check alerts after login else False
                               dynamic_wait_search_element=40,          #wait time for access
                               sleep_time=1                             #sleep time after execution
                               ) ->tuple[bool,str,None]:
        """
        Validate whether zcc is logged in or not

        args :
        check_alerts_after_login:bool -->  checks and handle VPN and alerts after login Default - True 
        dynamic_wait_search_element:int --> wait time for accessing elements
        sleep_time:int --> sleep time after function execution

        returns :
         returns a tuple --> (status, message, None)

        """
        res=self.bring_zcc_to_focus()
        if not res[0]:
            return res
        i=0
        zcc_logged_in_validated =False
        while i<=3:    
            try:
                zcc_logged_in_validated= self.ui.check_if_exists(self.elements.LOGOUT_BUTTON, dynamic_wait_search_element=dynamic_wait_search_element)
                time.sleep(sleep_time)
            except Exception as e:
            # Log the exception and continue with the loop
                self.logger.exception(f"Exception occurred while checking for logout button: {e}")
                i += 1
                time.sleep(5)
            else:
            # If no exception was raised, break the loop
                break
        if zcc_logged_in_validated:
            self.logger.info("*********Logged into ZCC successfully**********")
            if check_alerts_after_login:
                self.check_for_alerts_after_login()
            self.logger.info(f"###### Logout button exists, zcc logged in ######")
            return (zcc_logged_in_validated, "Logout button exists, zcc logged in", None) # Add return statement here
        else:
            self.logger.error("Logout button does not exists, zcc already logged out...")
            return (False, "Logout button does not exists, zcc already logged out...", None) # Add return statement here

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    
    # handle alerts after login
    def handle_alerts(self, 
                      allow: bool=False      # True if allow alerts else False
                    ):  #
        """
        This function handles alerts accept/dismiss
        """
        try:
            alert_msg = self.ui.driver.switch_to.alert.text
            self.logger.info(f"Found Alert : {alert_msg}")
            if "VPN" in alert_msg:
                self.logger.info("**********VPN Alert**********")
                if allow:
                    self.logger.info("Allowing VPN")
                    self.ui.driver.switch_to.alert.dismiss()
                else:
                    self.logger.info("Not allowing VPN")
                    self.ui.driver.switch_to.alert.accept()
            elif "Notifications" in alert_msg:
                self.logger.info("**********Notification Permission**********")
                if allow:
                    self.logger.info("Allowing Notifications")
                    self.ui.driver.switch_to.alert.accept()
                else:
                    self.logger.info("Not allowing Notifications")
                    self.ui.driver.switch_to.alert.dismiss()
            return (True, "Alerts handled", None)
        except Exception as e:
            return (False, f"Error :: {e}", None)

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    
    # helper function for handling alerts after login
    def is_alert_present(self):
        """
        This function checks if any alert is present on UI
        returns - (status, msg, Text on Alert)
        """
        try:
            self.ui.driver.switch_to.alert
            return (True, "Alert is present", self.ui.driver.switch_to.alert.text)
        except NoAlertPresentException:
            return (False, "Alert is not present", self.ui.driver.switch_to.alert.text)

    # -----------------------------------------------------------------------------------------------------------------------------------------------------

    def bring_zcc_to_focus(self,
                           number_of_trials: int = 2,      # number of times to try to open Zcc before raising exception
                           sleep_before_focus: int = 1,    # int seconds to sleep before bringing zcc to focus
                            ) -> tuple[bool, str, None]:
        """  
        This function helps to bring zcc forefront
        """
        self.logger.info("in bring_zcc_to_focus")
        time.sleep(sleep_before_focus)
        for i in range(number_of_trials):
            try:
                self.ui.driver.activate_app(self.elements.APP)
                return True, "ZCC Brought to Focus", None  # Return success status, message and empty dictionary
                
            except Exception as e:
                self.logger.info(f"Warning :: trying to bring ZCC to Focus again! ; {e}\n")
                if i == number_of_trials - 1:  # If this is the last trial
                    self.logger.error("Failed to bring ZCC to Focus")
                    return False,"Failed to bring ZCC to Focus", None
                else:
                    self.logger.info("Retrying...\n")
                    time.sleep(1)  # Wait for a second before the next trial
                    
    # ---------------------------------------------------------- #
    #               HELPER FUNCTIONS FOR ZCC LOGIN               #   
    # ---------------------------------------------------------- #
    def do_okta_login(
                      self,
                      user_id : str,                    # okta username
                      user_password : str,              # okta password
                      search_log_time : str = None,     # time to be used for log search - datetime.now() should be passed
                      partial_login : bool = False,     # defines whether to terminate login or do complete login
                      search_file: str = "latest",      # defines whether to search latest file or oldest file
                      bba_enabled: bool = False,        # True if bba is enabled else False 
                      ) -> tuple[bool, str, None]:
        """ Function that handles okta login  """
        try:
            if partial_login:
                self.logger.info("Aborting Okta Login , hitting back button on ZCC")
                self.ui.click(self.elements.ZCC_BACK_BUTTON_AT_OKTA,sleep_time=1, AEFC="Cannot click ZCC Back Button")  
                return (False, "Partial login requested.",None)

            self.logger.info("########## Initializing OKTA Login")
            time.sleep(10)
            if bba_enabled:
                    if self.ui.check_if_exists(path=self.elements.SAFARI_OPEN_BUTTON, boolean=True, dynamic_wait_search_element=10):
                        self.ui.click(path=self.elements.SAFARI_OPEN_BUTTON, dynamic_wait_search_element=10)
                        self.logger.info("########## Success :: Okta Login Successful ##########") 
                        return True, "Success::Okta login successful", None # Added return statement here with empty dictionary as third element of the tuple

            self.ui.click(path=self.elements.OKTA_SIGN_IN_TEXT, dynamic_wait_search_element=20)
            okta_username=self.ui.check_if_exists(self.elements.OKTA_USERNAME,boolean=True, dynamic_wait_search_element=10)
            okta_password_box=self.ui.check_if_exists(self.elements.OKTA_PASSWORD_BOX,boolean=True, dynamic_wait_search_element=10)

            if okta_username and okta_password_box:
                self.ui.driver.find_element(By.XPATH, self.elements.OKTA_USERNAME).click() #adding click and clear as it is not working with type function
                self.ui.driver.find_element(By.XPATH, self.elements.OKTA_USERNAME).clear()
                self.ui.click(self.elements.OKTA_USERNAME,retry_frequency=5,max_threshold=10,sleep_time=1, AEFC="Cannot click okta username")
                self.ui.type(path=self.elements.OKTA_USERNAME, text=user_id, sleep_time=1, AEFC="Something wrong with username")
                self.ui.click(self.elements.OKTA_PASSWORD_BOX, AEFC="Cannot click okta password")
                self.ui.type(path=self.elements.OKTA_PASSWORD_BOX, text=user_password, sleep_time=1, AEFC="Something wrong with passwd")
                self.ui.click(self.elements.OKTA_KEEP_ME_SIGNED_IN,retry_frequency=5,max_threshold=10,sleep_time=1, AEFC="Error in enabling Keep me signed in")
                self.ui.click(self.elements.OKTA_KEEP_ME_SIGNED_IN,retry_frequency=5,max_threshold=10,sleep_time=1, AEFC="Error in disabling Keep me signed in")
                okta_sign_in=self.ui.check_if_exists(self.elements.OKTA_SIGN_IN_BUTTON,boolean=True)
                if okta_sign_in:
                    self.ui.click(self.elements.OKTA_SIGN_IN_BUTTON, AEFC="Cannot click okta Sign In button")
                else:
                    self.ui.click(self.elements.GO, AEFC="Cannot click GO button on keyboard on Okta page")
            
            if bba_enabled:
                    if self.ui.check_if_exists(path=self.elements.SAFARI_OPEN_BUTTON, boolean=True, dynamic_wait_search_element=20):
                        self.ui.click(path=self.elements.SAFARI_OPEN_BUTTON)
                
            self.logger.info("########## Success :: Okta Login Successful ##########") 
            return True, "Success::Okta login successful", None # Added return statement here with empty dictionary as third element of the tuple
        except Exception as e:
            self.logger.error(f"########## Failed  :: Okta Login Failed due to {e} ##########") 
            return False, f"Error::{str(e)}", None # Added return statement in case

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
       
    def initial_zcc_login_window(
                                 self,
                                 username: str  #username to login
                                 ) -> tuple[bool, str, None]:
        
        """helper function for initial userfield at zcc login page"""
        try:
            time.sleep(1)
            # Type the username into the input field and click the login button
            if self.ui.check_if_exists(path=self.elements.USERNAME, dynamic_wait_search_element=5):
                self.ui.type(self.elements.USERNAME, text=username, sleep_time=1, AEFC="Something wrong with username", dynamic_wait_search_element=10)
            elif self.ui.check_if_exists(path=self.elements.EMAILID, dynamic_wait_search_element=5):
                # For exception handling as Email ID field is present when app config is passed through MDM
                self.ui.type(self.elements.EMAILID, text=username, sleep_time=1, AEFC="Something wrong with email id", dynamic_wait_search_element=10)
            else:
                self.logger.error(f"Error :: Neither Email ID nor Username field found on login page")
                raise Exception("Error :: Neither Email ID nor Username field found on login page")
            self.ui.click(self.elements.LOGIN_BUTTON,sleep_time=5, AEFC="Unable to Click Login Button")

            # Record the start time for log search
            self.start_time_for_log_search = datetime.datetime.now()
            
            # Return a tuple indicating success and an empty string as a placeholder
            return True, "Success", None

        except Exception as e:
            self.logger.error(f"Error :: Login Failed at USER_ID Page :: {e}")
            return False, str(e), None

    
    # -----------------------------------------------------------------------------------------------------------------------------------------------------
        
    def handle_aup(
                   self,
                   time_for_log_search,             # datetime.now()
                   zia_saml_login: bool = False,    #True if ZIA saml login is used for authentication method else False
                   cancel_aup: bool = False,        #True if cancel AUP else False
                   ) -> tuple[bool, str, None]:     #return 
        """helper function for handling zcc aup login"""

        try:
            self.bring_zcc_to_focus()
            self.ui.click(self.elements.DECLINE_BUTTON if cancel_aup else self.elements.ACCEPT_BUTTON, sleep_time=1, AEFC="Cannot click aup Decline" if cancel_aup else "Cannot click aup Accept")
            # self.start_time_for_log_search = datetime.now()
            return True, "AUP process completed successfully",None  # Added return statement with tuple
        except Exception as e:
            self.logger.error(f"Error :: Login Failed at aup Page :: {e}")
            return False, f"Error :: Login Failed at aup Page :: {e}", None  # Added return statement with tuple

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    
    def handle_zia_saml(
                        self,               # helper function for zia saml login
                        username: str,                  #username to login
                        password : str,                 #password to login
                        time_for_log_search,            # datetime.now() object
                        cancel_zia_saml_login : bool,   #True if cancel login else False
                        bba_enabled: bool               #True if bba is enabled else False 
                        )->tuple[bool,str,None]:
        """ This function handles zia saml login"""
        if cancel_zia_saml_login:
            self.logger.info("Login aborted at zia SAML")
            return False, "Login aborted", None
        else:
            self.logger.info("########## Redirecting to ZIA SAML Login Page ##########")
            res=self.do_okta_login(user_id=username,user_password=password,search_log_time=time_for_log_search,partial_login=cancel_zia_saml_login, bba_enabled=bba_enabled)
            if not res[0]:
                return res
        self.logger.info("########## Success :: ZIA SAML Login ##########")
        return True, "ZIA Saml Login successful", None

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
        
    def handle_zia_login(self,                  # helper function for zia login
                        password: str,          #password to login
                        time_for_log_search,    # datetime.now() object
                        cancel_zia_login: bool, #True if cancel ZIA login else False
                        zpa
                        ) -> tuple[bool, str, None]:
        
        """ This function handles zia login"""
        status = False
        try:
            if cancel_zia_login:
                self.logger.info("Aborting Login at zia, hitting back button on ZCC")
                self.ui.click(self.elements.ICON_BACK,sleep_time=1, AEFC="Cannot click ZCC Back Button")
                status=True
                return status, "Success" if status else "Failure", None
            else:
                is_password_box_up = False
                is_password_box_up = self.ui.check_if_exists(self.elements.PASSWORD,sleep_time=2,dynamic_wait_search_element=90)
                if is_password_box_up:
                    self.ui.type(self.elements.PASSWORD, text=password, sleep_time=1, AEFC="password button not found")
                    self.ui.click(self.elements.LOGIN_BUTTON, sleep_time=5, AEFC="Unable to click Login Button")
                    status=True
                    return status, "Success" if status else "Failure", None
        except Exception as e:
            self.logger.error(f"Error :: Login Failed at Form password Page :: {e}")
            return False,"Login Failed at Form password Page",None
    
    # -----------------------------------------------------------------------------------------------------------------------------------------------------

    def handle_zia_one_id_login(self):
        """ placeholder for zia one id login
        """

        pass

    # ------------------------------------------------------------------------------------------------------------------------------------------------------   
    
    def handle_zpa_login(self,
                         username: str,              #username to login
                         password: str,              #password to login
                         time_for_log_search,        # datetime.now() object
                         cancel_zpa_login: bool,     #True if cancel ZPA login
                         bba_enabled: bool           #True if bba is enabled for ZPA login
                         ) -> tuple[bool, str, None]:
        
        """  helper function for zpa login   """
        status = False
        if cancel_zpa_login:
            self.logger.info("Login aborted at zpa SAML")
            return status, "Login aborted", None
        else:
            self.logger.info("***** Initializing ZPA Login *****")
            res=self.do_okta_login(user_id=username,user_password=password,search_log_time=time_for_log_search,partial_login=cancel_zpa_login,bba_enabled=bba_enabled)
            if not res[0]:
                return res
            status=True
            self.logger.info("***** ZPA Login successful *****")
            return status, "Login successful", None
    
    # -----------------------------------------------------------------------------------------------------------------------------------------------------

    @allure.step("zcc login")       
    @calculate_func_execute_time
    def login(self,                              # PARENT FUNCTION FOR ZCC LOGIN
              zia_user: str = None,              # zia user id
              zia_password: str = None,          # zia user password
              zpa_user: str = None,              # zpa user id
              zpa_password: str = None,          # zpa user password
              aup: bool= False,                  # Defines aup enabled or not 
              zia_saml_login: bool= False,       # Defines zia SAML enabled or not
              zia_one_id_login: bool= False,     # Defines zia SAML enabled or not
              cancel_login_at_zia: bool= False,  # Defines whether to cancel login at zia login page 
              cancel_login_at_zpa: bool= False,  # Defines whether to cancel login at zpa login page
              cancel_aup: bool= False,           # Defines whether to cancel or decline aup
              sleep_time: int = 10,              # Defines sleep time after login is done
              bba_enabled: bool = False,         # True if bba is enabled else False
              skip_initial_window: bool = False  # True if authentication starts with Okta directly / username is passed through MDM 
              )->tuple[bool,str,None]:   

        ''' Function for login zcc'''

        try:
            zcc_in_focus = self.restart_zcc(5)
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[1])
            status=True
            zia,zpa = False,False
            if zia_user==zia_password==zpa_user==zpa_password==None:  # no zia user is given, check for zpa user
                
                self.logger.error("Neither zia or zpa user given to be logged in, cannot login")
                raise Exception("Neither zia or zpa user given to be logged in, cannot login")
            
            if zia_user and zia_password:
                zia=True
            if zpa_user and zpa_password: 
                zpa=True     
            if zia and zpa:
                self.logger.info("Given user details are zia+zpa")
            if not zia:
                self.logger.info("Given user is zpa only user")
            if not zpa:
                self.logger.info("Given user is zia only user")
        
        # Initial username field -------------------------
            if not skip_initial_window:
                if zia or zpa:
                    res = self.initial_zcc_login_window(
                        username=zia_user if zia else zpa_user)
                    if not res[0]:
                        raise Exception(res[1])
            
            # aup -------------------------
            if aup:
                res = self.handle_aup(
                    time_for_log_search=self.start_time_for_log_search,
                    zia_saml_login=zia_saml_login,
                    cancel_aup=cancel_aup)
                if not res[0]:
                    raise Exception(res[1])
                self.logger.info("AUP handled")

            # zia -------------------------
            if zia: 
                if zia_saml_login:
                        res = self.handle_zia_saml(
                            username=zia_user,
                            password=zia_password,
                            time_for_log_search=self.start_time_for_log_search,
                            cancel_zia_saml_login=cancel_login_at_zia,
                            bba_enabled=bba_enabled)
                        if not res[0]:
                            raise Exception(res[1])
                        self.logger.info("Zia Saml Login Successful")
                elif zia_one_id_login:
                        self.handle_zia_one_id_login()
                else:
                        res = self.handle_zia_login(
                            password=zia_password,
                            time_for_log_search=self.start_time_for_log_search,
                            cancel_zia_login=cancel_login_at_zia,zpa=zpa)
                        if not res[0]:
                            raise Exception(res[1])
                try:
                    self.ui.explicit_wait(xpath=self.elements.REGISTERING_DEVICE) 
                except:
                    pass
                time_taken_for_registering = 0
                while(self.ui.check_if_exists(path=self.elements.REGISTERING_DEVICE,dynamic_wait_search_element=2)):
                    time.sleep(5)
                    time_taken_for_registering+=5
                    self.logger.info(f"Registering Device... took {time_taken_for_registering} seconds so far")
                    if time_taken_for_registering >180:
                        raise Exception("Error :: Time taken for registering ZIA service is more than 3 minutes")
                self.logger.info("Zia Login Successful")
            # zpa -------------------------
            if zpa:
                if bba_enabled:
                    time.sleep(15)
                    if self.ui.check_if_exists(path=self.elements.SAFARI_OPEN_BUTTON, boolean=True, dynamic_wait_search_element=20):
                        self.ui.click(path=self.elements.SAFARI_OPEN_BUTTON, dynamic_wait_search_element=10)
                if self.ui.check_if_exists(path=self.elements.OKTA_SIGN_IN_TEXT, boolean=True, dynamic_wait_search_element=40):
                    res = self.handle_zpa_login(username=zpa_user, password=zpa_password,
                                            time_for_log_search=self.start_time_for_log_search, cancel_zpa_login=cancel_login_at_zpa, bba_enabled=bba_enabled)
                    if not res[0]:
                        raise Exception(res[1])
                self.logger.info("Zpa Login Successful")      
                    
            self.logger.info("Success :: Logged into ZCC Succesfully!")
        except Exception as e:   # Add exception handling to return a tuple with status and error message. 
            status = False
            error = str(e)
            return (status, error, None)  # Return a tuple with status as False and the error message. 
        return (status, "Success", None)  # Return a tuple with status as True and success message.
        
    # -----------------------------------------------------------------------------------------------------------------------------------------------------

    @calculate_func_execute_time
    @allure.step("logout ZCC")
    def logout( self,
                password: str =None,            # Passowrd/OTP to be used for logout
                sleep_time: int =15,           # number of seconds to sleep when executed
                failure_expected: bool=False,   # Defines whether logout should fail - used with incorrect password
                cancel_logout: bool = False,    # Defines whether to cancel logout operation
                force_logout: bool = False      # True to force logout (for teardown method)
               )->tuple[bool,str,None]:
        """"
        This function is used to log out of a system or service.
        :param self: Instance of the class.
        :param password: Password or OTP to be used for logout. Default is None.
        :param sleep_time: Number of seconds to sleep when the function is executed. Default is 180.
        :param failure_expected: Flag that defines whether the logout should fail. Default is False.
        :param cancel_logout: Flag that defines whether to cancel the logout operation. Default is False.
        """
    
        if sleep_time==0:
            sleep_time=30
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            if force_logout:
                self.restart_zcc(sleep_time=15)
            self.ui.click(self.elements.LOGOUT_BUTTON, sleep_time=1 * self.sleep_factor,AEFC="Unable to click Logout Button")
            if cancel_logout:
                self.logger.info("Aborting Logout")
                self.ui.click(self.elements.CANCEL_BUTTON, sleep_time=0, AEFC="Unable to confirm Logout disable")
                return(True,"Success::Logout cancelled",None)
            # If a password is provided, enter it and confirm the logout operation
            if password:
                self.ui.type(self.elements.LOGOUT_PASSWORD, text=password, sleep_time=1*self.sleep_factor, AEFC="Unable to enter password")
                self.ui.click(self.elements.LOGOUT_PASSWORD_OK_BUTTON, sleep_time=0*self.sleep_factor, AEFC="Unable to confirm Logout")
            # If no password is provided, simply click the OK button to confirm logout and handle any expected errors
            else:
                self.ui.click(self.elements.LOGOUT_OK,sleep_time=0*self.sleep_factor,AEFC="unable to logout prompt")
            if failure_expected:    # mostly used when incorrect password is given intentionally
                    print("***** Failure Expected *****")
                    time.sleep(5)
                    not_logged_out = self.ui.check_if_exists(path=self.elements.MORE)
                    if not_logged_out:
                        return (True,"Success :: Not logged out as Failure was expected",None)
                    else:
                        return (False,"Failure :: Logged out unexpectedly", None)
            # Assert that the logout was successful by checking if the login button exists (should exist after logging out)
            self.logger.info("logging out...")
            time.sleep(sleep_time)
            if self.ui.check_if_exists(self.elements.LOGIN_BUTTON, boolean=True, AEFC="Login button doesn't exist", AESC="Login button exists", dynamic_wait_search_element=60) or self.ui.check_if_exists(self.elements.OKTA_SIGN_IN_BUTTON, boolean=True, AEFC="Sign in button doesn't exist", AESC="Login button exists", dynamic_wait_search_element=60):
                self.logger.info("********** Successfully logged out **********")
                return (True, "Success::Logout successful")
            else:
                self.logger.error("********** Logout Failed **********")
                return (False, "Error::Logout Failed")
        except Exception as e:
            print("adding for code modify") 
            self.logger.error("Logout Failed at ZSTray Page ; {}\n".format(e))
            return False,"Logout Failed at ZSTray Page",None
    
    # -----------------------------------------------------------------------------------------------------------------------------------------------------

    def validate_tunnel_version(self, expected_version: str, tab:str="zia", sdk: bool = False) -> tuple[bool, str]: # this function was also added to verify tunnel/sdk version with the expected version given
        """
        Checks if the fetched Tunnel Version value from the UI matches the expected version.
        Works for both Tunnel version as well as for Tunnel SDK version and for both tunnel version v1.0 as well as v2.0

        Args:
            expected_version (str): The version to validate against, e.g., "v1.0", "v2.0".
            sdk (bool): If true then this function will validate Tunnel SDK version and if False then this function will validate Tunnel Version (v1.0 or v2.0).

        Returns:
            tuple[bool, str]: Tuple containing:
                - status (True/False): Validation result.
                - message (str): Success or failure reason.
        """
        tunnel_sdk_version_value = ""
        if tab=="zpa" :
            self.ui.click(self.elements.PRIVATE_ACCESS, sleep_time=0, AEFC="Unable to click Internet Security button on ZCC")
            tunnel_sdk_version_value = self.elements.TUNNEL_SDK_VERSION_VALUE_ZPA
        else :
            self.ui.click(self.elements.INTERNET_ACCESS, sleep_time=0, AEFC="Unable to click Internet Access button on ZCC")
            tunnel_sdk_version_value = self.elements.TUNNEL_SDK_VERSION_VALUE_ZIA
        if sdk :
            try:
                self.logger.info("Fetching Tunnel SDK Version value from the UI...")
                # Fetch the tunnel sdk version value using the `fetch_attribute` function
                fetched_version = self.ui.fetch_attribute(xpath=tunnel_sdk_version_value, fetch_text=True)
                self.logger.info(f" Fetched version is: {fetched_version}")
                # Validate whether the fetched version matches the expected version
                if fetched_version is None:
                    self.logger.error("Failed to fetch Tunnel SDK Version value from the UI.")
                    return False, "Failure: Tunnel SDK Version value could not be fetched from the UI."
                if fetched_version == expected_version:
                    self.logger.info(f"Tunnel SDK Version validation successful. Fetched: {fetched_version}, Expected: {expected_version}")
                    return True, f"Success: Tunnel SDK Version matches the expected version '{expected_version}'."
                else:
                    self.logger.warning(f"Tunnel SDK Version validation failed. Fetched: {fetched_version}, Expected: {expected_version}")
                    return False, f"Failure: Tunnel SDK Version '{fetched_version}' does not match the expected version '{expected_version}'."
            except Exception as e:
                self.logger.error(f"Exception occurred during Tunnel SDK Version validation: {e}")
                return False, f"Error: Unable to validate Tunnel SDK Version. Exception: {e}"
        else :
            try:
                self.logger.info("Fetching Tunnel Version value from the UI...")
                # Fetch the tunnel version value using the `fetch_attribute` function
                fetched_version = self.ui.fetch_attribute(xpath=self.elements.TUNNEL_VERSION_VALUE, fetch_text=True)
                # Validate whether the fetched version matches the expected version
                if fetched_version is None:
                    self.logger.error("Failed to fetch Tunnel Version value from the UI.")
                    return False, "Failure: Tunnel Version value could not be fetched from the UI."
                # ver1 = expected_version + " - TLS"
                # ver2 = expected_version + " - DTLS"
                if fetched_version == expected_version:
                    self.logger.info(f"Tunnel Version validation successful. Fetched: {fetched_version}, Expected: {expected_version}")
                    return True, f"Success: Tunnel Version matches the expected version '{expected_version}'."
                # elif fetched_version == ver1:
                #     self.logger.info(f"Tunnel Version validation successful. Fetched: {fetched_version}, Expected: {ver1}")
                #     return True, f"Success: Tunnel Version matches the expected version '{ver1}'."
                # elif fetched_version == ver2:
                #     self.logger.info(f"Tunnel Version validation successful. Fetched: {fetched_version}, Expected: {ver2}")
                #     return True, f"Success: Tunnel Version matches the expected version '{ver2}'."
                else:
                    self.logger.warning(f"Tunnel Version validation failed. Fetched: {fetched_version}, Expected: {expected_version}")
                    return False, f"Failure: Tunnel Version '{fetched_version}' does not match the expected version '{expected_version}'."
            except Exception as e:
                self.logger.error(f"Exception occurred during Tunnel Version validation: {e}")
                return False, f"Error: Unable to validate Tunnel Version. Exception: {e}"

    # -----------------------------------------------------------------------------------------------------------------------------------------------------


    @calculate_func_execute_time
    @allure.step("validate_tunnel_status")
    def validate_tunnel_status(self,                    
                               zia_only_company=False,      #True if need to validate ZIA only tunnel status else False
                               zpa_only_company=False,      #True if need to validate ZPA only tunnel status else False
                               zia_on=False,                #True if ZIA service is expected to be in ON state
                               zpa_on=False                 #True if ZPA service is expected to be in ON state
                               ) -> tuple[bool, str, None]:
        """ Validating tunnel status for zia and zpa """
        res = self.bring_zcc_to_focus()
        if not res[0]:
            return res
        def check_status(access_element, status_element, status_on):
            self.ui.click(access_element)
            self.ui.driver.swipe(190,275,190,481)
            status_check = self.ui.check_if_exists(status_element,boolean=True, dynamic_wait_search_element=60)
            if status_check:
                self.logger.info(f"Status is {'On' if status_on else 'OFF'}")
            else:
                raise Exception(f"Failure:: Status is not {'On' if status_on else 'OFF'}")

        try:
            if zia_only_company:
                check_status(self.elements.INTERNET_ACCESS, self.elements.TURN_ON_STATUS if zia_on else self.elements.TURN_OFF_STATUS, zia_on)

            if zpa_only_company:
                check_status(self.elements.PRIVATE_ACCESS, self.elements.TURN_ON_STATUS if zpa_on else self.elements.TURN_OFF_STATUS, zpa_on)

            if zia_only_company and zpa_only_company:
                check_status(self.elements.INTERNET_ACCESS, self.elements.TURN_ON_STATUS if zia_on else self.elements.TURN_OFF_STATUS, zia_on)
                check_status(self.elements.PRIVATE_ACCESS, self.elements.TURN_ON_STATUS if zpa_on else self.elements.TURN_OFF_STATUS, zpa_on)
            
            return True, "Success::", None
        except Exception as e:
            return False, str(e), None
            
    # -----------------------------------------------------------------------------------------------------------------------------------------------------
            
    @calculate_func_execute_time
    @allure.step("Enable/disable Service ZIA/ZPA/ZDX")
    def toggle_service( self,
                        service: str,                   # Which service to Toggle (ZIA/ZPA/ZDX)
                        action: bool =True,             # Turn on if True, otherwise Turn off
                        password: str =None,            # Password/OTP for disabling service
                        disable_reason: str =None,      # Disable reason to enter while disabling any zscaler service (zia/zpa/zdx)
                        sleep_time=10,                  # Number of seconds to sleep after execution
                        cancel_toggle: bool =False,     # Determines whether to cancel toggle operation or not
                        failure_expected: bool =False   # Expect that password will not be accepted. deal accordingly
                        ):     
        """
            Toggle a Zscaler service (ZIA/ZPA/ZDX) on or off.

            Args:
                service (str): The service to toggle (ZIA/ZPA/ZDX).
                action (bool): If True, turn on the service. Otherwise, turn it off. Default is True.
                password (str): The password or OTP for disabling the service. Default is None.
                disable_reason (str): The disable reason to enter when disabling a Zscaler service. Default is None.
                sleep_time (int): The number of seconds to sleep after execution. Default is 10.
                cancel_toggle (bool): If True, cancel the toggle operation. Default is False.
                failure_expected (bool): If True, expect that the password will not be accepted. Default is False.

            Returns:
                tuple: A tuple containing a boolean indicating success or failure and a message describing the result.
        """           
        if service=="ZIA":
            TAB = self.elements.INTERNET_ACCESS
            TOGGLE_ON = self.elements.TURN_ON_TAB
            TOGGLE_OFF = self.elements.TURN_OFF_TAB
            PASSWORD_BOX = self.elements.DISABLE_SERVICE_PASSWORD_FIELD
            DISABLE_REASON_BOX_WITH_PASSWORD = self.elements.DISABLE_REASON_FIELD
            DISABLE_REASON_BOX = self.elements.DISABLE_REASON_FIELD
            OK = self.elements.OK
            OK_BUTTON = self.elements.OK_BUTTON
            CANCEL = self.elements.CANCEL
            CANCEL_BUTTON = self.elements.CANCEL_BUTTON
        elif service=="ZDP":
            # TAB = self.elements.DATA_PREVENTION_TAB
            # TOGGLE_ON = self.elements.DATA_PREVENTION_POWER_BUTTON_ON
            # TOGGLE_OFF = self.elements.DATA_PREVENTION_POWER_BUTTON_OFF
            # PASSWORD_BOX = self.elements.DATA_PREVENTION_PASSWORD_BOX
            # DISABLE_REASON_BOX_WITH_PASSWORD = self.elements.PASSWORD_DISABLE_REASON_BOX
            # DISABLE_REASON_BOX = self.elements.DISABLE_REASON_BOX
            # PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
            # PASSWORD_ACCEPT_BUTTON = self.elements.DATA_PREVENTION_PASSWORD_ACCEPT_BUTTON
            # CONFIRM_ACCEPT_BUTTON = self.elements.CONFIRM_ACCEPT_BUTTON
            # FAILED_CONFIRM_BUTTON = self.elements.DATA_PREVENTION_PASSWORD_FAILED_OK_BUTTON
            # CANCEL_BUTTON = self.elements.DATA_PREVENTION_PASSWORD_CANCEL_BUTTON
            pass
        elif service == "ZPA":
            TAB = self.elements.PRIVATE_ACCESS
            TOGGLE_ON = self.elements.TURN_ON_TAB
            TOGGLE_OFF = self.elements.TURN_OFF_TAB
            PASSWORD_BOX = self.elements.DISABLE_SERVICE_PASSWORD_FIELD
            DISABLE_REASON_BOX_WITH_PASSWORD = self.elements.DISABLE_REASON_FIELD
            DISABLE_REASON_BOX = self.elements.DISABLE_REASON_FIELD
            OK = self.elements.OK
            OK_BUTTON = self.elements.OK_BUTTON
            CANCEL = self.elements.CANCEL
            CANCEL_BUTTON = self.elements.CANCEL_BUTTON
        elif service == "ZDX":
            # TAB = self.elements.DIGITAL_EXPERIENCE_TAB
            # TOGGLE_ON = self.elements.DIGITAL_POWER_BUTTON_ON
            # TOGGLE_OFF = self.elements.DIGITAL_POWER_BUTTON_OFF
            # PASSWORD_BOX = self.elements.DIGITAL_EXPERIENCE_PASSWORD_BOX
            # DISABLE_REASON_BOX_WITH_PASSWORD = self.elements.PASSWORD_DISABLE_REASON_BOX
            # DISABLE_REASON_BOX = self.elements.DISABLE_REASON_BOX
            # PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
            # PASSWORD_ACCEPT_BUTTON = self.elements.DIGITAL_EXPERIENCE_PASSWORD_ACCEPT_BUTTON
            # CONFIRM_ACCEPT_BUTTON = self.elements.CONFIRM_ACCEPT_BUTTON
            # FAILED_CONFIRM_BUTTON = self.elements.DIGITAL_EXPERIENCE_PASSWORD_CANCEL_BUTTON
            # CANCEL_BUTTON = self.elements.DIGITAL_EXPERIENCE_PASSWORD_CANCEL_BUTTON
            pass
        
        try:
            res = self.bring_zcc_to_focus()
            if not res[0]:
                return res
            if not self.ui.check_if_exists(TAB, dynamic_wait_search_element=5): # This case is to handle on iPad as UI is different on it.
                self.ui.driver.swipe(275, 121, 551, 121)
            self.ui.click(TAB, sleep_time=1*self.sleep_factor,AEFC=f"Unable to open {service} tab")
            self.ui.driver.swipe(190,275,190,481)
            time.sleep(1)
            if action:
                self.ui.click(TOGGLE_ON ,sleep_time=1*self.sleep_factor, AEFC=f"Unable to turn on {service}")
            else:
                self.ui.click(TOGGLE_OFF, sleep_time=1*self.sleep_factor, AEFC=f"Unable to turn off {service}")
        
            if (password and disable_reason):
                self.ui.type(DISABLE_REASON_BOX, text=str(disable_reason),sleep_time=1 * self.sleep_factor, AEFC="Unable to enter disable reason")
                self.ui.type(PASSWORD_BOX, text=password,sleep_time=1 * self.sleep_factor, AEFC="Unable to enter password")
                # password ok button
                if cancel_toggle:
                    self.logger.info("Aborting Toggle")
                    self.ui.click(CANCEL,sleep_time=1*self.sleep_factor,AEFC=f"Unable to cancel {service} disable")
                else:
                    self.ui.click(OK,sleep_time=1*self.sleep_factor,AEFC=f"Unable to confirm {service} disable")
            else:
                if disable_reason:
                    self.ui.type(DISABLE_REASON_BOX, text=str(disable_reason),sleep_time=1*self.sleep_factor, AEFC="Unable to enter disable reason")
                    if cancel_toggle:
                        self.logger.info("Aborting Toggle")
                        self.ui.click(CANCEL,sleep_time=1*self.sleep_factor,AEFC=f"Unable to cancel {service} disable")
                    else:
                        self.ui.click(OK,sleep_time=1*self.sleep_factor,AEFC=f"Unable to confirm {service} disable")
                elif password:
                    self.ui.type(PASSWORD_BOX, text=password,sleep_time=1 * self.sleep_factor, AEFC="Unable to enter password")
                    # password ok button
                    if cancel_toggle:
                        self.logger.info("Aborting Toggle")
                        self.ui.click(CANCEL_BUTTON,sleep_time=1*self.sleep_factor,AEFC=f"Unable to cancel {service} disable")
                    else:
                        self.ui.click(OK_BUTTON,sleep_time=1*self.sleep_factor,AEFC=f"Unable to confirm {service} disable")
                else:
                    if cancel_toggle:
                        self.logger.info("Aborting Toggle")
                        self.ui.click(CANCEL_BUTTON,sleep_time=1*self.sleep_factor,AEFC=f"Unable to cancel {service} disable")
                    else:
                        if not action:
                            self.ui.click(OK_BUTTON, sleep_time=1*self.sleep_factor,AEFC=f"Unable to confirm {service} disable")
            if failure_expected:
                self.logger.info("***** Failure expected ******")
                if action:
                    if not self.ui.check_if_exists(path=self.elements.TURN_OFF_STATUS):
                        raise Exception(f"Element not visible :: {self.elements.TURN_OFF_STATUS} \n Error :: Service status is not OFF") 
                else:
                    if self.ui.check_if_exists(CANCEL, dynamic_wait_search_element=5):
                        self.ui.click(CANCEL, sleep_time=2,AEFC=f"Unable to close {service} disable reason box")
                    if not self.ui.check_if_exists(path=self.elements.TURN_ON_STATUS, dynamic_wait_search_element=20):
                        raise Exception(f"Element not visible :: {self.elements.TURN_ON_STATUS} \n Error :: Service status is not ON when failure is expected")
                        

            else:
                assert self.ui.check_if_exists(TOGGLE_OFF if action else TOGGLE_ON, boolean=True, AEFC=f"{service} toggle not accepted properly", dynamic_wait_search_element=60)

            # assert not self.ui.check_if_exists(CANCEL_BUTTON,boolean=True,AESC=f"{service} toggle not accpeted properly")

        except Exception as e:
            self.logger.error(f"Unable to Toggle Service: {service} {action} :: {e}")
            return (False,f"Error :: Unable to Toggle Service: {service} {action} :: {e}\n",None)
        return (True,f"Success :: Toggle Service: {service} {action} Successfully ",None)

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
        
    def check_and_handle_proxy(self, 
                            element, 
                            info_message
                            )->tuple[bool,str,None]:
        
        """
        Check if the given element exists and handle the click operation accordingly.
        """
        try:
            if self.ui.check_if_exists(element,boolean=True):
                self.ui.click(element, do_click=False)
                self.logger.info(info_message)
                return (True, "Success:: Element found and clicked",None)
            else:
                opposite_element = self.elements.PROXY_NOT_VIA_ZSCALER if element == self.elements.PROXY_VIA_ZSCALER else self.elements.PROXY_VIA_ZSCALER
                if self.ui.check_if_exists(opposite_element,boolean=True):
                    raise Exception("Failure")
                return (False, "Failure:: Element not found",None)
        except Exception as e:
            return (False, str(e),None)

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    @calculate_func_execute_time
    @allure.step("Clear Notifications")
    def clear_notifications(self, 
                            cancel=False    #True if cancel clear notifications else False
                            ):
        """
        Checks if notifications are found then delete them
        """
        try:
            self.ui.click(self.elements.NOTIFICATION, sleep_time=0, AEFC="Unable to click Notification button on ZCC")
            if self.ui.check_if_exists(path=self.elements.NO_NOTIFICATION_FOUND, dynamic_wait_search_element=3):
                if self.ui.check_if_exists(path=self.elements.BIN, dynamic_wait_search_element=3):
                    assert False, "No notification found but Bin is still present"
                self.logger.info("****** No Notification or Bin icon found ")
            else:
                self.ui.click(path=self.elements.BIN)
                if cancel:
                    self.ui.click(self.elements.CANCEL_BUTTON)
                else:
                    self.ui.click(self.elements.OK_BUTTON)
            
            if self.ui.check_if_exists(path=self.elements.NO_NOTIFICATION_FOUND, dynamic_wait_search_element=3):
                self.logger.info('****** Notifications cleared Successfully')
            return True, f"Success:: Cleared Notifications", None
        except Exception as e:
            return False, f"Unable to clear notification :: Error :: {e}", None 
    
    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    @calculate_func_execute_time
    @allure.step("Get ZPA tab data")
    def get_zpa_tab_data(self,
                     statistics:bool = False    #True if statistics data is expected in return
                     ):
        """
        This function returns zpa tab data
        """
        try:
            res=self.bring_zcc_to_focus()
            if not res[0]:
                return res
            res = self.validate_tunnel_status(zpa_only_company=True, zpa_on=True)
            if not res[0]:
                return res
            if statistics:
                self.ui.driver.swipe(187,545,183,350)
                time.sleep(1)
                sent = self.ui.click(self.elements.DATA_SENT_VALUE, return_text=True, do_click=False)
                received = self.ui.click(self.elements.DATA_RECEIVED_VALUE, return_text=True)
                return True, f"Success:: Fetched Statistics ::{(sent,received)}", (sent.split(" ")[0],received.split(" ")[0])

        except Exception as e:
            return False, f"Unable to fetch ZPA data from UI :: Error :: {e}", None 


    # -----------------------------------------------------------------------------------------------------------------------------------------------------

    def validate_traffic_going_through_tunnel(self,
                                              is_zia_enable=False, 
                                              is_zia_disable=False
                                              )->tuple[bool,str,None]:
        
        """
        Validate if traffic is going through tunnel based on Zscaler status.
        :param is_zia_enable: If True, checks if traffic goes through Zscaler.
        :type is_zia_enable: bool
        :param is_zia_disable: If True, checks if traffic doesn't go through Zscaler.
        :type is_zia_disable: bool
        """
        domain = "ip.zscaler.com"

        #for Safari
        #self.ui.driver.activate_app("com.apple.mobilesafari")
        #self.explicit_wait(self.elements.SAFARI_SEARCH_BAR)
        #self.ui.click(self.elements.SAFARI_SEARCH_BAR)
        #self.explicit_wait(self.elements.SAFARI_SEARCH_BAR_TEXT)
        #self.ui.type(self.elements.SAFARI_SEARCH_BAR_TEXT,text=domain, sleep_time=1, AEFC="password button not found")
            
        #time.sleep(40)
        #self.explicit_wait(self.elements.GO)
        #self.ui.click(By.XPATH,self.elements.GO)
        #time.sleep(20)
        #for chrome

        # Activate Chrome app
        try:
            self.ui.driver.activate_app("com.google.chrome.ios")
            self.ui.click(self.elements.GOOGLE_SEARCH)
            self.ui.type(self.elements.CHROME_SEARCH_BAR, text=domain, sleep_time=2, AEFC="password button not found")
            self.ui.driver.find_element(MobileBy.ACCESSIBILITY_ID, "Go").click()
        except:
            return False,"Chrome not activated",None
        if is_zia_disable:
                res=self.check_and_handle_proxy(self.elements.PROXY_NOT_VIA_ZSCALER, "Traffic is not going through cloud")
                if not res[0]:
                    return res
                return(True,"Success::Traffic is not going through tunnel",None)
        if is_zia_enable:
            try:
                res=self.check_and_handle_proxy(self.elements.PROXY_VIA_ZSCALER, "Failure")
                if not res[0]:
                    return res
                self.ui.driver.terminate_app("com.google.chrome.ios")
                res=self.bring_zcc_to_focus()
                if not res[0]:
                    return res
                return(True,"Success::Traffic is going through tunnel",None)
            except Exception as e:
                return (False, str(e),None)

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    
    @calculate_func_execute_time
    @allure.step("Restart Service")
    def restart_service(self,
                        sleep_time: int =20,                    # Number of seconds to sleep after execution
                        cancel: bool = False,                   # Cancel Restart Service                   
                        check_notifications: bool = False,      # True if need to check notifications after restarting service else False
                        )->tuple[bool,str,None]:
            """
            Restart a service. This function restarts a service. It is used when a service needs to berestarted.
            Args:
            sleep_time (int): Number of seconds to sleep after execution. Default is 60.
            Returns:
            None
            """
            res=self.bring_zcc_to_focus()
            if not res[0]:
                return res
            if check_notifications:
                res = self.clear_notifications()
                if not res[0]:
                    return res 
            if sleep_time <= 0:
                return False,f"Expected sleep_time to be a non-negative integer, but got {sleep_time}",None
            
            try:
                if not self.ui.check_if_exists(self.elements.MORE, dynamic_wait_search_element=5): # This case is to handle on iPad as UI is different on it.
                    self.ui.driver.swipe(551, 121, 275, 121)
                self.ui.click(self.elements.MORE, sleep_time=0, AEFC="Unable to click More button on ZCC")
                self.ui.click(self.elements.RESTART_SERVICE, sleep_time=3, AEFC="Unable to click Restart Service button on ZCC")  
                if cancel:
                    self.ui.click(self.elements.CANCEL_BUTTON, sleep_time=0, AEFC="Unable to Cancel ZCC Restart")
                else:
                    self.ui.click(self.elements.CONTINUE, sleep_time=sleep_time, AEFC="Unable to confirm ZCC Restart")
                self.ui.click(self.elements.INTERNET_ACCESS, sleep_time=0, AEFC="Unable to click on Internet Security")
                self.ui.click(self.elements.TURN_ON_STATUS, sleep_time=0, AEFC="Unable to Find ON", do_click=False)
                if check_notifications:
                    self.logger.info("Checking Notifications after restart service")
                    self.ui.click(self.elements.NOTIFICATION, sleep_time=0, AEFC="Unable to click Notification button on ZCC")
                    if self.ui.check_if_exists(self.elements.NO_NOTIFICATION_FOUND, dynamic_wait_search_element=2):
                        self.logger.error("No notification found after restart service")
                        raise Exception
                    self.logger.info("Notifications found after restart service")
                return (True, "Success :: Restarted Service!",None)
            except Exception as e:
                self.logger.error(f"Restart Service Failed ; {e}\n")
                return False,"Restart Service Failed",None
                
    # -----------------------------------------------------------------------------------------------------------------------------------------------------

    @calculate_func_execute_time
    @allure.step("Update Policy")
    def update_policy(self, 
                      sleep_time=20):
        """
            Update policy method for the application.

            args:
            sleep_time --> time to sleep after execution

            This method is responsible for updating the policy of the application. It
            performs a series of actions like getting the current ZCC version, clicking
            on the More button and the Update Policy button, and validating the update.

            Returns:
                tuple: A tuple containing a boolean status and a string message.

        """
        try:
            res=self.bring_zcc_to_focus()
            if not res[0]:
                return res
            try:
                if not self.ui.check_if_exists(self.elements.MORE, dynamic_wait_search_element=5): # This case is to handle on iPad as UI is different on it.
                    self.ui.driver.swipe(551, 121, 275, 121)
                self.ui.click(self.elements.MORE, sleep_time=1*self.sleep_factor, AEFC="Unable to click More button on ZCC")
                self.ui.driver.swipe(187,545,183,350)
                self.ui.click(self.elements.UPDATE_POLICY, sleep_time=1, AEFC="Unable to click Update Policy button")
                try:
                    self.ui.explicit_wait(xpath=self.elements.UPDATE_POLICY_SUCCESS, sleep_time=60, poll_frequency=0.1)
                    self.logger.info("***** Update policy Success message appeared *****")
                except:
                    self.logger.warn("##### Update policy success message didn't appear, check if update policy is working as expected #####")
                self.logger.info(f"Sleeping for {sleep_time} seconds after updating policy")
                time.sleep(sleep_time)
            except Exception as e: 
                self.logger.error(f"Update Policy Failed :: {e}")
                raise Exception(f"Error :: Update Policy Failed :: {e}")
        except Exception as e:
            return (False,f"Error ::  {e}",None)
        return (True,f"Success :: Updated Policy Successfully ",None)
    
    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    
    @calculate_func_execute_time
    @allure.step("Report An Issue")
    def report_an_issue(self,
                        sleep_time: int =10,             # number of seconds to sleep after execution
                        verify_in_logs:bool = False,     # Veriy in logs or not
                        airdrop_to:str = None            # airdrop logs to device (required when passing verify_in_logs as True)
                        ):  
        
        """
            This function reports an issue.

            Args:
                sleep_time (int): Number of seconds to sleep after execution. Default is 120.
                verify_in_logs (bool) : Veriy RAI in logs or not. Default is False.
                airdrop_to (str) : Pass device hostname to airdrop logs (required when passing verify_in_logs as True)
            Returns:
                tuple: A tuple containing a boolean status and a string message.
        """
        try:
            try:
                zcc_in_focus = self.bring_zcc_to_focus()
                if not zcc_in_focus[0]:
                    raise Exception(zcc_in_focus[2])
                self.ui.click(self.elements.MORE, sleep_time=0, AEFC="Unable to click More button on ZCC")
                self.ui.click(self.elements.REPORT_AN_ISSUE, sleep_time=1 * self.sleep_factor,AEFC="Unable to select Report an Issue button on ZCC")
                self.ui.type(self.elements.NAME, text="Automation User",sleep_time=1 * self.sleep_factor, AEFC="Unable to enter name in Report an Issue")
                self.ui.click(self.elements.COMMENTS_BOX, sleep_time=1 * self.sleep_factor,AEFC="Unable to click on Comment box")
                self.ui.click(self.elements.GO, sleep_time=1 * self.sleep_factor,AEFC="Unable to click on GO")
                start_time_for_log_search = datetime.datetime.now()
                self.ui.click(self.elements.SEND, sleep_time=1,AEFC="Unable to send report from Report an Issue on ZCC")
                self.ui.explicit_wait(xpath=self.elements.RAI_SUCCESS,poll_frequency=0.2)
                time.sleep(sleep_time)
                self.logger.info("Success :: Report An Issue!")
            except Exception as e:
                self.logger.error(f"Report an Issue Failed :: {e}")
                raise Exception(f"Error :: Report an Issue Failed :: {e}")
            
            if verify_in_logs:
                result=self.export_logs(airdrop=True, airdrop_to=airdrop_to)
                if not result[0]:
                    return result
                is_report_issue_validated = False
                logs_to_be_searched =  "Auth::Lib::reportIssueV3: Success:"
                try:
                    self.log_ops.search_log_file(file="ZSATray",words_to_find_in_line=[logs_to_be_searched],start_timestamp=start_time_for_log_search,search_mode=None,wait_time=sleep_time)
                    is_report_issue_validated=True
                except Exception as e:
                    is_report_issue_validated=False

                if is_report_issue_validated:
                    error_log = "Auth::Lib::reportIssueV3: Exception:"
                    try:
                        self.log_ops.search_log_file(file="ZSATray",words_to_find_in_line=[error_log],start_timestamp=start_time_for_log_search,search_mode=None, wait_time=5)
                    except Exception as e:
                        error = f"Auth::Lib::reportIssueV3: Exception :: {self.log_ops.recent_log_line}"
                        self.logger.info(f"Report an issue :: {error_log} log not found, all good")
                        self.logger.info(f"Report an Issue log line found :: {self.log_ops.recent_log_line}")
                    else:
                        error = f"Auth::Lib::reportIssueV3: Exception :: {self.log_ops.recent_log_line}"
                        self.logger.error(f"Report an issue Error :: {error} log found, report an issue failed")
                        raise Exception(f"reportIssue Error log {error_log} found, report an issue failed")
                    self.logger.info("Success :: Report An Issue!")
                else:
                    raise Exception(f"Report Issue not validated")
        except Exception as e:
            return (False,f"Error ::  {e}",None)
        return (True,f"Success :: Reported an Issue  Successfully ",None)
    
    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    
    @calculate_func_execute_time
    @allure.step("Clear Logs")
    def clear_logs(self):
        """
            This function clears the logs in the application.

            :return: A tuple containing a boolean status and a string message.
            :rtype: tuple
        """
        try:
            res=self.bring_zcc_to_focus()
            if not res[0]:
                return res
            self.ui.click(self.elements.MORE)
            self.ui.click(self.elements.CLEAR_LOGS)
            self.ui.click(self.elements.CONTINUE)
            if not self.ui.check_if_exists(self.elements.ALERT_CLEAR_LOGS, dynamic_wait_search_element=20):
                self.logger.error("No alert present after cicking on Clear Logs")
                raise Exception
            self.ui.click(self.elements.OK_BUTTON)
        except Exception as e:
            self.logger.error(f"CLEAR LOGS FAILED {e}".format(e))
            return (False,f"Error :: CLEAR LOGS FAILED :: {e}",None)
        return (True, "Cleared Logs", None) 
    
    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    
    #helper function to export logs
    def delete_zcc_logs_from_mac(self):
        try:
            if os.path.exists(f"{self.user_directory}/Library/Mobile Documents/com~apple~CloudDocs/zcc_logs"):
                shutil.rmtree(f"{self.user_directory}/Library/Mobile Documents/com~apple~CloudDocs/zcc_logs")
            self.logger.info("Deleted zcc_logs folder from icloud drive on mac")
        except Exception as e:
            self.logger.error(f"Unable to delete zcc_logs folder from icloud drive on mac - Error :: {e}")
            print(e)

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    
    #helper function to export logs and pull into current working directory on mac
    def download_logs(self,
                      from_icloud_drive:bool =False,
                      from_downloads:bool = False):
        try:
            status = False
            currentdir=os.getcwd()
            self.user_directory = os.path.expanduser("~")
            parentdir = os.path.join(currentdir, "OS_Ios")
            working_directory=os.path.join(parentdir,"Temp_Logs")
            
            if os.path.isdir(working_directory):

                shutil.rmtree(working_directory)

                self.logger.info ("CREATING NEW FOLDER")
                os.mkdir(working_directory)

            else:
                os.mkdir(working_directory)
                self.logger.info("Temp Logs is created")

            time.sleep(2)

            if from_icloud_drive:
                icloud_logs_directory = str(self.user_directory)+'/Library/Mobile Documents/com~apple~CloudDocs/zcc_logs'
                for i in range(3):
                    if os.listdir(icloud_logs_directory) == []:
                        self.logger.info(f"*** Trial : {i+1} ***")
                        self.logger.warn("Log Folder in icloud drive is empty")
                        self.logger.info("Opening icloud drive directory to force sync")
                        os.system("open ~/Library/Mobile\ Documents/com~apple~CloudDocs")
                        os.system(command="killall cloudd")
                        time.sleep(20)
                        os.system('osascript -e \'tell app "Finder" to close windows\'') #give permission first time

                for file in os.listdir(icloud_logs_directory):
                        if file.startswith("Zscaler-") and file.endswith('.zip'):
                            zip_file_path = os.path.join(icloud_logs_directory, file)
                            file_to_extract = file
                            break
            
            if from_downloads:
                zip_file_path=subprocess.Popen(f'find {self.user_directory}/Downloads -type f -name "Zscaler*.zip" -exec ls -t1 {{}} \\+ | head -1', shell=True, stdout=subprocess.PIPE).stdout.read().decode('utf-8').strip()
                print(f"***** File location - {zip_file_path}")
                
                file_to_extract = zip_file_path.split('/')[-1]
                print(f"***** Latest log zip file - {file_to_extract}")

            shutil.copy(zip_file_path, working_directory)
            time.sleep(2)
            if os.path.exists(working_directory+"/"+f"{file_to_extract}"):
                self.logger.info("Copying of zip file is successful")
                status = True
                msg = "Copying of zip file is successful"

            else:
                self.logger.info("Copying of zip file is not successful")
                msg = "Copying of zip file is not successful"

            file_path = os.path.join(working_directory, file_to_extract)

            with zipfile.ZipFile(file_path, 'r') as zip_ref:
                zip_ref.extractall(working_directory)
            os.remove(file_path)
            if from_downloads:
                os.remove(zip_file_path)
            else:
                self.delete_zcc_logs_from_mac()
        except Exception as e:
            return False,f"Error while exporting logs:: {e}", None
        return status, msg
    
    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    
    @calculate_func_execute_time
    @allure.step("Export Logs")
    def export_logs(self,
                    cancel_export:bool =False,          #True if cancel export logs else False
                    sleep_time:int = 30,                #function to sleep after execution
                    save_to_icloud_drive:bool = False,  #True if save logs to icloud drive else False 
                    airdrop:bool = False,               #True if export logs by airdrop else False 
                    airdrop_to:str = None               #hostname of the device to airdrop logs
                    ):
        """
            Defines whether to do export logs from zcc tray before or after login.

            :cancel_export: defines whether to cancel export logs.
            :sleep_time: time to sleep after exporting logs
            :save_to_icloud_drive: True if export logs to icloud drive else save it under "On My iPhone' location 
            :return: A tuple containing a boolean status and a string message.
            :rtype: tuple
        """
        res=self.bring_zcc_to_focus()
        if not res[0]:
            return res
        airdrop_to=subprocess.Popen('scutil --get ComputerName', shell=True, stdout=subprocess.PIPE).stdout.read().decode('utf-8').strip()
        print(f"MAC MACHINE NAME - {airdrop_to}")
        try:    
            if save_to_icloud_drive:
                if os.path.exists(f"{self.user_directory}/Library/Mobile Documents/com~apple~CloudDocs/zcc_logs"):
                    shutil.rmtree(f"{self.user_directory}/Library/Mobile Documents/com~apple~CloudDocs/zcc_logs")
                os.mkdir(f"{self.user_directory}/Library/Mobile Documents/com~apple~CloudDocs/zcc_logs")

            if self.ui.check_if_exists(self.elements.LOGIN_BUTTON, dynamic_wait_search_element=2):
                self.ui.click(self.elements.HAMBURGER, AEFC="Unable to click hamburger icon on ZCC")
                self.ui.click(path=self.elements.EXPORT_LOGS, AEFC="Unable to click Export logs on ZCC")
                if cancel_export:
                    self.ui.click(path=self.elements.CANCEL_BUTTON, AEFC="Unable to click Cancel button on ZCC")
                    self.ui.click(path=self.elements.CLOSE, AEFC="Unable to click Close button on ZCC")
                    return True,f"Cancelled Export logs", None 
            else:
                self.ui.click(self.elements.MORE, sleep_time=0, AEFC="Unable to click More button on ZCC")
                self.ui.click(path=self.elements.EXPORT_LOGS, AEFC="Unable to click Export logs on ZCC")
                if cancel_export:
                    self.ui.click(path=self.elements.CANCEL_BUTTON, AEFC="Unable to click Cancel button on ZCC")
                    return True,f"Cancelled Export logs", None 
            self.ui.click(path=self.elements.CONTINUE, AEFC="Unable to click Continue button on ZCC")
            # self.ui.driver.swipe(180,443,190,120)
            time.sleep(1)
            if airdrop:

                try:
                    self.ui.click(path=f'//XCUIElementTypeStaticText[@label="{airdrop_to}"]', dynamic_wait_search_element=5)
                    if self.ui.check_if_exists(path='//XCUIElementTypeStaticText[@label="Sent"]'):
                        self.logger.info(f"Sent copy of logs via airdrop to {airdrop_to}")  
                    else:
                        self.logger.error(f"error :: {e} -- Failed to airdrop to known device - {airdrop_to}... Trying again")
                except:
                    self.ui.click(path='//XCUIElementTypeCell[@label="AirDrop"]/XCUIElementTypeOther/XCUIElementTypeImage') #non-reusable
                    self.ui.click(path=f'//XCUIElementTypeButton[@label="{airdrop_to}"]')
                    if self.ui.check_if_exists(path='//XCUIElementTypeStaticText[@label="Sent"]'):
                        self.logger.info(f"Sent copy of logs via airdrop to {airdrop_to}")  
                    else:
                        self.ui.click(path='//XCUIElementTypeButton[@label="Done"]')
                        self.ui.click(path='//XCUIElementTypeButton[@label="Close"]')
                        self.logger.error(f"error :: {e} -- Failed to airdrop")
                        raise Exception(f"error :: {e} -- Failed to airdrop")
                    self.ui.click(path='//XCUIElementTypeButton[@label="Done"]')
                time.sleep(5)
                self.ui.click(path='//XCUIElementTypeButton[@label="Close"]')
                if not self.ui.check_if_exists(path=self.elements.MORE, dynamic_wait_search_element=5):
                    self.restart_zcc(5)
                res = self.download_logs(from_downloads=True)
                self.logger.info("Sleeping for 10 seconds after cloning logs to working directory")
                time.sleep(10)
                if not res[0]:
                    self.logger.error("Unable to download logs to local environment")
                    return res 
                self.logger.info (f"***** Shared logs via Airdrop *****")
            elif save_to_icloud_drive:
                self.ui.click(path=self.elements.SAVE_TO_FILES, AEFC="Unable to click Save to files")
                for i in range(10):
                    self.ui.click(path='//XCUIElementTypeNavigationBar[@name="FullDocumentManagerViewControllerNavigationBar"]/XCUIElementTypeButton[1]') #non reusable
                    time.sleep(1)
                    if self.ui.click('//XCUIElementTypeNavigationBar[@name="FullDocumentManagerViewControllerNavigationBar"]/XCUIElementTypeStaticText', do_click=False, return_text=True) == "Browse": #non reusable
                        break
                self.ui.click('//XCUIElementTypeCell[@name="DOC.sidebar.item.iCloud Drive"]') #non reusable
                try:
                    self.ui.click('//XCUIElementTypeCell[@name="zcc_logs, Folder"]', dynamic_wait_search_element=10) #non reusable
                except:
                    self.logger.warn("zcc_log folder didn't appear")
                    self.logger.info("Opening icloud drive directory to force sync")
                    os.system("open ~/Library/Mobile\ Documents/com~apple~CloudDocs")
                    time.sleep(10)
                    os.system('osascript -e \'tell app "Finder" to close windows\'') #give permission first time
                    self.ui.click('//XCUIElementTypeCell[@name="zcc_logs, Folder"]') #non reusable
                self.ui.click(self.elements.SAVE_BUTTON, AEFC="Unable to click Save button")
                self.logger.info (f"Saved logs to zcc_logs folder in icloud drive")
                self.logger.info("Waiting for icloud drive to sync")
                self.logger.info(f"Sleeping for :: {sleep_time} seconds after saving logs on device")
                time.sleep(sleep_time)
                res = self.download_logs(from_icloud_drive=True)
                if not res[0]:
                    self.logger.error("Unable to download logs to local environment")
                    return res 
                self.logger.info (f"***** Saved logs in 'iCloud Drive' location *****")
            else:
                self.ui.click(path=self.elements.SAVE_TO_FILES, AEFC="Unable to click Save to files")
                for i in range(10):
                    self.ui.click(path='//XCUIElementTypeNavigationBar[@name="FullDocumentManagerViewControllerNavigationBar"]/XCUIElementTypeButton[1]') #non reusable
                    time.sleep(1)
                    if self.ui.click('//XCUIElementTypeNavigationBar[@name="FullDocumentManagerViewControllerNavigationBar"]/XCUIElementTypeStaticText', do_click=False, return_text=True) == "Browse": #non reusable
                        break
                self.ui.click('//XCUIElementTypeCell[@name="DOC.sidebar.item.On My iPhone"]') #non reusable
                self.ui.click(self.elements.SAVE_BUTTON, AEFC="Unable to click Save button")
                self.logger.info (f"***** Saved logs in 'On My iPhone' location *****")
                time.sleep(1)
                
            self.logger.info("Exported logs successfully")
            return True,f"Exported logs", None                      
        except Exception as e:
            self.logger.error("Export logs failed")
            return False,f"Error while exporting logs:: {e}", None
    
    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    
    @calculate_func_execute_time
    @allure.step("Verify ZCC UI fields")
    def verify_zcc_ui_fields(self, 
                             zia_tab:bool = False,
                             zia_state:str = "on",
                             zpa_tab:bool = False,
                             zpa_state:str = "on",
                             notifications_tab:bool = False,
                             more_tab:bool = False,
                             zia_user:str = None,
                             zpa_user:str = None,
                             disable_options_from_MA:bool = False,
                             app_policy_name:str = None):
        """
        This function validates zcc ui fiedlds on diffetent tabs after login
        """
        self.logger.info("########## Verifying ZCC UI Fields ##########")
        time.sleep(10)
        try:
            error_list = []
            if zia_tab:
                self.ui.click(self.elements.INTERNET_ACCESS, sleep_time=0, AEFC="Unable to click Internet Security button on ZCC")
                if zia_state.lower()=='on':
                    self.logger.info("########## Verifying ZIA Tab Fields ##########")
                    self.ui.driver.swipe(190,275,190,481)

                    self.logger.info("########## Checking Username ##########")
                    if not self.ui.check_if_exists(path=self.elements.USERNAME_LABEL):
                        error_list.append("Username label is not present")
                    
                    if not self.ui.click(self.elements.USERNAME_VALUE, return_text=True, do_click=False) == zia_user:
                        error_list.append(f"Username value is not correct - {self.elements.USERNAME_VALUE}")

                    self.logger.info("########## Checking Service Status ##########")
                    if not self.ui.check_if_exists(path=self.elements.SERVICE_STATUS_LABEL):
                        error_list.append("Service Status label is not present")

                    if self.ui.click(self.elements.SERVICE_STATUS_VALUE, return_text=True, do_click=False) != "ON":
                        self.logger.error("********** Service status is not 'ON' ***********")
                        self.logger.error(f"********** ZIA state after login ::  {self.ui.click(self.elements.SERVICE_STATUS_VALUE, return_text=True, do_click=False)}***********")
                        error_list.append("ZIA is not in ON state")
                    
                    self.logger.info("########## Checking Network Status ##########")
                    if not self.ui.check_if_exists(path=self.elements.NETWORK_STATUS_LABEL):
                        error_list.append("Network Status label is not present")
                    
                    if self.ui.click(self.elements.NETWORK_STATUS_VALUE, return_text=True, do_click=False) == None:
                        error_list.append("Network Status value is not present")

                    self.logger.info("########## Checking Server IP ##########")
                    if not self.ui.check_if_exists(path=self.elements.SERVER_IP_LABEL):
                        error_list.append("Server IP label is not present")
                    
                    if self.ui.click(self.elements.SERVER_IP_VALUE, return_text=True, do_click=False) == None or self.ui.click(self.elements.SERVER_IP_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Server IP value is not present")
                    
                    self.logger.info("########## Checking Client IP ##########")
                    if not self.ui.check_if_exists(path=self.elements.CLIENT_IP_LABEL):
                        error_list.append("Client IP label is not present")
                    
                    if self.ui.click(self.elements.CLIENT_IP_VALUE, return_text=True, do_click=False) == None or self.ui.click(self.elements.CLIENT_IP_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Client IP value is not present")

                    self.logger.info("########## Checking Time Connected ##########")
                    if not self.ui.check_if_exists(path=self.elements.TIME_CONNECTED_LABEL):
                        error_list.append("Time Connected label is not present")
                    
                    if self.ui.click(self.elements.TIME_CONNECTED_VALUE, return_text=True, do_click=False) == None or self.ui.click(self.elements.TIME_CONNECTED_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Time Connected value is not present")
                    
                    self.logger.info("########## Checking Tunnel Version ##########")
                    if not self.ui.check_if_exists(path=self.elements.TUNNEL_VERSION_LABEL):
                        error_list.append("Tunnel Version label is not present")
                    
                    if self.ui.click(self.elements.TUNNEL_VERSION_VALUE, return_text=True, do_click=False) == None or self.ui.click(self.elements.TUNNEL_VERSION_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Tunnel Version value is not present")
                    
                    self.ui.driver.swipe(187,545,183,350)

                    self.logger.info("########## Checking Statistics ##########")
                    if not self.ui.check_if_exists(path=self.elements.STATISTICS):
                        error_list.append("Statistics are not present")
                    if not self.ui.check_if_exists(path=self.elements.BYTES_SENT_LABEL):
                        error_list.append("Bytes Sent label is not present")
                    if not self.ui.check_if_exists(path=self.elements.BYTES_RECEIVED_LABEL):
                        error_list.append("Bytes Received label is not present")
                    
                    if self.ui.click(self.elements.BYTES_SENT_VALUE, return_text=True, do_click=False) == None or self.ui.click(self.elements.BYTES_SENT_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Bytes Sent value is not present")
                    
                    if self.ui.click(self.elements.BYTES_RECEIVED_VALUE, return_text=True, do_click=False) == None or self.ui.click(self.elements.BYTES_RECEIVED_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Bytes Received value is present")
                
                if zia_state.lower() =="off":
                    self.logger.info("########## Checking Username ##########")
                    self.ui.driver.swipe(190,275,190,481)
                    if not self.ui.check_if_exists(path=self.elements.USERNAME_LABEL):
                        error_list.append("Username label is not present")
                    
                    if not self.ui.click(self.elements.USERNAME_VALUE, return_text=True, do_click=False) == zia_user:
                        error_list.append(f"Username value is not correct")

                    self.logger.info("########## Checking Service Status ##########")
                    if not self.ui.check_if_exists(path=self.elements.SERVICE_STATUS_LABEL):
                        error_list.append("Service Status label is not present")
                    
                    
                    if self.ui.click(self.elements.SERVICE_STATUS_VALUE, return_text=True, do_click=False) != "OFF":
                        self.logger.error("********** Service status is not 'OFF' ***********")
                        self.logger.error(f"********** ZIA Service status :: {self.ui.click(self.elements.SERVICE_STATUS_VALUE, return_text=True, do_click=False)} ***********")
                        error_list.append("ZIA is not in OFF state")
                    
                    self.logger.info("########## Checking Network Status ##########")
                    if not self.ui.check_if_exists(path=self.elements.NETWORK_STATUS_LABEL):
                        error_list.append("Network Status label is not present")
                    
                    if self.ui.click(self.elements.NETWORK_STATUS_VALUE, return_text=True, do_click=False)==None:
                        error_list.append("Network Status value is not present")
                    
                    self.logger.info("########## Checking Server IP ##########")
                    if not self.ui.check_if_exists(path=self.elements.SERVER_IP_LABEL):
                        error_list.append("Server IP label is not present")
                    
                    if not self.ui.click(self.elements.SERVER_IP_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Server IP value is not '...'")
                    
                    self.logger.info("########## Checking Client IP ##########")
                    if not self.ui.check_if_exists(path=self.elements.CLIENT_IP_LABEL):
                        error_list.append("Client IP label is not present")
                    
                    if not self.ui.click(self.elements.CLIENT_IP_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Client IP value is not '...'")

                    self.logger.info("########## Checking Time Connected ##########")
                    if not self.ui.check_if_exists(path=self.elements.TIME_CONNECTED_LABEL):
                        error_list.append("Time Connected label is not present")
                    
                    if not self.ui.click(self.elements.TIME_CONNECTED_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Time Connected value is not '...'")
                    
                    self.logger.info("########## Checking Tunnel Version ##########")
                    if not self.ui.check_if_exists(path=self.elements.TUNNEL_VERSION_LABEL):
                        error_list.append("Tunnel Version label is not present")
                    
                    if not self.ui.click(self.elements.TUNNEL_VERSION_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Tunnel Version value is not '...'")
                    
                    self.ui.driver.swipe(187,545,183,350)

                    self.logger.info("########## Checking Statistics ##########")
                    if not self.ui.check_if_exists(path=self.elements.STATISTICS):
                        error_list.append("Statistics are not present")
                    if not self.ui.check_if_exists(path=self.elements.BYTES_SENT_LABEL):
                        error_list.append("Bytes Sent label is not present")
                    if not self.ui.check_if_exists(path=self.elements.BYTES_RECEIVED_LABEL):
                        error_list.append("Bytes Received label is not present")
                    
                    if not self.ui.click(self.elements.BYTES_SENT_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Bytes Sent value is not '...'")

                    if not self.ui.click(self.elements.BYTES_RECEIVED_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Bytes Received value is not '...'")
                    
                    self.logger.info("########## Verified ZIA tab UI fields ##########")
            
            if zpa_tab:
                self.ui.click(self.elements.PRIVATE_ACCESS, sleep_time=0, AEFC="Unable to click Internet Security button on ZCC")
                if zpa_state.lower()=="on":
                    self.logger.info("########## Verifying ZPA Tab Fields ##########")                
                    self.logger.info("########## Checking Username ##########")
                    self.ui.driver.swipe(190,275,190,481)
                    if not self.ui.check_if_exists(path=self.elements.USERNAME_LABEL):
                        error_list.append("Username label is not present")
                    
                    username_on_ui = self.ui.click(self.elements.USERNAME_VALUE, return_text=True, do_click=False)
                    if not self.ui.click(self.elements.USERNAME_VALUE, return_text=True, do_click=False) == zpa_user:
                        error_list.append(f"Username value is not correct - {username_on_ui}")

                    self.logger.info("########## Checking Service Status ##########")
                    if not self.ui.check_if_exists(path=self.elements.SERVICE_STATUS_LABEL):
                        error_list.append("Service Status label is not present")

                    if self.ui.click(self.elements.SERVICE_STATUS_VALUE, return_text=True, do_click=False) != "ON":
                        self.logger.error("********** Service status is not 'ON' ***********")
                        self.logger.error(f"********** ZPA state :: {self.ui.click(self.elements.SERVICE_STATUS_VALUE, return_text=True, do_click=False)} ***********")
                        error_list.append("ZPA is not in ON state")
                    
                    self.logger.info("########## Checking Network Status ##########")
                    if not self.ui.check_if_exists(path=self.elements.NETWORK_STATUS_LABEL):
                        error_list.append("Network Status label is not present")
                    
                    if self.ui.click(self.elements.NETWORK_STATUS_VALUE, return_text=True, do_click=False) == None:
                        error_list.append("Network Status value is not present")

                    self.logger.info("########## Checking Authentication Status ##########")
                    if not self.ui.check_if_exists(path=self.elements.AUTHENTICATION_STATUS_LABEL):
                        error_list.append("Authentication Status label is not present")
                    
                    if self.ui.click(self.elements.AUTHENTICATION_STATUS_VALUE, return_text=True, do_click=False) != 'Authenticated':
                        error_list.append("Authentication Status value is not found to be 'Authenticated' just after login")
                    
                    self.logger.info("########## Checking Broker ##########")
                    if not self.ui.check_if_exists(path=self.elements.BROKER_LABEL):
                        error_list.append("Broker Status label is not present")
                    
                    if self.ui.click(self.elements.BROKER_VALUE, return_text=True, do_click=False) == '...':
                        error_list.append("Broker value is not found")

                    self.logger.info("########## Checking Client ##########")
                    if not self.ui.check_if_exists(path=self.elements.CLIENT_LABEL):
                        error_list.append("Client label is not present")
                    
                    if self.ui.click(self.elements.BROKER_VALUE, return_text=True, do_click=False) == '...':
                        error_list.append("Client IP value is not found")
                    
                    self.logger.info("########## Checking Time Connected ##########")
                    if not self.ui.check_if_exists(path=self.elements.TIME_CONNECTED_LABEL):
                        error_list.append("Time Connected label is not present")
                    
                    if self.ui.click(self.elements.TIME_CONNECTED_VALUE, return_text=True, do_click=False) == None or self.ui.click(self.elements.TIME_CONNECTED_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Time Connected value is not present")
                    
                    self.logger.info("########## Checking Tunnel Protocol ##########")
                    if not self.ui.check_if_exists(path=self.elements.TUNNEL_PROTOCOL_LABEL):
                        error_list.append("Tunnel Protocol label is not present")
                    
                    if self.ui.click(self.elements.TUNNEL_PROTOCOL_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Tunnel Protocol value is not present")
                    
                    self.ui.driver.swipe(187,545,183,350)

                    self.logger.info("########## Checking Statistics ##########")
                    if not self.ui.check_if_exists(path=self.elements.STATISTICS):
                        error_list.append("Statistics are not present")
                    if not self.ui.check_if_exists(path=self.elements.DATA_SENT_LABEL):
                        error_list.append("Data Sent label is not present")
                    if not self.ui.check_if_exists(path=self.elements.DATA_RECEIVED_LABEL):
                        error_list.append("Data Received label is not present")
                    
                    if self.ui.click(self.elements.DATA_SENT_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Data Sent value is not present")
                    
                    if self.ui.click(self.elements.DATA_RECEIVED_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Data Received value is present")
                    
                if zpa_state.lower()=="off":
                    self.logger.info("########## Checking Username ##########")
                    self.ui.driver.swipe(190,275,190,481)
                    if not self.ui.check_if_exists(path=self.elements.USERNAME_LABEL):
                        error_list.append("Username label is not present")
                    
                    if not self.ui.click(self.elements.USERNAME_VALUE, return_text=True, do_click=False) == zpa_user:
                        error_list.append("Username value is not correct")

                    self.logger.info("########## Checking Service Status ##########")
                    if not self.ui.check_if_exists(path=self.elements.SERVICE_STATUS_LABEL):
                        error_list.append("Service Status label is not present")
                    
                    if self.ui.click(self.elements.SERVICE_STATUS_VALUE, return_text=True, do_click=False) != "OFF":
                        self.logger.error("********** Service status is not 'OFF' ***********")
                        self.logger.error(f"********** ZIA state after turning off service ::  {self.ui.click(self.elements.SERVICE_STATUS_VALUE, return_text=True, do_click=False)}***********")
                        error_list.append("ZPA is not in OFF state")
                    
                    self.logger.info("########## Checking Network Status ##########")
                    if not self.ui.check_if_exists(path=self.elements.NETWORK_STATUS_LABEL):
                        error_list.append("Network Status label is not present")
                    
                    if self.ui.click(self.elements.NETWORK_STATUS_VALUE, return_text=True, do_click=False)==None:
                        error_list.append("Network Status value is not present")

                    self.logger.info("########## Checking Authentication Status ##########")
                    if not self.ui.check_if_exists(path=self.elements.AUTHENTICATION_STATUS_LABEL):
                        error_list.append("Authentication Status label is not present")
                    
                    if self.ui.click(self.elements.AUTHENTICATION_STATUS_VALUE, return_text=True, do_click=False) != '...':
                        error_list.append("Authentication Status value is not found to be '...' after switching off the service")
                    
                    self.logger.info("########## Checking Broker ##########")
                    if not self.ui.check_if_exists(path=self.elements.BROKER_LABEL):
                        error_list.append("Broker label is not present")
                    
                    if self.ui.click(self.elements.BROKER_VALUE, return_text=True, do_click=False) != '...':
                        error_list.append("Broker value is not found to be '...' after switching off the service")

                    self.logger.info("########## Checking Client ##########")
                    if not self.ui.check_if_exists(path=self.elements.BROKER_LABEL):
                        error_list.append("Client label is not present")
                    
                    if self.ui.click(self.elements.BROKER_VALUE, return_text=True, do_click=False) != '...':
                        error_list.append("Client IP value is not found to be '...' after switching off the service")
                    
                    self.logger.info("########## Checking Time Connected ##########")
                    if not self.ui.check_if_exists(path=self.elements.TIME_CONNECTED_LABEL):
                        error_list.append("Time Connected label is not present")
                    
                    if self.ui.click(self.elements.TIME_CONNECTED_VALUE, return_text=True, do_click=False) != "...":
                        error_list.append("Time Connected value is not found to be '...' after switching off service")
                    
                    self.logger.info("########## Checking Tunnel Protocol ##########")
                    if not self.ui.check_if_exists(path=self.elements.TUNNEL_PROTOCOL_LABEL):
                        error_list.append("Tunnel Protocol label is not present")
                    
                    if self.ui.click(self.elements.TUNNEL_PROTOCOL_VALUE, return_text=True, do_click=False) != "...":
                        error_list.append("Tunnel Protocol value is not found to be '...' after switching off the service")

                    self.ui.driver.swipe(187,545,183,350)

                    self.logger.info("########## Checking Statistics ##########")
                    if not self.ui.check_if_exists(path=self.elements.STATISTICS):
                        error_list.append("Statistics are not present")
                    if not self.ui.check_if_exists(path=self.elements.DATA_SENT_LABEL):
                        error_list.append("Data Sent label is not present")
                    if not self.ui.check_if_exists(path=self.elements.DATA_RECEIVED_LABEL):
                        error_list.append("Data Received label is not present")
                    
                    if not self.ui.click(self.elements.DATA_SENT_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Data Sent value is present")

                    if not self.ui.click(self.elements.DATA_RECEIVED_VALUE, return_text=True, do_click=False) == "...":
                        error_list.append("Data Received value is present")
                
            self.logger.info("########## Verified ZPA Tab Fields ##########")

            if notifications_tab:

                self.ui.click(self.elements.NOTIFICATION, sleep_time=0, AEFC="Unable to click Notification button on ZCC")

                self.logger.info("########## Verifying Notification Tab Fields ##########")

                self.clear_notifications(cancel=True)

                self.clear_notifications(cancel=False)
            if more_tab and disable_options_from_MA == False and app_policy_name:

                if not self.ui.check_if_exists(self.elements.MORE, dynamic_wait_search_element=5): # This case is to handle on iPad as UI is different on it.
                    self.ui.driver.swipe(551, 121, 275, 121)
                self.ui.click(self.elements.MORE, sleep_time=0, AEFC="Unable to click MORE button on ZCC")
                self.logger.info("########## Verifying More Tab Fields ##########")
                self.logger.info("########## Checking ZCC Notification Togggle ##########")
                self.ui.driver.swipe(190,275,190,481)
                if self.ui.check_if_exists(self.elements.ZCC_NOTIFICATION_LABEL):
                    self.ui.click(self.elements.ZCC_NOTIFICATION_TOGGLE, sleep_time=1)
                    if self.ui.click(self.elements.ZCC_NOTIFICATION_STATE, return_text=True, do_click=False) == "ON":
                        if not self.ui.click(self.elements.ZCC_NOTIFICATION_TOGGLE, return_text=True, do_click=False) ==  "TURN OFF":
                            error_list.append("Something wrong with ZCC Notification Toggle State is 'ON' but toggle is not 'TURN OFF'")
                    else:
                        if not self.ui.click(self.elements.ZCC_NOTIFICATION_TOGGLE, return_text=True, do_click=False) ==  "TURN ON":
                            error_list.append("Something wrong with ZCC Notification Toggle State is 'OFF' but toggle is not 'TURN ON'")
                else:
                    error_list.append("ZCC NOTIFICATION LABEL doesn't exist")

                if self.ui.check_if_exists(self.elements.ZPA_NOTIFICATION_LABEL):
                    if self.ui.click(self.elements.ZPA_NOTIFICATION_STATE, return_text=True, do_click=False) == "ON":
                        if not self.ui.click(self.elements.ZPA_NOTIFICATION_TOGGLE, return_text=True, do_click=False) ==  "TURN OFF":
                            error_list.append("Something wrong with ZPA Notification Toggle State is 'ON' but toggle is not 'TURN OFF'")
                    else:
                        if not self.ui.click(self.elements.ZPA_NOTIFICATION_TOGGLE, return_text=True, do_click=False) ==  "TURN ON":
                            error_list.append("Something wrong with ZPA Notification Toggle State is 'OFF' but toggle is not 'TURN ON'")

                else:
                    error_list.append("ZPA Reauth Notification Toggle didn't appear")

                self.logger.info("########## Checking Packet Capture ##########")
                self.ui.click(self.elements.PACKET_CAPTURE)
                if not self.ui.check_if_exists(self.elements.STOP_PACKET_CAPTURE):
                    error_list.append("Can't see stop packet capture option")
                self.ui.click(self.elements.STOP_PACKET_CAPTURE)

                self.logger.info("########## Checking Report An Issue ##########")

                self.ui.click(self.elements.REPORT_AN_ISSUE)
                if not self.ui.check_if_exists(self.elements.SEND):
                    error_list.append("Send option not available")
                self.ui.click(self.elements.CANCEL)

                self.logger.info("########## Checking Restart Service ##########")

                self.ui.click(self.elements.RESTART_SERVICE)
                if not self.ui.check_if_exists(self.elements.CONFIRM_RESTART_SERVICE_ALERT):
                    error_list.append("Restart Service Alert is not present")
                self.ui.click(self.elements.CANCEL_BUTTON)

                self.logger.info("########## Checking Export Logs, Clear Logs and Log Level ##########")
                    
                self.ui.click(self.elements.EXPORT_LOGS)
                if not self.ui.check_if_exists(self.elements.CONFIRM_EXPORT_LOGS_ALERT, dynamic_wait_search_element=5):
                    error_list.append("Export Logs Alert not present")
                self.ui.click(self.elements.CANCEL_BUTTON)

                self.ui.click(self.elements.CLEAR_LOGS)
                if not self.ui.check_if_exists(self.elements.CONTINUE, dynamic_wait_search_element=5):
                    error_list.append("Continue button not present")
                self.ui.click(self.elements.CANCEL_BUTTON)

                self.ui.click(self.elements.LOG_LEVEL)
                if self.ui.check_if_exists(self.elements.SELECT_LOG_MODE,dynamic_wait_search_element=5) and self.ui.check_if_exists(self.elements.ERROR,dynamic_wait_search_element=5) and self.ui.check_if_exists(self.elements.WARN,dynamic_wait_search_element=5) and self.ui.check_if_exists(self.elements.INFO,dynamic_wait_search_element=5) and self.ui.check_if_exists(self.elements.DEBUG,dynamic_wait_search_element=5):
                    self.logger.info("All log levels are present")
                else:
                    self.logger.info("Error :: All log levels are not present")
                    error_list.append("Can't select log level")
                self.ui.click(self.elements.CANCEL_BUTTON)

                self.ui.driver.swipe(187,545,183,350)

                self.logger.info("########## Checking APP Version ##########")
                if self.ui.check_if_exists(self.elements.APP_VERSION,dynamic_wait_search_element=5):
                    # Input version strings
                    version1 = os.environ.get("zcc_version")
                    version2 = self.ui.click(self.elements.APP_VERSION_VALUE, return_text=True).strip()

                    # Parsing the versions
                    # Split the first string by '.' to get individual parts
                    v1_parts = version1.split('.')

                    # For the second string, remove parentheses and split the first part
                    v2_main, v2_build = version2.split(' ')
                    v2_parts = v2_main.split('.') + [v2_build.strip('()')]

                    # Convert both lists into integers for easy comparison
                    v1_parts = list(map(int, v1_parts))
                    v2_parts = list(map(int, v2_parts))
                    self.logger.info(f"Expected ZCC Version - {v1_parts}")
                    self.logger.info(f"ZCC Version found on UI- {v2_parts}")

                    if v1_parts == v2_parts:
                        self.logger.info("The versions are equal.")
                    else:
                        error_list.append(f"App Version is found {self.ui.click(self.elements.APP_VERSION_VALUE, return_text=True)} !=  {os.environ.get("zcc_version")}")
                else:
                    error_list.append(f"App Version is not present")

                self.logger.info("########## Checking APP POLICY ##########")
                if self.ui.check_if_exists(self.elements.APP_POLICY,dynamic_wait_search_element=5):
                    if not self.ui.click(self.elements.APP_POLICY_VALUE, return_text=True) == app_policy_name:
                        error_list.append("Wrong App policy")
                else:
                    error_list.append(f"App Policy is not present")
                
                self.logger.info("########## Checking LICENSE AGREEMENT AND THIRD PARTY SOFTWARE ##########")
                if self.ui.check_if_exists(self.elements.LICENSE_AGREEMENT,dynamic_wait_search_element=5):
                    self.ui.click(path=self.elements.LICENSE_AGREEMENT)
                    if self.ui.check_if_exists(self.elements.LICENSE_AGREEMENT_BUTTON,dynamic_wait_search_element=5) and self.ui.check_if_exists(self.elements.THIRD_PARTY_SOFTWARE,dynamic_wait_search_element=5) and self.ui.check_if_exists(self.elements.CLOSE,dynamic_wait_search_element=5):
                        assert True, "LICENSE AGREEMENT AND THIRD PARTY SOFTWARE page is present"
                    else:
                        error_list.append("LICENSE AGREEMENT AND THIRD PARTY SOFTWARE page is not present")
                    self.ui.click(path=self.elements.CLOSE)
                else:
                    error_list.append(f"LICENSE AGREEMENT AND THIRD PARTY SOFTWARE label is not present")
            
            if more_tab and disable_options_from_MA:
                self.logger.info("########## Checking More tab after disabling controls from MA ##########")
                if not self.ui.check_if_exists(self.elements.MORE, dynamic_wait_search_element=5): # This case is to handle on iPad as UI is different on it.
                    self.ui.driver.swipe(551, 121, 275, 121)
                self.ui.click(self.elements.MORE, sleep_time=0, AEFC="Unable to click MORE button on ZCC")
                if self.ui.check_if_exists(self.elements.ZPA_NOTIFICATION_LABEL, dynamic_wait_search_element=3, expect_failure=True):
                    error_list.append("ZPA Reauth Notification Toggle is still present")
                
                if self.ui.check_if_exists(self.elements.REPORT_AN_ISSUE, dynamic_wait_search_element=3, expect_failure=True):
                    error_list.append("Report An Issue is still present")
                
                if self.ui.check_if_exists(self.elements.EXPORT_LOGS, dynamic_wait_search_element=3, expect_failure=True):
                    error_list.append("Export Logs is still present")
                elif self.ui.check_if_exists(self.elements.CLEAR_LOGS, dynamic_wait_search_element=3, expect_failure=True):
                    error_list.append("Clear Logs is still present")
                else:
                    self.ui.click(self.elements.LOG_LEVEL)
                    if self.ui.check_if_exists(self.elements.SELECT_LOG_MODE, dynamic_wait_search_element=3, expect_failure=True):
                        error_list.append("User is able to select LOG MODE")
                    self.ui.click(self.elements.CANCEL_BUTTON)
            if len(error_list)!=0:
                self.logger.error("Found following errors while validating ZCC ui fields : - \n")
                for i in range(len(error_list)):
                    self.logger.error(f"{i+1}--> {error_list[i]}")
                return (False,"Found errors while verifying ZCC UI fields",None)
            return (True, "ZCC UI Verification Successful",None)
        except Exception as e:
            self.logger.error(f"Errors:\n {error_list}")
            return (False, str(e),None)

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    
    @allure.step("validate network status on zcc")
    def validate_network_status_on_zcc(self,
                                trusted_network: int,               # accepted values 1 for "trusted" and 0 for "off trusted"
                                service: str= "zia",                # service for which netwrok type is to be validated, zia or zpa
                                failure_expected: bool =False    
    ):  
        try:
            res=self.bring_zcc_to_focus()
            if not res[0]:
                return res
            if service == "zia":
                self.ui.click(self.elements.INTERNET_ACCESS)
                if trusted_network:
                    self.ui.explicit_wait(xpath=self.elements.TRUSTED_NETWORK)
                else:
                    self.ui.explicit_wait(xpath=self.elements.OFF_TRUSTED_NETWORK)
            else:
                self.ui.click(self.elements.PRIVATE_ACCESS)
                if trusted_network:
                    self.ui.explicit_wait(xpath=self.elements.TRUSTED_NETWORK)
                else:
                    self.ui.explicit_wait(xpath=self.elements.OFF_TRUSTED_NETWORK)
        except Exception as e:
            if not failure_expected:
                return (False, f"Error while validating network status on ZCC tray :: {e}", None)
        return (True, f"Validated network status on ZCC for Trusted Network == {trusted_network}")


    # --------------------------ZPA UI Actions------------------------------------------------------------------------------------------------------------
    
    @allure.step("validate zpa reauth state")
    def validate_zpa_reauth_state(self):    #this function checks if zcc is in reauth state or not
        """
            This function checks if ZCC is in reauth state or not.

            :return: A tuple containing a boolean value and a string message.
        """        
        zcc_in_focus = self.bring_zcc_to_focus()
        if not zcc_in_focus[0]:
            raise Exception(zcc_in_focus[2])
        self.ui.click(self.elements.PRIVATE_ACCESS, sleep_time=1, AEFC="Unable to open Private Access tab")
        try:
            try:
                self.ui.click(self.elements.Authentication_Required,max_threshold=3,sleep_time=1, AEFC="Unable to select Private Access button on mac ZCC",do_click=False,dynamic_wait_search_element=10)
            except:
                try:
                    self.ui.click(self.elements.REAUTHENTICATE,max_threshold=3,sleep_time=1, AEFC="Unable to select Private Access button on ZCC - tried 2 different element id's",do_click=False,dynamic_wait_search_element=10)
                except Exception as e:
                    self.logger.error(e)
                    raise Exception(e)
            self.logger.info("ZPA in reauth required state")
            return (True,"Success :: ZPA in reauth required state",None)
        except:
            self.logger.error("ZPA Reauth button not found")
            return (False,"Error :: ZPA Reauth button not found",None)

    # -----------------------------------------------------------------------------------------------------------------------------------------------------


    @allure.step("Do ZPA Reauth ")
    def zpa_reauth(self,
                   zpa_username: str ,          # ZPA user for reauth
                   zpa_password: str ,          # ZPA user password for reauth
                   sleep_time: int =10,         # number of seconds to sleep after execution
                   partial_reauth: bool =False, # defines whether do to partial reauth
                   early_auth: bool =False,     # defines whether to do early reauth
                   use_login_hint: bool=False):    
        """
            This function performs a ZPA reauthentication.
            
            Args:
                zpa_username (str): ZPA user for reauth.
                zpa_password (str): ZPA user password for reauth.
                sleep_time (int, optional): Number of seconds to sleep after execution. Defaults to 10.
                partial_reauth (bool, optional): Defines whether to do partial reauth. Defaults to False.
                early_auth (bool, optional): Defines whether to do early reauth. Defaults to False.
            
            Returns:
                tuple: A tuple containing a boolean and a string indicating the success or failure of the operation.
        """        
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(self.elements.PRIVATE_ACCESS, sleep_time=1, AEFC="Unable to open Private Access tab")
            try:
                start_time_for_log_search = datetime.now()
                self.ui.click(self.elements.REAUTHENTICATE if early_auth==False else self.elements.PRIVATE_ACCESS_EARLY_REAUTH_BUTTON,max_threshold=3,sleep_time=0, AEFC="Unable to select Reauthenticate button on ZCC")
            except Exception as e:
                return (False,f"Error :: ZPA Reauth Failed :: {e}",None)
        
            self.do_okta_login(zpa_username,zpa_password,search_log_time=start_time_for_log_search,partial_login=partial_reauth,use_login_hint=use_login_hint,zpa_reauth=True)   
        except Exception as e:
            self.logger.error(f"ZPA Reauth Failed :: {e}")
            return (False,f"Error :: ZPA Reauth Failed :: {e}",None)
        self.logger.info("Success :: ZPA Reauth Done!")
        return (True,"Success :: ZPA Reauth Done!",None)
    
    # -----------------------------------------------------------------------------------------------------------------------------------------------------

    def restart_zcc(self, 
                    sleep_time=15,                 #time to sleep after execution
                    ):
        """
            This function kills and restart ZCC.
            
            Args:
                sleep_time=15   #time to sleep after execution default is 15
            
            Returns:
                tuple: A tuple containing a boolean and a string indicating the success or failure of the operation.
        """  
        try:
            self.logger.info("********** Restarting ZCC **********")
            self.ui.driver.terminate_app(self.elements.APP)
            time.sleep(5)
            self.ui.driver.activate_app(self.elements.APP)
            self.logger.info("********** Restarted ZCC **********")
            self.logger.info(f"Sleeping for {sleep_time} seconds after restarting ZCC")
            time.sleep(sleep_time)
            return (True, f"Success:: restarted ZCC", None)
        except Exception as e:
            return (False, f"Error:: {e}", None)

    
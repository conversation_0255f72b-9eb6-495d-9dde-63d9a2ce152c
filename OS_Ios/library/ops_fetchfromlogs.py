import os, re
from datetime import datetime, timedelta
from functools import wraps
from common_lib.common.logger import Logger
from common_lib.common.log_ops import LogOps

operating_system = os.environ.get("operating_system").title().lower()

class FetchFromLogs():
    """
    Add methods to fetch information from logs, the methods must be reusable.
    If for specific feature, it can be added in the feature specific module or in the testcase itself
    """
    def __init__(self,
                 handle: str ="easy",
                 dynamic_element_check: bool = True,
                 log_handle: bool =None):
        self.logger = (log_handle if log_handle else Logger.initialize_logger(log_file_name="FetchFromLogs.log", log_level="INFO"))
        self.operating_system = operating_system
        self.logger.info("Initialized FetchFromLogs object")
        self.start_time_for_log_search=""
        self.log_ops = LogOps(console_handle_needed=True, log_handle=self.logger)

    def fetch_applied_device_policy_name(self)->tuple[bool,str,str|None]:
        """
        Do update policy before executing this function
        Fetch the applied device policy name from the tray logs
        """
        start_time = datetime.now() - timedelta(seconds=30)
        file_to_search = "ZSATray"
        try:
            self.log_ops.search_log_file(file=file_to_search, words_to_find_in_line=['"policy_name" ='], break_after_first_hit=False, search_mode=None, start_timestamp=start_time)
        except Exception as e:
            self.logger.error(f"Error occured when searching for 'policy_name =', Error: {e}")
            return False, f"Error occured when searching for 'policy_name =', Error: {e}", None
        else:
            device_policy_name = self.log_ops.recent_log_line.strip().split("=")[1].strip().replace(";","").replace('"',"")
            self.logger.info(f"Required log found at line {self.log_ops.recent_log_line_number}, Device Policy: {device_policy_name}")
            return True,f"Required log found at line {self.log_ops.recent_log_line_number}, Device Policy: {device_policy_name}", device_policy_name

    def fetch_service_entitlement_info(self) -> tuple[bool, str, dict|None]:
        """
        Fetches the Enabled Service Entitlements from the tray logs. Trigger keepAlive before executing this function.
        """
        file_to_search = "ZSATray"

        enabled_services = {
            'zia': 0, 'zpa': 0, 'zdx': 0, 'zdp': 0
        }

        try:
            self.log_ops.search_log_file(file=file_to_search, words_to_find_in_line=['ZIA Enabled: '], break_after_first_hit=False)
        except Exception as e:
            self.logger.error(f"Error occured when searching for 'ZIA Enabled: ', Error: {e}")
            return False, f"Error occured when searching for 'ZIA Enabled: ', Error: {e}", None
        else:
            enabled_services['zia'] = int(self.log_ops.recent_log_line.strip().split(' ')[6].replace(',', ''))
            self.logger.info(f"Required log found at line {self.log_ops.recent_log_line_number}, 'ZIA Enabled: ': {enabled_services['zia']}")

        try:
            self.log_ops.search_log_file(file=file_to_search, words_to_find_in_line=['ZPA Enabled: '], break_after_first_hit=False)
        except Exception as e:
            self.logger.error(f"Error occured when searching for 'ZPA Enabled: ', Error: {e}")
            return False, f"Error occured when searching for 'ZPA Enabled: ', Error: {e}", None
        else:
            enabled_services['zpa'] = int(self.log_ops.recent_log_line.strip().split(' ')[6].replace(',', ''))
            self.logger.info(f"Required log found at line {self.log_ops.recent_log_line_number}, 'ZPA Enabled: ': {enabled_services['zpa']}")

        try:
            self.log_ops.search_log_file(file=file_to_search, words_to_find_in_line=['ZDX Enabled: '], break_after_first_hit=False)
        except Exception as e:
            self.logger.error(f"Error occured when searching for 'ZDX Enabled: ', Error: {e}")
            return False, f"Error occured when searching for 'ZDX Enabled: ', Error: {e}", None
        else:
            enabled_services['zdx'] = int(self.log_ops.recent_log_line.strip().split(' ')[6].replace(',', ''))
            self.logger.info(f"Required log found at line {self.log_ops.recent_log_line_number}, 'ZDX Enabled: ': {enabled_services['zdx']}")

        try:
            self.log_ops.search_log_file(file=file_to_search, words_to_find_in_line=['ZDP Enabled: '], break_after_first_hit=False)
        except Exception as e:
            self.logger.error(f"Error occured when searching for 'ZDP Enabled: ', Error: {e}")
            return False, f"Error occured when searching for 'ZDP Enabled: ', Error: {e}", None
        else:
            enabled_services['zdp'] = int(self.log_ops.recent_log_line.strip().split(' ')[6].replace(',', ''))
            self.logger.info(f"Required log found at line {self.log_ops.recent_log_line_number}, 'ZDP Enabled: ': {enabled_services['zdp']}")

        self.logger.info(f"Fetched Service Entitlement Info: Entitlement Info: {enabled_services}")
        return True,f"Fetched Service Entitlement Info: Entitlement Info: {enabled_services}", enabled_services
    
    def fetch_sme_ip_from_logs(self) -> tuple[bool, str, str]:
        """
        Fetches the SME IP from logs.

        Returns:
            tuple[bool, str, str]: A tuple containing a boolean indicating success or failure,
                                    a message, and the SME IP (if found).
        """
        start_time = datetime.now() - timedelta(seconds=30)
        log_list = [["SME: ", "Protocol:", "Status:"]]

        try:
            for every_word_list in log_list:
                try:
                    self.log_ops.search_log_file(file="ZSATunnel", search_mode=None, words_to_find_in_line=every_word_list)
                except Exception as e:
                    raise Exception(e, f"Error occured when searching for SME IP, Error: {e}")
                else:
                    self.sme_ip = self.log_ops.recent_log_line.strip().split(' ')[9]
        except Exception as e:
            self.logger.error(f"Error fetching SME IP: {e}")
            return False, f"SME IP Not found :: {e}", None

        self.logger.info(f"SME IP found: {self.sme_ip}")
        return True, "SME IP found", self.sme_ip

    def fetch_dns_sme_ip_from_logs(self)->tuple[bool, str, list]:
        """
        If DNS Exclusion or Inclusion is configured in App profile, then an entry to filter store is added for TCP & UDP for a SME IP with DST_PORT 53
        
        This indicates all the DNS Inclusions should go to the SME IP
        
        The flow to use this function would be:
            1. Restart Service to generate a new ZSATunnel File
            2. Update policy to make sure all new updates are fetched
            3. Call fetch_dns_sme_ip_from_logs()
        """
        try:
            
            # Search for the latest log line with "INF Local Filter Store has", Since the route information appears only after this log line
            self.logger.info("Searching for Local Filter")
            self.log_ops.search_log_file(
                file="ZSATunnel", 
                words_to_find_in_line=["Local Filter Store has"],
                start_timestamp=None,
                break_after_first_hit=False,
                wait_time=60
            )

            # Search all the route info in logs after Local Filter Store
            words_to_search_for = ["DST_PORT", "53 - 53", "ACTION", "Redirect"]
            self.log_ops.search_log_file(
                file="ZSATunnel", 
                words_to_find_in_line=words_to_search_for, 
                start_timestamp=None,
                break_after_first_hit=False,
                wait_time=60
            )
            log_lines_found = self.log_ops.list_of_hits_so_far
            regex_to_fetch_ips = r'(.*)IP:(.*)Mask:(.*)'
            sme_dns_ips = list(set(re.match(regex_to_fetch_ips, each_log_line).group(2).strip() for each_log_line in log_lines_found if re.match(regex_to_fetch_ips, each_log_line)))
        except Exception as e:
            self.logger.error(f"Exception while fetching dns sme ip, Error: {e}")
            return False, f"Exception while fetching dns sme ip, Error: {e}", None
        else:
            self.logger.info(f"SME DNS ip's fetched: {sme_dns_ips}")
            return True, f"SME DNS ip's fetched: {sme_dns_ips}", sme_dns_ips



from common_lib.common import logger, constants
from common_lib.mobileadmin import appprofile, trustednetwork, deviceposture, serviceentitlement, clientconnectornotifications, platformsettings
from common_lib.adminzpa import accesszpaapps, zpapolicy, createzpaapps
from common_lib.adminzia.samlzia import SamlZia
from OS_Ios.library import ui_zcc
from OS_Ios.library.ops_system import SysOps
import os, pytest, json, copy, subprocess
import time

config_file = os.path.join(os.getcwd(), "config", os.environ["CONFIG_FILE"])
config = {}
with open(config_file, 'r') as file:
    config.update(json.load(file))

CLOUD_NAME = config["CLOUD"]
const = constants
http_server_ip = "************"
application = const.ZccSanityConst.ZPA_APPS["DOMAIN"]
zpa_application = "http://"+const.ZccSanityConst.ZPA_APPS["DOMAIN"]['app']
zpa_application_assertion_element = '//XCUIElementTypeStaticText[@name="SUCCESS, CONNECTED TO AUTOMATION SERVER"]'
device_posture_logger = logger.Logger.initialize_logger(log_file_name="device_posture_regression.log")
create_zpa_apps = create_zpa_apps = createzpaapps.MakeZpaApplication(config["CLOUD"], config=config_file, log_handle=device_posture_logger,file=[application['app']], server_ip=http_server_ip)
ma_appprofile = appprofile.AppProfile(config=config_file, cloud=CLOUD_NAME)
ma_device_posture = deviceposture.DevicePosture(config=config_file, cloud=CLOUD_NAME)
ma_serviceentitlement = serviceentitlement.ServiceEntitlement(cloud=CLOUD_NAME, config=config_file)
platform_settings_obj = platformsettings.PlatformSettings(cloud=CLOUD_NAME, config=config_file, log_handle=device_posture_logger)
saml_zia_obj = SamlZia(cloud=CLOUD_NAME, config=config_file, log_handle=device_posture_logger, debug=True)
clienconnectornotif_obj = clientconnectornotifications.ClientConnectorNotifications(cloud=config['CLOUD'], config=config_file, log_handle=device_posture_logger)
zpa_policy = zpapolicy.ZpaPolicy(cloud=CLOUD_NAME, config=config_file)
zpa_appaccess = accesszpaapps.AccessZpaApps(config=config_file, cloud=CLOUD_NAME)
ACCESS_POLICY_NAME = "zpa_device_posture"
BLOCK_ALL_ACCESS_POLICY_NAME = "zpa_device_posture_block_all"

zcc = ui_zcc.Zcc(start_app=True)
sys_ops_obj = SysOps(driver=zcc.ui.driver, logger=device_posture_logger)

def make_teardown() -> None:
    """
    Performs teardown operations by deleting various profiles and policies.

    This function is responsible for cleaning up after a test run by deleting the
    application profile, forwarding profile, ZPA applications, and access policies.

    Args:
        None

    Raises:
        Exception: If any of the deletion operations fail.

    Returns:
        None
    """
    # Delete the application profile
    ma_appprofile.delete_app_profile()  # Delete the application profile
    
    # Delete the forwarding profile
    ma_appprofile.forwarding_profile.delete_forwarding_profile()  # Delete the forwarding profile
    
    # Delete the ZPA applications
    create_zpa_apps.delete_zpa_apps()  # Delete the ZPA applications
    
    # Delete the access policies
    zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=ACCESS_POLICY_NAME)  # Delete the access policy
    zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=BLOCK_ALL_ACCESS_POLICY_NAME)  # Delete the block all access policy


@pytest.fixture(scope="class", autouse=True)
def create_setup(request):
    """
    Fixture to create the setup for the test class.

    This fixture is used to create the necessary setup for the test class.
    It deletes any existing app profiles, forwarding profiles, ZPA apps, and device postures.
    Then it creates a new forwarding profile, app profile, and ZPA apps.
    It also deletes any existing access policies and creates new ones.
    Finally, it toggles ZIA, ZPA, and ZDX on, and configures AUP and SAML settings.

    Args:
        request: The pytest request object.

    Raises:
        Exception: If any of the setup steps fail.
    """
    
    try:
        # Delete any existing app profiles
        ma_appprofile.delete_app_profile()
        
        # Delete any existing forwarding profiles
        ma_appprofile.forwarding_profile.delete_forwarding_profile()
        
        # Delete any existing ZPA apps
        create_zpa_apps.delete_zpa_apps()
        
        # Delete any existing device postures
        ma_device_posture.delete_device_posture(delete_all_postures=True)

        # Create a new forwarding profile
        result = ma_appprofile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]  # Check if the creation was successful

        # Create a new app profile
        result = ma_appprofile.create_app_profile()
        assert result[0], result[1]  # Check if the creation was successful

        # Create new ZPA apps
        res = create_zpa_apps.create_zpa_apps()
        device_posture_logger.info(res[1])  # Log the result
        assert res[0], res[1]  # Check if the creation was successful

        # Delete any existing access policies
        zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=ACCESS_POLICY_NAME)
        zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=BLOCK_ALL_ACCESS_POLICY_NAME)
        ma_device_posture.delete_device_posture(delete_all_postures=True)

        # Toggle ZIA on
        result = ma_serviceentitlement.toggle_zia(action=True)
        assert result[0], result[1]  # Check if the toggle was successful

        # Toggle ZPA on
        result = ma_serviceentitlement.toggle_zpa(action=True)
        assert result[0], result[1]  # Check if the toggle was successful

        # Toggle ZDX on
        result = ma_serviceentitlement.toggle_zdx(action=True)
        assert result[0], result[1]  # Check if the toggle was successful

        # Create a new access policy
        result = zpa_policy.create_access_policy(zpa_user_name=config["ZPA_ADMIN_ID"], zpa_password=config["ZPA_ADMIN_PASSWORD"], access_policy_name=BLOCK_ALL_ACCESS_POLICY_NAME, action="DENY")
        assert result[0], result[1]  # Check if the creation was successful

        # Create another new access policy
        result = zpa_policy.create_access_policy(zpa_user_name=config["ZPA_ADMIN_ID"], zpa_password=config["ZPA_ADMIN_PASSWORD"], access_policy_name=ACCESS_POLICY_NAME)
        assert result[0], result[1]  # Check if the creation was successful

        # Toggle ZPA on again
        result=ma_serviceentitlement.toggle_zpa(action=True)
        assert result[0], result[1]  # Check if the toggle was successful

        # Configure AUP settings
        result=clienconnectornotif_obj.configure_aup(aup_frequency="never")
        assert result[0], result[1]  # Check if the configuration was successful

        # Enable SAML on ZIA
        result=saml_zia_obj.saml_action_smui(mode='enable')
        assert result[0], result[1]  # Check if the enablement was successful

        # Change SAML IDP on ZIA
        result =saml_zia_obj.change_saml_on_zia(idp_name=config['IDP_NAME'])
        assert result[0],result[1]  # Check if the change was successful

        # Disable browser-based authentication on MA
        result=platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]  # Check if the disablement was successful

    except Exception as e:
        # Log any errors that occur during setup
        device_posture_logger.error(f"Unable to create setup, Error: {e}")
        request.addfinalizer(make_teardown)  # Add a finalizer to clean up
    else:
        # Log a success message if setup is successful
        device_posture_logger.info(f"Successfully created setup")
        request.addfinalizer(make_teardown)  # Add a finalizer to clean up










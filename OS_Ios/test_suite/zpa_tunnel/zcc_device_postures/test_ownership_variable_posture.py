import pytest
import allure
from common_lib.Custom_Markers import zcc_mark_version, add_markers
# from OS_Ios.test_suite.zpa.zcc_device_postures import conftest
from OS_Ios.test_suite.zpa_tunnel.zcc_device_postures import conftest
import time

class TestOwnershipVariablePosture:
    ownership_variable = "Ownership123"
    device_posture_name = "ownership_variable_posture"
    mismatch_device_posture_name = "mismatch_ownershipvariable_posture"
    @allure.title("Setup Class")
    def setup_class(self):
        """
        Setup class for the test suite.
    
        This function performs the necessary setup for the test suite, including logging in to ZCC and creating device postures.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If the login or device posture creation fails.
        """
        # Login to ZCC with the provided credentials and settings
        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zpa_user=conftest.config["ZPA_USER_ID"],
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],
            zia_saml_login=True
        )
        # Assert that the login was successful
        assert result[0], result[1]
    
        # Validate that ZCC is logged in
        result = conftest.zcc.validate_zcc_logged_in()
        # Assert that the validation was successful
        assert result[0], result[1]
    
        # Delete any existing device postures with the same name
        conftest.ma_device_posture.delete_device_posture(posture_name=self.device_posture_name)
        conftest.ma_device_posture.delete_device_posture(posture_name=self.mismatch_device_posture_name)
    
        # Log a message indicating that the ownership variable device posture is being created for iOS
        conftest.device_posture_logger.info("Creating ownership variable device posture for iOS")
        # Create the ownership variable device posture
        result = conftest.ma_device_posture.create_ownership_posture(posture_name=self.device_posture_name, value=self.ownership_variable)
        # Assert that the creation was successful
        assert result[0], result[1]
    
        # Create a mismatched device posture with a different value
        result = conftest.ma_device_posture.create_ownership_posture(posture_name=self.mismatch_device_posture_name, value=self.ownership_variable+"4")
        # Assert that the creation was successful
        assert result[0], result[1]

    
    @pytest.mark.xray("QA-292613")
    @zcc_mark_version(3.7)
    @add_markers("sanity", "regression")
    @allure.title("Allow access to application if ownership variable has been detected as 1")
    def test_ownership_variable_allow_verified(self):
        """
        Test case to verify that access to an application is allowed when the ownership variable is detected as 1.
    
        This test case edits an access policy to allow access based on device posture, updates the policy, and then attempts to access the application.
    
        Args:
            self: A reference to the instance of the class that this function belongs to.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions in the test case fail.
        """
    
        # Edit the access policy to allow access based on device posture
        result = conftest.zpa_policy.edit_access_policy(
            access_policy_name=conftest.ACCESS_POLICY_NAME, 
            action="ALLOW", 
            criteria="deviceposture", 
            device_postures=[self.device_posture_name], 
            action_for_each_device_posture=[True]
        )
        # Assert that the policy edit was successful
        assert result[0], result[1]
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Attempt to access the application
        result = conftest.sys_ops_obj.access_application(
            browser="safari", 
            url=conftest.zpa_application, 
            assertion_element=conftest.zpa_application_assertion_element
        )
        # Assert that the application access was successful
        assert result[0], result[1]


    @pytest.mark.xray("QA-292614")
    @zcc_mark_version(3.7)
    @add_markers("sanity", "regression")
    @allure.title("Do not allow access to application if ownership variable has been detected as 1")
    def test_ownership_variable_deny_verified(self):
        """
        Test case to verify that access to an application is denied when the ownership variable is detected as 1.
    
        This test case edits an access policy to deny access based on device posture, updates the policy, and then attempts to access the application.
    
        Args:
            self (object): A reference to the instance of the class that this function belongs to.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions in the test case fail.
        """
        
        # Edit the access policy to deny access based on device posture
        result = conftest.zpa_policy.edit_access_policy(
            access_policy_name=conftest.ACCESS_POLICY_NAME, 
            action="DENY", 
            criteria="deviceposture", 
            device_postures=[self.device_posture_name], 
            action_for_each_device_posture=[True]
        )
        
        # Assert that the policy edit was successful
        assert result[0], result[1]
    
        # Update the policy
        result = conftest.zcc.update_policy()
        
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Attempt to access the application and expect failure
        result = conftest.sys_ops_obj.access_application(
            browser="safari", 
            url=conftest.zpa_application, 
            assertion_element=conftest.zpa_application_assertion_element, 
            failure_expected=True, 
            dynamic_wait_search_element=10
        )
        
        # Assert that the application access failed as expected
        assert result[0], result[1]

    @pytest.mark.xray("QA-292615")
    @zcc_mark_version(3.7)
    @add_markers("sanity", "regression")
    @allure.title("Allow access to application if ownership variable is not matched")
    def test_ownership_variable_allow_non_verified(self) -> None:
        """
        Test case to verify that access to an application is allowed when the ownership variable is not matched.
    
        This test case edits an access policy to allow access to an application based on a device posture criteria,
        updates the policy, and then attempts to access the application using a specified browser.
    
        Args:
            self: A reference to the instance of the class that this function belongs to.
    
        Returns:
            None
        """
        
        # Edit the access policy to allow access to the application based on a device posture criteria
        result = conftest.zpa_policy.edit_access_policy(
            access_policy_name=conftest.ACCESS_POLICY_NAME, 
            action="ALLOW", 
            criteria="deviceposture", 
            device_postures=[self.mismatch_device_posture_name], 
            action_for_each_device_posture=[False]
        )
        
        # Assert that the policy edit operation was successful
        assert result[0], result[1]
    
        # Update the policy
        result = conftest.zcc.update_policy()
        
        # Assert that the policy update operation was successful
        assert result[0], result[1]
        
        # Attempt to access the application using a specified browser
        result = conftest.sys_ops_obj.access_application(
            browser="safari", 
            url=conftest.zpa_application, 
            assertion_element=conftest.zpa_application_assertion_element
        )
        
        # Assert that the application access was successful
        assert result[0], result[1]

    @pytest.mark.xray("QA-292616")
    @zcc_mark_version(3.7)
    @add_markers("sanity", "regression")
    @allure.title("Do not allow access to application if ownership variable is not matched")
    def test_ownership_variable_deny_non_verified(self):
        """
        Test case to verify that access to an application is denied when the ownership variable does not match.
    
        This test case edits an access policy to deny access based on a device posture, updates the policy, and then attempts to access the application.
    
        Args:
            self (object): A reference to the instance of the class that this function belongs to.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions in the test case fail.
        """
    
        # Edit the access policy to deny access based on a device posture
        result = conftest.zpa_policy.edit_access_policy(
            access_policy_name=conftest.ACCESS_POLICY_NAME, 
            action="DENY", 
            criteria="deviceposture", 
            device_postures=[self.mismatch_device_posture_name], 
            action_for_each_device_posture=[False]
        )
        # Assert that the policy edit was successful
        assert result[0], result[1]
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Attempt to access the application and expect failure
        result = conftest.sys_ops_obj.access_application(
            browser="safari", 
            url=conftest.zpa_application, 
            assertion_element=conftest.zpa_application_assertion_element, 
            failure_expected=True, 
            dynamic_wait_search_element=10
        )
        # Assert that the application access failed as expected
        assert result[0], result[1]
        
    @allure.title("Teardown Class")
    def teardown_class(self):
        """
        Teardown class to delete created resources.
    
        This function is used to delete the created resources such as device postures and policies.
        It is called after the test class has finished execution.
    
        Args:
            self (object): A reference to the instance of the class that this function belongs to.
    
        Returns:
            None
    
        Raises:
            Exception: If an error occurs while deleting the resources.
        """
        # Log a message to indicate that the ownership variable device posture is being deleted
        conftest.device_posture_logger.info("Deleting ownership variable device posture")
        
        # Delete the access policy with the specified name
        conftest.zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=conftest.ACCESS_POLICY_NAME)
        
        # Delete the block all access policy with the specified name
        conftest.zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=conftest.BLOCK_ALL_ACCESS_POLICY_NAME)
        
        # Delete the device posture with the specified name
        conftest.ma_device_posture.delete_device_posture(posture_name=self.device_posture_name)
        
        # Delete the mismatch device posture with the specified name
        conftest.ma_device_posture.delete_device_posture(posture_name=self.mismatch_device_posture_name)
        
        # Force logout from the ZCC application
        conftest.zcc.logout()
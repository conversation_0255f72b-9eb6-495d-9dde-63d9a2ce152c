import pytest
import allure
from common_lib.Custom_Markers import zcc_mark_version, add_markers
# from OS_Ios.test_suite.zpa.zcc_device_postures import conftest
from OS_Ios.test_suite.zpa_tunnel.zcc_device_postures import conftest
import time

class TestCertificateTrustPosture:
    device_posture_name = "certificatetrust_posture"
    mismatch_device_posture_name = "mismatch_certificate_trust_posture"
    @allure.title("Setup Class")
    def setup_class(self):
        """
        Setup class method to perform initial setup tasks before running the tests.

        This method logs in to the ZCC, validates the login, deletes existing device postures,
        and creates new certificate trust device postures.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If the login or device posture creation fails.
        """
        # Login to ZCC with the provided credentials and settings
        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],  # ZIA user ID
            zia_password=conftest.config["ZIA_USER_PASSWORD"],  # ZIA user password
            zpa_user=conftest.config["ZPA_USER_ID"],  # ZPA user ID
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],  # ZPA user password
            zia_saml_login=True  # Use ZIA SAML login
        )
        # Assert that the login was successful
        assert result[0], result[1]
    
        # Validate that ZCC is logged in
        result = conftest.zcc.validate_zcc_logged_in()
        # Assert that the validation was successful
        assert result[0], result[1]
    
        # Delete any existing device postures with the same names
        conftest.ma_device_posture.delete_device_posture(posture_name=self.device_posture_name)
        conftest.ma_device_posture.delete_device_posture(posture_name=self.mismatch_device_posture_name)
    
        # Log a message indicating that a certificate trust device posture is being created
        conftest.device_posture_logger.info("Creating certificate trust device posture")
    
        # Create a certificate trust device posture with the provided certificate and posture name
        result = conftest.ma_device_posture.create_certificate_trust_dp(posture_name=self.device_posture_name, cert_to_be_uploaded="Cert_Trust_Verified")
        # Assert that the creation was successful
        assert result[0], result[1]
    
        # Create another certificate trust device posture with a different certificate and posture name
        result = conftest.ma_device_posture.create_certificate_trust_dp(posture_name=self.mismatch_device_posture_name, cert_to_be_uploaded="Exportable")
        # Assert that the creation was successful
        assert result[0], result[1]
    
    @pytest.mark.xray('QA-292609')
    @zcc_mark_version(3.7)
    @add_markers("sanity", "regression")
    @allure.title("Allow access to application if certificatetrust has been detected")
    def test_certificatetrust_allow_verified(self):
        pass
        # Edit the access policy to allow access to the application based on device posture
        result = conftest.zpa_policy.edit_access_policy(
            access_policy_name=conftest.ACCESS_POLICY_NAME, 
            action="ALLOW", 
            criteria="deviceposture", 
            device_postures=[self.device_posture_name], 
            action_for_each_device_posture=[True]
        )
        # Assert that the policy edit was successful
        assert result[0], result[1]
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Access the application to verify that access is allowed
        result = conftest.sys_ops_obj.access_application(
            browser="safari", 
            url=conftest.zpa_application, 
            assertion_element=conftest.zpa_application_assertion_element
        )
        # Assert that the application access was successful
        assert result[0], result[1]

    @pytest.mark.xray("QA-292610")
    @zcc_mark_version(3.7)
    @add_markers("sanity", "regression")
    @allure.title("Do not allow access to application if certificatetrust has been detected")
    def test_certificatetrust_deny_verified(self):
        """
        Test case to verify that access to an application is denied when certificate trust has been detected.
    
        This test case edits an access policy to deny access based on device posture, updates the policy, 
        and then attempts to access the application with the updated policy.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        
        # Edit the access policy to deny access based on device posture
        result = conftest.zpa_policy.edit_access_policy(
            access_policy_name=conftest.ACCESS_POLICY_NAME, 
            action="DENY", 
            criteria="deviceposture", 
            device_postures=[self.device_posture_name], 
            action_for_each_device_posture=[True]
        )
        
        # Assert that the policy edit was successful
        assert result[0], result[1]
    
        # Update the policy
        result = conftest.zcc.update_policy()
        
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Attempt to access the application with the updated policy
        result = conftest.sys_ops_obj.access_application(
            browser="safari", 
            url=conftest.zpa_application, 
            assertion_element=conftest.zpa_application_assertion_element, 
            failure_expected=True, 
            dynamic_wait_search_element=10
        )
        
        # Assert that the application access was denied as expected
        assert result[0], result[1]

    @pytest.mark.xray("QA-292611")
    @zcc_mark_version(3.7)
    @add_markers("sanity", "regression")
    @allure.title("Allow access to application if certificatetrust is not detected")
    def test_certificatetrust_allow_non_verified(self) -> None:
        """
        Test case to verify that access to an application is allowed when certificatetrust is not detected.
    
        This test case edits an access policy to allow access to an application when a specific device posture is not detected.
        It then updates the policy and attempts to access the application using a specified browser.
    
        Args:
            self (object): A reference to the instance of the class that this function belongs to.
    
        Returns:
            None
        """
    
        # Edit the access policy to allow access to the application when the device posture is not detected
        result = conftest.zpa_policy.edit_access_policy(
            access_policy_name=conftest.ACCESS_POLICY_NAME, 
            action="ALLOW", 
            criteria="deviceposture", 
            device_postures=[self.mismatch_device_posture_name], 
            action_for_each_device_posture=[False]
        )
        
        # Assert that the policy edit was successful
        assert result[0], result[1]
    
        # Update the policy
        result = conftest.zcc.update_policy()
        
        # Assert that the policy update was successful
        assert result[0], result[1]
        
        # Attempt to access the application using the specified browser
        result = conftest.sys_ops_obj.access_application(
            browser="safari", 
            url=conftest.zpa_application, 
            assertion_element=conftest.zpa_application_assertion_element
        )
        
        # Assert that the application access was successful
        assert result[0], result[1]

    @pytest.mark.xray("QA-292612")
    @zcc_mark_version(3.7)
    @add_markers("sanity", "regression")
    @allure.title("Do not allow access to application if certificatetrust is not detected")
    def test_certificatetrust_deny_non_verified(self) -> None:
        """
        Test case to verify that access to an application is denied when certificate trust is not detected.
    
        This test case edits an access policy to deny access based on device posture, updates the policy, 
        and then attempts to access the application with a mismatched device posture.
    
        Args:
            self: A reference to the instance of the class that this function belongs to.
    
        Returns:
            None
        """
        # Edit the access policy to deny access based on device posture
        result = conftest.zpa_policy.edit_access_policy(
            access_policy_name=conftest.ACCESS_POLICY_NAME, 
            action="DENY", 
            criteria="deviceposture",
            device_postures=[self.mismatch_device_posture_name], 
            action_for_each_device_posture=[False]
        )
        # Assert that the policy edit was successful
        assert result[0], result[1]
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Attempt to access the application with a mismatched device posture
        result = conftest.sys_ops_obj.access_application(
            browser="safari", 
            url=conftest.zpa_application, 
            assertion_element=conftest.zpa_application_assertion_element, 
            failure_expected=True, 
            dynamic_wait_search_element=10
        )
        # Assert that access to the application was denied
        assert result[0], result[1]
    
    @allure.title("Teardown Class")
    def teardown_class(self) -> None:
        """
        Teardown class to delete created policies and device postures.
    
        This function is used to delete the policies and device postures created during the test execution.
        It ensures that the test environment is cleaned up after the test run.
    
        Returns:
            None
        """
        # Log a message to indicate the start of the teardown process
        conftest.device_posture_logger.info("Deleting certificate trust device posture")
        
        # Delete the access policy created during the test execution
        conftest.zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=conftest.ACCESS_POLICY_NAME)
        
        # Delete the block all access policy created during the test execution
        conftest.zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=conftest.BLOCK_ALL_ACCESS_POLICY_NAME)
        
        # Delete the device posture created during the test execution
        conftest.ma_device_posture.delete_device_posture(posture_name=self.device_posture_name)
        
        # Delete the mismatch device posture created during the test execution
        conftest.ma_device_posture.delete_device_posture(posture_name=self.mismatch_device_posture_name)
        
        # Force logout from the ZCC application
        conftest.zcc.logout()

# ZPA Device Postures
The suite covers Zpa app access based on device postures

### Supported version
ZCC 3.7+

### Setup Documentation
https://confluence.corp.zscaler.com/display/~priyanshu.sharma/iOS+Automation+Setup

### Initial Setup
1. Install below certificates on iOS device from "/OS_Ios/resource/certificates":
    - CertificateTrustCert.pem
    - CertificateTrustNonVerified.pem
2. Push app configurations from MDM with Ownership Variable as "Ownership123" 
3. Make sure device is not jailbroken

### Basic Flow
1. Create a device posture, eg: Certificate Trust
2. Let zcc evaluate the device posture, It can be validated from logs if the posture has been evaluated.
3. Create required access policies in ZPA, eg: Allow access to app if the device posture is verified.
4. Validate the correct access to ZPA apps is given based on the access policies.


### Note
Required values to be passed through config :
{
    "ZIA_USER_ID" : "",
    "ZIA_USERNAME" : "",
    "ZIA_USER_PASSWORD" : "",
    "ZIA_ADMIN_ID" : "",
    "ZIA_ADMIN_PASSWORD" : "",
    "ZPA_ADMIN_ID" : "",
    "ZPA_ADMIN_PASSWORD" : "",
    "ZPA_USER_ID" : "",
    "ZPA_USER_PASSWORD" : "",
    "ZPA_APPLICATION_NAME" : "",
    "CLOUD" : "zscalertwo",
    "IDP_NAME" : "Okta",
    "MAC_HOSTNAME" : "Priyanshu's Macbook Pro"
  }

### Command To Trigger Automation
    python3 trigger_automation.py --testcases OS_Ios/test_suite/zpa_tunnel/zcc_device_postures/test_certificatetrust_posture.py --zccVersion 3.8  --config ios-automation.json

### Author
Priyanshu Sharma (<EMAIL>)
import pytest
import allure
from common_lib.Custom_Markers import zcc_mark_version, add_markers
# from OS_Ios.test_suite.zpa.zcc_device_postures import conftest
from OS_Ios.test_suite.zpa_tunnel.zcc_device_postures import conftest
import time

class TestUnauthorizedModificationPosture:
    device_posture_name = "unauthorized_modification_posture"
    @allure.title("Setup Class")
    def setup_class(self):
        """
        Setup class for the test suite.
    
        This function logs in to ZCC, validates the login, deletes any existing device posture, 
        and creates a new unauthorized modification device posture for iOS.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If the login or posture creation fails.
        """
        # Login to ZCC with the provided credentials and settings
        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],  # ZIA username
            zia_password=conftest.config["ZIA_USER_PASSWORD"],  # ZIA password
            zpa_user=conftest.config["ZPA_USER_ID"],  # ZPA username
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],  # ZPA password
            zia_saml_login=True  # Enable ZIA SAML login
        )
        # Assert that the login was successful
        assert result[0], result[1]
    
        # Validate that ZCC is logged in
        result = conftest.zcc.validate_zcc_logged_in()
        # Assert that the validation was successful
        assert result[0], result[1]
    
        # Delete any existing device posture with the given name
        conftest.ma_device_posture.delete_device_posture(posture_name=self.device_posture_name)
        # Log a message indicating that a new posture is being created
        conftest.device_posture_logger.info("Creating unauthorized modification device posture for iOS")
        # Create a new unauthorized modification device posture for iOS
        result = conftest.ma_device_posture.create_unauthorised_posture(posture_name=self.device_posture_name)
        # Assert that the posture creation was successful
        assert result[0], result[1]
    
    """
    test_unauthorized_modification_allow_verified:
        Tests if access to an application is allowed when unauthorized modification has been detected as 1.
    
        This test case edits an access policy to allow access to an application when unauthorized modification has been detected as 1.
        It then updates the policy and attempts to access the application.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If the test case fails.
    """
    @pytest.mark.xray("QA-292617")
    @zcc_mark_version(3.7)
    @add_markers("sanity", "regression")
    @allure.title("Allow access to application if unauthorized modification has been detected as 1")
    def test_unauthorized_modification_allow_verified(self):
        # Edit the access policy to allow access to the application when unauthorized modification has been detected as 1
        result = conftest.zpa_policy.edit_access_policy(
            access_policy_name=conftest.ACCESS_POLICY_NAME, 
            action="ALLOW", 
            criteria="deviceposture", 
            device_postures=[self.device_posture_name], 
            action_for_each_device_posture=[True]
        )
        # Assert that the policy edit was successful
        assert result[0], result[1]
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Attempt to access the application
        result = conftest.sys_ops_obj.access_application(
            browser="safari", 
            url=conftest.zpa_application, 
            assertion_element=conftest.zpa_application_assertion_element
        )
        # Assert that the application access was successful
        assert result[0], result[1]


    @pytest.mark.xray("QA-292618")
    @zcc_mark_version(3.7)
    @add_markers("sanity", "regression")
    @allure.title("Do not allow access to application if unauthorized modification has been detected as 1")
    def test_unauthorized_modification_deny_verified(self):
        """
        Test case to verify that access to an application is denied when unauthorized modification is detected.
    
        This test case edits an access policy to deny access when a specific device posture is detected, 
        updates the policy, and then attempts to access the application to verify that access is denied.
    
        Args:
            self: A reference to the instance of the class that this function belongs to.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions in the test case fail.
        """
        
        # Edit the access policy to deny access when the specified device posture is detected
        result = conftest.zpa_policy.edit_access_policy(
            access_policy_name=conftest.ACCESS_POLICY_NAME, 
            action="DENY", 
            criteria="deviceposture",
            device_postures=[self.device_posture_name], 
            action_for_each_device_posture=[True]
        )
        # Assert that the policy edit was successful
        assert result[0], result[1]
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Attempt to access the application and verify that access is denied
        result = conftest.sys_ops_obj.access_application(
            browser="safari", 
            url=conftest.zpa_application, 
            assertion_element=conftest.zpa_application_assertion_element, 
            failure_expected=True, 
            dynamic_wait_search_element=10, 
            close_tabs=True
        )
        # Assert that access was denied
        assert result[0], result[1]
        
    @allure.title("Teardown Class")
    def teardown_class(self):
        """
        Teardown class method to clean up resources after test execution.
    
        This method deletes the device posture and policies created during test setup.
        It ensures a clean environment for subsequent test runs.
    
        Args:
            self (object): A reference to the instance of the class that this method belongs to.
    
        Returns:
            None
        """
        # Log a message to indicate the start of teardown
        conftest.device_posture_logger.info("Deleting Unauthorized Modification device posture")
        
        # Delete the access policy created during test setup
        conftest.zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=conftest.ACCESS_POLICY_NAME)
        
        # Delete the block all access policy created during test setup
        conftest.zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=conftest.BLOCK_ALL_ACCESS_POLICY_NAME)
        
        # Delete the device posture created during test setup
        conftest.ma_device_posture.delete_device_posture(posture_name=self.device_posture_name)
        
        # Force logout from the ZCC application
        conftest.zcc.logout()
        
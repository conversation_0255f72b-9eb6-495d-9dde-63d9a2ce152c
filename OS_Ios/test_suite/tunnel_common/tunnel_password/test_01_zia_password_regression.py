import allure
import pytest
from common_lib.Custom_Markers import add_markers,zcc_mark_version #Required!
# from OS_Ios.test_suite.zcc_misc.zcc_password_regression import conftest
from OS_Ios.test_suite.ktunnel_common.ktunnel_password import conftest


"""
This class contains testcases for ZIA Disable Password Regression 
"""

class TestZiaDisablePassword:
    
    @allure.title('Setup')
    def setup_class(self):
        """
        1. Delete App policy
        2. Delete forwarding profile
        3. Create forwarding profile
        4. Create App profile
        4. Login ZCC
        """
        conftest.ma_app_profile.delete_app_profile()

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()

        result = conftest.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        result=conftest.ma_service_entitlement.toggle_zpa(action=True)
        assert result[0], result[1]

        result=conftest.ma_notifications.configure_aup()
        assert result[0], result[1]

        result=conftest.saml_zia_obj.saml_action_smui(mode='disable')
        assert result[0], result[1]

        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]

        result = conftest.zcc.login(
            aup=False,
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zpa_user=conftest.config["ZPA_USER_ID"],
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in(check_alerts_after_login=True, dynamic_wait_search_element=40)
        assert result[0], result[1]

    @pytest.mark.xray("QA-277648")
    @allure.title('Validate ZIA cannot be disabled if disable reason is not given when disable reason is enabled in app profile')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_without_reason(self):
        
        """
        setup steps following:
        1. Edit App policy with enable Disable Reason policy and try toggle off without a reason
        
        """
        # Set ZIA Disable Reason -> On ----------------------------------------------------------------------------------------------------------
        result = conftest.ma_app_profile.edit_app_profile(disable_reason=True)
        assert result[0], result[1]

        # Update Policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        # Try to Disable ZIA without Reason
        result = conftest.zcc.toggle_service(service='ZIA', action=False, failure_expected=True)
        assert result[0], result[1]

    @pytest.mark.xray("QA-277651")
    @allure.title('Disable ZIA with disable reason enabled')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_with_reason(self):
        """
        setup steps following:
        1. Edit App policy with enable Disable Reason policy and try toggle off with a reason

        """
        
        # Set ZIA Disable Reason -> On ----------------------------------------------------------------------------------------------------------
        result = conftest.ma_app_profile.edit_app_profile(disable_reason=True)
        assert result[0], result[1]

        # Update Policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        # Try to Disable ZIA with Reason
        result = conftest.zcc.toggle_service(service='ZIA', action=False, disable_reason=conftest.REASON)
        assert result[0], result[1]

        # Enable ZIA
        result = conftest.zcc.toggle_service(service='ZIA', action=True)
        assert result[0], result[1]

    @pytest.mark.xray("QA-277647")
    @allure.title('Validate ZIA cannot be disabled if disable reason & password is not given when disable reason & password is enabled in app profile')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_without_password_and_reason(self):
        """
        setup steps following:
        1. Edit App policy with enable Disable Password policy and disable reason. Now try toggle off with a wrong password or without a reason
        
        """
         # Set ZIA Disable Password -> On & Reason -> On -----------------------------------------------------------------------------------------
        result = conftest.ma_app_profile.edit_app_profile(zia_disable_password=conftest.NORMAL_PASSWORD, disable_reason=True)
        assert result[0], result[1]

        # Update policy in ZCC
        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        # Try to Disable ZIA with wrong Password and without disable reason
        result = conftest.zcc.toggle_service(service='ZIA', action=False, password=conftest.WRONG_PASSWORD, failure_expected=True)
        assert result[0], result[1]

        # Try to Disable ZIA with wrong Password and with disable reason
        result = conftest.zcc.toggle_service(service='ZIA', action=False, password=conftest.WRONG_PASSWORD,disable_reason=conftest.REASON,failure_expected=True)
        assert result[0], result[1]
        
         # Try to Disable ZIA with correct Password and without disable reason
        result = conftest.zcc.toggle_service(service='ZIA', action=False, password=conftest.NORMAL_PASSWORD,failure_expected=True)
        assert result[0], result[1]

    @pytest.mark.xray("QA-277650")
    @allure.title('Disable ZIA with password & disable reason enabled')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_with_password_and_reason(self):
        """
        setup steps following:
        1. Edit App policy with enable Disable Password policy and enable Disable Reason Policy and try toggle off with correct password and disable reason
        
        """
         # Set ZIA Disable Password -> On & Reason -> On -----------------------------------------------------------------------------------------
        result = conftest.ma_app_profile.edit_app_profile(zia_disable_password=conftest.NORMAL_PASSWORD, disable_reason=True)
        assert result[0], result[1]

        # Update policy in ZCC
        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        # Try to Disable ZIA with correct Password and with disable reason
        result = conftest.zcc.toggle_service(service='ZIA', action=False, password=conftest.NORMAL_PASSWORD,disable_reason=conftest.REASON)
        assert result[0], result[1]

        # Enable ZIA
        result = conftest.zcc.toggle_service(service='ZIA', action=True)
        assert result[0], result[1]
    
    @pytest.mark.xray("QA-277645")
    @allure.title('Validate that wrong password is not accepted to disable ZIA ')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_with_wrong_password(self):

        """
        setup steps following:
        1. Edit App policy with enable Disable Password policy and try toggle off with a wrong password
        
        """
         # Set ZIA Disable Password -> On & Reason -> Off -----------------------------------------------------------------------------------------
        result = conftest.ma_app_profile.edit_app_profile(zia_disable_password=conftest.NORMAL_PASSWORD, disable_reason=False)
        assert result[0], result[1]

        # Update policy in ZCC
        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        # Try to Disable ZIA with wrong Password
        result = conftest.zcc.toggle_service(service='ZIA', action=False, password=conftest.WRONG_PASSWORD, failure_expected=True)
        assert result[0], result[1]

    @pytest.mark.xray("QA-277649")
    @allure.title('Validate ZIA cannot be disabled if password is not given when password is set in app profile')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_without_password(self): 
        """
        setup steps following:
        1. Edit App policy with enable Disable Password policy and try toggle off with a without password
        
        """
        # Set ZIA Disable Password -> On & Reason -> Off -----------------------------------------------------------------------------------------
        result = conftest.ma_app_profile.edit_app_profile(zia_disable_password=conftest.NORMAL_PASSWORD, disable_reason=False)
        assert result[0], result[1]

        # Update policy in ZCC
        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        # Try to Disable ZIA without Password
        result = conftest.zcc.toggle_service(service='ZIA', action=False, failure_expected=True)
        assert result[0], result[1]

    @pytest.mark.xray("QA-277652")
    @allure.title('Disable ZIA with password enabled')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_with_correct_password(self):
        """
        setup steps following:
        1. Edit App policy with enable Disable Password policy and try toggle off with a correct password
        
        """
         # Set ZIA Disable Password -> On & Reason -> Off -----------------------------------------------------------------------------------------
        result = conftest.ma_app_profile.edit_app_profile(zia_disable_password=conftest.NORMAL_PASSWORD, disable_reason=False)
        assert result[0], result[1]

        # Update policy in ZCC
        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        # Try to Disable ZIA with Password
        result = conftest.zcc.toggle_service(service='ZIA', action=False, password=conftest.NORMAL_PASSWORD)
        assert result[0], result[1]

        # Enable ZIA
        result = conftest.zcc.toggle_service(service='ZIA', action=True)
        assert result[0], result[1]
    
    @pytest.mark.xray("QA-277646")
    @allure.title('Creating special character password for the zia disable ')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_with_special(self):
        """
        setup steps following:
        1. Edit App policy with enable Disable Password policy and try toggle off with a special character password
        
        """
        result = conftest.ma_app_profile.edit_app_profile(zia_disable_password=conftest.SPECIAL_PASSWORD)
        assert result[0], result[1]

        # Update policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        # Try to Disable ZIA with special character Password
        result = conftest.zcc.toggle_service(service='ZIA', action=False, password=conftest.SPECIAL_PASSWORD)
        assert result[0], result[1]

        # Enable ZIA
        result = conftest.zcc.toggle_service(service='ZIA', action=True)
        assert result[0], result[1]

    @pytest.mark.xray("QA-277644")
    @allure.title('Setting up a password with character length greater than the max limit')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_with_max_char(self):
        """
        setup steps following:
        1. Edit App policy with enable Disable Password policy and try toggle off with a password with length greater than the limit
        
        """
        result = conftest.ma_app_profile.edit_app_profile(zia_disable_password=conftest.MORE_THAN_LIMIT_PASSWORD, failure_expected=True)
        assert result[0], result[1]

        #* Set ZIA Disable Password to be equal to max allowed characters (Max Characters = 50) -----------------------------------------------------
        result = conftest.ma_app_profile.edit_app_profile(zia_disable_password=conftest.EQUAL_TO_LIMIT_PASSWORD, disable_reason=False)
        assert result[0], result[1]

        # Update policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        # Try to Disable ZIA with Password
        result = conftest.zcc.toggle_service(service='ZIA', action=False, password=conftest.EQUAL_TO_LIMIT_PASSWORD)
        assert result[0], result[1]

        # Enable ZIA
        result = conftest.zcc.toggle_service(service='ZIA', action=True)
        assert result[0], result[1]

    @pytest.mark.xray("QA-277653")
    @allure.title('Turn off ZIA using OTP')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_zia_with_otp(self):
        """
        setup steps following:
        1. Edit App policy with enable Disable Password policy and try toggle off with an OTP 
        
        """
        result = conftest.ma_app_profile.edit_app_profile(zia_disable_password=conftest.NORMAL_PASSWORD, disable_reason=False)
        assert result[0], result[1]

        # Update policy in ZCC
        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        # Export Logs 
        result=conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]

        # Fetch for Enrolled Device Details to get ZIA disable OTP
        result = conftest.ma_device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[1]

        zia_disable_otp = result[2]['service_password']
        
        # Try to Disable ZIA with OTP
        result = conftest.zcc.toggle_service(service='ZIA', action=False, password=zia_disable_otp )
        assert result[0], result[1]

        # Enable ZIA
        result = conftest.zcc.toggle_service(service='ZIA', action=True)
        assert result[0], result[1]

    @allure.title('Teardown') 
    def teardown_class(self):
        """
        1. Delete App profile
        2. Delete forwarding profile
        3. Logout ZCC
        """

        conftest.ma_app_profile.delete_app_profile() 

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()

        conftest.zcc.logout()
       





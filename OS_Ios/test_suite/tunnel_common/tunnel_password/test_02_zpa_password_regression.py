import allure, pytest, time
from common_lib.Custom_Markers import add_markers,zcc_mark_version #Required!
# from OS_Ios.test_suite.zcc_misc.zcc_password_regression import conftest
from OS_Ios.test_suite.ktunnel_common.ktunnel_password import conftest

"""
This class contains testcases for ZPA disable Password Regression 
"""

class TestZpaPasswordRegression:
    @allure.title('Setup')
    def setup_class(self):
        '''
        Setup Class Steps:
            1. Delete existing App profile
            2. Delete existing Forwarding profile
            3. Create fresh Forwarding profile
            4. Create fresh App profile
            5. Enable ZPA from MA service entitlement to facilitate ZIA+ZPA Login
            6. Login using ZIA+ZPA credentials
        '''
        conftest.ma_app_profile.delete_app_profile()

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()

        result = conftest.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        result=conftest.ma_service_entitlement.toggle_zpa(action=True)
        assert result[0], result[1]

        result=conftest.ma_notifications.configure_aup()
        assert result[0], result[1]

        result=conftest.saml_zia_obj.saml_action_smui(mode='disable')
        assert result[0], result[1]

        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]

        result = conftest.zcc.login(
            aup=False,
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zpa_user=conftest.config["ZPA_USER_ID"],
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in(check_alerts_after_login=True, dynamic_wait_search_element=40)
        assert result[0], result[1]

    @pytest.mark.xray("QA-277638")
    @allure.title('Validate ZPA cannot be disabled if disable reason is not given when disable reason is enabled in app profile')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_zpa_without_reason(self):
        '''
        Steps:
            1. Enable Disable_Reason in the App profile
            2. Update Policy in ZCC
            3. Disable ZPA without reason
        '''
        result = conftest.ma_app_profile.edit_app_profile(disable_reason=True)
        assert result[0], result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result = conftest.zcc.toggle_service(service='ZPA', action=False, failure_expected=True)
        assert result[0], result[1]
    
    @pytest.mark.xray("QA-277641")
    @allure.title('Disable ZPA with disable reason enabled')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_zpa_with_reason(self):
        '''
        Steps:
            1. Enable Disable_Reason in the App profile
            2. Update Policy in ZCC
            3. Disable ZPA with reason
            4. Enable ZPA for further test cases
        '''
        result = conftest.ma_app_profile.edit_app_profile(disable_reason=True)
        assert result[0], result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result = conftest.zcc.toggle_service(service='ZPA', action=False, disable_reason=conftest.REASON)
        assert result[0], result[1]

        result = conftest.zcc.toggle_service(service='ZPA', action=True)
        assert result[0], result[1]

    @pytest.mark.xray("QA-277637")
    @allure.title('Validate ZPA cannot be disabled if disable reason & password is not given when disable reason & password is enabled in app profile')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_zpa_without_reason_password(self):
        '''
        Steps:
            1. Enable ZPA Disable_Password and Disable_Reason in the App profile
            2. Update Policy in ZCC
            3. Disable ZPA without both reason and password
            4. Disable ZPA without reason
            5. Disable ZPA without password
        '''
        result = conftest.ma_app_profile.edit_app_profile(zpa_disable_password=conftest.NORMAL_PASSWORD, disable_reason=True)
        assert result[0], result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result = conftest.zcc.toggle_service(service='ZPA', action=False, failure_expected=True)
        assert result[0], result[1]

        result = conftest.zcc.toggle_service(service='ZPA', action=False, password=conftest.WRONG_PASSWORD, failure_expected=True)
        assert result[0], result[1]

        result = conftest.zcc.toggle_service(service='ZPA', action=False, disable_reason=conftest.REASON, failure_expected=True)
        assert result[0], result[1]


    @pytest.mark.xray("QA-277640")
    @allure.title('Disable ZPA with password & disable reason enabled')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_zpa_with_reason_password(self):
        '''
        Steps:
            1. Enable Disable_Password and Disable_Reason in the App profile
            2. Update Policy in ZCC
            3. Disable ZPA with Password and Reason
            4. Enable ZPA for further test cases
        '''
        result = conftest.ma_app_profile.edit_app_profile(zpa_disable_password=conftest.NORMAL_PASSWORD, disable_reason=True)
        assert result[0], result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result = conftest.zcc.toggle_service(service='ZPA', action=False, disable_reason=conftest.REASON, password=conftest.NORMAL_PASSWORD)
        assert result[0], result[1]

        result = conftest.zcc.toggle_service(service='ZPA', action=True)
        assert result[0], result[1]


    @pytest.mark.xray("QA-277635")
    @allure.title('Validate that wrong password is not accepted to disable ZPA')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_zpa_with_wrong_password(self):
        '''
        Steps:
            1. Enable Disable_Password in the App profile
            2. Update Policy in ZCC
            3. Disable ZPA with Wrong Password
        '''
        result = conftest.ma_app_profile.edit_app_profile(zpa_disable_password=conftest.NORMAL_PASSWORD, disable_reason=False)
        assert result[0], result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result = conftest.zcc.toggle_service(service='ZPA', action=False, password=conftest.WRONG_PASSWORD, failure_expected=True)
        assert result[0], result[1]


    @pytest.mark.xray("QA-277639")
    @allure.title('Validate ZPA cannot be disabled if password is not given when password is set in app profile')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_zpa_without_password(self):
        '''
        Steps:
            1. Enable Disable_Password in the App profile
            2. Update Policy in ZCC
            3. Disable ZPA without Password
        '''
        result = conftest.ma_app_profile.edit_app_profile(zpa_disable_password=conftest.NORMAL_PASSWORD, disable_reason=False)
        assert result[0], result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result = conftest.zcc.toggle_service(service='ZPA', action=False, failure_expected=True)
        assert result[0], result[1]


    @pytest.mark.xray("QA-277642")
    @allure.title('Disable ZPA with password enabled')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_zpa_with_normal_password(self):
        '''
        Steps:
            1. Enable Disable_Password in the App profile
            2. Update Policy in ZCC
            3. Disable ZPA with Correct Password
            4. Enable ZPA for further test cases
        '''
        result = conftest.ma_app_profile.edit_app_profile(zpa_disable_password=conftest.NORMAL_PASSWORD, disable_reason=False)
        assert result[0], result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result = conftest.zcc.toggle_service(service='ZPA', action=False, password=conftest.NORMAL_PASSWORD)
        assert result[0], result[1]

        result = conftest.zcc.toggle_service(service='ZPA', action=True)
        assert result[0], result[1]


    @pytest.mark.xray("QA-277636")
    @allure.title('Validate that special characters are accepted to disable ZPA')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_zpa_with_special_password(self):
        '''
        Steps:
            1. Enable Disable_Password and set it to Special_Password in the App profile
            2. Update Policy in ZCC
            3. Disable ZPA with Special_Password
            4. Enable ZPA for further test cases
        '''
        result = conftest.ma_app_profile.edit_app_profile(zpa_disable_password=conftest.SPECIAL_PASSWORD, disable_reason=False)
        assert result[0], result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result = conftest.zcc.toggle_service(service='ZPA', action=False, password=conftest.SPECIAL_PASSWORD)
        assert result[0], result[1]

        result = conftest.zcc.toggle_service(service='ZPA', action=True)
        assert result[0], result[1]


    @pytest.mark.xray("QA-277634")
    @allure.title('Validate if more than maximum number of allowed characters can be set as disable password for ZPA')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_zpa_password_longer_than_allowed(self):
        '''
        Steps:
            1. Enable Disable_Password and set it to a Password greater than the maximum limit i.e. 50 chars in the App profile
            2. Enable Disable_Password and set it to a Password equal to the maximum limit i.e. 50 chars in the App profile
            3. Update Policy in ZCC
            4. Disable ZPA with Password
            5. Enable ZPA for further test cases
        '''
        result = conftest.ma_app_profile.edit_app_profile(zpa_disable_password=conftest.MORE_THAN_LIMIT_PASSWORD, failure_expected=True)
        assert result[0], result[1]

        result = conftest.ma_app_profile.edit_app_profile(zpa_disable_password=conftest.EQUAL_TO_LIMIT_PASSWORD, disable_reason=False)
        assert result[0], result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result = conftest.zcc.toggle_service(service='ZPA', action=False, password=conftest.EQUAL_TO_LIMIT_PASSWORD)
        assert result[0], result[1]

        result = conftest.zcc.toggle_service(service='ZPA', action=True)
        assert result[0], result[1]
  

    @pytest.mark.xray("QA-277643")
    @allure.title('Disable ZPA with OTP')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_disable_zpa_with_otp(self):
        '''
        Steps:
            1. Enable Disable_Password in the App profile
            2. Update Policy in ZCC
            3. Fetch ZPA Disable OTP from MA
            4. Disable ZPA using the OTP
            5. Enable ZPA for further test cases
        '''
        result = conftest.ma_app_profile.edit_app_profile(zpa_disable_password=conftest.NORMAL_PASSWORD, disable_reason=False)
        assert result[0], result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        # Export Logs 
        result=conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]

        result = conftest.ma_device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[1]

        zpa_disable_otp = result[2]['service_password']
        
        result = conftest.zcc.toggle_service(service='ZPA', action=False, password=zpa_disable_otp)
        assert result[0], result[1]

        result = conftest.zcc.toggle_service(service='ZPA', action=True)
        assert result[0], result[1]


    @allure.title('Teardown') 
    def teardown_class(self):
        '''
        Teardown Class Steps:
            1. Delete existing App profile
            2. Delete existing Forwarding profile
            3. Logout from ZCC
        '''
        conftest.ma_app_profile.delete_app_profile()
        

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()
        

        conftest.zcc.logout()
        
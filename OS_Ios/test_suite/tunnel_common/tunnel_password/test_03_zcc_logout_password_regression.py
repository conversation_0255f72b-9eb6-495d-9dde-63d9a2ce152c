import allure
import pytest
from common_lib.Custom_Markers import add_markers,zcc_mark_version #Required!
# from OS_Ios.test_suite.zcc_misc.zcc_password_regression import conftest
from OS_Ios.test_suite.ktunnel_common.ktunnel_password import conftest
import time

"""
This class contains testcases for ZCC Logout Password Regression 
"""

class TestZccLogoutPasswordRegression:
    @allure.title('Setup Class')
    def setup_class(self):
        """
        1. Delete App policy
        2. Delete forwarding profile
        3. Create forwarding profile
        4. Create App profile
        4. Login ZCC
        """
        conftest.ma_app_profile.delete_app_profile()

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()

        result = conftest.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        result=conftest.ma_service_entitlement.toggle_zpa(action=True)
        assert result[0], result[1]

        result=conftest.ma_notifications.configure_aup()
        assert result[0], result[1]

        result=conftest.saml_zia_obj.saml_action_smui(mode='disable')
        assert result[0], result[1]

        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]

        result = conftest.zcc.login(
            aup=False,
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zpa_user=conftest.config["ZPA_USER_ID"],
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in(check_alerts_after_login=True, dynamic_wait_search_element=40)
        assert result[0], result[1]

    @allure.title('Setup Method')
    def setup_method(self):
        """
        setup steps following:
        1. Edit App policy with enable Logout Password policy 
        
        """
        result=conftest.ma_app_profile.edit_app_profile(logout_password=conftest.NORMAL_PASSWORD)
        assert result[0], result[1]



    @pytest.mark.xray("QA-277654")
    @allure.title('Validate ZCC doesnt logout if no password is specified in ZCC but is specified in app profile')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_logout_zcc_without_password(self):
        """
        setup steps following:
        1. Edit App policy with enable Logout Password policy and try logout without a password
        
        """
        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result=conftest.zcc.logout(failure_expected=True)
        assert result[0],result[1]


    @pytest.mark.xray("QA-277655")
    @allure.title('Validate ZCC doesnt logout with wrong logout password')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_logout_zcc_with_wrong_password(self):
        """
        setup steps following:
        1. Edit App policy with enable Logout Password policy and try logout with wrong password
        
        """
        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result=conftest.zcc.logout(password=conftest.WRONG_PASSWORD, failure_expected=True)
        assert result[0],result[1]


    @pytest.mark.xray("QA-277522")
    @allure.title('Validate logout with logout password')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_logout_zcc_with_correct_password(self):
        """
        setup steps following:
        1. Edit App policy with enable Logout Password policy and try logout with correct password
        
        """
        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result=conftest.zcc.logout(password=conftest.NORMAL_PASSWORD)
        assert result[0],result[1]

    @pytest.mark.xray("QA-278563")
    @allure.title('Validate logout with logout password')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_logout_zcc_with_otp(self):
        """
        setup steps following:
        1. Edit App policy with enable Logout Password policy and try logout with otp
        
        """
        result = conftest.zcc.restart_zcc(sleep_time=5)
        assert result[0], result[1]

        if not conftest.zcc.validate_zcc_logged_in(check_alerts_after_login=False, dynamic_wait_search_element=10)[0]:
            result = conftest.zcc.login(
                aup=False,
                zia_user=conftest.config["ZIA_USER_ID"],
                zia_password=conftest.config["ZIA_USER_PASSWORD"],
                zpa_user=conftest.config["ZPA_USER_ID"],
                zpa_password=conftest.config["ZPA_USER_PASSWORD"],
            )
            assert result[0], result[1]

            result=conftest.zcc.validate_zcc_logged_in(check_alerts_after_login=True, dynamic_wait_search_element=40)
            assert result[0], result[1]
        
        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        # Export Logs 
        result=conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]

        result = conftest.ma_device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[1]

        logout_otp = result[2]['service_password']

        result=conftest.zcc.logout(password=logout_otp)
        assert result[0],result[1]


    @allure.title('Teardown Class') 
    def teardown_class(self):
        """
        1. Delete App profile
        2. Delete forwarding profile
        """
        conftest.ma_app_profile.delete_app_profile(operating_system="Mac")
        

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()
        

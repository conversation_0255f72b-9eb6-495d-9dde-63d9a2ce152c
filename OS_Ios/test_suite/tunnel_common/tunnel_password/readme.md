# ZCC Password Regression

This module contains various testcases to check the functionality of Service enablement/disablement and ZCC Logout using passwords/OTPs in different scenarios.

#### Version Info

ZCC 3.7+

#### This Suite includes testcases 
    * Toggle ZIA
    * Toggle ZPA
    * Logout ZCC


#### Prerequisites
    * Export logs via airdrop requires both mac and iOS device use same apple ID in order to accept files automatically. Make sure to keep airdrop up and working.
    * Pass "MAC_HOSTNAME" such as "<PERSON><PERSON><PERSON><PERSON>'s Macbook Pro" as it is required for airdrop to work as expected
    * ZCC build installed and in Logged Out state.
    * A tenant account with ZIA, ZPA and MA configurations, filled in config.json file.
    * Quick Setup Guide: https://confluence.corp.zscaler.com/display/~priyanshu.sharma/iOS+Automation+Setup
    * In config file give following :
    --> "IDP_NAME" : "Okta" #(idp name configured on ZIA portal)
    --> "MAC_HOSTNAME" : "<PERSON>riyans<PERSON>'s Macbook Pro" #mac hostname


### Executing program

* Run terminal in Admin mode
* Command:

```
python3 trigger_automation.py --testcases OS_Ios/test_suite/zcc_misc/zcc_password_regression/{{testcase}} --zccVersion 3.7  --config yourconfig.json
```


### Author

Priyanshu Sharma      (<EMAIL>)

from common_lib.adminzpa import accesszpaapps,zpapolicy
from common_lib.common.constants import AppProfilePasswords, ZccUi
from common_lib.adminzia.samlzia import SamlZia
from common_lib.mobileadmin.platformsettings import PlatformSettings
from common_lib.mobileadmin import appprofile,serviceentitlement,deviceoverview
from common_lib.mobileadmin import clientconnectornotifications,clientconnectorsupport
from OS_Ios.library.ui_zcc import Zcc
import os,json
from common_lib.common.logger import Logger
import pytest

try:  
    config_file = os.path.join(os.getcwd(), "config", os.environ["CONFIG_FILE"])
    config = {}
    with open(config_file) as json_file:
        config.update(json.load(json_file))

    cloud_name = config["CLOUD"]
    log = Logger.initialize_logger(log_file_name="ios password and disable reason regression",log_level="INFO")
    zcc = Zcc(log_handle=log, start_app=True)
    zpa_app_access = accesszpaapps.AccessZpaApps(cloud_name, config_file , log_handle=log)
    zpa_policy = zpapolicy.ZpaPolicy(cloud_name, config_file, log_handle=log)
    ma_app_profile = appprofile.AppProfile(cloud_name, config_file, log_handle=log)
    ma_service_entitlement = serviceentitlement.ServiceEntitlement(cloud_name, config_file, log_handle=log)
    ma_notifications = clientconnectornotifications.ClientConnectorNotifications(cloud_name, config_file, log_handle=log)
    ma_support = clientconnectorsupport.ClientConnectorSupport(cloud_name, config_file, log_handle=log)
    ma_device_overview = deviceoverview.DeviceOverview(cloud_name, config_file, log_handle=log)
    platform_settings_obj = PlatformSettings(cloud=config['CLOUD'], config=config_file, log_handle=log)
    saml_zia_obj = SamlZia(cloud=config['CLOUD'], config=config_file, log_handle=log, debug=True)
    NORMAL_PASSWORD = AppProfilePasswords.ZIA_DISABLE_PASSWORD
    WRONG_PASSWORD = AppProfilePasswords.ZIA_DISABLE_PASSWORD+'A'
    SPECIAL_PASSWORD = AppProfilePasswords.SPECIAL_PASSWORD
    MORE_THAN_LIMIT_PASSWORD = AppProfilePasswords.MORE_THAN_LIMIT_PASSWORD
    EQUAL_TO_LIMIT_PASSWORD = AppProfilePasswords.EQUAL_TO_LIMIT_PASSWORD
    REASON = ZccUi.DISABLE_REASON
except Exception as e:
    pytest.skip(f"Skipping as Error :: {e}")


import pytest, time, allure
from common_lib.Custom_Markers import add_markers,zcc_mark_version
# from OS_Ios.test_suite.zcc_misc.zcc_trusted_network import conftest
from OS_Ios.test_suite.tunnel_common.tunnel_trusted_network.in_forwarding_profile_trusted_networks import conftest
import time

class TestPreDefinedTrustedNetworksSelectAny:

    def setup_class(self):
        """
        Setup method for the test class.

        This method performs the following setup steps:
        1. Fetches all resolved IPs for the specified application.
        2. Deletes any existing trusted networks.
        3. Creates two new trusted networks: one for the hostname IP and one for the DNS server.
        4. Creates a new access policy with the two trusted networks.
        5. Logs in to ZCC with the provided credentials and settings.
        6. Validates that <PERSON><PERSON> is logged in.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the setup steps fail.
        """
        result = conftest.sys_ops_obj.fetch_all_resolved_ips(application=conftest.HOSTNAME)
        assert result[0], result[1]

        resolved_ip_for_host = result[-1]

        conftest.sanity_zcc.ma_trusted_network.delete_trusted_network(network_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME + '_hostname_ip')
        conftest.sanity_zcc.ma_trusted_network.delete_trusted_network(network_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME + '_dns_server')

        result = conftest.sanity_zcc.ma_trusted_network.create_trusted_network(
            criteria_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME + '_hostname_ip',
            hostname=conftest.HOSTNAME,
            resolved_ips_for_hostname_list=resolved_ip_for_host
        )
        assert result[0], result[1]

        result = conftest.sanity_zcc.ma_trusted_network.create_trusted_network(
            criteria_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME + '_dns_server',
            dns_servers_list=conftest.DNS_SERVER
        )
        assert result[0], result[1]

        result = conftest.sanity_zcc.zpa_policy.create_access_policy(zpa_user_name=conftest.config["ZPA_ADMIN_ID"], zpa_password=conftest.config["ZPA_ADMIN_PASSWORD"], access_policy_name=conftest.ACCESS_POLICY_NAME, criteria="trustednetwork", trusted_networks=[conftest.TRUSTED_NETWORK_CRITERIA_NAME + '_hostname_ip', conftest.TRUSTED_NETWORK_CRITERIA_NAME + '_dns_server'], action_for_each_trusted_network=[True, True])
        assert result[0], result[1]

        # Login to ZCC with the provided credentials and settings
        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],  # ZIA user ID
            zia_password=conftest.config["ZIA_USER_PASSWORD"],  # ZIA user password
            zpa_user=conftest.config["ZPA_USER_ID"],  # ZPA user ID
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],  # ZPA user password
            zia_saml_login=True,  # Use ZIA SAML login
        )
        # Assert that the login was successful
        assert result[0], result[1]
        
        # Validate that ZCC is logged in
        result = conftest.zcc.validate_zcc_logged_in()
        # Assert that the validation was successful
        assert result[0], result[1]


    def teardown_class(self):
        """
        Teardown method for the test class.

        This method performs the following teardown steps:
        1. Deletes the access policy.
        2. Edits the forwarding profile.
        3. Deletes the two trusted networks.
        4. Logs out of ZCC.

        Args:
            None

        Returns:
            None

        Raises:
            None
        """
        conftest.sanity_zcc.zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=conftest.ACCESS_POLICY_NAME)
        conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile()
        conftest.sanity_zcc.ma_trusted_network.delete_trusted_network(network_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME + '_hostname_ip')
        conftest.sanity_zcc.ma_trusted_network.delete_trusted_network(network_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME + '_dns_server')
        conftest.zcc.logout()

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 1 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288044')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_1_to_tunnel_2_tls(self):
        """
        Validates predefined trusted network criteria with 'Match Any Condition Selected' 
        when OffTrusted is set to 'tunnel_1' and OnTrusted is set to 'tunnel_2_tls'.

        This test case checks the functionality of predefined trusted network with 
        'Match Any Condition Selected' and access to ZPA app disabled.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_tls', select_any_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 2 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288045')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_1_to_tunnel_2_dtls(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_dtls', select_any_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 3 - OffTrusted: Tunnel 1, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288046')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_1_to_twlp(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='twlp', select_any_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 4 - OffTrusted: Tunnel 1, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288047')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_1_to_enforce_proxy(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='enforce_proxy', select_any_tn=True, access_zpa_app=True)

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 5 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288048')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_tls_to_tunnel_1(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_1', select_any_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 6 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288049')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_tls_to_tunnel_2_dtls(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_2_dtls', select_any_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 7 - OffTrusted: Tunnel 2 TLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288050')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_tls_to_twlp(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='twlp', select_any_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 8 - OffTrusted: Tunnel 2 TLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288051')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_tls_to_enforce_proxy(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='enforce_proxy', select_any_tn=True, access_zpa_app=True)

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 9 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288052')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_dtls_to_tunnel_1(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_1', select_any_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 10 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288053')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_dtls_to_tunnel_2_tls(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_2_tls', select_any_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 11 - OffTrusted: Tunnel 2 DTLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288054')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_dtls_to_twlp(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='twlp', select_any_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 12 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288055')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_dtls_to_enforce_proxy(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='enforce_proxy', select_any_tn=True, access_zpa_app=True)

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 13 - OffTrusted: TWLP, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288056')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_twlp_to_tunnel_1(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_1', select_any_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 14 - OffTrusted: TWLP, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288057')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_twlp_to_tunnel_2_tls(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_tls', select_any_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 15 - OffTrusted: TWLP, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288058')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_twlp_to_tunnel_2_dtls(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_dtls', select_any_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 16 - OffTrusted: TWLP, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288059')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_twlp_to_enforce_proxy(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='enforce_proxy', select_any_tn=True, access_zpa_app=True)

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 17 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288060')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_enforce_proxy_to_tunnel_1(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_1', select_any_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 18 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288061')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_enforce_proxy_to_tunnel_2_tls(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_tls', select_any_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 19 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288062')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_enforce_proxy_to_tunnel_2_dtls(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_dtls', select_any_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match Any Condition Selected - Case 20 - OffTrusted: Enforce Proxy, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288063')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_enforce_proxy_to_twlp(self):
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='twlp', select_any_tn=True, access_zpa_app=True)

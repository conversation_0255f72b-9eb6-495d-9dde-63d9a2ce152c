import pytest, time, allure
from common_lib.Custom_Markers import add_markers,zcc_mark_version
# from OS_Ios.test_suite.zcc_misc.zcc_trusted_network import conftest
from OS_Ios.test_suite.tunnel_common.tunnel_trusted_network.predefined_trusted_network import conftest
import time

class TestPreDefinedTrustedNetworksHostnameIp:

    def setup_class(self):
        """
        Setup method for the test class.

        This method performs the following setup steps:
        1. Fetches all resolved IPs for the specified application.
        2. Deletes any existing access policy and trusted network.
        3. Creates a new trusted network with the resolved IPs.
        4. Creates a new access policy with the trusted network.
        5. Logs in to ZCC with the provided credentials and settings.
        6. Validates that <PERSON><PERSON> is logged in.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the setup steps fail.
        """
        result = conftest.sys_ops_obj.fetch_all_resolved_ips(application=conftest.HOSTNAME)
        assert result[0], result[1]

        resolved_ip_for_host = result[-1]

        conftest.sanity_zcc.zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=conftest.ACCESS_POLICY_NAME)
        conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile()
        conftest.sanity_zcc.ma_trusted_network.delete_trusted_network(network_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME)

        result = conftest.sanity_zcc.ma_trusted_network.create_trusted_network(
            criteria_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME,
            hostname=conftest.HOSTNAME,
            resolved_ips_for_hostname_list=resolved_ip_for_host
        )
        assert result[0], result[1]

        result = conftest.sanity_zcc.zpa_policy.create_access_policy(zpa_user_name=conftest.config["ZPA_ADMIN_ID"], zpa_password=conftest.config["ZPA_ADMIN_PASSWORD"], access_policy_name=conftest.ACCESS_POLICY_NAME, criteria="trustednetwork", trusted_networks=[conftest.TRUSTED_NETWORK_CRITERIA_NAME], action_for_each_trusted_network=[True])
        assert result[0], result[1]

        # result = conftest.sanity_zcc.zpa_policy.edit_access_policy(
        #     access_policy_name=conftest.ACCESS_POLICY_NAME,
        #     criteria="trustednetwork",
        #     trusted_networks=[conftest.TRUSTED_NETWORK_CRITERIA_NAME],
        #     action_for_each_trusted_network=[True]
        # )
        # assert result[0], result[1]

        # Login to ZCC with the provided credentials and settings
        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],  # ZIA user ID
            zia_password=conftest.config["ZIA_USER_PASSWORD"],  # ZIA user password
            zpa_user=conftest.config["ZPA_USER_ID"],  # ZPA user ID
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],  # ZPA user password
            zia_saml_login=True,  # Use ZIA SAML login
        )
        # Assert that the login was successful
        assert result[0], result[1]
        
        # Validate that ZCC is logged in
        result = conftest.zcc.validate_zcc_logged_in()
        # Assert that the validation was successful
        assert result[0], result[1]

    def teardown_class(self):
        """
        Teardown method for the test class.

        This method performs the following teardown steps:
        1. Deletes the access policy.
        2. Edits the forwarding profile.
        3. Deletes the trusted network.
        4. Logs out of ZCC.

        Args:
            None

        Returns:
            None

        Raises:
            None
        """
        conftest.sanity_zcc.zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=conftest.ACCESS_POLICY_NAME)
        conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile()
        conftest.sanity_zcc.ma_trusted_network.delete_trusted_network(network_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME)
        conftest.zcc.logout()

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 1 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288004')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_1_to_tunnel_2_tls(self):
        """
        Validates predefined trusted network criteria for hostname and IP address.
        This test case covers the scenario where the off-trusted tunnel mode is set to 'tunnel_1' and the on-trusted tunnel mode is set to 'tunnel_2_tls'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 2 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288005')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_1_to_tunnel_2_dtls(self):
        """
        Validates predefined trusted network criteria for hostname and IP.
        This test case validates the predefined trusted network criteria for hostname and IP
        with off-trusted tunnel mode set to 'tunnel_1' and on-trusted tunnel mode set to 'tunnel_2_dtls'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 3 - OffTrusted: Tunnel 1, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288006')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_1_to_twlp(self) -> None:
        """
        Validates predefined trusted network criteria for Hostname & IP with OffTrusted: Tunnel 1 and OnTrusted: TWLP.
        This test case checks the functionality of predefined trusted network with specific tunnel modes.
        """
        # Validate predefined trusted network with specified tunnel modes
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='twlp')
    

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 4 - OffTrusted: Tunnel 1, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288007')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_1_to_enforce_proxy(self) -> None:
        """
        Validates predefined trusted network criteria for Hostname & IP with OffTrusted mode set to 'tunnel_1' and OnTrusted mode set to 'enforce_proxy'.
    
        This test case checks the functionality of predefined trusted network with specific tunnel modes.
        """
        # Validate predefined trusted network with specified tunnel modes
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='enforce_proxy')
    

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 5 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288008')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_tls_to_tunnel_1(self) -> None:
        """
        Validates predefined trusted network criteria for hostname and IP address.
        
        This test case covers the scenario where the off-trusted tunnel mode is set to 'tunnel_2_tls' and the on-trusted tunnel mode is set to 'tunnel_1'.
        """
        # Validate predefined trusted network criteria with specified tunnel modes
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_1')
    

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 6 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288009')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_tls_to_tunnel_2_dtls(self):
        """
        Validates predefined trusted network criteria for hostname and IP.
        This test case validates the predefined trusted network criteria for hostname and IP
        with off-trusted tunnel mode as 'tunnel_2_tls' and on-trusted tunnel mode as 'tunnel_2_dtls'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 7 - OffTrusted: Tunnel 2 TLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288010')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_tls_to_twlp(self):
        """
        Validates predefined trusted network criteria for Hostname & IP with OffTrusted tunnel mode as 'tunnel_2_tls' and OnTrusted tunnel mode as 'twlp'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 8 - OffTrusted: Tunnel 2 TLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288011')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_tls_to_enforce_proxy(self):
        """
        Validates predefined trusted network criteria for Hostname & IP.
        This test case validates the predefined trusted network criteria for Hostname & IP
        with OffTrusted tunnel mode set to 'tunnel_2_tls' and OnTrusted tunnel mode set to 'enforce_proxy'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 9 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 1")
    @zcc_mark_version(min=3.7)
    @add_markers("regression","sanity")
    @pytest.mark.xray('QA-288012')
    def test_hostname_ip_tunnel_2_dtls_to_tunnel_1(self) -> None:
        """
        Validates predefined trusted network criteria for hostname and IP address.
        This test case covers the scenario where the off-trusted tunnel mode is set to 'tunnel_2_dtls' and the on-trusted tunnel mode is set to 'tunnel_1'.
        """
        # Validate predefined trusted network with specified tunnel modes
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_1')
    

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 10 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288013')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_dtls_to_tunnel_2_tls(self):
        """
        Validates predefined trusted network criteria for hostname and IP.
        This test case validates the predefined trusted network criteria for hostname and IP
        with off-trusted tunnel mode as Tunnel 2 DTLS and on-trusted tunnel mode as Tunnel 2 TLS.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 11 - OffTrusted: Tunnel 2 DTLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288014')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_dtls_to_twlp(self) -> None:
        """
        Validates predefined trusted network criteria for hostname and IP with off-trusted tunnel mode as 'tunnel_2_dtls' and on-trusted tunnel mode as 'twlp'.
        This test case is marked for regression and sanity testing, and it requires ZCC version 3.7 or higher.
        """
        # Validate predefined trusted network with specified tunnel modes
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='twlp')
    

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 12 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288015')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_dtls_to_enforce_proxy(self):
        """
        Validates predefined trusted network criteria for Hostname & IP.
    
        This test case validates the predefined trusted network criteria for Hostname & IP
        with OffTrusted tunnel mode set to 'tunnel_2_dtls' and OnTrusted tunnel mode set to 'enforce_proxy'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 13 - OffTrusted: TWLP, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288016')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_twlp_to_tunnel_1(self) -> None:
        """
        Validates predefined trusted network criteria for hostname and IP address.
    
        This test case validates the predefined trusted network criteria by switching 
        between TWLP and Tunnel 1 modes.
    
        Args:
            None
    
        Returns:
            None
        """
        # Validate predefined trusted network with specified tunnel modes
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_1')
    

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 14 - OffTrusted: TWLP, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288017')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_twlp_to_tunnel_2_tls(self):
        
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 15 - OffTrusted: TWLP, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288018')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_twlp_to_tunnel_2_dtls(self) -> None:
        """
        Validates predefined trusted network criteria for hostname and IP with off-trusted tunnel mode as TWLP and on-trusted tunnel mode as Tunnel 2 DTLS.
    
        This test case is marked for regression and sanity testing, and it requires ZCC version 3.7 or higher.
    
        Args:
            None
    
        Returns:
            None
        """
        # Validate predefined trusted network with specified tunnel modes
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_dtls')
    

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 16 - OffTrusted: TWLP, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288019')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_twlp_to_enforce_proxy(self):
        """
        Validates predefined trusted network criteria with hostname and IP.
    
        This test case validates the predefined trusted network criteria with hostname and IP
        when OffTrusted tunnel mode is set to TWLP and OnTrusted tunnel mode is set to Enforce Proxy.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 17 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288020')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_enforce_proxy_to_tunnel_1(self) -> None:
        """
        Validates predefined trusted network criteria for hostname and IP addresses.
        
        This test case covers the scenario where the off-trusted tunnel mode is set to 'enforce_proxy' and the on-trusted tunnel mode is set to 'tunnel_1'.
        
        Parameters:
        None
        
        Returns:
        None
        """
        # Validate predefined trusted network with specified tunnel modes
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_1')
    

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 18 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288021')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_enforce_proxy_to_tunnel_2_tls(self) -> None:
        """
        Validates predefined trusted network criteria for Hostname & IP with OffTrusted mode set to Enforce Proxy and OnTrusted mode set to Tunnel 2 TLS.
    
        This test case ensures that the predefined trusted network criteria is correctly applied when switching between Enforce Proxy and Tunnel 2 TLS modes.
    
        Args:
            None
    
        Returns:
            None
        """
        # Validate predefined trusted network with specified tunnel modes
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_tls')
    

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 19 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288022')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_enforce_proxy_to_tunnel_2_dtls(self) -> None:
        """
        Validates predefined trusted network criteria for hostname and IP with off-trusted tunnel mode set to 'enforce_proxy' and on-trusted tunnel mode set to 'tunnel_2_dtls'.
    
        This test case is marked for regression and sanity testing, and is only executed if the ZCC version is 3.7 or higher.
    
        Args:
            None
    
        Returns:
            None
        """
        # Validate predefined trusted network with specified tunnel modes
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_dtls')
    

    @allure.title("Validate predefined trusted network criteria - Hostname & IP - Case 20 - OffTrusted: Enforce Proxy, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288023')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_enforce_proxy_to_twlp(self) -> None:
        """
        Validates predefined trusted network criteria for hostname and IP with off-trusted tunnel mode as 'enforce_proxy' and on-trusted tunnel mode as 'twlp'.
    
        This test case is marked for regression and sanity testing, and is only executed if the ZCC version is 3.7 or higher.
    
        Args:
            self: The test instance.
    
        Returns:
            None
        """
        # Validate predefined trusted network with specified tunnel modes
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='twlp')
    

import pytest, time, allure
from common_lib.Custom_Markers import add_markers,zcc_mark_version
# from OS_Ios.test_suite.zcc_misc.zcc_trusted_network import conftest
from OS_Ios.test_suite.tunnel_common.tunnel_trusted_network.in_forwarding_profile_trusted_networks import conftest
import time


class TestPreDefinedTrustedNetworksDnsServer:

    def setup_class(self):
        """
        Setup method for the test class.

        This method performs the following setup steps:
        1. Deletes any existing access policy.
        2. Edits the forwarding profile.
        3. Deletes any existing trusted network.
        4. Creates a new trusted network with the specified DNS servers.
        5. Creates a new access policy with the trusted network.
        6. Logs in to ZCC with the provided credentials and settings.
        7. Validates that <PERSON><PERSON> is logged in.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the setup steps fail.
        """
        conftest.sanity_zcc.zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=conftest.ACCESS_POLICY_NAME)
        conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile()
        conftest.sanity_zcc.ma_trusted_network.delete_trusted_network(network_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME)
        result = conftest.sanity_zcc.ma_trusted_network.create_trusted_network(
            criteria_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME,
            dns_servers_list=conftest.DNS_SERVER
        )
        assert result[0], result[1]

        result = conftest.sanity_zcc.zpa_policy.create_access_policy(zpa_user_name=conftest.config["ZPA_ADMIN_ID"], zpa_password=conftest.config["ZPA_ADMIN_PASSWORD"], access_policy_name=conftest.ACCESS_POLICY_NAME, criteria="trustednetwork", trusted_networks=[conftest.TRUSTED_NETWORK_CRITERIA_NAME], action_for_each_trusted_network=[True])
        assert result[0], result[1]

        # Login to ZCC with the provided credentials and settings
        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],  # ZIA user ID
            zia_password=conftest.config["ZIA_USER_PASSWORD"],  # ZIA user password
            zpa_user=conftest.config["ZPA_USER_ID"],  # ZPA user ID
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],  # ZPA user password
            zia_saml_login=True,  # Use ZIA SAML login
        )
        # Assert that the login was successful
        assert result[0], result[1]
        
        # Validate that ZCC is logged in
        result = conftest.zcc.validate_zcc_logged_in()
        # Assert that the validation was successful
        assert result[0], result[1]

    def teardown_class(self):
        """
        Teardown method for the test class.

        This method performs the following teardown steps:
        1. Deletes the access policy.
        2. Edits the forwarding profile.
        3. Deletes the trusted network.
        4. Logs out of ZCC.

        Args:
            None

        Returns:
            None

        Raises:
            None
        """
        conftest.sanity_zcc.zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=conftest.ACCESS_POLICY_NAME)
        conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile()
        conftest.sanity_zcc.ma_trusted_network.delete_trusted_network(network_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME)
        conftest.zcc.logout()

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 1 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-287984')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_1_to_tunnel_2_tls(self):
        """
        Validate predefined trusted network criteria for DNS Server - Case 01.
        This test case validates the predefined trusted network criteria by transitioning 
        from 'tunnel_1' to 'tunnel_2_tls' and verifying the ZPA application access.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 2 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-287985')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_1_to_tunnel_2_dtls(self):
        """
        Validate predefined trusted network criteria for DNS Server - Case 02.
        This test case validates the predefined trusted network criteria by testing tunnel transition, 
        updating the policy, and checking the ZPA application access based on trusted network criteria.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 3 - OffTrusted: Tunnel 1, OnTrusted: TWLP")
    @pytest.mark.xray('QA-287986')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_1_to_twlp(self):
        """
        Validate predefined trusted network criteria for DNS Server - Case 03.
        Test case to validate predefined trusted network criteria for DNS Server.
        This test case covers the scenario where OffTrusted tunnel mode is set to 'tunnel_1' and OnTrusted tunnel mode is set to 'twlp'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 4 - OffTrusted: Tunnel 1, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-287987')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_1_to_enforce_proxy(self):
        """
        Validate predefined trusted network criteria for DNS Server - Case 04.
        Test case to validate predefined trusted network criteria for DNS Server.
        This test case covers the scenario where OffTrusted tunnel mode is set to 'tunnel_1' and OnTrusted tunnel mode is set to 'enforce_proxy'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 5 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-287988')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_2_tls_to_tunnel_1(self):
        """        
        Validate predefined trusted network criteria for DNS Server - Case 05.
        This test case validates the predefined trusted network criteria by transitioning 
        from 'tunnel_2_tls' to 'tunnel_1' and verifying the ZPA application access.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 6 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-287989')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_2_tls_to_tunnel_2_dtls(self) -> None:
        """
        Validate predefined trusted network criteria for DNS Server - Case 06.
        Validates the predefined trusted network criteria for ZPA application access 
        by testing tunnel transition from Tunnel 2 TLS to Tunnel 2 DTLS.
        """
        # Validate predefined trusted network criteria with specified tunnel modes
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_2_dtls')
    

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 7 - OffTrusted: Tunnel 2 TLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-287990')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_2_tls_to_twlp(self):
        """
        Validate predefined trusted network criteria for DNS Server - Case 07.
        Validates the predefined trusted network criteria for DNS Server access by testing 
        tunnel transition, updating the policy, and checking the DNS Server access based 
        on trusted network criteria.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 8 - OffTrusted: Tunnel 2 TLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-287991')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_2_tls_to_enforce_proxy(self):
        """
        Validate predefined trusted network criteria for DNS Server - Case 08.
        Validates the predefined trusted network criteria for DNS Server access by testing tunnel transition, 
        updating the policy, and checking the DNS Server access based on trusted network criteria.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 9 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-287992')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_2_dtls_to_tunnel_1(self):
        """
        Validate predefined trusted network criteria for DNS Server - Case 09.
        Validates the predefined trusted network criteria for DNS Server by testing tunnel transition, 
        updating the policy, and checking the DNS Server access based on trusted network criteria.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 10 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-287993')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_2_dtls_to_tunnel_2_tls(self):
        """
        Validate predefined trusted network criteria for DNS Server - Case 10.
        This test case validates the predefined trusted network criteria by testing tunnel transition 
        from 'tunnel_2_dtls' to 'tunnel_2_tls' and checks the ZPA application access based on trusted network criteria.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 11 - OffTrusted: Tunnel 2 DTLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-287994')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_2_dtls_to_twlp(self):
        """
        Validate predefined trusted network criteria for DNS Server - Case 11.
        This test case covers the scenario where the off-trusted tunnel mode is set to 
        'tunnel_2_dtls' and the on-trusted tunnel mode is set to 'twlp'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 12 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-287995')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_2_dtls_to_enforce_proxy(self):
        """
        Validate predefined trusted network criteria for DNS Server - Case 12.
        Validates the predefined trusted network criteria for DNS Server by testing tunnel transition, 
        updating the policy, and checking the DNS Server access based on trusted network criteria.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 13 - OffTrusted: TWLP, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-287996')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_twlp_to_tunnel_1(self):
        """
        Validate predefined trusted network criteria for DNS Server - Case 13.
        Validates the predefined trusted network criteria for ZPA application access 
        by testing tunnel transition from TWLP to Tunnel 1, updating the policy, 
        and checking the ZPA application access based on trusted network criteria.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 14 - OffTrusted: TWLP, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-287997')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_twlp_to_tunnel_2_tls(self) -> None:
        """
        Validate predefined trusted network criteria for DNS Server - Case 14.
        Validates the predefined trusted network criteria for ZPA application access 
        by testing tunnel transition from TWLP to Tunnel 2 TLS.
        """
        # Validate predefined trusted network criteria with specified tunnel modes
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_tls')
    

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 15 - OffTrusted: TWLP, OnTrusted: Tunnle 2 DTLS")
    @pytest.mark.xray('QA-287998')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_twlp_to_tunnel_2_dtls(self):
        """
        Validate predefined trusted network criteria for DNS Server - Case 15.
        This test case validates the predefined trusted network criteria by transitioning 
        from TWLP to Tunnel 2 DTLS mode and verifying the ZPA application access.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 16 - OffTrusted: TWLP, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-287999')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_twlp_to_enforce_proxy(self):
        """
        Validate predefined trusted network criteria for DNS Server - Case 16.
        Validates the predefined trusted network criteria for DNS Server access by testing 
        tunnel transition, updating the policy, and checking the DNS Server access based 
        on trusted network criteria.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 17 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288000')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_enforce_proxy_to_tunnel_1(self):
        """
        Validate predefined trusted network criteria for DNS Server - Case 17.
        This test case validates the predefined trusted network criteria for ZPA application access 
        by testing tunnel transition, updating the policy, and checking the ZPA application access 
        based on trusted network criteria with OffTrusted mode set to 'enforce_proxy' and OnTrusted mode set to 'tunnel_1'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 18 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288001')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_enforce_proxy_to_tunnel_2_tls(self):
        """
        Validate predefined trusted network criteria for DNS Server - Case 18.
        Validates the predefined trusted network criteria for DNS Server access by testing 
        tunnel transition, updating the policy, and checking the DNS Server access based 
        on trusted network criteria.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 19 - OffTrusted: Enforce Proxy, OnTrusted: Tunnle 2 DTLS")
    @pytest.mark.xray('QA-288002')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_enforce_proxy_to_tunnel_2_dtls(self):
        """
        Validate predefined trusted network criteria for DNS Server - Case 19.
        This test case validates the predefined trusted network criteria by testing tunnel transition, 
        updating the policy, and checking the ZPA application access based on trusted network criteria.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate predefined trusted network criteria - DNS Server - Case 20 - OffTrusted: Enforce Proxy, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288003')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_enforce_proxy_to_twlp(self):
        """
        Validate predefined trusted network criteria for DNS Server - Case 20.
        
        This test case validates the predefined trusted network criteria for ZPA application access 
        by testing tunnel transition, updating the policy, and checking the ZPA application access 
        based on trusted network criteria with OffTrusted mode set to 'enforce_proxy' and OnTrusted mode set to 'twlp'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='twlp')

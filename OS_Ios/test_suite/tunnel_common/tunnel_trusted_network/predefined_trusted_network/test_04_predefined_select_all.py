import pytest, time, allure
from common_lib.Custom_Markers import add_markers,zcc_mark_version
# from OS_Ios.test_suite.zcc_misc.zcc_trusted_network import conftest
from OS_Ios.test_suite.tunnel_common.tunnel_trusted_network.in_forwarding_profile_trusted_networks import conftest
import time

class TestPreDefinedTrustedNetworksSelectAll:

    def setup_class(self):
        """
        Setup method for the test class.

        This method performs the following setup steps:
        1. Fetches all resolved IPs for the specified application.
        2. Deletes any existing trusted networks.
        3. Creates two new trusted networks: one for the hostname IP and one for the DNS server.
        4. Creates a new access policy with the two trusted networks.
        5. Logs in to ZCC with the provided credentials and settings.
        6. Validates that <PERSON><PERSON> is logged in.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the setup steps fail.
        """
        result = conftest.sys_ops_obj.fetch_all_resolved_ips(application=conftest.HOSTNAME)
        assert result[0], result[1]

        resolved_ip_for_host = result[-1]

        conftest.sanity_zcc.ma_trusted_network.delete_trusted_network(network_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME + '_hostname_ip')
        conftest.sanity_zcc.ma_trusted_network.delete_trusted_network(network_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME + '_dns_server')

        result = conftest.sanity_zcc.ma_trusted_network.create_trusted_network(
            criteria_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME + '_hostname_ip',
            hostname=conftest.HOSTNAME,
            resolved_ips_for_hostname_list=resolved_ip_for_host
        )
        assert result[0], result[1]

        result = conftest.sanity_zcc.ma_trusted_network.create_trusted_network(
            criteria_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME + '_dns_server',
            dns_servers_list=conftest.DNS_SERVER
        )
        assert result[0], result[1]

        result = conftest.sanity_zcc.zpa_policy.create_access_policy(zpa_user_name=conftest.config["ZPA_ADMIN_ID"], zpa_password=conftest.config["ZPA_ADMIN_PASSWORD"], access_policy_name=conftest.ACCESS_POLICY_NAME, criteria="trustednetwork", trusted_networks=[conftest.TRUSTED_NETWORK_CRITERIA_NAME + '_hostname_ip', conftest.TRUSTED_NETWORK_CRITERIA_NAME + '_dns_server'], action_for_each_trusted_network=[True, True])
        assert result[0], result[1]

        # Login to ZCC with the provided credentials and settings
        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],  # ZIA user ID
            zia_password=conftest.config["ZIA_USER_PASSWORD"],  # ZIA user password
            zpa_user=conftest.config["ZPA_USER_ID"],  # ZPA user ID
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],  # ZPA user password
            zia_saml_login=True,  # Use ZIA SAML login
        )
        # Assert that the login was successful
        assert result[0], result[1]
        
        # Validate that ZCC is logged in
        result = conftest.zcc.validate_zcc_logged_in()
        # Assert that the validation was successful
        assert result[0], result[1]

    def teardown_class(self):
        """
        Teardown method for the test class.

        This method performs the following teardown steps:
        1. Deletes the access policy.
        2. Edits the forwarding profile.
        3. Deletes the two trusted networks.
        4. Logs out of ZCC.

        Args:
            None

        Returns:
            None

        Raises:
            None
        """
        conftest.sanity_zcc.zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=conftest.ACCESS_POLICY_NAME)
        conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile()
        conftest.sanity_zcc.ma_trusted_network.delete_trusted_network(network_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME + '_hostname_ip')
        conftest.sanity_zcc.ma_trusted_network.delete_trusted_network(network_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME + '_dns_server')
        conftest.zcc.logout()

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 1 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288064')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_1_to_tunnel_2_tls(self):
        """
        This test case covers the scenario where 'OffTrusted' tunnel mode is set to 'Tunnel 1' and 'OnTrusted' tunnel mode is set to 'Tunnel 2 TLS'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_tls', select_all_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 2 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288065')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_1_to_tunnel_2_dtls(self):
        """
        This test case validates the predefined trusted network criteria by selecting Match All Condition
        and verifying the behavior with OffTrusted: Tunnel 1 and OnTrusted: Tunnel 2 DTLS.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_dtls', select_all_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 3 - OffTrusted: Tunnel 1, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288066')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_1_to_twlp(self) -> None:
        """
        This test case covers the scenario where 'OffTrusted' tunnel mode is set to 'tunnel_1' and 'OnTrusted' tunnel mode is set to 'twlp'.
        """
        # Validate predefined trusted network with specified tunnel modes and conditions
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='twlp', select_all_tn=True, access_zpa_app=True)
    

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 4 - OffTrusted: Tunnel 1, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288067')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_1_to_enforce_proxy(self) -> None:
        """
        This test case covers the scenario where 'OffTrusted' tunnel mode is set to 'Tunnel 1' and 'OnTrusted' tunnel mode is set to 'Enforce Proxy'.
        """
        # Validate predefined trusted network with specified tunnel modes and conditions
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='enforce_proxy', select_all_tn=True, access_zpa_app=True)
    

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 5 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288068')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_tls_to_tunnel_1(self):
        """
        This test case validates the predefined trusted network criteria by selecting 'Match All' condition,
        setting 'OffTrusted' tunnel mode to 'Tunnel 2 TLS', and 'OnTrusted' tunnel mode to 'Tunnel 1'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_1', select_all_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 6 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288069')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_tls_to_tunnel_2_dtls(self):
        """
        This test case validates the predefined trusted network criteria by selecting 'Match All' condition,
        setting 'OffTrusted' tunnel mode to 'tunnel_2_tls', and 'OnTrusted' tunnel mode to 'tunnel_2_dtls'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_2_dtls', select_all_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 7 - OffTrusted: Tunnel 2 TLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288070')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_tls_to_twlp(self):
        """
        This test case validates the predefined trusted network criteria by selecting 'Match All' condition,
        setting 'OffTrusted' tunnel mode to 'tunnel_2_tls', and 'OnTrusted' tunnel mode to 'twlp'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='twlp', select_all_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 8 - OffTrusted: Tunnel 2 TLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288071')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_tls_to_enforce_proxy(self):
        """
        This test case validates the predefined trusted network criteria by selecting 'Match All' condition,
        setting 'OffTrusted' tunnel mode to 'tunnel_2_tls', and 'OnTrusted' tunnel mode to 'enforce_proxy'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='enforce_proxy', select_all_tn=True, access_zpa_app=True)

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 9 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288072')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_dtls_to_tunnel_1(self):
        """
        This test case validates the predefined trusted network criteria by selecting 'Match All' condition,
        setting 'OffTrusted' tunnel mode to 'tunnel_2_dtls', and 'OnTrusted' tunnel mode to 'tunnel_1'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_1', select_all_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 10 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288073')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_dtls_to_tunnel_2_tls(self):
        """
        This test case validates the predefined trusted network criteria by selecting 'Match All' condition,
        setting OffTrusted tunnel mode to 'Tunnel 2 DTLS' and OnTrusted tunnel mode to 'tunnel_2_tls'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_2_tls', select_all_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 11 - OffTrusted: Tunnel 2 DTLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288074')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_dtls_to_twlp(self):
        """
        This test case validates the predefined trusted network criteria by selecting the match all condition.
        It uses the off-trusted tunnel mode as 'tunnel_2_dtls' and on-trusted tunnel mode as 'twlp'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='twlp', select_all_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 12 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288075')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_dtls_to_enforce_proxy(self):
        """
        This test case validates the predefined trusted network criteria by selecting 'Match All' condition,
        setting OffTrusted tunnel mode to 'Tunnel 2 DTLS' and OnTrusted tunnel mode to 'Enforce Proxy'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='enforce_proxy', select_all_tn=True, access_zpa_app=True)

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 13 - OffTrusted: TWLP, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288076')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_twlp_to_tunnel_1(self) -> None:
        """
        This test case covers the scenario where the 'Off Trusted' tunnel mode is set to 'TWLP' and the 'On Trusted' tunnel mode is set to 'Tunnel 1'.
        """
        # Validate predefined trusted network with specified tunnel modes and conditions
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_1', select_all_tn=True, access_zpa_app=True)
    

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 14 - OffTrusted: TWLP, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288077')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_twlp_to_tunnel_2_tls(self) -> None:
        """
        This test case covers the scenario where the off-trusted tunnel mode is set to 'TWLP' and the on-trusted tunnel mode is set to 'Tunnel 2 TLS'.
        """
        # Validate predefined trusted network with specified tunnel modes and conditions
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_tls', select_all_tn=True, access_zpa_app=True)
    

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 15 - OffTrusted: TWLP, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288078')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_twlp_to_tunnel_2_dtls(self) -> None:
        """
        This test case covers the scenario where the off-trusted tunnel mode is set to 'TWLP' and the on-trusted tunnel mode is set to 'Tunnel 2 DTLS'.
        """
        # Validate predefined trusted network with specified tunnel modes and 'Match All' condition
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_dtls', select_all_tn=True, access_zpa_app=True)
    

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 16 - OffTrusted: TWLP, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288079')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_twlp_to_enforce_proxy(self) -> None:
        """
        This test case covers the scenario where the off-trusted tunnel mode is set to 'TWLP' and the on-trusted tunnel mode is set to 'Enforce Proxy'.
        """
        # Validate predefined trusted network with specified tunnel modes and conditions
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='enforce_proxy', select_all_tn=True, access_zpa_app=True)
    

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 17 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288080')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_enforce_proxy_to_tunnel_1(self):
        """
        This test case covers the scenario where 'OffTrusted' tunnel mode is set to 'Enforce Proxy' and 'OnTrusted' tunnel mode is set to 'Tunnel 1'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_1', select_all_tn=True, access_zpa_app=True)

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 18 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288081')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_enforce_proxy_to_tunnel_2_tls(self) -> None:
        """
        This test case covers the scenario where OffTrusted tunnel mode is set to 'Enforce Proxy' and OnTrusted tunnel mode is set to 'Tunnel 2 TLS'.
        """
        # Validate predefined trusted network with specified tunnel modes and conditions
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_tls', select_all_tn=True, access_zpa_app=True)
    

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 19 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288082')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_enforce_proxy_to_tunnel_2_dtls(self) -> None:
        """
        This test case covers the scenario where OffTrusted tunnel mode is set to 'Enforce Proxy' and OnTrusted tunnel mode is set to 'Tunnel 2 DTLS'.
        """
        # Validate predefined trusted network with specified tunnel modes and conditions
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_dtls', select_all_tn=True, access_zpa_app=True)
    

    @allure.title("Validate predefined trusted network criteria - Match All Condition Selected - Case 20 - OffTrusted: Enforce Proxy, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288083')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_enforce_proxy_to_twlp(self) -> None:
        """
        This test case covers the scenario where OffTrusted tunnel mode is set to 'Enforce Proxy' and OnTrusted tunnel mode is set to 'TWLP'.
        """
        # Validate predefined trusted network with specified tunnel modes and conditions
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='twlp', select_all_tn=True, access_zpa_app=True)
    

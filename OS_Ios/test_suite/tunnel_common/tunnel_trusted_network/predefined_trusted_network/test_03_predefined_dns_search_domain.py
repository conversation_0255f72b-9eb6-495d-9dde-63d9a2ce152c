import pytest, time, allure
from common_lib.Custom_Markers import add_markers,zcc_mark_version
# from OS_Ios.test_suite.zcc_misc.zcc_trusted_network import conftest
from OS_Ios.test_suite.tunnel_common.tunnel_trusted_network.in_forwarding_profile_trusted_networks import conftest


class TestPreDefinedTrustedNetworksDnsSearchDomain:

    def setup_class(self):
        """
        Setup method for the test class.

        This method performs the following setup steps:
        1. Fetches the DNS search domains from the network.
        2. Skips the test if no DNS search domains are found.
        3. Deletes any existing access policy.
        4. Edits the forwarding profile.
        5. Deletes any existing trusted network.
        6. Creates a new trusted network with the DNS search domains.
        7. Creates a new access policy with the trusted network.
        8. Logs in to ZCC with the provided credentials and settings.
        9. Validates that ZCC is logged in.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the setup steps fail.
            pytest.Skip: If no DNS search domains are found on the network.
        """
        # res = conftest.sys_ops_obj.fetch_network_dns_search_domains()
        # assert res[0], res[1]

        # search_domains = res[-1]

        # if not search_domains:
        #Skipping DNS Search Domain Cases as it is not deployed in customer environment for iOS ---- will use once required
        pytest.skip(allow_module_level=True, reason="No DNS search domains found on the network. Skipping all the dependent tests.")

        conftest.sanity_zcc.zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=conftest.ACCESS_POLICY_NAME)
        conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile()
        conftest.sanity_zcc.ma_trusted_network.delete_trusted_network(network_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME)
        result = conftest.sanity_zcc.ma_trusted_network.create_trusted_network(
            criteria_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME,
            dns_search_domain_list=search_domains
        )
        assert result[0], result[1]

        result = conftest.sanity_zcc.zpa_policy.create_access_policy(zpa_user_name=conftest.config["ZPA_ADMIN_ID"], zpa_password=conftest.config["ZPA_ADMIN_PASSWORD"], access_policy_name=conftest.ACCESS_POLICY_NAME, criteria="trustednetwork", trusted_networks=[conftest.TRUSTED_NETWORK_CRITERIA_NAME], action_for_each_trusted_network=[True])
        assert result[0], result[1]

        # result = conftest.sanity_zcc.zpa_policy.edit_access_policy(
        #     access_policy_name=conftest.ACCESS_POLICY_NAME,
        #     criteria="trustednetwork",
        #     trusted_networks=[conftest.TRUSTED_NETWORK_CRITERIA_NAME],
        #     action_for_each_trusted_network=[True]
        # )
        # assert result[0], result[1]

        # Login to ZCC with the provided credentials and settings
        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],  # ZIA user ID
            zia_password=conftest.config["ZIA_USER_PASSWORD"],  # ZIA user password
            zpa_user=conftest.config["ZPA_USER_ID"],  # ZPA user ID
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],  # ZPA user password
            zia_saml_login=True,  # Use ZIA SAML login
        )
        # Assert that the login was successful
        assert result[0], result[1]
        
        # Validate that ZCC is logged in
        result = conftest.zcc.validate_zcc_logged_in()
        # Assert that the validation was successful
        assert result[0], result[1]


    def teardown_class(self):
        """
        Teardown method for the test class.

        This method performs the following teardown steps:
        1. Deletes the access policy.
        2. Edits the forwarding profile.
        3. Deletes the trusted network.
        4. Logs out of ZCC.

        Args:
            None

        Returns:
            None

        Raises:
            None
        """
        conftest.sanity_zcc.zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=conftest.ACCESS_POLICY_NAME)
        conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile()
        conftest.sanity_zcc.ma_trusted_network.delete_trusted_network(network_name=conftest.TRUSTED_NETWORK_CRITERIA_NAME)
        conftest.zcc.logout()

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 1 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288024')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_1_to_tunnel_2_tls(self):
        """
        Validates predefined trusted network criteria for DNS Search Domain with OffTrusted: Tunnel 1 and OnTrusted: Tunnel 2 TLS.
    
        This test case checks the functionality of predefined trusted network criteria with different tunnel modes.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 2 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288025')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_1_to_tunnel_2_dtls(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        by setting the OffTrusted tunnel mode to 'tunnel_1' and the OnTrusted tunnel mode to 'tunnel_2_dtls'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 3 - OffTrusted: Tunnel 1, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288026')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_1_to_twlp(self):
        """
        Test case to validate predefined trusted network criteria for DNS Search Domain.
        Case 3: OffTrusted: Tunnel 1, OnTrusted: TWLP.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 4 - OffTrusted: Tunnel 1, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288027')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_1_to_enforce_proxy(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        with OffTrusted tunnel mode set to 'tunnel_1' and OnTrusted tunnel mode set to 'enforce_proxy'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 5 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288028')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_2_tls_to_tunnel_1(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        with OffTrusted tunnel mode set to 'tunnel_2_tls' and OnTrusted tunnel mode set to 'tunnel_1'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 6 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288029')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_2_tls_to_tunnel_2_dtls(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        with OffTrusted tunnel mode set to 'tunnel_2_tls' and OnTrusted tunnel mode set to 'tunnel_2_dtls'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 7 - OffTrusted: Tunnel 2 TLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288030')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_2_tls_to_twlp(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        with OffTrusted tunnel mode set to 'tunnel_2_tls' and OnTrusted tunnel mode set to 'twlp'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 8 - OffTrusted: Tunnel 2 TLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288031')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_2_tls_to_enforce_proxy(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        with OffTrusted tunnel mode set to 'tunnel_2_tls' and OnTrusted tunnel mode set to 'enforce_proxy'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 9 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288032')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_2_dtls_to_tunnel_1(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        with OffTrusted tunnel mode set to 'tunnel_2_dtls' and OnTrusted tunnel mode set to 'tunnel_1'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 10 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288033')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_2_dtls_to_tunnel_2_tls(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        with OffTrusted tunnel mode set to 'tunnel_2_dtls' and OnTrusted tunnel mode set to 'tunnel_2_tls'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 11 - OffTrusted: Tunnel 2 DTLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288034')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_2_dtls_to_twlp(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        with OffTrusted tunnel mode set to 'tunnel_2_dtls' and OnTrusted tunnel mode set to 'twlp'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 12 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288035')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_tunnel_2_dtls_to_enforce_proxy(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        with OffTrusted tunnel mode set to 'tunnel_2_dtls' and OnTrusted tunnel mode set to 'enforce_proxy'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 13 - OffTrusted: TWLP, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288036')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_twlp_to_tunnel_1(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        with OffTrusted tunnel mode set to 'twlp' and OnTrusted tunnel mode set to 'tunnel_1'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 14 - OffTrusted: TWLP, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288037')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_twlp_to_tunnel_2_tls(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        with OffTrusted tunnel mode set to 'twlp' and OnTrusted tunnel mode set to 'tunnel_2_tls'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 15 - OffTrusted: TWLP, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288038')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_twlp_to_tunnel_2_dtls(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        with OffTrusted tunnel mode set to 'twlp' and OnTrusted tunnel mode set to 'tunnel_2_dtls'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 16 - OffTrusted: TWLP, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288039')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_twlp_to_enforce_proxy(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        with OffTrusted tunnel mode set to 'twlp' and OnTrusted tunnel mode set to 'enforce_proxy'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 17 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288040')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_enforce_proxy_to_tunnel_1(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        with OffTrusted tunnel mode set to 'enforce_proxy' and OnTrusted tunnel mode set to 'tunnel_1'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 18 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288041')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_enforce_proxy_to_tunnel_2_tls(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        with OffTrusted tunnel mode set to 'enforce_proxy' and OnTrusted tunnel mode set to 'tunnel_2_tls'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 19 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288042')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_enforce_proxy_to_tunnel_2_dtls(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        with OffTrusted tunnel mode set to 'enforce_proxy' and OnTrusted tunnel mode set to 'tunnel_2_dtls'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate predefined trusted network criteria - DNS Search Domain - Case 20 - OffTrusted: Enforce Proxy, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288043')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_server_enforce_proxy_to_twlp(self):
        """
        This test case validates the predefined trusted network criteria for DNS Search Domain
        with OffTrusted tunnel mode set to 'enforce_proxy' and OnTrusted tunnel mode set to 'twlp'.
        """
        conftest.validate_predefined_trusted_network(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='twlp')

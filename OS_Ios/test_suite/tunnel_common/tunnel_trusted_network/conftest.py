from common_lib.common import logger, constants
from common_lib.mobileadmin import appprofile, trustednetwork
from common_lib.adminzpa import zpapolicy, accesszpaapps, createzpaapps
from common_lib.adminzia import samlzia
from OS_Ios.library import ui_zcc, ops_system
from OS_Ios.module import zcc_sanity
import os, pytest, json, copy, time

config_file = os.path.join(os.getcwd(), "config", os.environ["CONFIG_FILE"])
config = {}
with open(config_file, 'r') as file:
    config.update(json.load(file))

CLOUD_NAME = config["CLOUD"]
PREDEFINED_TRUSTED_NETWORK_NAME = "predefined_trusted_network_regression"

http_server_ip = "************"
application = constants.ZccSanityConst.ZPA_APPS["DOMAIN"]
zpa_application = "http://"+constants.ZccSanityConst.ZPA_APPS["DOMAIN"]['app']
zpa_application_assertion_element = '//XCUIElementTypeStaticText[@name="SUCCESS, CONNECTED TO AUTOMATION SERVER"]'
trusted_network_logger = logger.Logger.initialize_logger(log_file_name="trusted_network_regression.log")
zpa_appaccess = accesszpaapps.AccessZpaApps(config=config_file, cloud=CLOUD_NAME)
sanity_zcc = zcc_sanity.ZccSanity(cloud=CLOUD_NAME, config=config_file)
create_zpa_apps = create_zpa_apps = createzpaapps.MakeZpaApplication(config["CLOUD"], config=config_file, log_handle=trusted_network_logger,file=[application['app']], server_ip=http_server_ip)
zcc = ui_zcc.Zcc(start_app=True)
sys_ops_obj = ops_system.SysOps(driver=zcc.ui.driver, logger=trusted_network_logger)
DNS_SERVER = sys_ops_obj.fetch_system_dns_server_ips()[-1]   # both mac and ios device should be on same network 
HOSTNAME = "example.com"
RESOLVED_IP_FOR_HOST = sys_ops_obj.fetch_all_resolved_ips(application=HOSTNAME)[-1]
# NETWORK_RANGE c zcc.sys_ops.fetch_network_interface_info()[-1]["ipv4_network_address"]
# DEFAULT_GATEWAY c zcc.sys_ops.fetch_default_gateway_and_interface()[-1]["ipv4_default_gateway"]
TRUSTED_NETWORK_CRITERIA_NAME = "predefined_tn"
ACCESS_POLICY_NAME = "trusted_network_allow"
BLOCK_ALL_ACCESS_POLICY_NAME = "trusted_network_block_all"

def validate_predefined_trusted_network(off_trusted_tunnel_mode: str, on_trusted_tunnel_mode: str, select_all_tn: bool = False, select_any_tn: bool = False, access_zpa_app: bool = True):
    """
    Validates the predefined trusted network criteria for ZPA application access.

    This function tests the tunnel transition based on the trusted network criteria.
    It sets the tunnel mode to the 'off_trusted_tunnel_mode', updates the policy, 
    and then validates that the tunnel is in the expected mode. It then accesses 
    the ZPA application and checks if it's blocked or not based on the trusted 
    network criteria. The function then updates the forwarding profile with the 
    predefined trusted network and tunnel mode, updates the policy again, and 
    validates the tunnel mode and ZPA application access.

    Args:
    - off_trusted_tunnel_mode (str): The tunnel mode when the trusted network criteria is not met.
    - on_trusted_tunnel_mode (str): The tunnel mode when the trusted network criteria is met.
    - select_all_tn (bool, optional): Whether to select all predefined trusted networks. Defaults to False.
    - select_any_tn (bool, optional): Whether to select any predefined trusted networks. Defaults to False.
    - access_zpa_app (bool, optional): Whether to access the ZPA application. Defaults to True.

    Returns:
    - None
    """
    result = sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode=off_trusted_tunnel_mode)
    assert result[0], result[1]

    result = zcc.update_policy(sleep_time=20)
    assert result[0], result[1]

    # result = sanity_zcc.validate_tunnel_mode_from_logs(tunnel_version=off_trusted_tunnel_mode)
    # assert result[0], result[1]

    # Validate the network status for off-trusted networks
    result = zcc.validate_network_status_on_zcc(trusted_network=False)
    assert result[0], result[1]  # Assert that the network status is correct

    if access_zpa_app:
        result = sys_ops_obj.access_application(url=zpa_application,assertion_element=zpa_application_assertion_element, failure_expected=True, browser="safari", dynamic_wait_search_element=5)
        assert result[0], result[1]

    if select_all_tn:
        result = sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile(
            trusted_network="predefined",
            trusted_condition_match="all",
            tunnel_mode={
                'offTrusted':off_trusted_tunnel_mode,
                'onTrusted':on_trusted_tunnel_mode
            }
        )
        assert result[0], result[1]
    elif select_any_tn:
        result = sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile(
            trusted_network="predefined",
            trusted_condition_match="any",
            tunnel_mode={
                'offTrusted':off_trusted_tunnel_mode,
                'onTrusted':on_trusted_tunnel_mode
            }
        )
        assert result[0], result[1]
    else:
        result = sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile(
            trusted_network="predefined",
            specific_predefined_trusted_networks_to_apply=[TRUSTED_NETWORK_CRITERIA_NAME],
            tunnel_mode={
                'offTrusted':off_trusted_tunnel_mode,
                'onTrusted':on_trusted_tunnel_mode
            }
        )
        assert result[0], result[1]

    result = zcc.update_policy(sleep_time=20)
    assert result[0], result[1]

    # result = sanity_zcc.validate_tunnel_mode_from_logs(tunnel_version=on_trusted_tunnel_mode)
    # assert result[0], result[1]

    # Validate the network status for on-trusted networks
    result = zcc.validate_network_status_on_zcc(trusted_network=True)
    assert result[0], result[1]  # Assert that the network status is correct

    if access_zpa_app:
        result = sys_ops_obj.access_application(url=zpa_application, assertion_element=zpa_application_assertion_element, failure_expected=False, browser="safari")
        assert result[0], result[1]


def make_teardown():
    """
    Delete access policy in ZPA admin cloud
    Delete the created trusted networks in MA
    Delete device policy & forwarding profile in MA
    """
    sanity_zcc.zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=BLOCK_ALL_ACCESS_POLICY_NAME)
    sanity_zcc.zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=ACCESS_POLICY_NAME)
    sanity_zcc.ma_app_profile.delete_app_profile()
    sanity_zcc.ma_app_profile.forwarding_profile.delete_forwarding_profile()
    # Delete the ZPA applications
    create_zpa_apps.delete_zpa_apps(custom_app_segment_name="mtn_test")  # Delete the ZPA applications
    sanity_zcc.ma_trusted_network.delete_trusted_network(delete_all=True)

@pytest.fixture(scope="class", autouse=True)
def create_setup(request):
    """
    
    """
    try:
        # sanity_zcc.zpa_policy.delete_policy(policy_type="AccessPolicy", policy_name=ACCESS_POLICY_NAME)
        sanity_zcc.ma_app_profile.delete_app_profile()
        sanity_zcc.ma_app_profile.forwarding_profile.delete_forwarding_profile()
        sanity_zcc.ma_trusted_network.delete_trusted_network(delete_all=True)

        # Delete any existing ZPA apps
        create_zpa_apps.delete_zpa_apps(custom_app_segment_name="mtn_test")

         # Create new ZPA apps
        res = create_zpa_apps.create_zpa_apps(custom_app_segment_name="mtn_test")
        trusted_network_logger.info(res[1])  # Log the result
        assert res[0], res[1]  # Check if the creation was successful

        result = sanity_zcc.ma_serviceentitlement.toggle_zdx(action=False)
        assert result[0], result[1]

        result = sanity_zcc.ma_serviceentitlement.toggle_zpa(action=True)
        assert result[0], result[1]

        result = sanity_zcc.ma_serviceentitlement.toggle_zia(action=True)
        assert result[0], result[1]

        result = sanity_zcc.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = sanity_zcc.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        # result = sanity_zcc.zpa_policy.create_access_policy(zpa_user_name=config["ZPA_ADMIN_ID"], zpa_password=config["ZPA_ADMIN_PASSWORD"], access_policy_name=ACCESS_POLICY_NAME)
        # assert result[0], result[1]

        result=sanity_zcc.ma_clientconnectornotifications.configure_aup()
        assert result[0], result[1]

        # Enable SAML on ZIA
        result=sanity_zcc.saml_zia.saml_action_smui(mode='enable')
        assert result[0], result[1]  # Check if the enablement was successful

        result=sanity_zcc.ma_platform_settings.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]

        # Change SAML IDP on ZIA
        result =sanity_zcc.saml_zia.change_saml_on_zia(idp_name=config['IDP_NAME'])
        assert result[0],result[1]  # Check if the change was successful

        # Enable 4.3 SDK for T2 related validations as T2 is supportted with 4.3 SDK only
        result =sanity_zcc.ma_app_profile.edit_app_profile(use_tunnel_sdk_version_43=True)
        assert result[0],result[1]


    except Exception as e:
        trusted_network_logger.error(f"Unable to create setup, Error: {e}")
        request.addfinalizer(make_teardown)
    else:
        trusted_network_logger.info(f"Successfully created setup")
        request.addfinalizer(make_teardown)
    
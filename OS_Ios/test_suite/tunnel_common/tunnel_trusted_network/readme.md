# ZCC Trusted Networks

Validate that Trusted Networks works fine, when set up in Forwarding profile (Predefined TN + InForwardingProfiel TN) and ZPA Access Policies.


#### Version Info

ZCC 3.7+

#### This Suite includes testcases 
    * Validation of Predefined Trusted Networks
    * Validation of In Forward Profile Trusted Networks


#### Prerequisites
    * Both iOS and MAC device should be on same network (utilizing same dns server and dns search domain)
    * Export logs via airdrop requires both mac and iOS device use same apple ID in order to accept files automatically. Make sure to keep airdrop up and working.
    * Pass "MAC_HOSTNAME" such as "<PERSON>riya<PERSON><PERSON>'s Macbook Pro" as it is required for airdrop to work as expected
    * ZCC build installed and in Logged Out state.
    * On MAC, logout/exit from ZCC to run this suite
    * Quick Automation Setup Guide: https://confluence.corp.zscaler.com/display/~priyanshu.sharma/iOS+Automation+Setup
    *  In config file give following :
    --> "IDP_NAME" : "Okta" #(idp name configured on ZIA portal)
    --> "MAC_HOSTNAME" : "<PERSON><PERSON><PERSON><PERSON>'s Macbook Pro" #mac hostname

### Executing program

* Start WebDriverAgent 
* Start Appium Server
* Run terminal in Admin mode
* Command:

```
sudo python3 trigger_automation.py --testcases OS_Ios/test_suite/tunnel_common/tunnel_trusted_network/ --zccVersion 3.8  --config <yourconfig>.json
```


### Author

Priyanshu Sharma      (<EMAIL>)

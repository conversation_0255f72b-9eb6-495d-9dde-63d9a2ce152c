import pytest, time, allure
from common_lib.Custom_Markers import add_markers,zcc_mark_version
# from OS_Ios.test_suite.zcc_misc.zcc_trusted_network import conftest
from OS_Ios.test_suite.tunnel_common.tunnel_trusted_network.in_forwarding_profile_trusted_networks import conftest
import time

class TestInForwardingProfileTrustedNetworksConditionMatchAny:

    @allure.title("Setup Class")
    def setup_class(self):
        """
        Setup method for the test class.

        This method logs in to ZCC with the provided credentials and settings, and validates that the login was successful.
        It also validates that Z<PERSON> is logged in.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If the login or validation fails.
        """
        # Login to ZCC with the provided credentials and settings
        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],  # ZIA user ID
            zia_password=conftest.config["ZIA_USER_PASSWORD"],  # ZIA user password
            zpa_user=conftest.config["ZPA_USER_ID"],  # ZPA user ID
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],  # ZPA user password
            zia_saml_login=True,  # Use ZIA SAML login
        )
        # Assert that the login was successful
        assert result[0], result[1]
        
        # Validate that ZCC is logged in
        result = conftest.zcc.validate_zcc_logged_in()
        # Assert that the validation was successful
        assert result[0], result[1]

    def teardown_class(self):
        """
        Teardown method for the test class.

        This method edits the forwarding profile and logs out of ZCC.

        Args:
            None

        Returns:
            None

        Raises:
            None
        """
        conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile()
        conftest.zcc.logout()

    def validate_trusted_network_match_any_condition(self, off_trusted_tunnel_mode: str, on_trusted_tunnel_mode: str):
        """
        Validates the trusted network match any condition by switching between off-trusted and on-trusted tunnel modes.

        This method performs the following steps:
        1. Edits the forwarding profile to set the off-trusted tunnel mode.
        2. Updates the policy and exports logs to verify the changes.
        3. Validates the tunnel mode from the logs.
        4. Validates the network status for off-trusted networks.
        5. Fetches the IP address of the hostname.
        6. Edits the forwarding profile to set the on-trusted tunnel mode with the hostname IP and DNS servers.
        7. Updates the policy and exports logs to verify the changes.
        8. Validates the tunnel mode from the logs.
        9. Validates the network status for on-trusted networks.

        Args:
            off_trusted_tunnel_mode (str): The tunnel mode to use for off-trusted networks.
            on_trusted_tunnel_mode (str): The tunnel mode to use for on-trusted networks.

        Returns:
            None
        """
        
        result = conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode=off_trusted_tunnel_mode)
        assert result[0], result[1]

        conftest.sanity_zcc.log_ops.ignore_logs_before_this_time_point()

        result = conftest.zcc.update_policy(sleep_time=20)
        assert result[0], result[1]

        # Export logs to verify the changes
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful

        result = conftest.sanity_zcc.validate_tunnel_mode_from_logs(tunnel_version=off_trusted_tunnel_mode)
        assert result[0], result[1]

        # Validate the network status for off-trusted networks
        result = conftest.zcc.validate_network_status_on_zcc(trusted_network=False)
        assert result[0], result[1]  # Assert that the network status is correct

        result = conftest.sys_ops_obj.fetch_ip(application=conftest.HOSTNAME)
        assert result[0], result[1]

        resolved_ip_for_host = result[-1]
        hostname_ip = conftest.HOSTNAME + ',' + resolved_ip_for_host

        result = conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile(
            trusted_network="inforwardprofile",
            hostname_ip=hostname_ip,
            dns_servers=','.join(conftest.DNS_SERVER),
            trusted_condition_match='any',
            tunnel_mode={
                'offTrusted':off_trusted_tunnel_mode,
                'onTrusted':on_trusted_tunnel_mode
            }
        )
        assert result[0], result[1]

        conftest.sanity_zcc.log_ops.ignore_logs_before_this_time_point()

        result = conftest.zcc.update_policy(sleep_time=20)
        assert result[0], result[1]

        # Export logs to verify the changes
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful

        result = conftest.sanity_zcc.validate_tunnel_mode_from_logs(tunnel_version=on_trusted_tunnel_mode)
        assert result[0], result[1]

        # Validate the network status for on-trusted networks
        result = conftest.zcc.validate_network_status_on_zcc(trusted_network=True)
        assert result[0], result[1]  # Assert that the network status is correct

    # #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 1 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288144')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_1_to_tunnel_2_tls(self) -> None:
        """
        Validates trusted network match any condition by editing the forwarding profile, 
        updating the policy, exporting logs, validating the tunnel mode and network status 
        for both off-trusted and on-trusted networks with off-trusted tunnel mode as 'tunnel_1' 
        and on-trusted tunnel mode as 'tunnel_2_tls'.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If validation fails
        """
        # Validate trusted network match any condition with specified tunnel modes
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_tls')
    

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 2 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288145')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_1_to_tunnel_2_dtls(self) -> None:
        """
        Validates DNS search domain functionality when trusted network criteria is set to 'Match Any Condition' 
        and the tunnel mode is switched from 'Tunnel 1' to 'Tunnel 2 DTLS'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Validate trusted network match any condition with specified tunnel modes
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_dtls')
    

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 3 - OffTrusted: Tunnel 1, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288146')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_1_to_twlp(self):
        """
        Test case to validate trusted network match any condition with off-trusted tunnel mode as 'tunnel_1' and on-trusted tunnel mode as 'twlp'.
        """
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 4 - OffTrusted: Tunnel 1, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288147')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_1_to_enforce_proxy(self):
        """
        Validates trusted network match any condition by editing the forwarding profile, 
        updating the policy, exporting logs, validating the tunnel mode and network status 
        for both off-trusted and on-trusted networks with off-trusted tunnel mode as 'tunnel_1' 
        and on-trusted tunnel mode as 'enforce_proxy'.
    
        Marks tests to skip if the ZCC version is less than 3.7.
        Marks tests with pytest markers 'regression' and 'sanity' if they are defined in pytest.ini.
        """
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 5 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288148')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_2_tls_to_tunnel_1(self):
        """
        This function calls the validate_trusted_network_match_any_condition method with off-trusted tunnel mode as 'tunnel_2_tls' and on-trusted tunnel mode as 'tunnel_1'.
        Returns:
            None
    
        Raises:
            AssertionError: If trusted network match any condition validation fails.
        """
        # Validate trusted network match any condition with specified tunnel modes
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_1')
    

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 6 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288149')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_2_tls_to_tunnel_2_dtls(self):
        """
        This function calls the validate_trusted_network_match_any_condition method with off-trusted tunnel mode as 'tunnel_2_tls' and on-trusted tunnel mode as 'tunnel_2_dtls'.
        Returns:
            None
        Raises:
            AssertionError: If trusted network match any condition validation fails.
        """
        # Validate trusted network match any condition with specified tunnel modes
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 7 - OffTrusted: Tunnel 2 TLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288150')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_2_tls_to_twlp(self):
        """
        This function calls the validate_trusted_network_match_any_condition method with off-trusted tunnel mode as 'tunnel_2_tls' and on-trusted tunnel mode as 'twlp'.
        Returns:
            None
        Raises:
            AssertionError: If trusted network match any condition validation fails.
        """
        # Validate trusted network match any condition with specified tunnel modes
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 8 - OffTrusted: Tunnel 2 TLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288151')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_2_tls_to_enforce_proxy(self):
        """
        Validates trusted network match any condition by editing the forwarding profile, 
        updating the policy, exporting logs, validating the tunnel mode and network status 
        for both off-trusted and on-trusted networks with off-trusted tunnel mode as 'tunnel_2_tls' 
        and on-trusted tunnel mode as 'enforce_proxy'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None

        Raises:
            AssertionError: If trusted network match any condition validation fails.
        """
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 9 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288152')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_2_dtls_to_tunnel_1(self) -> None:
        """
        Validates trusted network match any condition by editing the forwarding profile, 
        updating the policy, exporting logs, validating the tunnel mode and network status 
        for both off-trusted and on-trusted networks with specific tunnel modes.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Validate trusted network match any condition with off-trusted tunnel mode as 'tunnel_2_dtls' and on-trusted tunnel mode as 'tunnel_1'
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 10 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288153')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_2_dtls_to_tunnel_2_tls(self) -> None:
        """
        Validates trusted network match any condition by editing the forwarding profile, 
        updating the policy, exporting logs, validating the tunnel mode and network status 
        for both off-trusted and on-trusted networks with specific tunnel modes.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Validate trusted network match any condition with off-trusted tunnel mode as 'tunnel_2_dtls' and on-trusted tunnel mode as 'tunnel_2_tls'
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_2_tls')
    

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 11 - OffTrusted: Tunnel 2 DTLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288154')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_2_dtls_to_twlp(self):
        """
        Validate trusted network match any condition with off-trusted tunnel mode as 'tunnel_2_dtls' and on-trusted tunnel mode as 'twlp'.
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 12 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288155')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_2_dtls_to_enforce_proxy(self):
        """
        Validates trusted network match any condition by editing the forwarding profile, 
        updating the policy, exporting logs, validating the tunnel mode and network status 
        for both off-trusted and on-trusted networks with off-trusted tunnel mode as 'tunnel_2_dtls' 
        and on-trusted tunnel mode as 'enforce_proxy'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 13 - OffTrusted: TWLP, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288156')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_twlp_to_tunnel_1(self) -> None:
        """
        Validates DNS search domain functionality when trusted network criteria is set to 'Match Any Condition' 
        and the tunnel mode is changed from 'TWLP' to 'Tunnel 1'.
    
        This test case covers the scenario where the trusted network criteria is set to 'Match Any Condition' 
        and the tunnel mode is changed from 'TWLP' to 'Tunnel 1'. It validates the DNS search domain functionality 
        in this scenario.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If the validation fails.
        """
        # Validate trusted network match any condition with specified tunnel modes
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 14 - OffTrusted: TWLP, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288157')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_twlp_to_tunnel_2_tls(self) -> None:
        """
        Validates DNS search domain functionality when trusted network criteria is set to 'Match Any Condition' 
        and the tunnel mode is changed from TWLP to Tunnel 2 TLS.
    
        This test case covers the scenario where the off-trusted tunnel mode is set to TWLP and the on-trusted 
        tunnel mode is set to Tunnel 2 TLS.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If the trusted network match any condition validation fails.
        """
        # Validate trusted network match any condition with specified tunnel modes
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_tls')
    

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 15 - OffTrusted: TWLP, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288158')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_twlp_to_tunnel_2_dtls(self) -> None:
        """
        Validates DNS search domain functionality when trusted network criteria is set to 'Match Any Condition' 
        and the tunnel mode is changed from TWLP to Tunnel 2 DTLS.
    
        This test case covers the scenario where the trusted network criteria is set to 'Match Any Condition' 
        and the tunnel mode is changed from TWLP to Tunnel 2 DTLS. It validates the DNS search domain functionality 
        in this scenario.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If the validation fails.
        """
        # Validate trusted network match any condition with specified tunnel modes
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_dtls')
    

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 16 - OffTrusted: TWLP, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288159')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_twlp_to_enforce_proxy(self):
        """
        Validates trusted network match any condition by editing the forwarding profile, 
        updating the policy, exporting logs, validating the tunnel mode and network status 
        for both off-trusted and on-trusted networks with TWLP and Enforce Proxy modes.
    
        Marks tests to skip if the ZCC version is less than 3.7.
        Adds pytest markers 'regression' and 'sanity' to the test function.
    
        Args:
            self: instance of the test class
    
        Returns:
            None
        """
        # Validate trusted network match any condition with specified tunnel modes
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 17 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288160')  # Link test case to Xray QA-288160
    @add_markers("regression", "sanity")  # Add pytest markers 'regression' and 'sanity'
    @zcc_mark_version(min=3.7)  # Skip test if ZCC version is less than 3.7
    def test_dns_search_domain_enforce_proxy_to_tunnel_1(self) -> None:
        """
        Validate trusted network match any condition by editing the forwarding profile, updating the policy, exporting logs, validating the tunnel mode and network status for both off-trusted and on-trusted networks.
    
        Args:
            self: Instance of the test class.
    
        Returns:
            None
        """
        # Validate trusted network match any condition with off-trusted tunnel mode as 'enforce_proxy' and on-trusted tunnel mode as 'tunnel_1'
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_1')
    

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 18 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288161')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_enforce_proxy_to_tunnel_2_tls(self) -> None:
        """
        Test case to validate trusted network match any condition with off-trusted tunnel mode as 'enforce_proxy' and on-trusted tunnel mode as 'tunnel_2_tls'.
        
        This test case is marked with xray ID 'QA-288161' and is part of regression and sanity test suites.
        It requires ZCC version to be 3.7 or higher.
        
        Parameters:
            self: The test class instance.
        
        Returns:
            None
        
        Raises:
            AssertionError: If trusted network match any condition validation fails.
        """
        # Validate trusted network match any condition with specified tunnel modes
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_tls')
    

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 19 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288162')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_enforce_proxy_to_tunnel_2_dtls(self) -> None:
        """
        Validates trusted network match any condition by editing the forwarding profile, 
        updating the policy, exporting logs, validating the tunnel mode and network status 
        for both off-trusted and on-trusted networks with off-trusted tunnel mode as 'enforce_proxy' 
        and on-trusted tunnel mode as 'tunnel_2_dtls'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Validate trusted network match any condition with specified tunnel modes
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_dtls')
    

    @allure.title("Validate in FP trusted network criteria - Match Any Condition Selected - Case 20 - OffTrusted: Enforce Proxy, OnTrusted: TWLP")
    @zcc_mark_version(min=3.7)  # Skip test if ZCC version is less than 3.7
    @add_markers("regression", "sanity")  # Add pytest markers for regression and sanity test suites
    @pytest.mark.xray('QA-288163')  # Mark test case with xray ID
    def test_dns_search_domain_enforce_proxy_to_twlp(self):
        """
        Validate trusted network match any condition with off-trusted tunnel mode as 'enforce_proxy' and on-trusted tunnel mode as 'twlp'.
        """
        self.validate_trusted_network_match_any_condition(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='twlp')
    

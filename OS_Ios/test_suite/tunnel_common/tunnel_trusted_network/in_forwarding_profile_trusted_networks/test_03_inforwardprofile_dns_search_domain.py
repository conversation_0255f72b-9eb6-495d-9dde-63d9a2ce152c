import pytest, time, allure
from common_lib.Custom_Markers import add_markers,zcc_mark_version
# from OS_Ios.test_suite.zcc_misc.zcc_trusted_network import conftest
from OS_Ios.test_suite.tunnel_common.tunnel_trusted_network.in_forwarding_profile_trusted_networks import conftest
import time

class TestInForwardingProfileTrustedNetworksDnsSearchDomain:

    search_domains = None

    @allure.title("Setup Class")
    def setup_class(self):
        """
        Setup method for the test class.

        This method logs in to ZCC with the provided credentials and settings, and validates that the login was successful.
        It also validates that ZCC is logged in.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If the login or validation fails.
        """

        # res = conftest.sys_ops_obj.fetch_network_dns_search_domains()
        # assert res[0], res[1]

        # self.search_domains = res[-1]

        # if not self.search_domains:
        #Skipping DNS Search Domain Cases as it is not deployed in customer environment for iOS ---- will use once required
        pytest.skip(allow_module_level=True, reason="No DNS search domains found on the network. Skipping all the dependent tests.")
        
        # Login to ZCC with the provided credentials and settings
        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],  # ZIA user ID
            zia_password=conftest.config["ZIA_USER_PASSWORD"],  # ZIA user password
            zpa_user=conftest.config["ZPA_USER_ID"],  # ZPA user ID
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],  # ZPA user password
            zia_saml_login=True,  # Use ZIA SAML login
        )
        # Assert that the login was successful
        assert result[0], result[1]
        
        # Validate that ZCC is logged in
        result = conftest.zcc.validate_zcc_logged_in()
        # Assert that the validation was successful
        assert result[0], result[1]

    def teardown_class(self):
        """
        Teardown method for the test class.

        This method edits the forwarding profile and logs out of ZCC.

        Args:
            None

        Returns:
            None

        Raises:
            None
        """
        conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile()
        conftest.zcc.logout()

    def validate_trusted_network_dns_search_domain(self, off_trusted_tunnel_mode: str, on_trusted_tunnel_mode: str):
        """
        Validates the trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes.

        This method performs the following steps:
        1. Edits the forwarding profile to set the off-trusted tunnel mode.
        2. Updates the policy and exports logs to verify the changes.
        3. Validates the tunnel mode for off-trusted networks.
        4. Validates the network status for off-trusted networks.
        5. Edits the forwarding profile to set the DNS search domains and tunnel mode for on-trusted networks.
        6. Updates the policy and exports logs to verify the changes.
        7. Validates the tunnel mode for on-trusted networks.
        8. Validates the network status for on-trusted networks.

        Args:
            off_trusted_tunnel_mode (str): The tunnel mode to use for off-trusted networks.
            on_trusted_tunnel_mode (str): The tunnel mode to use for on-trusted networks.

        Returns:
            None
        """
    
        # Edit the forwarding profile to set the tunnel mode for off-trusted networks
        result = conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode=off_trusted_tunnel_mode)
        assert result[0], result[1]  # Assert that the edit operation was successful

        conftest.sanity_zcc.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy to apply the changes
        result = conftest.zcc.update_policy(sleep_time=20)
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs to verify the changes
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Validate the tunnel mode for off-trusted networks
        result = conftest.sanity_zcc.validate_tunnel_mode_from_logs(tunnel_version=off_trusted_tunnel_mode)
        assert result[0], result[1]  # Assert that the tunnel mode is correct
    
        # Validate the network status for off-trusted networks
        result = conftest.zcc.validate_network_status_on_zcc(trusted_network=False)
        assert result[0], result[1]  # Assert that the network status is correct
    
        # Edit the forwarding profile to set the DNS search domains and tunnel mode for on-trusted networks
        result = conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile(
            trusted_network="inforwardprofile",
            dns_search_domains=','.join(self.search_domains),
            tunnel_mode={
                'offTrusted': off_trusted_tunnel_mode,
                'onTrusted': on_trusted_tunnel_mode
            }
        )
        assert result[0], result[1]  # Assert that the edit operation was successful

        conftest.sanity_zcc.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy to apply the changes
        result = conftest.zcc.update_policy(sleep_time=20)
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs to verify the changes
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Validate the tunnel mode for on-trusted networks
        result = conftest.sanity_zcc.validate_tunnel_mode_from_logs(tunnel_version=on_trusted_tunnel_mode)
        assert result[0], result[1]  # Assert that the tunnel mode is correct
    
        # Validate the network status for on-trusted networks
        result = conftest.zcc.validate_network_status_on_zcc(trusted_network=True)
        assert result[0], result[1]  # Assert that the network status is correct

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 1 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288124')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_tunnel_1_to_tunnel_2_tls(self):
        """
        Validates the trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the DNS Search Domains when the off-trusted tunnel mode is set to 'tunnel_1' and the on-trusted tunnel mode is set to 'tunnel_2_tls'.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate the trusted network DNS Search Domains
        # with off-trusted tunnel mode as 'tunnel_1' and on-trusted tunnel mode as 'tunnel_2_tls'
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 2 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288125')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_tunnel_1_to_tunnel_2_dtls(self):
        """
        Test case to validate DNS Search Domain in Forwarding Profile (FP) trusted network criteria.
        
        This test case validates the DNS Search Domain by switching between off-trusted and on-trusted tunnel modes.
        The off-trusted tunnel mode is set to 'tunnel_1' and the on-trusted tunnel mode is set to 'tunnel_2_dtls'.
        
        Args:
            None
        
        Returns:
            None
        
        Raises:
            AssertionError: If the validation of DNS Search Domain fails.
        """
        
        # Call the function to validate trusted network DNS Search Domain
        # with off-trusted tunnel mode as 'tunnel_1' and on-trusted tunnel mode as 'tunnel_2_dtls'
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 3 - OffTrusted: Tunnel 1, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288126')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_tunnel_1_to_twlp(self):
        """
        Validates the trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the DNS Search Domains when the off-trusted tunnel mode is set to 'tunnel_1' and the on-trusted tunnel mode is set to 'twlp'.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate the trusted network DNS Search Domains
        # with off-trusted tunnel mode as 'tunnel_1' and on-trusted tunnel mode as 'twlp'
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 4 - OffTrusted: Tunnel 1, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288127')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_tunnel_1_to_enforce_proxy(self):
        """
        Validates the trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the DNS Search Domains when the off-trusted tunnel mode is set to 'tunnel_1' and the on-trusted tunnel mode is set to 'enforce_proxy'.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If the validation fails.
        """
        # Call the function to validate the trusted network DNS Search Domains
        # with off-trusted tunnel mode as 'tunnel_1' and on-trusted tunnel mode as 'enforce_proxy'
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 5 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288128')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_tunnel_2_tls_to_tunnel_1(self):
        """
        Validates the trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes.
    
        This test case validates the DNS Search Domain functionality by switching between off-trusted (Tunnel 2 TLS) and on-trusted (Tunnel 1) tunnel modes.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate trusted network DNS Search Domain with off-trusted tunnel mode as 'tunnel_2_tls' and on-trusted tunnel mode as 'tunnel_1'
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 6 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288129')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_tunnel_2_tls_to_tunnel_2_dtls(self) -> None:
        """
        Validates the trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes.
    
        This test case validates the DNS Search Domains when the off-trusted tunnel mode is set to 'tunnel_2_tls' and the on-trusted tunnel mode is set to 'tunnel_2_dtls'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate the trusted network DNS Search Domains
        # with off-trusted tunnel mode as 'tunnel_2_tls' and on-trusted tunnel mode as 'tunnel_2_dtls'
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 7 - OffTrusted: Tunnel 2 TLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288130')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_tunnel_2_tls_to_twlp(self):
        """
        Test case to validate trusted network DNS Search Domain criteria.

        This test case validates the trusted network DNS Search Domain criteria by switching between off-trusted and on-trusted tunnel modes.
        It uses the validate_trusted_network_dns_search_domain function to perform the validation.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If the validation fails.
        """
        # Call the function to validate trusted network DNS Search Domain criteria
        # with off-trusted tunnel mode as 'tunnel_2_tls' and on-trusted tunnel mode as 'twlp'
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 8 - OffTrusted: Tunnel 2 TLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288131')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_tunnel_2_tls_to_enforce_proxy(self):
        """
        Validates the trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the DNS Search Domain functionality when the off-trusted tunnel mode is set to 'tunnel_2_tls' and the on-trusted tunnel mode is set to 'enforce_proxy'.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        
        # Call the function to validate the trusted network DNS Search Domain
        # with off-trusted tunnel mode as 'tunnel_2_tls' and on-trusted tunnel mode as 'enforce_proxy'
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 9 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288132')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_tunnel_2_dtls_to_tunnel_1(self) -> None:
        """
        Test case to validate DNS Search Domain in Forwarding Profile (FP) trusted network criteria.
        
        This test case validates the DNS Search Domain by switching between off-trusted and on-trusted tunnel modes.
        The off-trusted tunnel mode is set to 'tunnel_2_dtls' and the on-trusted tunnel mode is set to 'tunnel_1'.
        
        Args:
            None
        
        Returns:
            None
        
        Raises:
            AssertionError: If the validation fails.
        """
        
        # Call the function to validate trusted network DNS Search Domain
        # with off-trusted tunnel mode as 'tunnel_2_dtls' and on-trusted tunnel mode as 'tunnel_1'
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 10 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288133')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_tunnel_2_dtls_to_tunnel_2_tls(self):
        """
        Validates the trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes.
    
        This test case validates the DNS Search Domains when the off-trusted tunnel mode is set to 'tunnel_2_dtls' and the on-trusted tunnel mode is set to 'tunnel_2_tls'.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate the trusted network DNS Search Domains
        # with off-trusted tunnel mode as 'tunnel_2_dtls' and on-trusted tunnel mode as 'tunnel_2_tls'
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 11 - OffTrusted: Tunnel 2 DTLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288134')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_tunnel_2_dtls_to_twlp(self):
        """
        Validates the trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the DNS Search Domain functionality by switching between off-trusted (Tunnel 2 DTLS) and on-trusted (TWLP) tunnel modes.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate trusted network DNS Search Domain
        # with off-trusted tunnel mode as 'tunnel_2_dtls' and on-trusted tunnel mode as 'twlp'
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 12 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288135')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_tunnel_2_dtls_to_enforce_proxy(self):
        """
        Validate in FP trusted network criteria - DNS Search Domain - Case 12 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Enforce Proxy.
        
        This test case validates the trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes.
        
        Args:
            None
        
        Returns:
            None
        
        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Validate the trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes
        # Off-trusted tunnel mode is set to 'tunnel_2_dtls' and on-trusted tunnel mode is set to 'enforce_proxy'
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 13 - OffTrusted: TWLP, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288136')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_twlp_to_tunnel_1(self):
        """
        Validates the trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes.
    
        This test case validates the DNS Search Domain functionality by switching between TWLP (off-trusted) and Tunnel 1 (on-trusted) modes.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate trusted network DNS Search Domain with off-trusted tunnel mode as TWLP and on-trusted tunnel mode as Tunnel 1
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 14 - OffTrusted: TWLP, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288137')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_twlp_to_tunnel_2_tls(self):
        """
        Validates the trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes.
    
        This test case validates the DNS Search Domain functionality by switching between TWLP (off-trusted) and Tunnel 2 TLS (on-trusted) modes.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If the validation fails.
        """
        # Call the function to validate the trusted network DNS Search Domain
        # with off-trusted tunnel mode as TWLP and on-trusted tunnel mode as Tunnel 2 TLS
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 15 - OffTrusted: TWLP, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288138')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_twlp_to_tunnel_2_dtls(self):
        """
        Validate in FP trusted network criteria - DNS Search Domain - Case 15 - OffTrusted: TWLP, OnTrusted: Tunnel 2 DTLS.
    
        This test case validates the trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes.
        
        Args:
            None
        
        Returns:
            None
        
        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Validate trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes
        # off_trusted_tunnel_mode: 'twlp' (TWLP tunnel mode)
        # on_trusted_tunnel_mode: 'tunnel_2_dtls' (Tunnel 2 DTLS tunnel mode)
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 16 - OffTrusted: TWLP, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288139')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_twlp_to_enforce_proxy(self):
        """
        Validates the trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the DNS Search Domains when the off-trusted tunnel mode is set to TWLP and the on-trusted tunnel mode is set to Enforce Proxy.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate the trusted network DNS Search Domains
        # with off-trusted tunnel mode as TWLP and on-trusted tunnel mode as Enforce Proxy
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 17 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288140')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_enforce_proxy_to_tunnel_1(self):
        """
        Validates the trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the DNS Search Domain functionality by switching between off-trusted (Enforce Proxy) and on-trusted (Tunnel 1) tunnel modes.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        
        # Call the function to validate the trusted network DNS Search Domain
        # with off-trusted tunnel mode as Enforce Proxy and on-trusted tunnel mode as Tunnel 1
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 18 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288141')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_enforce_proxy_to_tunnel_2_tls(self):
        """
        Validates the trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes.

        This test case covers the scenario where the off-trusted tunnel mode is set to 'enforce_proxy' and the on-trusted tunnel mode is set to 'tunnel_2_tls'.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate the trusted network DNS Search Domains
        # with off-trusted tunnel mode as 'enforce_proxy' and on-trusted tunnel mode as 'tunnel_2_tls'
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 19 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288142')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_enforce_proxy_to_tunnel_2_dtls(self):
        """
        Test case to validate DNS Search Domain in Forwarding Profile (FP) trusted network criteria.
        
        This test case validates the DNS Search Domain functionality by switching between off-trusted and on-trusted tunnel modes.
        
        Args:
            None
        
        Returns:
            None
        
        Raises:
            AssertionError: If any of the validation steps fail.
        """
        
        # Call the function to validate trusted network DNS Search Domain
        # with off-trusted tunnel mode as 'enforce_proxy' and on-trusted tunnel mode as 'tunnel_2_dtls'
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate in FP trusted network criteria - DNS Search Domain - Case 20 - OffTrusted: Enforce Proxy, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288143')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_search_domain_enforce_proxy_to_twlp(self):
        """
        Validates the trusted network DNS Search Domains by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the DNS Search Domain functionality when the off-trusted tunnel mode is set to 'enforce_proxy' and the on-trusted tunnel mode is set to 'twlp'.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If the validation fails.
        """
        
        # Call the function to validate the trusted network DNS Search Domain
        # with off-trusted tunnel mode as 'enforce_proxy' and on-trusted tunnel mode as 'twlp'
        self.validate_trusted_network_dns_search_domain(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='twlp')

import pytest, time, allure
from common_lib.Custom_Markers import add_markers,zcc_mark_version
# from OS_Ios.test_suite.zcc_misc.zcc_trusted_network import conftest
from OS_Ios.test_suite.tunnel_common.tunnel_trusted_network.in_forwarding_profile_trusted_networks import conftest
import time

class TestInForwardingProfileTrustedNetworksHostnameIp:
    @allure.title("Setup Class")
    def setup_class(self):
        """
        Setup method for the test class.

        This method logs in to ZCC with the provided credentials and settings, and validates that the login was successful.
        It also validates that <PERSON><PERSON> is logged in.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If the login or validation fails.
        """
        # Login to ZCC with the provided credentials and settings
        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],  # ZIA user ID
            zia_password=conftest.config["ZIA_USER_PASSWORD"],  # ZIA user password
            zpa_user=conftest.config["ZPA_USER_ID"],  # ZPA user ID
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],  # ZPA user password
            zia_saml_login=True,  # Use ZIA SAML login
        )
        # Assert that the login was successful
        assert result[0], result[1]
        
        # Validate that ZCC is logged in
        result = conftest.zcc.validate_zcc_logged_in()
        # Assert that the validation was successful
        assert result[0], result[1]

    def teardown_class(self):
        """
        Teardown method for the test class.

        This method edits the forwarding profile and logs out of ZCC.

        Args:
            None

        Returns:
            None

        Raises:
            None
        """
        conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile()
        conftest.zcc.logout()

    def validate_trusted_network_hostname_ip(self, off_trusted_tunnel_mode: str, on_trusted_tunnel_mode: str) -> None:
        """
        Validates the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.

        This method performs the following steps:
        1. Edits the forwarding profile to set the off-trusted tunnel mode.
        2. Updates the policy and exports logs to verify the changes.
        3. Validates the tunnel mode for off-trusted networks.
        4. Validates the network status for off-trusted networks.
        5. Fetches the IP address for the hostname.
        6. Edits the forwarding profile to set the on-trusted tunnel mode with the hostname IP.
        7. Updates the policy and exports logs to verify the changes.
        8. Validates the tunnel mode for on-trusted networks.
        9. Validates the network status for on-trusted networks.

        Args:
            off_trusted_tunnel_mode (str): The tunnel mode to use for off-trusted networks.
            on_trusted_tunnel_mode (str): The tunnel mode to use for on-trusted networks.

        Returns:
            None
        """
    
        # Edit the forwarding profile to set the tunnel mode for off-trusted networks
        result = conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode=off_trusted_tunnel_mode)
        assert result[0], result[1]  # Check if the edit operation was successful

        conftest.sanity_zcc.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy to apply the changes
        result = conftest.zcc.update_policy(sleep_time=20)
        assert result[0], result[1]  # Check if the policy update was successful
    
        # Export logs to verify the changes
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Check if the log export was successful
    
        # Validate the tunnel mode for off-trusted networks
        result = conftest.sanity_zcc.validate_tunnel_mode_from_logs(tunnel_version=off_trusted_tunnel_mode)
        assert result[0], result[1]  # Check if the tunnel mode is correct
    
        # Validate the network status for off-trusted networks
        result = conftest.zcc.validate_network_status_on_zcc(trusted_network=False)
        assert result[0], result[1]  # Check if the network status is correct
    
        # Fetch the IP address for the hostname
        result = conftest.sys_ops_obj.fetch_ip(application=conftest.HOSTNAME)
        assert result[0], result[1]  # Check if the IP address was fetched successfully
        resolved_ip_for_host = result[-1]  # Get the resolved IP address
    
        # Create the hostname IP string
        hostname_ip = f"{conftest.HOSTNAME},{resolved_ip_for_host}"
    
        # Edit the forwarding profile to set the tunnel mode for on-trusted networks and hostname IP
        result = conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile(
            trusted_network="inforwardprofile",
            hostname_ip=hostname_ip,
            tunnel_mode={
                'offTrusted': off_trusted_tunnel_mode,
                'onTrusted': on_trusted_tunnel_mode
            }
        )
        assert result[0], result[1]  # Check if the edit operation was successful

        conftest.sanity_zcc.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy to apply the changes
        result = conftest.zcc.update_policy(sleep_time=20)
        assert result[0], result[1]  # Check if the policy update was successful
    
        # Export logs to verify the changes
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Check if the log export was successful
    
        # Validate the tunnel mode for on-trusted networks
        result = conftest.sanity_zcc.validate_tunnel_mode_from_logs(tunnel_version=on_trusted_tunnel_mode)
        assert result[0], result[1]  # Check if the tunnel mode is correct
    
        # Validate the network status for on-trusted networks
        result = conftest.zcc.validate_network_status_on_zcc(trusted_network=True)
        assert result[0], result[1]  # Check if the network status is correct

        

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 1 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288104')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_tunnel_1_to_tunnel_2_tls(self) -> None:
        """
        Test case to validate trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.
    
        This test case validates the trusted network hostname IP by switching between off-trusted (tunnel 1) and on-trusted (tunnel 2 TLS) tunnel modes.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate trusted network hostname IP with off-trusted tunnel mode as 'tunnel_1' and on-trusted tunnel mode as 'tunnel_2_tls'
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 2 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288105')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_tunnel_1_to_tunnel_2_dtls(self) -> None:
        """
        Validates the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the trusted network hostname IP by switching between off-trusted (Tunnel 1) and on-trusted (Tunnel 2 DTLS) tunnel modes.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        
        # Validate the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes
        # Off-trusted tunnel mode is set to 'tunnel_1' and on-trusted tunnel mode is set to 'tunnel_2_dtls'
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 3 - OffTrusted: Tunnel 1, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288106')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_tunnel_1_to_twlp(self):
        """
        Validates the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the trusted network hostname IP by switching between off-trusted (Tunnel 1) and on-trusted (TWLP) tunnel modes.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate the trusted network hostname IP
        # with off-trusted tunnel mode as 'tunnel_1' and on-trusted tunnel mode as 'twlp'
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 4 - OffTrusted: Tunnel 1, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288107')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_tunnel_1_to_enforce_proxy(self) -> None:
        """
        Validates the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.
    
        This test case validates the trusted network hostname IP by switching between off-trusted (tunnel 1) and on-trusted (enforce proxy) tunnel modes.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate the trusted network hostname IP
        # with off-trusted tunnel mode as 'tunnel_1' and on-trusted tunnel mode as 'enforce_proxy'
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 5 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288108')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_tunnel_2_tls_to_tunnel_1(self) -> None:
        """
        Validates the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.
    
        This test case validates the trusted network hostname IP by switching between off-trusted (Tunnel 2 TLS) and on-trusted (Tunnel 1) tunnel modes.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Call the function to validate the trusted network hostname IP
        # with off-trusted tunnel mode as 'tunnel_2_tls' and on-trusted tunnel mode as 'tunnel_1'
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 6 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288109')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_tunnel_2_tls_to_tunnel_2_dtls(self) -> None:
        """
        Test case to validate trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.
    
        This test case validates the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.
        It uses the validate_trusted_network_hostname_ip function to perform the validation.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate trusted network hostname IP
        # with off-trusted tunnel mode as 'tunnel_2_tls' and on-trusted tunnel mode as 'tunnel_2_dtls'
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 7 - OffTrusted: Tunnel 2 TLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288110')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_tunnel_2_tls_to_twlp(self):
        """
        Test case to validate trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the trusted network hostname IP by switching between off-trusted tunnel mode 'tunnel_2_tls' and on-trusted tunnel mode 'twlp'.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate trusted network hostname IP with off-trusted tunnel mode 'tunnel_2_tls' and on-trusted tunnel mode 'twlp'
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 8 - OffTrusted: Tunnel 2 TLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288111')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_tunnel_2_tls_to_enforce_proxy(self):
        """
        Test case to validate trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the trusted network hostname IP by switching between off-trusted tunnel mode 'tunnel_2_tls' 
        and on-trusted tunnel mode 'enforce_proxy'.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate trusted network hostname IP with off-trusted tunnel mode 'tunnel_2_tls' 
        # and on-trusted tunnel mode 'enforce_proxy'
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 9 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288112')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_hostname_ip_tunnel_2_dtls_to_tunnel_1(self) -> None:
        """
        Validates the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.
    
        This test case validates the trusted network hostname IP by switching between off-trusted (Tunnel 2 DTLS) and on-trusted (Tunnel 1) tunnel modes.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the validation steps fail.
        """
        
        # Validate the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 10 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288113')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_tunnel_2_dtls_to_tunnel_2_tls(self):
        """
        Test case to validate trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the trusted network hostname IP by switching between off-trusted (Tunnel 2 DTLS) and on-trusted (Tunnel 2 TLS) tunnel modes.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        
        # Validate trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes
        # Off-trusted tunnel mode is set to 'tunnel_2_dtls' and on-trusted tunnel mode is set to 'tunnel_2_tls'
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 11 - OffTrusted: Tunnel 2 DTLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288114')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_tunnel_2_dtls_to_twlp(self):
        """
        Validates the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the trusted network hostname IP by switching between off-trusted tunnel mode 'tunnel_2_dtls' and on-trusted tunnel mode 'twlp'.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate the trusted network hostname IP
        # with off-trusted tunnel mode as 'tunnel_2_dtls' and on-trusted tunnel mode as 'twlp'
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 12 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288115')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_tunnel_2_dtls_to_enforce_proxy(self) -> None:
        """
        Validates the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.
    
        This test case validates the trusted network hostname IP by switching between off-trusted tunnel mode 'tunnel_2_dtls' 
        and on-trusted tunnel mode 'enforce_proxy'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Call the function to validate the trusted network hostname IP with off-trusted tunnel mode 'tunnel_2_dtls' 
        # and on-trusted tunnel mode 'enforce_proxy'
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 13 - OffTrusted: TWLP, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288116')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_twlp_to_tunnel_1(self):
        """
        Test case to validate trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the trusted network hostname IP by switching between off-trusted (TWLP) and on-trusted (Tunnel 1) tunnel modes.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate trusted network hostname IP with off-trusted tunnel mode as TWLP and on-trusted tunnel mode as Tunnel 1
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 14 - OffTrusted: TWLP, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288117')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_twlp_to_tunnel_2_tls(self):
        """
        Validates the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the trusted network hostname IP by switching between off-trusted (TWLP) and on-trusted (Tunnel 2 TLS) tunnel modes.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate the trusted network hostname IP
        # with off-trusted tunnel mode as TWLP and on-trusted tunnel mode as Tunnel 2 TLS
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 15 - OffTrusted: TWLP, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288118')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_twlp_to_tunnel_2_dtls(self) -> None:
        """
        This test case validates the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.
        
        The off-trusted tunnel mode is set to TWLP and the on-trusted tunnel mode is set to Tunnel 2 DTLS.
        
        Args:
            None
        
        Returns:
            None
        
        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate the trusted network hostname IP
        # with off-trusted tunnel mode as TWLP and on-trusted tunnel mode as Tunnel 2 DTLS
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 16 - OffTrusted: TWLP, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288119')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_twlp_to_enforce_proxy(self):
        """
        Validates the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the trusted network hostname IP by switching between off-trusted (TWLP) and on-trusted (Enforce Proxy) tunnel modes.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate the trusted network hostname IP
        # with off-trusted tunnel mode as 'twlp' and on-trusted tunnel mode as 'enforce_proxy'
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 17 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288120')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_enforce_proxy_to_tunnel_1(self) -> None:
        """
        Validates the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.
    
        This test case validates the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.
        It uses the 'enforce_proxy' mode for off-trusted and 'tunnel_1' mode for on-trusted.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Call the function to validate the trusted network hostname IP
        # with 'enforce_proxy' mode for off-trusted and 'tunnel_1' mode for on-trusted
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 18 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288121')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_enforce_proxy_to_tunnel_2_tls(self):
        """
        Test case to validate trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.
        It uses the `validate_trusted_network_hostname_ip` method to perform the validation.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the method to validate trusted network hostname IP with off-trusted tunnel mode as 'enforce_proxy' and on-trusted tunnel mode as 'tunnel_2_tls'
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 19 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288122')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_enforce_proxy_to_tunnel_2_dtls(self):
        """
        Test case to validate trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the trusted network hostname IP by switching between off-trusted (enforce proxy) and on-trusted (tunnel 2 DTLS) tunnel modes.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate trusted network hostname IP with off-trusted tunnel mode as 'enforce_proxy' and on-trusted tunnel mode as 'tunnel_2_dtls'
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate in FP trusted network criteria - Hostname & IP - Case 20 - OffTrusted: Enforce Proxy, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288123')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_hostname_ip_enforce_proxy_to_twlp(self):
        """
        Validates the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the trusted network hostname IP by switching between off-trusted and on-trusted tunnel modes.
        It uses the 'enforce_proxy' tunnel mode for off-trusted networks and 'twlp' tunnel mode for on-trusted networks.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate the trusted network hostname IP
        # with 'enforce_proxy' as the off-trusted tunnel mode and 'twlp' as the on-trusted tunnel mode
        self.validate_trusted_network_hostname_ip(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='twlp')

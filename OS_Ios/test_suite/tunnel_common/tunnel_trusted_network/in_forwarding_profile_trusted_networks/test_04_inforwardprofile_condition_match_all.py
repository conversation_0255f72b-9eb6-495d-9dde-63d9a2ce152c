import pytest, time, allure
from common_lib.Custom_Markers import add_markers,zcc_mark_version
# from OS_Ios.test_suite.zcc_misc.zcc_trusted_network import conftest
from OS_Ios.test_suite.tunnel_common.tunnel_trusted_network.in_forwarding_profile_trusted_networks import conftest
import time

class TestInForwardingProfileTrustedNetworksConditionMatchAll:

    @allure.title("Setup Class")
    def setup_class(self):
        """
        Setup method for the test class.

        This method logs in to ZCC with the provided credentials and settings, and validates that the login was successful.
        It also validates that Z<PERSON> is logged in.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If the login or validation fails.
        """
        # Login to ZCC with the provided credentials and settings
        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],  # ZIA user ID
            zia_password=conftest.config["ZIA_USER_PASSWORD"],  # ZIA user password
            zpa_user=conftest.config["ZPA_USER_ID"],  # ZPA user ID
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],  # ZPA user password
            zia_saml_login=True,  # Use ZIA SAML login
        )
        # Assert that the login was successful
        assert result[0], result[1]
        
        # Validate that ZCC is logged in
        result = conftest.zcc.validate_zcc_logged_in()
        # Assert that the validation was successful
        assert result[0], result[1]

    def teardown_class(self):
        """
        Teardown method for the test class.

        This method edits the forwarding profile and logs out of ZCC.

        Args:
            None

        Returns:
            None

        Raises:
            None
        """
        conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile()
        conftest.zcc.logout()

    def validate_trusted_network_match_all_condition(self, off_trusted_tunnel_mode: str, on_trusted_tunnel_mode: str):
        """
        Validates the 'Match All' condition for trusted networks by switching between off-trusted and on-trusted tunnel modes.

        This method performs the following steps:
        1. Edits the forwarding profile to set the off-trusted tunnel mode.
        2. Updates the policy and waits for 60 seconds.
        3. Exports logs to verify the changes.
        4. Validates the tunnel mode from the logs.
        5. Validates the network status for off-trusted networks.
        6. Fetches the IP address of the hostname.
        7. Edits the forwarding profile to set the on-trusted tunnel mode, DNS servers, and 'Match All' condition.
        8. Updates the policy and waits for 60 seconds.
        9. Exports logs to verify the changes.
        10. Validates the tunnel mode from the logs.
        11. Validates the network status for on-trusted networks.

        Args:
            off_trusted_tunnel_mode (str): The tunnel mode to use for off-trusted networks.
            on_trusted_tunnel_mode (str): The tunnel mode to use for on-trusted networks.

        Returns:
            None
        """
        
        result = conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode=off_trusted_tunnel_mode)
        assert result[0], result[1]

        conftest.sanity_zcc.log_ops.ignore_logs_before_this_time_point()

        result = conftest.zcc.update_policy(sleep_time=20)
        assert result[0], result[1]

        # Export logs to verify the changes
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful

        result = conftest.sanity_zcc.validate_tunnel_mode_from_logs(tunnel_version=off_trusted_tunnel_mode)
        assert result[0], result[1]

        # Validate the network status for off-trusted networks
        result = conftest.zcc.validate_network_status_on_zcc(trusted_network=False)
        assert result[0], result[1]  # Assert that the network status is correct

        result = conftest.sys_ops_obj.fetch_ip(application=conftest.HOSTNAME)
        assert result[0], result[1]

        resolved_ip_for_host = result[-1]
        hostname_ip = conftest.HOSTNAME + ',' + resolved_ip_for_host

        result = conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile(
            trusted_network="inforwardprofile",
            hostname_ip=hostname_ip,
            dns_servers=','.join(conftest.DNS_SERVER),
            trusted_condition_match='all',
            tunnel_mode={
                'offTrusted':off_trusted_tunnel_mode,
                'onTrusted':on_trusted_tunnel_mode
            }
        )
        assert result[0], result[1]

        conftest.sanity_zcc.log_ops.ignore_logs_before_this_time_point()

        result = conftest.zcc.update_policy(sleep_time=20)
        assert result[0], result[1]

        # Export logs to verify the changes
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful

        result = conftest.sanity_zcc.validate_tunnel_mode_from_logs(tunnel_version=on_trusted_tunnel_mode)
        assert result[0], result[1]

        # Validate the network status for on-trusted networks
        result = conftest.zcc.validate_network_status_on_zcc(trusted_network=True)
        assert result[0], result[1]  # Assert that the network status is correct

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 1 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288164')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)  # Skip test if ZCC version is less than 3.7
    def test_dns_search_domain_tunnel_1_to_tunnel_2_tls(self) -> None:
        """
        Validates trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes.
    
        This test case covers the scenario where the off-trusted tunnel mode is set to 'tunnel_1' and the on-trusted tunnel mode is set to 'tunnel_2_tls'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Validate trusted network DNS search domain with specified tunnel modes
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_tls')
    

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 2 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288165')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7) # Skip test if ZCC version is less than 3.7
    def test_dns_search_domain_tunnel_1_to_tunnel_2_dtls(self) -> None:
        """
        Validates trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes.
    
        This test case validates the DNS search domain by switching between 'tunnel_1' and 'tunnel_2_dtls' modes.
        It ensures the network status, DNS servers, and tunnel modes are correctly updated in the logs and ZCC.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Validate trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_dtls')
    

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 3 - OffTrusted: Tunnel 1, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288166') 
    @add_markers("regression","sanity") # Mark test for regression and sanity suites
    @zcc_mark_version(min=3.7) # Skip test if ZCC version is less than 3.7
    def test_dns_search_domain_tunnel_1_to_twlp(self) -> None:
        """
        Validates trusted network DNS search domain by switching between 'tunnel_1' and 'twlp' trusted tunnel modes.
    
        This test case covers the scenario where the trusted network criteria is set to 'Match All Condition Selected'
        and the off-trusted tunnel mode is 'tunnel_1' and the on-trusted tunnel mode is 'twlp'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='twlp')
    

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 4 - OffTrusted: Tunnel 1, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288167')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_1_to_enforce_proxy(self):
        """
        Validates trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes.
    
        This test case covers the scenario where the off-trusted tunnel mode is set to 'tunnel_1' and the on-trusted tunnel mode is set to 'enforce_proxy'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 5 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288168')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_2_tls_to_tunnel_1(self) -> None:
        """
        Validates trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes.
    
        This test case covers the scenario where the off-trusted tunnel mode is set to 'tunnel_2_tls' and the on-trusted tunnel mode is set to 'tunnel_1'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Validate trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_1')
    

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 6 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288169')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_2_tls_to_tunnel_2_dtls(self) -> None:
        """
        Validates trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes.
    
        This test case covers the scenario where the off-trusted tunnel mode is set to 'tunnel_2_tls' and the on-trusted tunnel mode is set to 'tunnel_2_dtls'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Validate trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_2_dtls')
    

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 7 - OffTrusted: Tunnel 2 TLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288170')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_2_tls_to_twlp(self) -> None:
        """
        Validates trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes.
    
        This test case covers the scenario where the off-trusted tunnel mode is set to 'tunnel_2_tls' and the on-trusted tunnel mode is set to 'twlp'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Validate trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='twlp')
    

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 8 - OffTrusted: Tunnel 2 TLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288171')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_2_tls_to_enforce_proxy(self):
        """
        Validates trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes.
    
        This test case covers the scenario where the off-trusted tunnel mode is set to 'tunnel_2_tls' and the on-trusted tunnel mode is set to 'enforce_proxy'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 9 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288172')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_2_dtls_to_tunnel_1(self) -> None:
        """
        Validates trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes.
    
        This test case covers the scenario where the off-trusted tunnel mode is set to 'tunnel_2_dtls' and the on-trusted tunnel mode is set to 'tunnel_1'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Validate trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_1')
    

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 10 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288173')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_2_dtls_to_tunnel_2_tls(self) -> None:
        """
        Validates trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes.
    
        This test case covers the scenario where the off-trusted tunnel mode is set to 'tunnel_2_dtls' and the on-trusted tunnel mode is set to 'tunnel_2_tls'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Validate trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_2_tls')
    

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 11 - OffTrusted: Tunnel 2 DTLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288174')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_2_dtls_to_twlp(self):
        """
        Validates trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes.
    
        This test case covers the scenario where the off-trusted tunnel mode is set to 'tunnel_2_dtls' and the on-trusted tunnel mode is set to 'twlp'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 12 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288175')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_tunnel_2_dtls_to_enforce_proxy(self):
        """
        Validates trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes.
    
        This test case covers the scenario where the off-trusted tunnel mode is set to 'tunnel_2_dtls' and the on-trusted tunnel mode is set to 'enforce_proxy'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 13 - OffTrusted: TWLP, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288176')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_twlp_to_tunnel_1(self) -> None:
        """
        Validates trusted network DNS search domain by switching between TWLP and Tunnel 1 modes.
    
        This test case validates the trusted network DNS search domain by switching between 
        off-trusted tunnel mode (TWLP) and on-trusted tunnel mode (Tunnel 1) and verifying 
        the network status, DNS servers, and tunnel modes in the logs and ZCC.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            Exception: If the test case fails.
        """
        # Validate trusted network DNS search domain by switching between TWLP and Tunnel 1 modes
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 14 - OffTrusted: TWLP, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288177')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_twlp_to_tunnel_2_tls(self) -> None:
        """
        Validates trusted network DNS search domain by switching between TWLP and Tunnel 2 TLS trusted tunnel modes.
    
        This test case covers the scenario where the trusted network criteria is set to 'Match All Condition Selected'
        and the off-trusted tunnel mode is set to TWLP, while the on-trusted tunnel mode is set to Tunnel 2 TLS.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Validate trusted network DNS search domain by switching between off-trusted and on-trusted tunnel modes
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_tls')
    

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 15 - OffTrusted: TWLP, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288178')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_twlp_to_tunnel_2_dtls(self) -> None:
        """
        Validates trusted network DNS search domain by switching between TWLP and Tunnel 2 DTLS modes.
    
        This test case validates the trusted network DNS search domain by switching between 
        off-trusted tunnel mode (TWLP) and on-trusted tunnel mode (Tunnel 2 DTLS) and verifying 
        the network status, DNS servers, and tunnel modes in the logs and ZCC.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Validate trusted network DNS search domain by switching between TWLP and Tunnel 2 DTLS modes
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 16 - OffTrusted: TWLP, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288179')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_twlp_to_enforce_proxy(self) -> None:
        """
        Validates trusted network DNS search domain by switching between TWLP and Enforce Proxy tunnel modes.
    
        This test case covers the scenario where the trusted network criteria is set to 'Match All Condition Selected'
        and the tunnel mode is switched from TWLP to Enforce Proxy.
    
        Args:
            None
    
        Returns:
            None
        """
        # Validate trusted network DNS search domain by switching between TWLP and Enforce Proxy tunnel modes
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 17 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288180')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_enforce_proxy_to_tunnel_1(self) -> None:
        """
        Validates trusted network DNS search domain by switching between 'enforce_proxy' and 'tunnel_1' trusted tunnel modes.
    
        This test case covers the scenario where the trusted network criteria is set to 'Match All Condition Selected' 
        and the off-trusted tunnel mode is 'enforce_proxy' while the on-trusted tunnel mode is 'tunnel_1'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Validate trusted network DNS search domain by switching between 'enforce_proxy' and 'tunnel_1' modes
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_1')
    

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 18 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288181')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_enforce_proxy_to_tunnel_2_tls(self) -> None:
        """
        Validates trusted network DNS search domain by switching between 'enforce_proxy' and 'tunnel_2_tls' 
        trusted tunnel modes and verifying the network status, DNS servers, and tunnel modes in the logs and ZCC.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Validate trusted network DNS search domain by switching between 'enforce_proxy' and 'tunnel_2_tls' modes
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_tls')
    

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 19 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288182')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_enforce_proxy_to_tunnel_2_dtls(self) -> None:
        """
        Validates trusted network DNS search domain by switching between 'enforce_proxy' and 'tunnel_2_dtls' trusted tunnel modes.
    
        This test case covers the scenario where the trusted network criteria is set to 'Match All Condition Selected' 
        and the off-trusted tunnel mode is 'Enforce Proxy' while the on-trusted tunnel mode is 'Tunnel 2 DTLS'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Validate DNS search domain by switching between off-trusted and on-trusted tunnel modes
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_dtls')
    

    @allure.title("Validate in FP trusted network criteria - Match All Condition Selected - Case 20 - OffTrusted: Enforce Proxy, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288183')
    @add_markers("regression","sanity")
    @zcc_mark_version(min=3.7)
    def test_dns_search_domain_enforce_proxy_to_twlp(self) -> None:
        """
        Validates trusted network DNS search domain by switching between 'enforce_proxy' and 'twlp' tunnel modes.
    
        This test case covers the scenario where the trusted network criteria is set to 'Match All Condition Selected'
        and the tunnel modes are switched from 'enforce_proxy' to 'twlp'.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            Exception: If the test fails due to any unexpected error.
        """
        # Validate trusted network DNS search domain by switching between 'enforce_proxy' and 'twlp' tunnel modes
        self.validate_trusted_network_match_all_condition(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='twlp')
    

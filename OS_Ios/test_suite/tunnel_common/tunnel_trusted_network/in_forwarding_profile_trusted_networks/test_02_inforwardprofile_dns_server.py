import pytest, time, allure
from common_lib.Custom_Markers import add_markers,zcc_mark_version
# from OS_Ios.test_suite.zcc_misc.zcc_trusted_network import conftest
from OS_Ios.test_suite.tunnel_common.tunnel_trusted_network.in_forwarding_profile_trusted_networks import conftest
import time

class TestInForwardingProfileTrustedNetworksDnsServer:

    @allure.title("Setup Class")
    def setup_class(self):
        """
        Setup method for the test class.

        This method logs in to ZCC with the provided credentials and settings, and validates that the login was successful.
        It also validates that <PERSON><PERSON> is logged in.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If the login or validation fails.
        """
        # Login to ZCC with the provided credentials and settings
        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],  # ZIA user ID
            zia_password=conftest.config["ZIA_USER_PASSWORD"],  # ZIA user password
            zpa_user=conftest.config["ZPA_USER_ID"],  # ZPA user ID
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],  # ZPA user password
            zia_saml_login=True,  # Use ZIA SAML login
        )
        # Assert that the login was successful
        assert result[0], result[1]
        
        # Validate that ZCC is logged in
        result = conftest.zcc.validate_zcc_logged_in()
        # Assert that the validation was successful
        assert result[0], result[1]

    def teardown_class(self):
        """
        Teardown method for the test class.

        This method edits the forwarding profile and logs out of ZCC.

        Args:
            None

        Returns:
            None

        Raises:
            None
        """
        conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile()
        conftest.zcc.logout()

    def validate_trusted_network_dns_server(self, off_trusted_tunnel_mode: str, on_trusted_tunnel_mode: str) -> None:
        """
        Validates the trusted network DNS server by switching between off-trusted and on-trusted tunnel modes.

        This method performs the following steps:
        1. Edits the forwarding profile to set the off-trusted tunnel mode.
        2. Updates the policy and exports logs to verify the changes.
        3. Validates the tunnel mode from logs for off-trusted networks.
        4. Validates the network status on ZCC for off-trusted networks.
        5. Edits the forwarding profile to set the on-trusted tunnel mode with DNS servers.
        6. Updates the policy and exports logs to verify the changes.
        7. Validates the tunnel mode from logs for on-trusted networks.
        8. Validates the network status on ZCC for on-trusted networks.

        Args:
            off_trusted_tunnel_mode (str): The tunnel mode to use for off-trusted networks.
            on_trusted_tunnel_mode (str): The tunnel mode to use for on-trusted networks.

        Returns:
            None
        """
    
        # Edit the forwarding profile to set the tunnel mode for off-trusted networks
        result = conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode=off_trusted_tunnel_mode)
        assert result[0], result[1]  # Assert that the edit operation was successful

        conftest.sanity_zcc.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy to apply the changes
        result = conftest.zcc.update_policy(sleep_time=20)
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs to verify the changes
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Validate the tunnel mode from logs
        result = conftest.sanity_zcc.validate_tunnel_mode_from_logs(tunnel_version=off_trusted_tunnel_mode)
        assert result[0], result[1]  # Assert that the tunnel mode is correct
    
        # Validate the network status on ZCC for off-trusted networks
        result = conftest.zcc.validate_network_status_on_zcc(trusted_network=False)
        assert result[0], result[1]  # Assert that the network status is correct
    
        # Edit the forwarding profile to set the tunnel mode for on-trusted networks and DNS servers
        result = conftest.sanity_zcc.ma_app_profile.forwarding_profile.edit_forwarding_profile(
            trusted_network="inforwardprofile",
            dns_servers=','.join(conftest.DNS_SERVER),
            tunnel_mode={
                'offTrusted': off_trusted_tunnel_mode,
                'onTrusted': on_trusted_tunnel_mode
            }
        )
        assert result[0], result[1]  # Assert that the edit operation was successful

        conftest.sanity_zcc.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy to apply the changes
        result = conftest.zcc.update_policy(sleep_time=20)
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs to verify the changes
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Validate the tunnel mode from logs
        result = conftest.sanity_zcc.validate_tunnel_mode_from_logs(tunnel_version=on_trusted_tunnel_mode)
        assert result[0], result[1]  # Assert that the tunnel mode is correct
    
        # Validate the network status on ZCC for on-trusted networks
        result = conftest.zcc.validate_network_status_on_zcc(trusted_network=True)
        assert result[0], result[1]  # Assert that the network status is correct

    # -----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 1 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288084')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_tunnel_1_to_tunnel_2_tls(self) -> None:
        """
        Test case to validate trusted network criteria for DNS Server.
    
        This test case validates the trusted network criteria for DNS Server by setting 
        off-trusted tunnel mode to 'tunnel_1' and on-trusted tunnel mode to 'tunnel_2_tls'.
    
        Args:
            None
    
        Returns:
            None
        """
        
        # Validate trusted network DNS server with off-trusted tunnel mode as 'tunnel_1' and on-trusted tunnel mode as 'tunnel_2_tls'
        self.validate_trusted_network_dns_server(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 2 - OffTrusted: Tunnel 1, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288085')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_tunnel_1_to_tunnel_2_dtls(self) -> None:
        """
        Test case to validate trusted network DNS server criteria.

        This test case validates the trusted network DNS server criteria by setting 
        off-trusted tunnel mode to 'tunnel_1' and on-trusted tunnel mode to 'tunnel_2_dtls'.

        Args:
            None

        Returns:
            None
        """
        
        # Validate trusted network DNS server criteria with off-trusted tunnel mode as 'tunnel_1' and on-trusted tunnel mode as 'tunnel_2_dtls'
        self.validate_trusted_network_dns_server(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 3 - OffTrusted: Tunnel 1, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288086')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_tunnel_1_to_twlp(self) -> None:
        """
        Test case to validate trusted network DNS server criteria in forwarding profile.
        
        This test case validates the trusted network DNS server criteria in forwarding profile 
        by setting off-trusted tunnel mode to 'tunnel_1' and on-trusted tunnel mode to 'twlp'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Call the validate_trusted_network_dns_server method to perform the validation
        # with off-trusted tunnel mode set to 'tunnel_1' and on-trusted tunnel mode set to 'twlp'
        self.validate_trusted_network_dns_server(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 4 - OffTrusted: Tunnel 1, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288087')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_tunnel_1_to_enforce_proxy(self):
        """
        Test case to validate trusted network DNS server criteria.
    
        This test case validates the trusted network DNS server criteria by setting 
        off-trusted tunnel mode to 'tunnel_1' and on-trusted tunnel mode to 'enforce_proxy'.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            Exception: If the validation fails.
        """
        # Call the function to validate trusted network DNS server criteria
        # with off-trusted tunnel mode as 'tunnel_1' and on-trusted tunnel mode as 'enforce_proxy'
        self.validate_trusted_network_dns_server(off_trusted_tunnel_mode='tunnel_1', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 5 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288088')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_tunnel_2_tls_to_tunnel_1(self) -> None:
        """
        Validates the trusted network DNS server criteria for a specific tunnel mode configuration.
    
        This test case validates the trusted network DNS server criteria when the off-trusted tunnel mode is set to 'tunnel_2_tls' and the on-trusted tunnel mode is set to 'tunnel_1'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Call the validate_trusted_network_dns_server method to perform the validation
        # with the specified off-trusted and on-trusted tunnel modes
        self.validate_trusted_network_dns_server(
            # Set the off-trusted tunnel mode to 'tunnel_2_tls'
            off_trusted_tunnel_mode='tunnel_2_tls', 
            # Set the on-trusted tunnel mode to 'tunnel_1'
            on_trusted_tunnel_mode='tunnel_1'
        )

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 6 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288089')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_tunnel_2_tls_to_tunnel_2_dtls(self):
        """
        Validate in FP trusted network criteria - DNS Server - Case 6 - OffTrusted: Tunnel 2 TLS, OnTrusted: Tunnel 2 DTLS.
        
        This test case validates the trusted network criteria for DNS Server in the forwarding profile.
        It checks the tunnel mode for off-trusted and on-trusted networks.
        
        Args:
            None
        
        Returns:
            None
        
        Raises:
            Exception: If the test case fails.
        """ 
        # Validate the trusted network DNS server with off-trusted tunnel mode as 'tunnel_2_tls' and on-trusted tunnel mode as 'tunnel_2_dtls'
        self.validate_trusted_network_dns_server(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 7 - OffTrusted: Tunnel 2 TLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288090')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_tunnel_2_tls_to_twlp(self) -> None:
        """
        Test case to validate trusted network DNS server criteria.
    
        This test case validates the trusted network DNS server criteria by setting 
        off-trusted tunnel mode to 'tunnel_2_tls' and on-trusted tunnel mode to 'twlp'.
    
        Args:
            None
    
        Returns:
            None
        """
        # Call the function to validate trusted network DNS server criteria
        # with off-trusted tunnel mode as 'tunnel_2_tls' and on-trusted tunnel mode as 'twlp'
        self.validate_trusted_network_dns_server(off_trusted_tunnel_mode='tunnel_2_tls', on_trusted_tunnel_mode='twlp')

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 8 - OffTrusted: Tunnel 2 TLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288091')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_tunnel_2_tls_to_enforce_proxy(self) -> None:
        """
        Validates the trusted network DNS server criteria for a specific tunnel mode configuration.
    
        This test case validates the trusted network DNS server criteria when the off-trusted tunnel mode is set to 'tunnel_2_tls' and the on-trusted tunnel mode is set to 'enforce_proxy'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Call the validate_trusted_network_dns_server method to perform the validation
        # with the specified tunnel mode configuration
        self.validate_trusted_network_dns_server(
            # Set the off-trusted tunnel mode to 'tunnel_2_tls'
            off_trusted_tunnel_mode='tunnel_2_tls', 
            # Set the on-trusted tunnel mode to 'enforce_proxy'
            on_trusted_tunnel_mode='enforce_proxy'
        )

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 9 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288092')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_tunnel_2_dtls_to_tunnel_1(self):
        """
        Validate in FP trusted network criteria - DNS Server - Case 9 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 1
        
        This test case validates the trusted network criteria for DNS Server in the forwarding profile.
        It checks the off-trusted tunnel mode as 'tunnel_2_dtls' and on-trusted tunnel mode as 'tunnel_1'.
        
        Args:
            None
        
        Returns:
            None
        
        Raises:
            Exception: If the test case fails.
        """
        # Validate the trusted network DNS server with off-trusted tunnel mode as 'tunnel_2_dtls' and on-trusted tunnel mode as 'tunnel_1'
        self.validate_trusted_network_dns_server(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 10 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288093')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_tunnel_2_dtls_to_tunnel_2_tls(self):
        """
        Test case to validate trusted network DNS server criteria.
    
        This test case validates the trusted network DNS server criteria by setting 
        off-trusted tunnel mode to 'tunnel_2_dtls' and on-trusted tunnel mode to 'tunnel_2_tls'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Call the function to validate trusted network DNS server criteria
        # with off-trusted tunnel mode as 'tunnel_2_dtls' and on-trusted tunnel mode as 'tunnel_2_tls'
        self.validate_trusted_network_dns_server(off_trusted_tunnel_mode='tunnel_2_dtls', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 11 - OffTrusted: Tunnel 2 DTLS, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288094')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_tunnel_2_dtls_to_twlp(self) -> None:
        """
        Validates the trusted network DNS server criteria for a specific tunnel mode configuration.
    
        This test case validates the trusted network DNS server criteria when the off-trusted tunnel mode is set to 'tunnel_2_dtls' and the on-trusted tunnel mode is set to 'twlp'.
    
        Args:
            self: The test class instance.
    
        Returns:
            None
        """
        # Call the validate_trusted_network_dns_server method to perform the validation
        # with the specified tunnel mode configuration
        self.validate_trusted_network_dns_server(
            # Set the off-trusted tunnel mode to 'tunnel_2_dtls'
            off_trusted_tunnel_mode='tunnel_2_dtls', 
            # Set the on-trusted tunnel mode to 'twlp'
            on_trusted_tunnel_mode='twlp'
        )

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 12 - OffTrusted: Tunnel 2 DTLS, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288095')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_tunnel_2_dtls_to_enforce_proxy(self) -> None:
        """
        Validates the trusted network DNS server criteria for a specific tunnel mode configuration.

        This test case validates the trusted network DNS server criteria when the off-trusted tunnel mode is set to 'tunnel_2_dtls' and the on-trusted tunnel mode is set to 'enforce_proxy'.

        Args:
            None

        Returns:
            None
        """
        
        # Call the validate_trusted_network_dns_server method to perform the validation
        # with the specified off-trusted and on-trusted tunnel modes
        self.validate_trusted_network_dns_server(
            # Set the off-trusted tunnel mode to 'tunnel_2_dtls'
            off_trusted_tunnel_mode='tunnel_2_dtls', 
            # Set the on-trusted tunnel mode to 'enforce_proxy'
            on_trusted_tunnel_mode='enforce_proxy'
        )

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 13 - OffTrusted: TWLP, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288096')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_twlp_to_tunnel_1(self):
        """
        Validates the trusted network DNS server criteria in the forwarding profile.

        This test case validates the DNS server configuration in the forwarding profile
        when the off-trusted tunnel mode is set to TWLP and the on-trusted tunnel mode is set to Tunnel 1.

        Args:
            None

        Returns:
            None

        Raises:
            Exception: If the validation fails.
        """
        # Call the function to validate the trusted network DNS server criteria
        # with off-trusted tunnel mode as TWLP and on-trusted tunnel mode as Tunnel 1
        self.validate_trusted_network_dns_server(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 14 - OffTrusted: TWLP, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288097')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_twlp_to_tunnel_2_tls(self) -> None:
        """
        Validates the trusted network DNS server criteria in the forwarding profile.
    
        This test case validates the DNS server configuration in the forwarding profile
        when the off-trusted tunnel mode is set to TWLP and the on-trusted tunnel mode
        is set to Tunnel 2 TLS.
    
        Args:
            None
    
        Returns:
            None
        """
        # Call the validate_trusted_network_dns_server method to perform the validation
        # with the specified tunnel modes
        self.validate_trusted_network_dns_server(
            # Set the off-trusted tunnel mode to TWLP
            off_trusted_tunnel_mode='twlp',
            # Set the on-trusted tunnel mode to Tunnel 2 TLS
            on_trusted_tunnel_mode='tunnel_2_tls'
        )

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 15 - OffTrusted: TWLP, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288098')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_twlp_to_tunnel_2_dtls(self):
        """
        Test case to validate trusted network DNS server criteria.

        This test case validates the trusted network DNS server criteria by setting 
        off-trusted tunnel mode to TWLP and on-trusted tunnel mode to Tunnel 2 DTLS.

        Args:
            None

        Returns:
            None

        Raises:
            Exception: If the test case fails.
        """
        # Call the function to validate trusted network DNS server criteria
        # with off-trusted tunnel mode as TWLP and on-trusted tunnel mode as Tunnel 2 DTLS
        self.validate_trusted_network_dns_server(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 16 - OffTrusted: TWLP, OnTrusted: Enforce Proxy")
    @pytest.mark.xray('QA-288099')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_twlp_to_enforce_proxy(self):
        """
        Validates the trusted network DNS Server by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the DNS Server configuration in the forwarding profile by switching between TWLP (off-trusted) and Enforce Proxy (on-trusted) tunnel modes.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If the validation fails.
        """
        
        # Call the function to validate the trusted network DNS Server
        # with off-trusted tunnel mode set to TWLP and on-trusted tunnel mode set to Enforce Proxy
        self.validate_trusted_network_dns_server(off_trusted_tunnel_mode='twlp', on_trusted_tunnel_mode='enforce_proxy')

    #-----------------------------------------------------------------------------------------------------------------------------------

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 17 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 1")
    @pytest.mark.xray('QA-288100')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_enforce_proxy_to_tunnel_1(self):
        """
        Validates the trusted network DNS Server by switching between off-trusted and on-trusted tunnel modes.
    
        This test case covers the scenario where the off-trusted tunnel mode is set to 'enforce_proxy' and the on-trusted tunnel mode is set to 'tunnel_1'.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the validation steps fail.
        """
        # Call the function to validate the trusted network DNS Server
        # with off-trusted tunnel mode as 'enforce_proxy' and on-trusted tunnel mode as 'tunnel_1'
        self.validate_trusted_network_dns_server(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_1')

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 18 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 TLS")
    @pytest.mark.xray('QA-288101')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_enforce_proxy_to_tunnel_2_tls(self):
        """
        Validates the trusted network DNS Server by switching between off-trusted and on-trusted tunnel modes.

        This test case covers the scenario where the off-trusted tunnel mode is set to 'enforce_proxy' and the on-trusted tunnel mode is set to 'tunnel_2_tls'.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        
        # Call the function to validate the trusted network DNS Server
        # with off-trusted tunnel mode as 'enforce_proxy' and on-trusted tunnel mode as 'tunnel_2_tls'
        self.validate_trusted_network_dns_server(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_tls')

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 19 - OffTrusted: Enforce Proxy, OnTrusted: Tunnel 2 DTLS")
    @pytest.mark.xray('QA-288102')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_enforce_proxy_to_tunnel_2_dtls(self):
        """
        Validates the trusted network DNS Server by switching between off-trusted and on-trusted tunnel modes.

        This test case validates the DNS Server functionality by switching between off-trusted (Enforce Proxy) and on-trusted (Tunnel 2 DTLS) tunnel modes.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        
        # Call the function to validate the trusted network DNS Server
        # with off-trusted tunnel mode as Enforce Proxy and on-trusted tunnel mode as Tunnel 2 DTLS
        self.validate_trusted_network_dns_server(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='tunnel_2_dtls')

    @allure.title("Validate in FP trusted network criteria - DNS Server - Case 20 - OffTrusted: Enforce Proxy, OnTrusted: TWLP")
    @pytest.mark.xray('QA-288103')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_dns_server_enforce_proxy_to_twlp(self):
        """
        Validates the trusted network DNS Server by switching between off-trusted and on-trusted tunnel modes.

        This test case covers the scenario where the off-trusted tunnel mode is set to 'enforce_proxy' and the on-trusted tunnel mode is set to 'twlp'.

        Args:
            None

        Returns:
            None

        Raises:
            AssertionError: If any of the validation steps fail.
        """
        
        # Call the function to validate the trusted network DNS Server
        # with off-trusted tunnel mode as 'enforce_proxy' and on-trusted tunnel mode as 'twlp'
        self.validate_trusted_network_dns_server(off_trusted_tunnel_mode='enforce_proxy', on_trusted_tunnel_mode='twlp')

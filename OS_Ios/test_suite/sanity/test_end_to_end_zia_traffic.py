import os, sys,json
import time 
import pytest
import allure
# from OS_Ios.test_suite.smoke_testing.zia_and_zpa import conftest
from OS_Ios.test_suite.sanity import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version


class TestZiaTraffic:
    """
    This is class created for running tests regarding ZIA traffic.
    1. We have a setup method which helps in creating FP, DP along with login into ZCC.
    2. Testcase 1 helps in testing ZIA app access.
    3. Testcase 2 helps in testing PAC bypass.
    4. Testcase 3 helps in testing VPN bypass.
    5. Testcase 4 helps in testing ZIA app access in Edge browser.
    6. Then we have a teardown method which helps in cleaning the setup created.   
    """
    @allure.title("Setup")
    def setup_class(self):
        """
        1. Delete App policy
        2. Delete forwarding profile
        3. Create forwarding profile
        4. Create App profile
        5. Change MA/admin portal config for login
        6. Login ZCC
        """
        pass
        self.test_url = ["https://www.example.com", "https://www.example.net", "https://www.ebay.com"]

        conftest.app_profile_obj.delete_app_profile()

        conftest.app_profile_obj.forwarding_profile.delete_forwarding_profile()

        result = conftest.app_profile_obj.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.app_profile_obj.create_app_profile()
        assert result[0], result[1]

        result=conftest.app_profile_obj.edit_app_profile(ipv6mode=4)
        assert result[0], result[1]

        result = conftest.app_profile_obj.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert result[0], result[1]

        result=conftest.service_entitlement_obj.toggle_zpa(action=True)
        assert result[0], result[1]

        result=conftest.clienconnectornotif_obj.configure_aup()
        assert result[0], result[1]

        conftest.zia_pac_obj.delete_pac_file(name="test_pac_bypass_ios")

        result=conftest.saml_zia_obj.saml_action_smui(mode='disable')
        assert result[0], result[1]

        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]

        result = conftest.zcc_obj.login(
            aup=False,
            zia_user=conftest.variables["ZIA_USER_ID"],
            zia_password=conftest.variables["ZIA_USER_PASSWORD"],
            zpa_user=conftest.variables["ZPA_USER_ID"],
            zpa_password=conftest.variables["ZPA_USER_PASSWORD"],
        )
        assert result[0], result[1]

        result=conftest.zcc_obj.validate_zcc_logged_in()
        assert result[0], result[1]
    
    @pytest.mark.xray(["QA-264145","QA-264148"])
    @allure.title("Verify ZIA app access")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zia_app_access(self):
        '''
        Steps:
            1. Clear logs
            2. Validate tunnel status
            3. Access ZIA application in any browser
            4. Export logs 
            5. Verify app access in latest Tunnel log file
        '''
        conftest.log.info("########## Executing :: Test ZIA app access ##########")

        result=conftest.zcc_obj.validate_tunnel_status(zia_on=True, zia_only_company=True)
        assert result[0], result[1]

        result=conftest.sys_ops_obj.access_application(browser="safari", url=self.test_url[0], assertion_element='//XCUIElementTypeStaticText[@name="Example Domain"]', sleep_time=2)
        assert result[0], result[1]

        result=conftest.zcc_obj.export_logs(airdrop=True, airdrop_to=conftest.variables["MAC_HOSTNAME"])
        assert result[0], result[1]

        conftest.log_obj.search_log_file(file="ZSATunnel", words_to_find_in_line="Tunnel to SME for host=www.example.com:443")
        
        conftest.log.info("########## Executed :: Test ZIA app access ##########")

    @pytest.mark.xray("QA-264147")
    @allure.title("Test PAC bypass")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_pac_bypass(self):
        '''
        Steps:
            1. Create pac file and configure it in applied app profile
            2. Update policy
            2. Clear logs
            3. Validate tunnel status
            4. Access application bypassed in pac file
            5. Export logs 
            6. Verify app access in latest Tunnel log file
        '''
        conftest.log.info("########## Executing :: Test PAC Bypass ##########")

        result=conftest.zia_pac_obj.create_pac_file(name="test_pac_bypass_ios", pac_content='function FindProxyForURL(url, host) {if (shExpMatch(url, "*example*")){ return "DIRECT";}/* Default Traffic Forwarding. Forwarding to Zen on port 80, but you can use port 9400 also */return "PROXY ${GATEWAY_FX}:80; PROXY ${SECONDARY_GATEWAY_FX}:80; DIRECT";}')
        assert result[0], result[1]

        result=conftest.zia_pac_obj.get_pac_url(name="test_pac_bypass_ios")
        assert result[0], result[1]
        pac_url = result[2]

        result=conftest.app_profile_obj.edit_app_profile(pac=pac_url)
        assert result[0], result[1]

        result=conftest.zcc_obj.update_policy()
        assert result[0], result[1]

        result=conftest.zcc_obj.validate_tunnel_status(zia_only_company=True, zia_on=True)
        assert result[0], result[1]

        result=conftest.sys_ops_obj.access_application(browser="safari", url=self.test_url[1], assertion_element='//XCUIElementTypeStaticText[@name="Example Domain"]', sleep_time=5)
        assert result[0], result[1]

        result=conftest.zcc_obj.export_logs(airdrop=True, airdrop_to=conftest.variables["MAC_HOSTNAME"])
        assert result[0], result[1]

        conftest.log_obj.search_log_file(file="ZSATunnel", words_to_find_in_line="Bypassing proxy for url=https://www.example.net")

        conftest.log.info("########## Executed :: Test PAC Bypass ##########")

    @pytest.mark.xray("QA-264146")
    @allure.title("Test VPN bypass")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_vpn_bypass(self):
        '''
        Steps:
            1. Edit app profile and add VPN bypass
            2. Update policy
            2. Clear logs
            3. Validate tunnel status
            4. Access VPN bypassed application in any browser
            5. Export logs 
            6. Verify app access in latest Tunnel log file
        '''
        
        conftest.log.info("########## Executing :: Test VPN Bypass ##########")

        result=conftest.app_profile_obj.edit_app_profile(vpn_bypass="ebay.com", pac="")
        assert result[0], result[1]

        result=conftest.zcc_obj.update_policy()
        assert result[0], result[1]

        result=conftest.zcc_obj.validate_tunnel_status(zia_only_company=True, zia_on=True)
        assert result[0], result[1]

        result=conftest.sys_ops_obj.access_application(browser="safari", url=self.test_url[2], assertion_element='//XCUIElementTypeTextField[@name="Search eBay"]', sleep_time=2)
        assert result[0], result[1]

        result=conftest.zcc_obj.export_logs(airdrop=True, airdrop_to=conftest.variables["MAC_HOSTNAME"])
        assert result[0], result[1]

        conftest.log_obj.search_log_file(file="ZSATunnel", words_to_find_in_line=f"Bypassing proxy for url={self.test_url[2]}")
        
        conftest.log.info("########## Executed :: Test VPN Bypass ##########")

    @pytest.mark.xray("QA-271815")
    @allure.title("Test ZIA app access in Edge browser")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zia_app_access_in_edge(self):
        '''
        Steps:
            1. Clear logs
            2. Validate tunnel status
            3. Access ZIA application in Microsoft Edge Browser
            4. Export logs 
            5. Verify app access in latest Tunnel log file
        '''
        
        conftest.log.info("########## Executing :: Test ZIA app access in Edge ##########")

        result=conftest.zcc_obj.validate_tunnel_status(zia_on=True, zia_only_company=True)
        assert result[0], result[1]

        result=conftest.sys_ops_obj.access_application(browser="edge", url=self.test_url[0], assertion_element='//XCUIElementTypeStaticText[@name="This domain is for use in illustrative examples in documents. You may use this domain in literature without prior coordination or asking for permission."]')
        assert result[0], result[1]

        result=conftest.zcc_obj.export_logs(airdrop=True, airdrop_to=conftest.variables["MAC_HOSTNAME"])
        assert result[0], result[1]

        conftest.log_obj.search_log_file(file="ZSATunnel", words_to_find_in_line="Tunnel to SME for host=www.example.com:443")

        conftest.log.info("########## Executed :: Test ZIA app access in Edge ##########")
    
    @pytest.mark.xray("QA-264141")
    @allure.title("Test ZIA ip and ipv6 test sites access in chrome")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zia_ip_and_v6_test_in_chrome(self):
        '''
        Steps:
            1. Clear logs
            2. Validate tunnel status
            3. Access ZIA ip test sites in chrome
            4. Export logs 
            5. Verify app access in latest Tunnel log file
        '''
        conftest.log.info("########## Executing :: Test ZIA test IP sites access in Chrome ##########")

        test_ip_sites = ('http://speedtest.zscaler.com', 'https://ipv6test.google.com')

        assertion_elements = (f'//XCUIElementTypeStaticText[@name="{conftest.variables["ZIA_USER_ID"]}"]','//XCUIElementTypeStaticText[@name="No problems detected."]')

        error = []
        for  i in range(len(test_ip_sites)):

            result=conftest.zcc_obj.validate_tunnel_status(zia_on=True, zia_only_company=True)
            assert result[0], result[1]

            result=conftest.sys_ops_obj.access_application(browser="chrome", url=test_ip_sites[i], assertion_element=assertion_elements[i])
            if result[0]==False:
                error.append(result[1])

        if error != []:
            conftest.log.error("Found following errors while accessing ip test sites in chrome")
            for i in range(len(error)):
                conftest.log.error(f"{i} --> {error[i]}")
            assert False, "Found errors while accessing ip test sites in chrome"

        conftest.log.info("########## Executed :: Test ZIA test IP sites access ##########")

    @pytest.mark.xray("QA-264140")
    @allure.title("Test ZIA ip and ipv6 test sites access in edge")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zia_ip_and_v6_test_in_edge(self):
        '''
        Steps:
            1. Clear logs
            2. Validate tunnel status
            3. Access ZIA ip test sites in edge
            4. Export logs 
            5. Verify app access in latest Tunnel log file
        '''
        conftest.log.info("########## Executing :: Test ZIA test IP sites access in Edge ##########")

        test_ip_sites = ('http://speedtest.zscaler.com', 'https://ipv6test.google.com')

        assertion_elements = (f'//XCUIElementTypeStaticText[@name="{conftest.variables["ZIA_USER_ID"]}"]','//XCUIElementTypeStaticText[@name="No problems detected."]', '//XCUIElementTypeStaticText[@name="Original game created by Arend Hintze"]' )

        error = []
        for  i in range(len(test_ip_sites)):

            result=conftest.zcc_obj.validate_tunnel_status(zia_on=True, zia_only_company=True)
            assert result[0], result[1]

            result=conftest.sys_ops_obj.access_application(browser="edge", url=test_ip_sites[i], assertion_element=assertion_elements[i])
            if result[0]==False:
                error.append(result[1])
        
        if error != []:
            conftest.log.error("Found following errors while accessing ip test sites in edge")
            for i in range(len(error)):
                conftest.log.error(f"{i} --> {error[i]}")
            assert False, "Found errors while accessing ip test sites in edge"
            
        conftest.log.info("########## Executed :: Test ZIA test IP sites access in Edge ##########")

    
    @allure.title("Teardown")
    def teardown_class(self):
        """
        1. Delete PAC file
        2. Delete App profile
        3. Delete forwarding profile
        4. Logout ZCC
        """

        conftest.log.info("########## Tearing Down ##########")

        conftest.zia_pac_obj.delete_pac_file(name="test_pac_bypass_ios")

        conftest.app_profile_obj.delete_app_profile()

        conftest.app_profile_obj.forwarding_profile.delete_forwarding_profile()

        conftest.zcc_obj.clear_logs()

        conftest.zcc_obj.logout()

        conftest.log.info(f"########## Executed all applicable cases from {os.path.basename(__file__)} ##########")
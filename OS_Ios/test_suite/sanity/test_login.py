import os, sys,json
import time 
import pytest
import allure
# from OS_Ios.test_suite.smoke_testing.zia_and_zpa import conftest
from OS_Ios.test_suite.sanity import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version


class TestZccLogin:
    """
    This is class created for running tests around ZCC Login.
    1. We have a setup method which helps in creating FP, DP.
    2. Testcase performs various configurations on MA and SMUI to test different login scenarios.
    3. Then we have a teardown method which helps in cleaning the setup created.   
    """

    @allure.title("Setup")
    def setup_class(self):
        """
        1. Delete App policy
        2. Delete forwarding profile
        3. Create forwarding profile
        4. Create App profile
        """
        conftest.app_profile_obj.delete_app_profile()

        conftest.app_profile_obj.forwarding_profile.delete_forwarding_profile()

        result = conftest.app_profile_obj.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.app_profile_obj.create_app_profile()
        assert result[0], result[1]

    @pytest.mark.xray("QA-264120")
    @allure.title("Login to ZCC with ZIA form based authentication")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zia_only_hosted_db(self):
        """
        This function tests the login process when form based authentication method is configured for both ZIA.
        1. Disable ZPA from Zscaler Service Entitlement on MA
        2. Configure AUP as Never
        3. Disable SAML based authentication for ZIA on ZIA admin portal
        4. Login to ZCC
        5. Validate if user is logged in 
        6. Logout for further test cases.
        """

        conftest.log.info("########## Executing :: Test ZIA only(Hosted DB) Login ##########")

        result = conftest.service_entitlement_obj.toggle_zpa(action=False)
        assert result[0],result[1]

        result = conftest.clienconnectornotif_obj.configure_aup(aup_frequency="never")
        assert result[0],result[1]

        result = conftest.saml_zia_obj.saml_action_smui(mode='disable')
        assert result[0],result[1]

        result=conftest.zcc_obj.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"])
        assert result[0],result[1]

        result=conftest.zcc_obj.validate_zcc_logged_in()
        assert result[0],result[1]
        
        result = conftest.zcc_obj.logout()
        assert result[0], result[1]

        conftest.log.info("########## Executed :: Test ZIA only(Hosted DB) Login ##########")

    @pytest.mark.xray("QA-264121")
    @allure.title("Login to ZCC with ZIA configured as SAML(Okta) based authentication")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zia_only_saml(self):
        """
        This function tests the login process when SAML(Okta) based authentication method is configured for both ZIA.
        1. Disable ZPA from Zscaler Service Entitlement on MA
        2. Configure AUP as Never
        3. Disable BBA
        4. Enable SAML based authentication for ZIA on ZIA admin portal
        5. Change SAML on ZIA to given IDP_NAME in config
        6. Login to ZCC
        7. Validate if user is logged in 
        8. Logout for further test cases.
        """
        conftest.log.info("########## Executing :: Test ZIA only SAML(Okta) Login ##########")

        result = conftest.service_entitlement_obj.toggle_zpa(action=False)
        assert result[0],result[1]

        result = conftest.clienconnectornotif_obj.configure_aup(aup_frequency="never")
        assert result[0],result[1]

        result = conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0],result[1]

        result = conftest.saml_zia_obj.saml_action_smui(mode='enable')
        assert result[0],result[1]

        result = conftest.saml_zia_obj.change_saml_on_zia(idp_name=conftest.variables['IDP_NAME'])
        assert result[0],result[1]

        result=conftest.zcc_obj.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"], zia_saml_login=True)
        assert result[0],result[1]

        result=conftest.zcc_obj.validate_zcc_logged_in()
        assert result[0],result[1]
        
        result = conftest.zcc_obj.logout()
        assert result[0], result[1]

        conftest.log.info("########## Executed :: Test ZIA only SAML(Okta) Login ##########")

    @pytest.mark.xray("QA-264122")
    @allure.title("Login to ZCC with ZIA configured as SAML(Okta) based authentication with BBA enabled")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zia_only_saml_bba(self):
        """
        This function tests the login process when SAML(Okta) based authentication method is configured for both ZIA.
        1. Disable ZPA from Zscaler Service Entitlement on MA
        2. Configure AUP as Never
        3. Enable BBA
        4. Enable SAML based authentication for ZIA on ZIA admin portal
        5. Change SAML on ZIA to given IDP_NAME in config
        6. Login to ZCC
        7. Validate if user is logged in 
        8. Logout for further test cases.
        """
        conftest.log.info("########## Executing :: Test ZIA only SAML(Okta) Login ##########")

        result = conftest.service_entitlement_obj.toggle_zpa(action=False)
        assert result[0],result[1]

        result = conftest.clienconnectornotif_obj.configure_aup(aup_frequency="never")
        assert result[0],result[1]

        result = conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="enable")
        assert result[0],result[1]

        result = conftest.saml_zia_obj.saml_action_smui(mode='enable')
        assert result[0],result[1]

        result = conftest.saml_zia_obj.change_saml_on_zia(idp_name=conftest.variables['IDP_NAME'])
        assert result[0],result[1]

        result=conftest.zcc_obj.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"], zia_saml_login=True, bba_enabled=True)
        assert result[0],result[1]

        result=conftest.zcc_obj.validate_zcc_logged_in()
        assert result[0],result[1]
        
        result = conftest.zcc_obj.logout()
        assert result[0], result[1]

        conftest.log.info("########## Executed :: Test ZIA only SAML(Okta) Login with BBA enabled ##########")

    @pytest.mark.xray("QA-264125")
    @allure.title("Login to ZCC with ZIA form based and ZPA configured as SAML(Okta) based authentication")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7) 
    def test_zia_hosted_db_zpa_saml_login_(self):
        """
        This function tests the login process when form based and SAML(Okta) based authentication method is configured for ZIA and ZPA respectively.
        1. Enable ZPA from Zscaler Service Entitlement on MA
        2. Configure AUP as Never
        3. Disable BBA 
        4. Disable SAML based authentication for ZIA on ZIA admin portal
        5. Login to ZCC
        6. Validate if user is logged in 
        7. Logout for further test cases.
        """
        conftest.log.info("########## Executing :: Test ZIA(Hosted DB) & ZPA SAML(Okta) Login ##########")

        result = conftest.service_entitlement_obj.toggle_zpa(action=True)
        assert result[0],result[1]

        result = conftest.clienconnectornotif_obj.configure_aup(aup_frequency="never")
        assert result[0],result[1]

        result = conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0],result[1]

        result = conftest.saml_zia_obj.saml_action_smui(mode='disable')
        assert result[0],result[1]

        result=conftest.zcc_obj.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                        conftest.variables ["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0],result[1]

        result=conftest.zcc_obj.validate_zcc_logged_in()
        assert result[0],result[1]
        
        result = conftest.zcc_obj.logout()
        assert result[0], result[1]

        conftest.log.info("########## Executed :: Test ZIA(Hosted DB) & ZPA SAML(Okta) Login ##########")
    
    @pytest.mark.xray("QA-264126")
    @allure.title("Login to ZCC with ZIA form based and ZPA configured as SAML(Okta) based authentication with BBA enabled")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7) 
    def test_zia_hosted_db_zpa_saml_bba_login(self):
        """
        This function tests the login process when form based and SAML(Okta) based authentication method is configured for ZIA and ZPA respectively with bba enabled.
        1. Enable ZPA from Zscaler Service Entitlement on MA
        2. Configure AUP as Never
        3. Enable BBA 
        4. Disable SAML based authentication for ZIA on ZIA admin portal
        5. Login to ZCC
        6. Validate if user is logged in 
        7. Logout for further test cases.
        """
        conftest.log.info("########## Executing :: Test ZIA(Hosted DB) & ZPA SAML(Okta) Login ##########")

        result = conftest.service_entitlement_obj.toggle_zpa(action=True)
        assert result[0],result[1]

        result = conftest.clienconnectornotif_obj.configure_aup(aup_frequency="never")
        assert result[0],result[1]

        result = conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="enable")
        assert result[0],result[1]

        result = conftest.saml_zia_obj.saml_action_smui(mode='disable')
        assert result[0],result[1]

        # Logging in with ZIA and ZPA user credentials
        result=conftest.zcc_obj.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                        conftest.variables ["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"], bba_enabled=True)
        assert result[0],result[1]

        # Validating if the user is logged in successfully
        result=conftest.zcc_obj.validate_zcc_logged_in()
        assert result[0],result[1]
        
        result = conftest.zcc_obj.logout()
        assert result[0], result[1]

        conftest.log.info("########## Executed :: Test ZIA(Hosted DB) & ZPA SAML(Okta) Login with BBA enabled ##########")

    @pytest.mark.xray("QA-264127")
    @allure.title("Login to ZCC with ZIA and ZPA both configured as SAML(Okta) based authentication")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zia_zpa_saml_login(self):
        """
        This function tests the login process when SAML(Okta) based authentication method is configured for both ZIA and ZPA.
        1. Enable ZPA from Zscaler Service Entitlement on MA
        2. Configure AUP as Never
        3. Disable BBA 
        4. Enable SAML based authentication for ZIA on ZIA admin portal
        5. Change SAML on ZIA to given IDP_NAME in config
        6. Login to ZCC
        7. Validate if user is logged in 
        8. Logout for further test cases.
        """
        conftest.log.info("########## Executing :: Test ZIA+ZPA SAML(Okta) Login ##########")

        result = conftest.service_entitlement_obj.toggle_zpa(action=True)
        assert result[0],result[1]

        result = conftest.clienconnectornotif_obj.configure_aup(aup_frequency="never")
        assert result[0],result[1]

        result = conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0],result[1]

        result = conftest.saml_zia_obj.saml_action_smui(mode='enable')
        assert result[0],result[1]

        result = conftest.saml_zia_obj.change_saml_on_zia(idp_name=conftest.variables['IDP_NAME'])
        assert result[0],result[1]
        time.sleep(5)

        # Logging in with ZIA and ZPA user credentials
        result=conftest.zcc_obj.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                        conftest.variables ["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"], zia_saml_login=True)
        assert result[0],result[1]

        # Validating if the user is logged in successfully
        result=conftest.zcc_obj.validate_zcc_logged_in()
        assert result[0],result[1]
        
        result = conftest.zcc_obj.logout()
        assert result[0], result[1]

        conftest.log.info("########## Executed :: Test ZIA+ZPA SAML(Okta) Login ##########")

    @pytest.mark.xray("QA-264128")
    @allure.title("Login to ZCC with ZIA and ZPA both configured as SAML(Okta) based authentication with Browser Based Authentication enabled")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zia_zpa_saml_bba(self):
        """
        This function tests the login process when SAML(Okta) based authentication method is configured for both ZIA and ZPA with BBA enabled.
        1. Enable ZPA from Zscaler Service Entitlement on MA
        2. Configure AUP as Never
        3. Enable BBA 
        4. Enable SAML based authentication for ZIA on ZIA admin portal
        5. Change SAML on ZIA to given IDP_NAME in config
        6. Login to ZCC
        7. Validate if user is logged in 
        8. Logout for further test cases.
        """

        conftest.log.info("########## Executing :: Test ZIA + ZPA SAML(Okta) + BBA  Login ##########")
        
        result = conftest.service_entitlement_obj.toggle_zpa(action=True)
        assert result[0],result[1]

        result = conftest.clienconnectornotif_obj.configure_aup(aup_frequency="never")
        assert result[0],result[1]

        result = conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="enable")
        assert result[0],result[1]

        result = conftest.saml_zia_obj.saml_action_smui(mode='enable')
        assert result[0],result[1]

        result = conftest.saml_zia_obj.change_saml_on_zia(idp_name=conftest.variables['IDP_NAME'])
        assert result[0],result[1]
        time.sleep(5)

        result=conftest.zcc_obj.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                        conftest.variables ["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"], zia_saml_login=True, bba_enabled=True)
        assert result[0],result[1]

        result=conftest.zcc_obj.validate_zcc_logged_in()
        assert result[0],result[1]

        result = conftest.zcc_obj.logout()
        assert result[0], result[1]

        conftest.log.info("########## Executed :: Test ZIA + ZPA SAML(Okta) + BBA  Login ##########")


    @allure.title("teardown")
    def teardown_class(self):
        
        """
        1. Delete App profile
        2. Delete forwarding profile
        3. Logout ZCC if logged in
        """
        conftest.log.info("########## Tearing Down ##########")

        conftest.app_profile_obj.delete_app_profile()

        conftest.app_profile_obj.forwarding_profile.delete_forwarding_profile()

        if conftest.zcc_obj.validate_zcc_logged_in(check_alerts_after_login=False, dynamic_wait_search_element=10)[0]:
            conftest.zcc_obj.logout()

        conftest.log.info(f"########## Executed all applicable cases from {os.path.basename(__file__)} ##########")
import os, sys,json
import time 
import pytest
import allure
# from OS_Ios.test_suite.smoke_testing.zia_and_zpa import conftest
from OS_Ios.test_suite.sanity import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version


class TestZccMisc:
    """
    This class represents a test class for ZCC UI fields verification.

    1. We have a setup method which helps in creating FP and DP along with login into ZCC.
    2. Testcase 1 helps in verifying ZIA reactivation feature.
    3. Testcase 2 helps in verifying restart service control.
    4. Testcase 3 helps in verifying report an issue support.
    5. Testcase 4 helps in verifying logout password.
    6. Then we have a teardown method which helps in cleaning the setup created.
    """

    @allure.title("Setup")
    def setup_class(self):
        """
        1. Delete App policy
        2. Delete forwarding profile
        3. Create forwarding profile
        4. Create App profile
        5. Make MA config changes for login
        6. Login 
        7. Validate zcc login status
        """
        conftest.app_profile_obj.delete_app_profile()

        conftest.app_profile_obj.forwarding_profile.delete_forwarding_profile()

        result = conftest.app_profile_obj.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.app_profile_obj.create_app_profile()
        assert result[0], result[1]

        result = conftest.app_profile_obj.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert result[0], result[1]

        result=conftest.service_entitlement_obj.toggle_zpa(action=True)
        assert result[0], result[1]

        result=conftest.clienconnectornotif_obj.configure_aup()
        assert result[0], result[1]

        result=conftest.saml_zia_obj.saml_action_smui(mode='disable')
        assert result[0], result[1]

        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]

        result = conftest.clienconnectorsupport_obj.toggle_report_an_issue(action=True)
        assert result[0], result[1]

        result = conftest.zcc_obj.login(
            aup=False,
            zia_user=conftest.variables["ZIA_USER_ID"],
            zia_password=conftest.variables["ZIA_USER_PASSWORD"],
            zpa_user=conftest.variables["ZPA_USER_ID"],
            zpa_password=conftest.variables["ZPA_USER_PASSWORD"],
        )
        assert result[0], result[1]

        result=conftest.zcc_obj.validate_zcc_logged_in()
        assert result[0], result[1]
    
    @pytest.mark.xray("QA-264159")
    @allure.title("Test ZIA reactivation")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zia_reactivation(self):
        """
        This function tests zia reactivation
        1. Edit app profile and give reactivate_web_security_minutes=1
        2. Update policy
        3. Validate ZIA service status
        4. Turn off ZIA service
        5. Wait for ZIA to reactivate after 1 min
        6. Validate ZIA service status
        """

        conftest.log.info("########## Executing :: ZIA Reactivation Test Case ##########")

        result = conftest.app_profile_obj.edit_app_profile(reactivate_web_security_minutes=1)
        assert result[0], result[1]

        result= conftest.zcc_obj.update_policy()
        assert result[0], result[1]

        result = conftest.zcc_obj.validate_tunnel_status(zia_only_company=True, zia_on=True)
        assert result[0], result[1]

        result = conftest.zcc_obj.toggle_service(service="ZIA", action=False)
        assert result[0], result[1]

        conftest.log.info("***** Sleeping for One minute to verify ZIA reactivation *****")
        time.sleep(65)

        result = conftest.zcc_obj.validate_tunnel_status(zia_only_company=True, zia_on=True)
        assert result[0], result[1]

        conftest.log.info("########## Executed :: ZIA Reactivation Test Case Passed ##########")

    @pytest.mark.xray("QA-264151")
    @allure.title("Test restart service")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_restart_service(self):
        """
        This function tests restart service and verify on ui if service is restarted
        Step - Restart service and verify in notifications tab
        """
        conftest.log.info("########## Executing :: Test restart service ##########")

        result = conftest.zcc_obj.restart_service(check_notifications=True)
        assert result[0], result[1]

        conftest.log.info("########## Executed :: Test restart service ##########")

    @pytest.mark.xray("QA-264149")
    @allure.title("Test report an issue")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_report_an_issue(self):
        """
        This function tests report an issue support for the end user
        1. clear logs
        2. Report an issue and verify in logs
        """
        conftest.log.info("########## Executing :: Test report an issue ##########")

        result= conftest.zcc_obj.update_policy(sleep_time=30)
        assert result[0], result[1]

        result = conftest.zcc_obj.report_an_issue(verify_in_logs=True, airdrop_to=conftest.variables["MAC_HOSTNAME"])
        assert result[0], result[1]

        conftest.log.info("########## Executed :: Test report an issue ##########")

    @pytest.mark.xray("QA-264154")
    @allure.title("Test logout password")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_logout_password(self):
        """
        This function tests logout password functionality
        1. Edit app profile and give logout password
        2. Update policy
        3. Logout with the password configured
        """
        conftest.log.info("########## Executing :: Test logout password ##########")

        result = conftest.zcc_obj.restart_zcc()
        assert result[0], result[1]
        
        test_logout_password = "logout"

        result = conftest.app_profile_obj.edit_app_profile(logout_password=test_logout_password)
        assert result[0], result[1]

        result= conftest.zcc_obj.update_policy()
        assert result[0], result[1]
        
        result = conftest.zcc_obj.logout(password=test_logout_password)
        assert result[0], result[1]

        conftest.log.info("########## Executed :: Test logout password ##########")

    
    @allure.title("Teardown")
    def teardown_class(self):

        conftest.log.info("########## Tearing Down ##########")

        conftest.app_profile_obj.delete_app_profile()

        conftest.app_profile_obj.forwarding_profile.delete_forwarding_profile()

        conftest.log.info(f"########## Executed all applicable cases from {os.path.basename(__file__)} ##########")

    
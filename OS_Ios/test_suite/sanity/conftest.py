import json
import os
from OS_Ios.library.ui_zcc import Zcc  
from OS_Ios.library.ops_system import SysOps
from common_lib.common import log_ops
from common_lib.common import logger,constants
from common_lib.common.constants import AppProfilePasswords, ZccUi
from common_lib.Custom_Markers import add_markers,zcc_mark_version
from common_lib.mobileadmin.appprofile import AppProfile
from common_lib.mobileadmin.forwardingprofile import ForwardingProfile
from common_lib.mobileadmin.serviceentitlement import ServiceEntitlement
from common_lib.mobileadmin.platformsettings import PlatformSettings
from common_lib.mobileadmin.clientconnectornotifications import ClientConnectorNotifications
from common_lib.mobileadmin.clientconnectorsupport import ClientConnectorSupport
from common_lib.adminzia.samlzia import SamlZia
from common_lib.adminzia.ziapac import ZiaPac
from common_lib.adminzpa import accesszpaapps,zpapolicy,createzpaapps,appsegment
from OS_Ios.library import ops_system as SysOps



config_path = os.path.join(os.getcwd(), "config", os.environ.get("CONFIG_FILE"))

    #: The variables loaded from the configuration file.
with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)

const = constants
application = const.ZccSanityConst.ZPA_APPS["DOMAIN"]
zpa_private_app = "http://"+const.ZccSanityConst.ZPA_APPS["DOMAIN"]['app']
zpa_private_app_assertion = '//XCUIElementTypeStaticText[@name="SUCCESS, CONNECTED TO AUTOMATION SERVER"]'
zpa_public_app = "https://www.netflix.com"
zpa_public_app_assertion = '//XCUIElementTypeStaticText[@name="Netflix"]'
http_server_ip = "************"

log = logger.Logger.initialize_logger(log_file_name="smoke_cases_ios",log_level="INFO")
zcc_obj = Zcc(log_handle=log, start_app=True)
sys_ops_obj = SysOps.SysOps(driver=zcc_obj.ui.driver, logger=log)
log_obj = log_ops.LogOps(log_handle=log)

app_profile_obj = AppProfile(cloud=variables["CLOUD"],config=config_path,log_handle=log)
forwarding_profile_obj=ForwardingProfile(cloud=variables["CLOUD"],config=config_path,log_handle=log)
service_entitlement_obj=ServiceEntitlement(cloud=variables["CLOUD"],config=config_path,log_handle=log)
platform_settings_obj = PlatformSettings(cloud=variables['CLOUD'], config=config_path, log_handle=log)
saml_zia_obj = SamlZia(cloud=variables['CLOUD'], config=config_path, log_handle=log, debug=True)
clienconnectornotif_obj = ClientConnectorNotifications(cloud=variables['CLOUD'], config=config_path, log_handle=log)
clienconnectorsupport_obj = ClientConnectorSupport(cloud=variables['CLOUD'], config=config_path, log_handle=log)
zia_pac_obj = ZiaPac(cloud=variables['CLOUD'], config=config_path, log_handle=log)
zpa_app_access = accesszpaapps.AccessZpaApps(cloud=variables['CLOUD'], config=config_path, log_handle=log)
zpa_policy = zpapolicy.ZpaPolicy(cloud=variables['CLOUD'], config=config_path, log_handle=log)
create_zpa_apps = createzpaapps.MakeZpaApplication(variables["CLOUD"], config=config_path, log_handle=log,file=[application['app']], server_ip=http_server_ip)
app_segment_obj = appsegment.AppSegment(cloud=variables['CLOUD'], config=config_path, log_handle=log)

test_password_zia = AppProfilePasswords.ZIA_DISABLE_PASSWORD
test_password_zpa = AppProfilePasswords.ZPA_DISABLE_PASSWORD
test_reason_zia = ZccUi.DISABLE_REASON
test_reason_zpa = ZccUi.DISABLE_REASON
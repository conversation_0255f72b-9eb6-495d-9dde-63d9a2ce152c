import os, sys,json
import time 
import pytest
import allure
# from OS_Ios.test_suite.smoke_testing.zia_and_zpa import conftest
from OS_Ios.test_suite.sanity import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version


class TestZpaTraffic:
    """
    This is class created for running tests regarding ZPA traffic.
    1. We have a setup method which helps in creating FP, DP along with login into ZCC.
    2. Testcase 1 helps in testing ZPA public app access via ZPA.
    3. Testcase 2 helps in testing ZPA private app access in edge.
    4. Testcase 3 helps in testing ZPA private app access in chrome.
    5. Then we have a teardown method which helps in cleaning the setup created.   
    """
    @allure.title("Setup")
    def setup_class(self):
        """
        1. Delete App policy
        2. Delete forwarding profile
        3. Create forwarding profile
        4. Create App profile
        5. Change MA/admin portal config for login
        6. Login ZCC
        """
        conftest.app_profile_obj.delete_app_profile()

        conftest.app_profile_obj.forwarding_profile.delete_forwarding_profile()

        result = conftest.app_profile_obj.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.app_profile_obj.create_app_profile()
        assert result[0], result[1]

        result = conftest.app_profile_obj.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert result[0], result[1]

        result=conftest.app_profile_obj.edit_app_profile(ipv6mode=4)
        assert result[0], result[1]

        result=conftest.service_entitlement_obj.toggle_zpa(action=True)
        assert result[0], result[1]

        result=conftest.clienconnectornotif_obj.configure_aup()
        assert result[0], result[1]

        result=conftest.saml_zia_obj.saml_action_smui(mode='disable')
        assert result[0], result[1]

        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]

        # Delete the ZPA applications
        conftest.create_zpa_apps.delete_zpa_apps(custom_app_segment_name="smoke_automation_public")  # Delete the ZPA applications

        # Create new ZPA apps
        res = conftest.create_zpa_apps.create_zpa_apps(custom_app_segment_name="smoke_automation_public", enable_dynamic_discovery=True, domains=["www.netflix.com",])
        conftest.log.info(res[1])  # Log the result
        assert res[0], res[1]  # Check if the creation was successful

        result = conftest.zpa_policy.delete_policy(
            zpa_user_name=conftest.variables["ZPA_ADMIN_ID"],
            zpa_password=conftest.variables["ZPA_ADMIN_PASSWORD"],
            policy_type="AccessPolicy",
        )

        result = conftest.zpa_policy.create_access_policy(zpa_user_name=conftest.variables["ZPA_ADMIN_ID"], zpa_password=conftest.variables["ZPA_ADMIN_PASSWORD"])
        assert result[0], result[1]

        result = conftest.zcc_obj.login(
            aup=False,
            zia_user=conftest.variables["ZIA_USER_ID"],
            zia_password=conftest.variables["ZIA_USER_PASSWORD"],
            zpa_user=conftest.variables["ZPA_USER_ID"],
            zpa_password=conftest.variables["ZPA_USER_PASSWORD"],
        )
        assert result[0], result[1]

        result=conftest.zcc_obj.validate_zcc_logged_in()
        assert result[0], result[1]

    @pytest.mark.xray("QA-264144")
    @allure.title("Test public app access via ZPA")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zpa_public_app_access(self):
        '''
        Steps:
            1. Create access policy for zpa apps
            2. Get ZPA statistics
            3. Clear logs
            4. Validate tunnel status
            5. Access public application via ZPA in any browser
            6. Export logs 
            7. Verify app access in latest Tunnel log file
        '''
        conftest.log.info("########## Executing :: Test ZPA public app access ##########")
        
        result = conftest.zcc_obj.get_zpa_tab_data(statistics=True)
        assert result[0], result[1]
        sent_byte_before, rcvd_byte_before = result[2]

        result = conftest.sys_ops_obj.access_application(
            browser="chrome",
            assertion_element=conftest.zpa_public_app_assertion,
            url=conftest.zpa_public_app)
        assert result[0], result[1]

        result = conftest.zcc_obj.get_zpa_tab_data(statistics=True)
        assert result[0], result[1]
        sent_byte_after, rcvd_byte_after = result[2]

        result = conftest.zpa_app_access.validate_access_bytes(sent_byte_before, rcvd_byte_before, sent_byte_after, rcvd_byte_after)
        assert result[0], result[1]

        conftest.log.info("########## Executed :: Test ZPA public app access ##########")

    @allure.title("Teardown")
    def teardown_class(self):
        """
        1. Delete PAC file
        2. Delete App profile
        3. Delete forwarding profile
        4. Delete ZPA access policy
        5. Logout ZCC
        """

        conftest.log.info("########## Tearing Down ##########")

        conftest.app_profile_obj.delete_app_profile()

        conftest.app_profile_obj.forwarding_profile.delete_forwarding_profile()

        # Delete the ZPA applications
        conftest.create_zpa_apps.delete_zpa_apps(custom_app_segment_name="smoke_automation_public")  # Delete the ZPA applications

        conftest.zpa_policy.delete_policy(
            zpa_user_name=conftest.variables["ZPA_ADMIN_ID"],
            zpa_password=conftest.variables["ZPA_ADMIN_PASSWORD"],
            policy_type="AccessPolicy",
        )

        conftest.zcc_obj.logout()

        conftest.log.info(f"########## Executed all applicable cases from {os.path.basename(__file__)} ##########")
import os, sys,json
import time 
import pytest
import allure
# from OS_Ios.test_suite.smoke_testing.zia_and_zpa import conftest
from OS_Ios.test_suite.sanity import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version


class TestZccUi:
    """
    This class represents a test class for ZCC UI fields verification.

    1. We have a setup method which helps in creating FP and DP along with login into ZCC.
    2. Testcase 1 helps in verifying ZIA tab fields in both scenarios when service is either on or off.
    3. Testcase 2 helps in verifying ZPA tab fields in both scenarios when service is either on or off.
    4. Testcase 3 helps in verifying notification tab fields.
    5. Testcase 4 helps in verifying More tab fields by enabling/disabling controls for user from MA.
    6. Then we have a teardown method which helps in cleaning the setup created.
    """
    @allure.title("Setup")
    def setup_class(self):
        """
        1. Delete App policy
        2. Delete forwarding profile
        3. Create forwarding profile
        4. Create App profile
        5. Change MA/admin portal config for login
        6. Login ZCC
        """
        conftest.app_profile_obj.delete_app_profile()

        conftest.app_profile_obj.forwarding_profile.delete_forwarding_profile()

        result = conftest.app_profile_obj.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.app_profile_obj.create_app_profile()
        assert result[0], result[1]

        result=conftest.service_entitlement_obj.toggle_zpa(action=True)
        assert result[0], result[1]

        result=conftest.clienconnectornotif_obj.configure_aup()
        assert result[0], result[1]

        result=conftest.saml_zia_obj.saml_action_smui(mode='disable')
        assert result[0], result[1]

        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]

        result = conftest.zcc_obj.login(
            aup=False,
            zia_user=conftest.variables["ZIA_USER_ID"],
            zia_password=conftest.variables["ZIA_USER_PASSWORD"],
            zpa_user=conftest.variables["ZPA_USER_ID"],
            zpa_password=conftest.variables["ZPA_USER_PASSWORD"],
        )
        assert result[0], result[1]

        result=conftest.zcc_obj.validate_zcc_logged_in(check_alerts_after_login=True)
        assert result[0], result[1]
    
    @pytest.mark.xray("QA-264137")
    @allure.title('Verify ZIA tab fields')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_verify_zia_tab_fields(self):
        '''
        Steps:
            1. Validate if ZIA service status is ON
            2. Verify ZIA tab fields when service status is ON
            3. Turn off ZIA
            4. Verify ZIA tab fields when service status is OFF
        '''
        conftest.log.info("########## Executing :: Test Verify ZIA tab fields ##########")
        
        result=conftest.zcc_obj.validate_tunnel_status(zia_only_company=True, zia_on=True)
        assert result[0], result[1]

        result=conftest.zcc_obj.verify_zcc_ui_fields(zia_tab=True, zia_state="on" ,zia_user=conftest.variables["ZIA_USER_ID"])
        assert result[0], result[1]

        result=conftest.zcc_obj.toggle_service(action=False, service="ZIA")
        assert result[0], result[1]

        result=conftest.zcc_obj.verify_zcc_ui_fields(zia_tab=True, zia_state="off" ,zia_user=conftest.variables["ZIA_USER_ID"])
        assert result[0], result[1]

        result=conftest.zcc_obj.toggle_service(action=True, service="ZIA")
        assert result[0], result[1]

        conftest.log.info("########## Executed :: Test Verify ZIA tab fields ##########")
    
    @pytest.mark.xray("QA-264138")
    @allure.title('Verify ZPA tab fields')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_verify_zpa_tab_fields(self):
        '''
        Steps:
            1. Validate if ZPA service status is ON
            2. Verify ZPA tab fields when service status is ON
            3. Turn off ZPA
            4. Verify ZPA tab fields when service status is OFF
        '''
        conftest.log.info("########## Executing :: Test Verify ZPA tab fields ##########")

        result=conftest.zcc_obj.validate_tunnel_status(zpa_only_company=True, zpa_on=True)
        assert result[0], result[1]

        result=conftest.zcc_obj.verify_zcc_ui_fields(zpa_tab=True, zpa_state="on" ,zpa_user=conftest.variables["ZPA_USER_ID"])
        assert result[0], result[1]

        result=conftest.zcc_obj.toggle_service(action=False, service="ZPA")
        assert result[0], result[1]

        result=conftest.zcc_obj.verify_zcc_ui_fields(zpa_tab=True, zpa_state="off" ,zpa_user=conftest.variables["ZPA_USER_ID"])
        assert result[0], result[1]

        result=conftest.zcc_obj.toggle_service(action=True, service="ZPA")
        assert result[0], result[1]

        conftest.log.info("########## Executed :: Test Verify ZIA tab fields ##########")
    
    @pytest.mark.xray("QA-266034")
    @allure.title('Verify Notifications tab fields')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_verify_notifications_tab_fields(self):
        '''
        Steps:
            1. Restart service and check if notifications appear after restart service
            2. Verify Notification tab fields and clear notifications option
        '''
        conftest.log.info("########## Executing :: Test Verify Notifications tab fields ##########")

        result=conftest.zcc_obj.restart_service(check_notifications=True, sleep_time=20)
        assert result[0], result[1]

        result=conftest.zcc_obj.verify_zcc_ui_fields(notifications_tab=True)
        assert result[0], result[1]

        conftest.log.info("########## Executed :: Test Verify Notifications tab fields ##########")
    
    @pytest.mark.xray(["QA-264139" , "QA-264150"])
    @allure.title('Verify More tab fields')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_verify_more_tab_fields(self):
        '''
        Steps:
            1. Disable ZPA reauth notifications from MA
            2. Disable RAI and hide logging controls
            3. Update policy
            4. Verify more tab field after disabling above options from MA 
            5. Enable ZPA reauth notifications from MA
            6. Enable RAI and unhide logging controls
            7. Update policy
            8. Verify more tab field after enabling above options from MA 
        '''
        conftest.log.info("########## Executing :: Test Verify More tab fields ##########")

        result = conftest.clienconnectornotif_obj.create_tray_notification(enable_notifications_zpa_reauth=False)
        assert result[0], result[1]

        result = conftest.clienconnectorsupport_obj.toggle_report_an_issue(hide_logging_controls=True, action=False)
        assert result[0], result[1]

        result = conftest.zcc_obj.update_policy()
        assert result[0], result[1]

        result=conftest.zcc_obj.verify_zcc_ui_fields(more_tab=True, disable_options_from_MA=True)
        assert result[0], result[1]

        result = conftest.clienconnectornotif_obj.create_tray_notification(enable_notifications_zpa_reauth=True)
        assert result[0], result[1]

        result = conftest.clienconnectorsupport_obj.toggle_report_an_issue(hide_logging_controls=False, action=True)
        assert result[0], result[1]

        result = conftest.zcc_obj.update_policy()
        assert result[0], result[1]

        result=conftest.zcc_obj.verify_zcc_ui_fields(more_tab=True, app_policy_name="automation_ios_dp")
        assert result[0], result[1]

        conftest.log.info("########## Executed :: Test Verify More tab fields ##########")

    @allure.title("teardown")
    def teardown_class(self):
        """
        1. Delete App profile
        2. Delete forwarding profile
        3. Logout ZCC
        """
        conftest.log.info("########## Tearing Down ##########")

        conftest.app_profile_obj.delete_app_profile()

        conftest.app_profile_obj.forwarding_profile.delete_forwarding_profile()

        conftest.zcc_obj.logout()

        conftest.log.info(f"########## Executed all applicable cases from {os.path.basename(__file__)} ##########")

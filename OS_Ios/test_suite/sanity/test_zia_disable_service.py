import os, sys,json
import time 
import pytest
import allure
# from OS_Ios.test_suite.smoke_testing.zia_and_zpa import conftest
from OS_Ios.test_suite.sanity import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version


class TestZiaDisableService:
    """
    This is class created for running smoke tests for ZIA disable password and disable reason.
    1. We have a setup method which helps in creating FP and DP along with login into ZCC.
    2. Testcase 4 helps in testing zia disable password.
    3. Testcase 5 helps in testing zia disable reason.
    4. Testcase 6 helps in testing zia disable password along with zia disable reason.
    5. Then we have a teardown method which helps in cleaning the setup created.   
    """
    @allure.title("Setup")
    def setup_class(self):
        """
        1. Delete App policy
        2. Delete forwarding profile
        3. Create forwarding profile
        4. Create App profile
        5. Change MA/admin portal config for login
        6. Login ZCC
        """
        
        conftest.app_profile_obj.delete_app_profile()

        conftest.app_profile_obj.forwarding_profile.delete_forwarding_profile()

        result = conftest.app_profile_obj.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.app_profile_obj.create_app_profile()
        assert result[0], result[1]

        result=conftest.service_entitlement_obj.toggle_zpa(action=False)
        assert result[0], result[1]

        result=conftest.clienconnectornotif_obj.configure_aup()
        assert result[0], result[1]

        result=conftest.saml_zia_obj.saml_action_smui(mode='disable')
        assert result[0], result[1]

        result = conftest.zcc_obj.login(
            aup=False,
            zia_user=conftest.variables["ZIA_USER_ID"],
            zia_password=conftest.variables["ZIA_USER_PASSWORD"]
        )
        assert result[0], result[1]

        result=conftest.zcc_obj.validate_zcc_logged_in(check_alerts_after_login=True, dynamic_wait_search_element=40)
        assert result[0], result[1]
    
    @pytest.mark.xray("QA-264152")
    @allure.title('Disable ZIA with disable password enabled')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_zia_disable_password(self):
        '''
        Steps:
            1. Enable Disable_Password for ZIA in the App profile
            2. Update Policy in ZCC
            3. Disable ZIA with password
            4. Enable ZIA for further test cases
        '''
        conftest.log.info("########## Executing :: Test ZIA disable password  ##########")

        result = conftest.app_profile_obj.edit_app_profile(zia_disable_password=conftest.test_password_zia, disable_reason=False)
        assert result[0], result[1]

        result = conftest.zcc_obj.update_policy()
        assert result[0], result[1]

        result = conftest.zcc_obj.toggle_service(service="ZIA", action=False, password=conftest.test_password_zia)
        assert result[0], result[1]

        result = conftest.zcc_obj.toggle_service(service="ZIA")
        assert result[0], result[1]

        conftest.log.info("########## Executed :: Test ZIA disable password ##########")
    
    @pytest.mark.xray("QA-264155")
    @allure.title('Disable ZIA with disable reason enabled')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_zia_disable_reason(self):
        '''
        Steps:
            1. Enable Disable_Reason in the App profile
            2. Update Policy in ZCC
            3. Disable ZIA with reason
            4. Enable ZIA for further test cases
        '''
        conftest.log.info("########## Executing :: Test ZIA disable reason  ##########")

        result = conftest.app_profile_obj.edit_app_profile(disable_reason=True, zia_disable_password="")
        assert result[0], result[1]

        result = conftest.zcc_obj.update_policy()
        assert result[0], result[1]

        result = conftest.zcc_obj.toggle_service(service="ZIA", action=False, disable_reason=conftest.test_reason_zia)
        assert result[0], result[1]

        result = conftest.zcc_obj.toggle_service(service="ZIA")
        assert result[0], result[1]

        conftest.log.info("########## Executed :: Test ZIA disable reason ##########")
     
    @pytest.mark.xray("QA-264156")
    @allure.title('Disable ZIA with disable password and disable reason enabled')
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_zia_disable_password_and_reason(self):
        '''
        Steps:
            1. Enable Disable_Password for ZIA and Disable_Reason in the App profile
            2. Update Policy in ZCC
            3. Disable ZIA with password and disable reason
            4. Enable ZIA for further test cases
        '''
        conftest.log.info("########## Executing :: Test ZIA disable password and reason  ##########")

        result = conftest.app_profile_obj.edit_app_profile(zia_disable_password=conftest.test_password_zia, disable_reason=True)
        assert result[0], result[1]

        result = conftest.zcc_obj.update_policy()
        assert result[0], result[1]

        result = conftest.zcc_obj.toggle_service(service="ZIA", action=False, password=conftest.test_password_zia, disable_reason=conftest.test_reason_zia)
        assert result[0], result[1]

        result = conftest.zcc_obj.toggle_service(service="ZIA")
        assert result[0], result[1]
        

        conftest.log.info("########## Executed :: Test ZIA disable password and reason ##########")
    

    @allure.title("teardown")
    def teardown_class(self):
        """
        1. Delete App profile
        2. Delete forwarding profile
        3. Logout ZCC
        """
        conftest.log.info("########## Tearing Down ##########")

        conftest.app_profile_obj.delete_app_profile()

        conftest.app_profile_obj.forwarding_profile.delete_forwarding_profile()

        conftest.zcc_obj.logout()

        conftest.log.info(f"########## Executed all applicable cases from {os.path.basename(__file__)} ##########")
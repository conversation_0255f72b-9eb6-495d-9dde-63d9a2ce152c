# ZCC Password Regression

This suite contains smoke test cases for 

#### Version Info
iOS ZApp 3.7 +

#### This Suite includes testcases 
    * Disable ZIA/ZPA service with password/disable reason/password and disable reason
    * End to end ZIA traffic flow verification
    * End to end ZPA traffic flow verification
    * Login to ZCC with multiple authentication methods
    * Zia reactivation, logout password and other zcc events
    * ZCC UI fields verification


#### Prerequisites
    * Export logs via airdrop requires both mac and iOS device use same apple ID in order to accept files automatically. Make sure to keep airdrop up and working.
    * Pass "MAC_HOSTNAME" such as "Priyans<PERSON>'s Macbook Pro" as it is required for airdrop to work as expected
    * Safari, Chrome and MS Edge browsers should be installed on the device to test traffic through multiple browsers. Make sure to open these apps once so that it would not hinder automation flow.
    * ZCC build installed and in Logged Out state. Open app and accept popups once if installing through testflight.
    * A tenant account with ZIA and ZPA configurations, filled in config.json file.
    * In config file give following :
    --> "ZPA_APPLICATION_NAME" : {"private":"authtest2.com","public":"ferrari.com"} (private and public zpa app configured on ZPA portal given as a dictionary)
    --> "IDP_NAME" : "Okta" (idp name configured on ZIA portal)
    * Quick Setup Guide: https://confluence.corp.zscaler.com/display/~SKalra/Setup


### Executing program

* Run terminal in Admin mode
* Command:

```
sudo python3 trigger_automation.py --testcases OS_Ios/test_suite/sanity --zccVersion 3.7  --config yourconfig.json
```


### Authors

Priyanshu Sharma (<EMAIL>)
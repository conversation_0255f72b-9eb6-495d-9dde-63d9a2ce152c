# ZCC ZIA Posture
This suite covers the ZIA Posture evaluation in ZCC iOS 

## Supported version
ZCC 3.7+

## Documentation
https://confluence.corp.zscaler.com/display/~priyanshu.sharma/iOS+Automation+Setup

### Note
An initial setup of all the device posture needs to be done on the system

### Initial Setup
1. Install below certificates on iOS device from "/OS_Ios/resource/certificates":
    - CertificateTrustCert.pem
    - CertificateTrustNonVerified.pem
2. Push app configurations from MDM with Ownership Variable as "Ownership123" 
3. Make sure device is not jailbroken

## Basic Flow
1. Create a device posture, eg: Certificate Trust
2. Create a ZIA posture profile with the newly created device posture as the criteria for the expected level of trust, eg: Certificate Trust as High Trust
3. Assign the ZIA posture profile to the required device policy.
4. Create URL filters in ZIA admin portal, eg: Block URL's related to Radio/Social Media if the device trust level is Unknown, Low or Medium
5. Login/Update policy to validate the trust level of the device.
6. Validate the action applied by SME based on the URL accessed & the filters added in ZIA admin portal.

## Command To Trigger Automation
    sudo python3 trigger_automation.py --testcases OS_Ios/test_suite/zia/zia_posture/ --zccVersion 3.8  --config <youconfig.json>.json

### Author
Priyanshu Sharma (<EMAIL>)

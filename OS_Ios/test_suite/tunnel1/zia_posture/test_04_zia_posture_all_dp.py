import allure
import subprocess
import pytest
from common_lib.Custom_Markers import add_markers,zcc_mark_version #Required!
# from OS_Ios.test_suite.zia.zia_posture import conftest
from OS_Ios.test_suite.tunnel1.zia_posture import conftest
import time

class TestZiaPostureAll:
    """
    Test cases for evaluating ZIA postures in Tunnel 1.0.

    Attributes:
        unauthorized_modification_posture_name (str): Unauthorized modification posture name.
        certificate_trust_verified_name (str): Certificate trust verified posture name.
        certificate_trust_non_verified_name (str): Certificate trust non-verified posture name.
        ownership_variable (str): Ownership variable value.
        ownership_variable_posture_name (str): Ownership variable posture name.

    Methods:
        setup_class: Sets up the test environment.
        teardown_class: Tears down the test environment.
        test_*: Test cases for various ZIA posture scenarios.
    """
    unauthorized_modification_posture_name = "unauthorized_modification_posture"
    certificate_trust_verified_name = "certificate_trust_verified"
    certificate_trust_non_verified_name = "certificate_trust_non_verified"
    ownership_variable = "Ownership123"
    ownership_variable_posture_name = "ownership_variable_posture"
    @allure.title("Setup class")
    def setup_class(self):
        """
        1. Delete URL policies, app profle, forwarding profile, zia posture profile, device postures
        2. Create forwarding profile, app profile, zpa access policy, device postures, create zia posture profile, assign zia posture profile to app profile, edit URL Filtering policies
        3. Login to zcc
        """
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Allow", policy_rule="allow")
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Block", policy_rule="block")
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Allow_All", policy_rule="allow")
        conftest.ma_app_profile.delete_app_profile()
        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()

        # Toggle ZIA on
        result = conftest.ma_service_entitlement.toggle_zia(action=True)
        assert result[0], result[1]  # Check if the toggle was successful

        # Toggle ZPA on
        result = conftest.ma_service_entitlement.toggle_zpa(action=True)
        assert result[0], result[1]  # Check if the toggle was successful

        # Toggle ZDX off
        result = conftest.ma_service_entitlement.toggle_zdx(action=False)
        assert result[0], result[1]  # Check if the toggle was successful
        
        conftest.zcc_zia_posture.zia_posture.delete_zia_posture_profile(profile_name="ZIAPosture")
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.unauthorized_modification_posture_name)
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.ownership_variable_posture_name)
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.certificate_trust_verified_name)
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.certificate_trust_non_verified_name)
        
        result = conftest.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert result[0], result[1]

        result = conftest.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        conftest.zpa_policy.delete_policy(policy_type="AccessPolicy")

        result = conftest.zpa_policy.create_access_policy(zpa_user_name=conftest.config["ZPA_ADMIN_ID"], zpa_password=conftest.config["ZPA_ADMIN_PASSWORD"])
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.device_posture.create_unauthorised_posture(posture_name=self.unauthorized_modification_posture_name)
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.device_posture.create_certificate_trust_dp(posture_name=self.certificate_trust_verified_name, cert_to_be_uploaded="Cert_Trust_Verified")
        assert result[0], result[1]
        
        result = conftest.zcc_zia_posture.device_posture.create_certificate_trust_dp(posture_name=self.certificate_trust_non_verified_name, cert_to_be_uploaded="Cert_Trust_Non_Verified")
        assert result[0], result[1]
        
        result = conftest.zcc_zia_posture.device_posture.create_ownership_posture(posture_name=self.ownership_variable_posture_name, value=self.ownership_variable)
        assert result[0], result[1]
        
        result = conftest.zcc_zia_posture.zia_posture.create_zia_posture_profile(high_risk_condition=self.certificate_trust_verified_name,med_risk_condition=self.certificate_trust_non_verified_name, profile_name="ZIAPosture")
        assert result[0], result[1]

        # result = conftest.ma_app_profile.edit_app_profile(zia_posture="ZIAPosture", use_tunnel_sdk_version_43=True)
        # assert result[0], result[1]        
        result = conftest.ma_app_profile.edit_app_profile(zia_posture="ZIAPosture")
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.create_delete_url_policy(action="create", policy_name="Automation_URL_Allow", policy_rule="allow")
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.create_delete_url_policy(action="create", policy_name="Automation_URL_Block", policy_rule="block")
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.create_delete_url_policy(action="create", policy_name="Automation_URL_Allow_All", policy_rule="allow")
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.zia_url_policy.zia_helper.zia_activate()
        assert result[0], result[1]

        # Configure AUP settings
        result=conftest.ma_notifications.configure_aup(aup_frequency="never")
        assert result[0], result[1]  # Check if the configuration was successful

        # Enable SAML on ZIA
        result=conftest.saml_zia_obj.saml_action_smui(mode='enable')
        assert result[0], result[1]  # Check if the enablement was successful

        # Change SAML IDP on ZIA
        result =conftest.saml_zia_obj.change_saml_on_zia(idp_name=conftest.config['IDP_NAME'])
        assert result[0],result[1]  # Check if the change was successful

        # Disable browser-based authentication on MA
        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]  # Check if the disablement was successful
    
        result = conftest.zcc.login(
            aup=False,
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zpa_user=conftest.config["ZPA_USER_ID"],
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],
            zia_saml_login=True
        )
        assert result[0], result[1]

        result = conftest.zcc.validate_zcc_logged_in()
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.zia_url_policy.edit_url_filter_policy(policy={'deviceTrustLevels':['HIGH_TRUST','MEDIUM_TRUST']},policy_name='Automation_URL_Allow')
        assert result[0], result[1]
        
        result = conftest.zcc_zia_posture.zia_url_policy.edit_url_filter_policy(policy={'deviceTrustLevels':['LOW_TRUST','UNKNOWN_DEVICETRUSTLEVEL']},policy_name='Automation_URL_Block')
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.zia_url_policy.zia_helper.zia_activate()
        assert result[0], result[1]

# ALL DEVICE POSTURES COMBINATION
    @pytest.mark.xray("QA-301619")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - Certificate trust verified as medium Trust & Unauthorized Modification as Low Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_certificate_trust_as_medium_and_unauthorized_modification_as_low(self):
        """
        This test case evaluates ZIA postures in Tunnel 1.0 with Certificate trust verified as medium Trust and Unauthorized Modification as Low Trust.
    
        It performs the following steps:
        1. Edits the ZIA posture profile to set Certificate trust verified as medium Trust and Unauthorized Modification as Low Trust.
        2. Updates the policy.
        3. Exports logs via airdrop.
        4. Validates the trust level from logs.
        5. Validates if a URL is accessible.
        6. Resets the ZIA posture profile.
        7. Updates the policy again.
        8. Exports logs via airdrop again.
        9. Validates the trust level from logs again.
        10. Validates if a URL is not accessible.
    
        Args:
            self: The instance of the class.
    
        Returns:
            None
        """
    
        # Edit the ZIA posture profile to set Certificate trust verified as medium Trust and Unauthorized Modification as Low Trust
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='', 
            med_risk_condition=self.certificate_trust_verified_name, 
            low_risk_condition=self.unauthorized_modification_posture_name, 
            profile_name="ZIAPosture"
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the export operation was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        # Assert that the validation was successful
        assert result[0], result[1]
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the validation was successful
        assert result[0], result[1]
    
        # Reset the ZIA posture profile
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', med_risk_condition='', profile_name="ZIAPosture")
        # Assert that the reset operation was successful
        assert result[0], result[1]

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
    
        # Validate if a URL is not accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the validation was successful
        assert result[0], result[1]

    @pytest.mark.xray("QA-301620")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - Unauthorized Modification verified as medium Trust & True Ownership Variable as Low Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_unauthorized_modification_as_medium_and_ownership_variable_as_low(self):
        """
        Test case to evaluate ZIA postures in Tunnel 1.0.
    
        This test case verifies that Unauthorized Modification is classified as medium trust and True Ownership Variable is classified as low trust.
        It edits the ZIA posture profile, updates the policy, exports logs, validates the trust level from logs, and checks if a URL is accessible.
    
        Args:
            self: The instance of the class.
    
        Returns:
            None
        """
        # Edit the ZIA posture profile to set Unauthorized Modification as medium trust and True Ownership Variable as low trust
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='',med_risk_condition=self.unauthorized_modification_posture_name,low_risk_condition=self.ownership_variable_posture_name,profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the edit operation was successful

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs via airdrop to the specified MAC hostname
        result=conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        assert result[0], result[1]  # Assert that the trust level is correct
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        assert result[0], result[1]  # Assert that the URL is accessible
    
        # Reset the ZIA posture profile
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='',med_risk_condition='',profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the reset operation was successful

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
    
        # Validate if a URL is not accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        assert result[0], result[1]  # Assert that the URL is not accessible

    @pytest.mark.xray("QA-301621")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - True Ownership Variable verified as medium Trust & Certificate Trust verified as Low Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_ownership_variable_as_medium_and_certificate_trust_as_low(self):
        """
        Test case to evaluate ZIA postures in Tunnel 1.0 with True Ownership Variable verified as medium Trust & Certificate Trust verified as Low Trust.
        
        This test case performs the following steps:
        1. Edits the ZIA posture profile to set medium risk condition to ownership variable and low risk condition to certificate trust.
        2. Updates the policy.
        3. Exports logs via airdrop.
        4. Validates the trust level from logs.
        5. Validates if a URL is accessible.
        6. Reverts the ZIA posture profile changes.
        7. Updates the policy again.
        8. Exports logs via airdrop again.
        9. Validates the trust level from logs again.
        10. Validates if a URL is not accessible.
        
        Args:
            None
        
        Raises:
            AssertionError: If any of the assertions fail.
        
        Returns:
            None
        """
        
        # Edit ZIA posture profile to set medium risk condition to ownership variable and low risk condition to certificate trust
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='',med_risk_condition=self.ownership_variable_posture_name,low_risk_condition=self.certificate_trust_verified_name,profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the edit operation was successful

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs via airdrop
        result=conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Validate trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        assert result[0], result[1]  # Assert that the trust level is as expected
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        assert result[0], result[1]  # Assert that the URL is accessible
    
        # Revert ZIA posture profile changes
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='',med_risk_condition='',profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the edit operation was successful

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
    
        # Validate if a URL is not accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        assert result[0], result[1]  # Assert that the URL is not accessible

    @pytest.mark.xray("QA-301622")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - Certificate trust verified as high Trust & Unauthorized Modification as medium Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_certificate_trust_as_high_and_unauthorized_modification_as_medium(self):
        """
        Test case to evaluate ZIA postures in Tunnel 1.0 with Certificate trust verified as high Trust & Unauthorized Modification as medium Trust.
    
        This test case performs the following steps:
        1. Edits the ZIA posture profile with high risk condition as Certificate trust verified and medium risk condition as Unauthorized Modification.
        2. Updates the policy.
        3. Exports logs via airdrop.
        4. Validates the trust level from logs.
        5. Validates if a URL is accessible.
        6. Repeats steps 1-5 with high and low risk conditions reset.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile with high risk condition as Certificate trust verified and medium risk condition as Unauthorized Modification
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition=self.certificate_trust_verified_name, 
            med_risk_condition=self.unauthorized_modification_posture_name, 
            low_risk_condition='', 
            profile_name="ZIAPosture"
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the export operation was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        # Assert that the trust level is valid
        assert result[0], result[1]
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the URL is accessible
        assert result[0], result[1]
    
        # Reset the high and low risk conditions and repeat the above steps
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', low_risk_condition='', profile_name="ZIAPosture")
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
    
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]
    
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        assert result[0], result[1]
    
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        assert result[0], result[1]

    @pytest.mark.xray("QA-301623")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - Unauthorized Modification verified as high Trust & True Ownership Variable as medium Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_unauthorized_modification_as_high_and_ownership_variable_as_medium(self):
        """
        Test case to evaluate ZIA postures in Tunnel 1.0 - Unauthorized Modification verified as high Trust & True Ownership Variable as medium Trust.
        
        This test case performs the following steps:
        - Edits the ZIA posture profile to set high risk condition as unauthorized modification and medium risk condition as ownership variable.
        - Updates the policy.
        - Exports logs via airdrop.
        - Validates the trust level from logs.
        - Validates if a URL is accessible.
        - Reverts the ZIA posture profile changes.
        - Updates the policy again.
        - Exports logs via airdrop again.
        - Validates the trust level from logs again.
        - Validates if a URL is accessible again.
        
        Args:
            None
        
        Raises:
            AssertionError: If any of the assertions fail.
        
        Returns:
            None
        """

        # Edit the ZIA posture profile to set high risk condition as unauthorized modification and medium risk condition as ownership variable
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition=self.unauthorized_modification_posture_name,med_risk_condition=self.ownership_variable_posture_name,low_risk_condition='',profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the edit operation was successful

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs via airdrop
        result=conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        assert result[0], result[1]  # Assert that the trust level is as expected
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        assert result[0], result[1]  # Assert that the URL is accessible
    
        # Revert the ZIA posture profile changes
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='',low_risk_condition='',profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the edit operation was successful

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs via airdrop again
        result=conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Validate the trust level from logs again
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        assert result[0], result[1]  # Assert that the trust level is as expected
    
        # Validate if a URL is accessible again
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        assert result[0], result[1]  # Assert that the URL is accessible

    @pytest.mark.xray("QA-301624")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - True Ownership Variable verified as high Trust & Certificate Trust verified as medium Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_ownership_variable_as_high_and_certificate_trust_as_medium(self):
        """
        This test case evaluates ZIA postures in Tunnel 1.0 with True Ownership Variable verified as high Trust 
        and Certificate Trust verified as medium Trust.
    
        It performs the following steps:
        1. Edits the ZIA posture profile to set high risk condition to ownership variable and medium risk condition to certificate trust.
        2. Updates the policy.
        3. Exports logs via airdrop.
        4. Validates the trust level from logs.
        5. Validates if a URL is accessible.
        6. Resets the ZIA posture profile to default.
        7. Updates the policy again.
        8. Exports logs via airdrop again.
        9. Validates the trust level from logs again.
        10. Validates if a URL is accessible again.
    
        Args:
            self: The instance of the class.
    
        Returns:
            None
        """
    
        # Edit the ZIA posture profile to set high risk condition to ownership variable and medium risk condition to certificate trust
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition=self.ownership_variable_posture_name, 
            med_risk_condition=self.certificate_trust_verified_name, 
            low_risk_condition='', 
            profile_name="ZIAPosture"
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the export operation was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        # Assert that the trust level is correct
        assert result[0], result[1]
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the URL is accessible
        assert result[0], result[1]
    
        # Reset the ZIA posture profile to default
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='', 
            low_risk_condition='', 
            profile_name="ZIAPosture"
        )
        # Assert that the reset operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy again
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop again
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the export operation was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs again
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        # Assert that the trust level is correct
        assert result[0], result[1]
    
        # Validate if a URL is accessible again
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the URL is accessible
        assert result[0], result[1]

    @pytest.mark.xray("QA-301625")
    @allure.title("Evaluate ZIA postures with all three trust levels in Tunnel 1.0 - Certificate trust verified as high Trust, True Ownership Variable as medium trust and Unauthorized Modification as low Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_ct_high_ov_medium_um_low(self):
        """
        Test case to evaluate ZIA postures with all three trust levels in Tunnel 1.0.
    
        This test case evaluates the ZIA postures by setting the certificate trust verified as high trust, 
        true ownership variable as medium trust, and unauthorized modification as low trust. It then updates 
        the policy, exports the logs, and validates the trust level from the logs. The test case also 
        validates if the URL is accessible after each update.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile with the provided risk conditions
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition=self.certificate_trust_verified_name, 
            med_risk_condition=self.ownership_variable_posture_name, 
            low_risk_condition=self.unauthorized_modification_posture_name, 
            profile_name="ZIAPosture"
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Export the logs
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the log export was successful
        assert result[0], result[1]
    
        # Validate the trust level from the logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        # Assert that the trust level validation was successful
        assert result[0], result[1]
    
        # Validate if the URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the URL is accessible
        assert result[0], result[1]
    
        # Edit the ZIA posture profile with the provided risk conditions (medium and low trust empty)
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='', 
            profile_name="ZIAPosture"
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Export the logs
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the log export was successful
        assert result[0], result[1]
    
        # Validate the trust level from the logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        # Assert that the trust level validation was successful
        assert result[0], result[1]
    
        # Validate if the URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the URL is accessible
        assert result[0], result[1]
    
        # Edit the ZIA posture profile with the provided risk conditions (high and medium trust empty)
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='', 
            med_risk_condition='', 
            profile_name="ZIAPosture"
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
    
        # Validate if the URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the URL is not accessible
        assert result[0], result[1]
    
        # Edit the ZIA posture profile with the provided risk conditions (all trust levels empty)
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='', 
            med_risk_condition='', 
            low_risk_condition='', 
            profile_name="ZIAPosture"
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Export the logs
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the log export was successful
        assert result[0], result[1]
    
        # Validate the trust level from the logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(1)
        # Assert that the trust level validation was successful
        assert result[0], result[1]
    
        # Validate if the URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the URL is not accessible
        assert result[0], result[1]

    @pytest.mark.xray("QA-301626")
    @allure.title("Evaluate ZIA postures with all three trust levels in Tunnel 1.0 - Unauthorized Modification verified as high Trust, Certificate trust verified & True Ownership Variable as low Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_um_high_ct_medium_ov_low(self):
        """
        Test case to evaluate ZIA postures with all three trust levels in Tunnel 1.0.
    
        This test case verifies the following scenarios:
        - Unauthorized Modification verified as high Trust
        - Certificate trust verified as medium Trust
        - True Ownership Variable as low Trust
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Returns:
            None
        """
    
        # Edit ZIA posture profile to set high risk condition to Unauthorized Modification
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition=self.unauthorized_modification_posture_name,
            med_risk_condition=self.certificate_trust_verified_name,
            low_risk_condition=self.ownership_variable_posture_name,
            profile_name="ZIAPosture"
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the export operation was successful
        assert result[0], result[1]
    
        # Validate trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        # Assert that the trust level is correct
        assert result[0], result[1]
    
        # Validate if URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the URL is accessible
        assert result[0], result[1]
    
        # Edit ZIA posture profile to set high risk condition to empty
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', profile_name="ZIAPosture")
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the export operation was successful
        assert result[0], result[1]
    
        # Validate trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        # Assert that the trust level is correct
        assert result[0], result[1]
    
        # Validate if URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the URL is accessible
        assert result[0], result[1]
    
        # Edit ZIA posture profile to set high and medium risk conditions to empty
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', med_risk_condition='', profile_name="ZIAPosture")
        # Assert that the edit operation was successful
        assert result[0], result[1]

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
    
        # Validate if URL is not accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the URL is not accessible
        assert result[0], result[1]
    
        # Edit ZIA posture profile to set all risk conditions to empty
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', med_risk_condition='', low_risk_condition='', profile_name="ZIAPosture")
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the export operation was successful
        assert result[0], result[1]
    
        # Validate trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(1)
        # Assert that the trust level is correct
        assert result[0], result[1]
    
        # Validate if URL is not accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the URL is not accessible
        assert result[0], result[1]

    @pytest.mark.xray("QA-301627")
    @allure.title("Evaluate ZIA postures with all three trust levels in Tunnel 1.0 - True Ownership Variable verified as high Trust, Unauthorized Modification as medium trust & Certificate Trust verified as low Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_ov_high_um_medium_ct_low(self):
        """
        This test case evaluates ZIA postures with all three trust levels in Tunnel 1.0.
        
        It tests the following scenarios:
        - True Ownership Variable verified as high Trust
        - Unauthorized Modification as medium trust
        - Certificate Trust verified as low Trust
        
        The test case performs the following steps:
        - Edits the ZIA posture profile with the specified risk conditions
        - Updates the policy
        - Exports logs via airdrop
        - Validates the trust level from logs
        - Validates if a URL is accessible
        
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
        
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile with high risk condition as ownership variable posture name,
        # medium risk condition as unauthorized modification posture name, and low risk condition as certificate trust verified name
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition=self.ownership_variable_posture_name,
            med_risk_condition=self.unauthorized_modification_posture_name,
            low_risk_condition=self.certificate_trust_verified_name,
            profile_name="ZIAPosture"
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop to the specified MAC hostname
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the export operation was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs with a trust level of 4
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        # Assert that the validation was successful
        assert result[0], result[1]
    
        # Validate if a URL is accessible with accessed set to True
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the validation was successful
        assert result[0], result[1]
    
        # Edit the ZIA posture profile with high risk condition as an empty string
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', profile_name="ZIAPosture")
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop to the specified MAC hostname
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the export operation was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs with a trust level of 3
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        # Assert that the validation was successful
        assert result[0], result[1]
    
        # Validate if a URL is accessible with accessed set to True
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the validation was successful
        assert result[0], result[1]
    
        # Edit the ZIA posture profile with high risk condition and medium risk condition as empty strings
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', med_risk_condition='', profile_name="ZIAPosture")
        # Assert that the edit operation was successful
        assert result[0], result[1]

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
    
        # Validate if a URL is accessible with accessed set to False
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the validation was successful
        assert result[0], result[1]
    
        # Edit the ZIA posture profile with high risk condition, medium risk condition, and low risk condition as empty strings
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', med_risk_condition='', low_risk_condition='', profile_name="ZIAPosture")
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop to the specified MAC hostname
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the export operation was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs with a trust level of 1
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(1)
        # Assert that the validation was successful
        assert result[0], result[1]
    
        # Validate if a URL is accessible with accessed set to False
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the validation was successful
        assert result[0], result[1]

    @allure.title("Teardown class")
    def teardown_class(self):
        """
        Teardown class to clean up resources after test execution.
    
        This method deletes various policies, profiles, and postures created during test execution.
        It ensures a clean state for subsequent test runs.
    
        Args:
            self: The instance of the class.
    
        Returns:
            None
        """
        # Delete URL policies
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Allow", policy_rule="allow")  # Delete Automation_URL_Allow policy
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Block", policy_rule="block")  # Delete Automation_URL_Block policy
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Allow_All", policy_rule="allow")  # Delete Automation_URL_Allow_All policy
    
        # Delete app profiles
        conftest.ma_app_profile.delete_app_profile()  # Delete app profile
    
        # Delete forwarding profiles
        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()  # Delete forwarding profile
    
        # Delete ZIA posture profiles
        conftest.zcc_zia_posture.zia_posture.delete_zia_posture_profile(profile_name="ZIAPosture")  # Delete ZIAPosture profile
    
        # Delete device postures
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.certificate_trust_verified_name)  # Delete certificate trust verified posture
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.certificate_trust_non_verified_name)  # Delete certificate trust non-verified posture
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.unauthorized_modification_posture_name)  # Delete unauthorized modification posture
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.ownership_variable_posture_name)  # Delete ownership variable posture
    
        # Logout from the system
        conftest.zcc.logout()  # Logout from the system
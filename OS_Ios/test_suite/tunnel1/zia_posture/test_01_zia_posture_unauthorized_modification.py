import allure
import subprocess
import pytest
from common_lib.Custom_Markers import add_markers,zcc_mark_version #Required!
from OS_Ios.test_suite.tunnel1.zia_posture import conftest
import time

class TestZiaPostureUnauthorizedModification:
    """
    Test cases for evaluating ZIA postures with Unauthorized Modification in Tunnel 1.0.

    Attributes:
        um_high (str): Unauthorized modification high risk condition.
        um_medium (str): Unauthorized modification medium risk condition.
        um_low (str): Unauthorized modification low risk condition.

    Methods:
        setup_class: Sets up the test environment.
        teardown_class: Tears down the test environment.
        test_*: Test cases for various Unauthorized Modification scenarios.
    """
    um_high = "UM_HIGH"
    um_medium = "UM_MEDIUM"
    um_low = "UM_LOW"
    @allure.title("Setup")
    def setup_class(self):
        """
        1. Delete URL policies, app profle, forwarding profile, zia posture profile, device postures
        2. Create forwarding profile, app profile, zpa access policy, device postures, create zia posture profile, assign zia posture profile to app profile, edit URL Filtering policies
        3. Login to zcc
        """
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Allow", policy_rule="allow")
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Block", policy_rule="block")
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Allow_All", policy_rule="allow")

        conftest.ma_app_profile.delete_app_profile()

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()

        # Toggle ZIA on
        result = conftest.ma_service_entitlement.toggle_zia(action=True)
        assert result[0], result[1]  # Check if the toggle was successful

        # Toggle ZPA on
        result = conftest.ma_service_entitlement.toggle_zpa(action=True)
        assert result[0], result[1]  # Check if the toggle was successful

        # Toggle ZDX off
        result = conftest.ma_service_entitlement.toggle_zdx(action=False)
        assert result[0], result[1]  # Check if the toggle was successful

        conftest.zcc_zia_posture.zia_posture.delete_zia_posture_profile(profile_name="ZIAPosture")
        
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.um_high)

        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.um_medium)

        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.um_low)
        
    
        time.sleep(5)
        result = conftest.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert result[0], result[1]

        result = conftest.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        conftest.zpa_policy.delete_policy(policy_type="AccessPolicy")

        result = conftest.zpa_policy.create_access_policy(
            zpa_user_name=conftest.config["ZPA_ADMIN_ID"], zpa_password=conftest.config["ZPA_ADMIN_PASSWORD"]
        )
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.device_posture.create_unauthorised_posture(posture_name=self.um_high)
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.device_posture.create_unauthorised_posture(posture_name=self.um_medium)
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.device_posture.create_unauthorised_posture(posture_name=self.um_low)
        assert result[0], result[1]
        
        result = conftest.zcc_zia_posture.zia_posture.create_zia_posture_profile(
            high_risk_condition=self.um_high,
            med_risk_condition=self.um_medium,
            low_risk_condition=self.um_low,
            profile_name="ZIAPosture",
        )
        assert result[0], result[1]

        # result = conftest.ma_app_profile.edit_app_profile(zia_posture="ZIAPosture", use_tunnel_sdk_version_43=True)
        # assert result[0], result[1]

        result = conftest.ma_app_profile.edit_app_profile(zia_posture="ZIAPosture")
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.create_delete_url_policy(action="create", policy_name="Automation_URL_Allow", policy_rule="allow")
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.create_delete_url_policy(action="create", policy_name="Automation_URL_Block", policy_rule="block")
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.create_delete_url_policy(action="create", policy_name="Automation_URL_Allow_All", policy_rule="allow")
        assert result[0], result[1]

         # Activate the ZIA policy
        result = conftest.zcc_zia_posture.zia_url_policy.zia_helper.zia_activate()
        # Assert that the policy activation was successful
        assert result[0], result[1]

        # Configure AUP settings
        result=conftest.ma_notifications.configure_aup(aup_frequency="never")
        assert result[0], result[1]  # Check if the configuration was successful

        # Enable SAML on ZIA
        result=conftest.saml_zia_obj.saml_action_smui(mode='enable')
        assert result[0], result[1]  # Check if the enablement was successful

        # Change SAML IDP on ZIA
        result =conftest.saml_zia_obj.change_saml_on_zia(idp_name=conftest.config['IDP_NAME'])
        assert result[0],result[1]  # Check if the change was successful

        # Disable browser-based authentication on MA
        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]  # Check if the disablement was successful

        result = conftest.zcc.login(
            aup=False,
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zpa_user=conftest.config["ZPA_USER_ID"],
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],
            zia_saml_login=True
        )
        assert result[0], result[1]

        result = conftest.zcc.validate_zcc_logged_in()
        assert result[0], result[1]

        # Edit the URL filter policy to allow high trust devices
        result = conftest.zcc_zia_posture.zia_url_policy.edit_url_filter_policy(policy={'deviceTrustLevels':['HIGH_TRUST','MEDIUM_TRUST']}, policy_name='Automation_URL_Allow')
        # Assert that the policy edit was successful
        assert result[0], result[1]
    
        # Edit the URL filter policy to block medium, low, and unknown trust devices
        result = conftest.zcc_zia_posture.zia_url_policy.edit_url_filter_policy(policy={'deviceTrustLevels':['LOW_TRUST','UNKNOWN_DEVICETRUSTLEVEL']}, policy_name='Automation_URL_Block')
        # Assert that the policy edit was successful
        assert result[0], result[1]
    
        # Activate the ZIA policy
        result = conftest.zcc_zia_posture.zia_url_policy.zia_helper.zia_activate()
        # Assert that the policy activation was successful
        assert result[0], result[1]

# UNAUTHORIZED MODIFICATION DEVICE POSTURE

    @pytest.mark.xray(["QA-301765", "QA-301617"])
    @allure.title("Evaluate ZIA postures - Unauthorized Modification as low trust")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_unauthorized_modification_as_low_trust(self):
        """
        Test case to evaluate ZIA postures - Unauthorized Modification as low trust.
        
        This test case evaluates the ZIA postures by setting the unauthorized modification as low trust, 
        updating the policy, exporting logs, validating the trust level from logs, and validating if a URL is accessible.
        
        Args:
            None
        
        Raises:
            AssertionError: If any of the assertions fail.
        
        Returns:
            None
        """
        # Edit the ZIA posture profile to set the unauthorized modification as low trust
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', med_risk_condition='', low_risk_condition=self.um_low, profile_name="ZIAPosture")
        
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()

        # Update the policy
        result = conftest.zcc.update_policy()
        
        # Assert that the policy update was successful
        assert result[0], result[1]
        
        # Export logs via airdrop to the specified MAC hostname
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        
        # Assert that the log export was successful
        assert result[0], result[1]
        
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(2)
        
        # Assert that the trust level validation was successful
        assert result[0], result[1]
        
        # Validate if a URL is accessible (expected to be inaccessible)
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        
        # Assert that the URL accessibility validation was successful
        assert result[0], result[1]
        
        # Reset the ZIA posture profile to its original state
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', med_risk_condition='', low_risk_condition='', profile_name="ZIAPosture")
        assert result[0], result[1]

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
        
        # Validate if a URL is accessible (expected to be inaccessible) again
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        assert result[0], result[1]

    @pytest.mark.xray(["QA-301764","QA-301767"])
    @allure.title("Evaluate ZIA postures - Unauthorized Modification as medium trust")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_unauthorized_modification_as_medium_trust(self):
        """
        Test case to evaluate ZIA postures with Unauthorized Modification as medium trust.
    
        This test case performs the following steps:
        1. Edits the ZIA posture profile to set medium risk condition.
        2. Updates the policy.
        3. Exports logs via airdrop.
        4. Validates the trust level from logs.
        5. Validates if the URL is accessible.
        6. Resets the ZIA posture profile.
        7. Updates the policy again.
        8. Exports logs via airdrop again.
        9. Validates the trust level from logs again.
        10. Validates if the URL is not accessible.
    
        Args:
            self: The instance of the class.
    
        Returns:
            None
        """
    
        # Activate the ZIA helper
        result = conftest.zcc_zia_posture.zia_url_policy.zia_helper.zia_activate()
        assert result[0], result[1]  # Assert that the activation was successful
    
        # Edit the ZIA posture profile to set medium risk condition
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='',med_risk_condition=self.um_medium,low_risk_condition='',profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the profile edit was successful

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs via airdrop
        result=conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        assert result[0], result[1]  # Assert that the trust level validation was successful
    
        # Validate if the URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        assert result[0], result[1]  # Assert that the URL is accessible
    
        # Reset the ZIA posture profile
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='',med_risk_condition='',low_risk_condition='',profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the profile reset was successful

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
    
        # Validate if the URL is not accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        assert result[0], result[1]  # Assert that the URL is not accessible

    @pytest.mark.xray(["QA-301763","QA-301766"])
    @allure.title("Evaluate ZIA postures - Unauthorized Modification as high trust")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_unauthorized_modification_as_high_trust(self):
        """
        Test case to evaluate ZIA postures with Unauthorized Modification as high trust.
    
        This test case performs the following steps:
        1. Edits the ZIA posture profile to set Unauthorized Modification as high trust.
        2. Updates the policy.
        3. Exports logs via airdrop.
        4. Validates the trust level from logs.
        5. Validates if a URL is accessible.
        6. Resets the ZIA posture profile.
        7. Updates the policy again.
        8. Exports logs via airdrop again.
        9. Validates the trust level from logs again.
        10. Validates if a URL is not accessible.
    
        Args:
            self: The instance of the class.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile to set Unauthorized Modification as high trust
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition=self.um_high, med_risk_condition='', low_risk_condition='', profile_name="ZIAPosture")
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the log export was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        # Assert that the trust level is correct
        assert result[0], result[1]
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the URL is accessible
        assert result[0], result[1]
    
        # Reset the ZIA posture profile
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', med_risk_condition='', low_risk_condition='', profile_name="ZIAPosture")
        # Assert that the reset operation was successful
        assert result[0], result[1]

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
    
        # Validate if a URL is not accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the URL is not accessible
        assert result[0], result[1]


    @pytest.mark.xray("QA-301646")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - when device trust level is high and tray is not running")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_high_trust_tray_not_running(self):
        """
        Test case to evaluate ZIA postures in Tunnel 1.0 when device trust level is high and tray is not running.
    
        This test case performs the following steps:
        1. Edits the ZIA posture profile to set high risk condition.
        2. Updates the policy.
        3. Exports logs via airdrop.
        4. Terminates the ZCC app.
        5. Validates the trust level from logs.
        6. Validates if a URL is accessible.
        7. Edits the ZIA posture profile to reset risk conditions.
        8. Updates the policy.
        9. Exports logs via airdrop.
        10. Terminates the ZCC app.
        11. Validates the trust level from logs.
        12. Validates if a URL is not accessible.
    
        Args:
            self: The instance of the class.
    
        Returns:
            None
        """
    
        # Edit the ZIA posture profile to set high risk condition
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition=self.um_high, med_risk_condition='', low_risk_condition='', profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the edit operation was successful

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Terminate the ZCC app
        time.sleep(1)
        conftest.zcc.ui.driver.terminate_app(conftest.zcc.elements.APP)
        time.sleep(10)

        conftest.zcc_zia_posture.sys_ops.access_application(url="https://example.com", browser="safari", zpa_app=True)

        time.sleep(30)

        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        assert result[0], result[1]  # Assert that the trust level is valid
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        assert result[0], result[1]  # Assert that the URL is accessible


    @pytest.mark.xray("QA-301645")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - when device trust level is medium and tray is not running")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_medium_trust_tray_not_running(self):
        """
        Test case to evaluate ZIA postures in Tunnel 1.0 when device trust level is medium and tray is not running.
    
        This test case performs the following steps:
        1. Edits the ZIA posture profile to set the medium risk condition.
        2. Updates the policy.
        3. Exports logs via airdrop.
        4. Terminates the ZCC app.
        5. Validates the trust level from logs.
        6. Validates if a URL is accessible.
        7. Edits the ZIA posture profile to reset the risk conditions.
        8. Updates the policy.
        9. Exports logs via airdrop.
        10. Terminates the ZCC app.
        11. Validates the trust level from logs.
        12. Validates if a URL is not accessible.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile to set the medium risk condition
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', med_risk_condition=self.um_medium, low_risk_condition='', profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the edit operation was successful

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Terminate the ZCC app
        time.sleep(1)
        conftest.zcc.ui.driver.terminate_app(conftest.zcc.elements.APP)
        time.sleep(10)

        conftest.zcc_zia_posture.sys_ops.access_application(url="https://example.com", browser="safari", zpa_app=True)

        time.sleep(30)
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        assert result[0], result[1]  # Assert that the trust level is 3
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        assert result[0], result[1]  # Assert that the URL is accessible


    @pytest.mark.xray("QA-301644")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - when device trust level is low and tray is not running")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_low_trust_tray_not_running(self):
        """
        Test case to evaluate ZIA postures in Tunnel 1.0 when device trust level is low and tray is not running.
    
        This test case performs the following steps:
        1. Edits the ZIA posture profile to set the low risk condition.
        2. Updates the policy.
        3. Exports logs via airdrop.
        4. Terminates the ZCC app.
        5. Validates the trust level from logs.
        6. Validates if a URL is accessible.
        7. Repeats steps 1-6 to test the posture profile with an empty low risk condition.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile to set the low risk condition
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', med_risk_condition='', low_risk_condition=self.um_low, profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the profile edit was successful

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Terminate the ZCC app
        time.sleep(1)
        conftest.zcc.ui.driver.terminate_app(conftest.zcc.elements.APP)
        time.sleep(10)

        conftest.zcc_zia_posture.sys_ops.access_application(url="https://example.com", browser="safari", zpa_app=True)

        time.sleep(30)
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(2)
        assert result[0], result[1]  # Assert that the trust level is valid
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        assert result[0], result[1]  # Assert that the URL is not accessible

    @pytest.mark.xray("QA-301643")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - when device trust level is unknown and tray is not running")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_unknown_trust_tray_not_running(self):
        """
        Test case to evaluate ZIA postures in Tunnel 1.0 when device trust level is unknown and tray is not running.
    
        This test case performs the following steps:
        1. Edits the ZIA posture profile to set high, medium, and low risk conditions.
        2. Updates the policy.
        3. Exports logs via airdrop.
        4. Terminates the ZCC application.
        5. Validates the trust level from logs.
        6. Validates if a URL is accessible.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile to set high, medium, and low risk conditions
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', med_risk_condition='', low_risk_condition='', profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the profile edit was successful

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Terminate the ZCC app
        time.sleep(1)
        conftest.zcc.ui.driver.terminate_app(conftest.zcc.elements.APP)
        time.sleep(10)

        conftest.zcc_zia_posture.sys_ops.access_application(url="https://example.com", browser="safari", zpa_app=True)

        time.sleep(30)
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(1)
        assert result[0], result[1]  # Assert that the trust level validation was successful
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        assert result[0], result[1]  # Assert that the URL accessibility validation was successful


    @pytest.mark.xray("QA-301631")
    @allure.title("Modify ZIA posture profile so that connected device moves from one Risk type to another")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_trust_level_evaluation_on_modifying_zia_posture_profile(self):
        """
        Test case to evaluate trust level on modifying ZIA posture profile.
    
        This test case modifies the ZIA posture profile to change the risk type of a connected device.
        It then validates the trust level from logs and checks if a URL is accessible.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile to set high risk condition to empty string
        # and low risk condition to um_low, for the profile named "ZIAPosture"
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', med_risk_condition='', low_risk_condition=self.um_low, profile_name="ZIAPosture")
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Export logs via airdrop to the device with hostname MAC_HOSTNAME
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the log export was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs, expecting a trust level of 2
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(2)
        # Assert that the trust level validation was successful
        assert result[0], result[1]
    
        # Validate if a URL is accessible, expecting it to be inaccessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the URL accessibility validation was successful
        assert result[0], result[1]
    
        # Edit the ZIA posture profile to set high risk condition to um_high
        # and low risk condition to um_low, for the profile named "ZIAPosture"
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition=self.um_high, med_risk_condition='', low_risk_condition=self.um_low, profile_name="ZIAPosture")
        # Assert that the edit operation was successful
        assert result[0], result[1]

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
    
        # Validate if a URL is accessible, expecting it to be accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the URL accessibility validation was successful
        assert result[0], result[1]

    @pytest.mark.xray("QA-301635")
    @allure.title("Remove_ZIA_Posture_from_AppProfile")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_remove_zia_posture_from_app_profile(self):
        """
        Test case to remove ZIA Posture from App Profile.
    
        This test case removes the ZIA Posture from the App Profile, updates the policy, 
        exports the logs, and validates the trust level from the logs.
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Returns:
            None
        """
        
        # Edit the ZIA Posture profile by removing the high, medium, and low risk conditions
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', med_risk_condition='', low_risk_condition=self.um_low, profile_name="ZIAPosture")
        # Assert that the operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the operation was successful
        assert result[0], result[1]
    
        # Export the logs via airdrop to the specified MAC hostname
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the operation was successful
        assert result[0], result[1]
    
        # Validate the trust level from the logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(2)
        # Assert that the operation was successful
        assert result[0], result[1]
        
        # Edit the App Profile by removing the ZIA Posture
        result = conftest.ma_app_profile.edit_app_profile(zia_posture=None)
        # Assert that the operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
            
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the operation was successful
        assert result[0], result[1]
    
        # Export the logs via airdrop to the specified MAC hostname
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the operation was successful
        assert result[0], result[1]
            
        # Validate the trust level from the logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(0)
        # Assert that the operation was successful
        assert result[0], result[1]
        

    @pytest.mark.xray("QA-301634")
    @allure.title("Delete/Remove device posture from configured expression on Low/Medium/High Risk Type on configured ZIA posture profile")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_delete_device_posture_from_applied_zia_posture_profile(self):
        """
        This test case deletes/removes a device posture from a configured expression 
        on Low/Medium/High Risk Type on a configured ZIA posture profile.
    
        It performs the following steps:
        1. Edits the ZIA posture profile to set the low risk condition.
        2. Updates the policy.
        3. Exports the logs via airdrop.
        4. Validates the trust level from the logs.
        5. Edits the ZIA posture profile to remove the device posture.
        6. Updates the policy again.
        7. Exports the logs via airdrop again.
        8. Validates the trust level from the logs again.
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Raises:
            AssertionError: If any of the assertions fail.
    
        Returns:
            None
        """
        result = conftest.ma_app_profile.edit_app_profile(zia_posture="ZIAPosture")
        assert result[0], result[1]
    
        # Edit the ZIA posture profile to set the low risk condition
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='',med_risk_condition=self.um_medium,low_risk_condition=self.um_low,profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the edit operation was successful

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export the logs via airdrop
        result=conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Validate the trust level from the logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        assert result[0], result[1]  # Assert that the trust level validation was successful
    
        # Edit the ZIA posture profile to remove the device posture
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='',med_risk_condition='',profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the edit operation was successful

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export the logs via airdrop again
        result=conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Validate the trust level from the logs again
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(2)
        assert result[0], result[1]  # Assert that the trust level validation was successful
    

    @pytest.mark.xray("QA-301641")
    @allure.title("ZCC_login_with_ZIA_posture_Medium_High_Risk_types")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zcc_login_with_zia_posture_medium_high_risk_types(self):
        """
        Test case to verify ZCC login with ZIA posture medium and high risk types.
    
        This test case updates the ZIA posture profile with medium and high risk conditions,
        updates the policy, exports the logs, and validates the trust level from the logs.
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Raises:
            AssertionError: If any of the assertions fail.
    
        Returns:
            None
        """
    
        # Update the ZIA posture profile with medium and high risk conditions
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            profile_name="ZIAPosture", 
            low_risk_condition='', 
            high_risk_condition=self.um_high, 
            med_risk_condition=self.um_medium
        )
        # Assert that the profile update was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Export the logs via airdrop to the specified hostname
        result = conftest.zcc.export_logs(
            airdrop=True, 
            airdrop_to=conftest.config["MAC_HOSTNAME"]
        )
        # Assert that the log export was successful
        assert result[0], result[1]
    
        # Validate the trust level from the logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        # Assert that the trust level validation was successful
        assert result[0], result[1]

   
    @pytest.mark.xray("QA-301640")
    @allure.title("ZCC_login_with_ZIA_posture_only_Low_Risk_type")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zcc_login_with_zia_posture_only_low_risk_type(self):
        """
        Test case to verify ZCC login with ZIA posture only Low Risk type.
        
        This test case covers the following scenarios:
        - Edit ZIA posture profile with low risk condition
        - Edit ZIA posture profile with high and medium risk conditions empty
        - Update policy
        - Export logs via airdrop
        - Validate trust level from logs
        
        Args:
            None
        
        Returns:
            None
        
        Raises:
            AssertionError: If any of the assertions fail
        """
        # Edit ZIA posture profile with low risk condition
        # This step updates the ZIA posture profile with the specified low risk condition
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(profile_name="ZIAPosture", low_risk_condition=self.um_low)
        assert result[0], result[1]  # Assert that the profile update was successful
    
        # Edit ZIA posture profile with high and medium risk conditions empty
        # This step updates the ZIA posture profile with empty high and medium risk conditions
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(profile_name="ZIAPosture", high_risk_condition='', med_risk_condition='')
        assert result[0], result[1]  # Assert that the profile update was successful

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update policy
        # This step updates the policy to reflect the changes made to the ZIA posture profile
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs via airdrop
        # This step exports the logs via airdrop to the specified hostname
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Validate trust level from logs
        # This step validates the trust level from the logs to ensure it matches the expected value
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(2)
        assert result[0], result[1]  # Assert that the trust level validation was successful


    @pytest.mark.xray("QA-301639")
    @allure.title("ZCC_login_with_ZIA_posture_only_Medium_Risk_type")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zcc_login_with_zia_posture_only_medium_risk_type(self):
        """
        Test case to verify ZCC login with ZIA posture only Medium Risk type.
    
        This test case edits the ZIA posture profile to set medium risk condition, 
        updates the policy, exports logs, and validates the trust level from logs.
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
        
        # Edit the ZIA posture profile to set medium risk condition
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(profile_name="ZIAPosture", med_risk_condition=self.um_medium)
        # Assert that the profile edit was successful
        assert result[0], result[1]
    
        # Edit the ZIA posture profile to clear high and low risk conditions
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(profile_name="ZIAPosture", high_risk_condition='', low_risk_condition='')
        # Assert that the profile edit was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the log export was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        # Assert that the trust level validation was successful
        assert result[0], result[1]

    @pytest.mark.xray("QA-301638")
    @allure.title("ZCC_login_with_ZIA_posture_only_High_Risk_type")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zcc_login_with_zia_posture_only_high_risk_type(self):
        """
        Test case to verify ZCC login with ZIA posture only High Risk type.
    
        This test case edits the ZIA posture profile to set high risk condition, 
        updates the policy, exports logs, and validates the trust level from logs.
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Raises:
            AssertionError: If any of the assertions fail.
    
        Returns:
            None
        """
    
        # Edit the ZIA posture profile to set high risk condition
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(profile_name="ZIAPosture", high_risk_condition=self.um_high)
        # Assert that the edit operation was successful
        assert result[0], result[1]
    
        # Edit the ZIA posture profile to clear low and medium risk conditions
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(profile_name="ZIAPosture", low_risk_condition='', med_risk_condition='')
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the log export was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        # Assert that the trust level validation was successful
        assert result[0], result[1]
        

    @pytest.mark.xray("QA-301629")
    @allure.title("ZCC_login_with_ZIA_posture_Tunnel1.0")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zcc_login_with_zia_posture_tunnel1(self):
        """
        Test case to verify ZCC login with ZIA posture and Tunnel 1.0.
    
        This test case edits the forwarding profile to use Tunnel 1.0, updates the ZIA posture profile,
        updates the policy, exports logs, and validates the trust level from logs.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the forwarding profile to use Tunnel 1.0
        result = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode='tunnel_1')
        assert result[0], result[1]  # Assert that the editing was successful
    
        # Update the ZIA posture profile
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(profile_name="ZIAPosture", high_risk_condition=self.um_high)
        assert result[0], result[1]  # Assert that the updating was successful

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the updating was successful
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the exporting was successful
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        assert result[0], result[1]  # Assert that the trust level is valid

    @pytest.mark.xray("QA-301628")
    @allure.title("ZCC_login_with_ZIA_posture_TWLP")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zcc_login_with_zia_posture_twlp(self):
        """
        Test case to verify ZCC login with ZIA posture TWLP.
    
        This test case edits the forwarding profile to TWLP, updates the ZIA posture profile, 
        updates the policy, exports logs, and validates the trust level from logs.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the forwarding profile to TWLP
        result = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode='twlp')
        # Assert that the forwarding profile edit was successful
        assert result[0], result[1]
    
        # Edit the ZIA posture profile with high risk condition
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(profile_name="ZIAPosture", high_risk_condition=self.um_high)
        # Assert that the ZIA posture profile edit was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
        
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the log export was successful
        assert result[0], result[1]
        
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        # Assert that the trust level validation was successful
        assert result[0], result[1]

        
    @pytest.mark.xray("QA-301642")
    @allure.title("ZCC_login_ZIA_only_with_ZIA_posture_Low_Medium_Risk_types")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_zcc_login_zia_only_with_zia_posture_Low_Medium_Risk_types(self):
        """
        Test case to verify ZCC login with ZIA only and ZIA posture Low Medium Risk types.
    
        This test case covers the following scenarios:
        1. Edit ZIA posture profile with Low and Medium risk conditions.
        2. Disable ZPA.
        3. Logout from ZCC.
        4. Login to ZCC with ZIA user credentials.
        5. Export logs via airdrop.
        6. Validate trust level from logs.
        7. Validate if URL is accessible.
    
        Args:
            self: Instance of the class.
    
        Returns:
            None
        """
    
        # Edit ZIA posture profile with Low and Medium risk conditions
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(profile_name="ZIAPosture", high_risk_condition='', low_risk_condition=self.um_low, med_risk_condition=self.um_medium)
        assert result[0], result[1]  # Assert that the edit operation is successful
    
        # Disable ZPA
        result = conftest.ma_service_entitlement.toggle_zpa(action=False)
        assert result[0], result[1]  # Assert that the disable operation is successful
    
        # Logout from ZCC
        result = conftest.zcc.logout(sleep_time=30)
        assert result[0], result[1]  # Assert that the logout operation is successful
    
        # Login to ZCC with ZIA user credentials
        result = conftest.zcc.login(
            aup=False,
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zia_saml_login=True
        )
        assert result[0], result[1]  # Assert that the login operation is successful

        result = conftest.zcc.validate_zcc_logged_in(check_alerts_after_login=True)
        assert result[0], result[1]
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the export operation is successful
    
        # Validate trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        assert result[0], result[1]  # Assert that the trust level is valid
    
        # Validate if URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        assert result[0], result[1]  # Assert that the URL is accessible

    @pytest.mark.xray("QA-301636")
    @allure.title("Modify_ZIA_Posture_from_P1_to_P2")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_modify_zia_posture_from_p1_to_p2(self):
        """
        Test case to modify ZIA posture from P1 to P2.
    
        This test case modifies the ZIA posture from P1 to P2 by editing the ZIA posture profile,
        updating the app profile, and then validating the trust level from logs.
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile to set the risk conditions to empty strings
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='', 
            med_risk_condition='', 
            low_risk_condition=self.um_low, 
            profile_name="ZIAPosture"
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]
    
        # Edit the app profile to use the modified ZIA posture profile
        result = conftest.ma_app_profile.edit_app_profile(zia_posture="ZIAPosture")
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop to the specified MAC hostname
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the export operation was successful
        assert result[0], result[1]
        
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(2)
        # Assert that the validation was successful
        assert result[0], result[1]
        
        # Create a new ZIA posture profile with the specified high risk condition
        result = conftest.zcc_zia_posture.zia_posture.create_zia_posture_profile(
            high_risk_condition=self.um_high, 
            profile_name="ZIAPosture1"
        )
        # Assert that the create operation was successful
        assert result[0], result[1]
        
        # Edit the app profile to use the new ZIA posture profile
        result = conftest.ma_app_profile.edit_app_profile(zia_posture="ZIAPosture1")
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
        
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop to the specified MAC hostname
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the export operation was successful
        assert result[0], result[1]
        
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        # Assert that the validation was successful
        assert result[0], result[1]


    @allure.title("Teardown")
    def teardown_class(self):
        """
        Teardown class to clean up resources after test execution.
    
        This function deletes various policies, profiles, and postures created during test execution.
        It ensures a clean environment for subsequent test runs.
    
        Args:
            self: Instance of the class.
    
        Returns:
            None
        """
        # Delete URL policies created during test execution
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Allow", policy_rule="allow")
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Block", policy_rule="block")
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Allow_All", policy_rule="allow")
    
        # Delete app profile on Mobile Admin
        conftest.ma_app_profile.delete_app_profile(operating_system="Mac")
    
        # Delete forwarding profile on Mobile Admin
        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()
    
        # Delete ZIA posture profiles
        conftest.zcc_zia_posture.zia_posture.delete_zia_posture_profile(profile_name="ZIAPosture")
        conftest.zcc_zia_posture.zia_posture.delete_zia_posture_profile(profile_name="ZIAPosture1")
    
        # Delete device postures
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.um_high)
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.um_medium)
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.um_low)
    
        # Logout from the application
        conftest.zcc.logout()
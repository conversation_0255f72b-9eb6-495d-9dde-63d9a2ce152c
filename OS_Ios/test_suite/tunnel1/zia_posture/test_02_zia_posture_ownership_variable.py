import allure
import subprocess
import pytest
from common_lib.Custom_Markers import add_markers,zcc_mark_version #Required!
# from OS_Ios.test_suite.zia.zia_posture import conftest
from OS_Ios.test_suite.tunnel1.zia_posture import conftest
import time

class TestZiaPostureOwnershipVariable:
    """
    Test cases for evaluating ZIA postures with Ownership Variable in Tunnel 1.0.

    Attributes:
        ownership_variable (str): Value of the ownership variable.
        device_posture_name (str): Name of the ownership variable device posture.
        mismatch_device_posture_name (str): Name of the mismatch ownership variable device posture.

    Methods:
        setup_class: Sets up the test environment.
        teardown_class: Tears down the test environment.
        test_*: Test cases for various Ownership Variable scenarios.
    """
    ownership_variable = "Ownership123"
    device_posture_name = "ownership_variable_posture"
    mismatch_device_posture_name = "mismatch_ownershipvariable_posture"
    @allure.title("Setup class")
    def setup_class(self):
        """
        1. Delete URL policies, app profle, forwarding profile, zia posture profile, device postures
        2. Create forwarding profile, app profile, zpa access policy, device postures, create zia posture profile, assign zia posture profile to app profile, edit URL Filtering policies
        3. Login to zcc
        """
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Allow", policy_rule="allow")
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Block", policy_rule="block")
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Allow_All", policy_rule="allow")
        conftest.ma_app_profile.delete_app_profile()
        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()
        # Toggle ZIA on
        result = conftest.ma_service_entitlement.toggle_zia(action=True)
        assert result[0], result[1]  # Check if the toggle was successful

        # Toggle ZPA on
        result = conftest.ma_service_entitlement.toggle_zpa(action=True)
        assert result[0], result[1]  # Check if the toggle was successful

        # Toggle ZDX off
        result = conftest.ma_service_entitlement.toggle_zdx(action=False)
        assert result[0], result[1]  # Check if the toggle was successful
        conftest.zcc_zia_posture.zia_posture.delete_zia_posture_profile(profile_name="ZIAPosture")
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.device_posture_name)
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.mismatch_device_posture_name)
        
        result = conftest.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert result[0], result[1]

        result = conftest.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        conftest.zpa_policy.delete_policy(policy_type="AccessPolicy")

        result = conftest.zpa_policy.create_access_policy(zpa_user_name=conftest.config["ZPA_ADMIN_ID"], zpa_password=conftest.config["ZPA_ADMIN_PASSWORD"])
        assert result[0], result[1]
        
        result = conftest.zcc_zia_posture.device_posture.create_ownership_posture(posture_name=self.device_posture_name, value=self.ownership_variable)
        assert result[0], result[1]
        
        result = conftest.zcc_zia_posture.device_posture.create_ownership_posture(posture_name=self.mismatch_device_posture_name, value=self.ownership_variable+"4")
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.zia_posture.create_zia_posture_profile(high_risk_condition=self.device_posture_name,med_risk_condition=self.mismatch_device_posture_name, profile_name="ZIAPosture")
        assert result[0], result[1]

        # result = conftest.ma_app_profile.edit_app_profile(zia_posture="ZIAPosture", use_tunnel_sdk_version_43=True)
        # assert result[0], result[1] 
        result = conftest.ma_app_profile.edit_app_profile(zia_posture="ZIAPosture")
        assert result[0], result[1]


        result = conftest.zcc_zia_posture.create_delete_url_policy(action="create", policy_name="Automation_URL_Allow", policy_rule="allow")
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.create_delete_url_policy(action="create", policy_name="Automation_URL_Block", policy_rule="block")
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.create_delete_url_policy(action="create", policy_name="Automation_URL_Allow_All", policy_rule="allow")
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.zia_url_policy.zia_helper.zia_activate()
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.zia_url_policy.edit_url_filter_policy(policy={'deviceTrustLevels':['HIGH_TRUST','MEDIUM_TRUST']},policy_name='Automation_URL_Allow')
        assert result[0], result[1]
        
        result = conftest.zcc_zia_posture.zia_url_policy.edit_url_filter_policy(policy={'deviceTrustLevels':['LOW_TRUST','UNKNOWN_DEVICETRUSTLEVEL']},policy_name='Automation_URL_Block')
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.zia_url_policy.zia_helper.zia_activate()
        assert result[0], result[1]

        # Configure AUP settings
        result=conftest.ma_notifications.configure_aup(aup_frequency="never")
        assert result[0], result[1]  # Check if the configuration was successful

        # Enable SAML on ZIA
        result=conftest.saml_zia_obj.saml_action_smui(mode='enable')
        assert result[0], result[1]  # Check if the enablement was successful

        # Change SAML IDP on ZIA
        result =conftest.saml_zia_obj.change_saml_on_zia(idp_name=conftest.config['IDP_NAME'])
        assert result[0],result[1]  # Check if the change was successful

        # Disable browser-based authentication on MA
        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]  # Check if the disablement was successful
    
        result = conftest.zcc.login(
            aup=False,
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zpa_user=conftest.config["ZPA_USER_ID"],
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],
            zia_saml_login=True
        )
        assert result[0], result[1]

        result = conftest.zcc.validate_zcc_logged_in()
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.zia_url_policy.edit_url_filter_policy(policy={'deviceTrustLevels':['HIGH_TRUST','MEDIUM_TRUST']},policy_name='Automation_URL_Allow')
        assert result[0], result[1]
        
        result = conftest.zcc_zia_posture.zia_url_policy.edit_url_filter_policy(policy={'deviceTrustLevels':['LOW_TRUST','UNKNOWN_DEVICETRUSTLEVEL']},policy_name='Automation_URL_Block')
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.zia_url_policy.zia_helper.zia_activate()
        assert result[0], result[1]

# OWNERSHIP VARIABLE DEVICE POSTURE

    @pytest.mark.xray(["QA-301601","QA-301762"])
    @allure.title("Evaluate ZIA postures - Ownership Variable as low trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_ownership_variable_as_low(self) -> None:
        """
        Test case to evaluate ZIA postures with ownership variable as low trust.
    
        This test case edits the ZIA posture profile to set the low risk condition, 
        updates the policy, exports logs, validates the trust level from logs, 
        and checks if a URL is accessible.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile to set the low risk condition
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='', 
            med_risk_condition='', 
            low_risk_condition=self.device_posture_name, 
            profile_name="ZIAPosture"
        )
        # Assert that the profile edit was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()

        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Export logs via airdrop to the specified MAC hostname
        result = conftest.zcc.export_logs(
            airdrop=True, 
            airdrop_to=conftest.config["MAC_HOSTNAME"]
        )
        # Assert that the log export was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(2)
        # Assert that the trust level validation was successful
        assert result[0], result[1]
    
        # Validate if a URL is accessible (expected to be inaccessible)
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the URL is inaccessible
        assert result[0], result[1]
    
        # Reset the ZIA posture profile to its original state
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='', 
            med_risk_condition='', 
            low_risk_condition='', 
            profile_name="ZIAPosture"
        )
        # Assert that the profile edit was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy again
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Export logs via airdrop to the specified MAC hostname again
        result = conftest.zcc.export_logs(
            airdrop=True, 
            airdrop_to=conftest.config["MAC_HOSTNAME"]
        )
        # Assert that the log export was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs again
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(1)
        # Assert that the trust level validation was successful
        assert result[0], result[1]
    
        # Validate if a URL is accessible (expected to be inaccessible) again
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the URL is inaccessible
        assert result[0], result[1]

    @pytest.mark.xray([["QA-301602","QA-301761"]])
    @allure.title("Evaluate ZIA postures - Ownership Variable as medium trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_ownership_variable_as_medium(self):
        """
        Test case to evaluate ZIA postures with ownership variable as medium trust.
    
        This test case edits the ZIA posture profile to set the medium risk condition,
        updates the policy, exports logs, validates the trust level from logs, and
        checks if a URL is accessible.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile to set the medium risk condition
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='', 
            med_risk_condition=self.device_posture_name, 
            low_risk_condition='', 
            profile_name="ZIAPosture"
        )
        # Assert that the profile edit was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(
            airdrop=True, 
            airdrop_to=conftest.config["MAC_HOSTNAME"]
        )
        # Assert that the log export was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        # Assert that the trust level is valid
        assert result[0], result[1]
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the URL is accessible
        assert result[0], result[1]
    
        # Reset the ZIA posture profile
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='', 
            med_risk_condition='', 
            low_risk_condition='', 
            profile_name="ZIAPosture"
        )
        # Assert that the profile edit was successful
        assert result[0], result[1]

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
    
        # Validate if a URL is not accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the URL is not accessible
        assert result[0], result[1]

    @pytest.mark.xray(["QA-301603","QA-301760"])
    @allure.title("Evaluate ZIA postures - Ownership Variable as high trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_ownership_variable_as_high(self):
        """
        Test case to evaluate ZIA postures with ownership variable as high trust.
    
        This test case edits the ZIA posture profile to set the high risk condition to the device posture name,
        updates the policy, exports logs, validates the trust level from logs, and checks if a URL is accessible.
        It then resets the ZIA posture profile, updates the policy again, exports logs, validates the trust level from logs,
        and checks if the URL is not accessible.
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Raises:
            AssertionError: If any of the assertions fail.
    
        Returns:
            None
        """
    
        # Edit the ZIA posture profile to set the high risk condition to the device posture name
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition=self.device_posture_name,med_risk_condition='',low_risk_condition='',profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the edit operation was successful

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs via airdrop to the specified MAC hostname
        result=conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        assert result[0], result[1]  # Assert that the trust level is as expected
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        assert result[0], result[1]  # Assert that the URL is accessible
    
        # Reset the ZIA posture profile
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='',med_risk_condition='',low_risk_condition='',profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the reset operation was successful

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
    
        # Validate if a URL is not accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        assert result[0], result[1]  # Assert that the URL is not accessible

    @pytest.mark.xray("QA-301757")
    @allure.title("Evaluate ZIA postures - False Ownership Variable as high trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_false_ownership_variable_as_high(self):
        """
        Test case to evaluate ZIA postures with False Ownership Variable as high trust.
    
        This test case edits the ZIA posture profile, updates the policy, exports logs, 
        validates the trust level from logs, and checks if a URL is accessible.
    
        Args:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
    
        Returns:
            None
        """
        
        # Edit the ZIA posture profile with high risk condition as mismatch device posture name
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition=self.mismatch_device_posture_name, 
            med_risk_condition='', 
            low_risk_condition='', 
            profile_name="ZIAPosture"
        )
        # Assert that the profile edit was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Export logs via airdrop to the specified MAC hostname
        result = conftest.zcc.export_logs(
            airdrop=True, 
            airdrop_to=conftest.config["MAC_HOSTNAME"]
        )
        # Assert that the log export was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(1)
        # Assert that the trust level validation was successful
        assert result[0], result[1]
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the URL accessibility validation was successful
        assert result[0], result[1]

    @pytest.mark.xray("QA-301758")
    @allure.title("Evaluate ZIA postures - False Ownership Variable as medium trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_false_ownership_variable_as_medium(self):
        """
        test_false_ownership_variable_as_medium:
        Evaluates ZIA postures with False Ownership Variable as medium trust.
    
        This test case edits the ZIA posture profile, updates the policy, exports logs, 
        validates the trust level from logs, and checks if a URL is accessible.
    
        Args:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
    
        Returns:
            None
        """
        # Edit the ZIA posture profile to set the medium risk condition to the mismatch device posture name
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='', 
            med_risk_condition=self.mismatch_device_posture_name, 
            low_risk_condition='', 
            profile_name="ZIAPosture"
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Export logs via airdrop to the specified MAC hostname
        result = conftest.zcc.export_logs(
            airdrop=True, 
            airdrop_to=conftest.config["MAC_HOSTNAME"]
        )
        # Assert that the log export was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(1)
        # Assert that the trust level validation was successful
        assert result[0], result[1]
    
        # Check if a URL is accessible (expected to be inaccessible)
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the URL is inaccessible
        assert result[0], result[1]

    @pytest.mark.xray("QA-301759")
    @allure.title("Evaluate ZIA postures - False Ownership Variable as low trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_false_ownership_variable_as_low(self):
        """
        Test case to evaluate ZIA postures with False Ownership Variable as low trust.
    
        This test case updates the ZIA posture profile with a low risk condition, 
        updates the policy, exports logs, validates the trust level from logs, 
        and checks if a URL is accessible.
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Update the ZIA posture profile with a low risk condition
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='', 
            med_risk_condition='', 
            low_risk_condition=self.mismatch_device_posture_name, 
            profile_name="ZIAPosture"
        )
        # Assert that the update was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the update was successful
        assert result[0], result[1]
    
        # Export logs via airdrop to the specified hostname
        result = conftest.zcc.export_logs(
            airdrop=True, 
            airdrop_to=conftest.config["MAC_HOSTNAME"]
        )
        # Assert that the export was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(1)
        # Assert that the trust level is valid
        assert result[0], result[1]
    
        # Check if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the URL is not accessible
        assert result[0], result[1]

    
    @pytest.mark.xray("QA-301612")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - True Ownership Variable as Medium Trust & False Ownership Variable as Low Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_ownership_variable_true_as_medium_and_false_as_low(self):
        """
        Test case to evaluate ZIA postures in Tunnel 1.0 with True Ownership Variable as Medium Trust and False Ownership Variable as Low Trust.
    
        This test case performs the following steps:
        1. Edits the ZIA posture profile to set the medium risk condition to the device posture name and the low risk condition to the mismatch device posture name.
        2. Updates the policy.
        3. Exports logs via airdrop to the specified MAC hostname.
        4. Validates the trust level from logs.
        5. Validates if a URL is accessible.
        6. Edits the ZIA posture profile to reset the medium risk condition.
        7. Updates the policy.
        8. Exports logs via airdrop to the specified MAC hostname.
        9. Validates the trust level from logs.
        10. Validates if a URL is not accessible.
    
        Args:
            self: The instance of the class.
    
        Returns:
            None
        """
    
        # Edit the ZIA posture profile to set the medium risk condition to the device posture name and the low risk condition to the mismatch device posture name
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='', 
            med_risk_condition=self.device_posture_name, 
            low_risk_condition=self.mismatch_device_posture_name, 
            profile_name="ZIAPosture"
        )
        
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
        
        # Update the policy
        result = conftest.zcc.update_policy()
        
        # Assert that the update operation was successful
        assert result[0], result[1]
        
        # Export logs via airdrop to the specified MAC hostname
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        
        # Assert that the export operation was successful
        assert result[0], result[1]
        
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        
        # Assert that the trust level is valid
        assert result[0], result[1]
        
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        
        # Assert that the URL is accessible
        assert result[0], result[1]
        
        # Edit the ZIA posture profile to reset the medium risk condition
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='', 
            med_risk_condition='', 
            profile_name="ZIAPosture"
        )
        
        # Assert that the edit operation was successful
        assert result[0], result[1]

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
        
        # Validate if a URL is not accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        
        # Assert that the URL is not accessible
        assert result[0], result[1]

    @pytest.mark.xray("QA-301613")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - False Ownership Variable as Medium Trust & True Ownership Variable as Low Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_ownership_variable_false_as_medium_and_true_as_low(self):
        """
        Test case to evaluate ZIA postures in Tunnel 1.0 with False Ownership Variable as Medium Trust and True Ownership Variable as Low Trust.
    
        This test case edits the ZIA posture profile, updates the policy, exports logs, validates the trust level from logs, and checks if a URL is accessible.
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile with the specified risk conditions
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='',  # No high risk condition
            med_risk_condition=self.mismatch_device_posture_name,  # Medium risk condition
            low_risk_condition=self.device_posture_name,  # Low risk condition
            profile_name="ZIAPosture"  # Profile name
        )
        
        # Assert that the profile edit was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Export logs via airdrop to the specified hostname
        result = conftest.zcc.export_logs(
            airdrop=True,  # Export via airdrop
            airdrop_to=conftest.config["MAC_HOSTNAME"]  # Hostname to airdrop to
        )
        
        # Assert that the log export was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(2)
        
        # Assert that the trust level validation was successful
        assert result[0], result[1]
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        
        # Assert that the URL accessibility validation was successful
        assert result[0], result[1]


    @pytest.mark.xray("QA-301614")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - True Ownership Variable as High Trust & False Ownership Variable as Medium Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_ownership_variable_true_as_high_and_false_as_medium(self):
        """
        Test case to evaluate ZIA postures in Tunnel 1.0 with True Ownership Variable as High Trust and False Ownership Variable as Medium Trust.
    
        This test case performs the following steps:
        1. Edits the ZIA posture profile to set high risk condition to the device posture name and medium risk condition to the mismatch device posture name.
        2. Updates the policy.
        3. Exports logs via airdrop to the specified MAC hostname.
        4. Validates the trust level from logs.
        5. Validates if a URL is accessible.
        6. Edits the ZIA posture profile to reset the high and low risk conditions.
        7. Updates the policy again.
        8. Exports logs via airdrop to the specified MAC hostname again.
        9. Validates the trust level from logs again.
        10. Validates if a URL is not accessible.
    
        Args:
            self: The instance of the class.
    
        Returns:
            None
        """
    
        # Edit the ZIA posture profile to set high risk condition to the device posture name and medium risk condition to the mismatch device posture name
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition=self.device_posture_name, med_risk_condition=self.mismatch_device_posture_name, low_risk_condition='', profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the edit operation was successful

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs via airdrop to the specified MAC hostname
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        assert result[0], result[1]  # Assert that the trust level validation was successful
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        assert result[0], result[1]  # Assert that the URL is accessible
    
        # Edit the ZIA posture profile to reset the high and low risk conditions
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', low_risk_condition='', profile_name="ZIAPosture")
        assert result[0], result[1]  # Assert that the edit operation was successful

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
    
        # Validate if a URL is not accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        assert result[0], result[1]  # Assert that the URL is not accessible  # Assert that the URL is not accessible

    @pytest.mark.xray("QA-301615")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - False Ownership Variable as High Trust & True Ownership Variable as Medium Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_ownership_variable_false_as_high_and_true_as_medium(self):
        """
        Evaluates ZIA postures in Tunnel 1.0 with False Ownership Variable as High Trust and True Ownership Variable as Medium Trust.
    
        This test case edits the ZIA posture profile, updates the policy, exports logs, validates the trust level from logs, 
        and checks if a URL is accessible.
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile with high risk condition as mismatch device posture name, 
        # medium risk condition as device posture name, and low risk condition as empty string
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition=self.mismatch_device_posture_name, 
            med_risk_condition=self.device_posture_name, 
            low_risk_condition='', 
            profile_name="ZIAPosture"
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop to the specified MAC hostname
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the export operation was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs with trust level 3
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        # Assert that the validation was successful
        assert result[0], result[1]
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the URL is accessible
        assert result[0], result[1]

    
    @pytest.mark.xray("QA-301633")
    @allure.title("Modify_Posture_Low_to_Medium")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_modify_posture_low_to_medium(self):
        """
        Test case to modify the posture from low to medium.
    
        This test case performs the following steps:
        1. Edits the ZIA posture profile to set the low risk condition.
        2. Updates the policy.
        3. Exports the logs via airdrop.
        4. Validates the trust level from logs.
        5. Validates if the URL is accessible.
        6. Edits the ZIA posture profile to set the medium risk condition.
        7. Updates the policy.
        8. Exports the logs via airdrop.
        9. Validates the trust level from logs.
        10. Validates if the URL is accessible.
    
        Args:
            self: The instance of the class.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile to set the low risk condition
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='', 
            med_risk_condition='', 
            low_risk_condition=self.device_posture_name, 
            profile_name="ZIAPosture"
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export the logs via airdrop
        result = conftest.zcc.export_logs(
            airdrop=True, 
            airdrop_to=conftest.config["MAC_HOSTNAME"]
        )
        # Assert that the export operation was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(2)
        # Assert that the trust level is correct
        assert result[0], result[1]
    
        # Validate if the URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the URL is not accessible
        assert result[0], result[1]
    
        # Edit the ZIA posture profile to set the medium risk condition
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='', 
            med_risk_condition=self.device_posture_name, 
            low_risk_condition='', 
            profile_name="ZIAPosture"
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
    
        # Validate if the URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the URL is accessible
        assert result[0], result[1]

    @pytest.mark.xray("QA-301637")
    @allure.title("URL_Policy_with_Low_Trust_Device")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_url_policy_with_low_trust_device(self):
        """
        Test URL policy with low trust device.
    
        This test case validates the URL policy with a low trust device.
        It edits the ZIA posture profile, updates the policy, exports logs, 
        validates the trust level from logs, checks URL accessibility, 
        edits URL filter policies, activates ZIA, and finally checks URL accessibility again.
    
        Args:
            self: The instance of the class, providing access to class properties and methods.
    
        Raises:
            AssertionError: If any of the assertions fail.
    
        Returns:
            None
        """
    
        # Edit ZIA posture profile to set low risk condition
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition='', 
            med_risk_condition='', 
            low_risk_condition=self.device_posture_name, 
            profile_name="ZIAPosture"
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
        
        # Update policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(
            airdrop=True, 
            airdrop_to=conftest.config["MAC_HOSTNAME"]
        )
        # Assert that the export operation was successful
        assert result[0], result[1]
        
        # Validate trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(2)
        # Assert that the trust level is valid
        assert result[0], result[1]
        
        # Validate if URL is not accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the URL is not accessible
        assert result[0], result[1]
        
        # Edit URL filter policy to allow low trust devices
        result = conftest.zcc_zia_posture.zia_url_policy.edit_url_filter_policy(
            policy={'deviceTrustLevels':['HIGH_TRUST','LOW_TRUST']}, 
            policy_name='Automation_URL_Allow'
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]
        
        # Edit URL filter policy to block medium and unknown trust devices
        result = conftest.zcc_zia_posture.zia_url_policy.edit_url_filter_policy(
            policy={'deviceTrustLevels':['MEDIUM_TRUST','UNKNOWN_DEVICETRUSTLEVEL']}, 
            policy_name='Automation_URL_Block'
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]
        
        # Activate ZIA
        result = conftest.zcc_zia_posture.zia_url_policy.zia_helper.zia_activate()
        # Assert that the activation was successful
        assert result[0], result[1]
        
        # Validate if URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the URL is accessible
        assert result[0], result[1]

    @pytest.mark.xray("QA-301632")
    @allure.title("Modify_Posture_in_ZIA_Posture_Profile_Policy_CompanyConfig")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_modify_posture_in_zia_posture_profile_policy_company_config(self):
        """
        Test case to modify posture in ZIA posture profile policy company config.
    
        This test case modifies the posture in ZIA posture profile policy company config, 
        updates the policy, exports logs, validates the trust level from logs, 
        edits the ownership posture, edits the app profile, updates the policy again, 
        validates the policy download type from tray manager logs, and exports logs again.
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Raises:
            AssertionError: If any of the assertions fail.
    
        Returns:
            None
        """
    
        # Edit the ZIA posture profile with the provided risk conditions
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(high_risk_condition='', med_risk_condition=self.device_posture_name, low_risk_condition='', profile_name="ZIAPosture")
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Export logs via airdrop to the specified hostname
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the log export was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        # Assert that the trust level validation was successful
        assert result[0], result[1]
    
        # Edit the ownership posture
        result = conftest.zcc_zia_posture.device_posture.edit_ownership_posture(posture_name=self.device_posture_name, value=self.ownership_variable + "4")
        # Assert that the edit operation was successful
        assert result[0], result[1]
    
        # Edit the app profile
        result = conftest.ma_app_profile.edit_app_profile(zpa_disable_password="disable")
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy again
        result = conftest.zcc.update_policy()
        # Assert that the policy update was successful
        assert result[0], result[1]
    
        # Validate the policy download type from tray manager logs
        result = conftest.zcc_zia_posture.validate_policy_download_type_tray_manager_logs(4)
        # Assert that the validation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop to the specified hostname again
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the log export was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs again
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(1)
        # Assert that the trust level validation was successful
        assert result[0], result[1]



    @allure.title("Teardown class")
    def teardown_class(self):
        """
        Teardown class to clean up resources after test execution.
    
        This method is responsible for deleting various policies, profiles, and postures created during test execution.
        It ensures that the test environment is restored to its original state after the tests are completed.
    
        Args:
            None
    
        Returns:
            None
    
        Raises:
            None
        """
        # Delete URL policies created during test execution
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Allow", policy_rule="allow")  # Delete Automation_URL_Allow policy
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Block", policy_rule="block")  # Delete Automation_URL_Block policy
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Allow_All", policy_rule="allow")  # Delete Automation_URL_Allow_All policy
    
        # Delete app profiles created during test execution
        conftest.ma_app_profile.delete_app_profile()  # Delete app profile
    
        # Delete forwarding profiles created during test execution
        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()  # Delete forwarding profile
    
        # Delete ZIA posture profiles created during test execution
        conftest.zcc_zia_posture.zia_posture.delete_zia_posture_profile(profile_name="ZIAPosture")  # Delete ZIAPosture profile
    
        # Delete device postures created during test execution
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.device_posture_name)  # Delete device posture
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name=self.mismatch_device_posture_name)  # Delete mismatch device posture
    
        # Logout from the application
        conftest.zcc.logout()  # Logout from the application
        

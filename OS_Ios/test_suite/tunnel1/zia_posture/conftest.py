from common_lib.adminzpa import accesszpaapps,zpapolicy
from common_lib.adminzia.samlzia import SamlZia
from common_lib.mobileadmin import appprofile,serviceentitlement
from common_lib.mobileadmin import clientconnectornotifications,clientconnectorsupport,platformsettings
from OS_Ios.library.ui_zcc import Zcc
import os,json
from common_lib.common.logger import Logger
from OS_Ios.module.zcc_zia_posture import ZccZiaPosture
import pytest
try:
    config_file = os.path.join(os.getcwd(), "config", os.environ["CONFIG_FILE"])
    config = {}
    with open(config_file) as json_file:
        config.update(json.load(json_file))

    cloud_name = config["CLOUD"]
    log = Logger.initialize_logger(log_file_name="zia posture ios",log_level="INFO")
    zcc_zia_posture = ZccZiaPosture(config["CLOUD"], config_file)
    zcc = zcc_zia_posture.zcc
    zpa_app_access = accesszpaapps.AccessZpaApps(cloud_name, config_file , log_handle=log)
    zpa_policy = zpapolicy.ZpaPolicy(cloud_name, config_file, log_handle=log)
    ma_app_profile = appprofile.AppProfile(cloud_name, config_file, log_handle=log)
    ma_service_entitlement = serviceentitlement.ServiceEntitlement(cloud_name, config_file, log_handle=log)
    ma_notifications = clientconnectornotifications.ClientConnectorNotifications(cloud_name, config_file, log_handle=log)
    ma_support = clientconnectorsupport.ClientConnectorSupport(cloud_name, config_file, log_handle=log)
    saml_zia_obj = SamlZia(cloud=cloud_name, config=config_file, log_handle=log, debug=True)
    platform_settings_obj = platformsettings.PlatformSettings(cloud=cloud_name, config=config_file, log_handle=log)
except Exception as e:
    pytest.skip(f"Skipping as Error :: {e}")
import allure
import subprocess
import pytest
from common_lib.Custom_Markers import add_markers,zcc_mark_version #Required!
# from OS_Ios.test_suite.zia.zia_posture import conftest
from OS_Ios.test_suite.tunnel1.zia_posture import conftest
import time

class TestZiaPostureCertificateTrust:
    """
    Test cases for evaluating ZIA postures with Certificate Trust in Tunnel 1.0.

    Methods:
        setup_class: Sets up the test environment.
        teardown_class: Tears down the test environment.
        test_*: Test cases for various Certificate Trust scenarios.
    """
    @allure.title("Setup class")
    def setup_class(self):
        """
        1. Delete URL policies, app profle, forwarding profile, zia posture profile, device postures
        2. Create forwarding profile, app profile, zpa access policy, device postures, create zia posture profile, assign zia posture profile to app profile, edit URL Filtering policies
        3. Login to zcc
        """
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Allow", policy_rule="allow")
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Block", policy_rule="block")
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Allow_All", policy_rule="allow")
        conftest.ma_app_profile.delete_app_profile()
        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()
        # Toggle ZIA on
        result = conftest.ma_service_entitlement.toggle_zia(action=True)
        assert result[0], result[1]  # Check if the toggle was successful

        # Toggle ZPA on
        result = conftest.ma_service_entitlement.toggle_zpa(action=True)
        assert result[0], result[1]  # Check if the toggle was successful

        # Toggle ZDX off
        result = conftest.ma_service_entitlement.toggle_zdx(action=False)
        assert result[0], result[1]  # Check if the toggle was successful
        
        conftest.zcc_zia_posture.zia_posture.delete_zia_posture_profile(profile_name="ZIAPosture")
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name="certificate_trust_verified")
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name="certificate_trust_non_verified")
        
        result = conftest.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert result[0], result[1]

        result = conftest.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        conftest.zpa_policy.delete_policy(policy_type="AccessPolicy")

        result = conftest.zpa_policy.create_access_policy(zpa_user_name=conftest.config["ZPA_ADMIN_ID"], zpa_password=conftest.config["ZPA_ADMIN_PASSWORD"])
        assert result[0], result[1]
        
        result = conftest.zcc_zia_posture.device_posture.create_certificate_trust_dp(posture_name="certificate_trust_verified", cert_to_be_uploaded="Cert_Trust_Verified")
        assert result[0], result[1]
        
        result = conftest.zcc_zia_posture.device_posture.create_certificate_trust_dp(posture_name="certificate_trust_non_verified", cert_to_be_uploaded="Cert_Trust_Non_Verified")
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.zia_posture.create_zia_posture_profile(high_risk_condition="certificate_trust_verified",med_risk_condition="certificate_trust_non_verified", profile_name="ZIAPosture")
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.create_delete_url_policy(action="create", policy_name="Automation_URL_Allow", policy_rule="allow")
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.create_delete_url_policy(action="create", policy_name="Automation_URL_Block", policy_rule="block")
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.create_delete_url_policy(action="create", policy_name="Automation_URL_Allow_All", policy_rule="allow")
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.zia_url_policy.zia_helper.zia_activate()
        assert result[0], result[1]

        # result = conftest.ma_app_profile.edit_app_profile(zia_posture="ZIAPosture", use_tunnel_sdk_version_43=True)
        # assert result[0], result[1]

        result = conftest.ma_app_profile.edit_app_profile(zia_posture="ZIAPosture")
        assert result[0], result[1]

        # Configure AUP settings
        result=conftest.ma_notifications.configure_aup(aup_frequency="never")
        assert result[0], result[1]  # Check if the configuration was successful

        # Enable SAML on ZIA
        result=conftest.saml_zia_obj.saml_action_smui(mode='enable')
        assert result[0], result[1]  # Check if the enablement was successful

        # Change SAML IDP on ZIA
        result =conftest.saml_zia_obj.change_saml_on_zia(idp_name=conftest.config['IDP_NAME'])
        assert result[0],result[1]  # Check if the change was successful

        # Disable browser-based authentication on MA
        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]  # Check if the disablement was successful        
    
        result = conftest.zcc.login(
            aup=False,
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zpa_user=conftest.config["ZPA_USER_ID"],
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],
            zia_saml_login=True
        )
        assert result[0], result[1]

        result = conftest.zcc.validate_zcc_logged_in()
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.zia_url_policy.edit_url_filter_policy(policy={'deviceTrustLevels':['HIGH_TRUST','MEDIUM_TRUST']},policy_name='Automation_URL_Allow')
        assert result[0], result[1]
        
        result = conftest.zcc_zia_posture.zia_url_policy.edit_url_filter_policy(policy={'deviceTrustLevels':['LOW_TRUST','UNKNOWN_DEVICETRUSTLEVEL']},policy_name='Automation_URL_Block')
        assert result[0], result[1]

        result = conftest.zcc_zia_posture.zia_url_policy.zia_helper.zia_activate()
        assert result[0], result[1]

# CERTIFICATE TRUST DEVICE POSTURE

    @pytest.mark.xray("QA-301609")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - Certificate Trust Verified as High Trust & Certificate Trust Non Verified as Medium Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_zia_posture_high_medium_verified_high(self):
        """
        Test case to evaluate ZIA postures in Tunnel 1.0 with Certificate Trust Verified as High Trust and Certificate Trust Non Verified as Medium Trust.
    
        This test case performs the following steps:
        1. Edits the ZIA posture profile to set Certificate Trust Verified as High Trust and Certificate Trust Non Verified as Medium Trust.
        2. Updates the policy.
        3. Exports logs via airdrop.
        4. Validates the trust level from logs.
        5. Validates if the URL is accessible.
        6. Repeats steps 1-5 with Certificate Trust Non Verified as Medium Trust.
    
        Args:
            self: The instance of the class.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile to set Certificate Trust Verified as High Trust and Certificate Trust Non Verified as Medium Trust
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition="certificate_trust_verified",  # Set Certificate Trust Verified as High Trust
            med_risk_condition="certificate_trust_non_verified",  # Set Certificate Trust Non Verified as Medium Trust
            low_risk_condition="",  # No Low Risk Condition
            profile_name="ZIAPosture",  # Profile name
        )
        assert result[0], result[1]  # Assert the result

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert the result
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert the result
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        assert result[0], result[1]  # Assert the result
    
        # Validate if the URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        assert result[0], result[1]  # Assert the result
    
        # Repeat the process with Certificate Trust Non Verified as Medium Trust
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition="",  # No High Risk Condition
            med_risk_condition="certificate_trust_non_verified",  # Set Certificate Trust Non Verified as Medium Trust
            low_risk_condition="",  # No Low Risk Condition
            profile_name="ZIAPosture",  # Profile name
        )
        assert result[0], result[1]  # Assert the result

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert the result
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert the result
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        assert result[0], result[1]  # Assert the result
    
        # Validate if the URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        assert result[0], result[1]  # Assert the result        

    @pytest.mark.xray("QA-301608")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - Certificate Trust Non Verified as High Trust & Certificate Trust Verified as Medium Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_zia_posture_high_medium_non_verified_high(self) -> None:
        """
        This test case evaluates ZIA postures in Tunnel 1.0 with Certificate Trust Non Verified as High Trust 
        and Certificate Trust Verified as Medium Trust.
    
        It performs the following steps:
        1. Edits the ZIA posture profile to set high risk condition to certificate trust non verified and medium risk condition to certificate trust verified.
        2. Updates the policy.
        3. Exports logs via airdrop.
        4. Validates the trust level from logs.
        5. Validates if a URL is accessible.
        6. Repeats steps 1-5 with high risk condition set to empty string.
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit ZIA posture profile to set high risk condition to certificate trust non verified and medium risk condition to certificate trust verified
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition="certificate_trust_non_verified",
            med_risk_condition="certificate_trust_verified",
            low_risk_condition="",
            profile_name="ZIAPosture",
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the export operation was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        # Assert that the trust level is valid
        assert result[0], result[1]
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the URL is accessible
        assert result[0], result[1]
    
        # Edit ZIA posture profile to set high risk condition to empty string and medium risk condition to certificate trust verified
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition="",
            med_risk_condition="certificate_trust_verified",
            low_risk_condition="",
            profile_name="ZIAPosture",
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the export operation was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        # Assert that the trust level is valid
        assert result[0], result[1]
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the URL is accessible
        assert result[0], result[1]  
    
    @pytest.mark.xray("QA-301607")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - Certificate Trust Verified as Medium Trust & Certificate Trust Non Verified as Low Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_zia_posture_medium_low_verified_medium(self):
        """
        This test case evaluates ZIA postures in Tunnel 1.0 by setting Certificate Trust Verified as Medium Trust 
        and Certificate Trust Non Verified as Low Trust.
    
        It performs the following steps:
        1. Edits the ZIA posture profile to set the medium and low risk conditions.
        2. Updates the policy.
        3. Exports logs via airdrop.
        4. Validates the trust level from logs.
        5. Validates if a URL is accessible.
        6. Repeats steps 1-5 with different risk conditions.
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile to set the medium risk condition as "certificate_trust_verified" 
        # and the low risk condition as "certificate_trust_non_verified".
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition="",  # No high risk condition is set.
            med_risk_condition="certificate_trust_verified",  # Medium risk condition is set to "certificate_trust_verified".
            low_risk_condition="certificate_trust_non_verified",  # Low risk condition is set to "certificate_trust_non_verified".
            profile_name="ZIAPosture",  # The name of the ZIA posture profile.
        )
        assert result[0], result[1]  # Assert that the operation was successful.

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy.
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the operation was successful.
    
        # Export logs via airdrop to the specified MAC hostname.
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the operation was successful.
    
        # Validate the trust level from logs.
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        assert result[0], result[1]  # Assert that the operation was successful.
    
        # Validate if a URL is accessible.
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        assert result[0], result[1]  # Assert that the operation was successful.
    
        # Repeat the above steps with different risk conditions.
        # Edit the ZIA posture profile to set the low risk condition as "certificate_trust_non_verified".
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition="",  # No high risk condition is set.
            med_risk_condition="",  # No medium risk condition is set.
            low_risk_condition="certificate_trust_non_verified",  # Low risk condition is set to "certificate_trust_non_verified".
            profile_name="ZIAPosture",  # The name of the ZIA posture profile.
        )
        assert result[0], result[1]  # Assert that the operation was successful.

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
    
        # Validate if a URL is not accessible.
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        assert result[0], result[1]  # Assert that the operation was successful.        

    @pytest.mark.xray("QA-301606")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - Certificate Trust Non Verified as Medium Trust & Certificate Trust Verified as Low Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_zia_posture_medium_low_non_verified_medium(self):
        """
        Test case to evaluate ZIA postures in Tunnel 1.0.
    
        This test case evaluates the ZIA postures in Tunnel 1.0 by setting the medium risk condition to 
        "certificate_trust_non_verified" and the low risk condition to "certificate_trust_verified". It then 
        updates the policy, exports the logs, and validates the trust level from the logs.
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile to set the medium risk condition to "certificate_trust_non_verified" 
        # and the low risk condition to "certificate_trust_verified"
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition="",
            med_risk_condition="certificate_trust_non_verified",
            low_risk_condition="certificate_trust_verified",
            profile_name="ZIAPosture",
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export the logs via airdrop to the specified hostname
        result=conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        # Assert that the export operation was successful
        assert result[0], result[1]
    
        # Validate the trust level from the logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(3)
        # Assert that the trust level is valid
        assert result[0], result[1]
    
        # Validate if the URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        # Assert that the URL is accessible
        assert result[0], result[1]
    
        # Edit the ZIA posture profile to set the low risk condition to "certificate_trust_verified"
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition="",
            med_risk_condition="",
            low_risk_condition="certificate_trust_verified",
            profile_name="ZIAPosture",
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
    
        # Validate if the URL is not accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the URL is not accessible
        assert result[0], result[1]      

    @pytest.mark.xray(["QA-301605","QA-301618"])
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - Certificate Trust Verified as Low Trust to unknown")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_zia_posture_low_unknown_verified_low(self):
        """
        Test case to evaluate ZIA postures in Tunnel 1.0 with Certificate Trust Verified as Low Trust.
    
        This test case edits the ZIA posture profile to set the low risk condition to "certificate_trust_verified",
        updates the policy, exports logs, validates the trust level from logs, and checks if a URL is accessible.
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile to set the low risk condition to "certificate_trust_verified"
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition="",  # No high risk condition
            med_risk_condition="",  # No medium risk condition
            low_risk_condition="certificate_trust_verified",  # Set low risk condition to certificate trust verified
            profile_name="ZIAPosture",  # Profile name
        )
        assert result[0], result[1]  # Assert that the edit operation was successful

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs via airdrop to the specified MAC hostname
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(2)
        assert result[0], result[1]  # Assert that the trust level validation was successful
    
        # Validate if a URL is accessible (expected to be inaccessible)
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        assert result[0], result[1]  # Assert that the URL is inaccessible
    
        # Reset the ZIA posture profile to its original state
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition="",  # No high risk condition
            med_risk_condition="",  # No medium risk condition
            low_risk_condition="",  # Reset low risk condition
            profile_name="ZIAPosture",  # Profile name
        )
        assert result[0], result[1]  # Assert that the edit operation was successful

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]  # Assert that the policy update was successful
    
        # Export logs via airdrop to the specified MAC hostname again
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]  # Assert that the log export was successful
    
        # Validate the trust level from logs again
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(1)
        assert result[0], result[1]  # Assert that the trust level validation was successful
    
        # Validate if a URL is accessible (expected to be inaccessible) again
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        assert result[0], result[1]  # Assert that the URL is inaccessible     

    @pytest.mark.xray("QA-301604")
    @allure.title("Evaluate ZIA postures in Tunnel 1.0 - Certificate Trust Non Verified as Low Trust")
    @add_markers("regression","sanity")
    @zcc_mark_version(3.7)
    def test_zia_posture_low_unknown_non_verified_low(self):
        """
        Test case to evaluate ZIA postures in Tunnel 1.0 with Certificate Trust Non Verified as Low Trust.
    
        This test case edits the ZIA posture profile to set the low risk condition to "certificate_trust_non_verified",
        updates the policy, exports logs, validates the trust level from logs, and checks if a URL is accessible.
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Returns:
            None
    
        Raises:
            AssertionError: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile to set the low risk condition to "certificate_trust_non_verified"
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition="",  # No high risk condition
            med_risk_condition="",  # No medium risk condition
            low_risk_condition="certificate_trust_non_verified",  # Set low risk condition to "certificate_trust_non_verified"
            profile_name="ZIAPosture",  # Profile name
        )
        # Assert that the edit operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop to the specified hostname
        result = conftest.zcc.export_logs(
            airdrop=True,  # Export logs via airdrop
            airdrop_to=conftest.config["MAC_HOSTNAME"]  # Hostname to airdrop logs to
        )
        # Assert that the export operation was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(2)
        # Assert that the trust level is valid
        assert result[0], result[1]
    
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the URL is not accessible
        assert result[0], result[1]
    
        # Reset the ZIA posture profile to its original state
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition="",  # No high risk condition
            med_risk_condition="",  # No medium risk condition
            low_risk_condition="",  # Reset low risk condition
            profile_name="ZIAPosture",  # Profile name
        )
        # Assert that the reset operation was successful
        assert result[0], result[1]

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
    
        # Update the policy again
        result = conftest.zcc.update_policy()
        # Assert that the update operation was successful
        assert result[0], result[1]
    
        # Export logs via airdrop to the specified hostname again
        result = conftest.zcc.export_logs(
            airdrop=True,  # Export logs via airdrop
            airdrop_to=conftest.config["MAC_HOSTNAME"]  # Hostname to airdrop logs to
        )
        # Assert that the export operation was successful
        assert result[0], result[1]
    
        # Validate the trust level from logs again
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(1)
        # Assert that the trust level is valid
        assert result[0], result[1]
    
        # Validate if a URL is accessible again
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        # Assert that the URL is not accessible
        assert result[0], result[1]     

    @pytest.mark.xray("QA-301630")
    @allure.title("Trust_Level_Evaluation_on_Update_Policy")
    @add_markers("regression", "sanity")
    @zcc_mark_version(3.7)
    def test_trust_level_evaluation_on_update_policy(self):
        """
        Test case to evaluate trust level on update policy.
    
        This test case evaluates the trust level on update policy by editing the ZIA posture profile,
        updating the policy, exporting logs, validating the trust level from logs, and validating if a URL is accessible.
    
        Args:
            self: The instance of the class, which is used to access the class attributes and methods.
    
        Returns:
            None
    
        Raises:
            Exception: If any of the assertions fail.
        """
    
        # Edit the ZIA posture profile to set the high risk condition to "certificate_trust_non_verified"
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition="certificate_trust_non_verified",
            med_risk_condition="",
            low_risk_condition="",
            profile_name="ZIAPosture",
        )

        conftest.zcc_zia_posture.log_ops.ignore_logs_before_this_time_point()
        
        # Update the policy
        result = conftest.zcc.update_policy()
        
        # Assert that the policy update was successful
        assert result[0], result[1]
        
        # Export logs via airdrop to the specified MAC hostname
        result = conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        
        # Assert that the log export was successful
        assert result[0], result[1]
        
        # Validate the trust level from logs
        result = conftest.zcc_zia_posture.validate_trust_level_from_logs(4)
        
        # Assert that the trust level validation was successful
        assert result[0], result[1]
        
        # Validate if a URL is accessible
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=True)
        
        # Assert that the URL accessibility validation was successful
        assert result[0], result[1]
        
        # Edit the ZIA posture profile to reset the high risk condition
        result = conftest.zcc_zia_posture.zia_posture.edit_zia_posture_profile(
            high_risk_condition="",
            med_risk_condition="",
            low_risk_condition="certificate_trust_non_verified",
            profile_name="ZIAPosture",
        )
        
        # Assert that the ZIA posture profile edit was successful
        assert result[0], result[1]

        # Update the policy again
        result = conftest.zcc.update_policy()
        assert result[0], result[1]
        
        # Validate if a URL is accessible again
        result = conftest.zcc_zia_posture.validate_if_url_is_accessible(accessed=False)
        
        # Assert that the URL accessibility validation was successful
        assert result[0], result[1]

    @allure.title("Teardown class")
    def teardown_class(self):
        """
        Teardown class to perform cleanup operations after test execution.
    
        This method is responsible for deleting created policies, profiles, and postures, 
        as well as logging out of the system to ensure a clean state for future test runs.
    
        Args:
            self (object): Instance of the class.
    
        Returns:
            None
        """
        # Delete URL policies created during test execution
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Allow", policy_rule="allow")  # Delete 'Automation_URL_Allow' policy
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Block", policy_rule="block")  # Delete 'Automation_URL_Block' policy
        conftest.zcc_zia_posture.create_delete_url_policy(action="delete", policy_name="Automation_URL_Allow_All", policy_rule="allow")  # Delete 'Automation_URL_Allow_All' policy
    
        # Delete app profiles and forwarding profiles created during test execution
        conftest.ma_app_profile.delete_app_profile()  # Delete app profile
        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()  # Delete forwarding profile
    
        # Delete ZIA posture profiles created during test execution
        conftest.zcc_zia_posture.zia_posture.delete_zia_posture_profile(profile_name="ZIAPosture")  # Delete 'ZIAPosture' profile
    
        # Delete device postures created during test execution
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name="certificate_trust_verified")  # Delete 'certificate_trust_verified' posture
        conftest.zcc_zia_posture.device_posture.delete_device_posture(posture_name="certificate_trust_non_verified")  # Delete 'certificate_trust_non_verified' posture
    
        # Log out of the system to ensure a clean state for future test runs
        conftest.zcc.logout()
    
        
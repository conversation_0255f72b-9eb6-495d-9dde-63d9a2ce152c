import allure, pytest,time
from common_lib.Custom_Markers import add_markers,zcc_mark_version #Required!
# from OS_Ios.test_suite.p0_testcases.zia import conftest
from OS_Ios.test_suite.tunnel1 import conftest


"""
This class contains 8 testcases for ZIA application when used under Tunnel 1 mode on IPv4 network.
"""

class TestValidateZiaTunnel1_IPv4TestCases:

    @allure.title('Setup')
    def setup_class(self):
        """
        1. Fetching DNS server IP and application resolved IP by running and capturing packets when ZCC is not logged in.
        2. Deleting app profile and forwarding profile and creating a new app profile and forwarding profile.
        3. Editing the forwarding profile to use Tunnel 1.
        4. Editing the app profile to toggle tunnel sdk 4.3 .
        5. Configuring aup.
        6. Disabling browser based auth.
        """
        conftest.sys.delete_pcap_file(path=conftest.PKT_FILE_URL)

        conftest.zcc.bring_zcc_to_focus()
        if conftest.zcc.ui.check_if_exists(conftest.zcc.elements.LOGOUT_BUTTON, dynamic_wait_search_element=5):
            conftest.zcc.logout()

        result1 = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=conftest.PKT_FILE_URL, capture_duration=conftest.DURATION, browser=conftest.BROWSER_NAME, url=conftest.URLV4, ios_udid=conftest.IDENTIFIER)
        

        if result1[0]:
            result = conftest.sys.fetch_ios_dns_servers(pcap_file_url=conftest.PKT_FILE_URL)
            if not result[0]:
                conftest.logger.error(f"Critical setup error: {result[1]}")
                assert result[0], result[1]
            else:
                conftest.DNS_SERVER_IP = result[2]

            result = conftest.sys.fetch_application_resolved_ip(pcap_file_url=conftest.PKT_FILE_URL, domain=conftest.DOMAINV4_FILTER, application=conftest.DOMAINV4)

            if not result[0]:
                conftest.logger.error(f"Critical setup error: {result[1]}")
                assert result[0], result[1]
            else:
                conftest.DOMAIN_IPV4 = conftest.zia_sanity.concatenate_strings(application_ip=result[2])
        else:
            conftest.logger.error(f"Critical setup error: {result1[1]}")
            assert result1[0], result1[1]

        conftest.ma_app_profile.delete_app_profile()

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()

        result = conftest.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert result[0], result[1]

        result = conftest.ma_app_profile.edit_app_profile(use_tunnel_sdk_version_43=True)
        assert result[0], result[1]

        result=conftest.ma_notifications.configure_aup()
        assert result[0], result[1]

        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]
        
    @pytest.mark.xray("QA-342773") 
    @add_markers("regression","sanity") 
    @allure.title('Validate ZIA Login SAML based')
    @zcc_mark_version(4.3)
    def test_zia_saml_zpa_off_zdx_off(self):
        """
        1. Enable ZIA from MA.
        2. Disable ZPA from MA.
        3. Disable ZDX from MA.
        4. Enabling SAML for ZIA SAML based login.
        5. Login ZCC.
        6. Validate ZCC has been logged in or not.
        7. Logout ZCC.
        """

        result=conftest.ma_service_entitlement.toggle_zia(action=True)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zpa(action=False)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zdx(action=False)
        assert result[0], result[1]

        result=conftest.saml_zia_obj.saml_action_smui(mode="enable")
        assert result[0],result[1]

        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zia_saml_login=True,
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in(check_alerts_after_login=True, dynamic_wait_search_element=40)
        assert result[0], result[1]

        result = conftest.zcc.logout()
        assert result[0], result[1]

    @pytest.mark.xray("QA-342772") 
    @add_markers("regression","sanity") 
    @allure.title('Validate ZIA Login Form based')
    @zcc_mark_version(4.3)
    def test_zia_form_zpa_off_zdx_off(self):
        """
        1. Disabling SAML for ZIA form based login.
        2. Disable ZPA from MA.
        3. Login ZCC.
        4. Validate ZCC has been logged in or not.
        """

        result=conftest.saml_zia_obj.saml_action_smui(mode="disable")
        assert result[0],result[1]

        result = conftest.ma_service_entitlement.toggle_zpa(action=False)
        assert result[0], result[1]

        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in(check_alerts_after_login=True, dynamic_wait_search_element=40)
        assert result[0], result[1]


    @pytest.mark.xray("QA-342759")
    @allure.title('Validate Tun1.0 is the Forwarding mode/Tunnel version for ZIA')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.3)
    def test_validate_zia_tunnel_version(self):
        """
        1. Validated the ZIA tunnel version using a function validate_tunnel_version created in ui_zcc file
        """
        result = conftest.zcc.validate_tunnel_version(expected_version="v1.0")
        assert result[0], result[1]
      
        
    @pytest.mark.xray("QA-342760")
    @allure.title('Validate Tun SDK for ZIA is 4.3')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.3)
    def test_validate_zia_tunnel_sdk_version(self):
        """
        1. Validated the ZIA tunnel SDK version using a function validate_tunnel_version created in ui_zcc file 
        """
        result = conftest.zcc.validate_tunnel_version(expected_version="4.3", sdk=True)
        assert result[0], result[1]

    @pytest.mark.xray("QA-342761") 
    @add_markers("regression","sanity") 
    @allure.title('Validate ZIA Application Domain V4 based')
    @zcc_mark_version(4.3)
    def test_validate_zia_application_domain_v4_based(self):
        """
        1. Initializing multiple variables to be used.
        2. Validated zia application domain v4 based via analyze_packets function in zia_sanity file.
        """
        domainUrl, iOS_id, pkt_file_url, domainv4_filter = conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER

        result = conftest.zia_sanity.analyze_packets(pkt_file_url=pkt_file_url, ios_udid=iOS_id, urlv4=domainUrl, domainv4_filter=domainv4_filter)
        assert result[0], result[1]
    
    @pytest.mark.xray('QA-342762')
    @add_markers("regression","sanity")
    @allure.title("DomainV4 based Bypass via Hostname Bypass in Tunnel1")
    @zcc_mark_version(4.3)
    def test_bypass_fqdn_ipv4_based(self):
        '''
        1. Initializing multiple variables to be used.
        2. Edited the app profile to configure vpn bypass.
        3. Updated policy.
        2. Validated domainV4 based Bypass via Hostname Bypass in Tunnel1 for zia.
        '''

        domainUrl, iOS_id, pkt_file_url, domainv4_filter = conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER

        result = conftest.zia_sanity.ma_app_profile.edit_app_profile(vpn_bypass=conftest.DOMAINV4, use_tunnel_sdk_version_43=True)
        assert result[0], result[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zia_sanity.analyze_packets(pkt_file_url=pkt_file_url, ios_udid=iOS_id, urlv4=domainUrl, domainv4_filter=domainv4_filter, bypass=True)
        assert result[0], result[1]


    @pytest.mark.xray('QA-342763')
    @add_markers("regression", "sanity")
    @allure.title("DomainV4 based Bypass via Pac url in Tunnel1")
    @zcc_mark_version(4.3)
    def test_bypass_domainv4_via_pac_file(self):
        """
        1. Initializing multiple variables to be used.
        2. Edited the app profile to configure vpn bypass.
        3. Updated policy.
        4. Validated  1. Initializing multiple variables to be used.
        2. Validated zia application domain v4 based via analyze_packets function in zia_sanity file.
        """
        
        domainUrl, iOS_id, pkt_file_url, domainv4_filter = conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER

        res = conftest.zia_sanity.ma_app_profile.edit_app_profile(pac=conftest.PAC_URL, use_tunnel_sdk_version_43=True)
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zia_sanity.analyze_packets(pkt_file_url=pkt_file_url, ios_udid=iOS_id, urlv4=domainUrl, domainv4_filter=domainv4_filter, bypass=True)
        assert result[0], result[1]

    @pytest.mark.xray("QA-342764") 
    @add_markers("regression","sanity") 
    @allure.title('Verify DNS for ZIA app is system dns(v4)')
    @zcc_mark_version(4.3)
    def test_validate_dns_for_zia_is_system_dnsv4(self):
        """
        1. Initializing multiple variables to be used.
        2. Deleting pcap file if it exists to ensure we are working on latest pcapng file.
        3. Running packet capture process as well as to access an application via threading.
        4. Fetching client IPv4 via ui.
        5. Validated dns via validate_dns function.
        6. Deleted pcap file generated.
        """
        dur, brow, domainUrl, iOS_id, pkt_file_url, domainv4_filter, dns_server_ip = conftest.DURATION, conftest.BROWSER_NAME, conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER, conftest.DNS_SERVER_IP

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        )

        result = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=pkt_file_url, capture_duration=dur, browser=brow, url=domainUrl, ios_udid=iOS_id)
        assert result[0], result[1]

        result = conftest.zia_sanity.fetch_client_ipv4()
        if not result[0]:
            assert result[0], result[1]
        else:
            client_ip = result[2]

        result = conftest.zia_sanity.validate_dns(pcap_file=pkt_file_url, dns_server_ip=dns_server_ip, client_ip=client_ip, domain=domainv4_filter)
        assert result[0], result[1]
            
        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        )
        @allure.title("Validate ZIA reactivation in Tunnel 1.0 when zcc is in background")
    @pytest.mark.xray('QA-318834')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.3)
    def test_tunnel1_zia_reactivate(self) :

        """
        Steps : 
        1. Set the tunnel mode to tunnel 1.
        2. Set zia reactivation time to 1 minute
        3. Update Policy in ZCC.
        4. Turn off ZIA service from ZCC
        5. Validate ZIA reactivation.
        """

        res = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert res[0], res[1]

        res = conftest.ma_app_profile.edit_app_profile()
        assert res[0], res[1]

        conftest.ma_app_profile.edit_app_profile(
            reactivate_web_security_minutes = 1  # number of minutes before enabling internet security service
        )

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        res = conftest.zcc.toggle_service(
            service="ZIA",
            action=False
        )
        assert res[0], res[1]

        time.sleep(60)

        res = conftest.zcc.verify_zcc_ui_fields(
            zia_tab=True,
            zia_user=conftest.config["ZIA_USER_ID"]
        )
        assert res[0], res[1]


    @allure.title('Teardown') 
    def teardown_class(self):
        """
        1. Change back the changes made for AUP.
        2. Toggle Off SAMl auth for ZIA from MA.
        3. Deleting the pcap folder after all the test cases has been ran.
        4. logged out of zcc.
        """
        conftest.ma_notifications.configure_aup(aup_frequency="never")

        conftest.saml_zia_obj.saml_action_smui(mode="disable")

        conftest.sys.delete_pcap_folder(folder_path_respective_cwd=conftest.PKT_CAPTURE_DIR)

        conftest.zcc.logout()

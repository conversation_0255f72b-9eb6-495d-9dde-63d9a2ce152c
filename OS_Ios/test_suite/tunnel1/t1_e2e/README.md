# p0_testcases for ZIA tunnel 1.0

This suite contains p0_testcases for ZIA covering tunnel 1.0 mode.

#### Version Info
iOS ZApp 4.3

#### This Suite includes testcases 
    * ZIA:
    * Test SAML based ZIA login.
    * Test Form based ZIA login.
    * Test validate ZIA tunnel version.
    * Test validate ZIA tunnel sdk version.
    * Test validate ZIA application domain based
    * Test bypass FQDN based.
    * Test bypass pac file based.
    * Test validate DNS for ZIA is system dns.


#### Prerequisites
    * Safari, Chrome and MS Edge browsers should be installed on the device to test traffic through multiple browsers. Make sure to open these apps once so that it would not hinder automation flow.
    * ZCC build installed and in Logged Out state. Open app and accept popups once if installing through testflight.
    * A tenant account with ZIA and ZPA configurations, filled in config.json file.
    * In config file give following :
    --> "IDP_NAME" : "Okta" (idp name configured on ZIA portal)
    * Quick Setup Guide: https://confluence.corp.zscaler.com/display/~SKalra/Setup
    * Before running suite you can also toggle airplane mode on/off to ensure no DNS caching.
    * Also clear history from the browsers safari/chrome.


### Executing program

* Run terminal in Admin mode
* Run appium server : appium
* Then in a new terminal window run the Command:

```
sudo python3.13 trigger_automation.py --testcases OS_Ios/test_suite/p0_testcases/zia/tunnel_1/test_tunnel_1_ipv4.py --zccVersion 4.3.3 --config yourconfig.json
```


### Authors

Ayush Rana (<EMAIL>)
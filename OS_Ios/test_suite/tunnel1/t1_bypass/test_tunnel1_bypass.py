import allure, pytest, time
# from OS_Ios.test_suite.zia.tunnel_1 import conftest
from OS_Ios.test_suite.tunnel1.t1_bypass import conftest
from common_lib.Custom_Markers import add_markers, zcc_mark_version

class TestTunnel1Bypass:
    @allure.title('Setup')
    def setup_class(self):
        """
        Enabling SAML based auth on SMUI
        Disable Browser based auth.
        """

        conftest.ma_app_profile.delete_app_profile()

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()

        result = conftest.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        result=conftest.saml_zia_obj.saml_action_smui(mode="enable")
        assert result[0],result[1]

        result = conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]

        result = conftest.ma_app_profile.edit_app_profile(use_tunnel_sdk_version_43=True)
        assert result[0], result[1]

        result=conftest.ma_service_entitlement.toggle_zia(action=True)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zpa(action=True)
        assert result[0], result[1]

        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zpa_user=conftest.config["ZPA_USER_ID"],
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],
            zia_saml_login=True
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in(check_alerts_after_login=True, dynamic_wait_search_element=40)
        assert result[0], result[1]

        time.sleep(10)

    @pytest.mark.xray("QA-342759")
    @allure.title('Validate Tun1.0 is the Forwarding mode/Tunnel version for ZIA')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.3)
    def test_validate_zia_tunnel_version(self):
        """
        1. Validated the ZIA tunnel version using a function validate_tunnel_version created in ui_zcc file
        """
        result = conftest.zcc.validate_tunnel_version(expected_version="v1.0")
        assert result[0], result[1]
      
        
    @pytest.mark.xray("QA-342760")
    @allure.title('Validate Tun SDK for ZIA is 4.3')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.3)
    def test_validate_zia_tunnel_sdk_version(self):
        """
        1. Validated the ZIA tunnel SDK version using a function validate_tunnel_version created in ui_zcc file 
        """
        result = conftest.zcc.validate_tunnel_version(expected_version="4.3", sdk=True)
        assert result[0], result[1]

    
    @allure.title("Bypass FQDN Tunnel1")
    @pytest.mark.xray('QA-294643')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_bypass_fqdn(self):
        '''
        1. Set Tunnel 1 in forwarding profile.
        2. Add Bypass domain in App profile
        3. Validate that Bypass domain is added in Bypass list.
        4. Access Application and capture packets.
        5. Validate that App is getting bypassed by checking packets captured.
        6. Remove Bypass domain from App profile.
        '''

        conftest.delete_pcap_file(path=conftest.pkt_capture_output_file)

        res = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert res[0], res[1]

        res = conftest.ma_app_profile.edit_app_profile(vpn_bypass=conftest.BYPASS_DOMAIN)
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        time.sleep(5)

        result = conftest.zcc.validate_tunnel_version(expected_version="v1.0", sdk=False, tab="zia")
        assert result[0], result[1]

        conftest.delete_pcap_file(
            path=conftest.pkt_capture_output_file
        )

        result = conftest.pkt_analyze.pktcapture_accessapp_delhistory(
            pkt_capture_file_path=conftest.pkt_capture_output_file,
            pkt_capture_duration=conftest.pkt_capture_duration,
            pkt_capture_interface=conftest.pkt_capture_interface,
            url=conftest.BYPASS_DOMAIN_URL,
            zpa_app=False,
            browser="chrome"
        )
        assert result[0], result[1]
        
        result = conftest.zia_sanity.fetch_client_ipv4()
        assert result[0], result[1]
        client_ip = result[2]

        result = conftest.zia_sanity.fetch_sme_ip()
        assert result[0], result[1]
        sme_ip = result[2]

        result = conftest.zia_sanity.validate_bypass(
            pcap_file=conftest.pkt_capture_output_file,
            domain_filter=conftest.BYPASS_DOMAIN_FILTER,
            client_ip = client_ip,
            sme_ip = sme_ip,
            bypass = True
        )
        assert result[0], result[1]

        res = conftest.ma_app_profile.edit_app_profile()
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        conftest.delete_pcap_file(
            path=conftest.pkt_capture_output_file
        )
        
    

    @allure.title("Bypass IP Tunnel1")
    @pytest.mark.xray('QA-294644')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_bypass_ip(self):
        '''
        1. Set Tunnel 1 in forwarding profile.
        2. Add Bypass IP in App profile
        3. Validate that Bypass IP is added in Bypass list.
        4. Access application and capture packets.
        5. Validate that App is getting bypassed by checking packets captured.
        6. Remove Bypass IP from App profile.
        '''

        res = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert res[0], res[1]

        res = conftest.zia_sanity.ma_app_profile.edit_app_profile(vpn_bypass=conftest.BYPASS_IP_STRING)
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zcc.validate_tunnel_version(expected_version="v1.0", sdk=False, tab="zia")
        assert result[0], result[1]
        
        conftest.delete_pcap_file(
            path=conftest.pkt_capture_output_file
        )

        time.sleep(10)

        result = conftest.pkt_analyze.pktcapture_accessapp_delhistory(
            pkt_capture_file_path=conftest.pkt_capture_output_file,
            pkt_capture_duration=conftest.pkt_capture_duration,
            pkt_capture_interface=conftest.pkt_capture_interface,
            url=conftest.BYPASS_DOMAIN_URL,
            zpa_app=False,
            browser="safari"
        )
        assert result[0], result[1]
        
        result = conftest.zia_sanity.fetch_client_ipv4()
        assert result[0], result[1]
        client_ip = result[2]

        result = conftest.zia_sanity.fetch_sme_ip()
        assert result[0], result[1]
        sme_ip = result[2]

        result = conftest.zia_sanity.validate_bypass(
            pcap_file=conftest.pkt_capture_output_file,
            domain_filter=conftest.BYPASS_DOMAIN_FILTER,
            client_ip = client_ip,
            sme_ip = sme_ip,
            bypass = True
        )
        assert result[0], result[1]

        res = conftest.ma_app_profile.edit_app_profile()
        assert res[0], res[1]

        res = conftest.zia_sanity.ma_app_profile.edit_app_profile()
        assert res[0], res[1]
        
        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        conftest.delete_pcap_file(
            path=conftest.pkt_capture_output_file
        )



    @allure.title("Bypass Subnet Tunnel1")
    @pytest.mark.xray('QA-294645')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_bypass_subnet(self):
        '''
        1. Set Tunnel 1 in forwarding profile.
        2. Add Bypass IP-Subnet in App profile
        3. Validate that Bypass IP-Subnet is added in Bypass list.
        4. Access application and capture the packets.
        5. Validate that App is getting bypassed by checking packets captured.
        6. Remove Bypass IP-Subnet from App profile.
        '''


        res = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert res[0], res[1]

        res = conftest.ma_app_profile.edit_app_profile(vpn_bypass=conftest.BYPASS_IP_SUBNET_STRING)
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zcc.validate_tunnel_version(expected_version="v1.0", sdk=False, tab="zia")
        assert result[0], result[1]


        conftest.delete_pcap_file(
            path=conftest.pkt_capture_output_file
        )

        result = conftest.pkt_analyze.pktcapture_accessapp_delhistory(
            pkt_capture_file_path=conftest.pkt_capture_output_file,
            pkt_capture_duration=conftest.pkt_capture_duration,
            pkt_capture_interface=conftest.pkt_capture_interface,
            url=conftest.BYPASS_DOMAIN_URL,
            zpa_app=False,
            browser="chrome"
        )
        assert result[0], result[1]
        
        result = conftest.zia_sanity.fetch_client_ipv4()
        assert result[0], result[1]
        client_ip = result[2]

        result = conftest.zia_sanity.fetch_sme_ip()
        assert result[0], result[1]
        sme_ip = result[2]

        result = conftest.zia_sanity.validate_bypass(
            pcap_file=conftest.pkt_capture_output_file,
            domain_filter=conftest.BYPASS_DOMAIN_FILTER,
            client_ip = client_ip,
            sme_ip = sme_ip,
            bypass = True
        )
        
        res = conftest.zia_sanity.ma_app_profile.edit_app_profile()
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        conftest.delete_pcap_file(
            path=conftest.pkt_capture_output_file
        )

    @allure.title('Teardown')
    def teardown_class(self):
        """
        1. disabling SAML based auth on SMUI
        2. Logout ZCC
        """

        conftest.ma_app_profile.delete_app_profile()

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()
        
        conftest.delete_pcap_folder(folder_path_respective_cwd=conftest.PKT_CAPTURE_DIR)

        result=conftest.saml_zia_obj.saml_action_smui(mode="disable")
        assert result[0],result[1]

        result = conftest.zcc.logout()
        assert result[0], result[1]
import ipaddress
from common_lib.adminzpa import accesszpaapps,zpapolicy
from common_lib.common.constants import AppProfilePasswords, ZccUi
from common_lib.adminzia.samlzia import SamlZia
from common_lib.mobileadmin.platformsettings import PlatformSettings
from common_lib.mobileadmin import appprofile,serviceentitlement,deviceoverview
from common_lib.mobileadmin import clientconnectornotifications,clientconnectorsupport
from OS_Ios.library.ui_zcc import Zcc
import os,json
from common_lib.common.logger import Logger
import pytest
from OS_Ios.library.ops_system import SysOps
from OS_Ios.library.ops_packet import OpsPacket
from OS_Ios.module.zia_sanity import ZiaSanity

IDENTIFIER = os.environ.get("ios_udid")
DURATION = 30
BROWSER_NAME = "safari"
BROWSER_NAME2 = "chrome"
URLV4 = "https://www.iana.org"
URLV6 = "https://ipv6test.google.com/?gws_rd=ssl"
DOMAINV4 = "iana.org"
DOMAINV6 = "ipv6test.google.com"
DOMAINV4_TO_BYPASS_IN_PAC = "iana"
DOMAINV4_FILTER = "iana"
DOMAINV6_FILTER = "ipv6"

PKT_CAPTURE_DIR = os.path.join(os.getcwd(),'OS_Ios','packet_capture')
PKT_FILE_URL = os.path.join(os.getcwd(),'OS_Ios','packet_capture','iOS_Packets.pcapng')
SE_PAC_FILE_PATH = os.path.join(os.getcwd(),"OS_Ios","resource","pac_files","test_template.pac")
SE_PAC_FILE_NAME = "tunnel_sanity_automation_Ios"

try:  
    if os.path.exists(PKT_CAPTURE_DIR):
        pass
    else:
        os.mkdir(PKT_CAPTURE_DIR)

    config_file = os.path.join(os.getcwd(), "config", os.environ["CONFIG_FILE"])
    config = {}
    with open(config_file) as json_file:
        config.update(json.load(json_file))

    cloud_name = config["CLOUD"]
    logger = Logger.initialize_logger(log_file_name="ios password and disable reason regression",log_level="INFO")
    zia_sanity = ZiaSanity(cloud=config["CLOUD"], config=config_file)
    o = OpsPacket()
    zcc = zia_sanity.zcc
    sys = zia_sanity.sys_ops

    zpa_app_access = accesszpaapps.AccessZpaApps(cloud_name, config_file , log_handle=logger)
    zpa_policy = zpapolicy.ZpaPolicy(cloud_name, config_file, log_handle=logger)
    ma_app_profile = appprofile.AppProfile(cloud_name, config_file, log_handle=logger)
    ma_service_entitlement = serviceentitlement.ServiceEntitlement(cloud_name, config_file, log_handle=logger)
    ma_notifications = clientconnectornotifications.ClientConnectorNotifications(cloud_name, config_file, log_handle=logger)
    ma_support = clientconnectorsupport.ClientConnectorSupport(cloud_name, config_file, log_handle=logger)
    ma_device_overview = deviceoverview.DeviceOverview(cloud_name, config_file, log_handle=logger)
    platform_settings_obj = PlatformSettings(cloud=config['CLOUD'], config=config_file, log_handle=logger)
    saml_zia_obj = SamlZia(cloud=config['CLOUD'], config=config_file, log_handle=logger, debug=True)
    
    with open(SE_PAC_FILE_PATH, 'r') as pac_file:
            pac_content = pac_file.read()
    pac_content = pac_content.replace("<UPDATE_BYPASS_DOMAINv4_HERE>",DOMAINV4_FILTER)
    pac_content = pac_content.replace("<UPDATE_BYPASS_DOMAINv6_HERE>", DOMAINV6_FILTER)

    zia_sanity.zia_pac.delete_pac_file(name=SE_PAC_FILE_NAME)
    result = zia_sanity.zia_pac.create_pac_file(name=SE_PAC_FILE_NAME,pac_content=pac_content)
    
    PAC_URL = zia_sanity.zia_pac.get_pac_url(name=SE_PAC_FILE_NAME)[-1]

    DNS_SERVER_IP = set()
    DOMAIN_IPV4: str = None


except Exception as e:
    pytest.skip(f"Skipping as Error :: {e}")




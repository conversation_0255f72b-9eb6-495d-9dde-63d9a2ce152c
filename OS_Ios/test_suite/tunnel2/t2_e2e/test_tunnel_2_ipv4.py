import allure, pytest
from common_lib.Custom_Markers import add_markers,zcc_mark_version #Required!
# from OS_Ios.test_suite.p0_testcases.zia import conftest
from OS_Ios.test_suite.tunnel2.t2_e2e import conftest


"""
This class contains 7 P0 testcases for Tunnel version 2.0 over an IPv4 network.
"""

class TestValidateZiaTunnel2_Ipv4Testcases:
    @allure.title('Setup')
    def setup_class(self):
        """
        1. Fetching DNS server IP and application resolved IP by running and capturing packets when ZCC is not logged in.
        2. Deleting app profile and forwarding profile and creating a new app profile and forwarding profile.
        3. Editing the forwarding profile to use Tunnel 2.
        4. Editing the app profile to toggle tunnel sdk 4.3 .
        5. Configuring aup.
        6. Disabling browser based auth.
        7. Disabling SAML to use form based login instead of SAML based.
        8. Toggling ZIA to True.
        9. Toggling ZPA to False.
        10. Toggling ZDX to False.
        11. Doing the Form based login with the credentials in config file.
        12. Validating ZCC has been logged in or not.
        """

        conftest.sys.delete_pcap_file(path=conftest.PKT_FILE_URL)

        conftest.zcc.bring_zcc_to_focus()
        if conftest.zcc.ui.check_if_exists(conftest.zcc.elements.LOGOUT_BUTTON, dynamic_wait_search_element=5):
            conftest.zcc.logout()

        result1 = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=conftest.PKT_FILE_URL, capture_duration=conftest.DURATION, browser=conftest.BROWSER_NAME, url=conftest.URLV4, ios_udid=conftest.IDENTIFIER)
        

        if result1[0]:
            result = conftest.sys.fetch_ios_dns_servers(pcap_file_url=conftest.PKT_FILE_URL)
            if not result[0]:
                conftest.logger.error(f"Critical setup error: {result[1]}")
                assert result[0], result[1]
            else:
                conftest.DNS_SERVER_IP = result[2]

            result = conftest.sys.fetch_application_resolved_ip(pcap_file_url=conftest.PKT_FILE_URL, domain=conftest.DOMAINV4_FILTER, application=conftest.DOMAINV4)

            if not result[0]:
                conftest.logger.error(f"Critical setup error: {result[1]}")
                assert result[0], result[1]
            else:
                conftest.DOMAIN_IPV4 = conftest.zia_sanity.concatenate_strings(application_ip=result[2])
        else:
            conftest.logger.error(f"Critical setup error: {result1[1]}")
            assert result1[0], result1[1]

        conftest.ma_app_profile.delete_app_profile()

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()

        result = conftest.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_2")
        assert result[0], result[1]

        result = conftest.ma_app_profile.edit_app_profile(use_tunnel_sdk_version_43=True)
        assert result[0], result[1]

        result=conftest.ma_notifications.configure_aup()
        assert result[0], result[1]

        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]

        result=conftest.saml_zia_obj.saml_action_smui(mode="disable")
        assert result[0],result[1]

        result=conftest.ma_service_entitlement.toggle_zia(action=True)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zpa(action=False)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zdx(action=False)
        assert result[0], result[1]

        result = conftest.zcc.login(
            aup=False,
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in(check_alerts_after_login=True, dynamic_wait_search_element=40)
        assert result[0], result[1]


    @pytest.mark.xray("QA-342765")
    @allure.title('Validate Tun2.0 is the Forwarding mode/Tunnel version for ZIA')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.3)
    def test_validate_zia_tunnel_version(self):
        """
        1. Validated the ZIA tunnel version using a function validate_tunnel_version created in ui_zcc file
        """
        result = conftest.zcc.validate_tunnel_version(expected_version="v2.0 - TLS")
        assert result[0], result[1]

    @pytest.mark.xray("QA-342767") 
    @add_markers("regression","sanity") 
    @allure.title('Validate ZIA Application Domain V4 based')
    @zcc_mark_version(4.3)
    def test_validate_zia_application_domain_v4_based(self):
        """
        1. Initializing multiple variables to be used.
        2. Validated zia application domain v4 based via analyze_packets function in zia_sanity file.
        """
        domainUrl, iOS_id, pkt_file_url, domainv4_filter = conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER
        
        result = conftest.zia_sanity.analyze_packets(pkt_file_url=pkt_file_url, ios_udid=iOS_id, urlv4=domainUrl, domainv4_filter=domainv4_filter, tunnel_ver=2)
        assert result[0], result[1]

    @allure.title('Teardown') 
    def teardown_class(self):
        """
        1. Change back the changes made for AUP.
        2. Toggle Off SAMl auth for ZIA from MA.
        3. Deleting the pcap folder after all the test cases has been ran.
        4. logged out of zcc.
        """
        conftest.ma_notifications.configure_aup(aup_frequency="never")

        conftest.saml_zia_obj.saml_action_smui(mode="disable")

        conftest.sys.delete_pcap_folder(folder_path_respective_cwd=conftest.PKT_CAPTURE_DIR)

        conftest.zcc.logout()

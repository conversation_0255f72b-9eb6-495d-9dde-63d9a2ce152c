import allure
import pytest
# from OS_Ios.test_suite.zia.tunnel_2_dns import conftest
from OS_Ios.test_suite.tunnel2.t2_dns import conftest
from common_lib.Custom_Markers import add_markers, zcc_mark_version

"""
    This class contains 8 test cases related to DNS regression on tunnel 2.0.
"""

class TestDnsTls:

    @allure.title('Setup')
    def setup_class(self):
        """
        1. Fetching DNS server IP and application resolved IP by running and capturing packets when ZCC is not logged in.
        2. Deleting app profile and forwarding profile and creating a new app profile and forwarding profile.
        3. Editing the forwarding profile to use Tunnel 2.
        4. Editing the app profile to toggle tunnel sdk 4.3 .
        5. Configuring aup.
        6. Disabling browser based auth.
        7. Disabling SAML to use form based login instead of SAML based.
        8. Toggling ZIA to True.
        9. Toggling ZPA to False.
        10. Toggling ZDX to False.
        11. Doing the Form based login with the credentials in config file.
        12. Validating ZCC has been logged in or not.
        """

        conftest.sys.delete_pcap_file(path=conftest.PKT_FILE_URL)

        conftest.zcc.bring_zcc_to_focus()
        if conftest.zcc.ui.check_if_exists(conftest.zcc.elements.LOGOUT_BUTTON, dynamic_wait_search_element=5):
            conftest.zcc.logout()

        result1 = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=conftest.PKT_FILE_URL, capture_duration=conftest.DURATION, browser=conftest.BROWSER_NAME, url=conftest.URLV4, ios_udid=conftest.IDENTIFIER)
        
        if result1[0]:
            result = conftest.sys.fetch_ios_dns_servers(pcap_file_url=conftest.PKT_FILE_URL)
            if not result[0]:
                conftest.logger.error(f"Critical setup error: {result[1]}")
                assert result[0], result[1]
            else:
                conftest.DNS_SERVER_IP = result[2]
                conftest.SYSTEM_DNS_STRING = ','.join(map(str, conftest.DNS_SERVER_IP))
                conftest.logger.info(f"System DNS String is : {conftest.DNS_SERVER_IP}")

            result = conftest.sys.fetch_application_resolved_ip(pcap_file_url=conftest.PKT_FILE_URL, domain=conftest.DOMAINV4_FILTER, application=conftest.DOMAINV4)

            if not result[0]:
                conftest.logger.error(f"Critical setup error: {result[1]}")
                assert result[0], result[1]
            else:
                conftest.DOMAIN_IPV4 = conftest.zia_sanity.concatenate_strings(application_ip=result[2])
        else:
            conftest.logger.error(f"Critical setup error: {result1[1]}")
            assert result1[0], result1[1]

        conftest.ma_app_profile.delete_app_profile()

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()

        result = conftest.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_2_tls")
        assert result[0], result[1]

        result = conftest.ma_app_profile.edit_app_profile(use_tunnel_sdk_version_43=True)
        assert result[0], result[1]

        result=conftest.ma_notifications.configure_aup()
        assert result[0], result[1]

        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]

        result=conftest.saml_zia_obj.saml_action_smui(mode="disable")
        assert result[0],result[1]

        result=conftest.ma_service_entitlement.toggle_zia(action=True)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zpa(action=True)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zdx(action=False)
        assert result[0], result[1]

        result = conftest.zcc.login(
            aup=False,
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zpa_user=conftest.config["ZPA_USER_ID"],
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in(check_alerts_after_login=True, dynamic_wait_search_element=40)
        assert result[0], result[1]

    @allure.title("Validate DNS requests are intercepted by ZCC.")
    @pytest.mark.xray(["QA-351617"])
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_dns_inclusion(self):
        """
        Steps:
        1. Initializing multiple variables to be used.
        2. Edited the app profile to enter the domain name in the dns include field and enabled sdk version 4.3 to use tunnel 2.
        3. Updated the policy and deleted the pcap file if it exists.
        4. Accessed the application which is dns included and captured it's packets for analysis.
        5. Validated dns inclusion via validate dns inclusion function and then deleted the pcap file.
        """

        dur, brow, domainUrl, iOS_id, pkt_file_url, domainName = conftest.DURATION, conftest.BROWSER_NAME2, conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4

        res = conftest.zia_sanity.ma_app_profile.edit_app_profile(dns_include=domainName, use_tunnel_sdk_version_43=True)
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        )

        result = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=pkt_file_url, capture_duration=dur, browser=brow, url=domainUrl, ios_udid=iOS_id)
        assert result[0], result[1]

        result = conftest.zia_sanity.validate_dns_inclusion(pcap_file=pkt_file_url, domain_urlv4=domainUrl)
        assert result[0], result[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        )

    @allure.title("Validate DNS requests are not being resolved by ZCC.")
    @pytest.mark.xray(["QA-351618"])
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_dns_exclusion(self):
        """
        Steps:
        1. Initializing multiple variables to be used.
        2. Edited the app profile to enter the domain name for dns include and dns exclude field and enabled sdk version 4.3 to use tunnel 2.
        3. Updated the policy and deleted the pcap file if it exists.
        4. Accessed the application which is dns excluded and captured it's packets for analysis.
        5. Validated dns exclusion via validate dns exclusion function and then deleted the pcap file.
        """

        dur, brow, iOS_id, pkt_file_url = conftest.DURATION, conftest.BROWSER_NAME, conftest.IDENTIFIER, conftest.PKT_FILE_URL

        res = conftest.zia_sanity.ma_app_profile.edit_app_profile(dns_include=conftest.DNS_INCLUSION_DOMAIN, dns_exclude=conftest.DNS_EXCLUSION_DOMAIN, use_tunnel_sdk_version_43=True)
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        )

        result = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=pkt_file_url, capture_duration=dur, browser=brow, url=conftest.DNS_EXCLUSION_DOMAIN_URLV4, ios_udid=iOS_id)
        assert result[0], result[1]

        result = conftest.zia_sanity.validate_dns_exclusion(pcap_file=pkt_file_url, domain_urlv4=conftest.DNS_EXCLUSION_DOMAIN_URLV4)
        assert result[0], result[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        ) 
    
    @allure.title("When System DNS string is entered in Destination inclusion parameter then validating that the DNS requests for include domain are intercepted by ZCC and for exclude domain DNS requests are not being resolved by ZCC.")
    @pytest.mark.xray(["QA-351619"])
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_dns_exclusion_system_dns_in_dest_inclusion(self):
        """
        Steps:
        1. Initializing multiple variables to be used.
        2. Edited the app profile to enter the domain name for dns include and dns exclude field and entered system dns string in the destinaion include field and enabled sdk version 4.3 to use tunnel 2.
        3. Updated the policy and deleted the pcap file if it exists.
        4. Accessed the application which is dns included and captured it's packets for analysis.
        5. Validated dns inclusion via validate dns inclusion function and then deleted the pcap file.
        6. Accessed the application which is dns excluded and captured it's packets for analysis.
        7. Validated dns exclusion via validate dns exclusion function and then deleted the pcap file.
        """
        
        dur, brow, domainUrl, iOS_id, pkt_file_url = conftest.DURATION, conftest.BROWSER_NAME, conftest.DNS_INCLUSION_DOMAIN2_URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL

        result = conftest.zia_sanity.ma_app_profile.edit_app_profile(dns_include=conftest.DNS_INCLUSION_DOMAIN2, dns_exclude=conftest.DNS_EXCLUSION_DOMAIN2, include=conftest.SYSTEM_DNS_STRING, use_tunnel_sdk_version_43=True)
        assert result[0], result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        )

        result = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=pkt_file_url, capture_duration=dur, browser=brow, url=domainUrl, ios_udid=iOS_id)
        assert result[0], result[1]

        result = conftest.zia_sanity.validate_dns_inclusion(pcap_file=pkt_file_url, domain_urlv4=domainUrl)
        assert result[0], result[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        )

        result = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=pkt_file_url, capture_duration=dur, browser=brow, url=conftest.DNS_EXCLUSION_DOMAIN2_URLV4, ios_udid=iOS_id)
        assert result[0], result[1]

        result = conftest.zia_sanity.validate_dns_exclusion(pcap_file=pkt_file_url, domain_urlv4=conftest.DNS_EXCLUSION_DOMAIN2_URLV4)
        assert result[0], result[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        ) 
    
    @allure.title("When System DNS string is entered in Destination exclusion parameter then validating that the DNS requests for exclude domain are not being resolved by ZCC.")
    @pytest.mark.xray(["QA-351620"])
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_dns_exclusion_system_dns_in_dest_exclusion(self):
        """
        Steps:
        1. Initializing multiple variables to be used.
        2. Edited the app profile to enter the domain name for dns exclude field and entered the system dns string in the destination exclude field and enabled sdk version 4.3 to use tunnel 2.
        3. Updated the policy and deleted the pcap file if it exists.
        4. Accessed the application which is dns excluded and captured it's packets for analysis.
        5. Validated dns exclusion via validate dns exclusion function and then deleted the pcap file.
        """
        
        dur, brow, iOS_id, pkt_file_url = conftest.DURATION, conftest.BROWSER_NAME2, conftest.IDENTIFIER, conftest.PKT_FILE_URL

        result = conftest.zia_sanity.ma_app_profile.edit_app_profile(dns_exclude=conftest.DNS_EXCLUSION_DOMAIN2, exclude=conftest.SYSTEM_DNS_STRING, use_tunnel_sdk_version_43=True)
        assert result[0], result[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        )

        result = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=pkt_file_url, capture_duration=dur, browser=brow, url=conftest.DNS_EXCLUSION_DOMAIN2_URLV4, ios_udid=iOS_id)
        assert result[0], result[1]

        result = conftest.zia_sanity.validate_dns_exclusion(pcap_file=pkt_file_url, domain_urlv4=conftest.DNS_EXCLUSION_DOMAIN2_URLV4)
        assert result[0], result[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        ) 

    @allure.title("When Destination inclusion contains 0.0.0.0 and destination exclusion contains a dummy ip range then validating the dns requests are not being resolved for dns exclude domain.")
    @pytest.mark.xray(["QA-351621"])
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_dns_bypassed_no_dns_inclusion_destination_inclusion_contains_all(self):
        """
        Steps:
        1. Initializing multiple variables to be used.
        2. Edited the app profile to enter the domain name for dns exclude field and entered the dummy ip in the destination exclude field and enabled sdk version 4.3 to use tunnel 2.
        3. Updated the policy and deleted the pcap file if it exists.
        4. Accessed the application which is dns excluded and captured it's packets for analysis.
        5. Validated dns exclusion via validate dns exclusion function and then deleted the pcap file.
        """
        
        dur, brow, iOS_id, pkt_file_url = conftest.DURATION, conftest.BROWSER_NAME, conftest.IDENTIFIER, conftest.PKT_FILE_URL

        result = conftest.zia_sanity.ma_app_profile.edit_app_profile(dns_exclude=conftest.DNS_EXCLUSION_DOMAIN2, use_tunnel_sdk_version_43=True, exclude=conftest.DUMMY_IP_RANGE)
        assert result[0], result[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        )

        result = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=pkt_file_url, capture_duration=dur, browser=brow, url=conftest.DNS_EXCLUSION_DOMAIN2_URLV4, ios_udid=iOS_id)
        assert result[0], result[1]

        result = conftest.zia_sanity.validate_dns_exclusion(pcap_file=pkt_file_url, domain_urlv4=conftest.DNS_EXCLUSION_DOMAIN2_URLV4)
        assert result[0], result[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        ) 
    
    @allure.title("Validating DNS requests are bypassed if DNS inclusion is not added and destination inclusion contains a dummy IP range")
    @pytest.mark.xray(["QA-351622"])
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_dns_bypassed_no_dns_inclusion_destination_inclusion_not_contains_all(self):
        """
        Steps:
        1. Initializing multiple variables to be used.
        2. Edited the app profile to enter the dummy ip in the destination include field and enabled sdk version 4.3 to use tunnel 2.
        3. Updated the policy and deleted the pcap file if it exists.
        4. Accessed an application and captured it's packets for analysis.
        5. Validated dns inclusion via validate dns inclusion function and then deleted the pcap file.
        6. Accessed another application and captured it's packets for analysis.
        7. Validated dns exclusion via validate dns exclusion function and then deleted the pcap file.
        """
        
        dur, brow, domainUrl, iOS_id, pkt_file_url = conftest.DURATION, conftest.BROWSER_NAME2, conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL

        result = conftest.zia_sanity.ma_app_profile.edit_app_profile(include=conftest.DUMMY_IP_RANGE, use_tunnel_sdk_version_43=True)
        assert result[0], result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        )

        result = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=pkt_file_url, capture_duration=dur, browser=brow, url=domainUrl, ios_udid=iOS_id)
        assert result[0], result[1]

        result = conftest.zia_sanity.validate_dns_inclusion(pcap_file=pkt_file_url, domain_urlv4=domainUrl)
        assert result[0] == False, result[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        )

        result = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=pkt_file_url, capture_duration=dur, browser=brow, url=conftest.DNS_EXCLUSION_DOMAIN_URLV4, ios_udid=iOS_id)
        assert result[0], result[1]

        result = conftest.zia_sanity.validate_dns_exclusion(pcap_file=pkt_file_url, domain_urlv4=conftest.DNS_EXCLUSION_DOMAIN_URLV4)
        assert result[0], result[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        ) 

    @allure.title("Validate DNS request when dns include contains * as a value and nothing in dns exclude parameter, all the DNS request must be resolved/intercepted by ZCC.")
    @pytest.mark.xray(["QA-351623"])
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_dns_not_bypassed_dns_include_all_exclude_none(self):
        """
        Steps:
        1. Initializing multiple variables to be used.
        2. Edited the app profile to enter * in the dns include field and entered the dummy ip in the destination include field and enabled sdk version 4.3 to use tunnel 2.
        3. Updated the policy and deleted the pcap file if it exists.
        4. Accessed an application and captured it's packets for analysis.
        5. Validated dns inclusion via validate dns inclusion function and then deleted the pcap file.
        6. Accessed another application and captured it's packets for analysis.
        7. Validated dns inclusion via validate dns inclusion function and then deleted the pcap file.
        """
        
        dur, brow, domainUrl, iOS_id, pkt_file_url = conftest.DURATION, conftest.BROWSER_NAME2, conftest.DNS_INCLUSION_DOMAIN2_URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL

        result = conftest.zia_sanity.ma_app_profile.edit_app_profile(dns_include="*", include=conftest.DUMMY_IP_RANGE, use_tunnel_sdk_version_43=True)
        assert result[0], result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        )

        result = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=pkt_file_url, capture_duration=dur, browser=brow, url=domainUrl, ios_udid=iOS_id)
        assert result[0], result[1]

        result = conftest.zia_sanity.validate_dns_inclusion(pcap_file=pkt_file_url, domain_urlv4=domainUrl)
        assert result[0], result[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        )

        result = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=pkt_file_url, capture_duration=dur, browser=brow, url=conftest.DNS_EXCLUSION_DOMAIN2_URLV4, ios_udid=iOS_id)
        assert result[0], result[1]

        result = conftest.zia_sanity.validate_dns_inclusion(pcap_file=pkt_file_url, domain_urlv4=conftest.DNS_EXCLUSION_DOMAIN2_URLV4)
        assert result[0], result[1]

    @allure.title("Verify the behaviour when the Domain Inclusion for DNS Request list contains * and Domain Exclusion for DNS Request contains ZPA applications")
    @pytest.mark.xray(["QA-351624"])
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_dns_bypass_for_zpa_application(self):
        """
        Steps:
        1. Initializing multiple variables to be used.
        2. Edited the app profile to enter * in dns include field and entered the zpa application in the dns exclude field and enterd the dummy ip in the destination include field and enabled sdk version 4.3 to use tunnel 2.
        3. Updated the policy and deleted the pcap file if it exists.
        4. Accessed an application and captured it's packets for analysis.
        5. Validated dns inclusion via validate dns inclusion function and then deleted the pcap file.
        6. Validated zpa dns via validate is zpa dns via broker function.
        """
        
        dur, brow, domainUrl, iOS_id, pkt_file_url = conftest.DURATION, conftest.BROWSER_NAME, conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL

        result = conftest.zia_sanity.ma_app_profile.edit_app_profile(dns_include="*", include=conftest.DUMMY_IP_RANGE, use_tunnel_sdk_version_43=True, dns_exclude=conftest.ZPA_APPLICATION_TO_ACCESS)
        assert result[0], result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        )

        result = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=pkt_file_url, capture_duration=dur, browser=brow, url=domainUrl, ios_udid=iOS_id)
        assert result[0], result[1]

        result = conftest.zia_sanity.validate_dns_inclusion(pcap_file=pkt_file_url, domain_urlv4=domainUrl)
        assert result[0], result[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        )

        result = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=pkt_file_url, capture_duration=dur, browser=brow, url=conftest.zpa_app_url, ios_udid=iOS_id)
        assert result[0], result[1]

        result = conftest.zia_sanity.validate_dns_inclusion(pcap_file=pkt_file_url, domain_urlv4=conftest.ZPA_APPLICATION_TO_ACCESS)
        assert result[0], result[1]

    @allure.title('Teardown') 
    def teardown_class(self):
        """
        1. Change back the changes made for AUP.
        2. Toggle Off SAMl auth for ZIA from MA.
        3. Deleting the pcap folder after all the test cases has been ran.
        4. Deleted the pac file generated.
        5. logged out of zcc.
        """
        conftest.ma_notifications.configure_aup(aup_frequency="never")

        conftest.saml_zia_obj.saml_action_smui(mode="disable")

        conftest.sys.delete_pcap_folder(folder_path_respective_cwd=conftest.PKT_CAPTURE_DIR)

        conftest.zia_sanity.zia_pac.delete_pac_file(name=conftest.SE_PAC_FILE_NAME)

        conftest.zcc.logout()
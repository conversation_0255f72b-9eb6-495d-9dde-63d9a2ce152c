# tunnel 2.0 dns regression testcases

This suite contains tunnel 2.0 dns regression testcases.

#### Version Info
iOS ZApp 4.3

#### This Suite includes testcases 
    * Test DNS inclusion.
    * Test DNS exclusion.
    * Test DNS exclusion system DNS in destination inclusion.
    * Test DNS exclusion system dns in destination exclusion.
    * Test DNS bypassed no dns inclusion destination inclusion contains all.
    * Test DNS bypassed no dns inclusion destination inclusion not contains all.
    * Test DNS not bypassed dns include all exclude none.
    * Test DNS bypass for ZPA application.

#### Prerequisites
    * Safari, Chrome and MS Edge browsers should be installed on the device to test traffic through multiple browsers. Make sure to open these apps once so that it would not hinder automation flow.
    * ZCC build installed and in Logged Out state. Open app and accept popups once if installing through testflight.
    * A tenant account with ZIA and ZPA configurations, filled in config.json file.
    * In config file give following :
    --> "IDP_NAME" : "Okta" (idp name configured on ZIA portal)
    * Quick Setup Guide: https://confluence.corp.zscaler.com/display/~SKalra/Setup
    * Before running suite you can also toggle airplane mode on/off to ensure no DNS caching.
    * Also clear history from the browsers safari/chrome.


### Executing program

* Run terminal in Admin mode
* Run appium server : appium
* Then in a new terminal window run the Command:

```
sudo python3.13 trigger_automation.py --testcases OS_Ios/test_suite/tunnel2/t2_dns/test_02_dns_regression_tls.py --zccVersion 4.3.3 --config yourconfig.json
```


### Authors

Ayush Rana (<EMAIL>)
import allure
import pytest
# from OS_Ios.test_suite.zia.tunnel_2 import conftest
from OS_Ios.test_suite.tunnel2.t2_bypass import conftest
from common_lib.Custom_Markers import add_markers, zcc_mark_version

"""
    This is class created for running tests Tunnel Sanity.
    1. We have a setup method which helps to set the desired tunnel mode (TLS here).
    2. Testcase 1 verifies tunnel 2 tls e2e traffic.
    3. Testcase 2 verifies tunnel 2 tls e2e traffic when an mtu value of 1380 is set.
"""

class TestTunnelTLSe2e:

    @allure.title("setup")
    def setup_class(self):

        conftest.ma_app_profile.delete_app_profile()

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()

        result = conftest.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_2_tls")
        assert result[0], result[1]

        result = conftest.ma_app_profile.edit_app_profile(use_tunnel_sdk_version_43=True)
        assert result[0], result[1]

        result=conftest.ma_notifications.configure_aup()
        assert result[0], result[1]

        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]

        result=conftest.saml_zia_obj.saml_action_smui(mode="disable")
        assert result[0],result[1]

        result=conftest.ma_service_entitlement.toggle_zia(action=True)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zpa(action=False)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zdx(action=False)
        assert result[0], result[1]

        result = conftest.zcc.login(
            aup=False,
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in(check_alerts_after_login=True, dynamic_wait_search_element=40)
        assert result[0], result[1]


    @allure.title("E2E Traffic Test ZCC in Tunnel2 TLS")
    @pytest.mark.xray('QA-351599')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_01_tunnel2_tls_e2e(self):
        """
        Steps:
        1. Initializing multiple variables to be used.
        2. Validated zia application e2e traffic on tunnel 2 tls via analyze_packets function in zia_sanity file.
        """

        domainUrl, iOS_id, pkt_file_url, domainv4_filter = conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER

        result = conftest.zia_sanity.analyze_packets(pkt_file_url=pkt_file_url, ios_udid=iOS_id, urlv4=domainUrl, domainv4_filter=domainv4_filter, tunnel_ver=2)
        assert result[0], result[1]
    
    @allure.title("Validate web traffic is accessible with a custom MTU on T2 Tls")
    @pytest.mark.xray('QA-351600')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_custom_path_mtu_tls(self):
        """
        Steps:
        1. Initializing multiple variables to be used.
        2. Edited the forwarding profile to set mtu value as 1380.
        3. Updated the policy.
        2. Validated zia application e2e traffic on tunnel 2 tls with mtu value as 1380, via analyze_packets function in zia_sanity file.
        """

        domainUrl, iOS_id, pkt_file_url, domainv4_filter = conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER

        result = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_2_tls", mtu_value=1380)
        assert result[0], result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result = conftest.zia_sanity.analyze_packets(pkt_file_url=pkt_file_url, ios_udid=iOS_id, urlv4=domainUrl, domainv4_filter=domainv4_filter, tunnel_ver=2)
        assert result[0], result[1]

    @allure.title('Teardown') 
    def teardown_class(self):
        """
        1. Change back the changes made for AUP.
        2. Toggle Off SAMl auth for ZIA from MA.
        3. Deleting the pcap folder after all the test cases has been ran.
        4. logged out of zcc.
        """
        conftest.ma_notifications.configure_aup(aup_frequency="never")

        conftest.saml_zia_obj.saml_action_smui(mode="disable")

        conftest.sys.delete_pcap_folder(folder_path_respective_cwd=conftest.PKT_CAPTURE_DIR)

        conftest.zia_sanity.zia_pac.delete_pac_file(name=conftest.SE_PAC_FILE_NAME)

        conftest.zcc.logout()
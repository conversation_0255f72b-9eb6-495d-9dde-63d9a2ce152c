import allure
import pytest
# from OS_Ios.test_suite.zia.tunnel_2 import conftest
from OS_Ios.test_suite.tunnel2.t2_bypass import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version #Required!

"""
    This is class created for running tests Tunnel Sanity.
    1. We have a setup method which helps to set the desired tunnel mode (TLS here).
    2. Testcase 1 verifies bypass for iana.org.
    3. Testcase 2 verifies bypass for IP address that iana.org resolves to.
    4. Testcase 3 verifies bypass for subnet ip for iana.org.   
"""

class TestT2Sanity:

    @allure.title('Setup')
    def setup_class(self):
        """
        1. Fetching DNS server IP and application resolved IP by running and capturing packets when ZCC is not logged in.
        2. Deleting app profile and forwarding profile and creating a new app profile and forwarding profile.
        3. Editing the forwarding profile to use Tunnel 2.
        4. Editing the app profile to toggle tunnel sdk 4.3 .
        5. Configuring aup.
        6. Disabling browser based auth.
        7. Disabling SAML to use form based login instead of SAML based.
        8. Toggling ZIA to True.
        9. Toggling ZPA to False.
        10. Toggling ZDX to False.
        11. Doing the Form based login with the credentials in config file.
        12. Validating ZCC has been logged in or not.
        """

        conftest.delete_pcap_file(path=conftest.PKT_FILE_URL)

        conftest.zcc.bring_zcc_to_focus()
        if conftest.zcc.ui.check_if_exists(conftest.zcc.elements.LOGOUT_BUTTON, dynamic_wait_search_element=5):
            conftest.zcc.logout()

        result1 = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=conftest.PKT_FILE_URL, capture_duration=conftest.DURATION, browser=conftest.BROWSER_NAME, url=conftest.URLV4, ios_udid=conftest.IDENTIFIER)

        if result1[0]:

            result = conftest.sys.fetch_application_resolved_ip(pcap_file_url=conftest.PKT_FILE_URL, domain=conftest.DOMAINV4_FILTER, application=conftest.DOMAINV4)

            if not result[0]:
                conftest.logger.error(f"Critical setup error: {result[1]}")
                raise Exception(f"Setup failed - {result[1]}")
            else:
                conftest.DOMAIN_IPV4 = conftest.zia_sanity.concatenate_strings(application_ip=result[2])
                conftest.BYPASS_IP_SUBNET_LIST = list(set([each_ip.split(".")[0]+".0.0.0/8" for each_ip in result[2]]))
                conftest.BYPASS_IP_SUBNET_STRING = ','.join(conftest.BYPASS_IP_SUBNET_LIST)
                conftest.logger.info(f"Bypass IP Subnet string is : {conftest.BYPASS_IP_SUBNET_STRING}")
        else:
            conftest.logger.error(f"Critical setup error: {result1[1]}")
            raise Exception(f"Setup failed - {result1[1]}")

        conftest.ma_app_profile.delete_app_profile()

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()

        result = conftest.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_2_tls")
        assert result[0], result[1]

        result = conftest.ma_app_profile.edit_app_profile(use_tunnel_sdk_version_43=True)
        assert result[0], result[1]

        result=conftest.ma_notifications.configure_aup()
        assert result[0], result[1]

        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]

        result=conftest.saml_zia_obj.saml_action_smui(mode="disable")
        assert result[0],result[1]

        result=conftest.ma_service_entitlement.toggle_zia(action=True)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zpa(action=False)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zdx(action=False)
        assert result[0], result[1]

        result = conftest.zcc.login(
            aup=False,
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in(check_alerts_after_login=True, dynamic_wait_search_element=40)
        assert result[0], result[1]

    @allure.title("Test VPN bypassess by adding FQDNs and run traffic test to confirm bypasses are working")
    @pytest.mark.xray('QA-351596')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_bypass_fqdn_t2_tls(self):
        """
        1. Initializing multiple variables to be used.
        2. Edited the app profile to enter domain to be bypassed in the vpn bypass field and also enabling sdk version 4.3 to enable tunnel 2.
        3. Updated the policy.
        4. Validated domain bypass via fqdn for zia in Tunnel 2 via analyze_packets function in zia_sanity file.
        """
        
        domainUrl, iOS_id, pkt_file_url, domainv4_filter, bypass_domain = conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER, conftest.DOMAINV4

        res = conftest.zia_sanity.ma_app_profile.edit_app_profile(vpn_bypass=bypass_domain, use_tunnel_sdk_version_43=True)
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zia_sanity.analyze_packets(pkt_file_url=pkt_file_url, ios_udid=iOS_id, urlv4=domainUrl, domainv4_filter=domainv4_filter, tunnel_ver=2, bypass=True)
        assert result[0], result[1]

    @allure.title("Test VPN bypassess by adding IPs and run traffic test to confirm bypasses are working")
    @pytest.mark.xray('QA-351597')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_bypass_ip_t2_tls(self):
        """
        1. Initializing multiple variables to be used.
        2. Edited the app profile to enter ip address of a domain to be bypassed in the vpn bypass field and also enabling sdk version 4.3 to enable tunnel 2.
        3. Updated the policy.
        4. Validated domain bypass via domain's IP addresses for zia in Tunnel 2 via analyze_packets function in zia_sanity file.
        """
        
        domainUrl, iOS_id, pkt_file_url, domainv4_filter, domain_ipv4 = conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER, conftest.DOMAIN_IPV4

        res = conftest.zia_sanity.ma_app_profile.edit_app_profile(vpn_bypass=domain_ipv4, use_tunnel_sdk_version_43=True)
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zia_sanity.analyze_packets(pkt_file_url=pkt_file_url, ios_udid=iOS_id, urlv4=domainUrl, domainv4_filter=domainv4_filter, tunnel_ver=2, bypass=True)
        assert result[0], result[1]

    @allure.title("Test VPN bypassess by adding subnets and run traffic test to confirm bypasses are working")
    @pytest.mark.xray('QA-351598')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_bypass_ip_subnet_t2_tls(self):
        """
        1. Initializing multiple variables to be used.
        2. Edited the app profile to enter bypass ip subnet string of a domain to be bypassed in the vpn bypass field and also enabling sdk version 4.3 to enable tunnel 2.
        3. Updated the policy.
        4. Validated domain bypass via domain's bypass ip subnet string for zia in Tunnel 2 via analyze_packets function in zia_sanity file.
        """

        domainUrl, iOS_id, pkt_file_url, domainv4_filter, bypass_ip_subnet_string = conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER, conftest.BYPASS_IP_SUBNET_STRING

        res = conftest.zia_sanity.ma_app_profile.edit_app_profile(vpn_bypass=bypass_ip_subnet_string, use_tunnel_sdk_version_43=True)
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zia_sanity.analyze_packets(pkt_file_url=pkt_file_url, ios_udid=iOS_id, urlv4=domainUrl, domainv4_filter=domainv4_filter, tunnel_ver=2, bypass=True)
        assert result[0], result[1]

    @allure.title('Teardown') 
    def teardown_class(self):
        """
        1. Change back the changes made for AUP.
        2. Toggle Off SAMl auth for ZIA from MA.
        3. Deleting the pcap folder after all the test cases has been ran.
        4. logged out of zcc.
        """
        conftest.ma_notifications.configure_aup(aup_frequency="never")

        conftest.saml_zia_obj.saml_action_smui(mode="disable")

        conftest.sys.delete_pcap_folder(folder_path_respective_cwd=conftest.PKT_CAPTURE_DIR)

        conftest.zia_sanity.zia_pac.delete_pac_file(name=conftest.SE_PAC_FILE_NAME)

        conftest.zcc.logout()
import allure
import pytest
# from OS_Ios.test_suite.zia.tunnel_2 import conftest
from OS_Ios.test_suite.tunnel2.t2_bypass import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version #Required!

""" 
    This class contains 1 test case to verify pac bypass on tunnel 2 tls mode
"""

class TestT2Sanity:

    @allure.title('Setup')
    def setup_class(self):
        """
        1. Deleting app profile and forwarding profile and creating a new app profile and forwarding profile.
        2. Editing the forwarding profile to use Tunnel 2.
        3. Editing the app profile to toggle tunnel sdk 4.3 .
        4. Configuring aup.
        5. Disabling browser based auth.
        6. Disabling SAML to use form based login instead of SAML based.
        7. Toggling ZIA to True.
        8. Toggling ZPA to False.
        9. Toggling ZDX to False.
        10. Doing the Form based login with the credentials in config file.
        11. Validating ZCC has been logged in or not.
        """

        conftest.ma_app_profile.delete_app_profile()

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()

        result = conftest.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_2")
        assert result[0], result[1]

        result = conftest.ma_app_profile.edit_app_profile(use_tunnel_sdk_version_43=True)
        assert result[0], result[1]

        result=conftest.ma_notifications.configure_aup()
        assert result[0], result[1]

        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]

        result=conftest.saml_zia_obj.saml_action_smui(mode="disable")
        assert result[0],result[1]

        result=conftest.ma_service_entitlement.toggle_zia(action=True)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zpa(action=False)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zdx(action=False)
        assert result[0], result[1]

        result = conftest.zcc.login(
            aup=False,
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in(check_alerts_after_login=True, dynamic_wait_search_element=40)
        assert result[0], result[1]


    @allure.title("Pac Bypass Domain based on Tunnel2")
    @pytest.mark.xray('QA-351613')
    @add_markers("regression", "sanity")
    @zcc_mark_version(4.3)
    def test_bypass_domain_via_pac_file(self):
        """
        1. Initializing multiple variables to be used.
        2. Edited the forwarding profile to enable redirect web traffic.
        3. Edited the app profile to place the pac url to be used.
        4. Updated the policy.
        5. Validated domain bypass via pac file for zia in Tunnel 2 via analyze_packets function in zia_sanity file.
        6. Edited the forwarding profile to disable redirect web traffic.
        """

        domainUrl, iOS_id, pkt_file_url, domainv4_filter = conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER

        res = conftest.zia_sanity.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_2_tls", redirect_web_traffic=1)
        assert res[0], res[1]

        res = conftest.zia_sanity.ma_app_profile.edit_app_profile(pac=conftest.PAC_URL, use_tunnel_sdk_version_43=True)
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zia_sanity.analyze_packets(pkt_file_url=pkt_file_url, ios_udid=iOS_id, urlv4=domainUrl, domainv4_filter=domainv4_filter, tunnel_ver=2, bypass=True)
        assert result[0], result[1]

        res = conftest.zia_sanity.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_2_tls", redirect_web_traffic=0)
        assert res[0], res[1]

    @allure.title('Teardown') 
    def teardown_class(self):
        """
        1. Change back the changes made for AUP.
        2. Toggle Off SAMl auth for ZIA from MA.
        3. Deleting the pcap folder after all the test cases has been ran.
        4. Deleted the pac file generated.
        5. logged out of zcc.
        """
        conftest.ma_notifications.configure_aup(aup_frequency="never")

        conftest.saml_zia_obj.saml_action_smui(mode="disable")

        conftest.sys.delete_pcap_folder(folder_path_respective_cwd=conftest.PKT_CAPTURE_DIR)

        conftest.zia_sanity.zia_pac.delete_pac_file(name=conftest.SE_PAC_FILE_NAME)

        conftest.zcc.logout()

# tunnel 2.0 testcases

This suite contains tunnel 2.0 testcases.

#### Version Info
iOS ZApp 4.3

#### This Suite includes testcases 
    * test_bypass_fqdn_t2_tls.
    * test_bypass_ip_t2_tls.
    * test_bypass_ip_subnet_t2_tls.
    * test_01_tunnel2_tls_e2e.
    * test_custom_path_mtu_tls.
    * Test DNS resolver system dest exclusion.
    * Test destination exclusion IP.
    * Test destination exclusion IP subnet.
    * Test destination exclusion IP port.
    * Test destination exclusion IP port range.
    * Test destination exclusion IP port protocol.
    * Test DNS resolver system dest inclusion.
    * Test destination inclusion IP.
    * Test destination inclusion IP subnet.
    * Test destination inclusion IP port.
    * Test destination inclusion IP port range.
    * Test destination inclusion IP port protocol.
    * Test bypass via pac file.
    * Test Tunnel 2 tls to twlp.
    * Test Tunnel 2 tls to Tunnel 1.
    * Test Tunnel 2 tls to. tunnel 2 dtls.

#### Prerequisites
    * Safari, Chrome and MS Edge browsers should be installed on the device to test traffic through multiple browsers. Make sure to open these apps once so that it would not hinder automation flow.
    * ZCC build installed and in Logged Out state. Open app and accept popups once if installing through testflight.
    * A tenant account with ZIA and ZPA configurations, filled in config.json file.
    * In config file give following :
    --> "IDP_NAME" : "Okta" (idp name configured on ZIA portal)
    * Quick Setup Guide: https://confluence.corp.zscaler.com/display/~SKalra/Setup
    * Before running suite you can also toggle airplane mode on/off to ensure no DNS caching.
    * Also clear history from the browsers safari/chrome.


### Executing program

* Run terminal in Admin mode
* Run appium server : appium
* Then in a new terminal window run the Command:

```
sudo python3.13 trigger_automation.py --testcases OS_Ios/test_suite/tunnel2/t2_bypass/{testfile_name_that_you_want_to_execute} --zccVersion 4.3.3 --config yourconfig.json
```


### Authors

Ayush Rana (<EMAIL>)
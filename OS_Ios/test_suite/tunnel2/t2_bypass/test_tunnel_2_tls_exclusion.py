import allure
import pytest
# from OS_Ios.test_suite.zia.tunnel_2 import conftest
from OS_Ios.test_suite.tunnel2.t2_bypass import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version #Required!

"""
    This class contains 6 test cases related to destination exclusion
"""

class TestT2Sanity:
    @allure.title("Setup")
    def setup_class(self):

        conftest.sys.delete_pcap_file(path=conftest.PKT_FILE_URL)

        conftest.zcc.bring_zcc_to_focus()
        if conftest.zcc.ui.check_if_exists(conftest.zcc.elements.LOGOUT_BUTTON, dynamic_wait_search_element=5):
            conftest.zcc.logout()

        result1 = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=conftest.PKT_FILE_URL, capture_duration=conftest.DURATION, browser=conftest.BROWSER_NAME, url=conftest.URLV4, ios_udid=conftest.IDENTIFIER)
        

        if result1[0]:
            result = conftest.sys.fetch_ios_dns_servers(pcap_file_url=conftest.PKT_FILE_URL)
            if not result[0]:
                conftest.logger.error(f"Critical setup error: {result[1]}")
                assert result[0], result[1]
            else:
                conftest.DNS_SERVER_IP = result[2]

            result = conftest.sys.fetch_application_resolved_ip(pcap_file_url=conftest.PKT_FILE_URL, domain=conftest.DOMAINV4_FILTER, application=conftest.DOMAINV4)

            if not result[0]:
                conftest.logger.error(f"Critical setup error: {result[1]}")
                assert result[0], result[1]
            else:
                conftest.NORMAL_IP = list(result[2])
                conftest.logger.info(f"All Resolved IP for the application is : {conftest.NORMAL_IP}")
                conftest.DOMAIN_IPV4 = conftest.zia_sanity.concatenate_strings(application_ip=result[2])
                conftest.BYPASS_IP_SUBNET_LIST = list(set([each_ip.split(".")[0]+".0.0.0/8" for each_ip in result[2]]))
                conftest.BYPASS_IP_SUBNET_STRING = ','.join(conftest.BYPASS_IP_SUBNET_LIST)
                conftest.BYPASS_IP_WITH_PORT = ','.join([each_ip+":"+conftest.PORT for each_ip in conftest.NORMAL_IP])
                conftest.BYPASS_IP_WITH_PORT_RANGE = ','.join([each_ip+":"+conftest.PORT_RANGE for each_ip in conftest.NORMAL_IP])
                conftest.BYPASS_IP_WITH_PORT_RANGE_PROTOCOL = ','.join([each_ip+":"+conftest.PORT+":"+conftest.PROTOCOL for each_ip in conftest.NORMAL_IP])

        else:
            conftest.logger.error(f"Critical setup error: {result1[1]}")
            assert result1[0], result1[1]

        conftest.ma_app_profile.delete_app_profile()

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()

        result = conftest.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_2_tls")
        assert result[0], result[1]

        result = conftest.ma_app_profile.edit_app_profile(use_tunnel_sdk_version_43=True)
        assert result[0], result[1]

        result=conftest.ma_notifications.configure_aup()
        assert result[0], result[1]

        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]

        result=conftest.saml_zia_obj.saml_action_smui(mode="disable")
        assert result[0],result[1]

        result=conftest.ma_service_entitlement.toggle_zia(action=True)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zpa(action=False)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zdx(action=False)
        assert result[0], result[1]

        result = conftest.zcc.login(
            aup=False,
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in(check_alerts_after_login=True, dynamic_wait_search_element=40)
        assert result[0], result[1]

        result = conftest.zcc.validate_tunnel_version(expected_version="v2.0 - TLS")
        assert result[0], result[1]

    @allure.title("Validate dns resolution is done via the system dns when destination exclusion is added - T2 Tls")
    @pytest.mark.xray('QA-351601')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_dns_resolver_system_dest_exclusion_tls(self):
        """
        Steps:
        1. Initializing multiple variables to be used.
        2. Edited the app profile to enter the ip address of a domain in the exclude field and enabled sdk version 4.3 to use tunnel 2.
        3. Updated the policy.
        4. Fetched Client IPv4 address from the ZCC UI and then deleted the pcap file if it exists.
        5. Access the application which is destination excluded and captured it's packets for analysis.
        6. Validated dns requests are being resolved by system dns server via validate dns function. and then deleted the pcap file.
        """
        
        domainUrl, iOS_id, pkt_file_url, domainv4_filter, domain_ipv4, dns_server_ip, dur, brow = conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER, conftest.DOMAIN_IPV4, conftest.DNS_SERVER_IP, conftest.DURATION, conftest.BROWSER_NAME
        
        res = conftest.zia_sanity.ma_app_profile.edit_app_profile(exclude=domain_ipv4, use_tunnel_sdk_version_43=True)
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zia_sanity.fetch_client_ipv4()
        if not result[0]:
            assert result[0], result[1]
        else:
            client_ip = result[2]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        )

        result = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=pkt_file_url, capture_duration=dur, browser=brow, url=domainUrl, ios_udid=iOS_id)
        assert result[0], result[1]

        result = conftest.zia_sanity.validate_dns(pcap_file=pkt_file_url, dns_server_ip=dns_server_ip, domain=domainv4_filter, client_ip=client_ip)
        assert result[0], result[1]

        conftest.sys.delete_pcap_file(
            path=pkt_file_url
        )

    @allure.title("Test Destination exclusion - IP addrsss - and run traffic test to confirm bypasses are working")
    @pytest.mark.xray('QA-351602')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_exclusion_ip_t2_tls(self):
        """
        Steps:
        1. Initializing multiple variables to be used.
        2. Edited the app profile to enter the ip address of a domain in the exclude field and enabled sdk version 4.3 to use tunnel 2.
        3. Updated the policy.
        4. Validated destination exclusion via analyze packets function.
        """

        domainUrl, iOS_id, pkt_file_url, domainv4_filter, domain_ipv4 = conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER, conftest.DOMAIN_IPV4
        
        res = conftest.zia_sanity.ma_app_profile.edit_app_profile(exclude=domain_ipv4, use_tunnel_sdk_version_43=True)
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zia_sanity.analyze_packets(pkt_file_url=pkt_file_url, ios_udid=iOS_id, urlv4=domainUrl, domainv4_filter=domainv4_filter, tunnel_ver=2, bypass=True)
        assert result[0], result[1]

    @allure.title("Test Destination exclusion - Subnet - and run traffic test to confirm bypasses are working")
    @pytest.mark.xray('QA-351603')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_exclusion_ip_subnet_t2_tls(self):
        """
        Steps:
        1. Initializing multiple variables to be used.
        2. Edited the app profile to enter the bypass ip subnet of a domain in the exclude field and enabled sdk version 4.3 to use tunnel 2.
        3. Updated the policy.
        4. Validated destination exclusion via analyze packets function.
        """

        domainUrl, iOS_id, pkt_file_url, domainv4_filter, bypass_ip_subnet_string = conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER, conftest.BYPASS_IP_SUBNET_STRING
        
        res = conftest.zia_sanity.ma_app_profile.edit_app_profile(exclude=bypass_ip_subnet_string, use_tunnel_sdk_version_43=True)
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zia_sanity.analyze_packets(pkt_file_url=pkt_file_url, ios_udid=iOS_id, urlv4=domainUrl, domainv4_filter=domainv4_filter, tunnel_ver=2, bypass=True)
        assert result[0], result[1]

    @allure.title("Test Destination exclusion - IP:Port - and run traffic test to confirm bypasses are working")
    @pytest.mark.xray('QA-351604')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_exclusion_ip_port_t2_tls(self):
        """
        Steps:
        1. Initializing multiple variables to be used.
        2. Edited the app profile to enter the bypass ip with port of a domain in the exclude field and enabled sdk version 4.3 to use tunnel 2.
        3. Updated the policy.
        4. Validated destination exclusion via analyze packets function.
        """

        domainUrl, iOS_id, pkt_file_url, domainv4_filter, bypass_ip_with_port = conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER, conftest.BYPASS_IP_WITH_PORT
        
        res = conftest.zia_sanity.ma_app_profile.edit_app_profile(exclude=bypass_ip_with_port, use_tunnel_sdk_version_43=True)
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zia_sanity.analyze_packets(pkt_file_url=pkt_file_url, ios_udid=iOS_id, urlv4=domainUrl, domainv4_filter=domainv4_filter, tunnel_ver=2, bypass=True)
        assert result[0], result[1]

    @allure.title("Test Destination exclusion - IP:Port - range - and run traffic test to confirm bypasses are working")
    @pytest.mark.xray('QA-351605')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_exclusion_ip_port_range_t2_tls(self):
        """
        Steps:
        1. Initializing multiple variables to be used.
        2. Edited the app profile to enter the bypass ip with port range of a domain in the exclude field and enabled sdk version 4.3 to use tunnel 2.
        3. Updated the policy.
        4. Validated destination exclusion via analyze packets function.
        """

        domainUrl, iOS_id, pkt_file_url, domainv4_filter, bypass_ip_with_port_range = conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER, conftest.BYPASS_IP_WITH_PORT_RANGE
        
        res = conftest.zia_sanity.ma_app_profile.edit_app_profile(exclude=bypass_ip_with_port_range, use_tunnel_sdk_version_43=True)
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zia_sanity.analyze_packets(pkt_file_url=pkt_file_url, ios_udid=iOS_id, urlv4=domainUrl, domainv4_filter=domainv4_filter, tunnel_ver=2, bypass=True)
        assert result[0], result[1]

    @allure.title("Test Destination exclusion - IP:Port:Protocol - and run traffic test to confirm bypasses are working")
    @pytest.mark.xray('QA-351606')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_exclusion_ip_port_protocol_t2_tls(self):
        """
        Steps:
        1. Initializing multiple variables to be used.
        2. Edited the app profile to enter the bypass ip port range protocol of a domain in the exclude field and enabled sdk version 4.3 to use tunnel 2.
        3. Updated the policy.
        4. Validated destination exclusion via analyze packets function.
        """

        domainUrl, iOS_id, pkt_file_url, domainv4_filter, bypass_ip_with_port_range_protocol = conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER, conftest.BYPASS_IP_WITH_PORT_RANGE_PROTOCOL
        
        res = conftest.zia_sanity.ma_app_profile.edit_app_profile(exclude=bypass_ip_with_port_range_protocol, use_tunnel_sdk_version_43=True)
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zia_sanity.analyze_packets(pkt_file_url=pkt_file_url, ios_udid=iOS_id, urlv4=domainUrl, domainv4_filter=domainv4_filter, tunnel_ver=2, bypass=True)
        assert result[0], result[1]

    @allure.title('Teardown') 
    def teardown_class(self):
        """
        1. Change back the changes made for AUP.
        2. Toggle Off SAMl auth for ZIA from MA.
        3. Deleting the pcap folder after all the test cases has been ran.
        4. Deleted the pac file generated.
        5. logged out of zcc.
        """
        conftest.ma_notifications.configure_aup(aup_frequency="never")

        conftest.saml_zia_obj.saml_action_smui(mode="disable")

        conftest.sys.delete_pcap_folder(folder_path_respective_cwd=conftest.PKT_CAPTURE_DIR)

        conftest.zia_sanity.zia_pac.delete_pac_file(name=conftest.SE_PAC_FILE_NAME)

        conftest.zcc.logout()

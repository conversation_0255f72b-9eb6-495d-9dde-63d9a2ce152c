import os
import json,pytest
from common_lib.mobileadmin import appprofile, serviceentitlement, forwardingprofile
from common_lib.mobileadmin import clientconnectornotifications, clientconnectorsupport
from common_lib.common.logger import Logger
from OS_Ios.library.ui_zcc import Zcc
from common_lib.common.log_ops import LogOps
from OS_Ios.library import ops_installation
from common_lib.adminzia.ziahelper import <PERSON><PERSON>H<PERSON>per
from common_lib.mobileadmin import platformsettings
from common_lib.common import constants
from common_lib.adminzia.samlzia import Saml<PERSON><PERSON>

try:
    config_file = os.path.join(os.getcwd(), "config", os.environ["CONFIG_FILE"])
    config = {}
    with open(config_file) as json_file:
        config.update(json.load(json_file))

    cloud_name = config["CLOUD"]
    log = Logger.initialize_logger(log_file_name="Ios Zcc Auth", log_level="INFO")
    zcc = Zcc(log_handle=log, start_app=True)
    ma_app_profile = appprofile.AppProfile(cloud_name, config_file, log_handle=log)
    ma_service_entitlement = serviceentitlement.ServiceEntitlement(cloud_name, config_file, log_handle=log)
    ma_notifications = clientconnectornotifications.ClientConnectorNotifications(cloud_name, config_file, log_handle=log)
    ma_support = clientconnectorsupport.ClientConnectorSupport(cloud_name, config_file, log_handle=log)
    ma_forwarding_profile = forwardingprofile.ForwardingProfile(cloud_name, config_file, log_handle=log)
    zcc_install_uninstall = ops_installation.InstallUninstall(logger=log)
    log_ops=LogOps(log_handle=log)
    zia_helper=ZiaHelper(config['CLOUD'],config_file,log_handle=log)
    ma_platfrom_settings = platformsettings.PlatformSettings(config['CLOUD'], config_file)
    const=constants.Utils()
    saml_zia=SamlZia(config["CLOUD"], config_file,log_handle=log)
    AUP_STRING = "This is your company's AUP"
except Exception as e:
    pytest.skip(f"Skipping as Error :: {e}")
def make_teardown():
    """
    Delete App profile, forwarding profile
    Disbale browser_based_auth
    """
    ma_app_profile.delete_app_profile()
    ma_app_profile.forwarding_profile.delete_forwarding_profile()
    ma_platfrom_settings.browser_based_auth_action_ma(toggle="disable",use_default_browser=False)

@pytest.fixture(scope="class", autouse=True)
def create_setup(request):
    """
    Uninstall ZCC and Install it back to make env clean
    Create forwarding profile, app profile
    Toggle ZPA on
    Disable login_hint
    Disable AUP
    """

    try:
        ma_app_profile.delete_app_profile()
        ma_app_profile.forwarding_profile.delete_forwarding_profile()

        result = ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = ma_app_profile.create_app_profile()
        assert result[0], result[1]

        result = ma_service_entitlement.toggle_zpa(action=True)
        assert result[0], result[1]

        result=ma_notifications.configure_aup(aup_frequency="never")
        assert result[0],result[1]

        result=ma_platfrom_settings.browser_based_auth_action_ma(toggle="disable",use_default_browser=False)
        assert result[0],result[1]

    except Exception as e:
        request.addfinalizer(make_teardown)
        raise Exception(f"An Exception has occured, during execution of setup at MA side: {e}")
    else:
        request.addfinalizer(make_teardown)

# ZCC Login using SAML and form based auth

This module contains testcases for login into ZCC using both SAML and Form based auth for ZIA and ZPA.

#### Version Info

ZCC 3.7+

#### This Suite includes testcases 
    * ZIA form based for ZIA only company
    * ZIA SAML based for ZIA only company
    * ZIA form based for ZIA + ZDX only company
    * ZIA + ZPA SAML based for ZIA + ZPA with ZDX company
    * ZIA + ZPA SAML based for ZIA + ZPA without ZDX company
    * ZIA form based + ZPA SAML based for ZIA + ZPA without ZDX company
    * ZIA form based + ZPA SAML based for ZIA + ZPA with ZDX company
    * All the above combinations with AUP.

#### Prerequisites
    * ZCC Build installed and in logged out state 
    * Unenroll iOS device from MDM 
    * A tenant account with ZIA, ZPA, ZDX and MA configurations, filled in config.json file. 
    * The user has to configure ZIA SAML on OKTA . Refer https://confluence.corp.zscaler.com/display/~bhasker.patel/1.2+ZIA+Okta+IdP+Setup+with+SAML for the same .
    * Quick Setup Guide: https://confluence.corp.zscaler.com/display/ZAPP/iOS+Automation+Setup
    * Automation Doc - 


### Executing program

* Get the appium server started 
* Run terminal in Admin mode
* Command:

```
python3 trigger_automation.py --testcases /Users/<USER>/Desktop/ios-automation/zapp_automation/OS_Ios/test_suite/zcc_misc/auth/zcc_auth_saml_formbased/ --zccVersion 3.8  --config ios-automation.json

```

### Authors
Priyanshu Sharma      (<EMAIL>)

import allure, pytest, time
from common_lib.Custom_Markers import add_markers,zcc_mark_version #Required!
#from OS_Ios.test_suite.zcc_misc.zcc_auth_saml_form import conftest
from OS_Ios.test_suite.auth.zcc_auth_saml_and_formbased import conftest

"""
This class contains testcases for using form based auth for Login into ZCC
"""
class TestLoginWithAUP:
    @allure.title('Setup')
    def setup_class(self):
        """
        1. Toggle SAML auth for ZIA.
        2. Set AUP string in MA.
        """
        result=conftest.saml_zia.saml_action_smui(mode="disable")
        assert result[0],result[1]
        result=conftest.ma_notifications.configure_aup(aup_frequency="after user enrolment",aup_string=conftest.AUP_STRING)
        assert result[0],result[1]

    @pytest.mark.xray("QA-293914") 
    @add_markers("regression","sanity") 
    @allure.title('Verify ZCC form based Auth for ZIA and SAML for ZPA with aup')
    @zcc_mark_version(3.7)
    def test_zia_form_zpa_form_without_zdx_with_aup(self):
        """
        1. Enable ZPA from MA.
        2. Disable ZDX from MA.
        3. Login ZCC.
        4. Logout ZCC.
        """
      
        result = conftest.ma_service_entitlement.toggle_zpa(action=True)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zdx(action=False)
        assert result[0], result[1]


        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zpa_user=conftest.config["ZPA_USER_ID"],
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],
            aup=True
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in()
        assert result[0],result[1]

        result = conftest.zcc.logout()
        assert result[0], result[1]
    

    @pytest.mark.xray("QA-293915") 
    @add_markers("regression","sanity") 
    @allure.title('Verify ZCC Auth with form based for ZIA and SAML for ZPA and ZDX WITH AUP')
    @zcc_mark_version(3.7)
    def test_zia_form_zpa_form_with_zdx_with_aup(self):
        """
        1. Enable ZPA from MA.
        2. Enable ZDX from MA.
        3. Login ZCC.
        4. Logout ZCC.
        """
      
        result = conftest.ma_service_entitlement.toggle_zpa(action=True)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zdx(action=True)
        assert result[0], result[1]

        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zpa_user=conftest.config["ZPA_USER_ID"],
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],
            aup=True
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in()
        assert result[0],result[1]

        result = conftest.zcc.logout()
        assert result[0], result[1]
    

    @pytest.mark.xray("QA-293916") 
    @add_markers("regression","sanity") 
    @allure.title('Verify ZCC form based Auth for ZIA + ZDX company with aup')
    @zcc_mark_version(3.7)
    def test_zia_form_zpa_off_with_zdx_with_aup(self):
        """
        1. Disable ZPA from MA.
        2. Login ZCC.
        3. Logout ZCC.
        """
      
        result = conftest.ma_service_entitlement.toggle_zpa(action=False)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zdx(action=True)
        assert result[0], result[1]

        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            aup=True
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in()
        assert result[0],result[1]

        result = conftest.zcc.logout()
        assert result[0], result[1]
    

    @pytest.mark.xray("QA-293917") 
    @add_markers("regression","sanity") 
    @allure.title('Verify ZCC form based Auth for ZIA only company with aup')
    @zcc_mark_version(3.7)
    def test_zia_form_zpa_off_zdx_off_with_aup(self):
        """
        1. Disable ZPA from MA.
        2. Disbale ZDX from MA.
        3. Login ZCC.
        4. Logout ZCC.
        """
      
        result = conftest.ma_service_entitlement.toggle_zpa(action=False)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zdx(action=False)
        assert result[0], result[1]

        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            aup=True
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in()
        assert result[0],result[1]

        result = conftest.zcc.logout()
        assert result[0], result[1]


    @allure.title('Teardown') 
    def teardown_class(self):
        """
        1. Change back the changes made for AUP.
        2. Toggle Off SAMl auth for ZIA from MA.
        """
        conftest.ma_notifications.configure_aup(aup_frequency="never")

        conftest.saml_zia.saml_action_smui(mode="disable")

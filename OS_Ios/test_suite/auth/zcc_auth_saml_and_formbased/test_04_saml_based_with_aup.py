import allure, pytest, time
from common_lib.Custom_Markers import add_markers,zcc_mark_version #Required!
#from OS_Ios.test_suite.zcc_misc.zcc_auth_saml_form import conftest
from OS_Ios.test_suite.auth.zcc_auth_saml_and_formbased import conftest

"""
This class contains testcases for using saml_login for Login into ZPA
"""
class TestFormLoginWithAUP:
    @allure.title('Setup')
    def setup_class(self):
        """
        1. Toggle SAML auth for ZIA.
        2. Change saml method to Okta configured on ZIA Admin portal
        3. Set AUP string in MA.
        """
        result=conftest.saml_zia.saml_action_smui(mode="enable")
        assert result[0],result[1]
        result = conftest.saml_zia.change_saml_on_zia(idp_name=conftest.config['IDP_NAME'])
        assert result[0],result[1]
        result=conftest.ma_notifications.configure_aup(aup_frequency="after user enrolment",aup_string=conftest.AUP_STRING)
        assert result[0],result[1]

    @pytest.mark.xray(["QA-293910"]) 
    @add_markers("regression","sanity") 
    @allure.title('iOS Test SAML based auth ( ZIA & ZPA ),Verify ZCC SAML based Auth for ZIA + ZPA company with OKTA IDP WITH AUP')
    @zcc_mark_version(3.7)
    def test_zia_saml_zpa_saml_without_zdx_with_aup(self):
        """
        1. Enable ZPA from MA.
        2. Disable ZDX from MA.
        3. Login ZCC.
        4. Logout ZCC.
        """
      
        result = conftest.ma_service_entitlement.toggle_zpa(action=True)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zdx(action=False)
        assert result[0], result[1]

        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zia_saml_login=True,
            aup=True
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in()
        assert result[0],result[1]

        result = conftest.zcc.logout()
        assert result[0], result[1]
        
    @pytest.mark.xray(["QA-293911"]) 
    @add_markers("regression","sanity") 
    @allure.title('iOS Test SAML based auth ( ZIA & ZPA ),Verify ZCC SAML based Auth for ZIA + ZPA + ZDX company with OKTA IDP WITH AUP')
    @zcc_mark_version(3.7)
    def test_zia_saml_zpa_saml_with_zdx_with_aup(self):
        """
        1. Enable ZPA from MA.
        2. Enable ZDX from MA.
        3. Login ZCC.
        4. Logout ZCC.
        """

        result = conftest.ma_service_entitlement.toggle_zpa(action=True)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zdx(action=True)
        assert result[0], result[1]

        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zia_saml_login=True,
            aup=True
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in()
        assert result[0],result[1]

        result = conftest.zcc.logout()
        assert result[0], result[1]
    

    @pytest.mark.xray("QA-293912") 
    @add_markers("regression","sanity") 
    @allure.title('Verify ZCC SAML based Auth for ZIA only company WITH AUP')
    @zcc_mark_version(3.7)
    def test_zia_saml_zpa_off_zdx_off_with_aup(self):
        """
        1. Disable ZPA from MA.
        2. Disable ZDX from MA.
        3. Login ZCC.
        4. Logout ZCC.
        """

        result = conftest.ma_service_entitlement.toggle_zpa(action=False)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zdx(action=False)
        assert result[0], result[1]

        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zia_saml_login=True,
            aup=True
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in()
        assert result[0],result[1]

        result = conftest.zcc.logout()
        assert result[0], result[1]

    @pytest.mark.xray("QA-293913") 
    @add_markers("regression","sanity") 
    @allure.title('Verify ZCC form based Auth for ZIA + ZDX company')
    @zcc_mark_version(3.7)
    def test_zia_saml_zpa_off_with_zdx_with_aup(self):
        """
        1. Disable ZPA from MA.
        2. Login ZCC.
        3. Logout ZCC.
        """
      
        result = conftest.ma_service_entitlement.toggle_zpa(action=False)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zdx(action=True)
        assert result[0], result[1]

        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zia_saml_login=True,
            aup=True
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in()
        assert result[0],result[1]

        result = conftest.zcc.logout()
        assert result[0], result[1]

    @allure.title('Teardown') 
    def teardown_class(self):
        """
        1. Change back the changes made for AUP.
        2. Toggle Off SAML auth for ZIA from MA.
        """
        conftest.ma_notifications.configure_aup(aup_frequency="never")

        conftest.saml_zia.saml_action_smui(mode="disable")
    

    



       




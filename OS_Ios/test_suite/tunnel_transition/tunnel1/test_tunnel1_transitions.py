import allure, pytest, time
# from OS_Ios.test_suite.zia.tunnel_1 import conftest
from OS_Ios.test_suite.tunnel_transition.tunnel1 import conftest
from common_lib.Custom_Markers import add_markers, zcc_mark_version

class TestTunnelTransitionsTunnel1:

    @allure.title('Setup')
    def setup_class(self):
        """
        Delete app profile
        Delete forwarding profile
        create forwarding profile
        create app profile
        Enabling SAML based auth on SMUI
        
        Disable Browser based auth.
        """

        conftest.ma_app_profile.delete_app_profile()

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()

        result = conftest.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        result=conftest.saml_zia_obj.saml_action_smui(mode="enable")
        assert result[0],result[1]

        result = conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]

        result = conftest.ma_app_profile.edit_app_profile(use_tunnel_sdk_version_43=True)
        assert result[0], result[1]

        result=conftest.ma_service_entitlement.toggle_zia(action=True)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zpa(action=True)
        assert result[0], result[1]

        result = conftest.zcc.login(
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
            zpa_user=conftest.config["ZPA_USER_ID"],
            zpa_password=conftest.config["ZPA_USER_PASSWORD"],
            zia_saml_login=True
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in(check_alerts_after_login=True, dynamic_wait_search_element=40)
        assert result[0], result[1]


    @allure.title("Test Tunnel transition between Tun1.0 -> Tunnel2 TLS")
    @pytest.mark.xray('QA-318835')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.3)
    def test_tunnel1_to_tunnel2_tls(self):
        """
        Steps:
        1. Set the tunnel mode to tunnel 1.
        2. Ignore the tunnel logs before this time stamp.
        3. Update Policy in ZCC.
        4. Validate the tunnel mode as tunnel 1.
        5. Set the tunnel mode to tunnel 2 tls.
        6. Ignore the tunnel logs before this time stamp.
        7. Update Policy in ZCC.
        8. Validate the tunnel mode as tunnel 2 tls.
        """
        res = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zcc.validate_tunnel_version(expected_version="v1.0", sdk=False, tab="zia")
        assert result[0], result[1]

        conftest.ma_trusted_network.delete_trusted_network(
            network_name=conftest.TRUSTED_NETWORK_NAME
        )

        res = conftest.ma_trusted_network.create_trusted_network(
            criteria_name=conftest.TRUSTED_NETWORK_NAME,
            hostname=conftest.NORMAL_DOMAIN,
            resolved_ips_for_hostname_list = conftest.NORMAL_DOMAIN_IP
        )
        assert res[0], res[1]

        
        res = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(
            tunnel_mode={'offTrusted':'tunnel_1','onTrusted':'tunnel_2_tls'},
            trusted_network="predefined",
            specific_predefined_trusted_networks_to_apply = [conftest.TRUSTED_NETWORK_NAME]
        )
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zcc.validate_tunnel_version(expected_version="v2.0 - TLS", sdk=False, tab="zia")
        assert result[0], result[1]


    @allure.title("Test Tunnel transition between Tun1.0 -> TWLP")
    @pytest.mark.xray('QA-318836')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.3)
    def test_tunnel1_to_twlp(self):
        """
        Steps:
        1. Set the tunnel mode to tunnel 1.
        2. Ignore the tunnel logs before this time stamp.
        3. Update Policy in ZCC.
        4. Validate the tunnel mode as tunnel 1.
        5. Set the tunnel mode to twlp.
        6. Ignore the tunnel logs before this time stamp.
        7. Update Policy in ZCC.
        8. Validate the tunnel mode as twlp.
        """
        res = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert res[0], res[1]

        conftest.zia_sanity.log_ops.ignore_logs_before_this_time_point()

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zcc.validate_tunnel_version(expected_version="v1.0", sdk=False, tab="zia")
        assert result[0], result[1]

        conftest.ma_trusted_network.delete_trusted_network(
            network_name=conftest.TRUSTED_NETWORK_NAME
        )
 
        res = conftest.ma_trusted_network.create_trusted_network(
            criteria_name=conftest.TRUSTED_NETWORK_NAME,
            hostname=conftest.NORMAL_DOMAIN,
            resolved_ips_for_hostname_list = conftest.NORMAL_DOMAIN_IP
        )
        assert res[0], res[1]

        res = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(
            tunnel_mode={'offTrusted':'tunnel_1','onTrusted':'twlp'},
            trusted_network="predefined",
            specific_predefined_trusted_networks_to_apply = [conftest.TRUSTED_NETWORK_NAME]
        )
        assert res[0], res[1]

        conftest.zia_sanity.log_ops.ignore_logs_before_this_time_point()

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        res = conftest.zcc.export_logs(
            airdrop = True,
            airdrop_to = conftest.config['MAC_HOSTNAME']
        )

        time.sleep(10)

        res = conftest.zia_sanity.validate_tunnel_mode_from_logs(tunnel_version='twlp')
        assert res[0], res[1]


    @allure.title("Test Tunnel transition between Tun1.0 -> Tunnel2 DTLS")
    @pytest.mark.xray('QA-318837')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.3)
    def test_tunnel1_to_tunnel2_dtls(self):
        """
        Steps:
        1. Set the tunnel mode to tunnel 1.
        2. Ignore the tunnel logs before this time stamp.
        3. Update Policy in ZCC.
        4. Validate the tunnel mode as tunnel 1.
        5. Set the tunnel mode to tunnel2 dtls.
        6. Ignore the tunnel logs before this time stamp.
        7. Update Policy in ZCC.
        8. Validate the tunnel mode as TLS.
        """
        res = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        result = conftest.zcc.validate_tunnel_version(expected_version="v1.0", sdk=False, tab="zia")
        assert result[0], result[1]

        conftest.ma_trusted_network.delete_trusted_network(
            network_name=conftest.TRUSTED_NETWORK_NAME
        )
 
        res = conftest.ma_trusted_network.create_trusted_network(
            criteria_name=conftest.TRUSTED_NETWORK_NAME,
            hostname=conftest.NORMAL_DOMAIN,
            resolved_ips_for_hostname_list = conftest.NORMAL_DOMAIN_IP
        )
        assert res[0], res[1]

        res = conftest.ma_app_profile.forwarding_profile.edit_forwarding_profile(
            tunnel_mode={'offTrusted':'tunnel_1','onTrusted':'tunnel_2_dtls'},
            trusted_network="predefined",
            specific_predefined_trusted_networks_to_apply = [conftest.TRUSTED_NETWORK_NAME]
        )
        assert res[0], res[1]

        res = conftest.zcc.update_policy()
        assert res[0], res[1]

        time.sleep(10)

        result = conftest.zcc.validate_tunnel_version(expected_version="v2.0 - TLS", sdk=False, tab="zia")
        assert result[0], result[1]

    @allure.title('Teardown')
    def teardown_class(self):
        """

        1. delete the trusted network created 
        2. disabling SAML based auth on SMUI
        3. Logout ZCC
        """

        conftest.ma_app_profile.delete_app_profile()

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()

        conftest.ma_trusted_network.delete_trusted_network(
            network_name=conftest.TRUSTED_NETWORK_NAME
        )

        result=conftest.saml_zia_obj.saml_action_smui(mode="disable")
        assert result[0],result[1]

        result = conftest.zcc.logout()
        assert result[0], result[1]
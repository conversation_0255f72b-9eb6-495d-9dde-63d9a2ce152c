from common_lib.adminzpa import zpapolicy
from common_lib.adminzia.samlzia import SamlZia
from common_lib.common.logger import Logger
from OS_Ios.library.ops_packet import OpsPacket
from OS_Ios.library.ops_system import SysOps
from OS_Ios.module.packet_analyzer import PacketAnalyzer
from OS_Ios.module import zia_sanity
import os, json, pytest
from common_lib.common import logger
from OS_Mac.module.zpa_traffic_verification_enhanced import ZpaTrafficTest
from common_lib.mobileadmin.platformsettings import PlatformSettings
from OS_Ios.library.ui_zcc import Zcc 
from common_lib.mobileadmin import appprofile,serviceentitlement, deviceoverview, trustednetwork
from common_lib.mobileadmin import clientconnectornotifications,clientconnectorsupport


ios_udid = os.environ["ios_udid"]
PKT_CAPTURE_DIR = os.path.join(os.getcwd(),'OS_Ios','packet_capture')
pkt_capture_output_file = os.path.join(os.getcwd(),'OS_Ios','packet_capture','iOS_Packets.pcapng')

def delete_pcap_file(path:str):
    if os.path.exists(path=path):
        os.remove(path=path)
def delete_pcap_folder(folder_path_respective_cwd:str):
    if os.path.exists(folder_path_respective_cwd):
        files_within = os.listdir(folder_path_respective_cwd)
        for each_file in files_within:
            each_file_path = os.path.join(folder_path_respective_cwd,each_file)
            delete_pcap_file(path=each_file_path)
        os.rmdir(folder_path_respective_cwd)


try:
    if os.path.exists(PKT_CAPTURE_DIR):
        pass
    else:
        os.mkdir(PKT_CAPTURE_DIR)

    config_file = os.path.join(os.getcwd(), "config", os.environ["CONFIG_FILE"])
    config = {}
    with open(config_file) as json_file:
        config.update(json.load(json_file))
    cloud_name = config["CLOUD"]
    log = Logger.initialize_logger(log_file_name="tunnel_1",log_level="INFO")

    zia_sanity = zia_sanity.ZiaSanity(cloud=config["CLOUD"], config=config_file)
    zcc = zia_sanity.zcc
    sys_ops = zia_sanity.sys_ops

    TRUSTED_NETWORK_NAME = "tunnel_sanity_tn"


    NORMAL_DOMAIN = "iana.org"
    NORMAL_DOMAIN_URL = "https://www.iana.org"
    NORMAL_DOMAIN_FILTER = "iana"
    NORMAL_DOMAIN_IP = sys_ops.fetch_all_resolved_ips(application=NORMAL_DOMAIN)[-1]

    NORMAL_DOMAIN_IP_URL = "https://**********"

    BYPASS_DOMAIN_URL = "https://example.com"
    BYPASS_DOMAIN = "example.com"
    BYPASS_DOMAIN_FILTER = "example"

    BYPASS_IP = sys_ops.fetch_all_resolved_ips(application=BYPASS_DOMAIN)[-1]
    BYPASS_IP_STRING = ','.join(BYPASS_IP)
    BYPASS_IP_SUBNET_LIST = list(set([each_ip.split(".")[0]+".0.0.0/8" for each_ip in BYPASS_IP]))
    BYPASS_IP_SUBNET_STRING = ','.join(BYPASS_IP_SUBNET_LIST)

    pkt_analyze = PacketAnalyzer(driver=zcc.ui.driver)
    ops_packet = OpsPacket()
    zpa_traffic_test = ZpaTrafficTest()

    zpa_policy = zpapolicy.ZpaPolicy(cloud_name, config_file, log_handle=log)
    ma_app_profile = appprofile.AppProfile(cloud_name, config_file, log_handle=log)
    ma_service_entitlement = serviceentitlement.ServiceEntitlement(cloud_name, config_file, log_handle=log)
    ma_notifications = clientconnectornotifications.ClientConnectorNotifications(cloud_name, config_file, log_handle=log)
    ma_support = clientconnectorsupport.ClientConnectorSupport(cloud_name, config_file, log_handle=log)
    ma_device_overview = deviceoverview.DeviceOverview(cloud_name, config_file, log_handle=log)
    platform_settings_obj = PlatformSettings(cloud=config['CLOUD'], config=config_file, log_handle=log)
    saml_zia_obj = SamlZia(cloud=config['CLOUD'], config=config_file, log_handle=log, debug=True)
    ma_trusted_network = trustednetwork.TrustedNetwork(cloud_name,config_file, log_handle=log)
    
    ios_interface = ops_packet.activate_device(udid=os.environ["ios_udid"])
    pkt_capture_interface = [ios_interface]
    pkt_capture_duration = 50
   
    log.info("\n\n\nFinished setup in conftest proceeding to test cases\n\n\n")
except Exception as e:
    pytest.skip(f"Skipping as Error :: {e}")


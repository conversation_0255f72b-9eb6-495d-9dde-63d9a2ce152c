import allure
import pytest
# from OS_Ios.test_suite.zia.tunnel_2 import conftest
from OS_Ios.test_suite.tunnel_transition.tunnel2 import conftest
from common_lib.Custom_Markers import add_markers, zcc_mark_version
"""
    This is class created for running tests Tunnel Sanity.
    1. Testcase 1 verifies transition from tunnel 2 tls to twlp.
    2. Testcase 2 verifies transition from tunnel 2 tls to tunnel 1.
    3. Testcase 3 verifies transition from tunnel 2 tls to tunnel 2 DTLS leads to fallback to Tunnel 2 TLS.
"""

class TestT2Sanity:
    @allure.title('Setup')
    def setup_class(self):
        """
        1. Fetching DNS server IP and application resolved IP by running and capturing packets when ZCC is not logged in.
        2. Deleting app profile and forwarding profile and creating a new app profile and forwarding profile.
        3. Editing the forwarding profile to use Tunnel 2.
        4. Editing the app profile to toggle tunnel sdk 4.3 .
        5. Configuring aup.
        6. Disabling browser based auth.
        7. Disabling SAML to use form based login instead of SAML based.
        8. Toggling ZIA to True.
        9. Toggling ZPA to False.
        10. Toggling ZDX to False.
        11. Doing the Form based login with the credentials in config file.
        12. Validating ZCC has been logged in or not.
        """
        conftest.sys.delete_pcap_file(path=conftest.PKT_FILE_URL)

        conftest.zcc.bring_zcc_to_focus()
        if conftest.zcc.ui.check_if_exists(conftest.zcc.elements.LOGOUT_BUTTON, dynamic_wait_search_element=5):
            conftest.zcc.logout()

        result1 = conftest.zia_sanity.run_packet_capture_and_access_application(output_file=conftest.PKT_FILE_URL, capture_duration=conftest.DURATION, browser=conftest.BROWSER_NAME, url=conftest.URLV4, ios_udid=conftest.IDENTIFIER)
        

        if result1[0]:
            result = conftest.sys.fetch_application_resolved_ip(pcap_file_url=conftest.PKT_FILE_URL, domain=conftest.DOMAINV4_FILTER, application=conftest.DOMAINV4)

            if not result[0]:
                conftest.logger.error(f"Critical setup error: {result[1]}")
                assert result[0], result[1]
            else:
                conftest.NORMAL_IP = list(result[2])
                conftest.logger.info(f"Normal IP list is:{conftest.NORMAL_IP}")

        else:
            conftest.logger.error(f"Critical setup error: {result1[1]}")
            assert result1[0], result1[1]

        conftest.ma_app_profile.delete_app_profile()

        conftest.ma_app_profile.forwarding_profile.delete_forwarding_profile()

        result = conftest.ma_app_profile.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.create_app_profile()
        assert result[0], result[1]

        result = conftest.ma_app_profile.edit_app_profile(use_tunnel_sdk_version_43=True)
        assert result[0], result[1]

        result=conftest.ma_notifications.configure_aup()
        assert result[0], result[1]

        result=conftest.platform_settings_obj.browser_based_auth_action_ma(toggle="disable")
        assert result[0], result[1]

        result=conftest.saml_zia_obj.saml_action_smui(mode="disable")
        assert result[0],result[1]

        res = conftest.zia_sanity.ma_trusted_network.create_trusted_network(
            criteria_name=conftest.TRUSTED_NETWORK_NAME,
            hostname=conftest.DOMAINV4,
            resolved_ips_for_hostname_list=conftest.NORMAL_IP
        )
        assert res[0], res[1]

        result=conftest.ma_service_entitlement.toggle_zia(action=True)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zpa(action=False)
        assert result[0], result[1]

        result = conftest.ma_service_entitlement.toggle_zdx(action=False)
        assert result[0], result[1]

        result = conftest.zcc.login(
            aup=False,
            zia_user=conftest.config["ZIA_USER_ID"],
            zia_password=conftest.config["ZIA_USER_PASSWORD"],
        )
        assert result[0], result[1]

        result=conftest.zcc.validate_zcc_logged_in(check_alerts_after_login=True, dynamic_wait_search_element=40)
        assert result[0], result[1]

    @allure.title("Test Tunnel transition between Tunnel2 TLS to twlp")
    @pytest.mark.xray('QA-351614')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_01_tunnel2_tls_to_twlp(self):
        """
        Steps:
        1. Set the tunnel mode to tunnel 2 tls.
        2. Update Policy in ZCC.
        3. Validate the tunnel mode as tunnel 2 tls.
        4. Set the tunnel mode to twlp.
        5. Update Policy in ZCC.
        6. Validate the tunnel mode as twlp.
        """
        result=conftest.zia_sanity.ma_app_profile.forwarding_profile.edit_forwarding_profile(
            tunnel_mode="tunnel_2_tls"
        )
        assert result[0],result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result = conftest.zcc.validate_tunnel_version(expected_version="v2.0 - TLS")
        assert result[0], result[1]

        result=conftest.zia_sanity.ma_app_profile.forwarding_profile.edit_forwarding_profile(
            tunnel_mode={'offTrusted':'tunnel_2_tls','onTrusted':'twlp'},
            trusted_network="predefined",
            specific_predefined_trusted_networks_to_apply=[conftest.TRUSTED_NETWORK_NAME]
        )
        assert result[0],result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result=conftest.zcc.export_logs(airdrop=True, airdrop_to=conftest.config["MAC_HOSTNAME"])
        assert result[0], result[1]

        result=conftest.zia_sanity.validate_tunnel_mode_from_logs(
            tunnel_version="twlp"
        )
        assert result[0],result[1]

    @allure.title("Test Tunnel transition between Tunnel2 TLS to Tunnel1")
    @pytest.mark.xray('QA-351615')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_02_tunnel2_tls_to_tunnel1(self):
        """
        Steps:
        1. Set the tunnel mode to tunnel 2 tls.
        2. Update Policy in ZCC.
        3. Validate the tunnel mode as tunnel 2 tls.
        4. Set the tunnel mode to tunnel 1.
        5. Update Policy in ZCC.
        6. Validate the tunnel mode as tunnel 1.
        """
        result=conftest.zia_sanity.ma_app_profile.forwarding_profile.edit_forwarding_profile(
            tunnel_mode="tunnel_2_tls"
        )
        assert result[0],result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result=conftest.zcc.validate_tunnel_version(expected_version="v2.0 - TLS")
        assert result[0],result[1]

        result=conftest.zia_sanity.ma_app_profile.forwarding_profile.edit_forwarding_profile(
            tunnel_mode={'offTrusted':'tunnel_2_tls','onTrusted':'tunnel_1'},
            trusted_network="predefined",
            specific_predefined_trusted_networks_to_apply=[conftest.TRUSTED_NETWORK_NAME]
        )
        assert result[0],result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result = conftest.zcc.validate_tunnel_version(expected_version="v1.0")
        assert result[0], result[1]
        
        domainUrl, iOS_id, pkt_file_url, domainv4_filter = conftest.URLV4, conftest.IDENTIFIER, conftest.PKT_FILE_URL, conftest.DOMAINV4_FILTER

        result = conftest.zia_sanity.analyze_packets(pkt_file_url=pkt_file_url, ios_udid=iOS_id, urlv4=domainUrl, domainv4_filter=domainv4_filter)
        assert result[0], result[1]

    @allure.title("Test Tunnel transition between Tunnel2 TLS to Tunnel2 DTLS")
    @pytest.mark.xray('QA-351616')
    @add_markers("regression","sanity")
    @zcc_mark_version(4.1)
    def test_03_tunnel2_tls_to_tunnel2_dtls(self):
        """
        Steps:
        1. Set the tunnel mode to tunnel 2 tls.
        2. Ignore the tunnel logs before this time stamp.
        3. Update Policy in ZCC.
        4. Validate the tunnel mode as tunnel 2 tls.
        5. Set the tunnel mode to tunnel 2 tls.
        6. Ignore the tunnel logs before this time stamp.
        7. Update Policy in ZCC.
        8. Validate the tunnel mode as tunnel 2 tls.
        """
        result=conftest.zia_sanity.ma_app_profile.forwarding_profile.edit_forwarding_profile(
            tunnel_mode="tunnel_2_tls"
        )
        assert result[0],result[1]

        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result=conftest.zcc.validate_tunnel_version(expected_version="v2.0 - TLS")
        assert result[0],result[1]

        result=conftest.zia_sanity.ma_app_profile.forwarding_profile.edit_forwarding_profile(
            tunnel_mode={'offTrusted':'tunnel_2_tls','onTrusted':'tunnel_2_dtls'},
            trusted_network="predefined",
            specific_predefined_trusted_networks_to_apply=[conftest.TRUSTED_NETWORK_NAME]
        )
        assert result[0],result[1]
        
        result = conftest.zcc.update_policy()
        assert result[0], result[1]

        result = conftest.zcc.validate_tunnel_version(expected_version="v2.0 - TLS")
        assert result[0], result[1]

    @allure.title('Teardown') 
    def teardown_class(self):
        """
        1. Change back the changes made for AUP.
        2. Toggle Off SAMl auth for ZIA from MA.
        3. Deleting the pcap folder after all the test cases has been ran.
        4. Delete the trusted network from MA.
        5. logged out of zcc.
        """
        conftest.ma_notifications.configure_aup(aup_frequency="never")

        conftest.saml_zia_obj.saml_action_smui(mode="disable")

        conftest.sys.delete_pcap_folder(folder_path_respective_cwd=conftest.PKT_CAPTURE_DIR)

        conftest.zia_sanity.ma_trusted_network.delete_trusted_network(
            network_name=conftest.TRUSTED_NETWORK_NAME
        )

        conftest.zia_sanity.zia_pac.delete_pac_file(name=conftest.SE_PAC_FILE_NAME)

        conftest.zcc.logout()
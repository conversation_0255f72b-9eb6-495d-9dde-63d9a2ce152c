import requests,platform,socket
import sys,os,json,time,datetime
import logging
from zdx.Lib.common_lib import zdx_res
#import zdx_res
requests.packages.urllib3.disable_warnings() 
import copy

class ZDX_ADMIN:
    def __init__(self, cloudname, configfile, logger=None,Variable=None):
        self.var = Variable if Variable else None
        self.cloudname = cloudname
        self.logger = logger if logger != None else self.Initialize_Logger("ZDX_ADMIN.log", Log_Level="INFO")
        self.logger.info("Intializing ZDX-Admin api module"); self.logger.info(f"ZDX Cloudname / IP : {self.cloudname}");self.logger.info("Fetching credentials from config file")
        if type(configfile) == dict : self.config = configfile; user = copy.deepcopy(self.config["ZIA_USER_ID"]); self.config = copy.deepcopy(self.config['ZDX_ADMIN']); self.config['ZDX_USERNAME'] = user; del(user)
        else:self.get_config(configfilename=configfile)
        self.resource = zdx_res.ZDX_RES(cloudname=self.cloudname, Variable = Variable)
        if not os.path.exists(self.resource.authtokenPath): self.logger.warning("Token directory does not exists. Creating one..."); os.makedirs(self.resource.authtokenPath)
        self.session = requests.Session()
        self.session.verify= False
        self.applicationdata = {}
        self.dtsessionsinfo = {}
        
    def Initialize_Logger(self,
                          Log_File_Name,  # (str)     Name of the Log file to be created
                          Log_Level,  # (str)     Level of logging (INFO/DEBUG/WARNING/CRICITCAL etc)
                          console_handle_needed=True  # (bool)    print_ on command line if True only
                          ):
        """
        Target: Create Logger object.
        Work Flow
        ----------
            1. Create a logger object.
            2. Create a console handler if required
                - console handler is used to print_ required info on command line window.
            3. Create a file handler -> mandatory
                - Make sure 'Logs' folder exists, as this file handler will be created in this folder only.
                - file handler is used to send all information to a log file in specified folder.
            4. Set the debug level (INFO/DEBUG/WARNING/CRITICAL etc). Default: INFO
            5. Specify how the logging must happen (with/without datetime/loglevel/username)
            6. Add all created handlers to the logger instance created in step 1.
            7. return the logger instance.
        """
        # 1, 2 & 3
        self.Logs_Directory = os.path.join(self.var.automationlogslocation, 'Logs') if self.var else os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "Logs")
        logger = logging.getLogger(Log_File_Name)
        if console_handle_needed: console_handler = logging.StreamHandler()
        if not os.path.exists(self.Logs_Directory): os.mkdir(self.Logs_Directory)
        file_handler = logging.FileHandler(os.path.join(self.Logs_Directory, Log_File_Name))

        # 4
        if Log_Level:
            logger.setLevel(eval("logging.{}".format(Log_Level)))
            if console_handle_needed: console_handler.setLevel(eval("logging.{}".format(Log_Level)))
            file_handler.setLevel(eval("logging.{}".format(Log_Level)))

        # 5
        if console_handle_needed: console_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))

        # 6
        if console_handle_needed: logger.addHandler(console_handler)
        logger.addHandler(file_handler)
        # 7
        return logger

    def get_config(self, configfilename):
        self.config = None
        configdir = zdx_res.gparent
        configdir = os.path.join(zdx_res.gparent, "Config", configfilename)
        if os.path.exists(configdir):
            with open(configdir) as config_file:
                self.config = json.load(config_file)
                user = copy.deepcopy(self.config["ZIA_USER_ID"])
                self.config = copy.deepcopy(self.config['ZDX_ADMIN'])
                self.config['ZDX_USERNAME'] = user
                del(user)
                # self.config['username'] = temp["ZDX_ADMIN_CRED"]['ZDX_ADMIN_ID']
                # self.config['password'] = temp["ZDX_ADMIN_CRED"]['ZDX_ADMIN_PASSWORD']
        else:
            self.logger.error("Config file does not exist or invalid path specified")
    
    def REQ(self, reqtype, url, data = None, params = None, headers=None,retry=False):
        assert reqtype in ["get", "post", "delete", "put"]
        response= None
        try:
            if reqtype == "get":
                response = self.session.get(url=url, headers=headers, json=data, params=params)
            if reqtype == "post":
                response = self.session.post(url=url, headers=headers, json=data, params=params)
            if reqtype == "delete":
                response = self.session.delete(url, headers=headers, json=data, params=params)
            if reqtype == "put":
                response = self.session.put(url, headers=headers, json=data, params=params)
            
            if str(response.status_code) >= str(200) and str(response.status_code)<=str(300):
                self.logger.debug(f'Success - {response.status_code}')
            else: raise Exception(response)
        except Exception as E:
            if retry: raise Exception(f'failed - {response.status_code} - {response.text}\n url: {url}\nHeaders: {headers}\ndata: {data}')
            try:
                self.session.headers.update(self.resource.authheader)
                if not self.session.verify and os.path.exists(self.resource.authtokenFile):
                    with open(self.resource.authtokenFile, "r") as f:
                        new_data= f.read().split(";")
                        self.session.headers['X-Csrf-Token'] = new_data[0]
                        self.session.cookies.set(**{"name":"JSESSIONID", "value":new_data[1]})
                        response = self.session.get(url=self.resource.authurl)
                        self.session.verify=True
                if response is None: self.logger.critical(f"Encountered error {E}\nResponse: {response}\nRetrying after 30 seconds..."); time.sleep(30)
                if response is None or str(response.status_code) != str(200):
                    self.session.close()
                    self.session = requests.Session()
                    self.session.headers.update(self.resource.authheader)
                    response = self.session.get(self.resource.authurl)
                    self.session.headers['X-Csrf-Token'] = response.headers.get('X-Csrf-Token')
                    self.logger.warning("Resetting the session to cloud {}".format(self.cloudname))
                    self.__login()
                else: self.logger.info(f"Using existing session for user {self.config['ZDX_ADMIN_ID']}")
                return self.REQ(reqtype=reqtype,url=url,data=data,params=params,headers=headers,retry=True)
            except Exception as E:
                raise Exception (E)
        try:
            info = info = json.loads(response.text)
        except Exception as E:
            self.logger.debug("Response seems to be in html format which is not useful. Ignoring this response.")
            self.logger.debug(response.text)
            return None
        
        return info
    
    def __login(self):
        response = self.session.post(url = self.resource.authurl, data = {"username": self.config['ZDX_ADMIN_ID'], "password":self.config['ZDX_ADMIN_PASSWORD']})
        if str(response.status_code) !=str(200):
            self.logger.error(f"Unable to login specified user {self.config['ZDX_ADMIN_ID']} into ZDX Cloud {self.cloudname}\nResponse Status Code : {response.status_code}")
            raise Exception(f"Unable to login specified user {self.config['ZDX_ADMIN_ID']} into ZDX Cloud {self.cloudname}")
        with open(self.resource.authtokenFile, 'w', encoding="utf-8") as f:
            data = ';'.join([self.session.headers['X-Csrf-Token'], response.cookies.get('JSESSIONID')])
            f.write(data)
    
    def logout(self):
        response = self.session.delete(url= self.resource.authurl)
        if str(response.status_code) != str(200):
            self.logger.error(f"Unable to logout \n {response.text}")
        self.session.close()
    
    def activate(self):
        response = self.REQ(reqtype="put", url = self.resource.activateurl, data = json.dumps({}))
        if not response['status'] == "active": raise Exception("Could not activate status from pending to active")
        self.logger.info("Activated the pending changes")

    def list_all_users(self):
        response = self.REQ(reqtype="get", url = self.resource.allusersurl)
        return response
    
    def list_all_devices(self, userid): #userid should be string
        response = self.REQ(reqtype="get", url=self.resource.devicesurl.format(userid))
        return response
    
    def delete_application_and_monitors(self, configs=None):
        if configs == None:
            configs = self.config['ZDX_APPLICATION']
        response = self.REQ(reqtype="get", url=self.resource.applicationurl)
        ListIssues = []
        app = None
        for config in configs:
            if config['application']['name'] in self.applicationdata:app = self.applicationdata[config['application']['name']]
            else:
                app = {"appid": None, "WEB":{}, "TCP":{}, "UDP":{}, "ICMP": {}, "ADAPTIVE": {}}
                for application in response: 
                    if application['name'].lower() == config['application']['name'].lower() :app['appid'] = application['id']; break
                if app['appid'] == None:self.logger.warning(f"Cannot delete application {config['application']['name']} as it does not exist");continue
                response = self.REQ(reqtype="get", url = self.resource.listallmonitorsurl)
                for probes in response:
                    if probes['application']['id'] == app['appid'] and probes['type'] == "WEB": app['WEB']['id'] = probes['id']; app['WEB']['name'] = probes['name']
                    if probes['application']['id'] == app['appid'] and probes['type'] == "TRACERT" and probes['protocol'] == "TCP": app['TCP']['id']= probes['id'];app['TCP']['name'] = probes['name']
                    if probes['application']['id'] == app['appid'] and probes['type'] == "TRACERT" and probes['protocol'] == "UDP": app['UDP']['id']= probes['id'];app['UDP']['name'] = probes['name']
                    if probes['application']['id'] == app['appid'] and probes['type'] == "TRACERT" and probes['protocol'] == "ICMP": app['ICMP']['id']= probes['id'];app['ICMP']['name'] = probes['name']
                    if probes['application']['id'] == app['appid'] and probes['type'] == "TRACERT" and probes['protocol'] == "ADAPTIVE": app['ADAPTIVE']['id']= probes['id'];app['ADAPTIVE']['name'] = probes['name']
            try:
                if app['ADAPTIVE']: self.REQ(reqtype="delete", url = self.resource.monitorurl.format(str(app['appid']))+"/"+str(app['ADAPTIVE']['id'])); self.logger.info(f"Successfully deleted ADAPTIVE monitor with id {app['ADAPTIVE']}")
                if app['ICMP']: self.REQ(reqtype="delete", url = self.resource.monitorurl.format(str(app['appid']))+"/"+str(app['ICMP']['id'])); self.logger.info(f"Successfully deleted ICMP monitor with id {app['ICMP']}")
                if app['UDP']: self.REQ(reqtype="delete", url = self.resource.monitorurl.format(str(app['appid']))+"/"+str(app['UDP']['id'])); self.logger.info(f"Successfully deleted UDP monitor with id {app['UDP']}")
                if app['TCP']: self.REQ(reqtype="delete", url = self.resource.monitorurl.format(str(app['appid']))+"/"+str(app['TCP']['id'])); self.logger.info(f"Successfully deleted TCP monitor with id {app['TCP']}")
                if app['WEB']:
                    payload = copy.deepcopy(self.resource.disablewebprobe)
                    payload['id'] = app['WEB']['id']; payload['name'] = app['WEB']['name']; payload['url'] = config['web_probe']['url']
                    self.REQ(reqtype="put", url=self.resource.monitorurl.format(str(app['appid']))+"/"+str(app['WEB']['id']), data = payload)
                    self.logger.info(f"Successfully disabled Web probe {config['web_probe']['name']}")
                    self.REQ(reqtype="delete", url=self.resource.monitorurl.format(str(app['appid']))+"/"+str(app['WEB']['id']))
                    self.logger.info(f"Successfully deleted Web probe {config['web_probe']['name']}")
                    self.REQ(reqtype="delete", url=self.resource.applicationurl+"/"+str(app['appid']))
                    self.logger.info(f"Successfully deleted application {config['application']['name']}")
                if config['application']['name'] in self.applicationdata: self.applicationdata.pop(config['application']['name'])
            except Exception as E:
                ListIssues.append(E)
                self.logger.error(E)
        self.activate()
        if len(ListIssues) > 0:
            raise Exception(ListIssues)
        
        
    def create_application_and_monitors(self, configs=None):#this will create applications if not exist and the web and trcrt monitors if not exist
        if configs == None:
            configs = self.config['ZDX_APPLICATION']
        responses = self.REQ(reqtype="get", url=self.resource.applicationurl)
        for config in configs:
            probesexist = {"appid": None,"name": "", "WEB":{}, "TCP":{}, "UDP":{}, "ICMP": {}, "ADAPTIVE": {}}
            for application in responses:
                if application['name'].lower() == config['application']['name'].lower() :probesexist['appid'] = application['id'];probesexist['name']= application['name'];self.logger.info(f"Application {application['name']} already exist"); break
            if probesexist['appid'] == None:
                payload = copy.deepcopy(self.resource.createApppayload)
                for key in config['application'].keys():
                    payload[key] = config['application'][key]
                response = self.REQ(reqtype="post", url= self.resource.applicationurl, data=payload)
                self.logger.info(f"Application {config['application']['name']} created")
                probesexist['appid'] = response['id']
                probesexist['name'] = response['name']
            #print(type(appid))
            response = self.REQ(reqtype="get", url = self.resource.listallmonitorsurl)
            
            for probes in response:
                if probes['application']['id'] == probesexist['appid'] and probes['type'] == "WEB": probesexist['WEB']['id'] = probes['id']; probesexist['WEB']['name'] = probes['name']; self.logger.warning(f"Web probe {probes['name']} already exist with id {probes['id']}")
                if probes['application']['id'] == probesexist['appid'] and probes['type'] == "TRACERT" and probes['protocol'] == "TCP": probesexist['TCP']['id']= probes['id']; probesexist['TCP']['name'] = probes['name'];  self.logger.warning(f"Traceroute probe {probes['name']} with protocol {probes['protocol']} already exist with id {probes['id']}")
                if probes['application']['id'] == probesexist['appid'] and probes['type'] == "TRACERT" and probes['protocol'] == "UDP": probesexist['UDP']['id']= probes['id']; probesexist['UDP']['name'] = probes['name'];  self.logger.warning(f"Traceroute probe {probes['name']} with protocol {probes['protocol']} already exist with id {probes['id']}")
                if probes['application']['id'] == probesexist['appid'] and probes['type'] == "TRACERT" and probes['protocol'] == "ICMP":probesexist['ICMP']['id']=probes['id']; probesexist['ICMP']['name'] = probes['name']; self.logger.warning(f"Traceroute probe {probes['name']} with protocol {probes['protocol']} already exist with id {probes['id']}")
                if probes['application']['id'] == probesexist['appid'] and probes['type'] == "TRACERT" and probes['protocol'] == "ADAPTIVE":probesexist['ADAPTIVE']['id']=probes['id']; probesexist["ADAPTIVE"]['name'] = probes['name']; self.logger.warning(f"Traceroute probe {probes['name']} with protocol {probes['protocol']} already exist with id {probes['id']}")

            if not probesexist['WEB']:
                payload = copy.deepcopy(self.resource.webprobepayload)
                for key in config['web_probe'].keys():
                    payload[key] = config['web_probe'][key]
                response = self.REQ(reqtype="post", url = self.resource.monitorurl.format(str(probesexist['appid'])), data = payload)
                probesexist['WEB']['id'] = response['id']
                probesexist['WEB']['name']= response['name']
                self.logger.info(f"Web probe {response['name']} created successfully with id {response['id']}")
            if not probesexist['TCP'] and not config.get("tracert_tcp") == None:
                payload = copy.deepcopy(self.resource.tracertpayload)
                payload["followWebMonitor"] = probesexist['WEB']
                payload["tcpPort"]=443
                payload["protocol"]="TCP"
                for key in config["tracert_tcp"].keys():
                    payload[key] = config["tracert_tcp"][key]
                response = self.REQ(reqtype="post", url = self.resource.monitorurl.format(str(probesexist['appid'])), data = payload)
                probesexist['TCP']['id'] = response['id']
                probesexist['TCP']['name']= response['name']
                self.logger.info(f"Traceroute probe {response['name']} with protocol {response['protocol']} created successfully with id {response['id']}")
            if not probesexist['UDP'] and not config.get("tracert_udp") == None:
                payload = copy.deepcopy(self.resource.tracertpayload)
                payload["followWebMonitor"] = probesexist['WEB']
                payload["udpPort"]=33434
                payload["protocol"]="UDP"
                for key in config["tracert_udp"].keys():
                    payload[key] = config["tracert_udp"][key]
                response = self.REQ(reqtype="post", url = self.resource.monitorurl.format(str(probesexist['appid'])), data = payload)
                probesexist['UDP']['id'] = response['id']
                probesexist['UDP']['name']= response['name']
                self.logger.info(f"Traceroute probe {response['name']} with protocol {response['protocol']} created successfully with id {response['id']}")
            if not probesexist['ICMP'] and not config.get('tracert_icmp') == None:
                payload = copy.deepcopy(self.resource.tracertpayload)
                payload["followWebMonitor"] = probesexist['WEB']
                payload["protocol"]="ICMP"
                for key in config["tracert_icmp"].keys():
                    payload[key] = config["tracert_icmp"][key]
                response = self.REQ(reqtype="post", url = self.resource.monitorurl.format(str(probesexist['appid'])), data = payload)
                probesexist['ICMP']['id'] = response['id']
                probesexist['ICMP']['name']= response['name']
                self.logger.info(f"Traceroute probe {response['name']} with protocol {response['protocol']} created successfully with id {response['id']}")
            if not probesexist['ADAPTIVE'] and not config.get('tracert_adaptive') == None:
                payload = copy.deepcopy(self.resource.tracertpayload)
                payload["followWebMonitor"] = probesexist['WEB']
                payload["protocol"]='ADAPTIVE'
                for key in config["tracert_adaptive"].keys():
                    payload[key] = config["tracert_adaptive"][key]
                response = self.REQ(reqtype="post", url = self.resource.monitorurl.format(str(probesexist['appid'])), data = payload)
                probesexist['ADAPTIVE']['id'] = response['id']
                probesexist['ADAPTIVE']['name']= response['name']
                self.logger.info(f"Traceroute probe {response['name']} with protocol {response['protocol']} created successfully with id {response['id']}")
            self.applicationdata[config['application']['name']] = probesexist
            self.logger.debug(self.applicationdata)
        if len(self.applicationdata) > 0 :self.activate()

    def init_wait_DT(self):
        if not self.dtsessionsinfo: self.logger.info("No active DT session exist"); return
        self.logger.info("Initialising waiting period for ongoing DT Sessions")
        ListIssues = []
        while(True):
            response = self.REQ(reqtype="get", url=self.resource.dturl+self.resource.iscompletedparams)
            if len(response) > 0:
                for res in response:
                    if res['id'] in self.dtsessionsinfo:
                        self.logger.info(f"DT - {self.dtsessionsinfo[res['id']]['name']} completed successfully")
                        self.dtsessionsinfo.pop(res['id'])
            response = self.REQ(reqtype="get", url=self.resource.dturl+self.resource.isfailedparams)
            if len(response) > 0:
                for res in response:
                    if res['id'] in self.dtsessionsinfo:
                        self.logger.info(f"DT - {self.dtsessionsinfo[res['id']]['name']} failed")
                        ListIssues.append(f"DT - {self.dtsessionsinfo[res['id']]['name']} failed")
                        self.dtsessionsinfo.pop(res['id'])

            if ListIssues:
                return [False, None, ListIssues]
            
            if len(self.dtsessionsinfo) == 0:
                return [True, None, None]
            time.sleep(30)

    def create_dt_session(self, application="Google", configs=None):
        if configs == None:
            configs = self.config['ZDX_APPLICATION']
        ListIssues = []
        for config in configs:
            if config['application']['name'].lower() == application.lower() and not config.get("DT") == None:
                DTList = copy.deepcopy(config['DT'])
                for DT in DTList:
                    payload = {}
                    parameters=[]
                    if not DT.get("DT_PROTOCOL") == None and (not DT.get("BANDWIDTH_TEST") == None and DT["BANDWIDTH_TEST"]):self.logger.error(f"DT session - {DT['name']} cannot be performed in parallel with Bandwidth testing"); ListIssues.append(f"DT session - {DT['name']} cannot be performed in parallel with Bandwidth testing"); continue
                    if not DT.get("DT_PROTOCOL") == None and not config['application']['name'] in self.applicationdata: self.create_application_and_monitors(configs= list().append(config))
                    if not DT.get("DT_PROTOCOL") == None:
                        # response = self.REQ(reqtype="get", url = self.resource.monitorurl.format(str(self.applicationdata[config['application']['name']]['appid'])))
                        # #print(response)
                        dtapppayload = copy.deepcopy(self.resource.dtapppayload)
                        # dtapppayload['application']['id']= response[0]['application']['id']; dtapppayload['application']['name']= response[0]['application']['name']
                        temp = self.applicationdata[config['application']['name']]
                        dtapppayload['application']['id']= temp['appid']; dtapppayload['application']['name']= temp ['name']
                        for key in temp['WEB'].keys():
                            dtapppayload['monitors'][0][key] = temp['WEB'][key]
                        if temp.get(DT['DT_PROTOCOL'])== None or not temp[DT['DT_PROTOCOL']]: 
                            self.logger.error(f"Cannot create a DT session as type of DT_PROTOCOL {DT['DT_PROTOCOL']}(probe) does not exist for application {temp['name']}");ListIssues.append(f"Cannot create a DT session as type of DT_PROTOCOL {DT['DT_PROTOCOL']}(probe) does not exist for application {temp['name']}");continue
                        for key in temp[DT['DT_PROTOCOL']].keys():
                            dtapppayload['monitors'][1][key] = temp[DT['DT_PROTOCOL']][key]
                        parameters.append(dtapppayload)
                    if not DT.get("BANDWIDTH_TEST") == None and DT["BANDWIDTH_TEST"]:
                        parameters.append({"type": "BANDWIDTH_TEST"})
                    if not DT.get("PCAP") == None:
                        dtpcappayload = copy.deepcopy(self.resource.dtpcappayload)
                        for key in DT['PCAP'].keys():
                            dtpcappayload[key]= DT['PCAP'][key]
                        parameters.append(dtpcappayload)
                    if len(parameters) <=0: self.logger.error("No valid parameter input passed. Check if application/bandwidth/pcap input exist in configuration file"); continue
                    response = self.list_all_users()
                    for res in response:
                        if self.config['ZDX_USERNAME'] in res['email']:payload['user']={"id": res['id'], 'name': res['nameWithEmail']}; break
                    if payload.get("user") == None: self.logger.error(f"Could not find the user - {self.config['ZDX_USERNAME']} defined in config registered with zdx"); ListIssues.append(f"Could not find the user - {self.config['ZDX_USERNAME']} defined in config registered with zdx"); continue
                    response = self.list_all_devices(userid= str(payload['user']['id']))
                    if DT.get("name") == None or len(DT['name']) == 0:
                        DT['name'] = f"{DT['devicename'] if DT.get('devicename') else str(platform.node() if platform.node == socket.gethostname() else socket.gethostname())+'-'+str(datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S'))}"
                        if len(DT['name'])> 64:
                            DT['name'] = f"ZDX-Automation-{str(datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S'))}"
                    if DT.get("devicename") == None or len(DT['devicename']) == 0: 
                        self.logger.warning(f"Devicename does not exist for the DT session - {DT['name']}\nAutomation will provide hostname on which automation is running as devicename"); 
                        DT['devicename'] = platform.node() if platform.node() == socket.gethostname() else socket.gethostname()
                    
                    for res in response:
                        if DT['devicename'] in res['name']: payload['device'] = res; break
                    if payload.get("device") == None : self.logger.error(f"Device {DT['devicename']} is not registered"); ListIssues.append(f"Device {DT['devicename']} is not registered"); continue
                    payload["deviceFieldCleared"]= False; payload["durationMinutes"] = DT["durationMinutes"]; payload['name'] = DT['name']; payload['parameters'] = parameters
                    try: 
                        response = self.REQ(reqtype="post", url=self.resource.dturl, data=payload)
                        self.logger.info(f"Successfully Created DT session {response['name']} with id - {response['id']}")
                        self.dtsessionsinfo[response['id']] = response
                    except Exception as E:
                        self.logger.error(E)
                        ListIssues.append(E)

        if ListIssues:
            return [False, None, ListIssues]

        return [True, None, None]

    def get_ssit_notif_list(self, minutes: int):
        """
        This function return the notification list of a specific time range

        :param self: The object itself.
        :minutes: number of minutes
        :return: a list contain [status, message, response]
        """
        try:
            range = [[int(time.time()) - minutes * 60, int(time.time())]]
            self.resource.ssitpayload["filters"]["time"]["range"] = range
            response = self.REQ(reqtype="post", url=self.resource.ssitreporturl.format("SSIT_NOTIF_LIST"), data=self.resource.ssitpayload)
            return [True, None, response]
        except Exception as E:
            return [False, None, str(E)]

    def toggle_self_service(self, isEnable:bool, isEnablePushNotification:bool, isEnablePushNotificationByUser:bool):
        """
        This function Toggle Self Service

        :param self: The object itself.
        :isEnable: Boolean value to enable or disable ssit
        :isEnablePushNotification: Boolean value to enable or disable PushNotification
        :isEnablePushNotificationByUser: Boolean value to enable or disable PushNotificationByUser
        :return: a list contain [status, message, response]
        """
        try:
            response = self.REQ(reqtype="get", url = self.resource.selfserviceurl)
            response["enabled"] = isEnable
            response["enablePushNotification"] = isEnablePushNotification
            response["enablePushNotificationByUser"] = isEnablePushNotificationByUser
            self.REQ(reqtype="put", url=self.resource.selfserviceurl, data = response)
            self.activate()
            return [True, None, None]
        except Exception as E:
            return [False, None, str(E)]
    
    def toggle_collect_inventory_data(self, isEnable:bool,):
        """
        This function Toggle collect inventory data

        :param self: The object itself.
        :isEnable: Boolean value to enable or disable collect inventory data
        return: a list contain [status, message, response]
        """
        try:
            response = self.REQ(reqtype="get", url = self.resource.toggle_collect_inventory_data_url)
            response["enabled"] = isEnable
            self.REQ(reqtype="put", url=self.resource.toggle_collect_inventory_data_url, data = response)
            self.activate()
            return [True, None, None]
        except Exception as E:
            return [False, None, str(E)]


    def get_self_service_info(self):
        """
        This function get the SSIT information from the zdx admin cloud

        :param self: The object itself.
        :return: a list contain [status, message, response]
        """
        try:
            response = self.REQ(reqtype="get", url = self.resource.selfserviceurl)
            return [True, "", response]
        except Exception as E:
            return [False, None, str(E)]

if __name__ == "__main__":
    a = ZDX_ADMIN(cloudname="zdxbeta", configfile="zdx.json")
    #a.REQ(reqtype="get", url=a.resource.authurl)
    a.create_application_and_monitors()
    #time.sleep(30)
    #a.delete_application_and_monitors()
    #a.create_dt_session()
    #a.init_wait_DT()
################################################################
#               DOCUMENTATION FOR ADMIN_OPS.PY                 #
#                                                              #
# AUTHOR : SRINIVASA BHASWANTH (<EMAIL>)        #
################################################################
__author__ = '{srinivasa bhaswanth}'
__credits__ = ['{srinivasa bhaswanth, abhimanyu sharma, sahil kumar}']
__email__ = '{<EMAIL>}'

#####   Modules     ###################################################################################################################################
from lib2to3.pgen2.token import OP
import requests
import os
import sys
import json
import datetime
from datetime import date, datetime, timedelta
from contextlib import contextmanager
import time
import inspect
import platform
import copy
import subprocess
import logging
import socket
import urllib
import allure



# new additions for MA Auth Token and API ID
import jwt  # pip install pyjwt
import base64  # exists in python by default

#import admin_res, Constants
from zdx.Lib.common_lib import admin_res, Constants

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir)

# from lib import admin_login
#from Lib import admin_res, Constants

# get to know the operating system
if platform.uname()[0] == 'Windows':
    try:
        check_android=subprocess.run("adb devices", capture_output=True)
        check_android_result=str(check_android.stdout)
        android_udid=check_android_result.split("\\r\\n")[1].split("\\t")[0]
        if len(android_udid)<=0:
            raise Exception
        os_version = "Android"
    except:
        os_version = "Windows"
elif platform.uname()[0] == 'Darwin':
    os_version = "MAC"
else:
    os_version = "Windows"
    print("Warning: OS version cannot be determined")


############### CLASS FOR ADMIN LOGIN REQUIRED FOR LOGIN TO PRODUCTION/BETA CLOUD #########################

class Admin_Login:

    def __init__(self, cloud, username, password):

        # cloud = eg: zscalerone.net
        # self.cloud = eg: admin.zscalerone.net
        self.cloud = f'admin.{cloud}'
        self.session = requests.Session()  # Session Is required to get values from cookies
        # self.ip_address = self.get_serv_ip(self.cloud)  # Get IP of cloud eg: admin.zscalerone.net
        self.ip_address = self.cloud  # Get IP of cloud eg: admin.zscalerone.net
        self.key = "jj7tg80fEGao"  # Fixed KEY required to produce obfuscateApiKey
        self.timestamp = str(int(round(time.time() * 1000)))  # Required for obfuscateApiKey and Auth-Function
        self.apikey = self.obfuscateApiKey(
            self.timestamp,
            self.key
        )

        #print(self.ip_address, self.apikey)

        # Auth-Request --> Will get ZS_SESSION_CODE AND JESSIONID

        self.admin_payload = {
            'username': f'{username}',
            'password': f'{password}',
            'apiKey': self.apikey,
            'timestamp': self.timestamp,
        }

        self.header = {
            "Content-Type": 'application/json'
        }

        authentication_url = f"https://{self.ip_address}/zsapi/v1/authenticatedSession"

        self.ZS_SESSION_CODE, self.JSESSIONID = self.makeAuthReq(authentication_url, self.admin_payload, self.header)

        # Will make request to admin_sso which will send "samlResponse"

        admin_sso_url = f"https://{self.ip_address}/zsapi/v1/sso/mobilePortal?_={self.timestamp}"

        self.header = {
            "Content-Type": 'application/json',
            "ZS_CUSTOM_CODE": f"{self.ZS_SESSION_CODE}",  # ZS_CUSTOM_CODE = ZS_SESSION_CODE
            "JSESSIONID": f"{self.JSESSIONID}",
            "User_Agent": "Z-OPS-Monitoring"
        }

        json_SSO_response = self.adminSSOReq(admin_sso_url, self.header)

        #print(json_SSO_response['samlResponse'])

        # Making request to connect to MA
        mobile_url = f"https://mobileadmin.{cloud}/sso.do" if 'gov' not in cloud else f"https://mobile.{cloud}/sso.do"
        self.MAPayload = {
            'providername': '',
            'SAMLResponse': f'{json_SSO_response["samlResponse"]}',
            'RelayState': ''
        }
        self.header = {
            "Content-Type": 'application/x-www-form-urlencoded'
        }

        self.authToken = self.connectToMA(mobile_url, self.header, self.MAPayload)

    def get_serv_ip(self, cloud):
        # ---- CAN BE MERGED TO INIT
        print(f"Cloud is : {cloud}")
        return cloud
        #return socket.gethostbyname(cloud)

    def obfuscateApiKey(self, timestamp, key):
        apikey = ""
        ts = str(timestamp)
        high = ts[len(ts) - 6:]

        # print(f"ts[len(ts) - 6:] = {ts[len(ts) - 6:]}")
        # print(f"0xFFFFFFFF = {0xFFFFFFFF}")
        # print(f"int(high) >> 1 = {int(high) >> 1}")
        # print(f"(0xFFFFFFFF & int(high) >> 1) = {(0xFFFFFFFF & int(high) >> 1)}")
        # print(f"str(0xFFFFFFFF & int(high) >> 1) = {str(0xFFFFFFFF & int(high) >> 1)}")

        low = str(0xFFFFFFFF & int(high) >> 1)

        while len(low) < 6:
            low = "0" + low
        for i in range(0, len(low), 1):
            apikey += key[int(high[i])]
        for j in range(0, len(low), 1):
            apikey += key[int(low[j]) + 2]

        # print(apikey)
        return str(apikey)

    def makeAuthReq(self, url, payload, header):

        try:
            print(f"Trying to connect to URL :: {url}\n")
            req = self.session.post(
                url, data=json.dumps(payload), headers=header, verify=False
            )

            if not (req.status_code >= 200 and req.status_code < 300):
                raise Exception(f"Request to {url} has status code {req.status_code}")

            #print(f"makeAuthReq --> {req.text}\n{req.status_code}\n{req.cookies}\n\n")

        except Exception as e:
            raise Exception(e)

        return req.cookies['ZS_SESSION_CODE'], req.cookies['JSESSIONID']

    def adminSSOReq(self, url, header):

        try:
            print(f"Trying to connect to URL :: {url}")
            req = self.session.get(
                url, headers=header, verify=False
            )

            if not (req.status_code >= 200 and req.status_code < 300):
                raise Exception(f"Request to {url} has status code {req.status_code}")

            #print(f"adminSSOReq --> {req.text}\n{req.status_code}\n\n")

        except Exception as e:
            raise Exception(e)

        return json.loads(req.text)

    def connectToMA(self, url, header, payload):

        try:
            print(f"Trying to connect to URL :: {url}")

            req = self.session.post(
                url, headers=header, data=urllib.parse.urlencode(payload), verify=False
            )

            if not (req.status_code >= 200 and req.status_code < 300):
                raise Exception(f"Request to {url} has status code {req.status_code}")

        except Exception as e:
            raise Exception(e)
        # print(f"connectToMA --> {req.text}\n{req.status_code}\n\n")

        try:
            mobile_response_data = req.text

            authToken = mobile_response_data.split('<span class="mobile-token">')[1].split('</span>')[0]

            #print(f'\nAuth-Token is --> {authToken}\n\n')

            # new addition for getting Api ID
            API_ID = None
            try:
                jwt_payload = jwt.decode(authToken, options={"verify_signature": False})
                api_header_string = jwt_payload['apiHeader']
                api_header_string_ascii = api_header_string.encode('ascii')
                api_header_string_64bit_encoded_ascii = base64.b64encode(api_header_string_ascii)
                API_ID = api_header_string_64bit_encoded_ascii.decode('ascii')
            except Exception as e:
                # hashing this block, keeping in mind that only beta needs API ID, not anything else
                # raise Exception("API ID cannot be decoded, this maybe a serious issue")
                print(f"API ID cannot be decoded, this maybe a serious issue {e}")

            return authToken, API_ID
        except Exception as e:
            raise Exception(e)


#####   admin_ops     ###############################################################################################################################

class admin_ops():
    """
    Base class to be used for MA/ZIA related actions
    any class can import this for performing its own set of MA actions
    Environment
    ------------
        1. operating_system     (str)   name of operating system ('windows'/'mac')
        2. device type          (str)   device type as a string, which is an integer. 4 for mac, 3 for windows
        3. temp_path/temp_file  (str)   directory for temp file path/ temp file name. used for storing IDs of profiles
        4. trusted_net_temp_file/non_trusted_temp_file
                                (str)   directory for temp file path/ temp file name. used for storing IDs of trusted networks
        5. forwarding_profile_name
                                (str)   name of forwarding profile
        6. device_policy_name   (str)   name of device policy
        7. is_forwarding_profile_created/is_device_policy_created
                                (bool)  flag to represent forwarding profile or app profile existence. exists if True, no if False
    Work Flow
    ----------
        1. There are 2 important Central Functions
            a. Authenticate_To_ZIA:             gets auth token for ZIA
            b. Authenticate_To_Mobile_Admin:    gets auth token for MA
        2. all urls, headers and payloads are imported from admin_ops.py, a central payload storing py file
        2. any function will be using these auth tokens for manipulating MA/ZIA state
        3. some functions note down the IDs of profiles into temp files to retain the MA/ZIA state information between multiple sessions.
    """

    def __init__(self,
                 cloud,  # (str)     cloud of which automation should be performed
                 Config,  # (str)     name of config file in /Config folder
                 logger=None,
                 variable = None
                 ):
        self.var = variable if variable else None
        self.cloud = cloud
        self._Logger = logger if logger != None else self.Initialize_Logger("admin_ops.log", Log_Level="INFO")
        if type(Config) == dict:self.Config = Config
        else:self.Get_Config_Details(Config)
        self.Definition = admin_res.admin_res(self.cloud)
        self.os_version = os_version
        # self.temp_path = os.getcwd() + str("\\TMP\\" if self.os_version!="MAC" else "/TMP/")
        if self.var:
            self.temp_path = os.path.join(self.var.automationloc, "TMP")
            self.temp_file = os.path.join(self.var.automationloc, "TMP", "temp_file")
        else:
            self.temp_path = os.path.join(os.getcwd(), "TMP")
            self.temp_file = os.path.join(os.getcwd(), "TMP", "temp_file")
        self.auth_token_temp_file = os.path.join(self.temp_path, "auth_token.txt")
        self.device_type = ('4' if os_version == "MAC" else '3')
        self.non_trusted_net_temp_file = os.path.join(self.temp_path, "non_trusted_net_temp_file")
        self.trusted_net_temp_file = os.path.join(self.temp_path, "trusted_net_temp_file")
        self.forwarding_profile_name = "automation_{}_fp".format("mac" if self.os_version == "MAC" else "windows")
        self.device_policy_name = "automation_{}_dp".format("mac" if self.os_version == "MAC" else "windows")
        self.linux_device_policy_name = "automation_linux_dp"
        self.android_device_policy_name = "automation_android_dp"
        self.ios_device_policy_name = "automation_ios_dp"
        self.export_device_details_file_path = self.temp_path + "export_device_details.csv"
        self.export_service_status_file_path = self.temp_path + "export_service_status.csv"
        self.is_forwarding_profile_created = False
        self.is_device_policy_created = False
        self.default_vpn_bypasses = '1.3.5.7,connect.corp.zscaler.com,199.168.150.189'
        self.TrustedNetworkName = "Automated"
        self.CreatedTrustedNetworks = []
        self.UDID = None
        self.auth_type = "new"
        self.Web_Insights = None
        self.auth_type = "new"
        self.Delete_Auth_Token_Temp_File()
        self.admin_role_name = "automation_{}_ar".format("mac" if self.os_version == "MAC" else "windows")
        self.admin_role_id = None
        self.admin_role_status = False
        self.is_admin_role_created = False
        self.ZIA_posture_name = "automation_{}_fp".format("mac" if self.os_version=="MAC" else "windows")
        self.device_posture_id = False
        self.device_posture_name = "automation_{}_dpolicy".format("mac" if self.os_version=="MAC" else "windows")
        self.is_device_posture_created = False
        # make sure TMP folder exists
        # if os_version=="MAC":
        #     if not os.path.exists(os.getcwd()+"/TMP/"): os.mkdir(os.getcwd()+"/TMP/")
        # else:
        #     if not os.path.exists(os.getcwd()+"\\TMP\\"): os.mkdir(os.getcwd()+"\\TMP\\")
        if not os.path.exists(self.temp_path): os.mkdir(self.temp_path)

    def REQ(self, request_type, url, headers, data=None, verify=False,ZIA_Request=False):
        assert request_type in ["get", "post", "delete","put"]
        try:
            if not ZIA_Request:headers["auth-token"], headers["API-Id"] = self.Authenticate_To_Mobile_Admin()
            # url clean
            if self.cloud != "zscalergov":
                url = url.replace("mobile.", "mobileadmin.")
            headers['Host'] = f'mobile.{self.cloud}.net'
            headers['Connection'] = 'keep-alive'
            headers[
                'User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.181 Safari/537.36'

            # header cleanup
            for each_key, each_value in headers.items():
                try:
                    if "mobile." in each_value and self.cloud != "zscalergov":
                        headers[each_key] = each_value.replace("mobile.", "mobileadmin.")
                except:
                    pass
        except Exception as e:
            print("some weird exception, moving on - {}".format(e))

        # print(self.Authenticate_To_Mobile_Admin())
        # print(headers, "maaann")
        self._Logger.info("Performing {} Request..".format(request_type))
        try:
            time.sleep(1)
            if ZIA_Request:
                self._Logger.info("ZIA Only Request, removing auth-token and API-ID as only cookie and ZS_CUSTOM_CODE is required")
                #del headers['auth-token']
                del headers['Host']
                #del headers['API-Id']
            
            if request_type=="get":
                self._Logger.debug(f"\nURL :: {url}\nHEADER :: {headers}\n")
                response = requests.get(url=url, headers=headers, data=(data if data else None), verify=verify)
            if request_type=="post":
                self._Logger.debug(f"\nURL :: {url}\nHEADER :: {headers}\nPAYLOAD :: {data}")
                response = requests.post(url=url, headers=headers, data=(data if data else None), verify=verify)
            if request_type=="delete":
                self._Logger.debug(f"\nURL :: {url}\nHEADER :: {headers}")
                response = requests.delete(url=url, headers=headers, data=(data if data else None), verify=verify)
            if request_type=="put":
                self._Logger.debug(f"\nURL :: {url}\nHEADER :: {headers}\nPAYLOAD :: {data}")
                response = requests.put(url=url, headers=headers, data=(data if data else None), verify=verify)
        except Exception as E:
            self._Logger.info(f"{E} encountered\n\n, retying after 30 seconds")
            time.sleep(30)
            if request_type=="get":
                self._Logger.debug(f"\nURL :: {url}\nHEADER :: {headers}")
                response = requests.get(url=url, headers=headers, data=(data if data else None), verify=verify)
            if request_type=="post":
                self._Logger.debug(f"\nURL :: {url}\nHEADER :: {headers}\nPAYLOAD :: {data}\n")
                response = requests.post(url=url, headers=headers, data=(data if data else None), verify=verify)
            if request_type=="delete":
                self._Logger.debug(f"\nURL :: {url}\nHEADER :: {headers}")
                response = requests.delete(url=url, headers=headers, data=(data if data else None), verify=verify)
            if request_type=="put":
                self._Logger.debug(f"\nURL :: {url}\nHEADER :: {headers}\nPAYLOAD :: {data}\n")
                response = requests.put(url=url, headers=headers, data=(data if data else None), verify=verify)
        self._Logger.info("REQ response: " +str(response.status_code))
        return response

    # --------------------------------------------------------------------------------------------------------------------------
    def Initialize_Logger(self,
                          Log_File_Name,  # (str)     Name of the Log file to be created
                          Log_Level,  # (str)     Level of logging (INFO/DEBUG/WARNING/CRICITCAL etc)
                          console_handle_needed=True  # (bool)    print_ on command line if True only
                          ):
        """
        Target: Create Logger object.
        Work Flow
        ----------
            1. Create a logger object.
            2. Create a console handler if required
                - console handler is used to print_ required info on command line window.
            3. Create a file handler -> mandatory
                - Make sure 'Logs' folder exists, as this file handler will be created in this folder only.
                - file handler is used to send all information to a log file in specified folder.
            4. Set the debug level (INFO/DEBUG/WARNING/CRITICAL etc). Default: INFO
            5. Specify how the logging must happen (with/without datetime/loglevel/username)
            6. Add all created handlers to the logger instance created in step 1.
            7. return the logger instance.
        """
        # 1, 2 & 3
        self.Logs_Directory = os.path.join(os.path.abspath(os.path.dirname(os.path.abspath("trigger_automation_zdx.py"))),"zdx", "Logs")
        logger = logging.getLogger(Log_File_Name)
        if console_handle_needed: console_handler = logging.StreamHandler()
        if not os.path.exists(self.Logs_Directory): os.mkdir(self.Logs_Directory)
        file_handler = logging.FileHandler(os.path.join(self.Logs_Directory, Log_File_Name))

        # 4
        if Log_Level:
            logger.setLevel(eval("logging.{}".format(Log_Level)))
            if console_handle_needed: console_handler.setLevel(eval("logging.{}".format(Log_Level)))
            file_handler.setLevel(eval("logging.{}".format(Log_Level)))

        # 5
        if console_handle_needed: console_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))

        # 6
        if console_handle_needed: logger.addHandler(console_handler)
        logger.addHandler(file_handler)
        # 7
        return logger

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    def Get_Config_Details(self, configFile):
        """
        Target: Initialize the config dictionary that contains all credentials
        Work Flow
        ---------
            1. if configFile exists in Config folder, import the credentials
            2. if configFile exists in custom location, configFile must be the full directory location. import accordingly
            3. raise Exception otherwise
        """
        self.Config = {}
        if os.path.exists(os.path.join(os.getcwd(), "Config", configFile)):
            with open(os.path.join(os.getcwd(), "Config", configFile)) as json_file:
                self.Config.update(json.load(json_file))
        elif os.path.exists(configFile):
            with open(configFile) as json_file:
                self.Config.update(json.load(json_file))
        else:
            raise Exception("configFile cannot be located!!")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Delete_Auth_Token_Temp_File(self):
        try:
            self._Logger.info(f"Removing {self.temp_file}")
            if os.path.exists(self.temp_file): os.remove(self.temp_file)
            if os.path.exists(self.auth_token_temp_file): os.remove(self.auth_token_temp_file)
        except:
            self._Logger.info("Temp file not found. Moving on..")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Authenticate_To_Mobile_Admin(self,  # Function to fetch auth token for MA
                                     ADMIN_ID=None,  # (str)     admin credential used for MA
                                     ADMIN_PASSWORD=None,  # (str)     admin password used for MA
                                     readFile=True  # (bool)    to get auth-token via file or by making request
                                     ):
        """
        Work Flow
        ----------
            1. get production cloud dict for MA
            2. return auth token
        """
        if ADMIN_ID==None and ADMIN_PASSWORD==None:
            if self.Config["ZIA_ADMIN_ID"]!="None" and self.Config["ZIA_ADMIN_PASSWORD"]!="None":
                ADMIN_ID = self.Config["ZIA_ADMIN_ID"]
                ADMIN_PASSWORD = self.Config["ZIA_ADMIN_PASSWORD"]
            elif self.Config["ZPA_ADMIN_ID"]!="None" and self.Config["ZPA_ADMIN_PASSWORD"]!="None":
                # create a seperate config file and py object in case of zpa only account for ease
                ADMIN_ID = self.Config["ZPA_ADMIN_ID"]
                ADMIN_PASSWORD = self.Config["ZPA_ADMIN_PASSWORD"]
                self.auth_type = "old"
        # if os_version=="MAC":
        #     authTokenPath =os.getcwd()+"/TMP/auth_token.txt"
        # else:
        #     authTokenPath =os.getcwd()+"\\TMP\\auth_token.txt"
        authTokenPath = os.path.join(self.temp_path, "auth_token.txt")
        # apiIdPath = os.path.join( self.temp_path, "apiId.txt" )
        timeDiff = 0
        if os.path.isfile(authTokenPath): timeDiff = int(time.time() - os.path.getmtime(authTokenPath))
        if os.path.isfile(authTokenPath) and readFile == True and int(timeDiff) < 3600:
            #print("reading file ")
            with open(authTokenPath, "r") as f:
                lines = f.read().splitlines()
                token = lines[0].strip()
                try:
                    # new addition for api ID
                    apiID = None
                    apiID = lines[1].strip()
                except:
                    print("api id prbably not present, moving on..")

            if token:
            #    print("str token is {}".format(str(token)))
                if apiID:
                    return str(token), str(apiID)
                else:
                    return str(token), None
        else:
            if timeDiff>=3600:
                self._Logger.info('\n**********\nAuth Token 1 hour old, doing auth again after 20 seconds\n**********\\n')
                time.sleep(20)
            Token = None
            self._Logger.debug(f"ADMIN ID :: {ADMIN_ID} --- PASSWORD :: {ADMIN_PASSWORD}")
            try:
                # this block might not work anymore
                if self.auth_type=="old" and self.cloud=="zscalerbeta":
                #    print("in old\n\n\n**************************\n")
                    apiKey = "130c2293-3bc3-4c2e-a9f0-36b4221581de"
                    self.Definition.MOBILE_ADMIN_AUTHENTICATION_PAYLOAD['loginname'] = ADMIN_ID
                    self.Definition.MOBILE_ADMIN_AUTHENTICATION_PAYLOAD["apikey"] = apiKey
                    self.Definition.MOBILE_ADMIN_AUTHENTICATION_PAYLOAD["role"] = "ADMIN"
                    AuthResult = self.REQ("post",self.Definition.MOBILE_ADMIN_AUTHENTICATION_URL,
                                               data=json.dumps(self.Definition.MOBILE_ADMIN_AUTHENTICATION_PAYLOAD),
                                               verify=False)
    #                 print(self.Definition.MOBILE_ADMIN_AUTHENTICATION_URL,
    # )
    #                 print(AuthResult.json())
                    if AuthResult.status_code == 200:
                        Token = AuthResult.json()['token']
                        # print("Ok....MA Auth result: {}, MA Status code: {}, Token: {}".format(AuthResult.json(),
                        #                                                                        #AuthResult.status_code,
                        #                                                                        #Token))
                    else:
                        raise Exception("Auth Token cant fetch")
                    
                    # new addition for getting Api ID
                    apiID = None
                    try:
                        jwt_payload = jwt.decode(Token, options={"verify_signature": False})
                        api_header_string = jwt_payload['apiHeader']
                        api_header_string_ascii = api_header_string.encode('ascii')
                        api_header_string_64bit_encoded_ascii = base64.b64encode(api_header_string_ascii)
                        apiID = api_header_string_64bit_encoded_ascii.decode('ascii')
                    except:
                        # hashing this block, keeping in mind that only beta needs API ID, not anything else
                        #raise Exception("API ID cannot be decoded, this maybe a serious issue")
                        print("API ID cannot be decoded, this maybe a serious issue")
                else:
                    adminLoginObj = Admin_Login(f"{self.cloud}.net", ADMIN_ID, ADMIN_PASSWORD)
                    Token, apiID = adminLoginObj.authToken

                with open(authTokenPath, "w") as f:
                    f.write("{}\n".format(Token))
                    if apiID:
                        f.write("{}\n".format(apiID))
                return Token, apiID
            except Exception as e:
                raise Exception(f"Error Getting Auth-Token for MA --> {e}")



    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Authenticate_to_ZIA(self,  # Function to authenticate to ZIA
                            ADMIN_ID=None,  # (str)     username for ZIA
                            ADMIN_PASSWORD=None  # (str)     password for ZIA
                            ):
        """
        Work Flow
        ----------
            1. get username and password for ZIA
            2. return auth token for ZIA
        """
        if ADMIN_ID == None and ADMIN_PASSWORD == None:
            if self.Config["ZIA_ADMIN_ID"] != "None" and self.Config["ZIA_ADMIN_PASSWORD"] != "None":
                ADMIN_ID = self.Config["ZIA_ADMIN_ID"]
                ADMIN_PASSWORD = self.Config["ZIA_ADMIN_PASSWORD"]
            elif self.Config["ZPA_ADMIN_ID"] != "None" and self.Config["ZPA_ADMIN_PASSWORD"] != "None":
                ADMIN_ID = self.Config["ZPA_ADMIN_ID"]
                ADMIN_PASSWORD = self.Config["ZPA_ADMIN_PASSWORD"]
        self.IN_VECTOR = str(int(round(time.time() * 1000)))

        # Import Helper Methods Required
        self._Logger.info("Test_Case log_into_smui starts :: %s", datetime.now().strftime('%Y%m%d%H%M%S%f'))

        # if os_version=="MAC":
        #     authTokenPath =os.getcwd()+"/TMP/ZIA_auth_token.txt"
        # else:
        #     authTokenPath =os.getcwd()+"\\TMP\\ZIA_auth_token.txt"

        authTokenPath = os.path.join(self.temp_path, "ZIA_auth_token.txt")

        timeDiff = 0
        if os.path.isfile(authTokenPath): timeDiff = int(time.time() - os.path.getmtime(authTokenPath))
        if os.path.isfile(authTokenPath) == True and int(timeDiff) < 3600:
            #print("reading file ")
            with open(authTokenPath, "r") as f:
                token = f.read().splitlines()
            if token:
                #print("str token is {}".format(str(token[0])))
                return token[0].split("|")[0], token[0].split("|")[1]
        else:
            if timeDiff >= 3600: self._Logger.info(
                '\n**********\nAuth Token 1 hour old, doing auth again\n**********\\n')

            try:
                authZIAObj = Admin_Login(
                    f"{self.cloud}.net",
                    ADMIN_ID, ADMIN_PASSWORD
                )

                self._Logger.info(f'{"*" * 10},{authZIAObj.JSESSIONID}, {authZIAObj.ZS_SESSION_CODE},{"*" * 10}')

                FinalAuthCookie, ZS_SESSION_CODE = f"JSESSIONID={authZIAObj.JSESSIONID}; ZS_SESSION_CODE={authZIAObj.ZS_SESSION_CODE}", authZIAObj.ZS_SESSION_CODE

                self._Logger.info(f"Got Data --> {FinalAuthCookie},{ZS_SESSION_CODE}")

                with open(authTokenPath, "w") as f:
                    f.write(f"{FinalAuthCookie}|{ZS_SESSION_CODE}")

                return FinalAuthCookie, ZS_SESSION_CODE
            except Exception as e:
                self._Logger.info(f"Error Auth to ZIA ERROR:: {e}")
                raise Exception(f"Error Auth to ZIA ERROR:: {e}")

     # -----------------------------------------------------------------------------------------------------------
    @allure.step("ZIA Activate")
    def ZIA_Activate(self,sleep_time=0):
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS[
            'ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self.ZIA_Activate_Response = self.REQ("put",self.Definition.ZIA_Activate_URL, headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        #print(self.ZIA_Activate_Response.status_code)
        if self.ZIA_Activate_Response.status_code >= 200 and self.ZIA_Activate_Response.status_code <= 300:
            self._Logger.info("ZIA_Activate Success")
            time.sleep(sleep_time)
        else:
            self._Logger.error("ZIA_Activate Failed")
            raise Exception("Invalid Status code for zia activate :: {self.ZIA_Activate_Response.status_code}")

    # -----------------------------------------------------------------------------------------------------------

    def Fetch_Web_Insights(self,
                           look_for=2,
                           returnData=True,
                           urlTobeSearched=None
                           ):
        # init
        def give_me_string(date_time_object):
            year = str(date_time_object.year).zfill(4)
            month = str(date_time_object.month).zfill(2)
            day = str(date_time_object.day).zfill(2)
            hour = str(date_time_object.hour).zfill(2)
            minute = str(date_time_object.minute).zfill(2)
            second = str(date_time_object.second).zfill(2)
            return '{0}.{1}.{2} {3}:{4}:{5}'.format(day, month, year, hour, minute, second)

        # set startDate, endDate and pageSize as required or we can use datetime to set a particular required time
        startDate = give_me_string(datetime.now() + timedelta(minutes=-1 * look_for))
        endDate = give_me_string(datetime.now())
        Time = int(round(time.time() * 1000))
        # url = 'https://admin.zscalerbeta.net/zsapi/v1/transactionData/webRequest'
        # payload = {"pageSize": "10000"}
        startDate = datetime.strptime(startDate, '%d.%m.%Y %H:%M:%S')
        endDate = datetime.strptime(endDate, '%d.%m.%Y %H:%M:%S')

        # -1980 to convert timestamp to IST timestamp
        self.Definition.Fetch_Web_Insights_Payload['startTime'] = int(time.mktime(startDate.timetuple())) * 1000 - 19800
        self.Definition.Fetch_Web_Insights_Payload['endTime'] = int(time.mktime(endDate.timetuple())) * 1000 - 19800
        self.Definition.Get_Web_Insights_Header['Cookie'], self.Definition.Get_Web_Insights_Header[
            'ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()

        if urlTobeSearched:
            self.Definition.Fetch_Web_Insights_Payload['urlSearchFilter'] = {
                "matchType": "CONTAINS",
                "urlPart": "ALL",
                "value": urlTobeSearched
            }
        self._Logger.info(
            f"Payload :: {self.Definition.Fetch_Web_Insights_Payload}\nURL :: {self.Definition.Get_Web_Insights_Url} ")
        self.AuthResult = self.REQ("post",self.Definition.Get_Web_Insights_Url,
                                        headers=self.Definition.Get_Web_Insights_Header,
                                        data=json.dumps(self.Definition.Fetch_Web_Insights_Payload),ZIA_Request=True)
        self._Logger.info(f"first {self.AuthResult.status_code}")
        time.sleep(5)
        timer = 0
        while timer < 10:
            time.sleep(2)
            url = self.Definition.Final_Get_Web_Insights_Url.replace("XXXXX",str(int(round(time.time() * 1000)) + timer))
            #print('URL is {}'.format(url))
            self.AuthResult = self.REQ("get",url, headers=self.Definition.Get_Web_Insights_Header,ZIA_Request=True)
            timer += 1
            #print(f"Status Code :: {self.AuthResult.status_code}\n")
            data = self.AuthResult.json()
            #self._Logger.info(f"Response :: {data}")
            if self.AuthResult.status_code == 200: 
                self._Logger.info(f"{data['status']}, ITEMS :: {data['progressItemsComplete']}")
                self._Logger.info("Making api call again as fetched logs are 0")
            if self.AuthResult.status_code >= 300 or ('status' in data.keys() and data['status'] == 'COMPLETE'):
                self._Logger.info(f"{data['status']}, ITEMS :: {data['progressItemsComplete']}")
                break

        url = url.replace('webRequest', 'web')
        self._Logger.info(f"URL :: {url}, HEADERS :: {json.dumps(self.Definition.Get_Web_Insights_Header)}")
        self.AuthResult = self.REQ("get",url, headers=self.Definition.Get_Web_Insights_Header,ZIA_Request=True)
        response_json = self.AuthResult.json()
        #print("last", self.AuthResult.status_code)
        number_of_web_insights_logs = len(response_json["transactions"])
        if number_of_web_insights_logs == 0:
            raise Exception("Error :: Web insights has 0 logs")
        if self.AuthResult.status_code == 200:
            self._Logger.info(f"Web Insights fetch success, Number of logs :: {number_of_web_insights_logs} !!")
            self.web_insights_fetched = True
            self.web_insights_content = copy.deepcopy(self.AuthResult.json()["transactions"])
        else:
            raise Exception("Error :: Web Insights fetch didnt go well!")

        if returnData: return self.AuthResult.json()
        # self.Web_Insights = copy.deepcopy(response_json["transactions"])

    def Search_Web_Insights(self,
                            urlToBeSearched,  # (str) url which has to be searched in logs
                            searchDict,
                            # (dict) dict containing exact key and value as that in webInsightLogs and its value, example for SSL Policy with Inspect rule searchDict will be {'sslPolicyReason':'INSPECTED'}
                            look_for=5,
                            mac_sleep_time=30
                            ):
        # if os_version=="MAC":
        #print(f'sleeping {mac_sleep_time} seconds')
        time.sleep(mac_sleep_time)
        webInsightLogs = self.Fetch_Web_Insights(urlTobeSearched=urlToBeSearched, look_for=look_for)['transactions']
        isLogFound = False
        for details in webInsightLogs:
            if details['urlDomain'] == urlToBeSearched and 'sslPolicyReason' in details.keys():
                self._Logger.info(f"Given application :: {urlToBeSearched} found in logs")

                # now check if time in log is greater than start time and less then end time used which searching webinsights logs
                if int(details['eventTimestamp']) >= int(
                        self.Definition.Fetch_Web_Insights_Payload['startTime']) and int(
                    details['eventTimestamp']) <= int(self.Definition.Fetch_Web_Insights_Payload['endTime']):
                    self._Logger.info(f"Time stamp validated for accessed application")
                else:
                    raise Exception(
                        f"Event log time was :: {details['eventTimestamp']}, But Log Start and End time is :: {self.Definition.Fetch_Web_Insights_Payload['startTime']} {self.Definition.Fetch_Web_Insights_Payload['endTime']}")

                # now check if policy applied matches with one got in webinsights logs
                for key in searchDict:
                    if details[key] == searchDict[key]:
                        self._Logger.info(f"{key} matched : Required :: {searchDict[key]} GOT :: {details[key]}")
                        isLogFound = True
                        break
                    else:
                        self._Logger.info(f"{key} not matched : Required :: {searchDict[key]} GOT :: {details[key]}")
        self._Logger.info(f"LOG FOUND ? :: {isLogFound}")
        return isLogFound

    # -----------------------------------------------------------------------------------------------------------
    def SAML_ACTION_SMUI(self,  # function to enable or disable SAML based auth on SMUI
                         mode
                         ):
        """
        Work Flow
        ----------
            1. Authencticate into SMUI
            2. Enable or disables SAML based login

        Prerequiste is that we should have IDP configured on SMUI
        """

        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        if 'enable' in mode.lower():
            self.Definition.SAML_ACTIONS_SMUI_PAYLOAD['samlEnabled'] = True
        elif 'disable' in mode.lower():
            self.Definition.SAML_ACTIONS_SMUI_PAYLOAD['samlEnabled'] = False
        self._Logger.info("\nSAML ACTION Payload : {}\nHEADER : {}\nURL : {}\n".format(
            json.dumps(self.Definition.SAML_ACTIONS_SMUI_PAYLOAD), self.Definition.ZIA_HEADERS,
            self.Definition.SAML_ACTIONS_SMUI_URL))

        samlAction = self.REQ("put",self.Definition.SAML_ACTIONS_SMUI_URL, headers=self.Definition.ZIA_HEADERS,
                                  data=json.dumps(self.Definition.SAML_ACTIONS_SMUI_PAYLOAD),ZIA_Request=True)
        if samlAction.status_code == 200:
            self._Logger.info("SAML ON SMUI: {}".format(self.Definition.SAML_ACTIONS_SMUI_PAYLOAD['samlEnabled']))
            self.ZIA_Activate()
        else:
            self._Logger.error("Invalid status code {} for SAML ACTION Request on SMUI".format(samlAction.status_code))
            raise Exception("Invalid status code {} for SAML ACTION Request on SMUI".format(samlAction.status_code))


    # ------------------------------------------------------------------------------------------------------------------------------------------------------

    def CHANGE_SAML_ON_ZIA(self,
                           idpName  # (str) name of idp configured on ZIA
                           ):
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        epochTime = str(int(time.time() * 1000))
        url = self.Definition.GET_AVAILABLE_IDP + epochTime
        getAvailableIDP = self.REQ("get",url, headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        idpID = ''
        idpFound = False
        if getAvailableIDP.status_code == 200:
            self._Logger.info(f"Get idp list status code :: {getAvailableIDP.status_code}")
            #print(getAvailableIDP.json())
            for idpDict in getAvailableIDP.json():
                if idpDict['idpName'] == idpName:
                    idpFound = True
                    self._Logger.info("given idp found")
                    idpID = str(idpDict['id'])
                    self._Logger.info(f"IPD :: {idpDict['idpName']} - IDP ID :: {idpDict['id']}")
        else:
            self._Logger.error(f"Invalid status code for getting idp list :: {getAvailableIDP.status_code}")
        if idpFound == False: raise Exception(f"Given idp {idpName} not found on ZIA")
        url = self.Definition.CHANGE_IDP_URL.replace('IDP_ID', idpID)
        self._Logger.info(f"change idp url :: {url}")
        changeIDP = self.REQ("put",url, headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        if changeIDP.status_code == 200:
            self._Logger.info(f"{changeIDP.json()['MESSAGE']}")
            self.ZIA_Activate()
        elif changeIDP.status_code == 400:
            if 'code' in changeIDP.json().keys() and changeIDP.json()['code'] == 'INVALID_INPUT_ARGUMENT':
                if 'is already set as default IDP' in changeIDP.json()["message"]:
                    self._Logger.info(f"{idpName} already set on ZIA")
                else:
                    raise Exception(f'ERROR :: {changeIDP.json()["message"]}')
            else:
                raise Exception(f"Invalid status code :: 400")

    # -----------------------------------------------------------------------------------------------------------------
    def GET_DEVICE_MANAGEMENT_DEVICE_LIST(self):
        """
        Target: To Get list of all logged in devices on ZIA Device Management
        Work Flow
        ----------
            1. Auth to ZIA
            2. Get List of all logged in devices
            3. return the list
        """
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS[
            'ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self._Logger.info("Getting logged in devices list")
        epochTime = str(int(time.time() * 1000))
        getDevicesList = self.REQ("get",self.Definition.Get_Device_Management_Devices_List_Url + epochTime,headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        self._Logger.info(f"Get Admin List status code :: {getDevicesList.status_code}")
        if getDevicesList.status_code == 200:
            return getDevicesList.json()
        else:
            raise Exception(f"Invalid status code for getting Admin List from ZIA :: {getDevicesList.status_code}")

    # -----------------------------------------------------------------------------------------------------------

    def Get_List_Of_Users(self,
                          getDictByEmail=None,  # (str)     Email of the user whose data dictionary has to be fetched
                          return_response=True
                          # (bool)    return all fetched raw data from ZIA if set to True, else do nothing
                          ):
        """
        Target: Get List of Available Users in ZIA
        Work Flow
        ----------
            1. auth to ZIA
            2. Get all data from ZIA User List
            3. if "getDictByEmail" is provided, the dictionary that contains the email that is identical to "getDictByEmail" will be returned
            4. by default, this returns all fetched data (set return_response to False to not do so)
        """
        time_of_interest = str(int(time.time() * 1000))
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS[
            'ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self.Definition.Get_List_Of_ZIA_Users_URL = self.Definition.Get_List_Of_ZIA_Users_URL.replace(
            "ADDEPOCHTIMESTAMPHERE", str(time_of_interest))

        self._Logger.info("\n\n URL: {0}\n Header: {1}\n\n".format(self.Definition.Get_List_Of_ZIA_Users_URL,self.Definition.ZIA_HEADERS))
        self.Get_List_Of_ZIA_Users_Response = self.REQ("get",self.Definition.Get_List_Of_ZIA_Users_URL,headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        #print(self.Get_List_Of_ZIA_Users_Response.status_code)
        if self.Get_List_Of_ZIA_Users_Response.status_code >= 200 and self.Get_List_Of_ZIA_Users_Response.status_code <= 300:
            self._Logger.debug(self.Get_List_Of_ZIA_Users_Response.json())
            self._Logger.info("User Data Fetch Success")
        else:
            self._Logger.error("User Data Fetch Failed")
            raise Exception(f"User Data Fetch Failed Invalid Status code :: {self.Get_List_Of_ZIA_Users_Response.status_code}")
        if getDictByEmail:
            for each_entry in self.Get_List_Of_ZIA_Users_Response.json():
                if "email" in each_entry.keys() and each_entry["email"] == getDictByEmail:
                    self._Logger.info("Required User Data Found!!.. returning it..")
                    return each_entry
            self._Logger.error("No User found with requested Email!!")
            raise Exception("No User found with requested Email!!")
        if return_response:
            return self.Get_List_Of_ZIA_Users_Response.json()

    def Get_List_Of_ZIA_Groups(self,
                               getDictByName=None,  # (str)     Name of the ZIA Group whose Dict has to be returned
                               return_response=True,
                               # (bool)    return all fetched raw data from ZIA if set to True, else do nothing
                               getIdByName=None
                               ):
        """
        Target: To Get list of all available Groups in ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. Get List of all available groups, and their corresponding IDs
            3. if "getDictByName" is provided, the dictionary that contains the Name that is identical to "getDictByName" will be returned
            4. by default, this returns all fetched data (set return_response to False to not do so)
        """
        time_of_interest = str(int(round(time.time() * 1000)))
        self.Definition.Get_List_Of_ZIA_Groups_URL = self.Definition.Get_List_Of_ZIA_Groups_URL.replace("ADDEPOCHTIMESTAMPHERE", time_of_interest)
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self._Logger.info("\n\n URL: {0}\n HEADERS: {1}\n\n".format(self.Definition.Get_List_Of_ZIA_Groups_URL,self.Definition.ZIA_HEADERS))
        self.Get_List_Of_ZIA_Groups_RESPONSE = self.REQ("get",url=self.Definition.Get_List_Of_ZIA_Groups_URL,headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        self._Logger.info(self.Get_List_Of_ZIA_Groups_RESPONSE.status_code)
        if self.Get_List_Of_ZIA_Groups_RESPONSE.status_code == 200:
            self._Logger.info("Group List Fetch Success")
        else:
            self._Logger.error("Group List Fetch Failed")
            raise Exception(
                f"Group List Fetch Failed Invalid Status code :: {self.Get_List_Of_ZIA_Groups_RESPONSE.status_code}")
        if getDictByName:
            print(self.Get_List_Of_ZIA_Groups_RESPONSE.json(), getDictByName)
            for each_entry in self.Get_List_Of_ZIA_Groups_RESPONSE.json():
                if "name" in each_entry.keys() and each_entry["name"] == getDictByName:
                    return each_entry
            self._Logger.error("No Group found with requested ID!!")
            raise Exception("No Group found with requested ID!!")
        if getIdByName:
            #print(self.Get_List_Of_ZIA_Groups_RESPONSE.json(), getIdByName)
            for each_entry in self.Get_List_Of_ZIA_Groups_RESPONSE.json():
                if "name" in each_entry.keys() and each_entry["name"] == getIdByName:
                    return each_entry["id"]
            #self._Logger.error("No Group found with requested ID!!")
            raise Exception("No Group found with requested ID!!")
        if return_response:
            return self.Get_List_Of_ZIA_Groups_RESPONSE.json()

    # -----------------------------------------------------------------------------------------------------------------
    def Get_List_Of_ZIA_Departments(self,
                                    getDictByName=None,
                                    # (str)     Name of the Department whose Dict has to be returned
                                    return_response=True  # (bool)    if True, return all the raw data fetched from ZIA
                                    ):
        """
        Target: To Get list of all available Departments in ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. Get List of all available Departments, and their corresponding IDs
            3. if "getDictByName" is provided, the data dictionary that contains the Name that is identical to "getDictByName" will be returned
            4. by default, this returns all fetched data (set return_response to False to not do so)
        """
        time_of_interest = str(int(round(time.time() * 1000)))
        self.Definition.Get_List_Of_Departments_ZIA_URL = self.Definition.Get_List_Of_Departments_ZIA_URL.replace("ADDEPOCHTIMESTAMPHERE", time_of_interest)
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self.Get_List_Of_Departments_ZIA_RESPONSE = self.REQ("get",url=self.Definition.Get_List_Of_Departments_ZIA_URL,headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        self._Logger.info(self.Get_List_Of_Departments_ZIA_RESPONSE.status_code)
        if self.Get_List_Of_Departments_ZIA_RESPONSE.status_code == 200:
            self._Logger.info("Department List Fetch Success")
        else:
            self._Logger.error("Department List Fetch Failed")
            raise Exception(
                f"Department List Fetch Failed Invalid Status Code :: {self.Get_List_Of_Departments_ZIA_RESPONSE.status_code}")
        if getDictByName:
            for each_entry in self.Get_List_Of_Departments_ZIA_RESPONSE.json():
                if "name" in each_entry.keys() and each_entry["name"] == getDictByName:
                    return each_entry
            self._Logger.error("No Group found with requested ID!!")
            raise Exception("No Group found with requested ID!!")
        if return_response:
            return self.Get_List_Of_Departments_ZIA_RESPONSE.json()

    def GET_MA_GROUP_LIST(self):
        """
        Target: To Get list of all available groups on Mobile Admin
        Work Flow
        ----------
            1. Auth to MA
            2. Get List of all available groups
            3. return the list
        """
        # authToken = self.Authenticate_To_Mobile_Admin()
        self._Logger.info("Getting groups list from MA")
        getGroupList = self.REQ(request_type="get", url=self.Definition.Get_Groups_List_MA_Url,headers={'auth-token': ''})
        self._Logger.info(f"Get Group List Status Code :: {getGroupList.status_code}")
        if getGroupList.status_code == 200:
            return getGroupList.json()
        else:
            raise Exception(f"Invalid status code for getting group list from MA :: {getGroupList.status_code}")

    def GET_ZIA_ADMIN_LIST(self):
        """
        Target: To Get list of all available admins on ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. Get List of all available Admins
            3. return the list
        """
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self._Logger.info("Getting ZIA Admins list")
        epochTime = str(int(time.time() * 1000))
        getAdminList = self.REQ("get",self.Definition.Get_Admin_List_Url + epochTime, headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        self._Logger.info(f"Get Admin List status code :: {getAdminList.status_code}")
        if getAdminList.status_code == 200:
            return getAdminList.json()
        else:
            raise Exception(f"Invalid status code for getting Admin List from ZIA :: {getAdminList.status_code}")

    def GET_ZIA_ADMIN_ROLES(self):
        """
        Target: To Get list of all available adminsn roles on ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. Get List of all available Admins roles
            3. return the list
        """
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self._Logger.info("Getting ZIA Admins Roles list")
        epochTime = str(int(time.time() * 1000))
        getAdminRolesList = self.REQ("get",self.Definition.Get_Admin_Roles_Url + epochTime,headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        self._Logger.info(f"Get Admin Roles List status code :: {getAdminRolesList.status_code}")
        if getAdminRolesList.status_code == 200:
            return getAdminRolesList.json()
        else:
            raise Exception(f"Invalid status code for getting Admin Roles List from ZIA :: {getAdminRolesList.status_code}")

    # ----------------------------------------------------------------------------------------------------------------
    def Create_User_ZIA(self,
                        username="whateveruser",  # (str)     name of the user as required
                        password="Admin@123",  # (str)     password of the user as required
                        department=None,  # (str)     Name of the department the user has to belong to
                        groups=None,  # (list of strings)
                        #           list of strings that are the names of the groups
                        comments="whoo",  # (str)     comments to add when creating the user
                        user_state=True,  # (bool)    enable or disable the user with True or False
                        return_id=False,  # (bool)    return the user id if set to True else dont
                        return_user_id=True
                        ):
        self.Definition = admin_res.admin_res(self.cloud)
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()

        self.Definition.Create_New_ZIA_User_PAYLOAD["name"] = username
        self.Definition.Create_New_ZIA_User_PAYLOAD["email"] = str(
            username + "@" + self.Config["ZIA_USER_ID"].split("@")[-1])

        self.Definition.Create_New_ZIA_User_PAYLOAD["groups"] = [self.Get_List_Of_ZIA_Groups(getDictByName=each) for each in groups]
        self.Definition.Create_New_ZIA_User_PAYLOAD["department"] = self.Get_List_Of_ZIA_Departments(getDictByName=department)
        # self.Definition.Create_New_ZIA_User_PAYLOAD["department"] = department
        # self.Definition.Create_New_ZIA_User_PAYLOAD["department"] = {each[0]:str(each[1]) for each in self.Get_List_Of_ZIA_Departments(getDictByName=department).items() if "id" in each or "name" in each}

        self.Definition.Create_New_ZIA_User_PAYLOAD["comments"] = comments
        self.Definition.Create_New_ZIA_User_PAYLOAD["password"] = password
        self.Definition.Create_New_ZIA_User_PAYLOAD["adminUser"] = False
        self.Definition.Create_New_ZIA_User_PAYLOAD["disabled"] = not user_state

        self._Logger.info(
            "\n\n URL: {0}\n Header: {1}\n Payload: {2}\n\n".format(self.Definition.Create_New_ZIA_User_URL,
                                                                    self.Definition.ZIA_HEADERS,
                                                                    self.Definition.Create_New_ZIA_User_PAYLOAD))
        self.Create_New_User_ZIA_Response = self.REQ("post",url=self.Definition.Create_New_ZIA_User_URL,headers=self.Definition.ZIA_HEADERS,data=json.dumps(self.Definition.Create_New_ZIA_User_PAYLOAD),ZIA_Request=True)
        self._Logger.info(self.Create_New_User_ZIA_Response.status_code, self.Create_New_User_ZIA_Response.json())
        if self.Create_New_User_ZIA_Response.status_code == 200:
            self._Logger.debug(self.Create_New_User_ZIA_Response.json())
            self._Logger.info("New User Creation Success")
            if return_id: return self.Create_New_User_ZIA_Response.json()
            if return_user_id: return copy.deepcopy(self.Definition.Create_New_ZIA_User_PAYLOAD["email"])
        else:
            if self.Create_New_User_ZIA_Response.json()['message']=='Duplicated User':
                # return str(username + "@" + self.Config["ZIA_USER_ID"].split("@")[-1])
                userDetails = self.Get_List_Of_Users(getDictByEmail=str(username + "@" + self.Config["ZIA_USER_ID"].split("@")[-1]).lower())
                self.Delete_User_ZIA(ID=userDetails['id'])
                self._Logger.info("Making create request again")
                self.Create_New_User_ZIA_Response = self.REQ("post",url=self.Definition.Create_New_ZIA_User_URL,headers=self.Definition.ZIA_HEADERS,data=json.dumps(self.Definition.Create_New_ZIA_User_PAYLOAD),ZIA_Request=True)
                self._Logger.info(self.Create_New_User_ZIA_Response.status_code)
                if self.Create_New_User_ZIA_Response.status_code == 200:
                    self._Logger.debug(self.Create_New_User_ZIA_Response.json())
                    self._Logger.info("New User Creation Success")
                    if return_id: return self.Create_New_User_ZIA_Response.json()
                    if return_user_id: return copy.deepcopy(self.Definition.Create_New_ZIA_User_PAYLOAD["email"])
                else:
                    self._Logger.debug(self.Create_New_User_ZIA_Response.json())
                    self._Logger.error(f"Invalid Status Code for user creation :: {self.Create_New_User_ZIA_Response.status_code}")
                    raise Exception(f"Invalid Status Code for user creation :: {self.Create_New_User_ZIA_Response.status_code}")
            else:
                self._Logger.debug(self.Create_New_User_ZIA_Response.json())
                self._Logger.error(f"Invalid Status Code for user creation :: {self.Create_New_User_ZIA_Response.status_code}")
                raise Exception(f"Invalid Status Code for user creation :: {self.Create_New_User_ZIA_Response.status_code}")
        

    # ---------------------------------------------------------------------------------------------------------------
    def Delete_User_ZIA(self,
                        Email=None,
                        ID=None,
                        Name=None
                        ):
        if not Email and not ID:
            if Name:
                Email = str(Name) + "@" + self.Config["ZIA_USER_ID"].split("@")[1]
            else:
                Email = "whateveruser" + "@" + self.Config["ZIA_USER_ID"].split("@")[1]
        if Email and not ID:
            response = self.Get_List_Of_Users(getDictByEmail=str(Email).lower())
            ID = response["id"]
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS[
            'ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self.Definition.Delete_New_ZIA_User_URL = self.Definition.Delete_New_ZIA_User_URL.replace("PASTETHEIDHERE",str(ID))

        self._Logger.info("\n\n URL: {0}\n Header: {1}\n\n".format(self.Definition.Delete_New_ZIA_User_URL,self.Definition.ZIA_HEADERS))
        self.Delete_New_ZIA_User_Response = self.REQ("delete",url=self.Definition.Delete_New_ZIA_User_URL,headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        #print(self.Delete_New_ZIA_User_Response.status_code)
        if self.Delete_New_ZIA_User_Response.status_code >= 200 and self.Delete_New_ZIA_User_Response.status_code <= 300:
            self._Logger.info("User Deletion Success")
        else:
            self._Logger.error(
                f"User Deletion Failed, Invalid Status Code :: {self.Delete_New_ZIA_User_Response.status_code}")
            raise Exception(
                f"User Deletion Failed, Invalid Status Code :: {self.Delete_New_ZIA_User_Response.status_code}")

    # ---------------------------------------------------------------------------------------------------------------

    def Create_Group_ZIA(self,
                         Group_Name="Automation_Test_Group",  # (str)     name to be used to create the group
                         Comments="Yo!",  # (str)     comments to be used to create the group
                         return_ID=False,  # (bool)    return the ID of group if True
                         return_All_Data=False
                         ):
        """
        Target: To Create a Test Group in ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. Create a Group with Specified Name and Comments
            3. if "return_ID" set to True, return the data dictionary of the Group that is just created
        """
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS[
            'ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self.Definition.Create_Group_Payload["name"] = Group_Name
        self.Definition.Create_Group_Payload["comments"] = Comments
        self.Create_Group_Response = self.REQ("post",url=self.Definition.Create_Group_Url,
                                                   headers=self.Definition.ZIA_HEADERS,
                                                   data=json.dumps(self.Definition.Create_Group_Payload),ZIA_Request=True)
        if self.Create_Group_Response.status_code == 200:
            self._Logger.debug(self.Create_Group_Response.json())
            self._Logger.info("Group Creation Success")
        else:
            self._Logger.error(f"Group Creation Failed, Invalid status code :: {self.Create_Group_Response.status_code}")
            raise Exception(f"Group Creation Failed, Invalid status code :: {self.Create_Group_Response.status_code}")
        if return_ID: return self.Create_Group_Response.json()["id"]
        if return_All_Data: return self.Create_Group_Response.json()

    def Delete_Group_ZIA(self,
                         Name="Automation_Test_Group",  # (str)     Name of the ZIA Group that has to be deleted
                         ID=None  # (str)     ID of the ZIA Group that has to be deleted
                         ):
        """
        Target: To Create a Test Group in ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. Create a Group with Specified Name and Comments
        """
        self.Definition = admin_res.admin_res(self.cloud)
        if Name and not ID:
            ID = self.Get_List_Of_ZIA_Groups(getDictByName=Name)["id"]
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS[
            'ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self.Definition.Delete_Group_Url = self.Definition.Delete_Group_Url.replace("XXXXX", str(ID))  # 11105685
        #print(self.Definition.Delete_Group_Url, self.Definition.ZIA_HEADERS)
        self.Delete_Group_Response = self.REQ("delete",url=self.Definition.Delete_Group_Url,
                                                     headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        #print(self.Delete_Group_Response.status_code)
        if self.Delete_Group_Response.status_code == 204:
            self._Logger.info("Group Deletion Success")
        else:
            self._Logger.error(f"Group Deletion Failed, Invalid status code :: {self.Delete_Group_Response.status_code}")
            raise Exception(f"Group Deletion Failed, Invalid status code :: {self.Delete_Group_Response.status_code}")

    def Sync_Groups_MA(self,
                       sleep_time=30,
                       returnResponse=False):
        """
        Target: To Sync a Test Group in ZIA
        Work Flow
        ----------
            1. Auth to MA
            2. Click MA/Administration/ClientConnectorSupport/AdvancedConfiguration/SyncGroups
        """
        # self.Definition.Sync_Groups_Header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        self.Sync_Groups_Response = self.REQ(request_type="post", url=self.Definition.Sync_Groups_Url,
                                             headers=self.Definition.Sync_Groups_Header,
                                             data=json.dumps(self.Definition.Sync_Groups_Payload))
        if self.Sync_Groups_Response.status_code == 200:
            self._Logger.debug(self.Sync_Groups_Response.json())
            self._Logger.info("Sync Groups Success")
            time.sleep(sleep_time)
            if returnResponse: return self.Sync_Groups_Response.json()
        else:
            self._Logger.error("Sync Groups Failed")
            raise Exception("Sync Groups Failed")

    # ----------------------------------------------------------------------------------------------------------------
    def CREATE_ZIA_ADMIN(self,
                         adminName,  # (str) name of admin
                         password,  # (str) password of admin
                         email,  # (str) email of admin
                         loginName,  # (str) name with which we login or do auth on ZIA/MA
                         role,  # (str) Role of admin, maps to the available roles on ZIA like Super Admin
                         returnData=False  # (bool) Defines whether to return the json response or not
                         ):
        """
        Target: Create new admin on ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. Get Available roles present on ZIA, compares it with input role, add it to admin
            3. Create Admin
        """
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        adminPayload = copy.deepcopy(self.Definition.Create_Admin_Payload)
        adminRoles = self.GET_ZIA_ADMIN_ROLES()
        isRoleFound = False
        for roles in adminRoles:
            if roles['name'] == role:
                isRoleFound = True
                adminPayload['role'] = {'id': roles['id'], 'name': roles['name']}
        if isRoleFound == False: raise Exception(f"ERROR :: Given role {role} not found on ZIA")
        adminPayload['userName'] = adminName
        adminPayload['password'] = password
        adminPayload['email'] = adminName + '@' + self.Config['ZIA_USER_ID'].split('@')[-1] if '@' not in email else email
        adminPayload['loginName'] = adminName + '@' + self.Config['ZIA_USER_ID'].split('@')[-1] if '@' not in loginName else loginName

        self._Logger.info(f"ADMIN PAYLOAD ::\n{json.dumps(adminPayload)}\nURL :: {self.Definition.Create_Admin_Url}")

        createAdmin = self.REQ("post",url=self.Definition.Create_Admin_Url, headers=self.Definition.ZIA_HEADERS,data=json.dumps(adminPayload),ZIA_Request=True)
        self._Logger.info(f"Create Admin Status Code :: {createAdmin.status_code}")
        if createAdmin.status_code == 200:
            self.createdAdminDetails = createAdmin.json()
            if returnData: return self.createdAdminDetails
        else:
            raise Exception(f"Invalid status code for creating ZIA admin :: {createAdmin.status_code}")

    def DELETE_ZIA_ADMIN(self,
                         ID=None,  # (int/str) ID of the admin to be deleted
                         NAME=None  # (str) Name of the admin to be deleted
                         ):
        """
        Target: Delete admin on ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. If ID is given, add it to delete url and make api call
            3. Else, if we have self.createdAdminDetails vairable, then we use that and pick up id from there and add it to url
            4. Else, if name is given, then we get the list of available admins on ZIA, checks if given name admin exisits, add its ID to url
            5. Make 2 API Calls :
                1. Revoke Admin
                2. Delete Admin
        """
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self._Logger.info("Delete ZIA Admin")
        revokeUrl = self.Definition.Delete_ZIA_Admin_Revoke_Url
        url = self.Definition.Delete_ZIA_Admin_Url
        if ID:
            self._Logger.info("Got Admin ID from ID argument")
            url += str(ID)
            revokeUrl += str(ID)
        elif hasattr(self, 'createdAdminDetails') and 'id' in self.createdAdminDetails.keys() and \
                self.createdAdminDetails['id'] > 0 and self.createdAdminDetails['name'] == NAME:
            self._Logger.info("Got Admin id from self.createdAdminDetails vairable")
            url += str(self.createdAdminDetails['id'])
            revokeUrl += str(self.createdAdminDetails['id'])
        else:
            if NAME == None:
                raise Exception("Either give ID or NAME of admin to be deleted")
            else:
                adminList = self.GET_ZIA_ADMIN_LIST()
                isAdminFound = False
                for admin in adminList:
                    if admin['name'] == NAME:
                        isAdminFound = True
                        url += str(admin['id'])
                        revokeUrl += str(admin['id'])
                if isAdminFound == False: raise Exception(f"Given Admin {NAME} not found on ZIA")
        self._Logger.info(f"\nDelete Admin Revoke URL :: {revokeUrl}\nDelete Admin URL :: {url}")
        revokeAdmin = self.REQ("post",url=revokeUrl, headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        self._Logger.info(f"Revoke URL Status Code :: {revokeAdmin.status_code}")
        if revokeAdmin.status_code == 200:
            self._Logger.info("Admin Revoked")
        else:
            raise Exception(f"Invalid status code for delete admin :: {revokeAdmin.status_code}")

        deleteAdmin = self.REQ("delete",url, headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        self._Logger.info(f"Delete Admin Status Code :: {deleteAdmin.status_code}")
        if deleteAdmin.status_code == 204:
            self._Logger.info("Admin deleted")
        else:
            raise Exception(f"Invalid status code for delete admin :: {deleteAdmin.status_code}")

    # ----------------------------------------------------------------------------------------------------------------
    def Get_Pac_Files_List(self):
        """
        Target: Gets list of available pac files hosted on ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. fetch list of avaialable pacs on ZIA
            3. Returns list
        """
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        pacFilesList = self.REQ("get",url=self.Definition.Get_Pac_Files_List_Url, headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        self._Logger.info(f"Get Pac File List status code :: {pacFilesList.status_code}")
        if pacFilesList.status_code == 200:
            return pacFilesList.json()
        else:
            raise Exception(f"Invalid status code for pac files list :: {pacFilesList.status_code}")
    
    def Create_Custom_Pac_File(self,
                        pacName= "Automation_Created_Pac",
                        privateIPBypass=True, 
                        dnsResolution=True, 
                        plainHostnameRule=True, 
                        ftpBypass=True, 
                        ZPASyntheticRangeBypass=True, 
                        trustURLBypass=True, 
                        customDefaultReturnStatementFlag=False, 
                        customDefaultReturnStatement="", 
                        appProfilePac=True, 
                        localProxyPac=False, 
                        proxyRulesList=[],
                        variableList=[],
                        variableBasedRulesList=[],
                        returnPacURL=False,
                        obfuscateUrl=True
                        ):
        """

        Args:
            privateIPBypass (bool, optional): To add Private IP Bypass Default Rule . Defaults to True.
            dnsResolution (bool, optional): To add DNS Resolution Default Rule. Defaults to True.
            plainHostnameRule (bool, optional): to add Defaul Plain Hostname Rule. Defaults to True.
            ftpBypass (bool, optional): to add default ftp bypass rule. Defaults to True.
            ZPASyntheticRangeBypass (bool, optional): to add default ZPA Bypass rule. Defaults to True.
            trustURLBypass (bool, optional): to add default trust bypass rule. Defaults to True.
            customDefaultReturnStatementFlag (bool, optional): to add add custom default return statement. Defaults to False.
            customDefaultReturnStatement (str, optional): Custom Default return statment will be added if above flag is true. Defaults to "".
            appProfilePac (bool, optional): If you need to create App Profile Pac. Defaults to True.
            localProxyPac (bool, optional): If you need to create . Defaults to False.
            proxyRulesList (list of dict, optional): FORMAT: [{"domain":"domain name", "action": "DIRECT/Some Proxy statement"}]. Right now it uses shExpMatch and will add custom bypass rules as asked by user,  Defaults to [].
            variableList (list of dict , optional): FORMAT: [{"name":"variable name", "value": "custom value"}]
            variableBasedRulesList(list of dict, optional): FORMAT:  variableBasedRulesList=[{'variableName':'tunnel2', 'operator':'==', 'variableValue':'true', 'returnStatement':'PROXY *************:80'}]). This is for using new Macros for DPPC Support for T2 and Trusted network macros.

        Working:
            The predefined pac function is broken into 7 Parts, Out of which 6 are option and customDefaultReturn Statement is must.

            1)When you want to make custom pac file and pass custom rule then you need to pass List of Dictionaries which contains Rules and Action Type.
            Example -->proxyRulesList=[{"domain":"*zoom.us", "action":"PROXY *************:80"},{"domain":"example.com", "action":"DIRECT"}, {"domain":"sbi.com", "action":" PROXY ${ZAPP_LOCAL_PROXY}"},{"domain":"*zoom.us", "action":"PROXY *************:80", "pacFunction":"shExpMatch"},{"domain":"*zoom.us", "action":"PROXY *************:80", "pacFunction":"isPlainHostName"},{"domain":"*zoom.us", "action":"PROXY *************:80", "pacFunction":"dnsDomainIs"},{"domain":"*zoom.us", "action":"PROXY *************:80", "pacFunction":"isResolvable"},{"domain":"*zoom.us", "action":"PROXY *************:80","pacFunction":"dnsResolve"},{"ipAddress":"*******", "action":"PROXY *************:80", "pacFunction":"isInNet", "netmask":"***********"},{"domain":"zoom.us", "action":"PROXY *************:80", "pacFunction":"isInNetEx", "ipPrefix":"64::ff9b/96"} ]
            By Default shExpMatch Function is used for everything.
            Above string converts to 

             if(shExpMatch(host,"*zoom.us"))
		        return "PROXY *************:80";

            if(shExpMatch(host,"example.com"))
                return "DIRECT";

            if(shExpMatch(host,"sbi.com"))
                return " PROXY ${ZAPP_LOCAL_PROXY}";

            if(shExpMatch(host,"*zoom.us"))
                return "PROXY *************:80";

            if(isPlainHostName("*zoom.us"))
                return "PROXY *************:80";

            if(dnsDomainIs(host, "*zoom.us"))
                return "PROXY *************:80";

            if(isResolvable("*zoom.us"))
                return "PROXY *************:80";

            if(dnsResolve("*zoom.us"))
                return "PROXY *************:80";

            if(isInNet(host, "*******", "***********"))
                return "PROXY *************:80";

            if(isInNetEx(host, "64::ff9b/96"))
                return "PROXY *************:80";

            **** Do Not give RETURN Keyword in the action. 
            **** Keys are case senstive.

            2)If user needs to add custom variable such as "tunnel2" and "network" you can create those variables using "variableList".

            Example --> variableList=[{'name':'tunnel2', 'value':'${ZT2_REQUEST}'}, {'name':'network', 'value':'${TRUSTED_NETWORK}'}] Will convert to:

                	var tunnel2 = "${ZT2_REQUEST}";

	                var network = "${TRUSTED_NETWORK}";

            3) If user needs to create custom bypass rule based on variables define above then they needs to use "variableBasedRulesList"

            Example --> variableBasedRulesList=[{'variableName':'tunnel2', 'operator':'==', 'variableValue':'true', 'returnStatement':'PROXY *************:80'}], this will convert to this:
                if( tunnel2 == "true")
		        return "PROXY *************:80";


            4)IF you want to make Local Proxy Pac then  set localProxyPac=True and appProfilePac=False. and if you don't give customDefaultReturnStatement and customDefaultReturnStatementFlag=True then it will use this " return "PROXY ${ZAPP_LOCAL_PROXY}";"

            5)IF you want to create a App Profile Pac then send appProfilePac=True. Default is True only. Custom Return statement can be passed. Default Return statement will be

            6) This Function inturns call self.Create_Pac_File to create Pac.

            ***** Documentation for pac Functions is here:
            ===> IPv4 --> https://developer.mozilla.org/en-US/docs/Web/HTTP/Proxy_servers_and_tunneling/Proxy_Auto-Configuration_PAC_file
            ===> IPv6 --> https://bits.netbeans.org/dev/javadoc/org-netbeans-core-network/org/netbeans/core/network/proxy/pac/PacHelperMethodsMicrosoft.html#isInNetEx-java.lang.String-java.lang.String-
            Returns:
            returns JSON Response Payload and user has to parse the JSON payload to extract URL and send it in MA API to add the URL
        """
        temp_pac_list=[]
        final_pac_string=""


        function_heading="\nfunction FindProxyForURL(url, host) {\n\n"
        
        function_ending="\n}"
        
        private_ip_regex="\t var privateIP = /^(0|10|127|192\\.168|172\\.1[6789]|172\\.2[0-9]|172\\.3[01]|169\\.254|192\\.88\\.99)\\.[0-9.]+$/;\n\n"
        
        dns_resolution_rule="\t var resolved_ip = dnsResolve(host);\n\n"
        
        plain_hostname_rule="\t if (isPlainHostName(host) || isInNet(resolved_ip, \"*********\",\"*************\") || privateIP.test(resolved_ip))\n\t\treturn \"DIRECT\";\n\n"
        
        ftp_bypass_rule="\t if (url.substring(0,4) == \"ftp:\")\n\t\treturn \"DIRECT\";\n\t\t\t\n"
        
        zpa_range_bypass_rule="\t if (isInNet(resolved_ip, \"**********\",\"***********\"))\n\t\treturn \"DIRECT\";\n\t\t\t\n"
        
        trust_bypass_rule="\t if (\n\t\t((localHostOrDomainIs(host, \"trust.zscaler.com\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zscaler.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zscalerone.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zscalertwo.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zscalerthree.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zscalergov.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zsdemo.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zscloud.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zsfalcon.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zdxcloud.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zdxpreview.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zdxbeta.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zsdevel.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zsbetagov.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zspreview.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zscalerten.net\")) || \n\t\t(localHostOrDomainIs(host, \"trust.zdxten.net\")) ) &&\n\t\t(url.substring(0,5) == \"http:\" || url.substring(0,6) == \"https:\")\n\t\t)\n\t\treturn \"DIRECT\";\n\n"
        
        default_proxy_string ="\t return \"PROXY ${GATEWAY_FX}:80; PROXY ${SECONDARY_GATEWAY_FX}:80; DIRECT\";\n"
        
        default_local_proxy_pac_string="\t return \"PROXY ${ZAPP_LOCAL_PROXY}\";"
        
        self._Logger.info("Starting Custom Pac Creation, Appending Function Start String")
        
        temp_pac_list.append(function_heading)

        if len(variableList) >0:
            self._Logger.info("Into Variable List Parsing")

            temp_variable_defination=""
            for index in range(0, len(variableList)):
                temp_variable_defination+="\t  var {} = \"{}\";\n\n".format(variableList[index]['name'], variableList[index]['value'])

            temp_pac_list.append(temp_variable_defination)

            self._Logger.info("Variable List Parsing Complete this is the string appended:\n  {}".format(temp_variable_defination))
        
        if privateIPBypass:
            self._Logger.info("Appending Private IP Bypass String")
            temp_pac_list.append(private_ip_regex)
        
        elif privateIPBypass ==False:
            self._Logger.info("Private IP Bypass Rule is set to False, modifying plain_hostname_rule to remove Private IP Substring")
            plain_hostname_rule=plain_hostname_rule.replace(" || privateIP.test(resolved_ip))", "")
        
        if dnsResolution:
            self._Logger.info("Appending DNS Resolution String")
            temp_pac_list.append(dns_resolution_rule)
            
        if plainHostnameRule:
            self._Logger.info("Appending Plain Hostname String")
            temp_pac_list.append(plain_hostname_rule)
            
        if ftpBypass:
            self._Logger.info("Appending FTP Bypass String")
            temp_pac_list.append(ftp_bypass_rule)
            
        if ZPASyntheticRangeBypass:
            self._Logger.info("Appending ZPA Syncthetic Bypass String")
            temp_pac_list.append(zpa_range_bypass_rule)
        
        if trustURLBypass:
            self._Logger.info("Appending Trust URL Bypass String")
            temp_pac_list.append(trust_bypass_rule)

        if len(variableBasedRulesList)>0:
            self._Logger.info("Into Varibale Based Rules List Parsing")
            temp_variable_based_rule=""
            for index in range(0,len(variableBasedRulesList)):
                temp_variable_based_rule+="\t if( {} {} \"{}\")\n\t\t return \"{}\";\n\n".format(variableBasedRulesList[index]['variableName'],variableBasedRulesList[index]['operator'], variableBasedRulesList[index]['variableValue'], variableBasedRulesList[index]['returnStatement'])
            temp_pac_list.append(temp_variable_based_rule)
            self._Logger.info("Variable Based Rules List parsing completed this is the string appended:\n {}".format(temp_variable_based_rule))


        if len(proxyRulesList) >0:
            self._Logger.info("Into Proxy Rules List Parsing")
            temp_rule=""
            for index in range(0, len(proxyRulesList)):
                temp_rule+=self.Pac_Helper_Function(proxyRulesList[index])
            temp_pac_list.append(temp_rule)
            self._Logger.info("Proxy Rules List Parsing completed this is the string appened:\n {}".format(temp_rule))

        
        if appProfilePac:
            self._Logger.info("Appending App Pac Default Proxy Return Statement")
            if customDefaultReturnStatementFlag:
                default_proxy_string="\t return \"{}\"; \n".format(customDefaultReturnStatement)
                self._Logger.info("Appending Custom App Pac Default Return Statement, this is the string {}".format(default_proxy_string))
            temp_pac_list.append(default_proxy_string)
        
        if localProxyPac:
            self._Logger.info("Appending Local Proxy Pac Default Proxy Return Statement")
            if customDefaultReturnStatementFlag:
                default_local_proxy_pac_string="\t return \"{}\"; \n".format(default_local_proxy_pac_string)
                self._Logger.info("Appending Custom Local Proxy Pac Default Return Statement, this is the string {}".format(default_local_proxy_pac_string))
            temp_pac_list.append(default_local_proxy_pac_string)
            
        self._Logger.info("Apending Funnction End Braces")
        temp_pac_list.append(function_ending)

        self._Logger.info("Creating Final Pac String")
        for element in temp_pac_list:
            final_pac_string+=element
        
        self._Logger.info("Final Proxy String is this: {}".format(final_pac_string))
    
        self._Logger.info("Calling Create_Pac_File")

        pacResponse=self.Create_Pac_File(name = pacName,pacContent=final_pac_string, returnData=True,obfuscateUrl=obfuscateUrl)

        self._Logger.info("Dumping Pac Response: \n {}".format(pacResponse))
        if returnPacURL:
            return pacResponse["pacUrl"]
            self._Logger.info("Pac URL is: {}".format(pacResponse['pacUrl']))
        
        else:
            return pacResponse

    def Pac_Helper_Function(self,
                            proxyRuleDict={}
                            ):
        temp_rule=""

        self._Logger.info("Into Pac_Function_helper")

        if  'pacFunction' not in proxyRuleDict or proxyRuleDict['pacFunction'] == "shExpMatch":
            temp_rule="\t if(shExpMatch(host,\"{}\"))\n\t\t return \"{}\";\n\n".format(proxyRuleDict["domain"], proxyRuleDict["action"])

        elif proxyRuleDict['pacFunction'] == "isPlainHostName":
            temp_rule="\t if(isPlainHostName(\"{}\"))\n\t\t return \"{}\";\n\n".format(proxyRuleDict['domain'], proxyRuleDict['action'])
        
        elif proxyRuleDict['pacFunction'] == "dnsDomainIs":
            temp_rule="\t if(dnsDomainIs(host, \"{}\"))\n\t\t return \"{}\";\n\n".format(proxyRuleDict['domain'], proxyRuleDict['action'])

        elif proxyRuleDict['pacFunction'] == "isResolvable":
            temp_rule="\t if(isResolvable(\"{}\"))\n\t\t return \"{}\";\n\n".format(proxyRuleDict['domain'], proxyRuleDict['action'])

        elif proxyRuleDict['pacFunction'] == "isInNet":
            temp_rule="\t if(isInNet(host, \"{}\", \"{}\"))\n\t\t return \"{}\";\n\n".format(proxyRuleDict['ipAddress'], proxyRuleDict['netmask'],proxyRuleDict['action'])

        elif proxyRuleDict['pacFunction']=="dnsResolve":
            temp_rule="\t if(dnsResolve(\"{}\"))\n\t\t return \"{}\";\n\n".format(proxyRuleDict['domain'], proxyRuleDict['action'])

        elif proxyRuleDict['pacFunction']=="dnsResolveEx":
            temp_rule="\t if(dnsResolveEx(\"{}\"))\n\t\t return \"{}\";\n\n".format(proxyRuleDict['domain'], proxyRuleDict['action'])

        elif proxyRuleDict['pacFunction'] == "isInNetEx":
            temp_rule="\t if(isInNetEx(host, \"{}\"))\n\t\t return \"{}\";\n\n".format(proxyRuleDict['ipPrefix'],proxyRuleDict['action'])
        
        elif proxyRuleDict['pacFunction'] == "isResolvableEx":
            temp_rule="\t if(isResolvableEx(\"{}\"))\n\t\t return \"{}\";\n\n".format(proxyRuleDict['domain'], proxyRuleDict['action'])

        else:
            self._Logger.error("Function Requested is not implemented / Supported.")
            raise Exception("{} Function is not supported/Included as of now.".format(proxyRuleDict))

        return temp_rule

    def Create_Pac_File(self,
                        name="Automation_Created_Pac",  # (str) Name of pac
                        description="Automation_Created_Pac",  # (str) Description of the pac
                        pacCommitMessage="Automation_Created_Pac",  # (str) Commit message while creating pac
                        pacContent=None,  # (str) Pac content in string format
                        primaryProxy=None,
                        # (str) Primary Proxy/SME IP in string format, give default if no IP is to be given
                        secondaryProxy=None,
                        # (str) Secindary Proxy/SME IP in string format, give default if no IP is to be given
                        returnData=False,  # (bool) Defines whether to return complete response of api call
                        obfuscateURL=True # (bool) Set Obfuscate URL
                        ):
        """
        Target: Creates PAC File on ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. Add given input to payload
            3. If pac content is given,it will be added to the payload as it is
            4. If not, then primary and secondary proxies are to be given, if no IP is to be given, give default
            3. Make Api call and returns response if required
        """
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self._Logger.info(f"Create pac file :: Primary Proxy :: {primaryProxy} Secondary Proxy :: {secondaryProxy}")
        if primaryProxy == None and secondaryProxy == None and pacContent == None:
            raise Exception('ERROR :: Either give complete pac content or give primary and secondary proxies, Nothing given !')
        pacPayload = copy.deepcopy(self.Definition.Create_Pac_File_Payload)
        timeStamp = str(time.time())
        if name != "Automation_Created_Pac":
            pacPayload['name'] = name
        else:
            pacPayload['name'] = name + '_' + timeStamp
        pacPayload['description'] = description + '_' + timeStamp
        pacPayload['pacCommitMessage'] = pacCommitMessage + '_' + timeStamp
        pacPayload['domain'] = self.Config['ZIA_USER_ID'].split('@')[-1]
        if pacContent:
            pacPayload['pacContent'] = pacContent
        else:
            if primaryProxy.lower() == 'default':
                self._Logger.info(r"Default primary proxy :: ${GATEWAY_FX}:80")
            else:
                pacPayload['pacContent'].replace(r'${GATEWAY_FX}', primaryProxy)
            if secondaryProxy.lower() == 'default':
                self._Logger.info(r'Default secondary proxy :: ${SECONDARY_GATEWAY_FX}:80')
            else:
                pacPayload['pacContent'].replace(r'${SECONDARY_GATEWAY_FX}', secondaryProxy)

        pacPayload['pacContent']['pacUrlObfuscated'] = 1 if obfuscateURL else 0

        self._Logger.info(
            f"PAC URL :: {self.Definition.Create_Pac_File_Url}\n\nPAC Payload ::\n{json.dumps(pacPayload)}\nPAC HEADERS :: {self.Definition.ZIA_HEADERS}")

        createPac = self.REQ("post",url=self.Definition.Create_Pac_File_Url, headers=self.Definition.ZIA_HEADERS,data=json.dumps(pacPayload),ZIA_Request=True)
        self._Logger.info(f"Create Pac Status Code :: {createPac.status_code}")
        if createPac.status_code == 200:
            self.pacResponse = createPac.json()
            self.ZIA_Activate()
            if returnData: return self.pacResponse
        else:
            raise Exception(f"Invalid status code for pac creation :: {createPac.status_code}")

    def Delete_Pac_File(self,
                        id=None,  # (str/int) ID of the pac to be deleted
                        name=None  # (str) Name of pac to be deleted
                        ):
        """
        Target: Deletes given pac file from ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. If ID is given, add it to delete url and make api call
            3. If self.pacResponse variable exists, pick id from there and add it to delete url and make api call
            4. If Name is given, get list of pacs on ZIA, find the pac with given name, pick its ID, add to delete url and make api call
        """
        self._Logger.info(f"Delete pac file :: {name}")
        url = self.Definition.Delete_Pac_File_Url
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()

        if id:
            self._Logger.info(f"Got createdPacID from id vairable :: {id} ")
            url += str(id)
        elif hasattr(self, 'pacResponse') and 'id' in self.pacResponse.keys() and self.pacResponse['id'] > 0 and self.pacResponse['name'] == name:
            self._Logger.info(f"Got createdPacID from self.pacResponse :: {self.pacResponse['id']} ")
            url += str(self.pacResponse['id'])
        else:
            if name == None:
                raise Exception("Name or ID of pac to be deleted not give, please given either one of these")
            else:
                pacFiles = self.Get_Pac_Files_List()
                isPacFound = False
                for pacFile in pacFiles:
                    if pacFile['name'] == name:
                        self._Logger.info(f"{name} found on ZIA, ID is :: {str(pacFile['id'])}")
                        isPacFound = True
                        url += str(pacFile['id'])
                if isPacFound == False: raise Exception(f"ERROR :: {name} not found on ZIA")
        deletePac = self.REQ("delete",url, headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        if deletePac.status_code == 204:
            self._Logger.info(f"{name} pac file deleted !!!")
            self.ZIA_Activate()
        else:
            raise Exception(f"Invalid status code for delete pac file :: {deletePac.status_code}")

    # ----------------------------------------------------------------------------------------------------------------
    def GET_DEVICE_GROUPS_ID_LIST(self):
        """
        Target: Gets list of available device groups on ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. gets list of available device groups on ZIA and return it
        """
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self._Logger.info("Getting device group ID's")
        epochTime = str(int(time.time() * 1000))
        deviceGroupsList = self.REQ("get",url=self.Definition.Get_Device_Group_ID_List_Url + epochTime,headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        self._Logger.info(f"Device Group ID List Status Code :: {deviceGroupsList.status_code}")
        if deviceGroupsList.status_code == 200:
            return deviceGroupsList.json()
        else:
            raise Exception(f"Invalid status code for device group id list :: {deviceGroupsList.status_code}, {deviceGroupsList}")

    def GET_URL_CATEGORY_LIST(self):
        """
        Target: Gets list of available url categories on ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. gets list of available url categories on ZIA and return it
        """
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self._Logger.info("Getting url category list")
        epochTime = str(int(time.time() * 1000))
        urlCategoryList = self.REQ("get",url=self.Definition.Get_Url_Category_List_Url + epochTime,headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        self._Logger.info(f"Device Group ID List Status Code :: {urlCategoryList.status_code}")
        if urlCategoryList.status_code == 200:
            return urlCategoryList.json()
        else:
            raise Exception(f"Invalid status code for device group id list :: {urlCategoryList.status_code}")

    def GET_SSL_POLICY_LIST(self):
        self._Logger.info("Get SSL Policy List")
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        epochTime = str(int(time.time() * 1000))
        sslPolicyList = self.REQ("get",url=self.Definition.Get_SSL_Policy_List_Url, headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        self._Logger.info(f"Get SSL Policy List status code :: {sslPolicyList.status_code}")
        if sslPolicyList.status_code == 200:
            return sslPolicyList.json()
        else:
            raise Exception(f"Invalid status code for getting ssl policy list :: {sslPolicyList.status_code}")

    def CREATE_SSL_POLICY(self,
                          action,  # (str) values accepted :: inspect,not inspect,block
                          deviceGroups,  # (list) list of deviceGroups :: ['Android','Linux','Mac','Windows','IOS']
                          urlCategories,
                          # (list) url category for which rule is to be applied, make sure exact name is given as present on ZIA :: ['NEWS_AND_MEDIA','TASTELESS']
                          name,  # (str) name of the policy
                          deviceTrustLevels=None, # (list) list ode deviceTrustLevels :: ['HIGH_TRUST','MEDIUM_TRUST','LOW_TRUST','UNKNOWN_DEVICETRUSTLEVEL']
                          show_End_User_Notification=True,
                          returnData=False
                          ):
        """
        Target: Creates SSL Policy on ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. Decide some payload value according to the given action (inspect,block,not inspect)
            3. Get DeviceGroups list, add the given devices and their id's to payload
            4. Get URLCatrogy list, add the given url categories to the payload
            5. Make API Call
        """
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self._Logger.info(f"Create Access Policy :: {action}")
        sslPolicyPayload = copy.deepcopy(self.Definition.Create_SSL_Policy_Payload)
        if action.lower() == 'inspect':
            sslPolicyPayload['action']['decryptSubActions'] = {
                "blockUndecrypt": False,
                "minClientTLSVersion": "CLIENT_TLS_1_0",
                "minServerTLSVersion": "SERVER_TLS_1_0",
                "ocspCheck": True,
                "serverCertificates": "BLOCK"
            }
            sslPolicyPayload['action']['type'] = 'DECRYPT'
        elif action.lower() == "not inspect":
            sslPolicyPayload['action']['doNotDecryptSubActions'] = {
                "bypassOtherPolicies": False,
                "minTLSVersion": "SERVER_TLS_1_0",
                "ocspCheck": False,
                "serverCertificates": "BLOCK"
            }
            sslPolicyPayload['action']["showEUN"] = show_End_User_Notification
            sslPolicyPayload['action']['type'] = "DO_NOT_DECRYPT"
        elif action.lower() == "block":
            sslPolicyPayload['action']["showEUN"] = show_End_User_Notification
            sslPolicyPayload['action']['type'] = "BLOCK"
        else:
            raise Exception(
                f"Invalid action given for SSL Policy :: {action}, Please give inspect,not inspect or block")

        deviceGroupList = self.GET_DEVICE_GROUPS_ID_LIST()
        devicesFound = []
        for devices in deviceGroups:
            for item in deviceGroupList:
                if item['name'] == devices:
                    sslPolicyPayload['deviceGroups'].append({"id": item['id'], "name": item['name']})
                    devicesFound.append(item['name'])
        if len(deviceGroups) == len(devicesFound):
            self._Logger.info("All given deviceGroups added !!!")
        else:
            raise Exception(f"Following device groups not found on ZIA :: {set(deviceGroups) - set(devicesFound)}")

        urlCategoryList = self.GET_URL_CATEGORY_LIST()
        for urlCategory in urlCategories:
            for category in urlCategoryList:
                if category['id'] == urlCategory:
                    sslPolicyPayload['urlCategories'].append(category['id'])
        #print(len(urlCategories), len(sslPolicyPayload['urlCategories']))
        if len(urlCategories) <= len(sslPolicyPayload['urlCategories']):
            self._Logger.info("All given urlCategories added !!!")
        else:
            raise Exception(f"Following url Categories not found on ZIA :: {set(urlCategories) - set(sslPolicyPayload['urlCategories'])}")

        sslPolicyPayload['name'] = name

        if deviceTrustLevels != None:
            sslPolicyPayload['deviceTrustLevels'] = deviceTrustLevels

        self._Logger.info(f"PAYLOAD SSL POLICY :: {json.dumps(sslPolicyPayload)}")
        createSSLPolicy = self.REQ("post",url=self.Definition.Create_SSL_Policy_Url, headers=self.Definition.ZIA_HEADERS,data=json.dumps(sslPolicyPayload),ZIA_Request=True)
        self._Logger.info(f"Status code for create SSL Policy :: {createSSLPolicy.status_code}")
        if createSSLPolicy.status_code == 200:
            self._Logger.info("SSL Policy Created !!!")
            self.createdPolicyData = createSSLPolicy.json()
            if returnData: return self.createdPolicyData
        else:
            if "Duplicate name is not allowed" in createSSLPolicy.json()["message"]:
                self._Logger.info("Deleting exisitng ssl policy")
                self.Delete_SSL_Policy(Name=name)
                self._Logger.info("Making request again\n\n")
                createSSLPolicy = self.REQ("post",self.Definition.Create_SSL_Policy_Url, headers=self.Definition.ZIA_HEADERS,data=json.dumps(sslPolicyPayload),ZIA_Request=True)
                self._Logger.info(f"Status code for create SSL Policy :: {createSSLPolicy.status_code}")
                if createSSLPolicy.status_code == 200:
                    self._Logger.info("SSL Policy Created !!!")
                    self.createdPolicyData = createSSLPolicy.json()
                    if returnData: return self.createdPolicyData
                else:
                    self._Logger.error(f"ERROR :: {createSSLPolicy.json()}")
                    raise Exception(f"Invalid status code for create SSL Policy :: {createSSLPolicy.status_code}")
            else:
                self._Logger.error(f"ERROR :: {createSSLPolicy.json()}")
                raise Exception(f"Invalid status code for create SSL Policy :: {createSSLPolicy.status_code}")

    def Delete_SSL_Policy(self,
                          ID=None,
                          Name=None
                          ):
        self._Logger.info("Delete SSL Policy")
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        url = self.Definition.Delete_SSL_Policy_Url
        if ID:
            self._Logger.info(f"Got policy ID from ID argument :: {ID}")
            url += str(ID)
        elif hasattr(self, 'createdPolicyData') and 'id' in self.createdPolicyData.keys() and self.createdPolicyData['id'] > 0 and self.createdPolicyData['name'] == Name:
            self._Logger.info(f"Got SSL Policy ID from self.createdPolicyData vairable")
            url += str(self.createdPolicyData['id'])
        else:
            if Name == None:
                raise Exception("ERROR :: Neither ID given not Name give, please give atleast one of them")
            else:
                policyList = self.GET_SSL_POLICY_LIST()
                isPolicyFound = False
                for policy in policyList:
                    if policy['name'] == Name:
                        url += str(policy['id'])
                        isPolicyFound = True
                if isPolicyFound == False: raise Exception(F"SSL Policy {Name} not found on ZIA")
        deleteSSLPolicy = self.REQ("delete",url, headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        self._Logger.info(f"Delete SSL Policy Status code :: {deleteSSLPolicy.status_code}")
        if deleteSSLPolicy.status_code == 204:
            self._Logger.info("Given Policy deleted successfully")
        else:
            raise Exception(f"Invalid status code for delete SSL Policy :: {deleteSSLPolicy.status_code}")

    # ----------------------------------------------------------------------------------------------------------------
    def GET_URL_FILTER_POLICY_LIST(self):
        self._Logger.info("Get URL Policy List")
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        urlPolicyList = self.REQ("get",self.Definition.Get_Url_Policy_List_Url, headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        self._Logger.info(f"Get SSL Policy List status code :: {urlPolicyList.status_code}")
        if urlPolicyList.status_code == 200:
            return urlPolicyList.json()
        else:
            raise Exception(f"Invalid status code for getting ssl policy list :: {urlPolicyList.status_code}")

    # ---------------------------------------------------------------------------------------------------------------
    def CREATE_URL_FILTER_POLICY(self,
                                 action,  # (str) values accepted :: allow,caution,block
                                 deviceGroups,
                                 # (list) list of deviceGroups :: ['Android','Linux','Mac','Windows','IOS']
                                 urlCategories,
                                 # (list) url category for which rule is to be applied, make sure exact name is given as present on ZIA :: ['NEWS_AND_MEDIA','TASTELESS']
                                 name,  # (str) name of the policy
                                 deviceTrustLevels=None,
                                 order=1,
                                 # (list) list of device trust levels :: ["UNKNOWN_DEVICETRUSTLEVEL", "LOW_TRUST", "MEDIUM_TRUST"]
                                 returnData=False
                                 ):
        """
        Target: Creates URL Filter Policy on ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. Decide some payload value according to the given action (allow,caution,block)
            3. Get DeviceGroups list, add the given devices and their id's to payload
            4. Get URLCatrogy list, add the given url categories to the payload
            5. Make API Call
        """
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self._Logger.info(f"Create Url Filter Policy :: {action}")
        urlFilterPayload = copy.deepcopy(self.Definition.Create_Url_Policy_Payload)
        if action.lower() == 'allow':
            urlFilterPayload['action'] = "ALLOW"
        elif action.lower() == 'caution':
            urlFilterPayload['action'] = "CAUTION"
        elif action.lower() == 'block':
            urlFilterPayload['action'] = "BLOCK"
        else:
            raise Exception(f"Invalid action given :: {action}, give allow/caution/block")
        deviceGroupList = self.GET_DEVICE_GROUPS_ID_LIST()
        devicesFound = []
        for devices in deviceGroups:
            for item in deviceGroupList:
                if item['name'] == devices:
                    urlFilterPayload['deviceGroups'].append({"id": item['id'], "name": item['name']})
                    devicesFound.append(item['name'])
        if len(deviceGroups) == len(devicesFound):
            self._Logger.info("All given deviceGroups added !!!")
        else:
            raise Exception(f"Following device groups not found on ZIA :: {set(deviceGroups) - set(devicesFound)}")

        urlCategoryList = self.GET_URL_CATEGORY_LIST()
        for urlCategory in urlCategories:
            for category in urlCategoryList:
                if category['id'] == urlCategory:
                    urlFilterPayload['urlCategories'].append(category['id'])
        #print(len(urlCategories), len(urlFilterPayload['urlCategories']))
        if len(urlCategories) <= len(urlFilterPayload['urlCategories']):
            self._Logger.info("All given urlCategories added !!!")
        else:
            raise Exception(f"Following url Categories not found on ZIA :: {set(urlCategories) - set(urlFilterPayload['urlCategories'])}")

        if deviceTrustLevels != None:
            urlFilterPayload['deviceTrustLevels'] = deviceTrustLevels

        if order != 1:
            urlFilterPayload['order'] = order

        urlFilterPayload['name'] = name
        self._Logger.info(f"PAYLOAD :: {json.dumps(urlFilterPayload)}")
        createUrlFilter = self.REQ("post",url=self.Definition.Create_Url_Policy_Url, headers=self.Definition.ZIA_HEADERS, data=json.dumps(urlFilterPayload),ZIA_Request=True)
        self._Logger.info(f"Create Url Filter Status code :: {createUrlFilter.status_code}")
        if createUrlFilter.status_code == 200:
            self._Logger.info("URL Policy Created !!!")
            self.createUrlFilterData = createUrlFilter.json()
            if returnData: return self.createUrlFilterData
        else:
            if "Duplicate name is not allowed" in createUrlFilter.json()["message"]:
                self._Logger.info("Deleting exisitng url policy")
                self.DELETE_URL_FILTER(Name=name)
                self._Logger.info("Making request again\n\n")
                createUrlFilter = self.REQ("post",self.Definition.Create_Url_Policy_Url, headers=self.Definition.ZIA_HEADERS, data=json.dumps(urlFilterPayload),ZIA_Request=True)
                self._Logger.info(f"Status code for create URL Policy :: {createUrlFilter.status_code}")
                if createUrlFilter.status_code == 200:
                    self._Logger.info("URL Policy Created !!!")
                    self.createdPolicyData = createUrlFilter.json()
                    if returnData: return self.createdPolicyData
                else:
                    self._Logger.error(f"ERROR :: {createUrlFilter.json()}")
                    raise Exception(f"Invalid status code for create URL Policy :: {createUrlFilter.status_code}")
            else:
                self._Logger.error(f"ERROR :: {createUrlFilter.json()}")
                raise Exception(f"Invalid status code for create URL Policy :: {createUrlFilter.status_code}")

            self._Logger.error(f"ERROR :: {createUrlFilter.json()}")
            raise Exception(f"Invalid status code for create url filter : {createUrlFilter.status_code}")

    # ---------------------------------------------------------------------------------------------------------------
    def EDIT_URL_FILTER_POLICY(self,
                               policyName,  # (str) URL policy name to be edited
                               policy,  # (dict)updated policy config
                               returnData=False
                               ):
        """
        Target: Edit URL Filter Policy on ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. Prepare updated payload
            3. Make API Call to edit the URL policy
        """
        if policy == None or policyName == None:
            raise Exception("Policy name or policy config to be updated is not provided")
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self._Logger.info(f"Edit Url Filter Policy :: {policyName}")
        getAllURLPolicies = self.GET_URL_FILTER_POLICY_LIST()
        urlFilterPayload = [policy for policy in getAllURLPolicies if policy['name'] == policyName][0]
        url = self.Definition.Edit_Url_Policy_Url + str(urlFilterPayload['id'])
        urlFilterPayload.update(policy)
        self._Logger.info(f"PAYLOAD :: {json.dumps(urlFilterPayload)}")
        editUrlFilter = self.REQ("put",url, headers=self.Definition.ZIA_HEADERS,
                                     data=json.dumps(urlFilterPayload),ZIA_Request=True)
        self._Logger.info(f"Edit Url Filter Status code :: {editUrlFilter.status_code}")
        if editUrlFilter.status_code == 200:
            self._Logger.info("URL Policy Updated !!!")
            self.updatedUrlFilterData = editUrlFilter.json()
            if returnData: return self.updatedUrlFilterData
        else:
            self._Logger.error(f"ERROR :: {editUrlFilter.json()}")
            raise Exception(f"Invalid status code for edit url filter : {editUrlFilter.status_code}")

    # ---------------------------------------------------------------------------------------------------------------

    def DELETE_URL_FILTER(self,
                          ID=None,
                          Name=None
                          ):
        self._Logger.info("Delete URL Policy")
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS['ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        url = self.Definition.Delete_Url_Policy_Url
        if ID:
            self._Logger.info(f"Got policy ID from ID argument :: {ID}")
            url += str(ID)
        elif hasattr(self, 'createUrlFilterData') and 'id' in self.createUrlFilterData.keys() and \
                self.createUrlFilterData['id'] > 0 and self.createdPolicyData and self.createdPolicyData['name'] == Name:
            self._Logger.info(f"Got SSL Policy ID from self.createdPolicyData vairable")
            url += str(self.createdPolicyData['id'])
        else:
            if Name == None:
                raise Exception("ERROR :: Neither ID given not Name give, please give atleast one of them")
            else:
                policyList = self.GET_URL_FILTER_POLICY_LIST()
                isPolicyFound = False
                for policy in policyList:
                    if policy['name'] == Name:
                        url += str(policy['id'])
                        isPolicyFound = True
                if isPolicyFound == False: raise Exception(F"URL Policy {Name} not found on ZIA")
        deleteURLPolicy = self.REQ("delete",url, headers=self.Definition.ZIA_HEADERS,ZIA_Request=True)
        self._Logger.info(f"Delete URL Policy Status code :: {deleteURLPolicy.status_code}")
        if deleteURLPolicy.status_code == 204:
            self._Logger.info("Given Policy deleted successfully")
        else:
            raise Exception(f"Invalid status code for delete SSL Policy :: {deleteURLPolicy.status_code}")

    # ----------------------------------------------------------------------------------------------------------------

    def BROWSER_BASED_AUTH_ACTION_MA(self,  # function to enable/disable browser based auth on MA
                                     toggle="disable",  # (str) defines to enable or disable browser based auth
                                     enable_os=os_version,
                                     # (str) defines to enable browser auth for win or mac, by default current os is updated if toggle is enabled
                                     disable_os=os_version,
                                     # (str) defines to disable browser auth for win or mac, by default current os is updated if toggle is disabled
                                     enablewebview2=False  # bool
                                     ):
        """
        Work Flow
        ----------
            1. Set payload accordingly
            2. Send Post request to MA to do the required changes

        Prerequiste is that we should have IDP configured on SMUI
        """
        if enablewebview2==True:
            self.Definition.BROWSER_AUTH_MA_PAYLOAD['installWebView2'] = "1"
        else:
            self.Definition.BROWSER_AUTH_MA_PAYLOAD['installWebView2'] = "0"
            


        if 'enable' in toggle.lower():
            self.Definition.BROWSER_AUTH_MA_PAYLOAD['browserAuth'] = "1"
            if "mac" in enable_os.lower():
                self.Definition.BROWSER_AUTH_MA_PAYLOAD['platformType'] = '4'
            elif "win" in enable_os.lower() or "windows" in enable_os.lower():
                self.Definition.BROWSER_AUTH_MA_PAYLOAD['platformType'] = '3'
            else:
                raise Exception("Invalid OS Type given, please give win/windows/mac")
        elif 'disable' in toggle.lower():
            self.Definition.BROWSER_AUTH_MA_PAYLOAD['browserAuth'] = "0"
            if "mac" in disable_os.lower():
                self.Definition.BROWSER_AUTH_MA_PAYLOAD['platformType'] = '4'
            elif "win" in disable_os.lower() or "windows" in disable_os.lower():
                self.Definition.BROWSER_AUTH_MA_PAYLOAD['platformType'] = '3'
            else:
                self._Logger.error("Invalid OS Type given, please give win/windows/mac")
                raise Exception("Invalid OS Type given, please give win/windows/mac")
        else:
            self._Logger.error("Invalid value given for toggle, please give enable or disable")
            raise Exception("Invalid value given for toggle, please give enable or disable")

        # self.Definition.BROWSER_AUTH_MA_HEADER['auth-token']=self.Authenticate_To_Mobile_Admin()
        self._Logger.info("UPDATE BROWSER BASED AUTH :: URL : {}\nPAYLOAD : {}\n HEADER : {}\n".format(
            self.Definition.BROWSER_AUTH_MA_URL, json.dumps(self.Definition.BROWSER_AUTH_MA_PAYLOAD),
            self.Definition.BROWSER_AUTH_MA_HEADER))

        updateBrowserAuthSetting = self.REQ(request_type="post", url=self.Definition.BROWSER_AUTH_MA_URL,
                                            data=json.dumps(self.Definition.BROWSER_AUTH_MA_PAYLOAD),
                                            headers=self.Definition.BROWSER_AUTH_MA_HEADER)

        if updateBrowserAuthSetting.status_code == 200:
            if updateBrowserAuthSetting.json()['success'] != 'true':
                self._Logger.error("ERROR with browser based auth : {}".format(updateBrowserAuthSetting.json()))
                raise Exception("ERROR with browser based auth : {}".format(updateBrowserAuthSetting.json()))
            else:
                self._Logger.info("Browser Based Auth Settings Updated")
        else:
            self._Logger.error("Invalid status code for updating browser based auth settings on MA, {}".format(updateBrowserAuthSetting.status_code))
            raise Exception("Invalid status code for updating browser based auth settings on MA, {}".format(updateBrowserAuthSetting.status_code))

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def clear_temp_file(self):  # Function to clear temp file information
        """
        Work Flow
        ----------
            1. if temp file doesnt exist, all good
            2. if it does, clear its contents
        """
        if not os.path.exists(self.temp_file):
            self._Logger.info("temp file not there, exiting..")
            return
        open(self.temp_file, "w").close()

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("enable/disable zpa from MA")
    def Toggle_ZPA(self,  # Function to Toggle ZPA from MA
                   action=True):  # (bool)    True turns on ZPA, False turns off
        """
        Work Flow
        ----------
            1.  Get payloads from Definitions. get Auth Token
            2.  Set 1/0 accordingly to Enable/Disable ZPA
            3.  send post request. validate response is 200
        """
        # self.Definition.ZPA_HEADER['auth-token']=self.Authenticate_To_Mobile_Admin()
        self.Definition.ZPA_PAYLOAD["enableForAll"] = (1 if action == True else 0)
        self._Logger.info(f"{str(self.Definition.ZPA_HEADER)},{str(self.Definition.ZPA_PAYLOAD)},{str(self.Definition.ZPA_URL)}")
        Profile = self.REQ(request_type="post", url=self.Definition.ZPA_URL, headers=self.Definition.ZPA_HEADER,
                           data=json.dumps(self.Definition.ZPA_PAYLOAD))
        # profile = self.REQ("post",url=self.Definition.ZPA_URL, headers=self.Definition.ZPA_HEADER, data=json.dumps(self.Definition.ZPA_PAYLOAD))
        # Validate success
        if Profile.status_code == 200:
            self._Logger.info("ZPA status: " + str("On" if action else "Off") + " response:" + str(Profile.json()))
        else:
            self._Logger.error("Error :: Toggle ZPA in MA didnt go well!")
            raise Exception("Error :: Toggle ZPA in MA didnt go well!")
    
    

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Toggle_ZDX(self,  # Function to Toggle ZDX from MA
                   action=True):  # (bool)    True turns on ZDX, False turns off
        """
        Work Flow
        ----------
            1.  Get payloads from Definitions. get Auth Token
            2.  Set 1/0 accordingly to Enable/Disable ZDX
            3.  send post request. validate response is 200
        """
        # self.Definition.ZDX_HEADER['auth-token']=self.Authenticate_To_Mobile_Admin()
        self.Definition.ZDX_PAYLOAD["enableForAll"] = (1 if action == True else 0)
        Profile = self.REQ(request_type="post", url=self.Definition.ZDX_URL, headers=self.Definition.ZDX_HEADER,data=json.dumps(self.Definition.ZDX_PAYLOAD))
        # Validate success
        if Profile.status_code == 200:
            self._Logger.info("ZDX status: " + str("On" if action else "Off") + " response:" + str(Profile.json()))
        else:
            self._Logger.error("Toggle ZDX in MA didnt go well!")
            raise Exception("Error :: Toggle ZDX in MA didnt go well!")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Register Device with IDP username")
    def Register_Device_with_IDP_Username(self,  # Function to register device with IDP Username
                                          action=True):  # (bool)    set to True to fill SAML Username automatically
        """
        Work Flow
        ----------
            1. auth to MA.
            2. toggle register device with IDP Username option
            3. validate response is 200
        """
        # self.Definition.Register_Device_with_IDP_Username_Header['auth-token']=self.Authenticate_To_Mobile_Admin()

        if action:
            self.Definition.Register_Device_with_IDP_Username_Payload["enableAutofillUsername"] = "1"
            self.Definition.Register_Device_with_IDP_Username_Payload["enableZpaAuthUserName"] = "1"

        else:
            self.Definition.Register_Device_with_IDP_Username_Payload["enableAutofillUsername"] = "0"
            self.Definition.Register_Device_with_IDP_Username_Payload["enableZpaAuthUserName"] = "0"

        Profile = self.REQ(request_type="post", url=self.Definition.Register_Device_with_IDP_Username_Url,
                           headers=self.Definition.Register_Device_with_IDP_Username_Header,
                           data=json.dumps(self.Definition.Register_Device_with_IDP_Username_Payload))
        # Validate success
        if Profile.status_code == 200:
            self._Logger.info("Username autofill status: " + str(action) + " response:" + str(Profile.json()))
        else:
            self._Logger.error("Enabling autofill Username didnt go well!")
            raise Exception("Error :: Enabling autofill Username didnt go well!")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Get_Client_Connector_IDP_Token(self,
                                       name
                                       ):
        # self.Definition.Get_Client_Connector_IDP_Token_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
        getAvailableTokens = self.REQ(request_type="get", url=self.Definition.Get_Client_Connector_IDP_Token_List,
                                      headers=self.Definition.Get_Client_Connector_IDP_Token_Header)
        self._Logger.info(f"Get Avaialable Client Connector Token Status Code :: {getAvailableTokens.status_code}")
        if getAvailableTokens.status_code == 200:
            tokenFound = False
            for idpToken in getAvailableTokens.json()['idpTokens']:
                if idpToken['description'] == name:
                    self._Logger.info(f"{name} token found !!!")
                    tokenFound = idpToken['token']
            self._Logger.info(f'Returning {tokenFound}')
            return tokenFound
        else:
            raise Exception("ERROR :: Invalid status code for getting Client Connector IDP Token")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------

    def Create_Client_Connector_IDP_Token(self,
                                          name="Automation_Token",
                                          password="Auto@123"
                                          ):

        token = self.Get_Client_Connector_IDP_Token(name=name)
        if token != False:
            return token
        else:
            self.Definition.Create_Client_Connector_IDP_Token_Payload['token'], self.Definition.Create_Client_Connector_IDP_Token_Payload['description'] = password, name

            createToken = self.REQ("post",self.Definition.Create_Client_Connector_IDP_Token_Url,
                                        headers=self.Definition.Get_Client_Connector_IDP_Token_Header,
                                        data=json.dumps(self.Definition.Create_Client_Connector_IDP_Token_Payload))

            self._Logger.info(f"Create Client Connector IDP Token Status Code :: {createToken.status_code}")
            if createToken.status_code == 200:
                token = self.Get_Client_Connector_IDP_Token(name=name)
                if token != False:
                    return token
                else:
                    raise Exception("Token Not found even after creating !!!")
            else:
                raise Exception("ERROR :: Invalid status code for creating Client Connector IDP Token")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def getIDEndPoint(self,
                      ):

        '''
            Use: To get the ID of endpoint Integration from MA
            --> This function can be used as a helper function.
        '''
        # self.Definition.getEndpoint_Header['auth-token']=self.Authenticate_To_Mobile_Admin()

        response = self.REQ(request_type="get",url=self.Definition.getEndpoint_URL, headers=self.Definition.getEndpoint_Header)

        if response.status_code >= 200 and response.status_code < 300:
            response_id = response.json()['id']
            self._Logger.info('Require ID is {}'.format(response_id))
        else:
            line = "Response code from request is {}, request not made!".format(response.status_code)
            self._Logger.error(line)
            raise Exception(line)

        return response_id

    # -------------------------------------------------------------------------------------------------------------------------
    def changeZCCPort(self,
                      port_to_change=9000  # (int/str) port number to set on MA
                      ):
        '''
            To change the port using API one needs to follow 2 steps:
                1. Get ID of End point Integration
                2. Using ID update payload and change Port.
        '''

        response_id = self.getIDEndPoint()

        if response_id == None:
            raise Exception('Response ID is none!')

        # self.Definition.endpoint_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()

        self.Definition.endpoint_Payload['id'] = response_id
        self.Definition.endpoint_Payload['tunnelTCPLocalPort'] = str(port_to_change)

        response = self.REQ(request_type="post",
                            url=self.Definition.endpoint_URL, headers=self.Definition.endpoint_Header,
                            data=json.dumps(self.Definition.endpoint_Payload)
                            )

        self._Logger.info(response.json())

        if response.status_code >= 200 and response.status_code < 300:
            if response.json()['success'] == 'true':
                self._Logger.info('Request to change Port Successfull!')
            else:
                line = 'Status code for Port change is {}, but request is not successfull'.format(response.status_code)
                self._Logger.error(line)
                raise Exception(line)
        else:
            line = 'Status code for Port change is {}, Request no successfull!'.format(response.status_code)
            self._Logger.error(line)
            raise Exception(line)

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("configure AUP")
    def configure_AUP(self,  # Function to Toggle ZDX from MA
                      aup_frequency="never",  # (str)     aup frequency setter (never/after user enrolment)
                      aup_string=None,  # (str)     sring to be seen in AUP page
                      aup_days=None):  # (str)     days on which we want AUP to be seen
        """
        Work Flow
        ----------
            1.  Get payloads from Definitions. get Auth Token
            2.  define how frequently aup has to come, along with aup string and aup days
            3.  send post request. validate response is 200
        """
        # self.Definition.configure_aup_header['auth-token']=self.Authenticate_To_Mobile_Admin()
        if aup_frequency == "never":
            self.Definition.configure_aup_payload["aup_type"] = 0
            aup_string = "Acceptable Use Policy is not configured for your company"
        elif aup_frequency == "after user enrolment":
            self.Definition.configure_aup_payload["aup_type"] = 1
        elif aup_frequency == "daily":
            self.Definition.configure_aup_payload["aup_type"] = 3
        elif aup_frequency == "weekly":
            self.Definition.configure_aup_payload["aup_type"] = 4
        elif aup_frequency == "custom" and aup_days != None:
            self.Definition.configure_aup_payload["aup_days"] = aup_days
            self.Definition.configure_aup_payload["aup_type"] = 5
        if aup_string != None:
            self.Definition.configure_aup_payload["aup_data"] = "<b>" + str(aup_string) + "</b>"
        Profile = self.REQ(request_type="post", url=self.Definition.configure_aup_url,
                           headers=self.Definition.configure_aup_header,
                           data=json.dumps(self.Definition.configure_aup_payload))
        # Validate success
        if Profile.status_code == 200:
            self._Logger.info("AUP status: " + str(aup_frequency) + " response:" + str(Profile.json()))
        else:
            self._Logger.error("Configuring AUP didnt go well!, status code {}".format(Profile.status_code))
            raise Exception("Error :: Configuring AUP didnt go well!")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Toggle_Packet_Capture(self,  # Function to Enable/Disable Packet capture
                              action="enable"):  # (str)     "enable" or "disable"
        """
        Work Flow
        ----------
            1.  Get payloads from Definitions. get Auth Token
            2.  Set "enable"/"disable" accordingly to Enable/Disable Packet Capture in MA
            3.  send post request. validate response is 200
        """
        self.Definition = admin_res.admin_res(self.cloud)
        ID = str(self.Get_PrivacyInfo())
        self.Definition.toggle_packet_capture_payload["id"] = ID
        self.Definition.toggle_packet_capture_payload["enablePacketCapture"] = "1" if action == "enable" else "0"
        Profile = self.REQ(request_type="post", url=self.Definition.toggle_packet_capture_url,
                           headers=self.Definition.toggle_packet_capture_header,
                           data=json.dumps(self.Definition.toggle_packet_capture_payload))
        # Validate success
        if Profile.status_code == 200:
            self._Logger.info("Packet Capture status: " + str(action) + " response:" + str(Profile.json()))
        else:
            self._Logger.error("Enabling packet capture didnt go well!")
            raise Exception("Error :: Enabling packet capture didnt go well!")
        
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Toggle_Collect_Location_Info(self,  # Function to Enable/Disable Collect Location
                              action="enable"):  # (str)     "enable" or "disable"
        """
        Work Flow
        ----------
            1.  Get payloads from Definitions. get Auth Token
            2.  Set "enable"/"disable" accordingly to Enable/Disable Collect Location in MA
            3.  send post request. validate response is 200
        """
        self.Definition = admin_res.admin_res(self.cloud)
        ID = str(self.Get_PrivacyInfo())
        self.Definition.toggle_packet_capture_payload["id"] = ID
        self.Definition.toggle_packet_capture_payload["collectZdxLocation"] = "1" if action == "enable" else "0"
        Profile = self.REQ(request_type="post", url=self.Definition.toggle_packet_capture_url,
                           headers=self.Definition.toggle_packet_capture_header,
                           data=json.dumps(self.Definition.toggle_packet_capture_payload))
        # Validate success
        if Profile.status_code == 200:
            self._Logger.info("Collect Location Info: " + str(action) + " response:" + str(Profile.json()))
        else:
            self._Logger.error("Enabling collect location info didnt go well!")
            raise Exception("Error :: Enabling collect location info didnt go well!")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Toggle_Report_an_Issue(self,  # Function to enable/disable Report an issue on MA
                               action=True,  # (bool)    True to enable, False to disable
                               email_id="<EMAIL>",
                               # (str)     email id to be mentioned by default, when reporting issue from ZCC
                               hide_logging_controls=False
                               ):
        """
        Work Flow
        ----------
            1.  Get payloads from Definitions. get Auth Token
            2.  Set True/False accordingly to Enable/Disable Report an Issue option for ZCC in MA
            3.  send post request. validate response is 200
        """
        if action:
            self.Definition.Toggle_Report_an_Issue_Payload["supportAdminEmail"] = email_id
            self.Definition.Toggle_Report_an_Issue_Payload["supportEnabled"] = "1"
        else:
            self.Definition.Toggle_Report_an_Issue_Payload["supportAdminEmail"] = ""
            self.Definition.Toggle_Report_an_Issue_Payload["supportEnabled"] = "0"
        self.Definition.Toggle_Report_an_Issue_Payload["disableSupportability"] = (
            "1" if hide_logging_controls else "0")

        #self._Logger.info(f"Payload is : {json.dumps(self.Definition.Toggle_Report_an_Issue_Payload)}")
        # self.Definition.Toggle_Report_an_Issue_Header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        Profile = self.REQ(request_type="post", url=self.Definition.Toggle_Report_an_Issue_URL,
                           headers=self.Definition.Toggle_Report_an_Issue_Header,
                           data=json.dumps(self.Definition.Toggle_Report_an_Issue_Payload))
        # Validate success
        if Profile.status_code == 200:
            self._Logger.info("Report an issue status: " + str(action) + " response:" + str(Profile.json()))
        else:
            self._Logger.error("Toggle Report an Issue in MA didnt go well!")
            raise Exception("Error :: Toggle Report an Issue in MA didnt go well!")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Toggle_Remote_Fetch_Logs(self,  # Function to enable/disable Report an issue on MA
                                 action=True,  # (bool)    True to enable, False to disable
                                 email_id="<EMAIL>"):  # (str)     email id to be mentioned by default, when reporting issue from ZCC
        """
        Work Flow
        ----------
            1.  Get payloads from Definitions. get Auth Token
            2.  Set True/False accordingly to Enable/Disable Report an Issue option for ZCC in MA
            3.  send post request. validate response is 200
        """
        if action:
            self.Definition.Toggle_Report_an_Issue_Payload["supportAdminEmail"] = email_id
            self.Definition.Toggle_Report_an_Issue_Payload["fetchLogsForAdminsEnabled"] = "1"
        else:
            self.Definition.Toggle_Report_an_Issue_Payload["supportAdminEmail"] = ""
            self.Definition.Toggle_Report_an_Issue_Payload["fetchLogsForAdminsEnabled"] = "0"
        self.Definition.Toggle_Report_an_Issue_Payload["supportEnabled"] = "0"
        self._Logger.info(f"Payload is : {json.dumps(self.Definition.Toggle_Report_an_Issue_Payload)}")
        # self.Definition.Toggle_Report_an_Issue_Header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        Profile = self.REQ(request_type="post", url=self.Definition.Toggle_Report_an_Issue_URL,
                           headers=self.Definition.Toggle_Report_an_Issue_Header,
                           data=json.dumps(self.Definition.Toggle_Report_an_Issue_Payload))
        # Validate success
        if Profile.status_code == 200:
            self._Logger.info("Remote fetch log toggle status: " + str(action) + " response:" + str(Profile.json()))
        else:
            self._Logger.error("Remote fetch log toggle in MA didnt go well!")
            raise Exception("Error :: Remote fetch log toggle in MA didnt go well!")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Toggle_Auto_Reauth_ZPA(self,
                               action=False
                               ):
        """
        Work Flow
        ----------
            1.  Get payloads from Definitions. get Auth Token
            2.  Set True/False accordingly to Enable/Disable Auto Reauth for ZPA in MA
            3.  send post request. validate response is 200
        """
        if action:
            self.Definition.Toggle_Report_an_Issue_Payload["zpaReauthEnabled"] = "1"
        else:
            self.Definition.Toggle_Report_an_Issue_Payload["zpaReauthEnabled"] = "0"
        self._Logger.info(f"Payload is : {json.dumps(self.Definition.Toggle_Report_an_Issue_Payload)}")
        # self.Definition.Toggle_Report_an_Issue_Header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        Profile = self.REQ(request_type="post", url=self.Definition.Toggle_Report_an_Issue_URL,
                           headers=self.Definition.Toggle_Report_an_Issue_Header,
                           data=json.dumps(self.Definition.Toggle_Report_an_Issue_Payload))
        # Validate success
        if Profile.status_code == 200:
            self._Logger.info("Auto Reauth ZPA Status: " + str(action) + " response:" + str(Profile.json()))
        else:
            self._Logger.error("Auto Reauth ZPA in MA didnt go well!")
            raise Exception("Error :: Auto Reauth ZPA in MA didnt go well!")

    def Edit_App_Fail_Open(self,
                           captivePortal_minutes='10',
                           public_service_edge="direct",
                           tunnel_fail='block'
                           ):
        if public_service_edge.lower() == "direct" or public_service_edge.lower() == "block":
            pass
        else:
            raise Exception("Invalid value given for public_service_edge, give direct or block")

        if tunnel_fail.lower() == "direct" or tunnel_fail.lower() == "block":
            pass
        else:
            raise Exception("Invalid value given for tunnel_fail, give direct or block")

        # self.Definition.Toggle_Report_an_Issue_Header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        companyID = self.REQ(request_type="get", url=self.Definition.Get_Company_Details_Url,
                             headers=self.Definition.Toggle_Report_an_Issue_Header)
        if companyID.status_code == 200:
            details = companyID.json()[0]
            self.Definition.App_Fail_Open_Payload["id"] = details['id']
        else:
            raise Exception(f"Invalid status code for getting company details :: {companyID.status_code}")
        self.Definition.App_Fail_Open_Payload[
            'enableWebSecOnProxyUnreachable'] = 0 if public_service_edge.lower() == "direct" else 1
        self.Definition.App_Fail_Open_Payload[
            'enableWebSecOnTunnelFailure'] = 0 if public_service_edge.lower() == "direct" else 1

        self._Logger.info(
            f"URL :: {self.Definition.App_Fail_Open_Url}\n PAYLOAD :: {json.dumps(self.Definition.App_Fail_Open_Payload)}")

        editAppFailOpen = self.REQ(request_type="post", url=self.Definition.App_Fail_Open_Url,
                                   data=json.dumps(self.Definition.App_Fail_Open_Payload),
                                   headers=self.Definition.Toggle_Report_an_Issue_Header)

        if editAppFailOpen.status_code == 200:
            if editAppFailOpen.json()['success'] == 'true':
                self._Logger.info("App fail open edited")
            else:
                raise Exception(f"Status code is 200 but json has error :: {editAppFailOpen.json()}")
        else:
            raise Exception(f"Invalid status code :: {editAppFailOpen.status_code}")



 #-------------------------------------------------------------------------------------------------------------------------------------------------------

    def get_ZDX_Enablement(self):
        groupEntitlementEnabledZdx=self.REQ(request_type="get",url=self.Definition.Get_ZDX_Entitlement_URL,headers=self.Definition.Get_ZDX_Entitlement_header)

        if groupEntitlementEnabledZdx.status_code==200:
            self._Logger.info("ZDX Group Entitlement Details - success")
            userDict=groupEntitlementEnabledZdx.json()
            if userDict['upmEnableForAll']==1:                             ####zdx zdx enabled for all
                self._Logger.info("ZDX is enabled for ALL")
                return True
            return False
    
    def set_ZDX_Enablement_Action(self, action):
        isEnabled = self.get_ZDX_Enablement()
        #print(isEnabled, upmdata)
        if action and not isEnabled: 
            self.Definition.SET_ZDX_Entitlement_payload['enableForAll'] = 1 
            response = self.REQ(request_type="post",url=self.Definition.Set_ZDX_Entitlement_URL,headers=self.Definition.Set_ZDX_Entitlement_header, data = json.dumps(self.Definition.SET_ZDX_Entitlement_payload))
            if response.status_code == 200:
                response = response.json()
                if response["success"] == "true": self._Logger.info("ZDX has been Enabled on MA portal for all."); return True
                else: self._Logger.error(f"Could not enable ZDX on MA portal {response['error']}"); return False
        
        if action and isEnabled:
            self._Logger.info("ZDX already enabled on MA portal"); return True

        if not action and isEnabled:
            self.Definition.SET_ZDX_Entitlement_payload['enableForAll'] = 0 
            response = self.REQ(request_type="post",url=self.Definition.Set_ZDX_Entitlement_URL,headers=self.Definition.Set_ZDX_Entitlement_header, data = json.dumps(self.Definition.SET_ZDX_Entitlement_payload))
            if response.status_code == 200:
                response = response.json()
                if response["success"] == "true": self._Logger.info("ZDX has been Disabled on MA portal for all."); return True
                else: self._Logger.error(f"Could not disable ZDX on MA portal {response['error']}"); return False
        
        if not action and not isEnabled:
            self._Logger.info("ZDX already disabled on MA portal"); return True


    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Get_Latest_ZCC(self,  # Function to download ZCC installer from Mobile Manager
                       branch,  # The ZCC branch from which we download ZCC Installer
                       version=None):  # choose a specific ZCC version to install or download latest installer
        """
        Work Flow
        ----------
            1.  Get payloads from Definitions.
            2.  choose a valid branch. version will take either the build number or "latest"
            3.  download this ZCC to "zapp_archive" folder as zapp.exe if windows or zapp.app if mac
        """
        self.Definition = admin_res.admin_res(self.cloud)
        url = self.Definition.latest_zapp_url.format(branch)
        # print("Latest zapp version Fetching..")
        # print("url :: " + str(url))
        # print("header :: " + str(self.Definition.latest_zapp_header))
        response1 = self.REQ("get",url=url, headers=self.Definition.latest_zapp_header)
        if response1.status_code == 200:
            pass
            #print("Success :: fetched zapp list")
        else:
            self._Logger.error("Latest zapp version Fetching didnt go well!")
            raise Exception("Latest zapp version Fetching didnt go well!")

        if version != None and version != "latest":  # download custom zapp
            for i in range(len(response1.json())):
                if str(version) in response1.json()[i]["buildversion"]:
                    zapp_download_url = response1.json()[i]["deployurl"].strip()
                    break
        else:
            zapp_download_url = response1.json()[0]["deployurl"].strip()
        # print("zapp version Downloading..")
        # print("url :: " + str(zapp_download_url))
        # print("header :: " + str(self.Definition.zapp_download_header))
        response2 = self.REQ("get",url=zapp_download_url)  # headers = self.Definition.zapp_download_header
        if response2.status_code == 200:
            self._Logger.info("Success :: fetched zapp build")
            if self.os_version != "MAC":
                open(os.path.dirname(os.getcwd()).replace('\\', '/') + '/zapp_archive/zapp.exe', 'wb').write(
                    response2.content)
            else:
                open(os.path.dirname(os.getcwd()).replace('\\', '/') + '/zapp_archive/zapp.app', 'wb').write(
                    response2.content)
        else:
            self._Logger.error("zapp version Downloading didnt go well!")
            raise Exception("zapp version Downloading didnt go well!")

# ------------------------------------------------------------------------------------------------------------------------------------------------------

    def get_Latest_ZCC_Version_From_URL_Available_On_MA(self,  # Function to get zcc versions available on MA not mobile manager
                                               buildType, applicationVersion=None, systype="86"  # (str) build version to be applied on MA
                                               ):
        # AuthToken = self.Authenticate_To_Mobile_Admin()
        # self.Definition.get_zcc_list_from_ma_header['auth-token']=AuthToken
        zccList = self.REQ(request_type="get", url=self.Definition.get_newzcc_list_from_ma_url,
                           headers=self.Definition.get_zcc_list_from_ma_header)
        self._Logger.info(f'Response Code : {zccList.status_code}')
        if zccList.status_code == 200:
            zccList = zccList.json()['applications']
            if os_version == "MAC":
                for buildDict in zccList:
                    if ('.zip' in buildDict['downloadLink']) and (buildType == buildDict['buildType']):
                        if applicationVersion == None or applicationVersion == buildDict['applicationVersion']:
                            return buildDict['applicationVersion'], buildDict['clientApplicationId'], buildDict['downloadLink']
            elif os_version == "Windows":
                for buildDict in zccList:
                    if buildType == buildDict['buildType']:
                        if applicationVersion == None or applicationVersion == buildDict['applicationVersion']:
                            if systype == "64": return buildDict['applicationVersion'], buildDict['clientApplicationId'], buildDict['secondaryDownloadLink64bit']
                            else: return buildDict['applicationVersion'], buildDict['clientApplicationId'], buildDict['secondaryDownloadLink']
        else:
            self._Logger.error(f'Status code is : {zccList.status_code}')
            raise Exception(f"ERROR :: Status code is {zccList.status_code}")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------

    def get_Latest_ZCC_Version_Available_On_MA(self,  # Function to get zcc versions available on MA not mobile manager
                                               buildVersion  # (str) build version to be applied on MA
                                               ):
        # AuthToken = self.Authenticate_To_Mobile_Admin()
        # self.Definition.get_zcc_list_from_ma_header['auth-token']=AuthToken
        zccList = self.REQ(request_type="get", url=self.Definition.get_zcc_list_from_ma_url,
                           headers=self.Definition.get_zcc_list_from_ma_header)
        self._Logger.info(f'Response Code : {zccList.status_code}')
        if zccList.status_code == 200:
            zccList = zccList.json()['applications']
            if os_version == "MAC":
                for buildDict in zccList:
                    if ('.zip' in buildDict['downloadLink']) and (buildVersion in buildDict['applicationVersion']):
                        return buildDict['applicationVersion'], buildDict['clientApplicationId']

            else:
                for buildDict in zccList:
                    if buildVersion in buildDict['applicationVersion']:
                        #print(buildDict['applicationVersion'], buildDict['clientApplicationId'])
                        return buildDict['applicationVersion'], buildDict['clientApplicationId']
        else:
            self._Logger.error(f'Status code is : {zccList.status_code}')
            raise Exception(f"ERROR :: Status code is {zccList.status_code}")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------

    def set_Latest_Build_For_App_Update_On_MA(self,
                                              buildVersion=None,  # (str) build version to be applied on MA
                                              disableAutoUpdate=False
                                              ):
        # AuthToken = self.Authenticate_To_Mobile_Admin()

        if disableAutoUpdate:
            pass
        else:
            if buildVersion == None:
                buildVersion = Constants.Utils.build_for_MA
            zccBuild, applicationVersionId = self.get_Latest_ZCC_Version_Available_On_MA(buildVersion)
            dictInPayload = "macEnabledImage" if os_version == "MAC" else "windowsEnabledImage"
            # ---- setting up payload for enabling zcc
            payload = self.Definition.set_Latest_Build_For_App_Update_On_MA_payload.copy()
            payload[dictInPayload]['applicationVersion'] = zccBuild
            payload[dictInPayload]['applicationVersionId'] = applicationVersionId
            payload[dictInPayload]['use64BitInstallerForWindows'] = "0"

        # self.Definition.get_zcc_list_from_ma_header['auth-token']=AuthToken

        self._Logger.info(f"Payload : {json.dumps(self.Definition.set_Latest_Build_For_App_Update_On_MA_payload)}")
        setLatestBuild = self.REQ(request_type="post", url=self.Definition.set_Latest_Build_For_App_Update_On_MA_url,
                                  headers=self.Definition.get_zcc_list_from_ma_header,
                                  data=json.dumps(self.Definition.set_Latest_Build_For_App_Update_On_MA_payload))

        if setLatestBuild.status_code == 200:
            if setLatestBuild.json()['success'] == 'true':
                #print(setLatestBuild.json())
                self._Logger.info(f"Build {zccBuild} applied on MA for {os_version} for auto update")
                Constants.Utils.current_build_set_on_ma_for_auto_update = zccBuild
            else:
                self._Logger.error(f"ERROR :: Status code is 200 but json response is {setLatestBuild.json()}")
                raise Exception(f"ERROR :: Status code is 200 but json response is {setLatestBuild.json()}")
        else:
            self._Logger.error(f"ERROR :: Status code is {setLatestBuild.status_code}")
            raise Exception(f"ERROR :: Status code is {setLatestBuild.status_code}")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def check_in_audit_logs(self,  # Function to check audit logs in MA
                            startTime=None,  # (time)    time stamp for start time (time.time object)
                            endTime=None,  # (time)    time stamp for end time (time.time object)
                            category=None):  # (str)     category as can be seen in app profiles
        """
        Work Flow
        ----------
            1. get auth token. set start time and end time, the period in which logs have to be checked.
            2. send POST request to MA.
            3. verify a log exists in audit logs with same category as expected.
        """
        # AuthToken = self.Authenticate_To_Mobile_Admin()
        # self.Definition.AUDIT_LOGS_header['auth-token'] = AuthToken
        self.Definition.AUDIT_LOGS_payload["endTime"] = endTime
        self.Definition.AUDIT_LOGS_payload["startTime"] = startTime
        response = self.REQ(request_type="post", url=self.Definition.AUDIT_LOGS_url,
                            headers=self.Definition.AUDIT_LOGS_header,
                            data=json.dumps(self.Definition.AUDIT_LOGS_payload))
        self._Logger.info('\n\n\n--------------\nResponse is {}\n-------------\n\n'.format(response))

        if response.status_code == 200:
            json_file = response.json()
            self._Logger.info('\n\n\n--------------\nAll results in JSON file are {}\n-------------\n\n'.format(json_file))
            if category:
                logs = [log for log in json_file if category == log['category']]

                self._Logger.info('\n\n\n--------------\nFiltered audit Logs are {}\n-------------\n\n'.format(logs))

                if len(category) > 0:
                    self._Logger.info('Required Logs for {} found in Audit logs of MA from time interval {} to {}!'.format(category,
                                                                                                             time.strftime(
                                                                                                                 '%Y-%m-%d %H:%M:%S',
                                                                                                                 time.localtime(
                                                                                                                     int(startTime / 1000))),
                                                                                                             time.strftime(
                                                                                                                 '%Y-%m-%d %H:%M:%S',
                                                                                                                 time.localtime(
                                                                                                                     int(endTime / 1000)))))
                else:
                    self._Logger.error('Required Logs for {} not found in Audit Logs on MA from time interval {} to {}'.format(
                            category, time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(startTime / 1000))),
                            time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(endTime / 1000)))))
                    raise Exception('Require Logs not found in Audit Logs!')
        else:
            self._Logger.error("Request to {} not sent with error code {}".format(self.Definition.AUDIT_LOGS_url,
                                                                                  response.status_code))
            raise Exception("Request to {} not sent with error code {}".format(self.Definition.AUDIT_LOGS_url,
                                                                               response.status_code))

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("create forwarding profile")
    def Create_Forwarding_Profile(self,  # Function to create the Forwarding Profile
                                  forwarding_profile_name = None,
                                  save_to_temp=True):  # (bool)    if True, save ID to a tempfile
        """
        Work Flow
        ----------
            1.  Get Forwarding profile payloads from Definitions. get Auth token.
            2.  create basic forwarding profile.
            3.  send post request. validate response is 200
            4.  write fp id into a temp file, to use later
        """
        self.Definition = admin_res.admin_res(self.cloud)
        if self.cloud not in self.Definition.CREATE_FORWARDING_PROFILE_PAYLOAD.keys():
            payload = self.Definition.CREATE_FORWARDING_PROFILE_PAYLOAD["zscalerbeta"]
        else:
            #print(f"\n\nPAYLOAD UPDATED FOR {self.cloud}\n\n")
            payload = self.Definition.CREATE_FORWARDING_PROFILE_PAYLOAD[self.cloud]
        payload['name'] = (self.forwarding_profile_name if forwarding_profile_name==None else forwarding_profile_name)
        if forwarding_profile_name: 
            # since this is a custom forwarding profile we dont want to save this value anywhere
            save_to_temp=False
        self.Definition.CREATE_FORWARDING_PROFILE_HEADER['auth-token'] = self.Authenticate_To_Mobile_Admin()
        # self._Logger.info("url is {}".format(self.Definition.CREATE_FORWARDING_PROFILE_URL))
        # self._Logger.info("Payload is {}".format(json.dumps(payload)))
        # self._Logger.info("Header is {}".format(self.Definition.CREATE_FORWARDING_PROFILE_HEADER))

        Profile = self.REQ(request_type="post", url=self.Definition.CREATE_FORWARDING_PROFILE_URL,
                           data=json.dumps(payload),
                           headers=self.Definition.CREATE_FORWARDING_PROFILE_HEADER)

        if Profile.status_code == 401:
            print('Invalid request! Resending request to get Auth-token via API')
            self.Definition.CREATE_FORWARDING_PROFILE_HEADER['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

            Profile = self.REQ(request_type="post", url=self.Definition.CREATE_FORWARDING_PROFILE_URL,
                               data=json.dumps(payload),
                               headers=self.Definition.CREATE_FORWARDING_PROFILE_HEADER)

        #print(Profile.status_code)
        if Profile.status_code == 200:
            response_json = Profile.json()
            #print(response_json)
            if response_json["success"]=="false":
                self._Logger.error(f"Status code was 200, but error in response :: {response_json}")
                raise Exception(f"Status code was 200, but error in response :: {response_json}")
            self.forwarding_profile_id = response_json["id"]
            self.is_forwarding_profile_created = True
        elif Profile.status_code == 200 and Profile.json()["error"] == "3070":
            pass
            #print("yolo pass")
        else:
            self._Logger.error("the return status is  {} and not 200, please check".format(Profile.status_code))
            raise Exception("the return status is  {} and not 200, please check".format(Profile.status_code))
        if save_to_temp:
            self._Logger.info("making directory {}".format(self.temp_path))
            if not os.path.exists(self.temp_path):
                os.makedirs(self.temp_path)
                #print("directory made")
            _file = open(self.temp_file, "a")
            _file.write("fpid:{}".format(self.forwarding_profile_id))
            _file.write("\n")
            #print("file written")
            _file.close()
        self._Logger.info(
            "\n\nForwarding Profile created. status_code={0}\n".format(Profile.status_code))

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Get_Forwarding_Profile_ID(self,  # Function to Fetch Forwarding profile ID
                                  forwarding_profile_name=None,     # (str)     name of forwarding_profile
                                  read_from_temp=True,  # (bool)    reads forwarding profile ID from tempfile if True
                                  read_from_ma=True):  # (bool)    reads forwarding profile ID from MA if True
        """
        Work Flow
        ----------
            1.  if forwarding profile is created recently, self variable stores its ID. so this will be returned by default
            2.  if forwarding profile is created in another session, ID can be read from temp file. return it.
            3.  if temp file is also lost, visit MA, find forwarding profile with "automation" name. get its ID. return it.
        """
        # if custom forwarding_profile_name is passed, it wont be stored in self variable
        if self.is_forwarding_profile_created and not forwarding_profile_name:
             self.forwarding_profile_id
        if read_from_temp == True and os.path.exists(self.temp_file) and not forwarding_profile_name:
            with open(self.temp_file, "r") as _file:
                file_contents = _file.read().splitlines()
                for each_line in file_contents:
                    if "fpid" in each_line:
                        return each_line.split(":")[1]
        if read_from_ma == True:
            self.Definition.GET_FORWARDING_PROFILE_LIST_HEADER['auth-token']=self.Authenticate_To_Mobile_Admin()
            Profile = self.REQ(request_type="get", url=self.Definition.GET_FORWARDING_PROFILE_LIST_URL,headers=self.Definition.GET_FORWARDING_PROFILE_LIST_HEADER)
            response_json = Profile.json()
            #self._Logger.info(response_json)
            if Profile.status_code == 200:
                for i in range(len(response_json['forwardingProfiles'])):
                    if response_json['forwardingProfiles'][i]['name'] == (self.forwarding_profile_name if not forwarding_profile_name else forwarding_profile_name):
              #          print(response_json['forwardingProfiles'][i]['id'])
                        if True:
                            return str(response_json['forwardingProfiles'][i]['id'])
                        else:
                            return response_json['forwardingProfiles']
            self._Logger.error("No such forwarding profile found")
            raise Exception("Error: No such forwarding profile found")
        #self._Logger.error("wrong selection of options or no id found")
        raise Exception("Error :: wrong selection of options or no id found")
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("edit Forwarding profile")
    def Edit_Forwarding_Profile(self,       # Function to Fetch Forwarding profile ID
    forwarding_profile_name=None,           # (str)     name of forwarding_profile
    tunnel_mode=None,                       # (str)     tunnel mode to configure. (tunnel_1/tunnel_2/twlp/enforce_proxy/none)
    Trusted_Network=None,                   # (str)     val -> "predefined" if predefined tNet are to be applied, else give 'inforwardprofile'
    dnsServers=None,                        # (str)     str of dns server, if u want to give multiple dns server, given them comma seprated "1.1.1,*******"
    dnsSearchDomains=None,                  # (str)     str of dnsSearchDomains, if u want to give multiple give them comma seprated "facebook.com,google.com"
    hostName_IP=None,                       # (str)     str of hostname and ips, "facebook.com,*******"
    mtu_value=None,                         # (int)     mtu value for fp. works only with tunnel_2 
    pac_url=None,                           # (str)     pac url that forwarding profile needs to follow
    fallback_to_tunnel1=None,               # (bool)    get forwarding profile to fallback to tunnel 1
    failure_expected=False,                 # (bool)    expect this function to fail
    pac_enforced=True,                      # (bool)    enforce pac
    driver_type="packet_filter_based",              # (str)     driver type to be used in forwarding profile (route_based/packet_filter_based)
    enable_split_vpn=False,                 # (bool)    enable/disable split vpn tunnel option
    skip_trusted_criteria_match_for_vpn=False, #(bool)   enable/disable "skip trusted criteria match for vpn adapter" knob on MA
    dropIPv6 = False,
    enableLWFDriver = False,
    tlsFallback = True,                     # (bool)    enable/disable capability of tunnel 2 to fallback to TLS protocol
    fallBackRule = 0,                       # (int)     0=Tunnel 1 with Internet access, 1 = Tunnel 1 with Internet block, 2 = Block all
    redirectWebTraffic = 0,                 # (int) 0 = Disable, 1 = Enable
    useTunnel2ForProxiedWebTraffic = 0,     # (int) 0 = Disable, 1 = Enable   OFFTRUSTED_T2 / SPLIT VPN - T1
    zpaTunnel_mode= None,                    # (str/dict) none, tunnel_tls, tunnel_dtls
    latencyBasedZenEnablement=False          # (bool)    enable/disable Latency Based Zen Enablement
    ):
        """ 
        Work Flow 
        ----------
            1. Get Forwarding profile ID. get Definitions. get auth token
            2. apply all necessary configurations to FP as specified by function parameters
            3. send post request, validate response is 200
        """
        
        fwdProfileActions = {
            'onTrusted': 0, 'offTrusted': 2, 'vpnTrusted': 1, 'splitVPN': 3,
            0: 'onTrusted', 2: 'offTrusted', 1: 'vpnTrusted', 3: 'splitVPN'
        }

        networkMapping = {0:'none',1:'none',2:'none',3:'none'}
        zpaNetworkMapping = {0:'none',1:'none',2:'none',3:'none'}

        if type(tunnel_mode) == type(dict()):
            for k in tunnel_mode.keys():
                networkMapping[fwdProfileActions[k]] = tunnel_mode[k]

        if type(zpaTunnel_mode) == type(dict()):
            for k in zpaTunnel_mode.keys():
                zpaNetworkMapping[fwdProfileActions[k]] = zpaTunnel_mode[k]

        self.Definition = admin_res.admin_res(self.cloud)
        if self.cloud not in self.Definition.CREATE_FORWARDING_PROFILE_PAYLOAD.keys():
            self.FWD_PAYLOAD = self.Definition.CREATE_FORWARDING_PROFILE_PAYLOAD["zscalerbeta"].copy()
        else:
            #print(f"\n\nPAYLOAD UPDATED FOR {self.cloud}\n\n")
            self.FWD_PAYLOAD = self.Definition.CREATE_FORWARDING_PROFILE_PAYLOAD[self.cloud].copy()
        #self.FWD_PAYLOAD = self.Definition.CREATE_FORWARDING_PROFILE_PAYLOAD[self.cloud].copy()
        
        self.FWD_PAYLOAD["id"] = self.Get_Forwarding_Profile_ID(forwarding_profile_name = forwarding_profile_name)
        self._Logger.info("previous forwarding profile idd : " + str(self.FWD_PAYLOAD["id"]))
        self.FWD_PAYLOAD["name"] = (self.forwarding_profile_name if not forwarding_profile_name else forwarding_profile_name)
        self.Definition.EDIT_FORWARDING_PROFILE_HEADER['auth-token']=self.Authenticate_To_Mobile_Admin()
        
        if Trusted_Network!=None:
            if 'pre' in Trusted_Network:
                self.FWD_PAYLOAD["predefinedTnAll"]=False
                self.FWD_PAYLOAD["predefinedTrustedNetworks"]=True
                self.FWD_PAYLOAD["predefinedTrustedNetworksFilter"]=""
                self.FWD_PAYLOAD["trustedNetworkIds"]=self.Get_Trusted_Network_ID()
            else:
                self.FWD_PAYLOAD["dnsSearchDomains"]= dnsSearchDomains if dnsSearchDomains else ""
                self.FWD_PAYLOAD["dnsServers"]=dnsServers if dnsServers else ""
                self.FWD_PAYLOAD["hostname"]=hostName_IP.split(',')[0] if hostName_IP else ""
                self.FWD_PAYLOAD["resolvedIpsForHostname"]=hostName_IP.split(',')[1] if hostName_IP else ""
        
        if  tunnel_mode == None:
            tunnel_mode = "tunnel_1"

        if driver_type == "packet_filter_based":
            self.FWD_PAYLOAD["enableLWFDriver"] = 1
        else:
            self.FWD_PAYLOAD["enableLWFDriver"] = 0

        if enable_split_vpn:
            self.FWD_PAYLOAD["enableSplitVpnTN"] = 1
        
        if skip_trusted_criteria_match_for_vpn:
            self.FWD_PAYLOAD["skipTrustedCriteriaMatch"] = 1

        if enableLWFDriver == True:
            self.FWD_PAYLOAD["enableLWFDriver"] = 1

        if tunnel_mode == "tunnel_1":
            for i in range(4):
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["actionType"] = 1
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["dropIpv6Traffic"] = str(0)
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["systemProxy"] = 0
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["systemProxyData"]["enablePAC"] = 0
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["systemProxyData"]["proxyAction"] = 1
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["latencyBasedZenEnablement"] = str(1) if latencyBasedZenEnablement else str(0)

                if dropIPv6 == True:
                    self.FWD_PAYLOAD["forwardingProfileActions"][i]["dropIpv6Traffic"] = 1
                else:
                    self.FWD_PAYLOAD["forwardingProfileActions"][i]["dropIpv6Traffic"] = 0

            #self._Logger.info("tunnel_1 applied")
        elif "tunnel_2" in tunnel_mode:
            self.FWD_PAYLOAD["enableLWFDriver"] = 1
            for i in range(4):
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["actionType"]=1
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["enablePacketTunnel"]=1
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["systemProxy"]=1
                if tunnel_mode=="tunnel_2_tls":self.FWD_PAYLOAD["forwardingProfileActions"][i]["primaryTransport"]=0       #applies TLS
                elif tunnel_mode=="tunnel_2_dtls":self.FWD_PAYLOAD["forwardingProfileActions"][i]["primaryTransport"]=1      #applies DTLS
                else: self._Logger.info("Tunnel 2 Mode Not defined, using DTLS by default")
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["systemProxy"]=1
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["dropIpv6Traffic"]=str(0)
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["DTLSTimeout"]=str(9)
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["mtuForZadapter"]=str(0)
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["redirectWebTraffic"]=str(redirectWebTraffic)
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["TLSTimeout"]=str(5)
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["useTunnel2ForProxiedWebTraffic"]=str(useTunnel2ForProxiedWebTraffic)
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["systemProxyData"]["proxyAction"]=str(1)
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["systemProxyData"]["enablePAC"]=str(0)
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["allowTLSFallback"] = 1 if tlsFallback==True else 0
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["tunnel2FallbackType"] = str(fallBackRule)
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["latencyBasedZenEnablement"] = str(1) if latencyBasedZenEnablement else str(0)

                if not (fallBackRule in (0, 1, 2)):
                    raise Exception(f"fallBackRule should have values 0,1,2 only, {fallBackRule} given!")

                self.FWD_PAYLOAD["forwardingProfileActions"][i]["tunnel2FallbackType"] = fallBackRule

                if dropIPv6 == True:
                    self.FWD_PAYLOAD["forwardingProfileActions"][i]["dropIpv6Traffic"] = 1
                else:
                    self.FWD_PAYLOAD["forwardingProfileActions"][i]["dropIpv6Traffic"] = 0

            if mtu_value != None:
                for i in range(4):
                    self.FWD_PAYLOAD["forwardingProfileActions"][i]["mtuForZadapter"] = str(mtu_value)
            if fallback_to_tunnel1 != None:
                for i in range(4):
                    self.FWD_PAYLOAD["forwardingProfileActions"][i]["primaryTransport"] = "0"
            #self._Logger.info("tunnel_2 applied ")


        elif tunnel_mode == "twlp":
            for i in range(4):
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["actionType"] = "2"
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["systemProxyData"]["enablePAC"] = "1"
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["latencyBasedZenEnablement"] = str(1) if latencyBasedZenEnablement else str(0)
            self._Logger.info("tunnel with local proxy applied *twlp*")
        elif tunnel_mode == "enforce_proxy":
            for i in range(4):
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["actionType"] = 3
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["systemProxy"] = 0
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["systemProxyData"]["enablePAC"] = 0
            self._Logger.info("enforce_proxy applied")
        elif tunnel_mode == "none":
            for i in range(4):
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["actionType"] = "0"
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["systemProxy"] = "2"
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["systemProxyData"]["proxyAction"] = "0"
                self.FWD_PAYLOAD["forwardingProfileActions"][i]["systemProxyData"]["enablePAC"] = 0
            self._Logger.info("tunnel_mode=none applied")
        else:
            tunnel_mode_list = "tunnel_1/tunnel_2/twlp/enforce_proxy/none"
            self._Logger.info(f"Tunnel mode selection error: choose a valid mode: {tunnel_mode_list}, Trying Different tunnel modes for different Networks")
            self._Logger.info(f"Network Dictionary is :: {networkMapping}")

            for network,tunnel_mode in networkMapping.items():
                if tunnel_mode=="tunnel_1":
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["actionType"]=1
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["dropIpv6Traffic"]=str(0)
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["systemProxy"]=0
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["systemProxyData"]["enablePAC"]=0
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["systemProxyData"]["proxyAction"]=1

                    if dropIPv6 == True:
                        self.FWD_PAYLOAD["forwardingProfileActions"][network]["dropIpv6Traffic"] = 1
                    else:
                        self.FWD_PAYLOAD["forwardingProfileActions"][network]["dropIpv6Traffic"] = 0

                    self._Logger.info(f"tunnel_1 applied on Network {fwdProfileActions[network]}")
                elif "tunnel_2" in tunnel_mode:
                    self.FWD_PAYLOAD["enableLWFDriver"]=1
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["actionType"]=1
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["enablePacketTunnel"]=1
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["systemProxy"]=1
                    if tunnel_mode=="tunnel_2_tls":self.FWD_PAYLOAD["forwardingProfileActions"][network]["primaryTransport"]=0       #applies TLS
                    elif tunnel_mode=="tunnel_2_dtls":self.FWD_PAYLOAD["forwardingProfileActions"][network]["primaryTransport"]=1      #applies DTLS
                    else: self._Logger.info("Tunnel 2 Mode Not defined, using DTLS by default")
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["systemProxy"]=1
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["dropIpv6Traffic"]=str(0)
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["DTLSTimeout"]=str(9)
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["mtuForZadapter"]=str(0)
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["redirectWebTraffic"]=str(redirectWebTraffic)
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["TLSTimeout"]=str(5)
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["useTunnel2ForProxiedWebTraffic"]=str(useTunnel2ForProxiedWebTraffic)
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["systemProxyData"]["proxyAction"]=str(1)
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["systemProxyData"]["enablePAC"]=str(0)
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["allowTLSFallback"] = 1 if tlsFallback==True else 0

                    if not(fallBackRule in (0,1,2)):
                        raise Exception(f"fallBackRule should have values 0,1,2 only, {fallBackRule} given!")

                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["tunnel2FallbackType"] = fallBackRule
                    
                    #Different Flags for Tunnel 2
                    if dropIPv6 == True:
                        self.FWD_PAYLOAD["forwardingProfileActions"][network]["dropIpv6Traffic"] = 1
                    else:
                        self.FWD_PAYLOAD["forwardingProfileActions"][network]["dropIpv6Traffic"] = 0
                    if mtu_value!=None:
                        self.FWD_PAYLOAD["forwardingProfileActions"][network]["mtuForZadapter"]=str(mtu_value)
                    if fallback_to_tunnel1!=None:
                        self.FWD_PAYLOAD["forwardingProfileActions"][network]["primaryTransport"]="0"

                elif tunnel_mode=="twlp":
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["actionType"]="2"
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["systemProxyData"]["enablePAC"]="1"
                    self._Logger.info(f"tunnel with local proxy applied *twlp* on Network {fwdProfileActions[network]}")

                elif tunnel_mode=="enforce_proxy":
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["actionType"]=3
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["systemProxy"]=0
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["systemProxyData"]["enablePAC"]=0
                    self._Logger.info(f"enforce_proxy applied on Network {fwdProfileActions[network]}")

                elif tunnel_mode=="none":
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["actionType"]="0"
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["systemProxy"]="2"
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["systemProxyData"]["proxyAction"]="0"
                    self.FWD_PAYLOAD["forwardingProfileActions"][network]["systemProxyData"]["enablePAC"]=0
                    self._Logger.info(f"tunnel_mode=none applied on Network {fwdProfileActions[network]}")
            # raise Exception("Tunnel mode selection error: choose a valid mode: "+tunnel_mode_list) 
        
        if pac_url!=None:
            self.FWD_PAYLOAD["forwardingProfileActions"][0]["systemProxyData"]["pacURL"]=str(pac_url)
            self.FWD_PAYLOAD["forwardingProfileActions"][1]["systemProxyData"]["pacURL"]=str(pac_url)
            self.FWD_PAYLOAD["forwardingProfileActions"][2]["systemProxyData"]["pacURL"]=str(pac_url)
            self.FWD_PAYLOAD["forwardingProfileActions"][3]["systemProxyData"]["pacURL"]=str(pac_url)

        ######## ZPA Traffic ###########

        if zpaTunnel_mode == None:
            for i in range(4):
                self.FWD_PAYLOAD["forwardingProfileZpaActions"][i]["actionType"] = 1
                self.FWD_PAYLOAD["forwardingProfileZpaActions"][i]["primaryTransport"] = 0
                self.FWD_PAYLOAD["forwardingProfileZpaActions"][i]["partnerInfo"]["primaryTransport"] = 0
        elif zpaTunnel_mode in ('tunnel_tls','tunnel_dtls','none'):
            if zpaTunnel_mode == 'tunnel_tls':
                for i in range(4):
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][i]["actionType"] = 1
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][i]["primaryTransport"] = 0
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][i]["partnerInfo"]["primaryTransport"] = 0
            elif zpaTunnel_mode == 'tunnel_dtls':
                for i in range(4):
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][i]["actionType"] = 1
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][i]["primaryTransport"] = 1
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][i]["partnerInfo"]["primaryTransport"] = 1
            else:
                for i in range(4):
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][i]["actionType"] = 0
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][i]["primaryTransport"] = 0
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][i]["partnerInfo"]["primaryTransport"] = 0
        else:
            # (str/dict) none, tunnel_tls, tunnel_dtls
            for zpa_network,zpa_tunnel_mode in zpaNetworkMapping.items():
                if zpa_tunnel_mode == 'tunnel_tls':
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][zpa_network]["actionType"] = 1
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][zpa_network]["primaryTransport"] = 0
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][zpa_network]["partnerInfo"]["primaryTransport"] = 0
                elif zpa_tunnel_mode == 'tunnel_dtls':
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][zpa_network]["actionType"] = 1
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][zpa_network]["primaryTransport"] = 1
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][zpa_network]["partnerInfo"]["primaryTransport"] = 1
                else:
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][zpa_network]["actionType"] = 0
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][zpa_network]["primaryTransport"] = 0
                    self.FWD_PAYLOAD["forwardingProfileZpaActions"][zpa_network]["partnerInfo"]["primaryTransport"] = 0


        #self._Logger.info("\n\nConfigureFP\nURL:{0}\n\nHEADER:{1}\n\nPAYLOAD:{2}\n\n".format(self.Definition.EDIT_FORWARDING_PROFILE_URL,self.Definition.EDIT_FORWARDING_PROFILE_HEADER,json.dumps(self.FWD_PAYLOAD)))
        self.Profile = self.REQ(request_type="post", url=self.Definition.EDIT_FORWARDING_PROFILE_URL, headers=self.Definition.EDIT_FORWARDING_PROFILE_HEADER, data=json.dumps(self.FWD_PAYLOAD))

        if self.Profile.status_code == 401:
            print('Invalid request! Resending request to get Auth-token via API')
            self.Definition.EDIT_FORWARDING_PROFILE_HEADER['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

            self.Profile = self.REQ(request_type="post", url=self.Definition.EDIT_FORWARDING_PROFILE_URL,
                                    headers=self.Definition.EDIT_FORWARDING_PROFILE_HEADER,
                                    data=json.dumps(self.FWD_PAYLOAD))

        if self.Profile.status_code == 200 and "error" not in self.Profile.json().keys():
            pass
            #self._Logger.info("The status code is 200, Token is {}".format(self.Profile.json()))
        elif self.Profile.status_code == 200 and "error" in self.Profile.json().keys():
            if failure_expected == True:
                self._Logger.info("Log :: Forwarding Profile Creation Failed - As Expected")
                return
            raise Exception("The status code is  200 , and response is{0}, please check for {1}".format(self.Profile.json(),
                                                                                            self.Definition.EDIT_FORWARDING_PROFILE_URL))
        else:
            if failure_expected == True:
                self._Logger.info("Log :: Forwarding Profile Creation Failed - As Expected")
                return
            raise Exception("The status code is not 200 , and is{0}, please check for {1}".format(self.Profile.status_code,self.Definition.EDIT_FORWARDING_PROFILE_URL))
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Delete Forwarding profile")
    def Delete_Forwarding_Profile(self,  # Function to delete forwarding profile
                                  forwarding_profile_name=None
                                  ):
        """
        Work Flow
        ----------
            1. get forwarding profile ID. get definitions. get auth token
            2. send a delete request for forwarding profile, validate response is 200
        """
        # assert self.is_forwarding_profile_created
        self.Definition.Delete_Forwarding_Profile_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
        self.Definition.Delete_Forwarding_Profile_Url = self.Definition.Delete_Forwarding_Profile_Url + str(
            self.Get_Forwarding_Profile_ID(forwarding_profile_name = forwarding_profile_name))

        self._Logger.info("\n\nDelete_Forwarding_Profile\nUrl:{0}\n".format(
            self.Definition.Delete_Forwarding_Profile_Url))
        self.Delete_Forwarding_Profile_Info = self.REQ(request_type="get",
                                                       url=self.Definition.Delete_Forwarding_Profile_Url,
                                                       headers=self.Definition.Delete_Forwarding_Profile_Header)
        # Validate success
        if self.Delete_Forwarding_Profile_Info.status_code == 200:
            # self._Logger.info(
            #     "FP status: " + str("deleted") + " response:" + str(self.Delete_Forwarding_Profile_Info.json()))
            if self.Delete_Forwarding_Profile_Info.json()['success']=="false":
                self._Logger.error("FP deletion didnt go well!")
                raise Exception(f"Error :: FP deletion didnt go well, status code 200 but error code :: {self.Delete_Forwarding_Profile_Info.json()['error']}!")

        else:
            self._Logger.error("FP deletion didnt go well!")
            raise Exception("Error :: FP deletion didnt go well!")
        self.is_forwarding_profile_created = False
        if not os.path.exists(self.temp_file):
            return
        file_contents = None
        with open(self.temp_file, "r") as _file:
            file_contents = _file.read().splitlines()
            fp_id_line_number = [i for i in range(len(file_contents)) if "fpid" in file_contents[i]][0]
            try:del file_contents[fp_id_line_number]
            except:pass
        _file = open(self.temp_file, "w")
        _file.write(''.join(file_contents))
        _file.close()
        self.response = self.Delete_Forwarding_Profile_Info.json()
        #self._Logger.info("\n\nDelete_Forwarding_Profile_response\nstatus_code:{0}\n".format(self.Delete_Forwarding_Profile_Info.status_code))

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Apply_UAS(self,  # Function to apply User Agent Suffix in MA
                  uas=None):  # (str)     string to be applied as User Agent Suffix
        """
        Work Flow
        ----------
            1. edit UAS payload accordingly to enable/disable UAS
            2. send post request, validate response is 200
        """
        if uas:
            self.Definition.APPLY_UAS_PAYLOAD['userAgentSuffixForAll'] = str(uas)
        else:
            self.Definition.APPLY_UAS_PAYLOAD['enableUserAgentSuffixForAll'] = 0
        # self.Definition.APPLY_UAS_HEADER['auth-token']=self.Authenticate_To_Mobile_Admin()
        Profile = self.REQ(request_type="post", url=self.Definition.APPLY_UAS_URL,
                           headers=self.Definition.APPLY_UAS_HEADER, data=json.dumps(self.Definition.APPLY_UAS_PAYLOAD))
        # validate success
        if Profile.status_code == 200:
            self._Logger.info("\n\nUser Agent Suffix applied in MA. response is {}\n\n".format(Profile.json()))
        else:
            self._Logger.error("Applying User Agent Suffix (UAS) didnt go well!")
            raise Exception("Error :: Applying User Agent Suffix (UAS) didnt go well!")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("create app policy")
    def Create_Device_Policy(self,  # Function to Create Device Policy in MA
                             Operating_System=None,
                             # (str)     Operating system on which Device Policy must be created (Windows/Mac/Linux/Android/Ios)
                             Policy_Name=None,  # (str)     Name to use to create Device Policy
                             EnableForParticularUser=False,
                             # (list)     App profile to be applied for only particular user
                             forwarding_profile_name=None   # (str)     name of forwarding_profile to use
                             ):
        """
        Note: Supports Win/Mac/Linux/Android/Ios
        Work Flow
        ----------
            1. get Definitions. get auth token. get FP ID, if created, or use Default FP.
            2. edit profile accordingly for windows/mac
            3.  send post request. validate response is 200
            4.  write dp id into a temp file, to use later
        """
        Auth_token = self.Authenticate_To_Mobile_Admin()
        self.Definition = admin_res.admin_res(self.cloud)
        self.Definition.CREATE_DEVICE_POLICY_HEADER['auth-token'] = Auth_token
        usersForAppProfileList = []
        if EnableForParticularUser:
            if EnableForParticularUser == True: EnableForParticularUser = [self.Config["ZIA_USER_ID" ]]
            for i in range(
                    len(EnableForParticularUser)):  # --- on reading data from config file, we got extra spaces in username, so dealing with it here
                EnableForParticularUser[i] = "".join(EnableForParticularUser[i].split())
            # if EnableForParticularUser:
            if type(EnableForParticularUser).__name__ == 'list':
                usersList = self.REQ(request_type="get", url=self.Definition.Get_Users_List, headers={'auth-token': ''})
                if usersList.status_code == 200:
                    self._Logger.info("Got users list from MA")
                    for userDict in usersList.json()['users']:
                        if userDict['loginName'] in EnableForParticularUser:
                            self._Logger.info(f"USER FOUND : {userDict['loginName']} iD : {userDict['id']}")
                            usersForAppProfileList.append(userDict['id'])
                    #self._Logger.info(f"Users IDs are : {usersForAppProfileList}")
                else:
                    raise Exception(f"ERROR :: Invalid status code for getting users list : {usersList.status_code}")
            else:
                raise Exception("ERROR :: Please give EnableForParticularUser in a list format")
        try:
            fp_id = int(self.Get_Forwarding_Profile_ID(forwarding_profile_name = forwarding_profile_name))
        except:
            fp_id = 0
        if (self.os_version=="MAC" and Operating_System==None) or Operating_System=="Mac":
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_mac['name']=(Policy_Name if Policy_Name else self.device_policy_name)
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_mac["device_type"] = 4
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD = copy.deepcopy(self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_mac)
        elif Operating_System == "Linux":
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_linux['name'] = (
                Policy_Name if Policy_Name else self.linux_device_policy_name)
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_linux["device_type"] = '5'
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD = copy.deepcopy(self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_linux)
        elif Operating_System == "Android":
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android['name'] = (
                Policy_Name if Policy_Name else self.android_device_policy_name)
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android["device_type"] = '2'
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD = copy.deepcopy(
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android)
        elif Operating_System == "Ios":
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_ios['name'] = (
                Policy_Name if Policy_Name else self.ios_device_policy_name)
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_ios["device_type"] = '1'
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD = copy.deepcopy(self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_ios)
        elif (self.os_version != "MAC" and Operating_System == None) or Operating_System == "Windows":
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_win['name'] = (
                Policy_Name if Policy_Name else self.device_policy_name)
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_win["device_type"] = '3'
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD = copy.deepcopy(self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_win)
        else:
            raise Exception("Unexpected Operating system value")

        self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['groups'] = []
        if len(usersForAppProfileList) > 0:
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['users'] = usersForAppProfileList
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["groupAll"] = 0
        else:
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['users'] = []
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["groupAll"] = 1
        self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['forwardingProfileId'] = fp_id
        self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['description'] = 'POLICY_CREATED'
        self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['sendDisableServiceReason'] = "0"

        # self._Logger.info("DEVICE URL is {}".format(self.Definition.CREATE_DEVICE_POLICY_URL))
        # self._Logger.info("DEVICE Header is {}".format(self.Definition.CREATE_DEVICE_POLICY_HEADER))
        # self._Logger.info("DEVICE Payload is {}".format(json.dumps(self.Definition.EDIT_DEVICE_POLICY_PAYLOAD)))

        Profile = self.REQ(request_type="post", url=self.Definition.CREATE_DEVICE_POLICY_URL,
                           data=json.dumps(self.Definition.EDIT_DEVICE_POLICY_PAYLOAD),
                           headers=self.Definition.CREATE_DEVICE_POLICY_HEADER)

        if Profile.status_code == 401:
            print('Invalid request! Resending request to get Auth-token via API')
            self.Definition.CREATE_DEVICE_POLICY_HEADER['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

            Profile = self.REQ(request_type="post", url=self.Definition.CREATE_DEVICE_POLICY_URL,
                               data=json.dumps(self.Definition.EDIT_DEVICE_POLICY_PAYLOAD),
                               headers=self.Definition.CREATE_DEVICE_POLICY_HEADER)

        if Profile.status_code == 200:
            response_json = Profile.json()
            #print(response_json)
            if response_json["success"]=="false":
                self._Logger.error(f"Status code was 200, but error in response :: {response_json}")
                raise Exception(f"Status code was 200, but error in response :: {response_json}")
            #self._Logger.info("\n\nstatus_code:{0}\nresponse:{1}\n\n".format(Profile.status_code, response_json))
            self.device_policy_id = int(response_json["id"])
            self.is_device_policy_created = True
        else:
            self._Logger.error("the status code is {} and not 200, please check".format(Profile.status_code))
            raise Exception("the status code is {} and not 200, please check".format(Profile.status_code))

        if not os.path.exists(self.temp_path):
            os.makedirs(self.temp_path)
        _file = open(self.temp_file, "a")
        _file.write(
            "{}dpid:{}\n".format(self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['device_type'], self.device_policy_id))
        _file.close()

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Get_Device_Policy_ID(self,  # Function to get Device Policy ID
                             read_from_temp=True,  # (bool)    read device policy id from temp file if set to True
                             read_from_ma=True,  # (bool)    read device policy id from MA if set to True
                             policyToken=False,  # (bool)    read device policy id from MA and get policyToken
                             skipRead=False,  # (bool)    skip sending self.device_policy_id
                             getAll=False,  # (bool)	to get All policies applied on MA
                             Operating_System=None,
                             # (str)     Operating system on which Device Policy must be found (Windows/Mac/Linux/Android/Ios)
                             Policy_Name=None  # (str)     Name to use to find Device Policy
                             ):
        """
        Note: Supports Win/Mac/Linux/Android/Ios
        Work Flow
        ----------
            1.  if device policy is created recently, self variable stores its ID. so this will be returned by default
            2.  if device policy  is created in another session, ID can be read from temp file. return it.
            3.  if temp file is also lost, visit MA, find device policy  with "automation" name. get its ID. return it.
        """
        if policyToken:
            read_from_temp=False
            read_from_ma=True
            skipRead=True

        if (self.os_version == "MAC" and Operating_System == None) or Operating_System == "Mac":
            device_type = '4'
            device_policy_name = (Policy_Name if Policy_Name else self.device_policy_name)
        elif (self.os_version != "MAC" and Operating_System == None) or Operating_System == "Windows":
            device_type = '3'
            device_policy_name = (Policy_Name if Policy_Name else self.device_policy_name)
        elif Operating_System == "Linux":
            device_type = '5'
            device_policy_name = (Policy_Name if Policy_Name else self.linux_device_policy_name)
        elif Operating_System == "Android":
            device_type = '2'
            device_policy_name = (Policy_Name if Policy_Name else self.android_device_policy_name)
        elif Operating_System == "Ios":
            device_type = '1'
            device_policy_name = (Policy_Name if Policy_Name else self.ios_device_policy_name)
        else:
            raise Exception("Illegal Operating System Selection")

        if self.is_device_policy_created and Operating_System == None:
            if skipRead == False:
                return self.device_policy_id
        if read_from_temp and os.path.exists(self.temp_file) and Operating_System==None:
            with open(self.temp_file, "r") as _file:
                file_contents = _file.read().splitlines()
                for each_line in file_contents:
                    if "{}dpid".format(device_type) in each_line:
                        return each_line.split(":")[1]
        if read_from_ma:
            # self.Definition.GET_APP_PROFILE_LIST_HEADER['auth-token']=self.Authenticate_To_Mobile_Admin()
            #print('*' * 10)
            self.Definition.GET_APP_PROFILE_LIST_URL = self.Definition.GET_APP_PROFILE_LIST_URL.replace("DEVICE_TYPE",device_type)
            # print(self.Definition.GET_APP_PROFILE_LIST_URL)
            # print(self.Definition.GET_APP_PROFILE_LIST_HEADER)
            # print('*' * 10)
            Profile = self.REQ(request_type="get", url=self.Definition.GET_APP_PROFILE_LIST_URL,headers=self.Definition.GET_APP_PROFILE_LIST_HEADER)
            # cloud = [item for item in self.Definition.GET_APP_PROFILE_LIST_URL.split('.') if 'zscaler' in item][0]
            cloud = self.cloud
            #print(Profile)

            if Profile.status_code == 401:
                print('Invalid request! Resending request to get Auth-token via API')
                # self.Definition.GET_APP_PROFILE_LIST_HEADER['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

                Profile = self.REQ(request_type="get", url=self.Definition.GET_APP_PROFILE_LIST_URL,
                                   headers=self.Definition.GET_APP_PROFILE_LIST_HEADER)

            if Profile.status_code == 200:

                response_json = Profile.json()
                #print(response_json)

                if getAll == True:
                    #print("Retunrning all the AppProfiles")
                    return response_json
                else:
                    for i in range(len(response_json['policies'])):
                        if response_json['policies'][i]['name'] == device_policy_name:
                            # print('*' * 10, '*' * 10)
                            # print(response_json['policies'][i]['name'])
                            # print(response_json['policies'][i]['id'])
                            # print(response_json['policies'][i]['policyToken'])
                            # print(cloud)
                            # print('*' * 10, '*' * 10)
                            if policyToken == True:
                                return (str(response_json['policies'][i]['id']),
                                        str(response_json['policies'][i]['policyToken']), cloud)
                            return str(response_json['policies'][i]['id'])
        raise Exception("Error: No such profile name")

    def getCloudAndPolicyToken(self
                               ):
        time.sleep(10)
        resultList = self.Get_Device_Policy_ID(read_from_temp=False, policyToken=True, skipRead=True)

        self.policyToken = resultList[1]
        self.getCloud = resultList[2]

        #print("Policy Token is {}, getCloud is {}".format(self.policyToken, self.getCloud))

    # --------------------------------------------------------------------------------------------------------------------------

    def Get_ZIA_Posture_Postures(self):
        self.Definition.Device_Posture_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
        self._Logger.info(f"Get ZIA Posture Profiles")
        if self.os_version == "MAC":
            URL = self.Definition.GET_ZIA_POSTURE_PROFILE_LIST_URL_MAC
        else:
            URL = self.Definition.GET_ZIA_POSTURE_PROFILE_LIST_URL
        self._Logger.info(f"URL : {URL}\n HEADER : {self.Definition.GET_ZIA_POSTURE_PROFILE_LIST_HEADER}")
        getPosturesFromMA = self.REQ(request_type="get", url=URL,
                                     headers=self.Definition.GET_ZIA_POSTURE_PROFILE_LIST_HEADER)
        if getPosturesFromMA.status_code == 200:
            Postures = getPosturesFromMA.json()['records']
            return Postures
        else:
            self._Logger.error(f"ERROR ::Failed to get ZIA Posture Profiles")
            raise Exception(f"ERROR ::Failed to get ZIA Posture Profiles")

    # --------------------------------------------------------------------------------------------------------------------------

    def Get_ZIA_Posture_Id(self, profile_name):
        profiles = self.Get_ZIA_Posture_Postures()
        return int([profile['id'] for profile in profiles if profile['name'] == profile_name][0])

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Edit_Device_Policy(self,                                # Function to edit device policy for windows/mac in MA
                            Vpn_Bypass=None ,                   # (str)     vpn hostname bypasses. send one or multiple seperated by commas as string
                            Exclude=None,                       # (str)     exclude ip values. send one or multiple seperated by commas as string
                            Include=None,                       # (str)     include ip values. send one or multiple seperated by commas as string
                            pac=None,                           # (str)     pac url to be applied as string
                            common_exclude_url=None,            # (str)     exclude a list of common urls if None. apply custom exclude urls otherwise
                            common_include_url=None,            # (str)     include a list of common urls if None. apply custom exclude urls otherwise
                            refresh=True,                       # (bool)    refresh definitions
                            DNS_Include=None,                   # (str)     DNS include hostnames . send one or multiple seperated by commas as string
                            DNS_Exclude=None,                   # (str)     DNS exclude hostnames . send one or multiple seperated by commas as string
                            Disable_Dns_Route_Exclusion=True,   # (bool)    will toggle "DNS Server Route Exclusion" on MA
                            ZPA_disable_password=None,          # (str)     set ZPA_disable_password if not None
                            ZIA_disable_password=None,          # (str)     set ZIA_disable_password if not None
                            ZDX_disable_password=None,          # (str)     set ZDX_disable_password if not None
                            logout_password=None,               # (str)     set Logout Password for ZCC
                            uninstall_password=None,            # (str)     set Password to Uninstall ZCC
                            check_audit_logs=None,              # (bool)    verify if app profile change is made in audit logs (MA)
                            Group_ID=None,                      # (str)     Group int ID or list of int IDs on which this Policy should be applied
                            failure_expected=False,             # (bool)    expect post request to fail if set to True
                            disable_reason=False,               # (bool)    enable "disable reason" option in MA App Profile
                            applyDefaultFP = False,             # (bool)    to apply default app profile if required
                            revert_zcc=False,                   # (bool)    enable ability to revert zcc
                            revert_password=None,               # (str)     password to revert zcc, by default this is None, meaning no password 
                            fallback_to_gateway=True,           # (bool)    toggle for fallback to gateway domain, True for fallback, False for not falling back
                            ZIA_posture=None,                   # (str)     Configure ZIA posture profile
                            enableWFPDriver=False,              # (bool)    install and enable/disable WFP/Flow Tracking Driver
                            #flowLoggerConfig=None,             # (str)     config tells which flow types to be enabled 
                            directFlowLoggingToggle=0,          # (int)     config tells whether to enable direct flow type
                            intranetFlowLoggingToggle=0,        # (int)     config tells whether to enable intranet flow type
                            loopbackFlowLoggingToggle=0,        # (int)     config tells whether to enable loopback flow type
                            vpnFlowLoggingToggle=0,             # (int)     config tells whether to enable vpn flow type
                            vpnTunnelFlowLoggingToggle=0,       # (int)     config tells whether to enable vpn flow type
                            zpaFlowLoggingToggle=0,             # (int)     config tells whether to enable zpa flow type
                            zccBlockedTrafficToggle=0,          # (int)     config tells whether to enable zcc blocked traffic flow type
                            appServiceIds=[],                   # (list)    Config tells whether to bypass Zoom or Teams Applications, Zoom has id 5, Teams has id ==> 3
                            Exclude_ipv6=None,                  # (str) exclude ipv6 addresses under Destination exclusions for IPv6
                            common_exclude_ipv6_url=None,       # (str) exclude a list of ipv6 common addresses if None. apply custom exclude addresses otherwise
                            Include_ipv6=None,                  # (str) include ipv6 addresses under Destination inclusions for IPv6
                            active=1,                           # (int)     set 0 to disable app profile
                            anti_tampering=False,               # (bool)    enable if True, disable if False
                            app_bypass_id=None,                 # (str/list) list of bypasses as strings or single string that is the id of app bypass
                            custom_app_bypass_id=None,          # (str/list) list of bypasses as strings or single string that is the id of custom app bypass
                            reactivateAntiTamperingTime=0,      # (int)     number of minutes before enabling anti tampering service
                            enableZDP=False,                    # (bool)    True to enable ZDP, False to disable
                            ZDP_disable_password=None,          # (str)     send a string to have a ZDP disable password
                            useProxyPortForT1=False,            # (bool)    enable if True, disable if False
                            useProxyPortForT2=False,            # (bool)    enable if True, disable if False
                            reactivateWebSecurityMinutes=None,  # (int)     number of minutes before enabling internet security service
                            Operating_System=None,              # (str)     Operating system on which Device Policy must be created (Windows/Mac/Linux/Android/Ios)
                            Policy_Name=None,                   # (str)     device policy whose configs must be edited
                            forwarding_profile_name=None,
                            bypass_mms_apps=False,              # (bool)    enable if True, disable if False - Applicable only for android
                            quota_in_roaming=False,             # (bool)    enable if True, disable if False - Applicable only for android
                            quota_limit=None,                   # (int) Set the quota limit in MB - ranges from 1 - 10000 - Applicable only for android
                            wifi_ssid=None,                     # (string) Set the wifi ssid for quota - Applicable only for android
                            block_notification_message=None,    # (string) Display a custom message when blocked
                            monthly_billing_day=None,           # (int) Ranges from 1 - 10000 - Applicable only for android
                            bypass_android_apps=None,           # (string) Comma seperated strings without white spaces, indicating which applications to be bypassed
                            android_apps_on_allowlist=None      # (string) Comma seperated strings without white spaces, indicating which applications to be allowed
                            ):
        """
        Note: Supports Win/Mac Only
        Work Flow
        ----------
            1. get/refresh definitions if necessary. get auth token. get device policy and forwarding profile ID
            2. set payload according to recieved function parameters, accordingly for windows/mac
            3. send post request, validate response is 200
        """

        def exception_dealer():
            if refresh == True and failure_expected == True:
                self._Logger.info("Log :: Device policy Edit Failed - As Expected")
                return

        try:
            if refresh == True:
                self.Definition = admin_res.admin_res(self.cloud)
            if Operating_System:
                if Operating_System=="Linux":
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_linux["linuxPolicy"]["install_ssl_certs"]="1"
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_linux["device_type"] = 5
                    if ZIA_disable_password: 
                        self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_linux['linuxPolicy']["disable_password"] = ZIA_disable_password
                    if logout_password:
                        self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_linux['linuxPolicy']["logout_password"] = logout_password
                    if uninstall_password:
                        self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_linux['linuxPolicy'][
                            "uninstall_password"] = uninstall_password
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD = copy.deepcopy(
                        self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_linux)
            elif self.os_version=="MAC":
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_mac["macPolicy"]["install_ssl_certs"]="1"
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_mac["device_type"] = 4
                if ZIA_disable_password: 
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_mac['macPolicy']["disable_password"] = ZIA_disable_password
                if logout_password:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_mac['macPolicy']["logout_password"] = logout_password
                if uninstall_password:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_mac['macPolicy'][
                        "uninstall_password"] = uninstall_password
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD = copy.deepcopy(
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_mac)
            elif self.os_version=="Windows":
                if not active:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_win["active"] = "0"     # added this to disable app profile 
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_win["windowsPolicy"]["install_ssl_certs"] = "1"
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_win["device_type"] = '3'
                if ZIA_disable_password:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_win['windowsPolicy'][
                        "disable_password"] = ZIA_disable_password
                if logout_password:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_win['windowsPolicy']["logout_password"] = logout_password
                if uninstall_password:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_win['windowsPolicy'][
                        "uninstall_password"] = uninstall_password
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD = copy.deepcopy(
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_win)
            elif self.os_version=="Android":
                if not active:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android["active"] = "0"     # added this to disable app profile 
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android["androidPolicy"]["install_ssl_certs"] = "1"
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android["device_type"] = '2'
                if ZIA_disable_password:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android['androidPolicy'][
                        "disable_password"] = ZIA_disable_password
                if logout_password:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android['androidPolicy']["logout_password"] = logout_password
                if uninstall_password:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android['androidPolicy'][
                        "uninstall_password"] = uninstall_password

                if bypass_mms_apps:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android['androidPolicy'][
                        "bypass_mms_apps"] = "1"
                if quota_in_roaming:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android['androidPolicy'][
                        "quota_in_roaming"] = "1"
                if quota_limit:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android['androidPolicy'][
                        "limit"] = quota_limit
                if wifi_ssid:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android['androidPolicy'][
                        "wifi_ssid"] = wifi_ssid
                if block_notification_message:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android['androidPolicy'][
                        "custom_text"] = block_notification_message
                if monthly_billing_day:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android['androidPolicy'][
                        "billing_day"] = monthly_billing_day
                if bypass_android_apps:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android['androidPolicy'][
                        "bypass_android_apps"] = bypass_android_apps
                if android_apps_on_allowlist:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android['androidPolicy'][
                        "allowed_apps"] = android_apps_on_allowlist
                    
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD = copy.deepcopy(self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_android)
            

            self.audit_logs_start_time = int(time.time() * 1000)
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['forwardingProfileId'] = self.Get_Forwarding_Profile_ID(forwarding_profile_name = forwarding_profile_name)
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["groupAll"] = 1
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['description'] = "POLICY EDITED"
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['name'] = (self.device_policy_name if not Policy_Name else Policy_Name)
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["id"] = self.Get_Device_Policy_ID(Policy_Name = Policy_Name, Operating_System=Operating_System)
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['groups'] = []
            # self.Definition.MOBILE_ADMIN_HEADER_FULL['auth-token'] = self.Authenticate_To_Mobile_Admin()
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["policyExtension"]["enableZdpService"] = (1 if enableZDP else 0)
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["policyExtension"]["zdpDisablePassword"] = (str(ZDP_disable_password) if (enableZDP and ZDP_disable_password) else  "")
            if Vpn_Bypass != None:
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["policyExtension"]["vpnGateways"] = str(Vpn_Bypass)
            if Exclude != None:
                if common_exclude_url == None:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["policyExtension"][
                        "packetTunnelExcludeList"] = "10.0.0.0/8,172.16.0.0/12,192.168.0.0/16,224.0.0.0/4" + "," + str(
                        Exclude)
                elif common_exclude_url == 0:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["policyExtension"]["packetTunnelExcludeList"] = str(
                        Exclude)
                else:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["policyExtension"]["packetTunnelExcludeList"] = str(
                        common_exclude_url) + "," + str(Exclude)
            if Include != None:
                if common_include_url == None:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["policyExtension"]["packetTunnelIncludeList"] = "0.0.0.0/0" + "," + str(Include)
                elif common_include_url == 0:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["policyExtension"]["packetTunnelIncludeList"] = str(
                        Include)
                else:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["policyExtension"]["packetTunnelIncludeList"] = str(
                        common_include_url) + "," + str(Include)
            
            if app_bypass_id:
                # this is a windows only feature
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["wfpDriver"] = "1"
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["windowsPolicy"]["wfpDriver"] = 1
                if type(app_bypass_id) == list:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["bypassAppIds"] = [str(each) for each in app_bypass_id]
                else:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["bypassAppIds"] = [str(app_bypass_id)]
            if custom_app_bypass_id:
                if type(custom_app_bypass_id) == list:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["bypassCustomAppIds"] = [str(each) for each in custom_app_bypass_id]
                else:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["bypassCustomAppIds"] = [str(custom_app_bypass_id)]


            # added these flags to exclude and include ipv6 destination addresses             
            if Exclude_ipv6 != None:
                if common_exclude_ipv6_url == None:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["policyExtension"][
                        "packetTunnelExcludeListForIPv6"] = "[FF00::/8],[FE80::/10],[FC00::/7]" + "," + str(Exclude_ipv6)
                else:
                    self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["policyExtension"][
                        "packetTunnelExcludeListForIPv6"] = str(common_exclude_ipv6_url) + ',' + str(Exclude_ipv6)

            if Include_ipv6 != None:
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["policyExtension"]["packetTunnelIncludeListForIPv6"] = Include_ipv6

            if anti_tampering!=None:
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["policyExtension"]["enableAntiTampering"] = (1 if anti_tampering==True else 0)
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["policyExtension"]["reactivateAntiTamperingTime"] = int(reactivateAntiTamperingTime)
            if reactivateWebSecurityMinutes != None:
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["reactivateWebSecurityMinutes"] = int(reactivateWebSecurityMinutes)
            if pac != None:
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["pac_url"] = str(pac)
                if os_version != "MAC": self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['windowsPolicy']['pacDataPath'] = str(pac)
            if DNS_Include:
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['policyExtension']["packetTunnelDnsIncludeList"] = DNS_Include
            if DNS_Exclude:
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['policyExtension']["packetTunnelDnsExcludeList"] = DNS_Exclude
            if ZPA_disable_password:
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["disable_password_for_zpa"] = ZPA_disable_password
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['policyExtension']["zpaDisablePassword"] = ZPA_disable_password
            if ZDX_disable_password:
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["disable_password_for_zdx"] = ZPA_disable_password
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['policyExtension']["zdxDisablePassword"] = ZDX_disable_password
            if applyDefaultFP == True:
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['forwardingProfileId'] = 0
            if revert_zcc:
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['enableZCCRevert'] = "1"
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['policyExtension']['enableZCCRevert'] = "1"
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['policyExtension']['zccRevertPassword'] = (
                    "" if revert_password == None else revert_password)
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['policyExtension']['fallbackToGatewayDomain'] = "1" if fallback_to_gateway == True else "0"

            if Group_ID:
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["groups"] = Group_ID if type(Group_ID)==list else [Group_ID]
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["groupAll"]=0
                
            if enableWFPDriver:
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["wfpDriver"]="1"                         # here value is str
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["windowsPolicy"]["wfpDriver"]=1          # here value is int
        
            if directFlowLoggingToggle!= 0 or loopbackFlowLoggingToggle!=0 or intranetFlowLoggingToggle!=0 or vpnFlowLoggingToggle!=0 or zpaFlowLoggingToggle!=0 or vpnTunnelFlowLoggingToggle!=0 or zccBlockedTrafficToggle!=0:
                Flow_Logger_Config_Dict={}
                Flow_Logger_Config_Dict['zpa']=zpaFlowLoggingToggle
                Flow_Logger_Config_Dict['vpn']=vpnFlowLoggingToggle
                Flow_Logger_Config_Dict['vpn_tunnel']=vpnTunnelFlowLoggingToggle
                Flow_Logger_Config_Dict['direct']=directFlowLoggingToggle
                Flow_Logger_Config_Dict['loopback']=loopbackFlowLoggingToggle
                Flow_Logger_Config_Dict['zcc_blocked_traffic']=zccBlockedTrafficToggle
                Flow_Logger_Config_Dict['intranet']=intranetFlowLoggingToggle
                flowLoggerConfig=json.dumps(Flow_Logger_Config_Dict).replace('"','\"')

                #self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["flowLoggerConfig"]=flowLoggerConfig
                self._Logger.info("\n\n\n\n\n\n dumping config{0} \n\n\n\n\n\n\n\n".format(flowLoggerConfig))
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["windowsPolicy"]["flowLoggerConfig"]=flowLoggerConfig

            if len(appServiceIds)!=0:
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["appServiceIds"]=appServiceIds
            
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["sendDisableServiceReason"] = ("1" if disable_reason else "0")

            if ZIA_posture:
                self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['ziaPostureConfigId'] = self.Get_ZIA_Posture_Id(ZIA_posture)
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['policyExtension']["disableDNSRouteExclusion"] = "1" if Disable_Dns_Route_Exclusion else "0"
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD["disableDNSRouteExclusion"] = "1" if Disable_Dns_Route_Exclusion else "0"

            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['policyExtension']["useProxyPortForT1"] = "1" if useProxyPortForT1 else "0"
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD['policyExtension']["useProxyPortForT2"] = "1" if useProxyPortForT2 else "0"
            self._Logger.info("\n\nEdit Device Policy\nurl:{0}\nheader:{1}\npayload:{2}\n\n".format(self.Definition.EDIT_POLICY_URL,self.Definition.MOBILE_ADMIN_HEADER_FULL,json.dumps(self.Definition.EDIT_DEVICE_POLICY_PAYLOAD)))

            self.AuthResult = self.REQ(request_type="post", url=self.Definition.EDIT_POLICY_URL,
                                       data=json.dumps(self.Definition.EDIT_DEVICE_POLICY_PAYLOAD),
                                       headers=self.Definition.MOBILE_ADMIN_HEADER_FULL)

            if self.AuthResult.status_code == 401:
                print('Invalid request! Resending request to get Auth-token via API')

                # self.Definition.MOBILE_ADMIN_HEADER_FULL['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

                self.AuthResult = self.REQ(request_type="post", url=self.Definition.EDIT_POLICY_URL,
                                           data=json.dumps(self.Definition.EDIT_DEVICE_POLICY_PAYLOAD),
                                           headers=self.Definition.MOBILE_ADMIN_HEADER_FULL)

            self.ResponseData = json.loads(self.AuthResult.text)
            self.Result = self.ResponseData.get("success")
            self._Logger.info(
                "\n\nEdit Policy response\nResponse : {0}\nAuthResult:{1}\nResult:{2}\n\n".format(self.AuthResult,
                                                                                                  self.ResponseData,
                                                                                                  self.Result))
            if check_audit_logs:
                time.sleep(30)
                self.audit_logs_end_time = int(time.time() * 1000)
                self.check_in_audit_logs(startTime=self.audit_logs_start_time, endTime=self.audit_logs_end_time,
                                         category='App Profiles')



        except requests.exceptions.Timeout as e:
            exception_dealer()
            self._Logger.error("Timeout")
            raise Exception("Timeout {}".format(self.Definition.EDIT_POLICY_URL))
        except requests.HTTPError as h:
            exception_dealer()
            self._Logger.error("HTTP Response is INVALID")
            raise Exception("HTTP Response is INVALID {}".format(self.Definition.EDIT_POLICY_URL))
        except requests.ConnectionError as c:
            exception_dealer()
            self._Logger.error("Connection Error")
            raise Exception("Connection Error {}".format(self.Definition.EDIT_POLICY_URL))
        except Exception as e:
            exception_dealer()
            self._Logger.error("Un-Expected ERROR :: %s", format(e))
            print('Error on line {}'.format(sys.exc_info()[-1].tb_lineno), type(e).__name__, e)
            raise Exception("Un-Expected ERROR :: ")
        else:
            exception_dealer()
            if self.AuthResult.status_code != 200:
                self._Logger.error("Known error :: Test_Case validate_Edit_New_Policy %s : ",
                                   self.AuthResult.status_code)
                raise Exception(
                    "Known error :: Test_Case validate_Edit_New_Policy {} : ".format(self.AuthResult.status_code))
            elif self.Result == "false":
                self._Logger.error("Validate_Edit_New_Policy Failed")
                raise Exception("Validate_Edit_New_Policy Failed")
            else:
                self._Logger.info("The policy has been edited")

    # --------------------------------------------------------------------------------------------------------------------------------------------------
    def editCustomDevicePolicy(self,
                               toEdit={},  # (dict) key-value pair of what to edit custom app profile
                               payload={}  # (dict) payload of app profile/device policy to edit
                               ):
        # self.Definition = admin_res.admin_res(self.cloud)

        '''
            Currently no use
        '''

        if len(toEdit) == 0:
            self._Logger.error("No values in toEdit dict available!")
            raise Exception("No values in toEdit dict available!")
        if len(payload) == 0:
            self._Logger.error("No Payload provided to edit!")
            raise Exception("No Payload provided to edit!")

        # print('\n\n\n')
        # print("Changing values for {}".format(payload['name']))

        for key, value in toEdit.items():
            payload[key] = value

        # print(json.dumps(payload))
        # print('-'*10)

        changedPayload = copy.deepcopy(
            self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_mac if os_version == 'MAC' else self.Definition.EDIT_DEVICE_POLICY_PAYLOAD_win)
        # for key,value in changedPayload.items():
        #     if key in payload:
        #         print('{} already present in Payload!'.format(key))
        #     else:
        #         print('{} not present in Payload'.format(key))
        #         payload[key] = value

        # self.Definition.MOBILE_ADMIN_HEADER_FULL['auth-token'] = self.Authenticate_To_Mobile_Admin()

        # # print('Changed payload is:\n{}'.format(json.dumps(payload)))

        # # print('\n\n\n')
        # response = self.REQ("post",self.Definition.EDIT_POLICY_URL,
        #                         data=json.dumps(payload), headers=self.Definition.MOBILE_ADMIN_HEADER_FULL)

        # if response.status_code == 401:
        #     print('Invalid request! Resending request to get Auth-token via API')
        #     self.Definition.MOBILE_ADMIN_HEADER_FULL['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

        #     response = self.REQ("post",self.Definition.EDIT_POLICY_URL,
        #                         data=json.dumps(changedPayload), headers=self.Definition.MOBILE_ADMIN_HEADER_FULL)

        # print(response.status_code)
        # print(response.text)

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Delete App policy")
    def Delete_Device_Policy(self,  # Function to delete device policy for windows/mac in MA
                             Operating_System=None,
                             # (str)     Operating system on which Device Policy must be deleted (Windows/Mac/Linux/Android/Ios)
                             Policy_Name=None  # (str)     Name to use to delete Device Policy
                             ):
        """
        Work Flow
        ----------
            1. get auth token
            2. set payload accordingly for windows/mac
            3. send delete request, validate response is 200
        """

        if (self.os_version == "MAC" and Operating_System == None) or Operating_System == "Mac":
            device_type = '4'
            device_policy_name = Policy_Name if Policy_Name else self.device_policy_name
        elif (self.os_version != "MAC" and Operating_System == None) or Operating_System == "Windows":
            device_type = '3'
            device_policy_name = Policy_Name if Policy_Name else self.device_policy_name
        elif Operating_System == "Linux":
            device_type = '5'
            device_policy_name = Policy_Name if Policy_Name else self.linux_device_policy_name
        elif Operating_System == "Android":
            device_type = '2'
            device_policy_name = Policy_Name if Policy_Name else self.android_device_policy_name
        elif Operating_System == "Ios":
            device_type = '1'
            device_policy_name = Policy_Name if Policy_Name else self.ios_device_policy_name
        else:
            raise Exception("Illegal Operating System Selection")

        self.Definition.Delete_Device_Policy_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
        self.Definition.Delete_Device_Policy_Url = self.Definition.Delete_Device_Policy_Url + str(
            self.Get_Device_Policy_ID(Operating_System=Operating_System,
                                      Policy_Name=Policy_Name)) + "&deviceType={}".format(device_type)
        self._Logger.info(
            "\n\nDelete_Device_Policy\nUrl:{0}\n".format(self.Definition.Delete_Device_Policy_Url))

        self.Delete_Device_Policy_Info = self.REQ(request_type="delete", url=self.Definition.Delete_Device_Policy_Url,
                                                  headers=self.Definition.Delete_Device_Policy_Header)

        if self.Delete_Device_Policy_Info.status_code == 200:
            pass
            #print("App Profile status: " + str("deleted") + " response:" + str(self.Delete_Device_Policy_Info.json()))
        else:
            raise Exception("Error :: App profile deletion didnt go well!")
        self.is_device_policy_created = False
        if not os.path.exists(self.temp_file):
            return
        file_contents = None
        with open(self.temp_file, "r") as _file:
            file_contents = _file.read().splitlines()
            dp_id_line_number = [i for i in range(len(file_contents)) if "{}dpid".format(device_type) in file_contents[i]][0]
            del file_contents[dp_id_line_number]
        _file = open(self.temp_file, "w")
        _file.write(''.join(file_contents))
        _file.close()
        self.response = self.Delete_Device_Policy_Info.json()
        #self._Logger.info("\n\nDelete_Device_Policy_response\nstatus_code:{0}\n".format(self.Delete_Device_Policy_Info.status_code))
        if ("false" in self.response["success"]) and ("error" in self.response):
            #self._Logger.error("Failure :  Deleting device Policy")
            raise Exception("Failure :  Deleting device Policy")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Create_Trusted_Network(self,        # Function to create Trusted Network in MA
    count=None,                                  # (int)     number of trusted networks to be applied
    dnsServers=None,                        # (list of str)     dns server(s) as string, seperated by commas to send multiple
    dnsSearchDomains=None,                  # (list of str)     dns search domain(s) as string, seperated by commas to send multiple
    hostnames=None,                         # (list of str)     hostname(s) as string, seperated by commas to send multiple
    resolvedIpsForHostname=None,            # (list of str)     resolved ips for hostname(s) as string. seperated by commas to send multiple
    network_name=None                       # (str)     name to use for trusted network
    ):            
        """
        Work Flow
        ----------
            1. get auth token
            2. set payload accordingly
            3. send post request, validate response is 200
        """
        self._Logger.info(f"COUNT : {count}" )
        self._Logger.info('DNS SERVERS ARE -> {}'.format(dnsServers))
        self._Logger.info('DNS SEARCH DOMAINS ARE -> {}'.format(dnsSearchDomains))
        self._Logger.info('HOSTNAMES ARE-> {}'.format(hostnames))
        self._Logger.info('RESOLVED IPs ARE -> {}'.format(resolvedIpsForHostname))

        err_count = 0
        err_list = []
        self.CreatedTrustedNetworks = []
        self.Definition = admin_res.admin_res(self.cloud)
        # self.Definition.MOBILE_ADMIN_FORWARDING_PROFILE_HEADERS['auth-token'] = self.Authenticate_To_Mobile_Admin()
        for i in range(count):
            self.Definition.Trusted_Network_Payload["networkName"] = self.TrustedNetworkName + str(i) + "_" + str(
                time.time())
            self.Definition.Trusted_Network_Payload["active"] = "1"
            if dnsServers:
                dnsServer = dnsServers[i]
                if (len(dnsServers) == count):
                    if dnsServer == 'None':
                        pass
                    else:
                        self._Logger.info(' ***************** ADDING DNSSERVER *****************')
                        self.Definition.Trusted_Network_Payload["dnsServers"] = dnsServer
                        self._Logger.info(self.Definition.Trusted_Network_Payload["dnsServers"])
                else:
                    self._Logger.error("Count and DNS SERVERS count mismatch")
            if dnsSearchDomains:
                dnsSearchDomain = dnsSearchDomains[i]
                if (len(dnsSearchDomains) == count):
                    if dnsSearchDomain == 'None':
                        pass
                    else:
                        self._Logger.info('***************** ADDING DNS SEARCH DOMAINS *****************')
                        self.Definition.Trusted_Network_Payload["dnsSearchDomains"] = dnsSearchDomain
                        self._Logger.info(self.Definition.Trusted_Network_Payload["dnsSearchDomains"])
                else:
                    self._Logger.error("Count and DNS SEARCH DOMAIN count mismatch")
            if hostnames and resolvedIpsForHostname:
                hostname = hostnames[i]
                resolvedIpsForHostName = resolvedIpsForHostname[i]
                if (hostname == 'None') or (resolvedIpsForHostName == 'None'):
                    pass
                else:
                    if (len(hostnames) == count and len(resolvedIpsForHostname) == count):
                        self._Logger.info("***************** ADDING HOSTNAMES AND RESOLVEDIPS *****************")
                        if "," in hostname and "," in resolvedIpsForHostName:
                            if len(hostname.split(",")) == len(resolvedIpsForHostName.split(",")):
                                self.Definition.Trusted_Network_Payload["hostnames"] = hostname
                                self.Definition.Trusted_Network_Payload[
                                    "resolvedIpsForHostname"] = resolvedIpsForHostName
                            else:
                                raise Exception("len mismatch for hostnames and resolvedipsforhostname {0} {1}".format(
                                    len(hostname), len(resolvedIpsForHostName)))
                        else:
                            self._Logger.info('HOSTNAME IS {}'.format(hostname))
                            self._Logger.info('RESOLVEDIP IS {}'.format(resolvedIpsForHostName))
                            self.Definition.Trusted_Network_Payload["hostnames"] = hostname
                            self.Definition.Trusted_Network_Payload["resolvedIpsForHostname"] = resolvedIpsForHostName
            if self.Definition.Trusted_Network_Payload["dnsServers"] == '' and self.Definition.Trusted_Network_Payload[
                "dnsSearchDomains"] == '' and self.Definition.Trusted_Network_Payload["hostnames"] == '':
                self._Logger.info('CANNOT CREATE THIS NETWORK ~ no criteria given\n')
                self._Logger.info(
                    '----------------------------------------------TRUSTED NETWORK NOT CREATED------------------------------------------------------')
                pass
            else:
                self.Profile = self.REQ(request_type="post", url=self.Definition.Trusted_Network_Url,
                                        data=json.dumps(self.Definition.Trusted_Network_Payload),
                                        headers=self.Definition.MOBILE_ADMIN_FORWARDING_PROFILE_HEADERS, verify=False)

                if self.Profile.status_code == 200:
                    self._Logger.info("status_code is {}".format(self.Profile.status_code))
                    res = self.Profile.json()
                    self._Logger.info(res)
                    if 'error' in res.keys():
                        self._Logger.info('\n!!! ********** ERROR : {}\n'.format(res['error']))
                        err_count += 1
                        err_list.append(res['error'])
                        self._Logger.info('\n!!!!!!!!!!!!!!!!!!!!!!!!!!!!----------------- TRUSTED NETWORK NOT CREATED -------------------------!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!')
                    else:
                        self._Logger.info('\n----------------------------------------------TRUSTED NETWORK CREATED SUCCESSFULLY------------------------------------------------------')
                        self._Logger.info('____________________________________________{}_____________________________________________'.format(
                                self.Definition.Trusted_Network_Payload["networkName"]))
                        self.CreatedTrustedNetworks.append(self.Definition.Trusted_Network_Payload["networkName"])

                else:
                    self._Logger.error(
                        '_____________________________________________________ERROR_________________________________________________________________________________')
                    raise Exception(
                        "\nINVALID STATUS CODE FOR TRUSTED NETWORK CREATION  {}".format(self.Profile.status_code))
            if err_count > 0:
                self._Logger.error(
                    '{} out of {} trusted networks were not created, error codes are {}\n\n\n'.format(err_count, count,
                                                                                                      err_list))
                raise Exception(
                    '{} out of {} trusted networks were not created, error codes are {}\n\n\n'.format(err_count, count,
                                                                                                      err_list))

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Get_Trusted_Network_ID(self,  # Function to get trusted network ID
                               network_name=None,  # (str)     name of trusted network for which id is required
                               getAllTrustedNetwork=False,
                               # (bool)    fetches data for all the available trusted networks, if false gives data only for networks with automated in their names
                               returnType='id'
                               # (str)     defines whether to return id or guid(used in case of zpa)
                               ):
        """
        Work Flow
        ----------
            1. get auth token
            2. set payload accordingly
            3. send post request, validate response is 200
            4. fetch trusted network id based on its name
        """
        # auth_token = self.Authenticate_To_Mobile_Admin()
        # self.Definition.Get_TN_ID_Header['auth-token']=auth_token
        self._Logger.info("Now fetching trusted networks list\n")
        Profile = self.REQ(request_type="get", url=self.Definition.Get_TN_ID_Url,
                           headers=self.Definition.Get_TN_ID_Header, verify=False)
        response_json = Profile.json()
        if Profile.status_code == 200:
            TrustedNetworkList = response_json['trustedNetworkContracts']  # list of dictionaries
            dataDict = {}
            if network_name:  # this block will get trusted network with given name
                for i in range(len(TrustedNetworkList)):
                    if TrustedNetworkList[i]['networkName'] == network_name:
                        dataDict[TrustedNetworkList[i]['networkName']] = {'id': TrustedNetworkList[i]['id'],
                                                                          'guid': TrustedNetworkList[i]['guid']}
            else:
                for _dict in TrustedNetworkList:
                    if getAllTrustedNetwork:  # this block will add all the available trusted networks
                        dataDict[_dict['networkName']] = {'id': _dict['id'], 'guid': _dict['guid']}
                    else:  # this block will get trusted networks with automated in their name
                        if self.TrustedNetworkName in _dict['networkName']:
                            dataDict[_dict['networkName']] = {'id': _dict['id'], 'guid': _dict['guid']}

            if len(dataDict) == 0:  # if no trusted network found, raise exception
                self._Logger.error("No Trusted Network Found")
                raise Exception("ERROR :: No Trusted Network Found")
            self._Logger.info("Available Trusted Networks :\n")
            for TrustedNetworkName, TrustedNetworkID in dataDict.items():
                self._Logger.info("{} - {}".format(TrustedNetworkName, TrustedNetworkID))
            returnList = []
            if returnType == 'id':
                for val in dataDict.values():
                    returnList.append(str(val['id']))
            else:
                for val in dataDict.values():
                    returnList.append(str(val['guid']))
            return returnList
        else:
            self._Logger.error("Invalid status code for getting trusted network list")
            raise Exception("ERROR :: Invalid status code for getting trusted network list")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Delete_Trusted_Network(self,  # Function to delete Trusted network
                               network_name=None,  # (str)     name of trusted network
                               network_id=None,  # (list of str)     list of trusted network id
                               deleteAllNetworks=False  # (bool)    true:delete all available trusted networks
                               ):
        """
        Work Flow
        ----------
            1. get auth token. set payload accordingly.
            2. get trusted network id either based on name
            3. send delete request, validate response is 200
        """
        # auth_token = self.Authenticate_To_Mobile_Admin()
        # self.Definition.Delete_TN_Header['auth-token']=auth_token
        err_count = 0
        err_list = []
        if network_id == None:
            network_id = self.Get_Trusted_Network_ID(network_name, deleteAllNetworks)

        for i in range(len(network_id)):
            DeleteRequest = self.REQ(request_type="delete", url=self.Definition.Delete_TN_Url + network_id[i],
                                     headers=self.Definition.Delete_TN_Header, verify=False)
            time.sleep(2)
            if DeleteRequest.status_code == 200:
                response_json = DeleteRequest.json()
                self._Logger.info("\n\nDelete_Trusted_Net\nstatus_code:{0}\n".format(DeleteRequest.status_code))
                if response_json["success"] == "true":
                    self._Logger.info("{} Deleted\n".format(network_id[i]))
                if response_json['success'] == 'false':
                    self._Logger.error('CANNOT DELETE - {}'.format(network_id[i]))
                    err_count += 1
                    err_list.append(response_json['error'])
        if err_count > 0:
            self._Logger.error('{} out of {} trusted networks were not deleted, error codes are {}\n\n\n'.format(err_count,
                                                                                                  len(network_id),
                                                                                                  err_list))
            raise Exception('{} out of {} trusted networks were not deleted, error codes are {}\n\n\n'.format(err_count,
                                                                                                              len(network_id),
                                                                                                              err_list))

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    def Get_Device_Hardware_Fingerprint(self):  # Function to fetch hardware fingerprint_ from ZSATray logs
        """
        Work Flow
        ----------
            1. use log ops to perform a single search to open latest tray file in zscaler log path.
            2. find the line that contains necessary keywords. preprocess data. return hardware fingerprint.
        """
        from common_lib.log_ops import log_ops
        self.log_ops = log_ops(logger=self._Logger)

        if self.os_version == "Windows":
            self.log_ops.search_log_file(file="ZSATrayManager",
                                         search_mode=None,
                                         start_line=None,
                                         words_to_find_in_line=["udid is"],
                                         break_after_first_hit=True)
            line_of_interest = copy.deepcopy(self.log_ops.recent_log_line)
            line_of_interest = line_of_interest.split("udid is")[1].strip()
        else:
            self.log_ops.search_log_file(file="ZSATray",
                                         log_path="/Users/<USER>/Library/Application Support/com.zscaler.Zscaler/".format(
                                             os.getcwd().split("/")[2]),  # Tray log path
                                         search_mode=None,
                                         start_line=None,
                                         words_to_find_in_line=["udid"],
                                         break_after_first_hit=True)
            line_of_interest = copy.deepcopy(self.log_ops.recent_log_line)
            line_of_interest = line_of_interest.split("udid:")[1].strip()

        return line_of_interest

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    def Get_ID_with_User_Name(self,  # Function to get ID with username from MA
                              User):  # (str)     Username based on which, ID has to be found
        """     This is not great! refrain from using
        Work Flow
        ----------
            1. get auth token. send get request
            2. validate response is 200.
            3. in returned list of dicts, find the dict, with username as 'User_Name'. return its ID
        """
        # self.Definition.Get_ID_with_User_Name_header["auth-token"] = self.Authenticate_To_Mobile_Admin()

        response = self.REQ(request_type="get", url=self.Definition.Get_ID_with_User_Name_url,
                            headers=self.Definition.Get_ID_with_User_Name_header,
                            data=json.dumps(self.Definition.Get_ID_with_User_Name_payload))
        # Validate Success
        if response.status_code == 200:
            print("\n\n useridFetch - success ")
        else:
            self._Logger.error("userid fetching in MA didnt go well!")
            raise Exception("Error :: userid fetching in MA didnt go well!")

        for each_entry in response.json():
            if each_entry["loginName"] == User:
                return each_entry["id"]

        self._Logger.error("Error :: Unable to find any username:{}".format(User))
        raise Exception("Error :: Unable to find any username:{}".format(User))

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Fetchh Enrolled Devices Details")
    def Fetch_Enrolled_Devices_Details(self,  # Function to get Enrolled Devices Details
                                       get_device_id=False,  # (bool)    return device id from enrolled devices info
                                       get_zcc_version=False,  # (bool)    return ZCC version from enrolled devices info
                                       get_registration_status=False,
                                       # (bool)    True will return bool, True if registered, False if not
                                       fetch_this_device_details=False,
                                       # (bool)    fetch details of a specific details from enrolled devices section
                                       ):
        """
        Work Flow
        ----------
            1. fetch auth token. fetch enrolled devices from MA.
            2. fetch all details of a device if specified.
        """
        # self.Definition.Fetch_Enrolled_Devices_Details_header["auth-token"] = self.Authenticate_To_Mobile_Admin()

        self.Fetch_Enrolled_Devices_Details_response = self.REQ(request_type="post",
                                                                url=self.Definition.Fetch_Enrolled_Devices_Details_url,
                                                                headers=self.Definition.Fetch_Enrolled_Devices_Details_header,
                                                                data=json.dumps(
                                                                    self.Definition.Fetch_Enrolled_Devices_Details_payload))

        if self.Fetch_Enrolled_Devices_Details_response.status_code == 401:
            print('Invalid request! Resending request to get Auth-token via API')
            # self.Definition.Fetch_Enrolled_Devices_Details_header['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

            self.Fetch_Enrolled_Devices_Details_response = self.REQ(request_type="post",
                                                                    url=self.Definition.Fetch_Enrolled_Devices_Details_url,
                                                                    headers=self.Definition.Fetch_Enrolled_Devices_Details_header,
                                                                    data=json.dumps(
                                                                        self.Definition.Fetch_Enrolled_Devices_Details_payload))

        if self.Fetch_Enrolled_Devices_Details_response.status_code == 200:
            print("\n\n Fetch_Enrolled_Devices_Details - success ")
        else:
            self._Logger.error("Error :: Fetch_Enrolled_Devices_Details  fetching in MA didnt go well!")
            raise Exception("Error :: Fetch_Enrolled_Devices_Details  fetching in MA didnt go well!")
        self._Logger.info("\n--Fetch_Enrolled_Devices_Details--\nurl:{0}\n\nheader:{1}\n\nresponse:{2}\n\n".format(
            self.Definition.Fetch_Enrolled_Devices_Details_url, self.Definition.Fetch_Enrolled_Devices_Details_header,
            self.Fetch_Enrolled_Devices_Details_response.status_code))

        #print(self.Fetch_Enrolled_Devices_Details_response.json())  # debug only
        UDID = self.Get_Device_Hardware_Fingerprint()
        #print(UDID)
        # print(self.Fetch_Enrolled_Devices_Details_response.json())
        for each in self.Fetch_Enrolled_Devices_Details_response.json()["deviceList"]:
            # if ('udid', UDID) in each.items():
            if ('udid', UDID) in each.items():
                print('Each is --->\n{}'.format(json.dumps(each, indent=4)))
                self._Logger.info("Success :: id found - {}".format(each["id"]))
                if get_device_id or fetch_this_device_details: self.device_id = each["id"]
                if get_zcc_version: self.zcc_version = each["agentVersion"]
                if get_registration_status: return (True if "Registered" == each["registrationState"] else False)

        orignalURL = self.Definition.Extra_Details_url
        if fetch_this_device_details:
            self.Definition.Extra_Details_url = self.Definition.Extra_Details_url + str(self.device_id)
            # self.Definition.Extra_Details_header["auth-token"] = self.Authenticate_To_Mobile_Admin()
            self.Definition.Extra_Details_payload["id"] = self.device_id

            self.Extra_Details_response = self.REQ(request_type="get", url=self.Definition.Extra_Details_url,
                                                   headers=self.Definition.Extra_Details_header,
                                                   data=json.dumps(self.Definition.Extra_Details_payload))

            if self.Extra_Details_response.status_code == 401:
                print('Invalid request! Resending request to get Auth-token via API')
                # self.Definition.Extra_Details_header['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

                self.Extra_Details_response = self.REQ(request_type="get", url=self.Definition.Extra_Details_url,
                                                       headers=self.Definition.Extra_Details_header,
                                                       data=json.dumps(self.Definition.Extra_Details_payload))

            if self.Extra_Details_response.status_code == 200:
                self._Logger.info("\n\n Extra_Details Fetch - success ")
            else:
                self._Logger.error("Error :: Extra_Details fetching in MA didnt go well!")
                raise Exception("Error :: Extra_Details fetching in MA didnt go well!")

            self._Logger.info("\n--Extra_Details--\n\nurl:{0}\n\nheader:{1}\n\npayload:{2}\n\nresponse:{3}\n\n".format(
                self.Definition.Extra_Details_url, self.Definition.Extra_Details_header,
                self.Definition.Extra_Details_payload, self.Extra_Details_response.status_code))

            self.Definition.Extra_Details_url = orignalURL
            return self.Extra_Details_response.json()

    # ----------------------------------------------------------------------------------------------------------------------------------------------------
    def remoteFetchLogs(self,
                        enrolledDeviceID  # (int/str) takes value of Enrolled Device ID to enable remote fetch logs.
                        ):
        '''
            start remote fetch logs on MA
                1. Get ID of via steps
                2. Enable Remote Fetch Logs on MA
        '''
        print('Enrolled Device ID is {}'.format(enrolledDeviceID))

        payload = copy.deepcopy(self.Definition.remoteFetchLogsPayload)
        payload['deviceId'] = int(enrolledDeviceID)

        # self.Definition.remoteFetchLogsHeaders['auth-token'] = self.Authenticate_To_Mobile_Admin()

        response = self.REQ(request_type="post", url=self.Definition.remoteFetchLogsURL, data=json.dumps(payload),
                            headers=self.Definition.remoteFetchLogsHeaders)

        if self.Extra_Details_response.status_code == 401:
            print('Invalid request! Resending request to get Auth-token via API')
            # self.Definition.remoteFetchLogsHeaders['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

            response = self.REQ(request_type="post", url=self.Definition.remoteFetchLogsURL, data=json.dumps(payload),
                                headers=self.Definition.remoteFetchLogsHeaders)

        if response.status_code >= 200 and response.status_code < 300:
            print(json.dumps(response.json()))
            self._Logger.info("Remotefetch logs enabled Successfully!")
        else:
            line = 'Status code for request is {}'.format(response.status_code)
            self._Logger.info(line)
            raise Exception(line)

        # .post(self.Definition.CUSTOM_CERT_URL,data=json.dumps(payload),headers=header)

    # -------------------------------------------------------------------------------------------------------------------
    def Search_Enrolled_Devices(self,  # Function to search enrolled devices
                                action=None,  # (str)     search action to be performed
                                #           accepted values: verify_version / verify_(zia,zpa,zdx)_(enabled,disabled,active,inactive,not_applicable)
                                version=None,  # (str)     version of ZCC
                                shouldBePresent=True):  # (bool)    specify if requested log should be found/not
        """
        Work Flow
        ----------
            1. assume enrolled devices details are fetched.
            2. get response json file, look into it as required
        """
        self.isFound = False
        self.most_recent_log = None

        def success(recent_log):
            self.isFound = True
            self.most_recent_log = recent_log

        contents = []
        contents.append(copy.deepcopy(self.Fetch_Enrolled_Devices_Details_response.json()))

        for each_entry in contents:
            # if not ('owner', self.CONST.dict3["owner"]) in each_entry.items(): continue
            # if not ('os_version', self.CONST.dict3['os_version']) in each_entry.items(): continue
            if not ('unique_id', self.Get_Device_Hardware_Fingerprint()) in each_entry.items(): continue

            if "verify_version" in action and version != None and version == each_entry["agent_version"]: success(
                each_entry)

            if "verify_zia" in action:
                if "_enabled" in action and each_entry["ziaEnabled"].lower() == 'true':
                    success(each_entry)
                elif "_disabled" in action and each_entry["ziaEnabled"].lower() == 'false':
                    success(each_entry)
                elif "_health" in action:
                    if "_active" in action and each_entry["ziaHealth"].lower() == 'active':
                        success(each_entry)
                    elif "_inactive" in action and each_entry["ziaHealth"].lower() == 'inactive':
                        success(each_entry)

            if "verify_zpa" in action:
                if "_enabled" in action and each_entry["zpaEnabled"].lower() == 'true':
                    success(each_entry)
                elif "_disabled" in action and each_entry["zpaEnabled"].lower() == 'false':
                    success(each_entry)
                elif "_health" in action:
                    if "_active" in action and each_entry["zpaHealth"].lower() == 'active':
                        success(each_entry)
                    elif "_inactive" in action and each_entry["zpaHealth"].lower() == 'inactive':
                        success(each_entry)

            if "verify_zdx" in action:
                if "_enabled" in action and each_entry["zdxEnabled"].lower() == 'true':
                    success(each_entry)
                elif "_disabled" in action and each_entry["zdxEnabled"].lower() == 'false':
                    success(each_entry)
                elif "_health" in action:
                    if "_active" in action and each_entry["zdxHealth"].lower() == 'active':
                        success(each_entry)
                    elif "_inactive" in action and each_entry["zdxHealth"].lower() == 'inactive':
                        success(each_entry)

        time.sleep(5)
        if self.isFound:
            if not shouldBePresent:
                self._Logger.error("Found hits for search: {} - Not as Expected".format(action))
                raise Exception("Found hits for search: {} - Not as Expected".format(action))
            self._Logger.info("Success :: Found hits for search: {}".format(action))
        else:
            if not shouldBePresent:
                self._Logger.info("Success :: Not Found hits for search: {} - as Expected".format(action))
            else:
                self._Logger.error("Failure :: Did not found hits for search: {}".format(action))
                raise Exception("Failure :: Did not found hits for search: {}".format(action))

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    def Export_File(self,  # Function to export service_status/device_details in MA Enrolled Details
                    file="service_status",  # (str)     file type should be service_status/device_details
                    User=None,  # (str)     User for ZCC login
                    save_as_csv=True):  # (bool)    save the info into csv file
        """
        Work Flow
        ----------
        """
        # self.Definition.Device_Details_Header["auth-token"] = self.Authenticate_To_Mobile_Admin()

        self.first_response = self.REQ(request_type="get",
                                       url=self.Definition.Service_Status_url if file == "service_status" else self.Definition.Device_Details_url,
                                       headers=self.Definition.Device_Details_Header)
        self.first_response_json = self.first_response.json()
        print([(each, type(each)) for each in self.first_response_json])
        if self.first_response.status_code == 200:
            self._Logger.info("\n\n downloadDeviceDetails - success ")
        else:
            self._Logger.error("Error :: downloadDeviceDetails in MA didnt go well!")
            raise Exception("Error :: downloadDeviceDetails in MA didnt go well!")
        self._Logger.info("\n--downloadDeviceUrl--\nurl:{0}\nheader:{1}\nresponse:{2}\n\n".format(
            self.Definition.Service_Status_url if file == "service_status" else self.Definition.Device_Details_url,
            self.Definition.Device_Details_Header, self.first_response.status_code))

        export_details_url = str(self.first_response_json) + str(
            "&configState=0&configState=1&type=0&osId=0&user=0&version=&searchProperty=id")

        self.Definition.Device_Details_payload["id"] = self.first_response_json.split("?id=")[1]
        self.Definition.Device_Details_payload["searchProperty"] = self.first_response_json.split("?id=")[1]

        self.Fetch_Enrolled_Devices_Details(get_device_id=True)
        self.Definition.Device_Details_payload["user"] = 0 if User == None else int(self.device_id)
        if self.Definition.Device_Details_payload["user"] != 0: export_details_url = export_details_url.replace(
            "user=0", "user={}".format(self.Definition.Device_Details_payload["user"]))
        self.second_response = self.REQ(request_type="get", url=export_details_url,
                                        headers=self.Definition.Device_Details_Header,
                                        data=json.dumps(self.Definition.Device_Details_payload))

        if self.second_response.status_code == 200:
            self._Logger.info("\n\n exportDeviceDetails - success ")
        else:
            self._Logger.error("Error :: exportDeviceDetails in MA didnt go well!")
            raise Exception("Error :: exportDeviceDetails in MA didnt go well!")

        self._Logger.info(
            "\n\n--exportDeviceDetails--\n\nurl:{0}\n\nheader:{1}\n\npayload:{2}\n\nresponse:{3}\n\n".format(
                export_details_url, self.Definition.Device_Details_Header, self.Definition.Device_Details_payload,
                self.second_response.status_code))

        if save_as_csv:
            if not os.path.exists(self.temp_path):
                os.makedirs(self.temp_path)
            else:
                self.Remove_Export_File()
                if file == "device_details":
                    new_file = self.export_device_details_file_path
                elif file == "service_status":
                    new_file = self.export_service_status_file_path
            _file = open(new_file, "a")
            _file.write(self.second_response.text)
            self._Logger.info("\n export_{}: csv created in /TMP\n".format(file))
            _file.close()
            if not os.path.exists(self.export_service_status_file_path):
                raise Exception("Error :: csv file not found")

    # -------------------------------------------------------------------------------------------------------------------
    def Search_Export_File(self, action=None, user=None, shouldBePresent=True, just_return_contents=None):
        # --------------    search in export file from MA Enrolled devices  -------------------------------------
        # prerequisite: self.export_file
        # action: verify_(zia,zpa,zdx)_(enabled,disabled,active,inactive,not_applicable)
        # shouldBePresent: bool

        self.isFound = False
        self.most_recent_log = None

        def success(recent_log):
            self.isFound = True
            self.most_recent_log = recent_log

        with open(self.export_service_status_file_path, mode="r") as infile:
            contents = infile.read()
            contents = contents.replace('"\n"', '@@@@@@@@').replace('\r\n', '').replace('@@@@@@@@', '"\n"').replace('"',
                                                                                                                    '').split(
                "\n")
            contents = [each.split(",") for each in contents]
        contents = [{key: value for key, value in zip(contents[0], contents[i])} for i in range(1, len(contents))]
        # print(contents)
        if just_return_contents: return contents

        for each_entry in contents:
            # if not ("Device type", self.CONST.dict2["Device type"]) in each_entry.items(): continue
            # if (not ("User", self.CONST.dict2["User"]) in each_entry.items()) and (not user in each_entry.values()): continue
            # if not ('Zscaler Client Connector Version', self.CONST.dict2['Zscaler Client Connector Version']) in each_entry.items(): continue
            if not ("UDID", self.Get_Device_Hardware_Fingerprint()) in each_entry.items(): continue

            # print("entered once atleast\n\n\n\n\n\n",each_entry)

            if "verify_zia" in action:
                if not ("ZIA Enabled" in each_entry.keys() and "ZIA Health" in each_entry.keys()): continue
                if "_enabled" in action and each_entry["ZIA Enabled"].lower() == 'true':
                    success(each_entry)
                elif "_disabled" in action and each_entry["ZIA Enabled"].lower() == 'false':
                    success(each_entry)
                elif "_not_applicable" in action and each_entry["ZIA Enabled"].lower() == 'not applicable':
                    success(each_entry)
                elif "_health" in action:
                    if "_active" in action and each_entry["ZIA Health"].lower() == 'active':
                        success(each_entry)
                    elif "_inactive" in action and each_entry["ZIA Health"].lower() == 'inactive':
                        success(each_entry)

            if "verify_zpa" in action:
                if not ("ZPA Enabled" in each_entry.keys() and "ZPA Health" in each_entry.keys()): continue
                if "_enabled" in action and each_entry["ZPA Enabled"].lower() == 'true':
                    success(each_entry)
                elif "_disabled" in action and each_entry["ZPA Enabled"].lower() == 'false':
                    success(each_entry)
                elif "_not_applicable" in action and each_entry["ZIA Enabled"].lower() == 'not applicable':
                    success(each_entry)
                elif "_health" in action:
                    if "_active" in action and each_entry["ZPA Health"].lower() == 'active':
                        success(each_entry)
                    elif "_inactive" in action and each_entry["ZPA Health"].lower() == 'inactive':
                        success(each_entry)

            if "verify_zdx" in action:
                if not ("ZDX Enabled" in each_entry.keys() and "ZDX Health" in each_entry.keys()): continue
                if "_enabled" in action and each_entry["ZDX Enabled"].lower() == 'true':
                    success(each_entry)
                elif "_disabled" in action and each_entry["ZDX Enabled"].lower() == 'false':
                    success(each_entry)
                elif "_not_applicable" in action and each_entry["ZDX Enabled"].lower() == 'not applicable':
                    success(each_entry)
                elif "_health" in action:
                    if "_active" in action and each_entry["ZDX Health"].lower() == 'active':
                        success(each_entry)
                    elif "_inactive" in action and each_entry["ZDX Health"].lower() == 'inactive':
                        success(each_entry)

            if action == "verify_registered":
                if not "Registration State" in each_entry.keys(): continue
                if each_entry["Registration State"].lower() == 'registered': success(each_entry)

            if action == "verify_unregistered":
                if not "Registration State" in each_entry.keys(): continue
                if each_entry["Registration State"].lower() == 'unregistered': success(each_entry)

        time.sleep(5)
        if self.isFound:
            if not shouldBePresent:
                # self._Logger.error("Found hits for search: {} - Not as Expected".format(action))
                raise Exception("Found hits for search: {} - Not as Expected".format(action))
            # self._Logger.info("Success :: Found hits for search: {}".format(action))
            self._Logger.info("Success :: Found hits for search: {}".format(action))
        else:
            if not shouldBePresent:
                # self._Logger.info("Success :: Not Found hits for search: {} - as Expected".format(action))
                self._Logger.info("Success :: Not Found hits for search: {} - as Expected".format(action))
            else:
                self._Logger.error("Failure :: Did not found hits for search: {}".format(action))
                raise Exception("Failure :: Did not found hits for search: {}".format(action))

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    def Remove_Export_File(self):  # Function to delete export files in temp file
        # works for win/mac
        if os.path.exists(self.export_device_details_file_path):
            os.remove(self.export_device_details_file_path)
        elif os.path.exists(self.export_service_status_file_path):
            os.remove(self.export_service_status_file_path)

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    def Upload_Custom_Cert(self,
                           certFile
                           ):
        certPath = os.path.join(os.getcwd(), 'Extras', certFile)
        if os.path.exists(certPath):
            self._Logger.info('Cert File Found')
            payload = self.Definition.CUSTOM_CERT_PAYLOAD.copy()
            with open(certPath, 'r') as f:
                payload['sslCert'] = f.readlines()[0]
            #self._Logger.info("PAYLOAD IS : {}".format(json.dumps(payload)))
            header = self.Definition.CREATE_FORWARDING_PROFILE_HEADER.copy()
            # header['auth-token'] = self.Authenticate_To_Mobile_Admin()

            uploadCustomCert = self.REQ(request_type="post", url=self.Definition.CUSTOM_CERT_URL,
                                        data=json.dumps(payload), headers=header)
            if uploadCustomCert.status_code == 200 and uploadCustomCert.json()['success'] == 'true':
                self._Logger.info("Custom Cert Uploaded Successfully \n")
            else:
                self._Logger.error("Unable to upload custom cert, status code : {} \n Response : {}".format(
                    uploadCustomCert.status_code, uploadCustomCert.json()))

    # ----------------------------------------------------------------------------------------------------------------------------------------------------
    def Delete_Custom_Cert(self
                           ):
        header = dict()
        # header['auth-token'] = str(self.Authenticate_To_Mobile_Admin())
        deleteCustomCert = self.REQ(request_type="delete", url=self.Definition.DELETE_CUSTOM_CERT_URL, headers=header)
        if deleteCustomCert.status_code == 200 and deleteCustomCert.json()['success'] == 'true':
            self._Logger.info("Custom Cert Deleted Successfully \n")
        else:
            err = "Unable to Delete custom cert, status code : {}".format(deleteCustomCert.status_code)
            self._Logger.error(err)
            raise Exception(err)

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    def Force_Remove_Device_MA(self):  # Function to force remove devices from MA
        """
        Work Flow
        ----------
            1. force rmove device policy from MA with ID
        """
        self.Fetch_Enrolled_Devices_Details(get_device_id=True)
        id = str(self.device_id)
        self.Definition.Force_Remove_Device_Payload["id"] = str(id)
        # self.Definition.Force_Remove_Device_Header["auth-token"] = str(self.Authenticate_To_Mobile_Admin())
        self.Definition.Force_Remove_Device_URL = self.Definition.Force_Remove_Device_URL + str(id)
        print(
            self.Definition.Force_Remove_Device_URL,
            self.Definition.Force_Remove_Device_Header,
            str(self.Definition.Force_Remove_Device_Payload)
        )
        self.device_remove_resp = self.REQ(request_type="get",
                                           url=self.Definition.Force_Remove_Device_URL,
                                           headers=self.Definition.Force_Remove_Device_Header,
                                           data=json.dumps(self.Definition.Force_Remove_Device_Payload)
                                           )

        print(self.device_remove_resp.json())
        if self.device_remove_resp.status_code == 200:
            self._Logger.info("Attempt to force remove device success!!")
        else:
            self._Logger.error("Attempt to force remove device success!!")
            raise Exception("Attempt to force remove device Failed!!")

    # ---------------------------------------------------------------------------------------------------------------
    def Soft_Remove_Device_MA(self):
        """
        Work Flow
        ----------
            1. Soft rmove device policy from MA with ID
        """
        self.Fetch_Enrolled_Devices_Details(get_device_id=True)
        id = str(self.device_id)
        # self.Definition.Soft_Remove_Device_Payload[0]= str(id)
        self.Definition.Soft_Remove_Device_Payload = [int(id)]
        # self.Definition.Soft_Remove_Device_Header["auth-token"] = str(self.Authenticate_To_Mobile_Admin())
        print(
            self.Definition.Soft_Remove_Device_URL,
            self.Definition.Soft_Remove_Device_Header,
            str(self.Definition.Soft_Remove_Device_Payload)
        )
        self.device_remove_resp = self.REQ(request_type="post",
                                           url=self.Definition.Soft_Remove_Device_URL,
                                           headers=self.Definition.Soft_Remove_Device_Header,
                                           data=json.dumps(self.Definition.Soft_Remove_Device_Payload)
                                           )

        print(self.device_remove_resp.json())
        if self.device_remove_resp.status_code == 200:
            self._Logger.info("Attempt to Soft remove device success!!")
        else:
            self._Logger.error("Attempt to Soft remove device success!!")
            raise Exception("Attempt to Soft remove device Failed!!")

    # ----------------------------------------------------------------------------------------------------------------------------------------
    def Auto_Upgrade_ZCC(self, 
                         mode="Edit",                           # (str)     Mode to Configure (ClearAll/Edit/Delete)
                         OS=["win", "mac", "lin"],              # (str/list)     can be win/mac/lin or list of any/all of these
                         version=None,                          # (str)     String that contains specific version of zcc to install
                         use64Bit=False,                        # (bool)    True if 64bit zcc needed else False
                         group_name=None,                       # (str)     Provide the name of the ZIA Group for which version has to be selected
                        ):
        
        if OS==None:
            OS = ("win" if os_version=="Windows" else "mac")
        
        # Fetch all available autoupgrade rules
        self.Profile = self.REQ(request_type="get", url=self.Definition.Client_Connector_App_Store_Page_URL,
                                headers=self.Definition.Client_Connector_App_Store_Page_HEADER)
        print(self.Profile.status_code)
        if self.Profile.status_code == 200:
            self._Logger.info("Client_Connector_App_Store_Page Data Fetch: " + str(mode) + " response: success" )
        else:
            self._Logger.error("Client_Connector_App_Store_Page Data Fetch didnt go well!")
            raise Exception("Error :: Client_Connector_App_Store_Page Data Fetch didnt go well!")
        self.list_of_available_zcc_versions = [each["applicationVersion"] for each in self.Profile.json()["applications"]]
        self.list_of_available_auto_upgrade_rules = self.Profile.json()["groupBasedApplicationList"]

        if mode == "Get_List_Of_Available_Versions":
            return copy.deepcopy(self.list_of_available_zcc_versions)
        elif mode == "Get_List_Of_Auto_Upgrade_Rules":
            return copy.deepcopy(self.list_of_available_auto_upgrade_rules)
        
        if mode=="Delete":

            deletion_row_id = None
            if group_name==None:
                self._Logger.error("Please provide a group name, cannot delete all group")
                raise Exception("Please provide a group name, cannot delete all group")

            # Find Auto Upgrade Rule if exists
            for each_auto_upgrade_rule in self.list_of_available_auto_upgrade_rules:
                if not "groupName" in each_auto_upgrade_rule.keys():
                    continue
                if each_auto_upgrade_rule["groupName"] == group_name:
                    deletion_row_id = each_auto_upgrade_rule["rowId"]
            
            if deletion_row_id==None:
                self._Logger.error("Please provide a group name with which rule is already created")
                raise Exception("Please provide a group name with which rule is already created")
                
            Delete_Auto_Upgrade_Rule_Url = copy.deepcopy(self.Definition.Delete_Auto_Upgrade_Rule_Url) + str(deletion_row_id)

            self._Logger.debug("\n\nURL: {0}\n Header: {1}\n\n".format(Delete_Auto_Upgrade_Rule_Url,
                                                                                    self.Definition.Auto_Upgrade_Header))
            Profile = self.REQ(request_type="delete", url=Delete_Auto_Upgrade_Rule_Url,
                            headers=self.Definition.Auto_Upgrade_Header)
            # Validate success
            if Profile.status_code == 200:
                self._Logger.info("Auto Upgrade Rule Deletion status: " + str(mode) + " response:" + str(Profile.json()))
            else:
                self._Logger.error("Deleting Auto Upgrade Rule didnt go well!")
                raise Exception("Error :: Deleting Auto Upgrade Rule didnt go well!")

        elif mode=="Edit":

            # Find Auto Upgrade Rule if exists, else create new rule
            auto_upgrade_payload = None
            max_row_id = -1
            for each_auto_upgrade_rule in self.list_of_available_auto_upgrade_rules:
                if not "groupName" in each_auto_upgrade_rule.keys():
                    if group_name==None:
                        auto_upgrade_payload = copy.deepcopy(each_auto_upgrade_rule)
                        auto_upgrade_payload["groupName"] = "All"
                        break
                    else:
                        continue
                if each_auto_upgrade_rule["groupName"] == group_name:
                    auto_upgrade_payload = copy.deepcopy(each_auto_upgrade_rule)
                if int(each_auto_upgrade_rule["rowId"]) > int(max_row_id):
                    max_row_id = int(each_auto_upgrade_rule["rowId"])
            if auto_upgrade_payload== None:
                auto_upgrade_payload = self.Definition.Auto_Upgrade_Group_Payload["groupBasedApplicationList"][0]
                auto_upgrade_payload["groupId"] = self.Get_List_Of_ZIA_Groups(getIdByName=group_name)
                auto_upgrade_payload["groupName"] = group_name
                #auto_upgrade_payload["rowId"] = max_row_id+1
            auto_upgrade_payload["rowId"] = (-1 if group_name==None else -2)
            # auto_upgrade_payload["rowId"] = -2

            
            # get specific version if provided
            if version not in ["Latest", "Disable"]:
                application_version_id = None
                application_version = None
                for each_app_version_info in self.Profile.json()["applications"]:
                    if version in each_app_version_info["applicationVersion"]:
                        application_version_id = each_app_version_info["clientApplicationId"]
                        application_version = each_app_version_info["applicationVersion"]
                if not application_version and application_version_id:
                    self._Logger.error("specific version not found")
                    raise Exception("specific version not found")

            if "win" in OS:
                if version=="Latest":
                    auto_upgrade_payload["windowsEnabledImage"]["groupAutoUpdateEnabled"] = 1
                elif version=="Disable":
                    auto_upgrade_payload["windowsEnabledImage"]["groupAutoUpdateEnabled"] = 2
                else:
                    auto_upgrade_payload["windowsEnabledImage"]["groupAutoUpdateEnabled"] = 0
                    auto_upgrade_payload["windowsEnabledImage"]["groupAutoUpdateEnabled"] = 0
                    auto_upgrade_payload["windowsEnabledImage"]["groupAutoUpdateEnabled"] = 0
                    if use64Bit:
                        auto_upgrade_payload["windowsEnabledImage"]["use64BitInstallerForWindows"] = 1
            if "mac" in OS:
                if version=="Latest":
                    auto_upgrade_payload["macEnabledImage"]["groupAutoUpdateEnabled"] = 1
                elif version=="Disable":
                    auto_upgrade_payload["macEnabledImage"]["groupAutoUpdateEnabled"] = 2
                else:
                    auto_upgrade_payload["macEnabledImage"]["groupAutoUpdateEnabled"] = 0
                    auto_upgrade_payload["macEnabledImage"]["groupAutoUpdateEnabled"] = 0
                    auto_upgrade_payload["macEnabledImage"]["groupAutoUpdateEnabled"] = 0
                    if use64Bit:
                        auto_upgrade_payload["windowsEnabledImage"]["use64BitInstallerForWindows"] = 1
            if "lin" in OS:
                if version=="Latest":
                    auto_upgrade_payload["linuxEnabledImage"]["groupAutoUpdateEnabled"] = 1
                elif version=="Disable":
                    auto_upgrade_payload["linuxEnabledImage"]["groupAutoUpdateEnabled"] = 2
                else:
                    auto_upgrade_payload["linuxEnabledImage"]["groupAutoUpdateEnabled"] = 0
                    auto_upgrade_payload["linuxEnabledImage"]["groupAutoUpdateEnabled"] = 0
                    auto_upgrade_payload["linuxEnabledImage"]["groupAutoUpdateEnabled"] = 0
                    if use64Bit:
                        auto_upgrade_payload["windowsEnabledImage"]["use64BitInstallerForWindows"] = 1
            
            self.Definition.Auto_Upgrade_Group_Payload["groupBasedApplicationList"] = [auto_upgrade_payload]
            self.Definition.Auto_Upgrade_Group_Payload["companyAutoUpdateEnabled"] = 3
            #self.Definition.Auto_Upgrade_Group_Payload["actionType"] = -1
            self.Definition.Auto_Upgrade_Group_Payload["actionType"] = (-1 if group_name==None else 1)
            # new addition v
            self.Definition.Auto_Upgrade_Group_Payload["applyFromTimestamp"] = ""
            self.Definition.Auto_Upgrade_Group_Payload["enableSlowRollout"] = "0"
            
            self._Logger.debug("\n\nURL: {0}\n Payload: {1}\n Header: {2}\n\n".format(self.Definition.Auto_Upgrade_Url,
                                                                                    self.Definition.Auto_Upgrade_Group_Payload,
                                                                                    self.Definition.Auto_Upgrade_Header))
            Profile = self.REQ(request_type="post", url=self.Definition.Auto_Upgrade_Url,
                            headers=self.Definition.Auto_Upgrade_Header,
                            data=json.dumps(self.Definition.Auto_Upgrade_Group_Payload))
            # Validate success
            if Profile.status_code == 200:
                self._Logger.info("Auto Upgrade status: " + str(mode) + " response:" + str(Profile.json()))
            else:
                self._Logger.error("Enabling Auto Upgrade didnt go well!")
                raise Exception("Error :: Enabling Auto Upgrade didnt go well!")

        elif mode=="ClearAll":

            deletion_row_id = None

            # Find Auto Upgrade Rule if exists
            for each_auto_upgrade_rule in self.list_of_available_auto_upgrade_rules:
                if not "groupName" in each_auto_upgrade_rule.keys():
                    continue
                deletion_row_id = each_auto_upgrade_rule["rowId"]
                self._Logger.info("_"*50 + "\n" + "Clearing rule :::: " + str(each_auto_upgrade_rule["groupName"]) + "\n" + "_"*90)


                Delete_Auto_Upgrade_Rule_Url = copy.deepcopy(self.Definition.Delete_Auto_Upgrade_Rule_Url) + str(deletion_row_id)

                self._Logger.info("\n\nURL: {0}\n Header: {1}\n\n".format(Delete_Auto_Upgrade_Rule_Url,
                                                                        self.Definition.Auto_Upgrade_Header))
                Profile = self.REQ(request_type="delete", url=Delete_Auto_Upgrade_Rule_Url,
                                headers=self.Definition.Auto_Upgrade_Header)
                # Validate success
                if Profile.status_code == 200:
                    self._Logger.info("Auto Upgrade Rule Deletion status: " + str(mode) + " response:" + str(Profile.json()))
                else:
                    self._Logger.error("Deleting Auto Upgrade Rule didnt go well!")
                    raise Exception("Error :: Deleting Auto Upgrade Rule didnt go well!")
            
            self.Auto_Upgrade_ZCC(mode="Edit", OS="win", group_name=None, version="Disable")
            self.Auto_Upgrade_ZCC(mode="Edit", OS="mac", group_name=None, version="Disable")
            self.Auto_Upgrade_ZCC(mode="Edit", OS="lin", group_name=None, version="Disable")

        



    # --------------------------------------------------------------------------------------------------------------        
    def DROPNONZSCALERPACKETS_MA(self, 
                                 action="enable"):
        self.Definition.toggle_dropnonzscalerpacket_payload["dropNonZscalerPacket"] = "1" if action == "enable" else "0"
        Profile = self.REQ(request_type="post", url=self.Definition.toggle_dropnonzscalerpacket_url, headers=self.Definition.toggle_dropnonzscalerpacket_header, data=json.dumps(self.Definition.toggle_dropnonzscalerpacket_payload))
        if Profile.status_code == 200: self._Logger.info("Drop Non Zscaler Packet Success")
        else:
            self._Logger.error("Drop Non Zscaler Packet Failed")
            raise Exception("Error :: Invalid Status code for Drop Non Zscaler Packet!")
    
    # --------------------------------------------------------------------------------------------------------------
    def Get_All_App_Bypasses(self):
        Profile = self.REQ(request_type="get", url=self.Definition.Get_All_App_Bypasses_Url, headers=self.Definition.App_Bypass_Header, data=json.dumps({"page": 1, "pageSize": 100, "search": ""}))
        if Profile.status_code == 200: 
            self._Logger.info("App Bypass List Fetch Success")
            return Profile.json()
        else:
            self._Logger.error("App Bypass List Fetch Failed")
            raise Exception("Error :: Invalid Status code for App Bypass List Fetch!")
    
    # --------------------------------------------------------------------------------------------------------------
    def Get_App_Bypass_Profile_ID_With_Name(self, 
                                         Bypass_Name="Automation_Bypass"):
        try:
            result = self.Get_All_App_Bypasses()
            for each_entry in result["appIdentities"]:
                if each_entry["appName"]==Bypass_Name:
                    return each_entry["id"]
            raise Exception()
        except Exception as e:
            self._Logger.error("App Bypass doesnt exist with given name", e)
            raise Exception("Error :: App Bypass doesnt exist with given name!", e)

    
    # --------------------------------------------------------------------------------------------------------------
    def Edit_App_Bypass(self,
                          Bypass_Name = "Automation_Bypass",
                          App_Path = None,
                          SHA2 = None,
                          Certificate_Subject = None,
                          Certificate_Signer = None
                          ):
        
        App_Name = "Sample"
        Certificate_Name = "Sample"
        self.Definition = admin_res.admin_res(self.cloud)
        try:
            App_Bypass_ID = str(self.Get_App_Bypass_Profile_ID_With_Name(Bypass_Name=Bypass_Name))
            url = self.Definition.Edit_App_Bypass_Url
            if App_Bypass_ID=='None':
                App_Bypass_ID = "-1"
                url = self.Definition.Create_App_Bypass_Url
        except:
            App_Bypass_ID = "-1"
            url = self.Definition.Create_App_Bypass_Url
        # if not os.path.exists(App_Path):
        #     self._Logger.error("This app doesnt exist -> {}".format(App_Path))
        #     raise Exception("This app doesnt exist -> {}".format(App_Path))
        
        self.Definition.App_Bypass_Payload["id"] = App_Bypass_ID
        self.Definition.App_Bypass_Payload["appName"] = Bypass_Name
        if type(App_Path)==list:
            self.Definition.App_Bypass_Payload["filePaths"] = App_Path
        elif type(App_Path)==str:
            self.Definition.App_Bypass_Payload["filePaths"] = [App_Path]
        else:
            raise Exception("Unexpected datatype for App Path")
        

        Certificate_Subject_Payload_List = []
        Certificate_Signer_Payload_List = []
        if SHA2:
            if type(SHA2) == str:
                App_Bypass_Signature_Payload_Instance = copy.deepcopy(self.Definition.App_Bypass_Signature_Payload).replace("APP_NAME", App_Name).replace("APP_SIGNATURE", SHA2)
                self.Definition.App_Bypass_Payload["signaturePayload"] = self.Definition.App_Bypass_Payload["signaturePayload"].replace("SIGNATURE_PAYLOAD", App_Bypass_Signature_Payload_Instance)
            elif type(SHA2) == list:
                Signature_Payload_List = []
                for each_SHA2 in SHA2:
                    App_Bypass_Signature_Payload_Instance = copy.deepcopy(self.Definition.App_Bypass_Signature_Payload).replace("APP_NAME", App_Name).replace("APP_SIGNATURE", each_SHA2)
                    Signature_Payload_List.append(App_Bypass_Signature_Payload_Instance)
                Signature_Payload_String = ",".join(Signature_Payload_List)
                self.Definition.App_Bypass_Payload["signaturePayload"] = self.Definition.App_Bypass_Payload["signaturePayload"].replace("SIGNATURE_PAYLOAD", Signature_Payload_String)
            else:
                self.Definition.App_Bypass_Payload["signaturePayload"] = self.Definition.App_Bypass_Payload["signaturePayload"].replace("SIGNATURE_PAYLOAD", copy.deepcopy(self.Definition.App_Bypass_Signature_Payload))
        else:
            self.Definition.App_Bypass_Payload["signaturePayload"] = self.Definition.App_Bypass_Payload["signaturePayload"].replace("SIGNATURE_PAYLOAD", "")
        if Certificate_Signer or Certificate_Subject:
            if Certificate_Subject:
                Certificate_Subject = Certificate_Subject.replace('"',"'")
                if type(Certificate_Subject) != list:
                    Certificate_Subject = [Certificate_Subject]
                for each_Certificate_Subject in Certificate_Subject:
                    App_Bypass_Certificate_Subject_Payload_Instance = copy.deepcopy(self.Definition.App_Bypass_Certificate_Payload).replace("CERTIFICATE_NAME", Certificate_Name).replace("CERTIFICATE_SUBJECT", each_Certificate_Subject)
                    Certificate_Subject_Payload_List.append(App_Bypass_Certificate_Subject_Payload_Instance)
            if Certificate_Signer:
                if type(Certificate_Signer) == str:
                    # App_Bypass_Certificate_Subject_Payload_Instance = copy.deepcopy(self.Definition.App_Bypass_Certificate_Payload).replace("CERTIFICATE_NAME", App_Name).replace("CERTIFICATE_SUBJECT", Certificate_Subject)
                    # self.Definition.App_Bypass_Payload["certificatePayload"] = self.Definition.App_Bypass_Payload["certificatePayload"].replace("CERTIFICATE_PAYLOAD", App_Bypass_Certificate_Subject_Payload_Instance)
                    Certificate_Signer = [Certificate_Signer]
                for each_Certificate_Signer in Certificate_Signer:
                    App_Bypass_Certificate_Signer_Payload_Instance = copy.deepcopy(self.Definition.App_Bypass_Certificate_Payload).replace("CERTIFICATE_NAME", Certificate_Name).replace("CERTIFICATE_SIGNER", each_Certificate_Signer)
                    Certificate_Signer_Payload_List.append(App_Bypass_Certificate_Signer_Payload_Instance)
            Certificate_Payload_String = ",".join(Certificate_Subject_Payload_List + Certificate_Signer_Payload_List)
            self.Definition.App_Bypass_Payload["certificatePayload"] = self.Definition.App_Bypass_Payload["certificatePayload"].replace("CERTIFICATE_PAYLOAD", Certificate_Payload_String)
        else:
            self.Definition.App_Bypass_Payload["certificatePayload"] = self.Definition.App_Bypass_Payload["certificatePayload"].replace("CERTIFICATE_PAYLOAD", copy.deepcopy(self.Definition.App_Bypass_Certificate_Payload.replace("CERTIFICATE_NAME", "").replace("CERTIFICATE_SIGNER", "").replace("CERTIFICATE_SUBJECT", "").replace("APP_SIGNATURE", "")))

        if SHA2 and Certificate_Signer:
            self.Definition.App_Bypass_Payload["matchingCriteria"] = 4
        elif SHA2 and Certificate_Subject:
            self.Definition.App_Bypass_Payload["matchingCriteria"] = 5
        elif SHA2:
            self.Definition.App_Bypass_Payload["matchingCriteria"] = 1
        elif Certificate_Subject:
            self.Definition.App_Bypass_Payload["matchingCriteria"] = 3   
        elif Certificate_Signer:
            self.Definition.App_Bypass_Payload["matchingCriteria"] = 2
            
        print(self.Definition.App_Bypass_Payload)
        Profile = self.REQ(request_type="post", url=url, headers=self.Definition.App_Bypass_Header, data=json.dumps(self.Definition.App_Bypass_Payload))
        if Profile.status_code == 200 and (Profile.json()["success"]=="true" if "success" in Profile.json().keys() else True): 
            self._Logger.info("App Bypass Create/Edit Success")
        else:
            self._Logger.error("App Bypass Create/Edit Failed")
            raise Exception("Error :: Invalid Status code for App Bypass Create/Edit!")
        
    # --------------------------------------------------------------------------------------------------------------
    def Delete_App_Bypass(self,
                          Bypass_Name = "Automation_Bypass"
                          ):
        self.Definition = admin_res.admin_res(self.cloud)
        App_Bypass_ID  = str(self.Get_App_Bypass_Profile_ID_With_Name(Bypass_Name = Bypass_Name))
        self.Definition.Delete_App_Bypass_Url = self.Definition.Delete_App_Bypass_Url.replace("ADD_ID_HERE", App_Bypass_ID)
        Profile = self.REQ(request_type="delete", url=self.Definition.Delete_App_Bypass_Url, headers=self.Definition.App_Bypass_Header)
        if Profile.status_code == 200: 
            self._Logger.info("App Bypass Deletion Success")
        else:
            self._Logger.error("App Bypass Deletion Failed")
            raise Exception("Error :: Invalid Status code for App Bypass Deletion!")
        


################################################################################################# Monika (Mobile Admin) ##########################################################################################################

    def Create_Admin_Role(self,         # Function to create Admin role
    save_to_temp=True):                 # (bool)    if True, save ID to a tempfile
        """
        Work Flow
        ----------
            1.  Get admin role payloads from Definitions. get Auth token.
            2.  create basic admin role.
            3.  send post request. validate response is 200
            4.  write admin role id into a temp file, to use later
        """
        self.Definition = admin_res.admin_res(self.cloud)
        self.Definition.CREATE_ADMIN_ROLE_PAYLOAD['roleName'] = self.admin_role_name
        #self.Definition.CREATE_ADMIN_ROLE_HEADER['auth-token'] = self.Authenticate_To_Mobile_Admin()
        self._Logger.info("url is {}".format(self.Definition.CREATE_ADMIN_ROLE_URL))
        #self._Logger.info("Payload is {}".format(json.dumps(self.Definition.CREATE_FORWARDING_PROFILE_PAYLOAD)))
        self._Logger.info("Header is {}".format(self.Definition.CREATE_FORWARDING_PROFILE_HEADER))

        Profile = self.REQ(request_type="post", url=self.Definition.CREATE_ADMIN_ROLE_URL, data=json.dumps(self.Definition.CREATE_ADMIN_ROLE_PAYLOAD),
                                headers=self.Definition.CREATE_ADMIN_ROLE_HEADER)

        if Profile.status_code == 401:
            print('Invalid request! Resending request to get Auth-token via API')
            self.Definition.CREATE_ADMIN_ROLE_HEADER['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

            Profile = requests.post(self.Definition.CREATE_ADMIN_ROLE_URL, data=json.dumps(self.Definition.CREATE_ADMIN_ROLE_PAYLOAD),
                                headers=self.Definition.CREATE_ADMIN_ROLE_HEADER)

        print(Profile.status_code)
        if Profile.status_code == 200:
            response_json = Profile.json()
            print(response_json)
            self.admin_role_status = response_json["success"]
            self.is_admin_role_created = True
        elif Profile.status_code==200 and Profile.json()["error"]=="3070":
            print("yolo pass")
        else:
            self._Logger.error("the return status is  {} and not 200, please check".format(Profile.status_code))
            raise Exception("the return status is  {} and not 200, please check".format(Profile.status_code))
        if save_to_temp:
            self._Logger.info("making directory {}".format(self.temp_path))
            if not os.path.exists(self.temp_path):
                os.makedirs(self.temp_path)
                print("directory made")
            _file = open(self.temp_file, "a")
            _file.write("arid:{}".format(self.admin_role_status))
            _file.write("\n")
            print("file written")
            _file.close()
        self._Logger.info("\n\nAdmin role created. response={0}\nid={1}\nstatus_code={2}\nsave_to_temp={3}\n\n".format(response_json,self.admin_role_id,Profile.status_code,save_to_temp))

    # -------------------------------------------------------------------------------------------------------------------

    def Get_Admin_Role_ID(self,  # Function to Fetch Admin Role ID
                                 # (bool)    reads forwarding profile ID from tempfile if True
                          read_from_ma=True):  # (bool)    reads Admin role ID from MA if True
        """
        Work Flow
        ----------
            1.  if admin role is created recently, self variable stores its ID. so this will be returned by default
            2.  if admin role is created in another session, ID can be read from temp file. return it.
            3.  if temp file is also lost, visit MA, find admin role with "automation" name. get its ID. return it.
            """
        if read_from_ma == True:
            #self.Definition.GET_ADMIN_ROLES_LIST_HEADER['auth-token'] = self.Authenticate_To_Mobile_Admin()
            Profile = self.REQ(request_type="get",url=self.Definition.GET_ADMIN_ROLES_LIST_URL,headers=self.Definition.GET_ADMIN_ROLES_LIST_HEADER)
            response_json = Profile.json()
            self._Logger.info(response_json)
            if Profile.status_code == 200:
                for i in range(len(response_json['records'])):
                    if response_json['records'][i]['roleName'] == self.admin_role_name:
                        print(response_json['records'][i]['id'])
                        if True:
                            return str(response_json['records'][i]['id'])
                        else:
                            return response_json['records']
            self._Logger.error("No such admin role found")
            raise Exception("Error: No such admin role found")
        self._Logger.error("wrong selection of options or no id found")
        raise Exception("Error :: wrong selection of options or no id found")

    # -------------------------------------------------------------------------------------------------------------------
    
    def Delete_Admin_Role(self,  # Function to delete admin role
                              ):
            """
            Work Flow
            ----------
                1. get admin role  ID. get definitions. get auth token
                2. send a delete request for admin role, validate response is 200
            """
            ID = str(self.Get_Admin_Role_ID())
            #self.Definition.Delete_Admin_Role_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
            self.Definition.Delete_Admin_Role_Url = self.Definition.Delete_Admin_Role_Url + ID

            self._Logger.info(
                "\n\nDelete_Admin_Role\nUrl:{0}\nheaders\n{1}\n\n".format(self.Definition.Delete_Admin_Role_Url, self.Definition.Delete_Admin_Role_Header))
            self.Definition.Delete_Admin_Role_Payload["roleId"] = ID
            self.Delete_Admin_Role_Info = self.REQ(request_type="post", url=self.Definition.Delete_Admin_Role_Url,data=json.dumps(self.Definition.Delete_Admin_Role_Payload),headers=self.Definition.Delete_Admin_Role_Header)

            # Validate success
            if self.Delete_Admin_Role_Info.status_code == 200:
                self._Logger.info(
                    "AR status: " + str("deleted") + "\nresponse:" + str(self.Delete_Admin_Role_Info.json()))
            else:
                self._Logger.error("Admin role deletion didnt go well!")
                raise Exception("Error :: AR deletion didnt go well!")
            self.is_admin_role_created = False
            if not os.path.exists(self.temp_file):
                return
            file_contents = None
            with open(self.temp_file, "r") as _file:
                file_contents = _file.read().splitlines()
                ar_id_line_number = [i for i in range(len(file_contents)) if "arid" in file_contents[i]][0]
                del file_contents[ar_id_line_number]
            _file = open(self.temp_file, "w")
            _file.write(''.join(file_contents))
            _file.close()
            self.response = self.Delete_Admin_Role_Info.json()
            self._Logger.info("\n\nDelete_Admin_Role_response\nstatus_code:{0}\nresponse:{1}\nSuccess:{2}".format(
                self.Delete_Admin_Role_Info.status_code, self.Delete_Admin_Role_Info.json(),
                "success" in self.response.keys()))
    
    # -------------------------------------------------------------------------------------------------------------------
    def Get_Admin_User_ID(self, save_to_temp=True,              # Function to get Admin users list
                          read_from_ma=True):                   # (bool) reads admin user ID from MA if True
        """
        Work Flow
        ----------
            1.  if admin user is created recently, self variable stores its ID. so this will be returned by default
            """
        if read_from_ma == True:
            #self.Definition.GET_ADMIN_USER_LIST_HEADER['auth-token'] = self.Authenticate_To_Mobile_Admin()
            Profile = self.REQ(request_type="get",url=self.Definition.GET_ADMIN_USER_LIST_URL, headers=self.Definition.GET_ADMIN_USER_LIST_HEADER, data=json.dumps(self.Definition.GET_ADMIN_USER_LIST_PAYLOAD))
            response_json = Profile.json()
            self._Logger.info(response_json)
            if Profile.status_code == 200:
                self._Logger.debug(response_json)
                self._Logger.info("Get Admin Roles Success")
            else:
                self._Logger.error("No such admin user found")
                raise Exception("Error: No such admin user found")
        if save_to_temp:
            self._Logger.info("making directory {}".format(self.temp_path))
            if not os.path.exists(self.temp_path):
                os.makedirs(self.temp_path)
                print("directory made")
        _file = open(self.temp_file, "a")
        _file.write("getAdminUsers_Response:{}".format(response_json))
        _file.write("\n")
        print("file written")
        _file.close()
        self._Logger.info("\n\nReminder notification, response={0}\nstatus_code={1}\nsave_to_temp={2}\n\n".format(response_json,Profile.status_code,save_to_temp))
    
    # -------------------------------------------------------------------------------------------------------------------
    def Sync_ZIA_Admins_MA(self
                           ):
        """
        Target: To Sync ZIA users to MA
        Work Flow
        ----------
            1. Auth to MA
            2. Click MA/Adminstration/Administration Management/Adminstrators/Click sync ZIA admins
        """
        #self.Definition.Sync_ZIA_Admin_Users_Header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        #if read_from_ma == True:
        Profile = self.REQ(request_type="post", url=self.Definition.Sync_ZIA_Admin_Users_Url, headers=self.Definition.Sync_ZIA_Admin_Users_Header)
        #response_json = Sync_ZIA_Admin_Users_Response.json()
        #self._Logger.info(response_json)
        print(Profile.json(),Profile.status_code)
        if Profile.status_code == 200:

            #self._Logger.debug(Sync_ZIA_Admin_Users_Response.json())    
            self._Logger.info("Set with Sync ZIA admins is success")

        else:   
            self._Logger.error("ZIA Sync Admin Failed")
            raise Exception("ZIA Sync Admins Failed")

        
    # -------------------------------------------------------------------------------------------------------------------
    def Sync_ZPA_Admins_MA(self
                           ):
        """
        Target: To sync ZPA admins to MA
        Work Flow
        ----------
            1. Auth to MA
            2.  Go to MA/Adminstration/Administration Management/Adminstrators/Click sync ZPA admins
        """
        #self.Definition.Sync_ZPA_Admin_Users_Header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        Profile = self.REQ(request_type="post",url=self.Definition.Sync_ZPA_Admin_Users_Url,
                                                           headers=self.Definition.Sync_ZPA_Admin_Users_Header)
    
        if Profile.status_code == 200:
            self._Logger.debug(Profile.json())
            self._Logger.info("Sync ZPA Admin Users Success")
        else:
            self._Logger.error("Sync ZPA Admin Users Failed")
            raise Exception("Sync ZPA Admin Users Failed")

    # -------------------------------------------------------------------------------------------------------------------
    def SyncInfo_ZIA_Admins_MA(self
                           ):
        """
        Work Flow
        ----------
            1. Sync Info ZIA admins
        """
        #self.Definition.SyncInfo_ZIA_Admin_Users_Header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        Profile = self.REQ(request_type="get", url=self.Definition.SyncInfo_ZIA_Admin_Users_Url, headers=self.Definition.SyncInfo_ZIA_Admin_Users_Header)
        
        if Profile.status_code == 200:
            self._Logger.debug(Profile.json())
            self._Logger.info("SyncInfo ZIA Admin Users Success")
        else:
            self._Logger.error("SyncInfo ZIA Admin Users Failed")
            raise Exception("SyncInfo ZIA Admin Users Failed")


    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Get_Application_List(self,  # Function to get zcc versions available on MA not mobile manager
    ):
        """
        Work Flow
        ----------
            1. Go to MA / Administration / Client Connector App store (It is setting up the automatic rollout for zapp)
        
        """
        AuthToken = self.Authenticate_To_Mobile_Admin()
        #self.Definition.get_zcc_list_from_ma_header['auth-token']=AuthToken
        Profile = self.REQ(request_type="get",url=self.Definition.Get_Application_List_Url,headers=self.Definition.Get_Application_List_Header)
        self._Logger.info(f'Response Code : {Profile.status_code}')
        response_json = Profile.json()
        if Profile.status_code == 200:
            self._Logger.debug(Profile.json())
            self._Logger.info("Get Application List Success")
        else:
            self._Logger.error("Get Application List Failed")
            raise Exception("Get Application List Failed")
        self._Logger.info("\n\nGet Application List, response={0}\nstatus_code={1}\n\n".format(response_json,Profile.status_code))


    def get_application_list(self,  # Function to get zcc versions available on MA not mobile manager
    ):
        """
        Work Flow
        ----------
            1. Go to MA / Administration / Client Connector App store (It is setting up the automatic rollout for zapp)
        
        """
        AuthToken = self.Authenticate_To_Mobile_Admin()
        #self.Definition.get_zcc_list_from_ma_header['auth-token']=AuthToken
        Profile = self.REQ(request_type="get",url=self.Definition.Get_Application_List_Url,headers=self.Definition.Get_Application_List_Header)
        self._Logger.info(f'Response Code : {Profile.status_code}')
        response_json = Profile.json()
        if Profile.status_code == 200:
            self._Logger.debug(Profile.json())
            self._Logger.info("Get Application List Success")
        else:
            self._Logger.error("Get Application List Failed")
            raise Exception("Get Application List Failed")
        self._Logger.info("\n\nGet Application List, response={0}\nstatus_code={1}\n\n".format(response_json,Profile.status_code))
        return response_json


    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Update_Application_List(self):  
            """
            Work Flow
            ----------
            1. Go to MA / Adminstration / Client Connector App store / Enable Update Setting (It is updating automatic rollout for zapp)

            """
            self.Definition = admin_res.admin_res(self.cloud)
            #self.Definition.Autoupdate_Application_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
            self._Logger.info("url is {}".format(self.Definition.Autoupdate_Application_Url))
            #self._Logger.info("Payload is {}".format(json.dumps(self.Definition.Autoupdate_Application_Payload)))
            #self._Logger.info("Header is {}".format(self.Definition.Autoupdate_Application_Header))

            Profile = self.REQ(request_type="post",url=self.Definition.Autoupdate_Application_Url,
                                    data=json.dumps(self.Definition.Autoupdate_Application_Payload),
                                    headers=self.Definition.Autoupdate_Application_Header)

            if Profile.status_code == 401:
                print('Invalid request! Resending request to get Auth-token via API')
                self.Definition.Autoupdate_Application_Header['auth-token'] = self.Authenticate_To_Mobile_Admin(
                    readFile=False)

                Profile = requests.post(self.Definition.Autoupdate_Application_Url,data=json.dumps(self.Definition.Autoupdate_Application_Payload),headers=self.Definition.Autoupdate_Application_Header)

            response_json = Profile.json()
            print(Profile.status_code)
            if Profile.status_code == 200:
                if Profile.json()['success']!='true':
                   self._Logger.error("ERROR with Update Application List: {}".format(Profile.json()))
                   raise Exception("ERROR with Update Application List : {}".format(Profile.json()))
                else:
                    self._Logger.info(" Update Application List is success: {}".format(Profile.json()))

            else:
                self._Logger.error("the return status is  {} and not 200, please check".format(Profile.status_code))
                raise Exception("the return status is  {} and not 200, please check".format(Profile.status_code))

            self._Logger.info("\n\nUpdate Application List response={0}\nstatus_code={1}\n\n".format(response_json, Profile.status_code))



    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def update_application_list(self, new_payload):  
            """
            Work Flow
            ----------
            1. Go to MA / Adminstration / Client Connector App store / Enable Update Setting (It is updating automatic rollout for zapp)

            """
            self.Definition = admin_res.admin_res(self.cloud)
            #self.Definition.Autoupdate_Application_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
            self._Logger.info("url is {}".format(self.Definition.Autoupdate_Application_Url))
            #self._Logger.info("Payload is {}".format(json.dumps(self.Definition.Autoupdate_Application_Payload)))
            #self._Logger.info("Header is {}".format(self.Definition.Autoupdate_Application_Header))

            Profile = self.REQ(request_type="post",url=self.Definition.Autoupdate_Application_Url,
                                    data=json.dumps(new_payload),
                                    headers=self.Definition.Autoupdate_Application_Header)

            if Profile.status_code == 401:
                print('Invalid request! Resending request to get Auth-token via API')
                self.Definition.Autoupdate_Application_Header['auth-token'] = self.Authenticate_To_Mobile_Admin(
                    readFile=False)

                Profile = requests.post(self.Definition.Autoupdate_Application_Url,data=json.dumps(new_payload),headers=self.Definition.Autoupdate_Application_Header)

            response_json = Profile.json()
            print(Profile.status_code)
            if Profile.status_code == 200:
                if Profile.json()['success']!='true':
                   self._Logger.error("ERROR with Update Application List: {}".format(Profile.json()))
                   raise Exception("ERROR with Update Application List : {}".format(Profile.json()))
                else:
                    self._Logger.info(" Update Application List is success: {}".format(Profile.json()))

            else:
                self._Logger.error("the return status is  {} and not 200, please check".format(Profile.status_code))
                raise Exception("the return status is  {} and not 200, please check".format(Profile.status_code))

            self._Logger.info("\n\nUpdate Application List response={0}\nstatus_code={1}\n\n".format(response_json, Profile.status_code))



    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Autoupdate_NewApplicationList(self, read_from_ma=True): 
        """
           Work Flow
        ----------
            1. Go to MA / Adminstration / Client Connector App store / New releases
            """
        if read_from_ma == True:
            #self.Definition.Get_NewApplicationList_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
            Profile = self.REQ(request_type="get",url=self.Definition.Get_NewApplicationList_Url,headers=self.Definition.Get_NewApplicationList_Header)
            response_json = Profile.json()
            self._Logger.info(response_json)
        if Profile.status_code == 200:
            self._Logger.debug(Profile.json())
            self._Logger.info("Get NewApplication List Success")
        else:
            self._Logger.error("Get NewApplication List Failed")
            raise Exception("Get NewApplication List Failed")
        self._Logger.info("\n\nGet NewApplication List, response={0}\nstatus_code={1}\n\n".format(response_json,Profile.status_code))
    
    
    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def get_new_application_list(self, read_from_ma=True): 
        """
           Work Flow
        ----------
            1. Go to MA / Adminstration / Client Connector App store / New releases
            """
        if read_from_ma == True:
            #self.Definition.Get_NewApplicationList_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
            Profile = self.REQ(request_type="get",url=self.Definition.Get_NewApplicationList_Url,headers=self.Definition.Get_NewApplicationList_Header)
            response_json = Profile.json()
            # self._Logger.info(response_json)
        if Profile.status_code == 200:
            # self._Logger.debug(Profile.json())
            self._Logger.info("Get NewApplication List Success")
        else:
            self._Logger.error("Get NewApplication List Failed")
            raise Exception("Get NewApplication List Failed")
        # self._Logger.info("\n\nGet NewApplication List, response={0}\nstatus_code={1}\n\n".format(response_json,Profile.status_code))

        return response_json
    

    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Update_NewApplication_List(self):                         
        """
        Work Flow
        ----------
           1. Go to MA / Adminstration / Client Connector App store / Enable Update Setting

        """
        self.Definition = admin_res.admin_res(self.cloud)
        #self.Definition.Autoupdate_NewApplication_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
        self._Logger.info("url is {}".format(self.Definition.Autoupdate_NewApplication_Url))
        self._Logger.info("Payload is {}".format(json.dumps(self.Definition.Autoupdate_NewApplication_Payload)))
        self._Logger.info("Header is {}".format(self.Definition.Autoupdate_NewApplication_Header))

        Profile = self.REQ(request_type="post",url=self.Definition.Autoupdate_NewApplication_Url, data=json.dumps(self.Definition.Autoupdate_NewApplication_Payload),
                                headers=self.Definition.Autoupdate_NewApplication_Header)

        if Profile.status_code == 401:
            print('Invalid request! Resending request to get Auth-token via API')
            #self.Definition.Autoupdate_NewApplication_Header['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

            Profile = self.REQ(request_type="post",url=self.Definition.Autoupdate_NewApplication_Url, data=json.dumps(self.Definition.Autoupdate_NewApplication_Payload),
                                headers=self.Definition.Autoupdate_NewApplication_Header)
        
        response_json = Profile.json()
        if Profile.status_code == 200:
                if Profile.json()['success']!='true':
                    self._Logger.error("ERROR with Update NewApplication List: {}".format(Profile.json()))
                    raise Exception("ERROR with Update NewApplication List : {}".format(Profile.json()))
                else:
                    self._Logger.info("Update NewApplication List success: {}".format(Profile.json()))

        else:
            self._Logger.error("the return status is  {} and not 200, please check".format(Profile.status_code))
            raise Exception("the return status is  {} and not 200, please check".format(Profile.status_code))

        self._Logger.info("\n\nUpdate NewApplication List response={0}\nstatus_code={1}\n\n".format(response_json, Profile.status_code))
     
    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def update_new_application_list(self, new_payload):                         
        """
        Work Flow
        ----------
           1. Go to MA / Adminstration / Client Connector App store / Enable Update Setting

        """
        self.Definition = admin_res.admin_res(self.cloud)
        #self.Definition.Autoupdate_NewApplication_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
        self._Logger.info("url is {}".format(self.Definition.Autoupdate_NewApplication_Url))
        self._Logger.info("Payload is {}".format(json.dumps(new_payload)))
        self._Logger.info("Header is {}".format(self.Definition.Autoupdate_NewApplication_Header))

        Profile = self.REQ(request_type="post",url=self.Definition.Autoupdate_NewApplication_Url, data=json.dumps(new_payload),
                                headers=self.Definition.Autoupdate_NewApplication_Header)

        if Profile.status_code == 401:
            print('Invalid request! Resending request to get Auth-token via API')
            #self.Definition.Autoupdate_NewApplication_Header['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

            Profile = self.REQ(request_type="post",url=self.Definition.Autoupdate_NewApplication_Url, data=json.dumps(new_payload),
                                headers=self.Definition.Autoupdate_NewApplication_Header)
        
        response_json = Profile.json()
        if Profile.status_code == 200:
                if Profile.json()['success']!='true':
                    self._Logger.error("ERROR with Update NewApplication List: {}".format(Profile.json()))
                    raise Exception("ERROR with Update NewApplication List : {}".format(Profile.json()))
                else:
                    self._Logger.info("Update NewApplication List success: {}".format(Profile.json()))

        else:
            self._Logger.error("the return status is  {} and not 200, please check".format(Profile.status_code))
            raise Exception("the return status is  {} and not 200, please check".format(Profile.status_code))

        self._Logger.info("\n\nUpdate NewApplication List response={0}\nstatus_code={1}\n\n".format(response_json, Profile.status_code))


    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Autoupdate_appService(self,read_from_ma=True):  
            """
            Work Flow
            ----------
                1. Go to MA / Adminstration / Application Bypass info / App profiles
                """
            if read_from_ma == True:
                #self.Definition.Get_appService_List_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
                Profile = self.REQ(request_type="get",url=self.Definition.Get_appService_List_Url,headers=self.Definition.Get_appService_List_Header,data=json.dumps(self.Definition.Get_appService_List_Payload))
                response_json = Profile.json()
                self._Logger.info(response_json)
                if Profile.status_code == 200:
                    self._Logger.debug(response_json)
                    self._Logger.info("Autoupdate app Service success")
                else:
                    self._Logger.error("Autoupdate app Service Failed")
                    raise Exception("Autoupdate app Service Failed")
            self._Logger.info("\n\nAutoUpdate AppService response={0}\nstatus_code={1}\n\n".format(response_json, Profile.status_code))


    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Update_Aup(self):  
        """
        Work Flow
        ----------
        1. Go to MA / Administration / Client Connector Notifications / Set Acceptance use policy settings
       
        """
        self.Definition = admin_res.admin_res(self.cloud)
        #self.Definition.Update_Aup_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
        self._Logger.info("url is {}".format(self.Definition.Update_Aup_Url))
        self._Logger.info("Payload is {}".format(json.dumps(self.Definition.Update_Aup_Payload)))
        self._Logger.info("Header is {}".format(self.Definition.Update_Aup_Header))

        Profile = self.REQ(request_type="post",url=self.Definition.Update_Aup_Url, data=json.dumps(self.Definition.Update_Aup_Payload),
                                headers=self.Definition.Update_Aup_Header)

        if Profile.status_code == 401:
            print('Invalid request! Resending request to get Auth-token via API')
            self.Definition.Autoupdate_Application_Header['auth-token'] = self.Authenticate_To_Mobile_Admin(
                readFile=False)

            Profile = self.REQ(request_type="post",url=self.Definition.Update_Aup_Url, data=json.dumps(self.Definition.Update_Aup_Payload),
                                    headers=self.Definition.Update_Aup_Header)

        print(Profile.status_code)
        if Profile.status_code == 200:
            response_json = Profile.json()
            print(response_json)
        
        elif Profile.status_code == 200 and Profile.json()["succes"] != "true":
            print("yolo pass")
        else:
            self._Logger.error("the return status is  {} and not 200, please check".format(Profile.status_code))
            raise Exception("the return status is  {} and not 200, please check".format(Profile.status_code))

    
    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Get_Cloud_Aup (self,read_from_ma = True):  
        """
        Work Flow
        ----------
            1. Go to MA / Administration / Client Connector Notifications
        """
        if read_from_ma == True:
            #self.Definition.Get_Cloud_Aup_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
            Profile = self.REQ(request_type="get",url=self.Definition.Get_Cloud_Aup_Url, headers=self.Definition.Get_Cloud_Aup_Header)
            response_json = Profile.json()
            if Profile.status_code == 200:
                self._Logger.debug(Profile.json())
                self._Logger.info("Get Cloud AUP Success")

            elif Profile.status_code == 200 and Profile.json()["succes"] != "true":
                print("yolo pass")

            else:
                self._Logger.error("Get Cloud AUP Failed")
                raise Exception("Get Cloud AUP Failed")
        self._Logger.info("\n\Get Cloud AUP  response={0}\nstatus_code={1}\n\n".format(response_json, Profile.status_code))


    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Get_Company_Info(self,read_from_ma = True):
            """
            Work Flow
            ----------
                1. Go to MA / Dashboard
                """
            if read_from_ma == True:
                #self.Definition.Get_Company_Info_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
                Profile = self.REQ(request_type="get",url=self.Definition.Get_Company_Info_Url,
                                            headers=self.Definition.Get_Company_Info_Header)
                response_json = Profile.json()
                if Profile.status_code == 200:
                    self._Logger.debug(Profile.json())
                    self._Logger.info("Get Company Info Success")
                else:
                    self._Logger.error("Get Company Info Failed")
                    raise Exception("Get Company Info Failed")
            self._Logger.info("\n\Get Company Information response={0}\nstatus_code={1}\n\n".format(response_json, Profile.status_code))


    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Get_Idp_Info(self,read_from_ma = True):
        """
        Work Flow
        ----------
            1. Go to MA / Adminstration / Client Connector IDP
            """
        if read_from_ma == True:
            #self.Definition.Get_Idp_Info_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
            Profile = self.REQ(request_type="get",url=self.Definition.Get_Idp_Info_Url, headers=self.Definition.Get_Idp_Info_Header)
            response_json = Profile.json()
            if Profile.status_code == 200:
                self._Logger.debug(Profile.json())
                self._Logger.info("Get IDP info Success")
            else:
                self._Logger.error("Get IDP info Failed")
                raise Exception("Get IDP info Failed")
        self._Logger.info("\n\Get Company Information response={0}\nstatus_code={1}\n\n".format(response_json, Profile.status_code))

    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Set_Idp_Info(self): 
            """
            Work Flow
            ----------
            1. Go to MA / Adminstration / Client Connector IDP / Create Device Token / Save
        
            """
            self.Definition = admin_res.admin_res(self.cloud)
            #self.Definition.Set_Idp_Info_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
            self._Logger.info("url is {}".format(self.Definition.Set_Idp_Info_Url))
            self._Logger.info("Payload is {}".format(json.dumps(self.Definition.Set_Idp_Info_Payload)))
            self._Logger.info("Header is {}".format(self.Definition.Set_Idp_Info_Header))
            Profile=self.REQ(request_type="post",url=self.Definition.Set_Idp_Info_Url,
                                    data=json.dumps(self.Definition.Set_Idp_Info_Payload),
                                    headers=self.Definition.Set_Idp_Info_Header)

            if Profile.status_code == 401:
                print('Invalid request! Resending request to get Auth-token via API')
                #self.Definition.Set_Idp_Info_Header['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

                Profile = self.REQ(request_type="post",url=self.Definition.Set_Idp_Info_Url,data=json.dumps(self.Definition.Set_Idp_Info_Payload),headers=self.Definition.Set_Idp_Info_Header)

            print(Profile.status_code)
            if Profile.status_code == 200:
                response_json = Profile.json()
                print(response_json)


            elif Profile.status_code == 200 and Profile.json()["success"] != "true":
                print("yolo pass")
            else:
                self._Logger.error("the return status is  {} and not 200, please check".format(Profile.status_code))
                raise Exception("the return status is  {} and not 200, please check".format(Profile.status_code))
            self._Logger.info("\n\Set IDP Information response={0}\nstatus_code={1}\n\n".format(response_json, Profile.status_code))

    
    # ---------------------------------------------------------------------------------------------------------------------------------------------
    
    def Get_SslCert_Info(self,  # Function ssl cert info
    read_from_ma=True):  # (bool)    reads ssl cert info ID from MA
        """
        Work Flow
        ----------
            1. Go to MA / Adminstration / Client Connector Support / Advanced Configuration
            """
        if read_from_ma == True:
            #self.Definition.Get_SslCert_Info_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
            Profile = self.REQ(request_type="get",url=self.Definition.Get_SslCert_Info_Url, headers=self.Definition.Get_SslCert_Info_Header)
            response_json = Profile.json()
            if Profile.status_code == 200:
                self._Logger.debug(Profile.json())
                self._Logger.info("Get SSL cert info Success")
            else:
                self._Logger.error("Get SSL cert info Failed")
                raise Exception("Get SSL cert info Failed")
            self._Logger.info("\n\Get SSl Cert Information response={0}\nstatus_code={1}\n\n".format(response_json, Profile.status_code))
            
    # ---------------------------------------------------------------------------------------------------------------------------------------------

    def Get_Reminder_Notification(self,  # Function to Fetch Reminder notification
                    read_from_ma=True,save_to_temp=True):  # (bool)    reads admin user ID from MA
        """
        Work Flow
        ----------
            1. Go to MA / Administration / Client Connector Notifications / Click Reminder Notification Settings
            """
        if read_from_ma == True:
            #self.Definition.Get_Reminder_Notification_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
            Profile = self.REQ(request_type="get",url=self.Definition.Get_Reminder_Notification_Url,headers=self.Definition.Get_Reminder_Notification_Header)
            response_json = Profile.json()
            if Profile.status_code == 200:
                self._Logger.debug(response_json)
                self._Logger.info("Get Reminder Notification Success")
            else:
                self._Logger.error("Get Reminder Notification Failed")
                raise Exception("Get Reminder Notification Failed")
            
        if save_to_temp:
            self._Logger.info("making directory {}".format(self.temp_path))
            if not os.path.exists(self.temp_path):
                os.makedirs(self.temp_path)
                print("directory made")
        _file = open(self.temp_file, "a")
        _file.write("getReminderNotification_Response:{}".format(response_json))
        _file.write("\n")
        print("file written")
        _file.close()
        self._Logger.info("\n\nReminder notification, response={0}\nstatus_code={1}\nsave_to_temp={2}\n\n".format(response_json,Profile.status_code,save_to_temp))
            
    # ---------------------------------------------------------------------------------------------------------------------------------------------    
    def Create_Reminder_Notification(self,         # Function to create reminder notification 
    save_to_temp=True):                            # (bool)    if True, save ID to a tempfile
        """
        Work Flow
        1. Go to MA / Administration / Client Connector Notifications / Click Reminder Notification Settings / update Configure Reminder Frequency
        ----------
        """
        self.Definition = admin_res.admin_res(self.cloud)
        #self.Definition.Create_Reminder_Notification_Payload['name'] = self.device_posture_name
        #self.Definition.Create_Reminder_Notification_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
        self._Logger.info("url is {}".format(self.Definition.Create_Reminder_Notification_Url))
        self._Logger.info("Payload is {}".format(json.dumps(self.Definition.Create_Reminder_Notification_Payload)))
        self._Logger.info("Header is {}".format(self.Definition.Create_Reminder_Notification_Header))

        Profile = self.REQ(request_type="post",url=self.Definition.Create_Reminder_Notification_Url, data=json.dumps(self.Definition.Create_Reminder_Notification_Payload),
                                headers=self.Definition.Create_Reminder_Notification_Header)

        if Profile.status_code == 401:
            print('Invalid request! Resending request to get Auth-token via API')
            self.Definition.Create_Reminder_Notification_Header['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

            Profile = requests.post(self.Definition.Create_Reminder_Notification_Url, data=json.dumps(self.Definition.Create_Reminder_Notification_Payload),
                                headers=self.Definition.Create_Reminder_Notification_Header)
        response_json = Profile.json()
        print(Profile.status_code)
        if Profile.status_code == 200:
            if Profile.json()['success']!='true':
                     self._Logger.error("ERROR with Reminder Notification: {}".format(Profile.json()))
                     raise Exception("ERROR with Reminder Notification : {}".format(Profile.json()))
            else:
                    self._Logger.info("Set Reminder Notification is success")

        else:
                self._Logger.error("the return status is  {} and not 200, please check".format(Profile.status_code))
                raise Exception("the return status is  {} and not 200, please check".format(Profile.status_code))
        
        if save_to_temp:
            self._Logger.info("making directory {}".format(self.temp_path))
            if not os.path.exists(self.temp_path):
                os.makedirs(self.temp_path)
                print("directory made")
        _file = open(self.temp_file, "a")
        _file.write("updateReminderNotification_Response:{}".format(response_json))
        _file.write("\n")
        print("file written")
        _file.close()
        self._Logger.info("\n\nReminder notification created response={0}\n\nstatus_code={1}\nsave_to_temp={2}\n\n".format(response_json,Profile.status_code,save_to_temp))

    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Get_Tray_Notification(self,  # Function to Fetch Tray notification 
                read_from_ma=True,save_to_temp=True):  # (bool)    reads admin user ID from MA
            """
            Work Flow
            ----------
                1.  Go to MA / Adminstration / Click "End User Notification Settings
                """
            if read_from_ma == True:
                #self.Definition.Get_Tray_Notification_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
                Profile = self.REQ(request_type="get",url=self.Definition.Get_Tray_Notification_Url,headers=self.Definition.Get_Tray_Notification_Header)
                response_json = Profile.json()
                if Profile.status_code == 200:
                    self._Logger.debug(response_json)
                    self._Logger.info("Get Tray Notification Success")
                else:
                    self._Logger.error("Get Tray Notification Failed")
                    raise Exception("Get Tray Notification Failed")
                
            if save_to_temp:
                self._Logger.info("making directory {}".format(self.temp_path))
                if not os.path.exists(self.temp_path):
                    os.makedirs(self.temp_path)
                    print("directory made")
            _file = open(self.temp_file, "a")
            _file.write("getTrayNotification_ResponseID:{}".format(response_json["id"]))
            _file.write("\n")
            print("file written")
            _file.close()
            self._Logger.info("\n\Tray notification, response={0}\nstatus_code={1}\nsave_to_temp={2}\n\n".format(response_json,Profile.status_code,save_to_temp))
    # ---------------------------------------------------------------------------------------------------------------------------------------------    
    def Create_Tray_Notification(self,         # Function to create tray notification 
    save_to_temp=True):                            # (bool)    if True, save ID to a tempfile
        """
        Work Flow

        1.  Go to MA / Adminstration / Click "End User Notification Settings / Enable/disable any tab
        ----------
           
        """
        self.Definition = admin_res.admin_res(self.cloud)
        #self.Definition.Create_Reminder_Notification_Payload['name'] = self.device_posture_name
        #self.Definition.Create_Reminder_Notification_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
        self._Logger.info("url is {}".format(self.Definition.Create_Tray_Notification_Url))
        self._Logger.info("Payload is {}".format(json.dumps(self.Definition.Create_Tray_Notification_Payload)))
        self._Logger.info("Header is {}".format(self.Definition.Create_Tray_Notification_Header))

        Profile = self.REQ(request_type="post",url=self.Definition.Create_Tray_Notification_Url, data=json.dumps(self.Definition.Create_Tray_Notification_Payload),
                                headers=self.Definition.Create_Tray_Notification_Header)

        if Profile.status_code == 401:
            print('Invalid request! Resending request to get Auth-token via API')
            #self.Definition.Create_Reminder_Notification_Header['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

            Profile = requests.post(self.Definition.Create_Tray_Notification_Url, data=json.dumps(self.Definition.Create_Tray_Notification_Payload),
                                headers=self.Definition.Create_Tray_Notification_Header)
        response_json = Profile.json()
        print(Profile.status_code)
        if Profile.status_code == 200:
                if Profile.json()['success']!='true':
                     self._Logger.error("ERROR with Tray Notification Update: {}".format(Profile.json()))
                     raise Exception("ERROR with Tray Notification Update : {}".format(Profile.json()))
                else:
                    self._Logger.info("Tray Notification Update is success")

        else:
                self._Logger.error("the return status is  {} and not 200, please check".format(Profile.status_code))
                raise Exception("the return status is  {} and not 200, please check".format(Profile.status_code))
        
        if save_to_temp:
                self._Logger.info("making directory {}".format(self.temp_path))
                if not os.path.exists(self.temp_path):
                    os.makedirs(self.temp_path)
                    print("directory made")
        _file = open(self.temp_file, "a")
        _file.write("updateTrayNotification_ResponseID:{}".format(response_json["id"]))
        _file.write("\n")
        print("file written")
        _file.close()
        self._Logger.info("\n\nTray notification created response={0}\n\nstatus_code={1}\nsave_to_temp={2}\n\n".format(response_json,Profile.status_code,save_to_temp))

    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Partner_device_list(self,                # Function to list partner device
                            save_to_temp=True):  # (bool)    if True, save ID to a tempfile
        """
        Work Flow
        ----------
        1. Go to MA / Enrolled Devices / Click Partnet Devices
            
        """
        self.Definition = admin_res.admin_res(self.cloud)
        # self.Definition.Create_Reminder_Notification_Payload['name'] = self.device_posture_name
        #self.Definition.Create_Reminder_Notification_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
        self._Logger.info("url is {}".format(self.Definition.Partner_device_list_Url))
        self._Logger.info("Payload is {}".format(json.dumps(self.Definition.Partner_device_list_Payload)))
        self._Logger.info("Header is {}".format(self.Definition.Partner_device_list_Header))

        Profile = self.REQ(request_type="post",url=self.Definition.Partner_device_list_Url,data=json.dumps(self.Definition.Partner_device_list_Payload),
                                headers=self.Definition.Create_Reminder_Notification_Header)

        if Profile.status_code == 401:
            print('Invalid request! Resending request to get Auth-token via API')
            #self.Definition.Partner_device_list_Header['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

            Profile = self.REQ(request_type="post",url=self.Definition.Partner_device_list_Url,data=json.dumps(self.Definition.Partner_device_list_Payload),
                                headers=self.Definition.Create_Reminder_Notification_Header)

        print(Profile.status_code)
        if Profile.status_code == 200:
            response_json = Profile.json()
        else:
            self._Logger.error("the return status is  {} and not 200, please check".format(Profile.status_code))
            raise Exception("the return status is  {} and not 200, please check".format(Profile.status_code))
        self._Logger.info(
            "\n\nPartner device list response={0}\n\nstatus_code={1}\nsave_to_temp={2}\n\n".format(response_json,
                                                                                                       Profile.status_code,
                                                                                                       save_to_temp))
    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Get_Posture_Profile_List(self,  # Function to Fetch posture profile list
                   read_from_ma=True,save_to_temp=True):  # (bool)    reads posture profile ID from MA
        """
        Work Flow
        ----------
            1. Go to MA / Adminstration / Device Posture delete/ add / edit"
            """
        if read_from_ma == True:
            #self.Definition.Get_Posture_Profile_List_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
            Profile = self.REQ(request_type="get",url=self.Definition.Get_Posture_Profile_List_Url,
                                        headers=self.Definition.Get_Posture_Profile_List_Header,
                                        data=json.dumps(self.Definition.Get_Posture_Profile_List_Payload))
            response_json = Profile.json()
            if Profile.status_code == 200:
                self._Logger.debug(Profile.json())
                self._Logger.info("List Posture profile Success")
            else:
                self._Logger.error("List Posture profile Failed")
                raise Exception("List Posture profile Failed")
        if save_to_temp:
            self._Logger.info("making directory {}".format(self.temp_path))
            if not os.path.exists(self.temp_path):
                os.makedirs(self.temp_path)
                print("directory made")
        _file = open(self.temp_file, "a")
        _file.write("PostureProfile_List_ResponseID:{}".format(response_json['postureProfiles'][0]["id"]))
        _file.write("\n")
        print("file written")
        _file.close()
        self._Logger.info("\n\nReminder notification, response={0}\nstatus_code={1}\nsave_to_temp={2}\n\n".format(response_json,Profile.status_code,save_to_temp))
    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Create_Device_Posture(self,         # Function to create the device posture
    name = None,
    description = "Device Posture Created",
    Operating_System = None,            # (Windows/Mac/Linux/Android/Ios)
    posture_type = None,
    process_path = None,
    applyToMachineTunnel = False,
    save_to_temp=True):                 # (bool)    if True, save ID to a tempfile
        """
        Work Flow
        ----------
        1. Go to MA / Adminstration / Device Posture / Edit existing Device posture"
           
        """
        self.Definition = admin_res.admin_res(self.cloud)
        type_value = {"Mac": 4, "Windows": 3, "Linux": 5, "Android": 2, "Ios": 1}[Operating_System]

        self.Definition.CREATE_DEVICE_POSTURE_PAYLOAD['name'] = (self.device_posture_name if not name else name)
        self.Definition.CREATE_DEVICE_POSTURE_PAYLOAD['description'] = description

        if posture_type == "Detect Mircosoft Defender":
            self.Definition.CREATE_DEVICE_POSTURE_PAYLOAD['type'] = 15
            self.Definition.CREATE_DEVICE_POSTURE_PAYLOAD['msDefenders'] = [{"type":type_value,"status":1,"processPath":process_path}]


        self.Definition.CREATE_DEVICE_POSTURE_PAYLOAD['description'] = description

        #self.Definition.CREATE_DEVICE_POSTURE_HEADER['auth-token'] = self.Authenticate_To_Mobile_Admin()
        self._Logger.info("url is {}".format(self.Definition.CREATE_DEVICE_POSTURE_URL))
        self._Logger.info("Payload is {}".format(json.dumps(self.Definition.CREATE_DEVICE_POSTURE_PAYLOAD)))
        self._Logger.info("Header is {}".format(self.Definition.CREATE_DEVICE_POSTURE_HEADER))

        Profile = self.REQ(request_type="post",url=self.Definition.CREATE_DEVICE_POSTURE_URL, data=json.dumps(self.Definition.CREATE_DEVICE_POSTURE_PAYLOAD),
                                headers=self.Definition.CREATE_DEVICE_POSTURE_HEADER)

        if Profile.status_code == 401:
            print('Invalid request! Resending request to get Auth-token via API')
            #self.Definition.CREATE_DEVICE_POSTURE_HEADER['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

            Profile = self.REQ(request_type="post",url=self.Definition.CREATE_DEVICE_POSTURE_URL, data=json.dumps(self.Definition.CREATE_DEVICE_POSTURE_PAYLOAD),
                                headers=self.Definition.CREATE_DEVICE_POSTURE_HEADER)

        print(Profile.status_code)
        if Profile.status_code == 200:
            response_json = Profile.json()
            print(response_json)
            self.device_posture_id = response_json["id"]
            self.is_device_posture_created = True
        elif Profile.status_code==200 and Profile.json()["error"]=="3070":
            print("yolo pass")
        else:
            self._Logger.error("the return status is  {} and not 200, please check".format(Profile.status_code))
            raise Exception("the return status is  {} and not 200, please check".format(Profile.status_code))
        if save_to_temp:
            self._Logger.info("making directory {}".format(self.temp_path))
            if not os.path.exists(self.temp_path):
                os.makedirs(self.temp_path)
                print("directory made")
            _file = open(self.temp_file, "a")
            _file.write("device_posture_id:{}".format(self.device_posture_id))
            _file.write("\n")
            print("file written")
            _file.close()
        self._Logger.info("\n\nDevice Posture created. response={0}\nid={1}\nstatus_code={2}\nsave_to_temp={3}\n\n".format(response_json,self.device_posture_id,Profile.status_code,save_to_temp))
    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Get_device_posture_ID(self,  # Function to Fetch device posture ID
                            # (bool)    reads device posture ID from tempfile if True
                          name = None,
                          fetchAll = False,
                          read_from_ma=True):  # (bool)    reads device posture ID from MA if True
        """
        Work Flow
        ----------
            1. Getting device posture ID from the created device posture
          
            """
        if read_from_ma == True:
            #self.Definition.Get_Device_Posture_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
            Profile = self.REQ(request_type="get",url=self.Definition.Get_Device_Posture_List_Url,
                                   headers=self.Definition.Get_Device_Posture_Header,data=json.dumps(self.Definition.Get_Device_Posture_Payload))
            response_json = Profile.json()
            self._Logger.info(response_json)
            if Profile.status_code == 200:
                if fetchAll:
                    return response_json['postureProfiles']
                for i in range(len(response_json['postureProfiles'])):
                    if response_json['postureProfiles'][i]['name'] == (self.device_posture_name if not name else name):
                        print(response_json['postureProfiles'][i]['id'])
                        if True:
                            return str(response_json['postureProfiles'][i]['id'])
                        else:
                            return str(response_json)
            self._Logger.error("No such posture profile found")
            raise Exception("Error: No such posture profile found")
        self._Logger.error("wrong selection of options or no id found")
        raise Exception("Error :: wrong selection of options or no id found")
    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Delete_Device_Posture(self,  # Function to delete device posture
                              name = None
                              ):
            """
            Work Flow
            ----------
            1. Get device posture ID to be deleted from Get_device_posture()
            2. Delete the device posture
               
            """
            ID = str(self.Get_device_posture_ID(name = name))
            #self.Definition.Delete_Device_Posture_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
            self.Definition.Delete_Device_Posture_Url = self.Definition.Delete_Device_Posture_Url + ID

            self._Logger.info("\n\nDelete_Device_Posture\nUrl:{0}\nheaders\n{1}\n\n".format(self.Definition.Delete_Device_Posture_Url,
                                                                          self.Definition.Delete_Device_Posture_Header))
            self.Definition.Delete_Device_Posture_Payload["id"] = ID
            Profile = self.REQ(request_type="get",url=self.Definition.Delete_Device_Posture_Url,
                                                        data=json.dumps(self.Definition.Delete_Device_Posture_Payload),
                                                       headers=self.Definition.Delete_Device_Posture_Header)

            # Validate success
            #print(Delete_Device_Posture_Info)
            if Profile.status_code == 200:
                self._Logger.info("Device Posture status: " + str("deleted") + "\nresponse:" + str(Profile.json()))
            else:
                self._Logger.error("Device Posture deletion didnt go well!")
                raise Exception("Error :: DP deletion didnt go well!")
            self.is_device_posture_created = False
            #if not os.path.exists(self.temp_file):
            #    return
            #file_contents = None
            #with open(self.temp_file, "r") as _file:
            #    file_contents = _file.read().splitlines()
            #    dpos_id_line_number = [i for i in range(len(file_contents)) if "dpid" in file_contents[i]][0]
            #    del file_contents[dpos_id_line_number]
            #_file = open(self.temp_file, "w")
            #_file.write(''.join(file_contents))
            #_file.close()
            response_json = Profile.json()
            self._Logger.info("\n\nDelete_Device_Posture_response\nstatus_code:{0}\nresponse:{1}\nSuccess:{2}".format(Profile.status_code, response_json,"success" in response_json.keys()))
    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Get_DevicePosture_OS_Version(self,  # Function to Fetch Reminder notification
                    read_from_ma=True,save_to_temp=True):  # (bool)    reads admin user ID from MA
        """
        Work Flow
        ----------
            1. Go to MA / Adminstration / Click Device posture
            """
        if read_from_ma == True:
            #self.Definition.Get_Reminder_Notification_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
            Profile = self.REQ(request_type="get",url=self.Definition.Get_Device_Posture_OS_Version_Url,headers=self.Definition.Get_Device_Posture_OS_Version_Healder,data=json.dumps(self.Definition.Get_Device_Posture_OS_Version_Payload))
            response_json = Profile.json()
            if Profile.status_code == 200:
                self._Logger.debug(response_json)
                self._Logger.info("Get Device Posture OS Version Success")
            else:
                self._Logger.error("Get Device Posture OS Version Failed")
                raise Exception("Get Device Posture OS Version Failed")
            
        if save_to_temp:
            self._Logger.info("making directory {}".format(self.temp_path))
            if not os.path.exists(self.temp_path):
                os.makedirs(self.temp_path)
                print("directory made")
        _file = open(self.temp_file, "a")
        _file.write("devicePostureOsVersion_Response:{}".format(response_json['records'][0]["id"]))
        _file.write("\n")
        print("file written")
        _file.close()
        self._Logger.info("\n\nDevice Posture OS Version, response={0}\nstatus_code={1}\nsave_to_temp={2}\n\n".format(response_json,Profile.status_code,save_to_temp))

    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Get_PrivacyInfo(self, save_to_temp=True,                 # Function to Fetch privacy info
                          read_from_ma=True):  # (bool)    reads privacy info info from MA if True
        """
        Work Flow
        ----------
           1. 1. Go to MA / Adminstration / Client Connector Support / Privicy Info
            """
        if read_from_ma == True:
            #self.Definition.GET_ADMIN_ROLES_LIST_HEADER['auth-token'] = self.Authenticate_To_Mobile_Admin()
            Profile = self.REQ(request_type="get",url=self.Definition.Get_Privacy_Info_Url,headers=self.Definition.Get_Privacy_Info_Header)
           
            if Profile.status_code == 200:
                response_json = Profile.json()
                self._Logger.info(Profile.json())
                self._Logger.debug(Profile.json())
                self._Logger.info("Get privacy info Success")
                return str(response_json["id"])         
            else:
                self._Logger.error("No privacy info found")
                raise Exception("Error: No such privacy info found")
        if save_to_temp:
            self._Logger.info("making directory {}".format(self.temp_path))
            if not os.path.exists(self.temp_path):
                os.makedirs(self.temp_path)
                print("directory made")
        _file = open(self.temp_file, "a")
        _file.write("getPrivacyInfo_ResponseID:{}".format(response_json["id"]))
        _file.write("\n")
        print("file written")
        _file.close()
        self._Logger.info("\n\nGet Privacy Info, response={0}\nstatus_code={1}\nsave_to_temp={2}\n\n".format(response_json,Profile.status_code,save_to_temp))
    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Set_Privacy_Info(self,         # Function to set privacy info
    save_to_temp=True,):                 # (bool)    if True, save ID to a tempfile
        """
        Work Flow
        ----------
        1. Go to MA / Adminstration / Client Connector Support / Set Privicy Info
         
        """
        self.Definition = admin_res.admin_res(self.cloud)
        ID = str(self.Get_PrivacyInfo())
       
        #print(ID)
        #self.Definition.Set_Privacy_Info_Payload['id'] = self.privacy_id
        #self.Definition.Set_Privacy_Info_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
        self.Definition.Set_Privacy_Info_Payload["id"] = ID
        print(self.Definition.Set_Privacy_Info_Payload["enablePacketCapture"])
        if self.Definition.Set_Privacy_Info_Payload["enablePacketCapture"] == 0 or self.Definition.Set_Privacy_Info_Payload["enablePacketCapture"] == "0":
        #if self.Definition.Set_Privacy_Info_Payload["enablePacketCapture"] == "0" :
            
            self.Definition.Set_Privacy_Info_Payload["enablePacketCapture"] = 1
        else:
            
            self.Definition.Set_Privacy_Info_Payload["enablePacketCapture"] = 0

        print("aaaaaaaaaaaaaaaaaaaaaaaaaaaa")
        print(self.Definition.Set_Privacy_Info_Payload["enablePacketCapture"])
            
        #self.Definition.Set_Privacy_Info_Payload["enablePacketCapture"] = (1 if action==True else 0)
        self._Logger.info("url is {}".format(self.Definition.Set_Privacy_Info_Url))
        self._Logger.info("Payload is {}".format(json.dumps(self.Definition.Set_Privacy_Info_Payload)))
        self._Logger.info("Header is {}".format(self.Definition.Set_Privacy_Info_Header))
       

        Profile = self.REQ(request_type="post",url=self.Definition.Set_Privacy_Info_Url, data=json.dumps(self.Definition.Set_Privacy_Info_Payload),
                                headers=self.Definition.Set_Privacy_Info_Header)

        if Profile.status_code == 401:
            print('Invalid request! Resending request to get Auth-token via API')
            #self.Definition.Create_Reminder_Notification_Header['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

            Profile = self.REQ(request_type="post",url=self.Definition.Set_Privacy_Info_Url, data=json.dumps(self.Definition.Set_Privacy_Info_Payload),
                                headers=self.Definition.Set_Privacy_Info_Header)

        response_json = Profile.json()
        print(Profile.status_code)
        if Profile.status_code == 200:
            if Profile.json()['success']!='true':
                self._Logger.error("ERROR set privacy info: {}".format(Profile.json()))
                raise Exception("ERROR with set privacy info : {}".format(Profile.json()))
            else:
                self._Logger.info("Set privacy info is success")

        else:
                self._Logger.error("the return status is  {} and not 200, please check".format(Profile.status_code))
                raise Exception("the return status is  {} and not 200, please check".format(Profile.status_code))
        if save_to_temp:
            self._Logger.info("making directory {}".format(self.temp_path))
            if not os.path.exists(self.temp_path):
                os.makedirs(self.temp_path)
                print("directory made")
            _file = open(self.temp_file, "a")
            _file.write("psetid:{}".format(response_json["id"]))
            _file.write("\n")
            print("file written")
            _file.close()
        self._Logger.info("\n\nSetting privacy info  response={0}\nstatus_code={1}\nsave_to_temp={2}\n\n".format(response_json,Profile.status_code,save_to_temp))
    # ---------------------------------------------------------------------------------------------------------------------------------------------
    # ---------------------------------------------------------------------------------------------------------------------------------------------

    def Get_TraceroutePolicyInfo(self,                           # Function to Fetch trace route information
                          read_from_ma=True,                     # (bool)    reads trace route info from MA if True
                          save_to_temp=True):                    # if Ture, save to resppnse to temp file
        """
        Work Flow
        ----------
           1. Go to MA/Adminstration/Client Connector Support/Network Performance
            """
        if read_from_ma == True:
            #self.Definition.GET_ADMIN_ROLES_LIST_HEADER['auth-token'] = self.Authenticate_To_Mobile_Admin()
            Profile = self.REQ(request_type="get",url=self.Definition.Get_traceroutepoliyinfo_Url,headers=self.Definition.Get_traceroutepoliyinfo_Header)
            response_json = Profile.json()
            if Profile.status_code == 200:
                
                self._Logger.info(Profile.json())
                self._Logger.debug(Profile.json())
                self._Logger.info("Get trace route policy info Success")
                return str(response_json["id"])       
            else:
                self._Logger.error("No such trace route info found")
                raise Exception("Error: No such trace route info found")
            
        if save_to_temp:
            self._Logger.info("making directory {}".format(self.temp_path))
            if not os.path.exists(self.temp_path):
                os.makedirs(self.temp_path)
                print("directory made")
            _file = open(self.temp_file, "a")
            _file.write("getTraceroutePolicyInfo_Response:{}".format(response_json["id"]))
            _file.write("\n")
            print("file written")
            _file.close()
        self._Logger.info("\n\nTrace route policy info, response={0}\nstatus_code={1}\nsave_to_temp={2}\n\n".format(response_json,Profile.status_code,save_to_temp))
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------

    def Set_TraceroutepolicyInfo(self,save_to_temp=True): 
            """
            Work Flow
            1. Go to MA/Adminstration/Client Connector Support/Network Performance
            2. Trun on / off any button
            ----------
            """
            self.Definition = admin_res.admin_res(self.cloud)
            ID = str(self.Get_TraceroutePolicyInfo())
            self.Definition.Set_TraceroutepolicyInfo_Payload["id"] = ID
            #enable_disable_reason = False
            #self.Definition.Set_Idp_Info_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
            #self.Definition.Set_TraceroutepolicyInfo_Payload["collectRouteInfoOnNetworkChangeEnabled"] = ("1" if enable_disable_reason else "0")
            #if self.Definition.Set_TraceroutepolicyInfo_Payload["collectRouteInfoOnNetworkChangeEnabled"] == 0:
            #   self.Definition.Set_TraceroutepolicyInfo_Payload["collectRouteInfoOnNetworkChangeEnabled"] = 1
            #else:
            #    self.Definition.Set_TraceroutepolicyInfo_Payload["collectRouteInfoOnNetworkChangeEnabled"] = 0
            self._Logger.info("url is {}".format(self.Definition.Set_TraceroutepolicyInfo_Url))
            self._Logger.info("Payload is {}".format(json.dumps(self.Definition.Set_TraceroutepolicyInfo_Payload)))
            self._Logger.info("Header is {}".format(self.Definition.Set_TraceroutepolicyInfo_Header))
            Profile=self.REQ(request_type="post",url=self.Definition.Set_TraceroutepolicyInfo_Url,data=json.dumps(self.Definition.Set_TraceroutepolicyInfo_Payload),headers=self.Definition.Set_TraceroutepolicyInfo_Header)

            if Profile.status_code == 401:
                print('Invalid request! Resending request to get Auth-token via API')
                #self.Definition.Set_Idp_Info_Header['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

                Profile = self.REQ(request_type="post",url=self.Definition.Set_TraceroutepolicyInfo_Url,data=json.dumps(self.Definition.Set_TraceroutepolicyInfo_Payload),headers=self.Definition.Set_TraceroutepolicyInfo_Header)
            response_json = Profile.json()
            print(Profile.status_code)
            if Profile.status_code == 200:
                if Profile.json()['success']!='true':
                     self._Logger.error("ERROR with Trace route info: {}".format(Profile.json()))
                     raise Exception("ERROR with Trace route info : {}".format(Profile.json()))
                else:
                    self._Logger.info("Set trace route policy info is success")

            else:
                self._Logger.error("the return status is  {} and not 200, please check".format(Profile.status_code))
                raise Exception("the return status is  {} and not 200, please check".format(Profile.status_code))
            
            if save_to_temp:
                self._Logger.info("making directory {}".format(self.temp_path))
                if not os.path.exists(self.temp_path):
                    os.makedirs(self.temp_path)
                    print("directory made")
            _file = open(self.temp_file, "a")
            _file.write("setTraceroutePolicyInfo_ResponseID:{}".format(response_json["id"]))
            _file.write("\n")
            print("file written")
            _file.close()
            self._Logger.info("\n\nSet Trace route policy info, response={0}\nstatus_code={1}\nsave_to_temp={2}\n\n".format(response_json,Profile.status_code,save_to_temp))
            
    # ----------------------------------------------------------------------------------------------------------------------
    @contextmanager
    def Get_ZpaPartnerLoginInfo(self):
        """
        Fetch ZPA partner login information

        WorkFlow:
            * The function fetches ZPA partner login information from an API endpoint using a GET request.
            * The function uses a try block to catch any exceptions that may occur during the request, and logs the error message if an exception is raised.
            * The function returns the id of the ZPA partner login information if the request is successful, or raises an exception if the request fails.
        """
        try:
            self.Definition = admin_res.admin_res(self.cloud)
            with self.REQ(request_type="get", url=self.Definition.Get_ZpaPartnerLoginInfo_Url, headers=self.Definition.Get_ZpaPartnerLoginInfo_Header) as response:
                response_json = response.json()
                self._Logger.info("Response: {}".format(response_json))
                if response.status_code == 200:
                    self._Logger.info("Get ZPA partner Login Info Success")
                    return response_json["id"]
                else:
                    self._Logger.error("No such ZPA partner login info found")
                    raise Exception("Error: No such ZPA partner login info found")
        except Exception as e:
            self._Logger.error("Error fetching ZPA partner login info: {}".format(e))
            raise e
       
    # ------------------------------------------------------------------------------------------------------------------------------------------------
    def Set_ZpaPartnerLoginInfo(self, 
        ID: int = None,                                  # ID: The ID of the tenant for which to set the ZPA partner login information.
        Allow_Partner_Login_To_This_Tenant: bool = True,    # Allow_Partner_Login_To_This_Tenant: A boolean value indicating whether partners are allowed to login to this tenant.
        Allow_This_Tenant_Login_To_Others: bool = True      # Allow_This_Tenant_Login_To_Others: A boolean value indicating whether this tenant is allowed to login to other partners.
        ) -> None:
        """Set ZPA partner login information for a tenant.
        
        Args:
            id (int): The ID of the tenant for which to set the ZPA partner login information.
            Allow_Partner_Login_To_This_Tenant (bool): A boolean value indicating whether partners are allowed to login to this tenant.
            Allow_This_Tenant_Login_To_Others (bool): A boolean value indicating whether this tenant is allowed to login to other partners.
        WorkFlow:
            * The code defines a context manager that sets the ZPA partner login information for a tenant. 
            * It takes three arguments: ID, Allow_Partner_Login_To_This_Tenant, and Allow_This_Tenant_Login_To_Others. 
            * The context manager makes a POST request to the Set_ZpaPartnerLoginInfo API endpoint with the payload and headers.
            * If the response status code is 200 and the response JSON contains a success message, the context manager logs a success message. 
            * Otherwise, it logs an error message and raises an exception.
        """
        self.Definition = admin_res.admin_res(self.cloud)
        self.Definition.Set_ZpaPartnerLoginInfo_Payload["id"]= str(ID)
        if Allow_Partner_Login_To_This_Tenant:
            self.Definition.Set_ZpaPartnerLoginInfo_Payload["partnersAllowedToLogin"]= "1"
        else:
            self.Definition.Set_ZpaPartnerLoginInfo_Payload["partnersAllowedToLogin"]= "0"
        if Allow_This_Tenant_Login_To_Others:
            self.Definition.Set_ZpaPartnerLoginInfo_Payload["userAllowedToAddPartner"]= "1"
        else:
            self.Definition.Set_ZpaPartnerLoginInfo_Payload["userAllowedToAddPartner"]= "0"

        partner_login_response=self.REQ(request_type="post",url=self.Definition.Set_ZpaPartnerLoginInfo_Url,data=json.dumps(self.Definition.Set_ZpaPartnerLoginInfo_Payload),headers=self.Definition.Set_ZpaPartnerLoginInfo_Header)

        self._Logger.info("Response : {}".format(partner_login_response.status_code))
        if partner_login_response.status_code == 200:
            if partner_login_response.json()['success']!='true':
                    self._Logger.error("ERROR with ZPA partner login : {}".format(partner_login_response.json()))
                    raise Exception("ERROR with ZPA partner login : {}".format(partner_login_response.json()))
            else:
                self._Logger.info("Set ZPA partner login is success")
        else:
            self._Logger.error("the return status is  {} and not 200, please check".format(partner_login_response.status_code))
            raise Exception("the return status is  {} and not 200, please check".format(partner_login_response.status_code))


     # ------------------------------------------------------------------------------------------------------------------------------------------------------

    def Get_UserAgentSuffix(self,save_to_temp=True,            # Function to Fetch user agent suffix information 
                          read_from_ma=True):                  # (bool)    reads user agent suffix information from MA if True
        """
        Work Flow
        ----------
           1. 1. Go to MA/Adminstration/User Agent
            """
        if read_from_ma == True:
            #self.Definition.GET_ADMIN_ROLES_LIST_HEADER['auth-token'] = self.Authenticate_To_Mobile_Admin()
            Profile = self.REQ(request_type="get",url=self.Definition.Get_user_agent_suffix_Url,headers=self.Definition.Get_user_agent_suffix_Header)
            response_json = Profile.json()
            self._Logger.info(response_json)
            if Profile.status_code == 200:
                self._Logger.debug(Profile.json())
                self._Logger.info("Get user agent suffix info Success")         
            else:
                self._Logger.error("No such user agent suffix  found")
                raise Exception("Error: No such user agent suffix found")
            if save_to_temp:
                self._Logger.info("making directory {}".format(self.temp_path))
                if not os.path.exists(self.temp_path):
                    os.makedirs(self.temp_path)
                    print("directory made")
            _file = open(self.temp_file, "a")
            _file.write("getUserAgentSuffix_Response:{}".format(response_json))
            _file.write("\n")
            print("file written")
            _file.close()
            self._Logger.info("\n\nGet User agent suffix info, response={0}\nstatus_code={1}\nsave_to_temp={2}\n\n".format(response_json,Profile.status_code,save_to_temp))
       


    # ------------------------------------------------------------------------------------------------------------------------------------------------

    def Set_UserAgentSuffix(self,save_to_temp=True): 
            """
            Work Flow
            1. Go to MA/Adminstration/User Agent
            2. Set WBC user agent 
            ----------
        
            """
            self.Definition = admin_res.admin_res(self.cloud)
            #self.Definition.Set_Idp_Info_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
            self._Logger.info("url is {}".format(self.Definition.Set_user_agent_suffix_Url))
            self._Logger.info("Payload is {}".format(json.dumps(self.Definition.Set_user_agent_suffix_Payload)))
            self._Logger.info("Header is {}".format(self.Definition.Set_user_agent_suffix_Header))
            Profile=self.REQ(request_type="post",url=self.Definition.Set_user_agent_suffix_Url,data=json.dumps(self.Definition.Set_user_agent_suffix_Payload),headers=self.Definition.Set_user_agent_suffix_Header)

            if Profile.status_code == 401:
                print('Invalid request! Resending request to get Auth-token via API')
                #self.Definition.Set_Idp_Info_Header['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

                Profile = self.REQ(request_type="post",url=self.Definition.Set_user_agent_suffix_Url,data=json.dumps(self.Definition.Set_user_agent_suffix_Payload),headers=self.Definition.Set_user_agent_suffix_Header)
            response_json=Profile.json()
            print(Profile.status_code)
            if Profile.status_code == 200:
                if Profile.json()['success']!='true':
                     self._Logger.error("ERROR with user agent suffix : {}".format(Profile.json()))
                     raise Exception("ERROR with user agent suffix : {}".format(Profile.json()))
                else:
                    self._Logger.info("Set user agent suffix is success")

            else:
                self._Logger.error("the return status is  {} and not 200, please check".format(Profile.status_code))
                raise Exception("the return status is  {} and not 200, please check".format(Profile.status_code))
            
            if save_to_temp:
                self._Logger.info("making directory {}".format(self.temp_path))
                if not os.path.exists(self.temp_path):
                    os.makedirs(self.temp_path)
                    print("directory made")
            _file = open(self.temp_file, "a")
            _file.write("gsetUserAgentSuffix_Response:{}".format(response_json))
            _file.write("\n")
            print("file written")
            _file.close()
            self._Logger.info("\n\nSet User agent suffix info, response={0}\nstatus_code={1}\nsave_to_temp={2}\n\n".format(response_json,Profile.status_code,save_to_temp))


    # ---------------------------------------------------------------------------------------------------------------------------------------------

    def Get_Domain(self,save_to_temp=True,
                          read_from_ma=True):  # (bool)    reads user agent suffix domain from MA if True
        """
        Work Flow
        ----------
           1. 1. Go to MA/Adminstration/User agent/WBC user agent(Domain Based)/choose domain
            """
        if read_from_ma == True:
            #self.Definition.GET_ADMIN_ROLES_LIST_HEADER['auth-token'] = self.Authenticate_To_Mobile_Admin()
            Profile = self.REQ(request_type="get",url=self.Definition.Get_UserAgentSuffix_domain_Url,headers=self.Definition.Get_UserAgentSuffix_domain_Header)
            response_json = Profile.json()
            self._Logger.info(response_json)
            if Profile.status_code == 200:
                self._Logger.debug(Profile.json())
                self._Logger.info("Get domain Success")         
            else:
                self._Logger.error("No such domain info found")
                raise Exception("Error: No such domain info found")
        if save_to_temp:
            self._Logger.info("making directory {}".format(self.temp_path))
            if not os.path.exists(self.temp_path):
                    os.makedirs(self.temp_path)
                    print("directory made")
        _file = open(self.temp_file, "a")
        _file.write("getDomains_Response:{}".format(response_json))
        _file.write("\n")
        print("file written")
        _file.close()
        self._Logger.info("\n\nSet User agent suffix info, response={0}\nstatus_code={1}\nsave_to_temp={2}\n\n".format(response_json,Profile.status_code,save_to_temp))

    # ---------------------------------------------------------------------------------------------------------------------------------------------
    def Update_TrustedNetwork(self,save_to_temp=True): 
        """
        Work Flow
        1. Go to MA/Adminstration/Trusted Network
        2. Edit existing TN and save
        ----------
    
        """
        self.Definition = admin_res.admin_res(self.cloud)
        #self.Definition.Set_Idp_Info_Header['auth-token'] = self.Authenticate_To_Mobile_Admin()
        self._Logger.info("url is {}".format(self.Definition.Update_Trusted_Network_Url))
        self._Logger.info("Payload is {}".format(json.dumps(self.Definition.Update_Trusted_Network_Payload)))
        self._Logger.info("Header is {}".format(self.Definition.Update_Trusted_Network_Header))
        Profile=self.REQ(request_type="post",url=self.Definition.Update_Trusted_Network_Url,data=json.dumps(self.Definition.Update_Trusted_Network_Payload),headers=self.Definition.Update_Trusted_Network_Header)

        if Profile.status_code == 401:
            print('Invalid request! Resending request to get Auth-token via API')
            #self.Definition.Set_Idp_Info_Header['auth-token'] = self.Authenticate_To_Mobile_Admin(readFile=False)

            Profile = self.REQ(request_type="post",url=self.Definition.Update_Trusted_Network_Url,data=json.dumps(self.Definition.Update_Trusted_Network_Payload),headers=self.Definition.Update_Trusted_Network_Header)
        response_json=Profile.json()
        print(Profile.status_code)
        if Profile.status_code == 200:
            #if Profile.json()['success']!='true':
            #        self._Logger.error("ERROR with user agent suffix : {}".format(Profile.json()))
            #        raise Exception("ERROR with user agent suffix : {}".format(Profile.json()))
            #else:
            self._Logger.info("Set user agent suffix is success")

        else:
            self._Logger.error("the return status is  {} and not 200, please check".format(Profile.status_code))
            raise Exception("the return status is  {} and not 200, please check".format(Profile.status_code))
        
        if save_to_temp:
            self._Logger.info("making directory {}".format(self.temp_path))
            if not os.path.exists(self.temp_path):
                os.makedirs(self.temp_path)
                print("directory made")
        _file = open(self.temp_file, "a")
        _file.write("Update_TN_Response:{}".format(response_json))
        _file.write("\n")
        print("file written")
        _file.close()
        self._Logger.info("\n\nUpdate Trusted Network, response={0}\nstatus_code={1}\nsave_to_temp={2}\n\n".format(response_json,Profile.status_code,save_to_temp))
    
    # -----------------------------------------------------------------------------------------
    def Get_List_Of_Custom_AppBypass(self):
        """
        Target: This method retrieves a list of custom app bypasses from the API.
        WorkFlow:
            * This function fetches the list of custom app bypasses from the server.
            * Returns Profile: The response from the server, which should be a JSON object containing the custom app bypass list.
            * Raises Exception: If the status code of the response is not 200 (OK), an exception is raised with the message "Error :: Invalid Status code for Custom App Bypass List Fetch!".
        """
        Profile = self.REQ(request_type="get", url=self.Definition.Get_Custom_AppBypass_URL, headers=self.Definition.Custom_AppBypass_HEADER, data=json.dumps({"page": 1, "pageSize": 100, "search": ""}))
        if Profile.status_code == 200: 
            self._Logger.info("Custom App Bypass List Fetch Success")
            return Profile.json()
        else:
            self._Logger.error("Custom App Bypass List Fetch Failed")
            raise Exception("Error :: Invalid Status code for Custom App Bypass List Fetch!")
    
    # --------------------------------------------------------------------------------------------------------------
    def Get_Custom_App_Bypass_Profile_ID_With_Name(self, 
                                         Bypass_Name="Automation_Bypass",
                                         return_dict=True):
        """
        Target: The Get_Custom_App_Bypass_Profile_ID_With_Name function is used to retrieve the ID of a custom app bypass profile with a given name.
        Work Flow
        ----------
            * The function first retrieves a list of all custom app bypass profiles using the function.
            * It then iterates over the list and checks if the name of each profile matches the argument.
            * If a match is found, the function returns the ID of the profile and other information about the profile.
            * If no match is found, the function returns None, None
        """
        try:
            print("Bypass_Name--->"+Bypass_Name)
            result = self.Get_List_Of_Custom_AppBypass()
            print(result)
            for each_entry in result["customAppContracts"]:
                if each_entry["appName"]==Bypass_Name:
                    if return_dict:
                        return (each_entry["id"], each_entry)
                    else:
                        return each_entry["id"]
            return None, None
        except Exception as e:
            self._Logger.error("Custom App Bypass doesnt exist with given name", e)
            raise Exception("Error :: Custom App Bypass doesnt exist with given name!", e)
    

    # -----------------------------------------------------------------------------------------
    def Edit_Custom_AppBypass(self, 
        Bypass_Name = "Automation_Bypass",
        mode = "ipv4",
        port = None,
        ip_address = None,
        protocol = None,
        refresh=True
        ):
        payload = None
        """
        Target: To Perform actions related to Editing Custom App ByPass via GUI
        Work Flow
        ----------
            * The Edit_Custom_AppBypass function is used to create or edit a custom application bypass profile.
            * The function takes several parameters, including the name of the bypass profile, the mode (either "ipv4" or "ipv6"), the port, IP address, and protocol.
            * Exception: If an error occurs during the creation/editing of the bypass profile.
        """
        try:
            Custom_App_Bypass_ID, payload  = self.Get_Custom_App_Bypass_Profile_ID_With_Name(Bypass_Name = Bypass_Name, return_dict=True)
            print(Custom_App_Bypass_ID)
            print(payload)
            if not payload:
                raise Exception()
            url = copy.deepcopy(self.Definition.Edit_Custom_AppBypass_URL)
            if refresh:
                payload = copy.deepcopy(self.Definition.Create_Custom_AppBypass_PAYLOAD)
                payload["appData"] = []
                payload["appDataV6"] = []
            payload["id"] = str(Custom_App_Bypass_ID)
        except:
            url = copy.deepcopy(self.Definition.Create_Custom_AppBypass_URL)
            payload = copy.deepcopy(self.Definition.Create_Custom_AppBypass_PAYLOAD)
            payload["id"] = -1
            payload["appData"] = []
            payload["appDataV6"] = []

        payload["appName"] = str(Bypass_Name)
        if mode=="ipv4":
            payload["appData"].append({
                                        "port" : str(port),
                                        "ipaddr" : str(ip_address),
                                        "proto" : str(protocol)
                                    })
        elif mode=="ipv6":
            payload["appDataV6"].append({
                                        "port" : str(port),
                                        "ipaddr" : str(ip_address),
                                        "proto" : str(protocol)
                                    })


        print(url)
        Profile = self.REQ(request_type="post", url=url, headers=self.Definition.Custom_AppBypass_HEADER, data=json.dumps(payload))
        if Profile.status_code == 200 and (Profile.json()["success"]=="true" if "success" in Profile.json().keys() else True): 
            self._Logger.info("Custom App Bypass Create/Edit Success")
        else:
            self._Logger.error("Custom App Bypass Create/Edit Failed")
            raise Exception("Error :: Invalid Status code for Custom App Bypass Create/Edit!")
        
    
    # -------------------------------------------------------------------------------------------------------------
    def Delete_Custom_AppBypass(self, Bypass_Name="Automation_Bypass"):
        """
        Target: This function is used to delete a custom application bypass profile.
        WorkFlow
            * The function takes a Bypass_Name, which is the name of the custom application bypass profile to be deleted.
            * The function uses the Get_Custom_App_Bypass_Profile_ID_With_Name function to get the ID of the custom application bypass profile with the given name.
            * The function uses the REQ function to send a DELETE request to the API endpoint to delete the custom application bypass profile.
            * The function checks the status code of the response and logs a message if the deletion was successful or not.
            * If the deletion was not successful, the function raises an exception.
        """
        self.Definition = admin_res.admin_res(self.cloud)
        Custom_App_Bypass_ID  = str(self.Get_Custom_App_Bypass_Profile_ID_With_Name(Bypass_Name = Bypass_Name, return_dict=False))
        url = copy.deepcopy(self.Definition.Delete_Custom_App_Bypass_URL.replace("ADD_ID_HERE", Custom_App_Bypass_ID))
        Profile = self.REQ(request_type="delete", url=url, headers=self.Definition.Custom_AppBypass_HEADER)
        if Profile.status_code == 200: 
            self._Logger.info("Custom App Bypass Deletion Success")
        else:
            self._Logger.error("Custom App Bypass Deletion Failed")
            raise Exception("Error :: Invalid Status code for Custom App Bypass Deletion!")
        
    


################################################################################################ Monika END #####################################################################################################

################################################  ######## Mridul ########  ###########################################################
    
    ######  QA-164225 start  #######



    def getSyncInfo(self      # Function to check API response of get sync info
                    ):        # Display Info of last Sync Details
        """                   
        Target: To get latest group-Sync Info on MA
        Work Flow
        ----------
            1. Auth to MA
            2. Click https://mobile.{}.net/webservice/api/web/setSyncInfo
        """
        #self.Definition.Get_Sync_Info_Header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        self.Get_Sync_Info_Response = self.REQ(request_type="get",url=self.Definition.Get_Sync_Info_URL,headers=self.Definition.Get_Sync_Info_Header)

        ## Validate response ##
        if self.Get_Sync_Info_Response.status_code==200:
            self._Logger.info("\n\n Group Sync Info Fetch - success ")
            self._Logger.info("\n"+str(self.Get_Sync_Info_Response.json()))
        else: 
            self._Logger.error("Sync Info fetching in MA didnt go well!")
            raise Exception("Error :: Sync Info fetching in MA didnt go well!")


    #-------------------------------------------------------------------------------------------------------------------------------------

    def getPartnerUserByCompany(self,            # Function to Get All Device ID having Partner Login 
                    save_to_temp=True):          # (bool)    if True, save ID to a tempfile
        """
        Target: Get Details of Partner Login Devices
        Work Flow
        ----------
            1. Auth to MA
            2. Go to Enrolled Devices 
            2. Click Partner Devices https://mobileadmin.{}.net/webservice/api/web/partnerUsersByCompany?page=1&pageSize=100&search=     
        """
        userPartnerDeviceList=[]
        #self.Definition.Partner_User_By_Company_Header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        #partnerUsersList = requests.get(self.Definition.Partner_User_By_Company_URL,headers=self.Definition.Partner_User_By_Company_Header)     ## add url and header
        
        
        partnerUsersList = self.REQ(request_type="get",url=self.Definition.Partner_User_By_Company_URL,headers=self.Definition.Partner_User_By_Company_Header)     ## add url and header
        
        if partnerUsersList.status_code==200:
            self._Logger.info("Got users list from MA")
            for userDict in partnerUsersList.json():
                self._Logger.info(f"USER FOUND : {userDict['loginName']} iD : {userDict['id']}")
                userPartnerDeviceList.append(userDict['id'])
            self._Logger.info(f"Users IDs are : {userPartnerDeviceList}")

            if save_to_temp:
                self._Logger.info("making directory {}".format(self.temp_path))
                if not os.path.exists(self.temp_path):
                    os.makedirs(self.temp_path)
                    print("directory made")
                _file = open(self.temp_file, "a")
                _file.write("Partner Devices ID \n")
                _file.write(str(userPartnerDeviceList))
                _file.write("\n")
                print("file written")
                _file.close()

        else:
            raise Exception(f"ERROR :: Invalid status code for getting Partner users list : {partnerUsersList.status_code}")
        

    #-----------------------------------------------------------------------------------------------------------------------------------------------------------

    def getUserByCompany(self,
            save_to_temp=True):
        """
        Target: Get Details and ID of Registered Devices
        Work Flow
        ----------
            1. Auth to MA
            2. Go to Enrolled Devices 
            2. Click Partner Devices https://mobileadmin.{}.net/webservice/api/web/partnerUsersByCompany?page=1&pageSize=100&search=     
        """
        registeredUserList=[]
        #self.Definition.User_By_Company_Header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        #userByCompany=requests.get(self.Definition.User_By_Company_URL,headers=self.Definition.User_By_Company_Header)
        userByCompany=self.REQ(request_type="get",url=self.Definition.User_By_Company_URL,headers=self.Definition.User_By_Company_Header)
        if userByCompany.status_code==200:
            self._Logger.info("Got Registered users list from MA")
            for userDict in userByCompany.json():
                self._Logger.info(f"USER FOUND : {userDict['loginName']} iD : {userDict['id']}")
                registeredUserList.append(userDict['id'])
            self._Logger.info(f"Users IDs are : {registeredUserList}")

            if save_to_temp:
                self._Logger.info("making directory {}".format(self.temp_path))
                if not os.path.exists(self.temp_path):
                    os.makedirs(self.temp_path)
                    print("directory made")
                _file = open(self.temp_file, "a")
                _file.write("Registered Devices ID \n")
                _file.write(str(registeredUserList))
                _file.write("\n")
                print("file written")
                _file.close()
        else:
            raise Exception(f"ERROR :: Invalid status code for getting Partner users list : {userByCompany.status_code}")
        
    # ------------------------------------------------------------------------------------------------------------------------------------------

    def getGroupsByCompany(self,save_to_temp=True):
        """
        Target: Get Details of all app profiles using some groups 
        Work Flow
        ----------
            1. Auth to MA
            2. Go to App profiles.      
        """
        appProfilesGroups=[]
        #self.Definition.Groups_By_Company_Header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        #groupsByCompany=requests.get(self.Definition.Groups_By_Comapny_URL,headers=self.Definition.Groups_By_Company_Header)
        groupsByCompany=self.REQ(request_type="get",url=self.Definition.Groups_By_Comapny_URL,headers=self.Definition.Groups_By_Company_Header)
        if groupsByCompany.status_code==200:
            self._Logger.info("Got Registered users list from MA")
            for userDict in groupsByCompany.json():
                self._Logger.info(f"USER FOUND : {userDict['name']} iD : {userDict['id']}")
                appProfilesGroups.append(userDict['id'])
            self._Logger.info(f"Users IDs are : {appProfilesGroups}")
        
            if save_to_temp:
                self._Logger.info("making directory {}".format(self.temp_path))
                if not os.path.exists(self.temp_path):
                    os.makedirs(self.temp_path)
                    print("directory made")
                _file = open(self.temp_file, "a")
                _file.write("App Profile ID using groups  \n")
                _file.write(str(appProfilesGroups))
                _file.write("\n")
                print("file written")
                _file.close()
        else:
            raise Exception(f"ERROR :: Invalid status code for getting Partner users list : {groupsByCompany.status_code}")


    #---------------------------------------------------------------------------------------------------------------------------------------------------

    def getAuditLogsUrl(self):
        """
        Target: Get Audit Logs url form MA 
        Work Flow
        ----------
        1. Auth to MA 
        2. Go to Audit logs
        3. Export Audit Logs 
        """
        #self.Definition.Get_Audit_logs_URL_Header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        #AuditLogsUrl_get=requests.get(self.Definition.Get_Audit_Logs_URL,headers=self.Definition.Get_Audit_logs_URL_header)
        AuditLogsUrl_get=self.REQ(request_type="get",url=self.Definition.Get_Audit_Logs_URL,headers=self.Definition.Get_Audit_logs_URL_header)

        if AuditLogsUrl_get.status_code==200:
            print("\n\n Get Audit Logs URL - success ")
            self._Logger.info("Get Audit Logs URL - success")
        else:
            self._Logger.error("Not Able to get Audit Logs URL from MA")
            raise Exception("Error :: Not Able to get Audit Logs URL from MA (status_code  {AuditLogsUrl.status_code})")


    # ------------------------------------------------------------------------------------------------------------------------------------------------

    def getGroupEntitlementEnabled(self):
        """
        Target: Get details of ZPA entitlement from MA  
        Work Flow
        ----------
        1. Auth to MA 
        2. Enable/Disable ZPA entitlement form MA 
        """
        groupsZPAEnabled=[]
        #self.Definition.Get_ZPA_Entitlement_header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        #groupEntitlementEnabled=requests.get(self.Definition.Get_ZPA_Entitlement_URL,headers=self.Definition.Get_ZPA_Entitlement_header)
        groupEntitlementEnabled=self.REQ(request_type="get",url=self.Definition.Get_ZPA_Entitlement_URL,headers=self.Definition.Get_ZPA_Entitlement_header)
        if groupEntitlementEnabled.status_code==200:
            self._Logger.info("ZPA Group Entitlement Details - success")
            userDict=groupEntitlementEnabled.json()
            if userDict['zpaEnableForAll']==1:
                self._Logger.info("ZPA is enabled for ALL")
            else:
                for groups in userDict['groupList']:
                    self._Logger.info(f"USER FOUND : {groups['name']} iD : {groups['id']}")
                    groupsZPAEnabled.append(groups['id'])
            self._Logger.info(f"Users IDs are : {groupsZPAEnabled}")            

        else:
            self._Logger.error("Get ZPA group Entitlement failed")
            raise Exception("Get ZPA group Entitlement failed")  

    # -------------------------------------------------------------------------------------------------------------------------------------------------
    
      
    def getGroupEntitlemnetEnabledZdx(self):
        """
        Target: Get Details of ZDX Entitlement from MA 
        ----------
        1. Auth to MA 
        2. Enable/Disable ZDX entitlement form MA 
        """
        groupsZDXEnabled=[]
        #self.Definition.Get_ZDX_Entitlement_header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        #groupEntitlementEnabledZdx=requests.get(self.Definition.Get_ZDX_Entitlement_URL,headers=self.Definition.Get_ZDX_Entitlement_header)
        groupEntitlementEnabledZdx=self.REQ(request_type="get",url=self.Definition.Get_ZDX_Entitlement_URL,headers=self.Definition.Get_ZDX_Entitlement_header)

        if groupEntitlementEnabledZdx.status_code==200:
            self._Logger.info("ZDX Group Entitlement Details - success")
            userDict=groupEntitlementEnabledZdx.json()
            if userDict['upmEnableForAll']==1:                             ####zdx zdx enabled for all
                self._Logger.info("ZDX is enabled for ALL")
            else:
                for groups in userDict['upmGroupList']:
                    self._Logger.info(f"USER FOUND : {groups['name']} iD : {groups['id']}")
                    groupsZDXEnabled.append(groups['id'])
            self._Logger.info(f"Users IDs are : {groupsZDXEnabled}")            

        else:
            self._Logger.error("Get ZDX group Entitlement failed")
            raise Exception("Get ZDX group Entitlement failed") 

    # -------------------------------------------------------------------------------------------------------------------------------------------------
    
    def getZiaGroupEntitlementEnabled(self):
        """
        Target: Get Details of ZDX Entitlement from MA 
        ----------
        1. Auth to MA 
        2. Enable/Disable ZIA entitlement form MA 
        """
        groupsZIAEnabled=[]
        #self.Definition.Get_ZIA_Entitlement_header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        #groupEntitlementEnabledZia=requests.get(self.Definition.Get_ZIA_Entitlement_URL,headers=self.Definition.Get_ZIA_Entitlement_header)
        groupEntitlementEnabledZia=self.REQ(request_type="get",url=self.Definition.Get_ZIA_Entitlement_URL,headers=self.Definition.Get_ZIA_Entitlement_header)

        if groupEntitlementEnabledZia.status_code==200:
            self._Logger.info("ZIA Group Entitlement Details - success")
            userDict=groupEntitlementEnabledZia.json()
            if userDict['proxyEnableForAll']==1:                             ####zia enable check
                self._Logger.info("ZIA is enabled for ALL")
            else:
                for groups in userDict['ziaDeviceGroupList']:
                    self._Logger.info(f"USER FOUND : {groups['name']} iD : {groups['id']}")
                    groupsZIAEnabled.append(groups['id'])
                self._Logger.info(f"Users IDs are : {groupsZIAEnabled}")            

        else:
            self._Logger.error("Get ZIA group Entitlement failed")
            raise Exception("Get ZIA group Entitlement failed")


    # ---------------------------------------------------------------------------------------------------------------------------------------------------
    
    # THIS ONLY WORKS IF POSTURE BASED SERVICE IS ENABLED FOR ORG

    def Toggle_ZIA(self,        # Function to Toggle ZIA from MA
    action=True):               # (bool)    True turns on ZIA, False turns off
        """ 
        Work Flow 
        ----------
            1.  Get payloads from Definitions. get Auth Token
            2.  Set 1/0 accordingly to Enable/Disable ZPA
            3.  send post request. validate response is 200
        """
        # self.Definition.ZPA_HEADER['auth-token']=self.Authenticate_To_Mobile_Admin()
        self.Definition.ZIA_PAYLOAD["enableForAll"]= (1 if action==True else 0)
        self._Logger.info(f"{str(self.Definition.ZIA_HEADER)},{str(self.Definition.ZIA_PAYLOAD)},{str(self.Definition.ZIA_URL)}")
        Profile = self.REQ(request_type="post", url=self.Definition.ZIA_URL, headers=self.Definition.ZIA_HEADER, data=json.dumps(self.Definition.ZIA_PAYLOAD))
        # profile = requests.post(url=self.Definition.ZPA_URL, headers=self.Definition.ZPA_HEADER, data=json.dumps(self.Definition.ZPA_PAYLOAD))
        # Validate success
        if Profile.status_code == 200: self._Logger.info("ZIA status: "+str("On" if action else "Off")+" response:"+str(Profile.json()))
        else: 
            self._Logger.error("Error :: Toggle ZIA in MA didnt go well!")
            raise Exception("Error :: Toggle ZIA in MA didnt go well!")




    # ---------------------------------------------------------------------------------------------------------------------------------------------------
    ######  QA-164225 Complete  #######


    #----------------------------------------------------------------------------------------------------------------------------------------

    ######  QA-164227  start   #########

    ## should I add it to a temp file ??
    ## use post to change values and then run get to check if it is working
    def getDeviceCleanUpInfo(self):
        """
        Target: Get Device CleanUP info form MA  
        Work Flow
        ----------
        1. Auth to MA 
        2. Go to Administration tab -> Client Connector Support -> Device Cleanup tab 
        """
        infoDeviceCleanUp=[]
        #self.Definition.Get_DeviceCleanUpInfo_header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        #deviceCleanUpInfo=requests.get(self.Definition.Get_DeviceCleanUpInfo_URL,headers=self.Definition.Get_DeviceCleanUpInfo_header)
        deviceCleanUpInfo=self.REQ(request_type="get",url=self.Definition.Get_DeviceCleanUpInfo_URL,headers=self.Definition.Get_DeviceCleanUpInfo_header)

        if deviceCleanUpInfo.status_code==200:
            self._Logger.info("Get Device CleanUp info - success")
            infoDeviceCleanUp=deviceCleanUpInfo.json()
            for values in infoDeviceCleanUp:
                self._Logger.info(f"{values}:{infoDeviceCleanUp[values]}")
            
        else:
            self._Logger.error("Get Device Clean Up info Failed")
            raise Exception("Get Device Clean Up info Failed") 

        return infoDeviceCleanUp

    #----------------------------------------------------------------------------------------------------------------------------------------

    def getDeviceGroupListByCompany(self,
                        save_to_temp=True):
        """
        Target: Get count and  Details of all Device Groups Present on MA
        Work Flow
        ----------
            1. Auth to MA
            2. Go to Administration -> device Groups. 
        """
        deviceGroups=[]
        #self.Definition.Get_DeviceGroup_ListByCompany_header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        #deviceGroupsByCompany=requests.get(self.Definition.Get_DeviceGroup_ListByCompany_URL,headers=self.Definition.Get_DeviceGroup_ListByCompany_header)
        deviceGroupsByCompany=self.REQ(request_type="get",url=self.Definition.Get_DeviceGroup_ListByCompany_URL,headers=self.Definition.Get_DeviceGroup_ListByCompany_header)
        
        if deviceGroupsByCompany.status_code==200:
            self._Logger.info("Got Device Groups from MA")
            deviceGroups=deviceGroupsByCompany.json()
            self._Logger.info(F"Total Number of Groups : {deviceGroups['totalCount']}")

            groupsName=[]
            # capture all Device groups name and id
            for groups in deviceGroups['deviceGroups']:
                self._Logger.info(f"Groups FOUND : {groups['name']} iD : {groups['id']}")
                groupsName.append({groups['name'],groups['id']})

            if save_to_temp:
                self._Logger.info("making directory {}".format(self.temp_path))
                if not os.path.exists(self.temp_path):
                    os.makedirs(self.temp_path)
                    print("directory made")
                _file = open(self.temp_file, "a")
                _file.write("Device Groups name and ID  \n")
                _file.write(str(groupsName))
                _file.write("\n")

                print("file written")
                _file.close()
        else:
            self._Logger.error("Get Device Grup List by Company did not work")
            raise Exception(f"ERROR :: Invalid status code for getting Partner users list : {deviceGroupsByCompany.status_code}") 


    # --------------------------------------------------------------------------------------------------------------------------------------------

    def getEndpointIntegrationInfo(self):          # this function returns custom vpn so that we can verify post request for endPoint integration works        
        """                                        
        Target: Get Endpoint Integration info from MA 
        Work Flow
        ----------
            1. Auth to MA
            2. Go to Administration -> Clent Connector support -> EndPoint Integration. 
        """
        endPointDetails=[]
        #self.Definition.Get_EndpointIntregration_Info_header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        #endPointIntegrationInfo=requests.get(self.Definition.Get_EndpointIntregration_Info_URL,headers=self.Definition.Get_EndpointIntregration_Info_header)
        endPointIntegrationInfo=self.REQ(request_type="get",url=self.Definition.Get_EndpointIntregration_Info_URL,headers=self.Definition.Get_EndpointIntregration_Info_header)

        if endPointIntegrationInfo.status_code==200:
            self._Logger.info("Got EndPoint integration info from MA")
            endPointDetails=endPointIntegrationInfo.json()

            customVpn=endPointDetails["customVpnTrustedNetworkAdapters"]
        
        else:
            line = "Response code from request is {}, request not made!".format(endPointIntegrationInfo.status_code)
            self._Logger.error(line)
            raise Exception(line)

        return customVpn
    
    # --------------------------------------------------------------------------------------------------------------------------------------------

    def setEndpointIntegrationInfo(self): 
        """                                        
        Target: Set Endpoint Integration info from MA , add a customVpnTrustedNetworkAdapters and test if etEndpointIntegrationInfo requests works
        Work Flow
        ----------
            1. Auth to MA
            2. Go to Administration -> Clent Connector support -> EndPoint Integration. 
            3. Set customVpnTrustedNetworkAdapters value with 'automation+str(time)' 
            4. Run a get endpoint integration info function and verify if new customVpnTrustedNetworkAdapters is added
        """
        customVpnValue="automation"+str(datetime.now())
        #self.Definition.Set_EndpointIntregration_Info_header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        id=self.getIDEndPoint()
        self.EndPointPayload={
            "id": id,
            "firefoxIntegrationEnabled": "0",
            "tunnelTCPLocalPort": "9000",
            "customVpnTrustedNetworkAdapters": customVpnValue,
            "defaultVpnTrustedNetworkAdapters": "Cisco,Juniper,Fortinet,PanGP,Check Point,VPN",
            "defaultVpnServices": "pulsesecureservice.exe,vpnagent.exe,pangpa.exe,vpn_proxyserver.exe,tracsrvwrapper.exe,openvpnconnect.exe,fortisslvpndaemon.exe,bigipedgeclient.exe",
            "dropNonZscalerPacket": 1
        }
        #setEndPointIntegrationInfo=requests.post(self.Definition.Set_EndpointIntregration_Info_URL,headers=self.Definition.Set_EndpointIntregration_Info_header,data=json.dumps(self.EndPointPayload))
        setEndPointIntegrationInfo=self.REQ(request_type="post",url=self.Definition.Set_EndpointIntregration_Info_URL,headers=self.Definition.Set_EndpointIntregration_Info_header,data=json.dumps(self.EndPointPayload))
        if setEndPointIntegrationInfo.status_code==200:
            newCustomVpnValue=self.getEndpointIntegrationInfo()
            flagTest=1
            if newCustomVpnValue==customVpnValue:
                self._Logger.info("Set Endpoint Integration Info -- success")
            else:
                self._Logger.error("Not able to add custom vpn Trusted Network Adapters")
                raise Exception("Error :: New added custom vpn Trusted Network Adapters is not updated")
            
        else:
            self._Logger.error("Set Endpoint Integration info did not work")
            raise Exception("Error :: Set Endpoint Integration info did not work")
        
    # --------------------------------------------------------------------------------------------------------------------------------------------

    def getFailOpenListByCompany(self):   # this function also returns current info  
                                          # mainly to test edit fail open policy
        """
        Target: return id of Fail Open Policy 
        Work Flow
        ----------
            1. Auth to MA
            2. Go to Administration -> Client Connector Support.
            3. Go to App Fail Open and get Details of policy
        """
        #self.Definition.self.Get_AppFailOpen_Header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        #appFailOpen=requests.get(self.Definition.self.Get_AppFailOpen_URL,headers=self.Definition.self.Get_AppFailOpen_Header)
        appFailOpen=self.REQ(request_type="get",url=self.Definition.Get_AppFailOpen_URL,headers=self.Definition.Get_AppFailOpen_Header)
        
        info=[]
        if appFailOpen.status_code==200:
            self._Logger.info("Got App Fail open info from MA -- Success")
            info=appFailOpen.json()
            #self._Logger.info(F"ID : {info['id']}")
        

        else:
            self._Logger.error("App fail open list by compnay did not work")
            raise Exception(f"ERROR :: App fail open list by compnay did not work : {appFailOpen.status_code}")
        
        return info
    
    # --------------------------------------------------------------------------------------------------------------------------------------------

    def editFailOpenPolicy(self): ## should all values be changed to verify api working
        """
        Target: edit Fail open Policy on MA 
        Work Flow
        ----------
            1. Auth to MA
            2. Go to Administration -> Client Connector Support.
            3. Go to App Fail Open and edit policy 
        """       
        #self.Definition.self.edit_AppFailOpen_Header["auth-token"] = self.Authenticate_To_Mobile_Admin()    

        """
        get info 
        compare info and add different values in policy captivePortalWebSecDisableMinutes
        change value edit policy
        get info again
        compare id and verify captivePortalWebSecDisableMinutes value is changed or not 
        """

        self.currentPolicyInfo=self.getFailOpenListByCompany()[0]
        
        currentDisableMinutes=self.currentPolicyInfo["captivePortalWebSecDisableMinutes"]
        policyId=self.currentPolicyInfo['id']
        newDisableMinutes=0
        if currentDisableMinutes > 0 or currentDisableMinutes < 60:
            newDisableMinutes=currentDisableMinutes+1
        elif currentDisableMinutes==0:
            newDisableMinutes=10
        else:   newDisableMinutes=20
        # Editing the payload with only new "captivePortalWebSecDisableMinutes" value
        self.failOpenPolicyPayload={
            "captivePortalWebSecDisableMinutes":newDisableMinutes,
            "id":policyId,
            "active":self.currentPolicyInfo['active'],
            "enableWebSecOnProxyUnreachable":self.currentPolicyInfo['enableWebSecOnProxyUnreachable'],
            "enableWebSecOnTunnelFailure":self.currentPolicyInfo['enableWebSecOnTunnelFailure'],
            "tunnelFailureRetryCount":self.currentPolicyInfo['tunnelFailureRetryCount']
            }
        #editFailopenPolicy=requests.post(self.Definition.edit_AppFailOpen_URL,headers=self.Definition.edit_AppFailOpen_Header,data=json.dumps(self.failOpenPolicyPayload))
        editFailopenPolicy=self.REQ(request_type="post",url=self.Definition.edit_AppFailOpen_URL,headers=self.Definition.edit_AppFailOpen_Header,data=json.dumps(self.failOpenPolicyPayload))
        
        if editFailopenPolicy.status_code==200:
            updatePolicyInfo=self.getFailOpenListByCompany()[0]
            updatedDisableMinutes=updatePolicyInfo["captivePortalWebSecDisableMinutes"]
            if policyId==updatePolicyInfo['id'] and updatedDisableMinutes==newDisableMinutes:
                self._Logger.info("Edit Fail open Policy --- Success")
            else:
                self._Logger.error("Edit Fail open Policy value did not update")
                raise Exception("Error :: Fail open Policy value did not update after editing")
        else:
            self._Logger.error("Edit fail open Policy did not work")
            raise Exception("Error ::Update Fail open Policy did not work")

    # --------------------------------------------------------------------------------------------------------------------------------------------

    def getMachinetunnelList(self,
                             save_to_temp=True):
        """
        Target: Get Machine Tunnel List from MA 
        Work Flow
        ----------
            1. Auth to MA
            2. Go to Enrolled Devices -> Machine Tunnel tab.
        """       
        #self.Definition.self.get_MacineTunnel_List_Header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        #machineTunnelList=requests.get(self.Definition.get_MachineTunnel_List_URL,headers=self.Definition.get_MacineTunnel_List_Header)
        
        machineTunnelList=self.REQ(request_type="get",url=self.Definition.get_MachineTunnel_List_URL,headers=self.Definition.get_MacineTunnel_List_Header)
        machineDetails=[]
        if machineTunnelList.status_code==200:
            self._Logger.info("Got Machine Tunnel Status from MA")
            machineDetails=machineTunnelList.json()
            self._Logger.info(F"Total Count: {machineDetails['totalCount']}")

            # capture all Machine tunnel id
            tunnelId=[] 
            for id in machineDetails['machineTunnelDeviceContracts']:
                tunnelId.append(id['machineTunnelDevId'])

            if save_to_temp:
                    self._Logger.info("making directory {}".format(self.temp_path))
                    if not os.path.exists(self.temp_path):
                        os.makedirs(self.temp_path)
                        print("directory made")
                    _file = open(self.temp_file, "a")
                    _file.write("Machine Tunnel Dev Id  \n")
                    _file.write(str(tunnelId))
                    _file.write("\n")
                    print("file written")
                    _file.close()

        else:
            self._Logger.error("Get Machine Tunnel List by Company did not work")
            raise Exception(f"ERROR :: Invalid status code for getting Machine Tunnel list : {machineTunnelList.status_code}")   


    # --------------------------------------------------------------------------------------------------------------------------------------------

    def getAppProfileWithMachineToken(self,
                            save_to_temp=True):
        """
        Target: Get App Profile id using Machine token 
        Work Flow
        ----------
            1. Auth to MA
            2. Go to windows App Profiles.
        """
        #self.Definition.self.get_machineToken_List_header["auth-token"] = self.Authenticate_To_Mobile_Admin()
        #machineTokenList=requests.get(self.Definition.get_machineToken_List_URL,headers=self.Definition.get_machineToken_List_header)
        machineTokenList=self.REQ(request_type="get",url=self.Definition.get_machineToken_List_URL,headers=self.Definition.get_machineToken_List_header)
        
        machineTokenProfiles=[]
        if machineTokenList.status_code==200:
            self._Logger.info("Got App Profiles where Machine Token is selected from MA")
            machineTokenProfiles=machineTokenList.json()

            appProfileDetails=[]
            for id in machineTokenProfiles['list']:
                self._Logger.info(F"App Profile name {id['name']} and ID {id['id']}")
                appProfileDetails.append({id['name'],id['id']})
            
            if save_to_temp:
                self._Logger.info("making directory {}".format(self.temp_path))
                if not os.path.exists(self.temp_path):
                    os.makedirs(self.temp_path)
                    print("directory made")
                _file = open(self.temp_file, "a")
                _file.write("App Profile using Machine Token name and ID \n")
                _file.write(str(appProfileDetails))
                _file.write("\n")
                print("file written")
                _file.close()
            
        else:
            self._Logger.error("Get App Profile list using  Machine Token did not work")
            raise Exception(f"ERROR :: Invalid status code for getting App Profile with Machine Token list : {machineTokenList.status_code}")


    # --------------------------------------------------------------------------------------------------------------------------------------------

    def getDeviceGroup(self,
                       save_to_temp=True):
       """
        Target: Get device Groups info from MA  
        Work Flow
        ----------
            1. Auth to MA
            2. Go to Administration -> Zscaler Service Entitlement.
        """

       deviceGroups=[]
       deviceGroupInfo = self.REQ(request_type='get',url=self.Definition.get_device_groups_info_URL,headers=self.Definition.get_device_groups_header)
       
       if deviceGroupInfo.status_code==200:
            groupid=[]
            self._Logger.info("Got Device Groups info From MA")
            deviceGroups=deviceGroupInfo.json()
            self._Logger.info('Device Groups Count ' + str(deviceGroups['totalCount']))
            for group in deviceGroups['deviceGroups']:
                    self._Logger.info(f"Group Name : {group['name']} iD : {group['id']}")
                    groupid.append(group['id'])
            
            if save_to_temp:
                self._Logger.info("making directory {}".format(self.temp_path))
                if not os.path.exists(self.temp_path):
                    os.makedirs(self.temp_path)
                    print("directory made")
                _file = open(self.temp_file, "a")
                _file.write("Device Groups ID \n")
                _file.write(str(groupid))
                _file.write("\n")
                print("file written")
                _file.close()

       else:
            self._Logger.error("Get Device Groups Info from MA did not go well")
            raise Exception(f"ERROR :: Invalid status code for getting Device Groups Info : {deviceGroupInfo.status_code}")
       

    # --------------------------------------------------------------------------------------------------------------------------------------------


    def setDeviceCleanupInfo(self):               # (bool)    True turns on ZIA, False turns off
        """ 
        Work Flow 
        ----------
            1.  Get payloads from Definitions. get Auth Token
            2.  Chcek The values currently by using getDecvice Cleanup info 
            3.  Enable/Disable Force Remove Oldest Device After User Enrolls value accordingly
            3.  send post request. validate response is 200
            4.  Confirm values are updated after post request using getDevice Cleanup info function
        """
        currentInfo=self.getDeviceCleanUpInfo()
        if currentInfo == []:
            self._Logger.error("Get Device cleanup info did not go well")
            raise Exception("ERROR :: Invalid status code for getting Device Groups Info")
        cleanupStatus=currentInfo['forceRemoveType']
        enabled=0
        if cleanupStatus == '0':
            cleanupStatus = 1
            enabled=1
        else:
            cleanupStatus = 0

        set_deviceCleanup_PAYLOAD={
            "id": currentInfo['id'],
            "active": currentInfo['active'],
            "forceRemoveType": cleanupStatus,
            "deviceExceedLimit": currentInfo['deviceExceedLimit'],
            "autoRemovalDays": currentInfo['autoRemovalDays'],
            "autoPurgeDays": currentInfo['autoPurgeDays']
        }

        setDevieCleanUp=self.REQ(request_type='post',url=self.Definition.set_deviceCleanup_URL,headers=self.Definition.set_deviceCleanup_header,data=json.dumps(set_deviceCleanup_PAYLOAD))
        status_set='Disabled'
        if enabled == 1:
            status_set='Enabled'

        if setDevieCleanUp.status_code ==200:
            self._Logger.info("Set Device Clean Up info --- Success")
            self._Logger.info("Force Remove Oldest Device After User Enrolls is " + status_set)

            # Get funtion to confirm value is updated
            newDeviceCleanUpInfo=self.getDeviceCleanUpInfo()
            if newDeviceCleanUpInfo['forceRemoveType'] != cleanupStatus:
                self._Logger.info("Force Remove Oldest Device After User Enrolls value is successfully updated")
            else:
                self._Logger.error("Force Remove Oldest Device After User Enrolls value is NOT updated")
                raise Exception("Set Device Cleanup Info did not go well")
        else:
            self._Logger.error("Set Device Cleanup Info did not go well")
            raise Exception(f"ERROR :: Invalid status code for Updating Device Groups Info : {setDevieCleanUp.status_code}")
        
    # -------------------------------------------------------------------------------
    def Get_ZDP_Rule_List(
        self,
        Get_ID_By_Rule_Name: [str] = None
    ) -> list:
        """
        Target: to get list of zdp rules from zia
        WorkFlow
            * The Get_ZDP_Rule_List method is used to retrieve a list of ZDP rules from the Zscaler Internet Access (ZIA) cloud service. 
            * The method takes an optional parameter Get_ID_By_Rule_Name which can be used to retrieve the ID of a specific rule by its name.
            * The Get_ZDP_Rule_List method returns a list of dictionaries, where each dictionary represents a ZDP rule. 
            * The dictionaries contain information about the rule, such as its name, ID, and action. 
            * If the Get_ID_By_Rule_Name parameter is specified, the method will return the ID of the rule with the specified name.
            * If no rule with the specified name is found, the method will return None.
        """
        Timestamp = str(int(round(time.time() * 1000)))
        Url = copy.deepcopy(self.Definition.Get_ZDP_Rule_URL).replace("TIME_STAMP_HERE", str(Timestamp))

        self.Definition.Create_ZDP_Rule_Header['Cookie'], self.Definition.Create_ZDP_Rule_Header[
            'ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()

        ZDP_Rule_list_fetch = self.REQ(request_type='get',url=Url,headers=self.Definition.Create_ZDP_Rule_Header, ZIA_Request=True)
        
        if ZDP_Rule_list_fetch.status_code==200:
            self._Logger.info("get ZDP Rule list --- Success")
        else:
            self._Logger.error("get ZDP Rule list --- Failure")
            raise Exception("get ZDP Rule list --- Failure")
        
        if Get_ID_By_Rule_Name:
            for each_rule_dict in ZDP_Rule_list_fetch.json():
                if each_rule_dict["name"] == Get_ID_By_Rule_Name:
                    return each_rule_dict["id"]
        else:
            return ZDP_Rule_list_fetch.json()

        self._Logger.error("fetch ZDP Rule list--- Failure")
        raise Exception("fetch ZDP Rule list--- Failure")

    
    # -----------------------------------------------------------------------------------------------
    def Delete_ZDP_Rule(
        self,
        Rule_Name: str = None
    ):
        """
        Target: Deletes a ZDP rule from the Zscaler Internet Access instance.

        Args:
        Rule_Name (str, optional): The name of the rule to delete. If not provided, the ID of the rule must be provided.

        Raises:
        Exception: If the deletion of the ZDP rule fails.
        """
        # Get the ID of the rule to delete
        Id = self.Get_ZDP_Rule_List(Get_ID_By_Rule_Name=Rule_Name)

        # Construct the URL for the delete request
        Url = copy.deepcopy(self.Definition.Delete_ZDP_Rule_URL).replace("RULE_ID_HERE", str(Id))

        # Authenticate to ZIA and get the cookie and custom code
        self.Definition.Create_ZDP_Rule_Header['Cookie'], self.Definition.Create_ZDP_Rule_Header[
            'ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()

        # Make the delete request
        ZDP_Rule_delete = self.REQ(request_type='delete',url=Url,headers=self.Definition.Create_ZDP_Rule_Header, ZIA_Request=True)
        
        # Check the status code of the response
        if ZDP_Rule_delete.status_code>200 and ZDP_Rule_delete.status_code<300:
            self._Logger.info("Delete ZDP Rule --- Success")
        else:
            self._Logger.error("Delete ZDP Rule --- Failure")
            raise Exception("Delete ZDP Rule --- Failure")

    
    # -----------------------------------------------------------------------------------------------
    def Create_ZDP_Rule(
        self,
        rule_name: str = "Automation_Rule",       # (str)     descripttion for the rule
        action: str = "ALLOW",                              # (str)     takes (ALLOW/BLOCK/CONFIRM)
        state: bool = True,                                 # (bool)    take True for "ENABLED" and False for "Disabled"
        rule_order: int = 1,                                # (int)     integer specifying rule order
        description: str = "Automation_Rule",               # (str)     descripttion for the rule
        rule_severity: str = "HIGH",                        # (str)     rule severity specify (HIGH/MEDIUM/LOW/INFORMATION)
        data_transfer_method: str = None,                   # (str)     carefully provide this
        content_matching: bool = False,                     # (bool)    dont enable it unless u know it and mean it
        mode: str = "create"                                # (str)     takes "create" for creating, "edit" for edit
    ) -> None:
        """
        Target: to create/edit ZDP rules in ZIA
        WorkFlow:
            * The code defines a function called Create_ZDP_Rule that creates a DLP rule in the Zscaler Internet Access (ZIA) platform. 
            * The function takes several parameters, including the rule name, action, state, rule order, description, rule severity, data transfer method, and content matching. 
            * The function also has a mode parameter that can be set to "create" or "edit".
            * The function first checks if the mode is set to "edit". If it is, it retrieves the ID of the rule to be edited using the Get_ZDP_Rule_List function and sets the ID in the payload. 
            * If the mode is set to "create", it sets the URL to the ZIA API endpoint for creating a new DLP rule.
            * The function then sets the payload for the API request based on the input parameters. 
            * It sets the action, state, severity, dataTransferMethod, order, name, and description fields in the payload. 
            * If the content_matching parameter is set to True, it sets the withoutContentInspection field to False.
            * Finally, the function makes an API request to ZIA using the REQ function, passing in the payload and headers. 
            * If the request is successful, it logs a success message and returns the response. 
            * If the request fails, it logs an error message and raises an exception.
        """
        if mode=="edit":
            Id = self.Get_ZDP_Rule_List(Get_ID_By_Rule_Name=rule_name)
            self.Definition.Create_ZDP_Rule_Payload["id"] = int(Id)
            Url = copy.deepcopy(self.Definition.Edit_ZDP_Rule_URL).replace("RULE_ID_HERE", str(Id))
        else:
            Url = copy.deepcopy(self.Definition.Create_ZDP_Rule_URL)

        if action in ["ALLOW", "BLOCK", "CONFIRM"]:
            self.Definition.Create_ZDP_Rule_Payload["action"] = action
        else:
            raise Exception("Invalid options for DLP rule creation, select in  (ALLOW/BLOCK/CONFIRM)")
        self.Definition.Create_ZDP_Rule_Payload["state"] = ("ENABLED" if True else "DISABLED")

        if rule_severity in ["HIGH", "MEDIUM", "LOW", "INFORMATION"]:
            self.Definition.Create_ZDP_Rule_Payload["severity"] = "RULE_SEVERITY_{}".format(rule_severity)
        else:
            raise Exception('Invalid options for DLP rule creation, select in  ["HIGH", "MEDIUM", "LOW", "INFORMATION"]')
        self.Definition.Create_ZDP_Rule_Payload["dataTransferMethod"] = data_transfer_method
        self.Definition.Create_ZDP_Rule_Payload["order"] = int(rule_order)
        self.Definition.Create_ZDP_Rule_Payload["name"] = str(rule_name)
        self.Definition.Create_ZDP_Rule_Payload["withoutContentInspection"] = (True if not content_matching else False)
        self.Definition.Create_ZDP_Rule_Payload["description"] = str(description)

        self.Definition.Create_ZDP_Rule_Header['Cookie'], self.Definition.Create_ZDP_Rule_Header[
            'ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        
        if mode=="create":
            ZDP_Rule_create = self.REQ(request_type='post',url=Url,headers=self.Definition.Create_ZDP_Rule_Header, data=json.dumps(self.Definition.Create_ZDP_Rule_Payload), ZIA_Request=True)
        else:
            ZDP_Rule_create = self.REQ(request_type='put',url=Url,headers=self.Definition.Create_ZDP_Rule_Header, data=json.dumps(self.Definition.Create_ZDP_Rule_Payload), ZIA_Request=True)

        if ZDP_Rule_create.status_code==200:
            self._Logger.info("Create ZDP Rule --- Success")
        else:
            self._Logger.error("Create ZDP Rule --- Failure")
            raise Exception("Create ZDP Rule --- Failure")
    
    # -------------------------------------------------------------------------------------------------------------------------
    def Create_Custom_Pac_File(self,
                               pacName = "Automation_Created_Pac",
                               privateIPBypass=True,
                               dnsResolution=True,
                               plainHostnameRule=True,
                               ftpBypass=True,
                               ZPASyntheticRangeBypass=True,
                               trustURLBypass=True,
                               customDefaultReturnStatementFlag=False,
                               customDefaultReturnStatement="",
                               appProfilePac=True,
                               localProxyPac=False,
                               proxyRulesList=[],
                               variableList=[],
                               variableBasedRulesList=[],
                               returnPacURL=False,
                               obfuscateURL=True
                               ):
        """

        Args:
            privateIPBypass (bool, optional): To add Private IP Bypass Default Rule . Defaults to True.
            dnsResolution (bool, optional): To add DNS Resolution Default Rule. Defaults to True.
            plainHostnameRule (bool, optional): to add Defaul Plain Hostname Rule. Defaults to True.
            ftpBypass (bool, optional): to add default ftp bypass rule. Defaults to True.
            ZPASyntheticRangeBypass (bool, optional): to add default ZPA Bypass rule. Defaults to True.
            trustURLBypass (bool, optional): to add default trust bypass rule. Defaults to True.
            customDefaultReturnStatementFlag (bool, optional): to add add custom default return statement. Defaults to False.
            customDefaultReturnStatement (str, optional): Custom Default return statment will be added if above flag is true. Defaults to "".
            appProfilePac (bool, optional): If you need to create App Profile Pac. Defaults to True.
            localProxyPac (bool, optional): If you need to create . Defaults to False.
            proxyRulesList (list of dict, optional): FORMAT: [{"domain":"domain name", "action": "DIRECT/Some Proxy statement"}]. Right now it uses shExpMatch and will add custom bypass rules as asked by user,  Defaults to [].
            variableList (list of dict , optional): FORMAT: [{"name":"variable name", "value": "custom value"}]
            variableBasedRulesList(list of dict, optional): FORMAT:  variableBasedRulesList=[{'variableName':'tunnel2', 'operator':'==', 'variableValue':'true', 'returnStatement':'PROXY *************:80'}]). This is for using new Macros for DPPC Support for T2 and Trusted network macros.

        Working:
            The predefined pac function is broken into 7 Parts, Out of which 6 are option and customDefaultReturn Statement is must.

            1)When you want to make custom pac file and pass custom rule then you need to pass List of Dictionaries which contains Rules and Action Type.
            Example -->proxyRulesList=[{"domain":"*zoom.us", "action":"PROXY *************:80"},{"domain":"example.com", "action":"DIRECT"}, {"domain":"sbi.com", "action":" PROXY ${ZAPP_LOCAL_PROXY}"},{"domain":"*zoom.us", "action":"PROXY *************:80", "pacFunction":"shExpMatch"},{"domain":"*zoom.us", "action":"PROXY *************:80", "pacFunction":"isPlainHostName"},{"domain":"*zoom.us", "action":"PROXY *************:80", "pacFunction":"dnsDomainIs"},{"domain":"*zoom.us", "action":"PROXY *************:80", "pacFunction":"isResolvable"},{"domain":"*zoom.us", "action":"PROXY *************:80","pacFunction":"dnsResolve"},{"ipAddress":"*******", "action":"PROXY *************:80", "pacFunction":"isInNet", "netmask":"***********"},{"domain":"zoom.us", "action":"PROXY *************:80", "pacFunction":"isInNetEx", "ipPrefix":"64::ff9b/96"} ]
            By Default shExpMatch Function is used for everything.
            Above string converts to

             if(shExpMatch(host,"*zoom.us"))
		        return "PROXY *************:80";

            if(shExpMatch(host,"example.com"))
                return "DIRECT";

            if(shExpMatch(host,"sbi.com"))
                return " PROXY ${ZAPP_LOCAL_PROXY}";

            if(shExpMatch(host,"*zoom.us"))
                return "PROXY *************:80";

            if(isPlainHostName("*zoom.us"))
                return "PROXY *************:80";

            if(dnsDomainIs(host, "*zoom.us"))
                return "PROXY *************:80";

            if(isResolvable("*zoom.us"))
                return "PROXY *************:80";

            if(dnsResolve("*zoom.us"))
                return "PROXY *************:80";

            if(isInNet(host, "*******", "***********"))
                return "PROXY *************:80";

            if(isInNetEx(host, "64::ff9b/96"))
                return "PROXY *************:80";

            **** Do Not give RETURN Keyword in the action.
            **** Keys are case senstive.

            2)If user needs to add custom variable such as "tunnel2" and "network" you can create those variables using "variableList".

            Example --> variableList=[{'name':'tunnel2', 'value':'${ZT2_REQUEST}'}, {'name':'network', 'value':'${TRUSTED_NETWORK}'}] Will convert to:

                	var tunnel2 = "${ZT2_REQUEST}";

	                var network = "${TRUSTED_NETWORK}";

            3) If user needs to create custom bypass rule based on variables define above then they needs to use "variableBasedRulesList"

            Example --> variableBasedRulesList=[{'variableName':'tunnel2', 'operator':'==', 'variableValue':'true', 'returnStatement':'PROXY *************:80'}], this will convert to this:
                if( tunnel2 == "true")
		        return "PROXY *************:80";


            4)IF you want to make Local Proxy Pac then  set localProxyPac=True and appProfilePac=False. and if you don't give customDefaultReturnStatement and customDefaultReturnStatementFlag=True then it will use this " return "PROXY ${ZAPP_LOCAL_PROXY}";"

            5)IF you want to create a App Profile Pac then send appProfilePac=True. Default is True only. Custom Return statement can be passed. Default Return statement will be

            6) This Function inturns call self.Create_Pac_File to create Pac.

            ***** Documentation for pac Functions is here:
            ===> IPv4 --> https://developer.mozilla.org/en-US/docs/Web/HTTP/Proxy_servers_and_tunneling/Proxy_Auto-Configuration_PAC_file
            ===> IPv6 --> https://bits.netbeans.org/dev/javadoc/org-netbeans-core-network/org/netbeans/core/network/proxy/pac/PacHelperMethodsMicrosoft.html#isInNetEx-java.lang.String-java.lang.String-
            Returns:
            returns JSON Response Payload and user has to parse the JSON payload to extract URL and send it in MA API to add the URL
        """
        temp_pac_list = []
        final_pac_string = ""

        function_heading = "\nfunction FindProxyForURL(url, host) {\n\n"

        function_ending = "\n}"

        private_ip_regex = "\t var privateIP = /^(0|10|127|192\\.168|172\\.1[6789]|172\\.2[0-9]|172\\.3[01]|169\\.254|192\\.88\\.99)\\.[0-9.]+$/;\n\n"

        dns_resolution_rule = "\t var resolved_ip = dnsResolve(host);\n\n"

        plain_hostname_rule = "\t if (isPlainHostName(host) || isInNet(resolved_ip, \"*********\",\"*************\") || privateIP.test(resolved_ip))\n\t\treturn \"DIRECT\";\n\n"

        ftp_bypass_rule = "\t if (url.substring(0,4) == \"ftp:\")\n\t\treturn \"DIRECT\";\n\t\t\t\n"

        zpa_range_bypass_rule = "\t if (isInNet(resolved_ip, \"**********\",\"***********\"))\n\t\treturn \"DIRECT\";\n\t\t\t\n"

        trust_bypass_rule = "\t if (\n\t\t((localHostOrDomainIs(host, \"trust.zscaler.com\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zscaler.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zscalerone.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zscalertwo.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zscalerthree.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zscalergov.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zsdemo.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zscloud.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zsfalcon.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zdxcloud.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zdxpreview.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zdxbeta.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zsdevel.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zsbetagov.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zspreview.net\")) ||\n\t\t(localHostOrDomainIs(host, \"trust.zscalerten.net\")) || \n\t\t(localHostOrDomainIs(host, \"trust.zdxten.net\")) ) &&\n\t\t(url.substring(0,5) == \"http:\" || url.substring(0,6) == \"https:\")\n\t\t)\n\t\treturn \"DIRECT\";\n\n"

        default_proxy_string = "\t return \"PROXY ${GATEWAY_FX}:80; PROXY ${SECONDARY_GATEWAY_FX}:80; DIRECT\";\n"

        default_local_proxy_pac_string = "\t return \"PROXY ${ZAPP_LOCAL_PROXY}\";"

        self._Logger.info("Starting Custom Pac Creation, Appending Function Start String")

        temp_pac_list.append(function_heading)

        if len(variableList) > 0:
            self._Logger.info("Into Variable List Parsing")

            temp_variable_defination = ""
            for index in range(0, len(variableList)):
                temp_variable_defination += "\t  var {} = \"{}\";\n\n".format(variableList[index]['name'],
                                                                              variableList[index]['value'])

            temp_pac_list.append(temp_variable_defination)

            self._Logger.info(
                "Variable List Parsing Complete this is the string appended:\n  {}".format(temp_variable_defination))

        if privateIPBypass:
            self._Logger.info("Appending Private IP Bypass String")
            temp_pac_list.append(private_ip_regex)

        elif privateIPBypass == False:
            self._Logger.info(
                "Private IP Bypass Rule is set to False, modifying plain_hostname_rule to remove Private IP Substring")
            plain_hostname_rule = plain_hostname_rule.replace(" || privateIP.test(resolved_ip))", "")

        if dnsResolution:
            self._Logger.info("Appending DNS Resolution String")
            temp_pac_list.append(dns_resolution_rule)

        if plainHostnameRule:
            self._Logger.info("Appending Plain Hostname String")
            temp_pac_list.append(plain_hostname_rule)

        if ftpBypass:
            self._Logger.info("Appending FTP Bypass String")
            temp_pac_list.append(ftp_bypass_rule)

        if ZPASyntheticRangeBypass:
            self._Logger.info("Appending ZPA Syncthetic Bypass String")
            temp_pac_list.append(zpa_range_bypass_rule)

        if trustURLBypass:
            self._Logger.info("Appending Trust URL Bypass String")
            temp_pac_list.append(trust_bypass_rule)

        if len(variableBasedRulesList) > 0:
            self._Logger.info("Into Varibale Based Rules List Parsing")
            temp_variable_based_rule = ""
            for index in range(0, len(variableBasedRulesList)):
                temp_variable_based_rule += "\t if( {} {} \"{}\")\n\t\t return \"{}\";\n\n".format(
                    variableBasedRulesList[index]['variableName'], variableBasedRulesList[index]['operator'],
                    variableBasedRulesList[index]['variableValue'], variableBasedRulesList[index]['returnStatement'])
            temp_pac_list.append(temp_variable_based_rule)
            self._Logger.info("Variable Based Rules List parsing completed this is the string appended:\n {}".format(
                temp_variable_based_rule))

        if len(proxyRulesList) > 0:
            self._Logger.info("Into Proxy Rules List Parsing")
            temp_rule = ""
            for index in range(0, len(proxyRulesList)):
                temp_rule += self.Pac_Helper_Function(proxyRulesList[index])
            temp_pac_list.append(temp_rule)
            self._Logger.info("Proxy Rules List Parsing completed this is the string appened:\n {}".format(temp_rule))

        if appProfilePac:
            self._Logger.info("Appending App Pac Default Proxy Return Statement")
            if customDefaultReturnStatementFlag:
                default_proxy_string = "\t return \"{}\"; \n".format(customDefaultReturnStatement)
                self._Logger.info("Appending Custom App Pac Default Return Statement, this is the string {}".format(
                    default_proxy_string))
            temp_pac_list.append(default_proxy_string)

        if localProxyPac:
            self._Logger.info("Appending Local Proxy Pac Default Proxy Return Statement")
            if customDefaultReturnStatementFlag:
                default_local_proxy_pac_string = "\t return \"{}\"; \n".format(default_local_proxy_pac_string)
                self._Logger.info(
                    "Appending Custom Local Proxy Pac Default Return Statement, this is the string {}".format(
                        default_local_proxy_pac_string))
            temp_pac_list.append(default_local_proxy_pac_string)

        self._Logger.info("Apending Funnction End Braces")
        temp_pac_list.append(function_ending)

        self._Logger.info("Creating Final Pac String")
        for element in temp_pac_list:
            final_pac_string += element

        self._Logger.info("Final Proxy String is this: {}".format(final_pac_string))

        self._Logger.info("Calling Create_Pac_File")

        pacResponse = self.Create_Pac_File(name=pacName, pacContent=final_pac_string, returnData=True, obfuscateURL=obfuscateURL)

        self._Logger.info("Dumping Pac Response: \n {}".format(pacResponse))
        if returnPacURL:
            return pacResponse["pacUrl"]
            self._Logger.info("Pac URL is: {}".format(pacResponse['pacUrl']))

        else:
            return pacResponse

    def Pac_Helper_Function(self,
                            proxyRuleDict={}
                            ):
        temp_rule = ""

        self._Logger.info("Into Pac_Function_helper")

        if 'pacFunction' not in proxyRuleDict or proxyRuleDict['pacFunction'] == "shExpMatch":
            temp_rule = "\t if(shExpMatch(host,\"{}\"))\n\t\t return \"{}\";\n\n".format(proxyRuleDict["domain"],
                                                                                         proxyRuleDict["action"])

        elif proxyRuleDict['pacFunction'] == "isPlainHostName":
            temp_rule = "\t if(isPlainHostName(\"{}\"))\n\t\t return \"{}\";\n\n".format(proxyRuleDict['domain'],
                                                                                         proxyRuleDict['action'])

        elif proxyRuleDict['pacFunction'] == "dnsDomainIs":
            temp_rule = "\t if(dnsDomainIs(host, \"{}\"))\n\t\t return \"{}\";\n\n".format(proxyRuleDict['domain'],
                                                                                           proxyRuleDict['action'])

        elif proxyRuleDict['pacFunction'] == "isResolvable":
            temp_rule = "\t if(isResolvable(\"{}\"))\n\t\t return \"{}\";\n\n".format(proxyRuleDict['domain'],
                                                                                      proxyRuleDict['action'])

        elif proxyRuleDict['pacFunction'] == "isInNet":
            temp_rule = "\t if(isInNet(host, \"{}\", \"{}\"))\n\t\t return \"{}\";\n\n".format(
                proxyRuleDict['ipAddress'], proxyRuleDict['netmask'], proxyRuleDict['action'])

        elif proxyRuleDict['pacFunction'] == "dnsResolve":
            temp_rule = "\t if(dnsResolve(\"{}\"))\n\t\t return \"{}\";\n\n".format(proxyRuleDict['domain'],
                                                                                    proxyRuleDict['action'])

        elif proxyRuleDict['pacFunction'] == "dnsResolveEx":
            temp_rule = "\t if(dnsResolveEx(\"{}\"))\n\t\t return \"{}\";\n\n".format(proxyRuleDict['domain'],
                                                                                      proxyRuleDict['action'])

        elif proxyRuleDict['pacFunction'] == "isInNetEx":
            temp_rule = "\t if(isInNetEx(host, \"{}\"))\n\t\t return \"{}\";\n\n".format(proxyRuleDict['ipPrefix'],
                                                                                         proxyRuleDict['action'])

        elif proxyRuleDict['pacFunction'] == "isResolvableEx":
            temp_rule = "\t if(isResolvableEx(\"{}\"))\n\t\t return \"{}\";\n\n".format(proxyRuleDict['domain'],
                                                                                        proxyRuleDict['action'])

        else:
            self._Logger.error("Function Requested is not implemented / Supported.")
            raise Exception("{} Function is not supported/Included as of now.".format(proxyRuleDict))

        return temp_rule

    def Create_Pac_File(self,
                        name="Automation_Created_Pac",  # (str) Name of pac
                        description="Automation_Created_Pac",  # (str) Description of the pac
                        pacCommitMessage="Automation_Created_Pac",  # (str) Commit message while creating pac
                        pacContent=None,  # (str) Pac content in string format
                        primaryProxy=None,
                        # (str) Primary Proxy/SME IP in string format, give default if no IP is to be given
                        secondaryProxy=None,
                        # (str) Secindary Proxy/SME IP in string format, give default if no IP is to be given
                        returnData=False,  # (bool) Defines whether to return complete response of api call
                        obfuscateURL=True # (bool) Set Obfuscate URL
                        ):
        """
        Target: Creates PAC File on ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. Add given input to payload
            3. If pac content is given,it will be added to the payload as it is
            4. If not, then primary and secondary proxies are to be given, if no IP is to be given, give default
            3. Make Api call and returns response if required
        """
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS[
            'ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()
        self._Logger.info(f"Create pac file :: Primary Proxy :: {primaryProxy} Secondary Proxy :: {secondaryProxy}")
        if primaryProxy == None and secondaryProxy == None and pacContent == None:
            raise Exception(
                'ERROR :: Either give complete pac content or give primary and secondary proxies, Nothing given !')
        pacPayload = copy.deepcopy(self.Definition.Create_Pac_File_Payload)
        timeStamp = str(time.time())
        if name != "Automation_Created_Pac":
            pacPayload['name'] = name
        else:
            pacPayload['name'] = name + '_' + timeStamp
        pacPayload['description'] = description + '_' + timeStamp
        pacPayload['pacCommitMessage'] = pacCommitMessage + '_' + timeStamp
        pacPayload['domain'] = self.Config['ZIA_USER_ID'].split('@')[-1]
        pacPayload['pacUrlObfuscated'] = obfuscateURL
        if pacContent:
            pacPayload['pacContent'] = pacContent
        else:
            if primaryProxy.lower() == 'default':
                self._Logger.info(r"Default primary proxy :: ${GATEWAY_FX}:80")
            else:
                pacPayload['pacContent'].replace(r'${GATEWAY_FX}', primaryProxy)
            if secondaryProxy.lower() == 'default':
                self._Logger.info(r'Default secondary proxy :: ${SECONDARY_GATEWAY_FX}:80')
            else:
                pacPayload['pacContent'].replace(r'${SECONDARY_GATEWAY_FX}', secondaryProxy)



        self._Logger.info(
            f"PAC URL :: {self.Definition.Create_Pac_File_Url}\n\nPAC Payload ::\n{json.dumps(pacPayload)}\nPAC HEADERS :: {self.Definition.ZIA_HEADERS}")

        createPac = self.REQ("post", url=self.Definition.Create_Pac_File_Url, headers=self.Definition.ZIA_HEADERS,
                             data=json.dumps(pacPayload), ZIA_Request=True)
        self._Logger.info(f"Create Pac Status Code :: {createPac.status_code}")
        if createPac.status_code == 200:
            self.pacResponse = createPac.json()
            self.ZIA_Activate()
            if returnData: return self.pacResponse
        else:
            raise Exception(f"Invalid status code for pac creation :: {createPac.status_code}")

    def Delete_Pac_File(self,
                        id=None,  # (str/int) ID of the pac to be deleted
                        name=None  # (str) Name of pac to be deleted
                        ):
        """
        Target: Deletes given pac file from ZIA
        Work Flow
        ----------
            1. Auth to ZIA
            2. If ID is given, add it to delete url and make api call
            3. If self.pacResponse variable exists, pick id from there and add it to delete url and make api call
            4. If Name is given, get list of pacs on ZIA, find the pac with given name, pick its ID, add to delete url and make api call
        """
        self._Logger.info(f"Delete pac file :: {name}")
        url = self.Definition.Delete_Pac_File_Url
        self.Definition.ZIA_HEADERS['Cookie'], self.Definition.ZIA_HEADERS[
            'ZS_CUSTOM_CODE'] = self.Authenticate_to_ZIA()

        if id:
            self._Logger.info(f"Got createdPacID from id vairable :: {id} ")
            url += str(id)
        elif hasattr(self, 'pacResponse') and 'id' in self.pacResponse.keys() and self.pacResponse['id'] > 0 and \
                self.pacResponse['name'] == name:
            self._Logger.info(f"Got createdPacID from self.pacResponse :: {self.pacResponse['id']} ")
            url += str(self.pacResponse['id'])
        else:
            if name == None:
                raise Exception("Name or ID of pac to be deleted not give, please given either one of these")
            else:
                pacFiles = self.Get_Pac_Files_List()
                isPacFound = False
                for pacFile in pacFiles:
                    if pacFile['name'] == name:
                        self._Logger.info(f"{name} found on ZIA, ID is :: {str(pacFile['id'])}")
                        isPacFound = True
                        url += str(pacFile['id'])
                if isPacFound == False: raise Exception(f"ERROR :: {name} not found on ZIA")
        deletePac = self.REQ("delete", url, headers=self.Definition.ZIA_HEADERS, ZIA_Request=True)
        if deletePac.status_code == 204:
            self._Logger.info(f"{name} pac file deleted !!!")
            self.ZIA_Activate()
        else:
            raise Exception(f"Invalid status code for delete pac file :: {deletePac.status_code}")

    # -------------------------------------------------------------------------------------------------------

    def Get_Pac_Url(self, name):
        pacList = self.Get_Pac_Files_List()
        pacUrl = [pac['pacUrl'] for pac in pacList if pac['name'] == name][0]
        return pacUrl

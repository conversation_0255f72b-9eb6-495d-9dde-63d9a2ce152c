import os
import sys
import json
current = os.path.dirname(os.path.realpath(__file__))
parent = os.path.dirname(current)
gparent = os.path.dirname(parent)
# sys.path.append(gparent)
# sys.path.append(gparent + "/Config/")

class ZDX_RES:
    def __init__(self, cloudname, Variable=None):
    #----------------------------------Auth to ZDX-Admin------------------------------------------
        self.var = Variable if Variable else None
        self.domain = ""
        if "." in cloudname: self.domain = cloudname
        else: self.domain = f"admin.{cloudname}.net"
        self.authurl = f"https://{self.domain}/zdx/api/v1/auth"
        self.authheader = { #only meant to be used for fetching x-csrf-token before login, else session header is enough when session is active
           "Content-Type": "application/json",
           "X-Csrf-Token": "Fetch",
           "Sec-Fetch-Site": "same-origin"}
        self.authtokenPath = os.path.join(self.var.automationloc,"Tokens") if self.var else os.path.join(os.path.dirname(os.path.realpath(__file__)), "Tokens")
        self.authtokenFile = os.path.join(self.authtokenPath, "ZDX_ADMIN_AUTH.txt")
        
    #-----------------------------------Activate-------------------------------------------------
        self.activateurl = f"https://{self.domain}/zdx/api/v1/activate"
    #--------------------------------Applications---------------------------------------------------
        self.applicationurl = f"https://{self.domain}/zdx/api/v1/applications"
        self.createApppayload = {"name": "","active":"false","type":"CUSTOM"}
    
    #--------------------------------List all monitors---------------------------------------------------
        self.listallmonitorsurl = f"https://{self.domain}/zdx/api/v1/monitors/summary"
    #--------------------------------Probe---------------------------------------------------------
        self.monitorurl = "https://{}/zdx/api/v1/applications/{}/monitors".format(self.domain,"{}")

        #-----------------------------web probe------------------------------------

        self.webprobepayload = {"frequencyInSeconds":300,"name":"","active":"true","type":"WEB","onUsers":[],"onGroups":[],"onLocations":[],"onLocationGroups":[],"onDepartments":[],"onDevices":[],"excludedUsers":[],"excludedGroups":[],"excludedLocations":[],"excludedDepartments":[],"excludedDevices":[],"class":"CUSTOM","expiresAtTimestamp":0,"url":"","requestMethods":["GET"],"requestBody":"","requestHeaders":[],"retries":1,"requestTimeoutSeconds":60,"followRedirects":"true","maxRedirects":5,"responseCodes":["200-299","304"],"responseHeaders":[],"responseBody":"","timeoutSeconds":60}
        self.tracertpayload = {"reverseLookupEnabled": "false","frequencyInSeconds":300,"class":"CUSTOM","name":"","type":"TRACERT","active":"true","expiresAtTimestamp":0,"onUsers":[],"excludedUsers":[],"onGroups":[],"excludedGroups":[],"onLocations":[],"onLocationGroups":[],"excludedLocations":[],"onDevices":[],"excludedDevices":[],"onDepartments":[],"excludedDepartments":[],"requestTimeoutSeconds":60,"maxPacketCount":5,"followWebMonitor":{},"packetIntervalMs":1000,"packetTimeoutMs":1000,"host":""}
        self.disablewebprobe = {"id":"","type":"WEB","active":"false", "class":"CUSTOM", "name":"", "url":""}
    
    #-------------------------------------List all users-----------------------------------------------------
        self.allusersurl = f"https://{self.domain}/zdx/api/v1/users"

    #-------------------------------------List all devices of specific user----------------------------------------
        self.devicesurl = "https://{}/zdx/api/v1/users/{}/devices".format(self.domain, "{}")

    #-------------------------------------resource for DT---------------------------------------
        self.dtapppayload = {
                            "type": "APPLICATIONS",
                            "deviceMonitoringEnabled": "true",
                            "application": {
                              "id": "",
                              "name": ""
                            },
                            "monitors": [
                              {
                                "id": "",
                                "name": "",
                                "type": "WEB",
                              },
                              {
                                "id": "",
                                "name": "",
                                "type": "TRACERT",
                              }
                            ]
                        }
        self.dtpcappayload={
                          "type": "PCAP",
                          "pcapFilterExpression": "",
                          "packetSizeInBytes": 1514,
                          "interfaces": []
                        }
    #--------------------------------CREATE DT SESSION----------------------------------------------------
        self.dturl = f"https://{self.domain}/zdx/api/v1/tracing"
        self.isactiveparams = "?state=STARTED&state=IN_PROGRESS&state=CREATED&sortOn=createdAtTimestamp&sortOrder=desc"
        self.iscompletedparams = "?state=COMPLETED&sortOn=createdAtTimestamp&sortOrder=desc"
        self.isfailedparams = "?state=ABORT_INITIATED&state=ABORTED&state=EXPIRED&state=FAILED&state=INCOMPLETE&sortOn=createdAtTimestamp&sortOrder=desc"
    #--------------------------------SSIT Notifiction List -----------------------------------------------
        self.selfserviceurl = f"https://{self.domain}/zdx/api/v1/selfService"
        self.ssitreporturl = "https://{}/zdx/api/v1/reports?widgetId={}".format(self.domain, "{}")
        self.ssitpayload = {
            "pageid": "SSIT_NOTIF_DASHBOARD",
            "widgetid": "SSIT_NOTIF_LIST",
            "filters": {
                "time": {
                    "range": [
                        [
                            1715596800,
                            1715598600
                        ]
                    ]
                }
            },
            "drilldownv2": [
                {
                    "view": "zdx_ssit_device_events_view",
                    "fields": [
                        "user_id",
                        "device_id",
                        "evt_name",
                        "evt_stime",
                        "evt_feedback"
                    ]
                }
            ],
            "sessionId": "OuTPfRzNkoQyG0pqPypyu"
        }
        
         #-- Inventory Setting--------------------------------
        self.toggle_collect_inventory_data_url = f"https://{self.domain}/zdx/api/v1/deviceInventory"

import allure
import time
import pytest
from OS_Windows.library.ui_zcc import Zcc

class UI_UTIL(Zcc):
    def __init__(self, Loggers, Variables) -> None:
        self.Loggers = Loggers
        self.Variables = Variables
        super().__init__(log_handle = self.Loggers)

    @allure.step("Check if service is running, if running provides service status")
    def check_service_state(self,service:str,retry:int,boolean:bool=True):
        """
        This function checks the zdx service for the given number of times passed in retry paramter
        :param self: The instance of the class.
        :param service: service name
        :param retry: number of time function should check
        :param boolean: running status

        :return return a tuple
        """
        while True:
            try:
                self.Variables.sysmgmt.get_process_architecture_by_name(process_name="ZSAUpm" if service=="ZDX" else "ZSATunnel")
                status = super().check_service_connected(service=service)
                if not status[0]: raise Exception(status[1])
                break
            except Exception as E:
                self.Loggers.warning(f"{E}\nWaiting for service to connect...")
                time.sleep(7)
                if retry == 0 and not boolean:
                    raise Exception(f"ZDX failed to start itself or failed to establish a successful connection\n{E}", "")
                if retry == 0: return (False, f"ZDX failed to start itself or failed to establish a successful connection\n{E}", "")
                retry -=1
        return (True, "Success", "")
    
    @allure.title("This function check the ZCC and ZDX service")
    def login_and_check_service(self):
        """
        This function checks wheather zcc is login if, not then login it and check if zdx is running 
        :param boolean: running status

        :return return bool 
        """
        isLoggedIn = super().validate_zcc_logged_in()[0]

        if not isLoggedIn:
            try:
                super().login(aup=True, zia_saml_login=True, zia_user=self.Variables.config['zdx']["ZIA_USER_ID"], zia_password=self.Variables.config['zdx']["ZIA_USER_PASSWORD"], zpa_user=self.Variables.config['zdx']["ZPA_USER_ID"], zpa_password=self.Variables.config['zdx']["ZPA_USER_PASSWORD"])
                time.sleep(30)
                isLoggedIn = super().validate_zcc_logged_in()[0]
            except Exception as e: 
                self.Loggers.error(str(e))
                return (False,f"Error :: Failed to verify ZCC login:: {e}\n".format(e),"")

        if isLoggedIn:
            status, exception, _ = self.check_service_state(service="ZDX",retry = 3)
            if not status: self.Loggers.error(f"{exception}"); pytest.fail(f"{exception}")
            return (True,"Success", "")
        else:
            return (False, "Failed", "")
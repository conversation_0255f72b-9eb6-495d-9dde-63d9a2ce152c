

################################################################
#               DOCUMENTATION FOR LOG_OPS.PY                   #
#                                                              #
# AUTHOR : SRINIVASA BHASWANTH (<EMAIL>)        #
################################################################
__author__ = '{srinivasa bhaswanth}'
__credits__ = ['{srinivasa bhaswanth, abhimanyu sharma, sahil kumar}']
__email__ = '{<EMAIL>}'

#####################################################
import os, sqlite3
import copy
import time
import datetime
from datetime import date, datetime, timedelta
from zipfile import ZipFile
import logging
import zipfile
import re
import subprocess
import platform

from Lib.common_lib import Constants

# get to know the operating system
if platform.uname()[0] == 'Windows':
    try:
        check_android=subprocess.run("adb devices", capture_output=True)
        check_android_result=str(check_android.stdout)
        android_udid=check_android_result.split("\\r\\n")[1].split("\\t")[0]
        if len(android_udid)<=0:
            raise Exception
        os_version = "Android"
    except:
        os_version = "Windows"
        import winreg
elif platform.uname()[0] == 'Darwin':
    os_version = "MAC"
else:
    os_version = "Windows"
    print("Warning: OS version cannot be determined")




##############################################################################################################################################################

# ------------------------------------------------------------------------------------------------------------------------------------------------------
class File:
    def __init__(self, 
        name: str, 
        path: str, 
        regex_pattern: str = None
    ):
        self.name = name
        self.path = path
        self.regex_pattern = regex_pattern
    
# ------------------------------------------------------------------------------------------------------------------------------------------------------
log_res = {
        "ZSATunnel" : File(
            name = "ZSATunnel", 
            path = Constants.Utils.log_path, 
            regex_pattern = r'ZSATunnel_\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}.\d{6}.log$' ),
        "ZSAService" : File(
            name = "ZSAService", 
            path = Constants.Utils.log_path, 
            regex_pattern = r'ZSAService_\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}.\d{6}.log$' ),
        "ZSATray" : File(
            name = "ZSATray", 
            path = Constants.Utils.tray_log_path, 
            regex_pattern = r'ZSATray_\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}.\d{6}.log$' ),
        "ZSATrayManager" : File(
            name = "ZSATrayManager", 
            path = Constants.Utils.log_path, 
            regex_pattern = r'ZSATrayManager_\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}.\d{6}.log$' ),

        "ZCC_Android": File(
            name = "ZCC_Android",
            path = Constants.Utils.log_path,
            regex_pattern = r'ZCC_Android_\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}.\d{6}.log$'),

        "AppInfo" : File(
            name = "AppInfo",
            path = Constants.Utils.log_path,
            regex_pattern = r'AppInfo_\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}.\d{6}.log$'),

        "Zscaler-Android-AppInfo": File(
            name = "Zscaler-Android-AppInfo",
            path = Constants.Utils.log_path,
            regex_pattern = r'Zscaler-Android-AppInfo_\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}.\d{6}.log$'),


        "Zscaler-Android-Logcat" : File(
            name = "Zscaler-Android-AppInfo",
            path = Constants.Utils.log_path,
            regex_pattern = r'Zscaler-Android-AppInfo_\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}.\d{6}.log$'),    
}

# ------------------------------------------------------------------------------------------------------------------------------------------------------
class log_ops:
    """
    The code is a Python class named `log_ops` that provides functionalities for searching logs in a log file. 
    The class has several methods that work together to perform the log search. 
    
    Here's a breakdown of how the code works:
        1. The class has an `__init__` method that initializes a logger, sets the operating system version, and resets some variables.
        2. The `Initialize_Logger` method is used to initialize a logger for the application. 
           It creates a logger object, sets the log level, and adds console and file handlers to the logger.
        3. The `get_log_line_timestamp` method extracts the timestamp from a log line.
        4. The `reset_variables` method resets the variables in the class to their initial states.
        5. The `ignore_logs_before_this_time_point` method sets the start time attribute for the class instance to the current time. 
           This can be used to ignore log messages that were created before this time point.
        6. The `get_latest_file` method is used to get the latest modified file from a given directory that matches a provided criteria (substring or regex pattern).
        7. The `log_reader` method reads the contents of a log file and returns a list of lines read from the log file.
        8. The `log_finder` method finds log lines within a given list of log lines that match specific conditions, such as a timestamp, line number, or line content. 
           It also allows the search to break after the first hit or to continue searching until all possible hits are found.
        9. The `search_log_file` method is the main method that uses other methods in the class to search a log file for specific words or lines based on 
           different search modes. It first gets the latest log file in the directory, resets some variables, reads the log file, and performs the actual log search 
           using the `log_finder` method. It then logs the result of the search and raises an exception if the log was found when it was not expected, or vice versa. 
           If the log was found successfully, it returns some information about that log line (like line number, timestamp, etc.). If the log was not found, it 
           raises an exception with details about the search.
        
    """
    def __init__(
        self, 
        logger: str = None,
        variable = None,
        console_handle_needed: bool = True
    ):
        self.var = variable if variable else None
        self.logger = (logger if logger else self.Initialize_Logger("log_ops.log", Log_Level = "INFO", console_handle_needed = console_handle_needed))
        self.os_version = os_version
        self.log_res = copy.deepcopy(log_res)
        self.CONST = Constants.Utils
        self.reset_variables()
        self.start_time = None
        self.zcc_version = None
        self.SID = None
        self.Hashed_SID = None
    
    
    def Remove_Console_Handle(self, 
                            logger
        ):
        logger.setLevel(logging.NOTSET)
    
    def Add_Console_Handle(self,
                           logger
                           ):
        logger.setLevel(logging.DEBUG)
        
    
    # --------------------------------------------------------------------------------------------------------------------------
    def Initialize_Logger(self, 
        Log_File_Name: str,                      
        Log_Level: str,                          
        console_handle_needed: bool = True          
    ):
        """
        This function initializes a logger for the application.

        Parameters:
            Log_File_Name (str): The name of the log file to be created.
            Log_Level (str): The level of logging (INFO/DEBUG/WARNING/CRITICAL etc).
            console_handle_needed (bool): Optional, print on command line if True only. Default is True.

        Returns:
            logger: A logger object that can be used to log messages.
            
        Work Flow:
            1. Create a logger object.
            2. Create a console handler if required
                - console handler is used to print_ required info on command line window.
            3. Create a file handler -> mandatory
                - Make sure 'Logs' folder exists, as this file handler will be created in this folder only.
                - file handler is used to send all information to a log file in specified folder.
            4. Set the debug level (INFO/DEBUG/WARNING/CRITICAL etc). Default: INFO
            5. Specify how the logging must happen (with/without datetime/loglevel/username)
            6. Add all created handlers to the logger instance created in step 1.
            7. return the logger instance.
        """
        # 1, 2 & 3
        if self.var: self.Logs_Directory = os.path.join(self.var.automationlogslocation, "Logs")
        else: self.Logs_Directory = os.path.join(os.path.abspath(os.path.dirname(os.path.abspath("trigger_automation_zdx.py"))),"zdx", "Logs")
        logger = logging.getLogger(Log_File_Name)
        if console_handle_needed: console_handler = logging.StreamHandler()
        if not os.path.exists(self.Logs_Directory): os.mkdir(self.Logs_Directory)
        file_handler = logging.FileHandler(os.path.join(self.Logs_Directory, Log_File_Name))

        # 4
        if Log_Level:
            logger.setLevel(eval("logging.{}".format(Log_Level)))
            if console_handle_needed: 
                console_handler.setLevel(eval("logging.{}".format(Log_Level)))
            file_handler.setLevel(eval("logging.{}".format(Log_Level)))

        # 5
        if console_handle_needed: 
            console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))

        # 6
        if console_handle_needed: 
            logger.addHandler(console_handler)
        logger.addHandler(file_handler)
        # 7
        logger.info("If you see this, logger console is ON")

        return logger
    
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def get_log_line_timestamp(self,        
        log_line: str   
    ):                           
        """
        Function to get timestamp of a log line.
        
        Parameters:
            * log_line (str): logline recieved as a string.
        
        Returns:
         * datetime: The absolute timestamp of the log line if the line is in the correct format, None otherwise.
        
        """
        try:
            log_line_pattern = r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}.\d{6})\(([+-]\d{4})\)(\[\d+:\d+\])"
            #                    [------------group1----------------------][----group2---][---group3---]
            #                    [------------timestamp-------------------][---timezone--][----misc----]
            match = re.search(log_line_pattern, log_line)
            # print(f"\nMATCH :: {match}")
            #2023-11-28 10:03:31.552001(-0800)[7056:19012]
            if match:
                time_stamp = match.group(1)
                time_zone = match.group(2)
                direction = time_zone[0]
                hours = int(time_zone[1:3])
                minutes = int(time_zone[3:5])
                if direction == "-":
                    hours *= -1
                    minutes *= -1
                absolute_time_stamp = datetime.strptime(time_stamp, '%Y-%m-%d %H:%M:%S.%f') + timedelta(hours=float(hours),minutes=float(minutes))
                return absolute_time_stamp
            else:
                return None
        except Exception as E:
            print(f"\n EXCEPTION :: {E}")
            return None
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def reset_variables(self):
        """
        Reset the variables in the class to their initial states.

        Attributes:
            * is_found (bool): Indicates if a specific log entry has been found. Default is False.
            * recent_log_line (str): The recent log line read from the log file. Default is None.
            * recent_log_line_number (int): The line number of the recent log line. Default is None.
            * recent_log_timestamp (str): The timestamp of the recent log line. Default is None.
            * recently_opened_file (str): The recently opened log file. Default is None.
            * recent_log_line_number_bottom (int): The line number of the recent log line from the bottom of the file. Default is None.
            * list_of_hits_so_far (list): A list of hits found so far. Default is an empty list.
        """
        self.is_found=False
        self.recent_log_line=None
        self.recent_log_line_number=None
        self.recent_log_timestamp=None
        self.recently_opened_file=None
        self.recent_log_line_number_bottom=None
        self.list_of_hits_so_far=[]

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def ignore_logs_before_this_time_point(self):
        """
        This method sets the start time attribute for the class instance.

        The start time is set to be the current time when this method is called.
        This can be used to ignore log messages that were created before this time point.

        :param self: The class instance
        :return: None
        """
        self.start_time = datetime.now()

	# ------------------------------------------------------------------------------------------------------------------------------------------------------
    def get_latest_file(self,
        directory: str = None,
        file_sub_string: str = None,                          
        file_regex_pattern: str = None,
        search_file = "latest"
    ):
        """
        This function is used to get the latest modified file from a given directory.
        
        Parameters:
            * directory (str): The directory path from where to search for the files. Default is None.
            * file_sub_string (str): A substring that should be present in the file name. Default is None.
            * file_regex_pattern (str): A regex pattern to match the file name. Default is None.
            
        Returns:
            * str: The latest modified file from the given directory that matches the provided criteria.
        
        Raises:
            * Exception: If the provided directory does not exist.
            * Exception: If neither regex_pattern nor file_sub_string is provided.
            * Exception: If provided regex pattern is invalid.
            * Exception: If no files are found that match the provided criteria.
        
        Example Usage:
            * latest_file = get_latest_file(directory='/path/to/directory', file_sub_string='example')
        """
        list_of_files = None
        latest_file = None

        if not os.path.exists(directory):
            self.logger.error("Error :: Provided directory doesnt exist")
            raise Exception("Error :: Provided directory doesnt exist")
        
        if not any([file_regex_pattern, file_sub_string]):
            self.logger.error("Error :: Please provide either regex pattern or file_sub_string")
            raise Exception("Error :: Please provide either regex pattern or file_sub_string")
        
        if file_regex_pattern and not re.compile(file_regex_pattern):
            self.logger.error("Error :: Invalid regex pattern")
            raise Exception("Error :: Invalid regex pattern")

        if file_regex_pattern:
            list_of_files = [each_file for each_file in sorted(os.listdir(directory), reverse=True) if re.search(file_regex_pattern, each_file)]
        elif file_sub_string:
            list_of_files = [each_file for each_file in sorted(os.listdir(directory), reverse=True) if file_sub_string in each_file]
            
        if len(list_of_files) == 0:
            self.logger.error("Error :: No file found with required keywords")
            raise Exception("Error :: No file found with required keywords")
        else:
            self.logger.info("Number of files found in {0} with provided matching criteria: {1}".format(directory, len(list_of_files)))
            latest_file = list_of_files[0] if search_file=="latest" else list_of_files[-1]
            #latest_file = max(list_of_files,key=os.path.getmtime)
            self.logger.info("Latest file (sorted based on time latest edited): {}".format(latest_file))
            return latest_file
    
    # ----- read part ----------------------------------------------------------------------------------------
    def log_reader(self, 
        log_file_full_path: str
    ):
        """
        This function reads the contents of a log file.

        Parameters:
            * self (object): The instance of the class.
            * log_file_full_path (str): The full path of the log file to be read.

        Returns:
            * list: A list of lines read from the log file.
        """
        #print(f"LOG FILE :: {log_file_full_path}\n\n")
        with open(log_file_full_path,'rb') as log_file_object:
            log_file_contents = log_file_object.readlines()
        self.logger.info("Log file Read success - {0}, looking from the {1}".format(log_file_full_path, "top"))

        if type(log_file_contents[0])==bytes:
            log_file_contents=[each.decode(errors='ignore') for each in log_file_contents]
        return log_file_contents
    
    # ----- find part ---------------------------------------------------------------------------------
    def log_finder(self, 
        log_file: str,
        log_contents: list,
        words_to_find_in_line,
        log_regex_pattern: str,
        start_timestamp: str,
        start_line_number: int,
        start_line: str,
        end_timestamp: str,
        end_line_number: int,
        end_line: str,
        break_after_first_hit: bool = True
    ):
        
        """
        Find log lines within a given list of log lines that match specific conditions.

        Parameters:
            * log_contents (list): List of log lines to search through.
            * start_timestamp (int): Start searching after this timestamp.
            * start_line_number (int): Start searching after this line number.
            * start_line (str): Start searching after this line.
            * end_timestamp (int): Stop searching after this timestamp.
            * end_line_number (int): Stop searching after this line number.
            * end_line (str): Stop searching after this line.
            * break_after_first_hit (bool): If True, stop searching after the first match is found.
            * words_to_find_in_line (list): List of words that must all be present in a log line for it to be considered a match.
            
        Returns: None

        WorkFlow:
            * The function `log_finder` is used to find specific log lines within a given list of log lines (`log_contents`). 
            * The search can be narrowed down by specifying start and end conditions such as a timestamp, line number, or line content. 
            * The search can also be set to break after the first hit or to continue searching until all possible hits are found.
        """
        for each_log_line_number, each_log_line in enumerate(log_contents):
            #----start filters beg----------------------------------------
            each_log_timestamp = self.get_log_line_timestamp(each_log_line)
            if start_timestamp and each_log_timestamp and each_log_timestamp < start_timestamp: 
                self.logger.debug("continued in start_timestamp {}".format(each_log_timestamp))
                continue
            if start_line_number and each_log_line_number < start_line_number: 
                self.logger.debug("continued in start_linenumber {}".format(each_log_line_number))
                continue
            if start_line and (start_line in log_contents) and each_log_line_number < log_contents.index(start_line): 
                self.logger.debug("continued in start_line {}".format(each_log_line))
                continue
            #----start filters end----------------------------------------
            
            if not log_regex_pattern:

                if all(word in each_log_line for word in words_to_find_in_line):
                    self.is_found = True
            else:
                self.is_found = re.search(log_regex_pattern, each_log_line)

            if self.is_found:
                self.recent_log_line = copy.deepcopy(each_log_line)
                self.recent_log_line_number = copy.deepcopy(each_log_line_number)
                self.recent_log_timestamp = copy.deepcopy(each_log_timestamp)
                self.list_of_hits_so_far.append(copy.deepcopy(each_log_line))
                self.recently_opened_file = copy.deepcopy(log_file)
                if break_after_first_hit: 
                    break

            #----end filters beg----------------------------------------
            if end_timestamp and each_log_timestamp > end_timestamp: 
                self.logger.debug("broken in end_timestamp {}".format(each_log_timestamp))
                break
            if end_line_number and each_log_line_number > end_line_number: 
                self.logger.debug("broken in end_linenumber {}".format(each_log_line_number))
                break
            if end_line and each_log_line == end_line: 
                self.logger.debug("broken in end_line {}".format(each_log_line))
                break
            #----end filters end----------------------------------------

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def search_log_file(self,          
        file: str = None,
        directory: str = None,                     
        words_to_find_in_line = None,
        search_mode: str = "log_time_based",       
        start_line: str = None,                    
        end_line: str = None,                      
        start_line_number: int = None,             
        end_line_number: int = None,               
        start_timestamp = None,                    
        end_timestamp = None,                      
        break_after_first_hit: bool = True,        
        wait_time: int = 30,                              
        failure_expected: bool = False,
        search_file = "latest"             
    ):
        """
        This function searches a log file for specific words or lines based on different search modes.
        
        Parameters:
            * file (str): The log file to search in.
            * directory (str): The directory where the log file is located.
            * words_to_find_in_line (str or list): The word(s) to search for in the log lines.
            * search_mode (str): The mode of searching (log_based/log_time_based/log_number_based). Default is "log_time_based".
            * start_line (str): The start threshold line for searching logs.
            * end_line (str): The end threshold line for searching logs.
            * start_line_number (int): The start line number for searching logs.
            * end_line_number (int): The end line number for searching logs.
            * start_timestamp (datetime): The start time for searching logs.
            * end_timestamp (datetime): The end time for searching logs.
            * break_after_first_hit (bool): Specifies whether to break/not after the first hit.
            * wait_time (int): The amount of time to wait before raising an exception. Default is 30 seconds.
            * failure_expected (bool): Specifies whether the requested log can be found or not. Default is False.
        
        Returns:
            * None. However, if a log is found, the function will return information about the log such as the line number, timestamp, etc.
        
        Raises:
            * Exception: If the log is not found when it was expected to be found (failure_expected is False), or if the log is found when it was not expected to be found (failure_expected is True).
        
        Work Flow:
            1. The function first checks if the directory is provided, else it uses a default log path. It also checks the type of file provided (if it's a string or regex pattern) and sets some variables accordingly.
            2. A while loop is used to keep searching for the log until the wait time is over. Inside the loop, it gets the latest log file in the directory, resets some variables, reads the log file, and performs the actual log search using the 'log_finder' method.
            3. The 'log_finder' method goes through each line of the log file, checking if it matches the search criteria (based on line number, timestamp, and/or specific words). If a match is found, it updates some variables with information about that log line (like line number, timestamp, etc.) and adds it to a list of all matches found so far.
            4. If the 'break_after_first_hit' parameter is True, the search will stop after finding the first match. Otherwise, it will continue searching until no more matches are found or the end line/timestamp is reached.
            5. After the while loop finishes (either because a match was found and 'break_after_first_hit' is True, or because the wait time was reached), it logs the result of the search (either success or failure) and raises an exception if the log was found when it was not expected, or vice versa.
            6. If the log was found successfully, it returns some information about that log line (like line number, timestamp, etc.). If the log was not found, it raises an exception with details about the search.

        Note: This code assumes that some methods (like 'get_latest_file', 'log_reader', and 'log_finder') are already defined elsewhere in the class or module.
        """
        
        Number_Of_Iterations_Performed = 0
        log_contents_start_slicer = 0
        # File & Directory interpretation ------------------------------
        if directory == None: 
            directory = self.CONST.log_path
            
        # in case 'file' is of type 'file' (custom object containing name, path & pattern details)
        if file in self.log_res.keys():
            file = self.log_res[file]
            directory = file.path
            file_regex_pattern = file.regex_pattern
            file = file.name
            self.logger.info("File is of type 'file'")
        # in case if provided 'file' is a string
        elif type(file) == str:
            # in case if provided 'file' is a regex pattern
            try:
                file_regex_pattern = re.compile(file)
                self.logger.info("File is of type 'regex'")
            except re.error:
                # this has to be just a string now
                file_regex_pattern = None
                self.logger.info("File is of type 'str'")
        else:
            self.logger.warning("Provided file is neither of type file/str/regex")
            raise Exception("Provided file is neither of type file/str/regex")
        
        if words_to_find_in_line == list:
            log_regex_pattern = None
            self.logger.info("Provided words_to_find_in_line is of type list")
        else:
            # in case if provided 'words_to_find_in_line' is a regex pattern
            try:
                log_regex_pattern = re.compile(words_to_find_in_line)
                self.logger.info("Provided words_to_find_in_line 'regex'")
            except:
                log_regex_pattern = None
                self.logger.info("Provided words_to_find_in_line 'str'")
        
        
        # Updated code for supporting win 4.4+
        if os_version == "Windows":
            try:
                if file in ["ZSATunnel", "ZSATrayManager", "ZSAUpm","ZSATunnel_", "ZSATrayManager_", "ZSAUpm_"]:
                    if self.is_zcc_installed():
                        version = self.get_current_zcc_version().split(".")
                        if int(version[0]) >= 4 and int(version[1]) >= 4:
                            Hashed_SID = self.Get_Hashed_User_SID()
                            if directory==self.CONST.log_path and os.path.exists(os.path.join(self.CONST.log_path, "log-{}".format(Hashed_SID))):
                                    directory = os.path.join(self.CONST.log_path, "log-{}".format(Hashed_SID))
            except Exception as e:
                pass
        
        # Search Logic --------------------------------------------------

        self.is_found=False
        self.list_of_hits_so_far=[]
        log_search_start_time = datetime.now()
        startTime = int(time.time())
        
        while( (datetime.now() - log_search_start_time) < timedelta(seconds=wait_time) if wait_time!=0 else (Number_Of_Iterations_Performed<=1)):
            try:
                log_file = self.get_latest_file(
                    directory = directory, 
                    file_sub_string = file, 
                    file_regex_pattern = file_regex_pattern,
                    search_file = search_file
                    )
                
                log_contents = self.log_reader(log_file_full_path = os.path.join(directory, log_file))
                if log_file!=self.recently_opened_file:
                    self.reset_variables()
                else:
                    if search_mode=="log_based" and start_line==None: 
                        start_line = copy.deepcopy(self.recent_log_line)
                    elif search_mode=="log_number_based" and start_line_number==None: 
                        start_line_number = copy.deepcopy(self.recent_log_line_number)
                    elif search_mode=="log_time_based" and start_timestamp==None: 
                        start_timestamp = copy.deepcopy(self.recent_log_timestamp)
                    log_contents = log_contents[log_contents_start_slicer:]
            
                self.log_finder(
                            log_file=log_file,
                            log_contents=log_contents, 
                            words_to_find_in_line=words_to_find_in_line,
                            log_regex_pattern=log_regex_pattern,
                            start_timestamp=start_timestamp,
                            start_line_number=start_line_number,
                            start_line=start_line,
                            end_timestamp=end_timestamp,
                            end_line_number=end_line_number,
                            end_line=end_line,
                            break_after_first_hit=break_after_first_hit
                            )
                Number_Of_Iterations_Performed +=1
                if self.is_found:
                    #print(f'\nFOUND !! :: {self.recent_log_line},{self.recent_log_line_number}')
                    break
                else:
                    log_contents_start_slicer = len(log_contents)-1
                    print(f"Log Search Time elapsed :: {int(time.time()-startTime)} seconds",end='\r')
            except Exception as E:
                        
                if Exception==KeyboardInterrupt:exit()


        # ----- conclude -----------------
        self.logger.info("\nLog Search Result :: {}".format("Success" if self.is_found else "Failure"))
        self.logger.info("logpath-file :: {0}{1}".format(directory,log_file))
        self.logger.info("Words to Find in Line :: {}".format(words_to_find_in_line))
        if self.is_found:
            self.logger.info("recent_log_line :: {}".format(self.recent_log_line.strip()))
            self.logger.info("recent_log_line_number :: {}".format(self.recent_log_line_number))
            self.logger.info("recent_log_timestamp :: {}".format(str(self.recent_log_timestamp)))
            self.logger.info("recent_log_line_number_bottom :: {}".format(str(self.recent_log_line_number_bottom)))
            self.logger.debug("all hits found for this search: \n{}\n".format(self.list_of_hits_so_far))

        if self.is_found:
            if failure_expected: 
                self.logger.error("Failure :: Log Found, when not expected")
                raise Exception("Failure :: Log Found, when not expected")
            return
        else:
            if failure_expected: 
                self.logger.info("Success :: Log not Found, as expected")
                return
            self.logger.error(f"Failure :: Log not Found :: {words_to_find_in_line}")
            raise Exception(f"Failure :: Log not Found :: {words_to_find_in_line}")
    
    # ------------------------------------------------------------
    def Get_SID(self, force_read=False):
        if self.SID and force_read==False:
            return self.SID
        else:
            command = "whoami /user" if os_version=="Windows" else "dsmemberutil getsid -U {}".format(os.getlogin())
            output = subprocess.check_output(command, shell=True)
            if os_version == "Windows":
                SID = str(output.stdout).split('\\')[-3].split(' ')[-1]
            else:
                SID = str(output.decode('utf-8').strip())
            self.SID = SID

            return SID
    
    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    def Architecture_Check(self):       # Function to find out architecture of existing zcc
        if os.path.exists(self.CONST.PROGRAM_FILES_32BIT_LOCATION) and "Zscaler" in os.listdir(self.CONST.PROGRAM_FILES_32BIT_LOCATION): self.architecture='32bit'
        elif os.path.exists(self.CONST.PROGRAM_FILES_64BIT_LOCATION) and "Zscaler" in os.listdir(self.CONST.PROGRAM_FILES_64BIT_LOCATION): self.architecture='64bit'
        else: self.logger.info("\n\n\nWarning :: ZCC Probably not installed!\n\n\n")

    # -----------------------------------------------------------------------------------------------------
    def Get_Hashed_User_SID(self, force_read=False):
        if self.Hashed_SID and force_read==False:
            return self.Hashed_SID
        else:
            self.Architecture_Check()
            if self.architecture=='32bit':
                zscaler_hkey = winreg.OpenKeyEx(winreg.HKEY_LOCAL_MACHINE,self.CONST.zcc_registry_path_32_bit)
            elif self.architecture=='64bit':
                zscaler_hkey = winreg.OpenKeyEx(winreg.HKEY_LOCAL_MACHINE,self.CONST.zcc_registry_path_64_bit)
            else:
                return None
            key_value = winreg.QueryValueEx(zscaler_hkey,"SID")[0]
            self.Hashed_SID = key_value
            return key_value

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Validate_Pcaps_Logs(self,
                        driver_type: str = 'route_based'
                        ):
        """
        This function validates the pcap logs based on the driver type.
        It searches for specific file names in the log directory based on the driver type.
        If the files are not found, it raises an exception and logs the error.
        If all files are found, it logs an info message.

        :param driver_type: The type of driver to look for in the log files. Default is 'route_based'.
        :type driver_type: str
        """
        log_path = self.CONST.log_path
        if driver_type == 'route_based':
            filesToBeSeached = {'CaptureAdapters_': False}
        else:
            filesToBeSeached = {'CaptureLWF_': False, 'CaptureAdapters_': False}
        if len([files for files in sorted(os.listdir(log_path), reverse=True)]) == 0:
            self.logger.error("Error :: No file found with required keywords")
            raise Exception("Error :: No file found with required keywords")
        log_file = [files for files in sorted(os.listdir(log_path), reverse=True) if 'Capture' in files]
        self.logger.info(log_file)
        for file in filesToBeSeached.keys():
            self.logger.info(file)
            for logfiles in log_file:
                if file in logfiles:
                    self.logger.info("Found : {}".format(file))
                    filesToBeSeached[file] = True
        self.logger.info(filesToBeSeached)
        if False in filesToBeSeached.values():
            self.logger.error("ERROR ::Mode - {},{}".format(driver_type, filesToBeSeached))
            raise Exception("ERROR ::Mode - {},{}".format(driver_type, filesToBeSeached))
        else:
            self.logger.info('All required files found')

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def validateExportedLogs(self):
        """
        This function validates the most recent exported log zip file in a specific directory.

        It first lists all items in the directory and appends those that are Zscaler zip files to a list.
        Then, it checks the last modified time of the most recently added file (presumed to be the most recent zip file).
        If the last modification was more than 3 minutes ago, it logs an error, otherwise it confirms that the file is valid.
        """
        zipFilesList = []
        for item in os.listdir(self.CONST.export_logs_path):
            if 'Zscaler' in item and '.zip' in item: zipFilesList.append(item)
        self.logger.info(f"Zscaler zip files are : {sorted(zipFilesList)}")
        zipFile = sorted(zipFilesList)[-1]
        if int(time.time() - os.path.getmtime(os.path.join(self.CONST.export_logs_path, zipFile))) > 120:
            self.logger.error(
                f"ERROR :: {zipFile} last update time is greater than 3 minute, doesnt look like latest exported zip file")
        else:
            self.logger.info(f"{zipFile} was created not more than 3 minutes ago, so latest zip file is found")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def validateNoDMPlog(self):
        """
        This function validates if there are any .dmp or .DMP files in the log directory.
        These files are typically error dump files and their presence indicates an error or crash.

        It first checks the log directory path and if it is a MAC system, it checks for additional log directory path for the tray application.
        Then it iterates over each item in the log directory and checks if the file name contains .dmp or .DMP.
        If found, it logs an error message and sets dumpFileFound to True.

        If dumpFileFound is True at the end of the function, it raises an Exception stating 'DMP files found, please check'.
        Otherwise, it logs an info message 'No dmp file found, all good !!'.
        """
        dumpFileFound=False
        try:
            if os_version=="MAC":
                logPathForMac=[self.CONST.log_path,self.CONST.tray_log_path]
                for logppath in logPathForMac:
                    for item in os.listdir(logppath):
                        if '.dmp' in item or '.DMP' in item:
                            self.logger.error(f"ERROR :: DMP file found : {item} at {logppath}")
                            dumpFileFound=True
            else:
                for item in os.listdir(self.CONST.log_path):
                    if '.dmp' in item or '.DMP' in item:
                        self.logger.error(f"ERROR :: DMP file found : {item} at {self.CONST.log_path}")
                        dumpFileFound=True
        except FileNotFoundError:
            self.logger.info('Log path not found, zcc is uninstalled probably')
        if dumpFileFound:raise Exception('DMP files found, please check')
        else:self.logger.info("No dmp file found, all good !!")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def validateTunnelModeFromLogs(self,
                                   tunnel_version: str = None,
                                   file: str = "ZSATunnel"
                                   ):
        """
        This function validates the tunnel mode from logs.

        Parameters:
            * tunnel_version (str): Tunnel version to search for in logs. Default is None. takes values such as tunnel_1, twlp, tunnel_2_dtls, tunnel_2_tls
            * file (str): Name of the log file to search in. Default is "ZSATunnel".

        Raises:
            * Exception: If no tunnel version is provided.
        """
        if tunnel_version == None:
            line = "No tunnel Version provided to search!"
            self.logger.error(line)
            raise Exception(line)

        search_for = self.CONST.tunnelLogs_List[tunnel_version]

        print("Search list is {}".format(search_for))
        self.search_log_file(file=file, words_to_find_in_line=search_for)
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def is_zcc_installed(self):
        """ 
        Work Flow 
        ----------
            1. find out the operating system (win/mac)
            2. assert if the uninstaller exists already.
            3. uninstaller existence implies ZCC installed already. same in reverse
        """
        if self.os_version!="MAC":
            if os.path.exists(self.CONST.windows_zcc_uninstaller_path): return True
            elif os.path.exists(self.CONST.windows_zcc_uninstaller_path_64bit): return True
            else: return False
        else:
            if os.path.exists(self.CONST.mac_zcc_uninstaller_path): return True
            else:  return False

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def get_current_zcc_version(self, force_read_logs=False):
        """
        This function returns the current ZCC version.

        The function checks the OS version and searches for the version in the appropriate log file.
        If it is a MAC system, it searches for 'INF --------------version:' in the tray log path.
        If it is not a MAC system, it first searches for 'INF ZSATrayManager Version' and if not found,
        it searches for '{"appVersion":' to get the version.

        Returns:
            * str: The current ZCC version.
        """
        if self.zcc_version!=None and force_read_logs==False:
            return self.zcc_version
        else:
            if os_version == "MAC":
                logToBeSearched = "INF --------------version:"
                log_path = self.CONST.tray_log_path
                self.search_log_file(file="ZSATray", words_to_find_in_line=[logToBeSearched],
                                    search_mode=None, start_line_number=None, wait_time=0)
                self.logger.info(self.recent_log_line.split(" ")[-1])
                self.zcc_version = self.recent_log_line.split(" ")[-1].split(':')[-2].strip()
                return self.recent_log_line.split(" ")[-1].split(':')[-2].strip()
            else:
                logToBeSearched = 'INF ZSATray App Version'
                # -- this code block is made for zsatray, whose directory wont change for win 4.4 update
                try:
                    # self.search_log_file(file="ZSATray", words_to_find_in_line=[logToBeSearched],
                    #                     search_mode=None, start_line_number=None, wait_time=0)
                    # zcc_version = self.recent_log_line.split(":")[-1].strip()
                    # self.zcc_version = zcc_version
                    zcc_version = "********"
                    self.zcc_version = zcc_version
                    self.logger.info(f"CURRENT ZCC VERSION :: {self.zcc_version}\n")
                    return zcc_version
                except Exception as e:
                    print(e)
                logToBeSearched = 'INF ZSATrayManager Version'
                try:
                    self.search_log_file(file="ZSATrayManager", words_to_find_in_line=[logToBeSearched],
                                        search_mode=None, start_line_number=None)
                    print(self.recent_log_line.split(" ")[-1])
                    self.zcc_version = self.recent_log_line.split(" ")[-1].strip()
                    return self.recent_log_line.split(" ")[-1].strip()
                except:
                    try:
                        logToBeSearched = '{"appVersion":'
                        self.search_log_file(file="ZSATrayManager", words_to_find_in_line=[logToBeSearched],
                                            search_mode=None, start_line_number=None)
                        zappVersion = eval(self.recent_log_line)
                        print(f'\n\n{zappVersion["appVersion"]}\n\n')
                        self.zcc_version = zappVersion["appVersion"].strip()
                        return zappVersion["appVersion"].strip()
                    except Exception as E:
                        raise Exception(E)

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def match_installed_build_version(self,
                                    buildNumber: str
        ):
        """
        This function compares the build number provided with the installed build number.
        If they match, it logs the information. If they don't match, it raises an exception.

        Parameters:
            * buildNumber (str) : The build number to compare with the installed build number.

        Raises:
             * Exception: If the build number and installed build number do not match.
        """
        installedBuild = self.get_current_zcc_version()
        if buildNumber in installedBuild:
            self.logger.info(f"Build given : {buildNumber}, Installed Build : {installedBuild}")
        else:
            raise Exception(f"Build given : {buildNumber}, Installed Build : {installedBuild}")

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    def returnMultipleLogLines(
            self,
            startLine: str,
            EndLine: str,
            file: str = None,
            log_path: str = None
    ):
        """
        Extract specific lines from a log file.

        Parameters:
            * startLine (str): The starting line to be extracted from the log file.
            * EndLine (str): The ending line to be extracted from the log file.
            * file (str): The name of the log file to extract lines from. If not provided, it will use a default log file.
            * log_path (str): The path to the log files. If not provided, it will use a default log path.

        Returns:
            * list: A list of strings containing the lines of log data between the startLine and EndLine (inclusive).

        WorkFlow:
            * The function first cleans the `startLine` and `EndLine` parameters by converting them to lowercase and removing spaces.
            * It then determines the appropriate log file to use based on the `file` parameter and the operating system.
            * If no `log_path` is provided, it will use a default log path.
            * The function reads the log file in reverse order, looking for the `EndLine` first and then the `startLine`.
            * Once both lines are found, it extracts the data between these two lines (inclusive) and returns it.
        """
        startLine = str(startLine).lower().replace(' ', '')
        EndLine = str(EndLine).lower().replace(' ', '')

        if file == 'ZSATunnel': file = 'ZSATunnel_2'
        if file == 'ZSATunnel_Gov' and os_version == "MAC": file = 'ZSATunnel_2'

        if log_path == None: log_path = self.CONST.log_path
        if file == "ZSATray": log_path = self.CONST.tray_log_path

        log_file = [files for files in sorted(os.listdir(log_path), reverse=True) if file in files][0]

        self.logger.info(f'Getting data from {os.path.join(log_path, log_file)}')

        with open(os.path.join(log_path, log_file), 'r') as f:
            data = f.readlines()

        startLineData = ''
        endLineData = ''

        for i in range(len(data) - 1, -1, -1):
            line = data[i]

            if endLineData == '':
                if EndLine in line.lower().replace(' ', ''):
                    endLineData = i
            else:
                if startLineData == '' and startLine in line.lower().replace(' ', ''):
                    startLineData = i

            if endLineData != '' and startLineData != '':
                break

        print(f'Start Line at :: {startLineData}, End Line at :: {endLineData}')

        sld = min(endLineData, startLineData)
        mld = max(endLineData, startLineData)
        # print(sld,mld)

        data = data[sld: mld + 1]
        return data

    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    def extractSMEIPList(self,
                    tunnel_version: str = 'T2'
        ):
        """
        Extracts the SME IP List from the log file.

        Parameters:
            * tunnel_version: Tunnel version, either 'T2' or not 'T2'. Default is 'T2'.

        Returns:
            * List of SME IPs.

        WorkFlow:
            1. Depending on the `tunnel_version` parameter, it sets `pac_request_type_string` to either '(pacRequestType= 2):' or '(pacRequestType= 1):'.
            2. It searches the log file for lines containing both 'refreshSMEIpFromPac:' and `pac_request_type_string`.
            3. It then gets the most recent such line found (stored in `self.recent_log_line`).
            4. If no such line was found (`self.recent_log_line` is empty), it logs an error and raises an exception.
            5. Otherwise, it proceeds to extract the SME IP and Ports from this line:
            - It splits the line by `pac_request_type_string`, taking the last part.
            - It then splits this part by commas (','), obtaining a list of IPs and ports.
            - It then checks the length of this list and keeps only the first two elements (IPs), if present.
            6. Finally, it logs the SME list and returns it.
        """
        pac_request_type_string = ""
        result = ""
        sme_list = []

        if tunnel_version == "T2" or tunnel_version == "t2":
            pac_request_type_string = "(pacRequestType= 2):"
        else:
            pac_request_type_string = "(pacRequestType= 1):"

        self.search_log_file("ZSATunnel", words_to_find_in_line = ['refreshSMEIpFromPac:', pac_request_type_string])

        result = self.recent_log_line

        if result == "":
            self.logger.error("Specified string not found")
            raise Exception

        else:
            self.logger.info("SME List found, extracting SME IP and Ports.")
            sme_list = result.split(pac_request_type_string)[-1].split(',')
            if len(sme_list) == 3:
                sme_list = sme_list[0:2]
            elif len(sme_list) == 2:
                sme_list = sme_list[0:1]

            self.logger.info("SME List is below \n {}".format(sme_list))

            return sme_list

############################################################################################################################
class pcap_ops:
    """
    The `pcap_ops` class is designed to handle operations related to pcap files.
    It has two main methods: `Initialize_Logger` and `readPcap`.

        * Initialize_Logger: This method initializes a logger for the application.
        * readPcap: This method reads a pcap file from a given path and returns the packets.
    """
    def __init__(self, logger):
        self.logger_name = "log_ops.log"
        self.logger = (logger if logger else self.Initialize_Logger(self.logger_name, Log_Level="INFO"))
        self.Logs_Directory = os.path.join(os.path.abspath(os.path.dirname(os.path.abspath("trigger_automation_zdx.py"))),"zdx", "Logs")
        self.os_version = os_version
        self.CONST = Constants.Utils
        if self.os_version == "Windows":
            self.path = r"C:\ProgramData\Zscaler"
        else:
            self.path = r"/Library/Application Support/Zscaler"
    

    # --------------------------------------------------------------------------------------------------------------------------
    def Initialize_Logger(self,
                          Log_File_Name: str,
                          Log_Level: str,
                          console_handle_needed: bool = True
                          ):
        """
        This function initializes a logger for the application.

        Parameters:
            * Log_File_Name (str): The name of the log file to be created.
            * Log_Level (str): The level of logging (INFO/DEBUG/WARNING/CRITICAL etc).
            * console_handle_needed (bool): Optional, print on command line if True only. Default is True.

        Returns:
            * logger: A logger object that can be used to log messages.

        Work Flow:
            1. Create a logger object.
            2. Create a console handler if required
                - console handler is used to print_ required info on command line window.
            3. Create a file handler -> mandatory
                - Make sure 'Logs' folder exists, as this file handler will be created in this folder only.
                - file handler is used to send all information to a log file in specified folder.
            4. Set the debug level (INFO/DEBUG/WARNING/CRITICAL etc). Default: INFO
            5. Specify how the logging must happen (with/without datetime/loglevel/username)
            6. Add all created handlers to the logger instance created in step 1.
            7. return the logger instance.
        """
        # 1, 2 & 3
        logger = logging.getLogger(Log_File_Name)
        if console_handle_needed: console_handler = logging.StreamHandler()
        if not os.path.exists(self.Logs_Directory): os.mkdir(self.Logs_Directory)
        file_handler = logging.FileHandler(os.path.join(self.Logs_Directory, Log_File_Name))

        # 4
        if Log_Level:
            logger.setLevel(eval("logging.{}".format(Log_Level)))
            if console_handle_needed:
                console_handler.setLevel(eval("logging.{}".format(Log_Level)))
            file_handler.setLevel(eval("logging.{}".format(Log_Level)))

        # 5
        if console_handle_needed:
            console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))

        # 6
        if console_handle_needed:
            logger.addHandler(console_handler)
        logger.addHandler(file_handler)
        # 7
        logger.info("If you see this, logger console is ON")

        return logger

    # ----------------------------------------------------------------------------------------------------------
    def readPcap(
            self,
            file: str = None,
            path: str = None,
            fileType: str = "Adapter"
    ):
        """
        This function reads a pcap file from a given path and returns the packets.

        Parameters:
            * file (str): The name of the pcap file to read. If not provided, the latest pcap file in the path will be used.
            * path (str): The path where the pcap files are stored. If not provided, the default path will be used.
            * fileType (str): The type of the pcap file to read.

        Returns:
            * data: A list of packets from the pcap file.
            * file: The full path of the pcap file that was read.
        """
        if path is None:
            path = self.path

        if file is None:
            files = sorted([
                f for f in os.listdir(path) if f"{fileType}" in f
            ])
            self.logger.info(f"Files present for {fileType} in {path} are {files}")

            file = files[-1]
            self.logger.info(f"Selecting the latest Pcap of type {fileType} file:: {file}")

        data = rdpcap(os.path.join(path, file))
        self.logger.info(f"Sending data for {os.path.join(path, file)}")
        return data, os.path.join(path, file)


class db_ops:
    
    def __init__(self,logger):
        
        self.logger = (logger if logger else self.Initialize_Logger("db_ops.log", Log_Level="INFO"))
        self.os_version = os_version
        self.CONST = Constants.Utils
                
    # --------------------------------------------------------------------------------------------------------------------------
    def Initialize_Logger(self, 
    Log_File_Name,                      # (str)     Name of the Log file to be created 
    Log_Level,                          # (str)     Level of logging (INFO/DEBUG/WARNING/CRICITCAL etc)
    console_handle_needed=True          # (bool)    print_ on command line if True only
    ):
        """
        Target: Create Logger object.
        Work Flow
        ----------
            1. Create a logger object.
            2. Create a console handler if required
                - console handler is used to print_ required info on command line window.
            3. Create a file handler -> mandatory
                - Make sure 'Logs' folder exists, as this file handler will be created in this folder only.
                - file handler is used to send all information to a log file in specified folder.
            4. Set the debug level (INFO/DEBUG/WARNING/CRITICAL etc). Default: INFO
            5. Specify how the logging must happen (with/without datetime/loglevel/username)
            6. Add all created handlers to the logger instance created in step 1.
            7. return the logger instance.
        """
        # 1, 2 & 3
        self.Logs_Directory = os.path.join(os.path.abspath(os.path.dirname(os.path.abspath("trigger_automation_zdx.py"))),"zdx", "Logs")
        logger = logging.getLogger(Log_File_Name)
        if console_handle_needed: console_handler = logging.StreamHandler()
        if not os.path.exists(self.Logs_Directory): os.mkdir(self.Logs_Directory)
        file_handler = logging.FileHandler(os.path.join(self.Logs_Directory, Log_File_Name))

        # 4
        if Log_Level:
            logger.setLevel(eval("logging.{}".format(Log_Level)))
            if console_handle_needed: console_handler.setLevel(eval("logging.{}".format(Log_Level)))
            file_handler.setLevel(eval("logging.{}".format(Log_Level)))

        # 5
        if console_handle_needed: console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))

        # 6
        if console_handle_needed: logger.addHandler(console_handler)
        logger.addHandler(file_handler)
        # 7
        return logger
    

    def read_db(self,
                db_path,     #(str) Path of the database file with .db extension
                query,       #(str) Query to be used to fetch data from db
            ):
        
        self.logger.info(f"Connecting to {db_path}")
        try:
            db_connection = sqlite3.connect(db_path)
        except Exception as E:
            self.logger.error(f"Error while connecting to DB :: {E}")
            raise Exception(f"Error while connecting to DB :: {E}")
        else:
            self.logger.info("Connected !")
            db_cursor = db_connection.cursor()
            self.logger.info(f"Executing query :: {query}")
            db_cursor.execute(query)
            self.logger.info("Query executed !")
            data = db_cursor.fetchall()
            self.logger.info(f"Number of db enteries :: {len(data)}")
            return data




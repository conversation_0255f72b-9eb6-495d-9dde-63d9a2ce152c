__author__ = '{"<PERSON><PERSON><PERSON>" : "<EMAIL>", "<PERSON><PERSON> V": "<EMAIL>", "<PERSON><PERSON><PERSON>": "<EMAIL>"}'

import pytest,os, json, time
from datetime import datetime
from OS_Windows.library.ui_zcc import Zcc
from Lib.common_lib.log_ops import log_ops
from common_lib.helper_function import return_bool
from typing import Callable
from Lib.common_lib.general_helpers import General_Helpers

class Events_Helper:
	def __init__(self, Loggers, Variables) -> None:
		self.logger = type(self).logger = Loggers
		self.Variables = type(self).Variables = Variables
		self.LOG_HANDLER = type(self).LOG_HANDLER = pytest.helpers.LOG_HANDLER()
		self.DB_HANDLER = type(self).DB_HANDLER = pytest.helpers.DB_HANDLER()
		self.zcc = type(self).zcc = Zcc(log_handle=self.logger)
		self.helper = type(self).helper = General_Helpers(Loggers=self.logger, Variables=self.Variables)
		self.logOps = type(self).logOps = log_ops(logger=self.logger)

	def get_uploaded_events(self, event_name : str, time_limit : int | None = None) -> tuple[bool, str, int]:
		"""
		[param]
			event_name : name of the event
			time_limit : (unix ms) the time until which the uploaded events are queried
		Returns:
			bool : status of function, True, if success else, False
			str : Intended message
			int : Number of events uploaded within the time_limit
		"""
		self.logger.info(f"Getting uploaded {event_name} events...")
		if time_limit:
			check_time = int(time.time() * 1000) - time_limit
			query = f"SELECT * FROM EVENTS WHERE EVENT_NAME = '{event_name}' AND UPLOAD_TIME IS NOT NULL AND UPLOAD_TIME >= {check_time}"
		else:
			query = f"SELECT * FROM EVENTS WHERE EVENT_NAME = '{event_name}' AND UPLOAD_TIME IS NOT NULL"
		status, res = self.DB_HANDLER.create_execute_close(
			os.path.join(self.Variables.LOGPATH, f'upm_device_events_{self.Variables.user_sid}.db'),
			query
		)
		if not status:
			self.logger.error(res)
			return (False, res, -1)
		self.logger.info(f"Cur uploaded events : {len(res)}")
		return (True, "Success", len(res))
	
	def get_unuploaded_events(self, event_name : str) -> tuple[bool, str, int]:
		"""
		[param]
			event_name : name of the event
		Returns:
			bool : status of function, True, if success else, False
			str : Intended message
			int : Number of unuploaded events
		"""
		self.logger.info(f"Getting unuploaded {event_name} events...")
		status, res = self.DB_HANDLER.create_execute_close(
			os.path.join(self.Variables.LOGPATH, f'upm_device_events_{self.Variables.user_sid}.db'),
			f"SELECT * FROM EVENTS WHERE EVENT_NAME = '{event_name}' AND UPLOAD_TIME IS NULL"
		)
		if not status:
			self.logger.error(res)
			return (False, res, -1)
		if not res:
			self.logger.error(f"Unuploaded {event_name} events absent")
			return (False, f"Unuploaded {event_name} events absent", 0)
		self.logger.info(f"Cur unuploaded events : {len(res)}")
		return (True, "Success", len(res))

	def delete_uploaded_events(self, event_name : str) -> tuple[bool, str]:
		"""
		[param]
			event_name : name of the event
		Returns:
			bool : status of function, True, if success else, False
			str : Intended message
		"""
		self.logger.info(f"Deleting uploaded {event_name} events...")
		try:
			for attempt in range(3):
				self.logger.info(f"Attempt {attempt + 1}/3 in deleting the db...")
				self.DB_HANDLER.create_db_connection(os.path.join(self.Variables.LOGPATH, f'upm_device_events_{self.Variables.user_sid}.db'))
				status, rows_affected = self.DB_HANDLER.execute(f"DELETE FROM EVENTS WHERE EVENT_NAME = '{event_name}' AND UPLOAD_TIME IS NOT NULL")
				if status:
					self.logger.info(f"Deleted, no.of rows affected : {rows_affected}")
					return (True,f"Deleted, no.of rows affected : {rows_affected}")
				self.logger.error("Failed")
			self.logger.error(f"Failed to deleted uploaded events of : {event_name}")
			return (False, f"Failed to deleted uploaded events of : {event_name}")		
		finally:    
			self.DB_HANDLER.close_db_connection()
		
	def check_uploaded_event_exists(self, event_name : str, s_time : int | None = None) -> tuple[bool, str]:
		self.logger.info(f"Checking existence of uploaded {event_name} events...")
		status, msg, no_of_uploaded_events = self.get_uploaded_events(event_name=event_name, time_limit=int(time.time() * 1000) - s_time if s_time else 0)
		if not status : return (False, f"{msg}")
		elif not no_of_uploaded_events:
			self.logger.error(f"No uploaded {event_name} event")
			return (False, f"No uploaded {event_name} event")
		else:
			self.logger.info("Uploaded event(s) exist(s)")
			return (True, "Uploaded event(s) exist(s)")

	def trigger_event(self, trigger_event_func : Callable, event_trigger_count : int = 1) -> tuple[bool, str]:
		self.logger.info("Trigerring event(s)...")
		for _ in range(event_trigger_count):
			if not return_bool(trigger_event_func()) : return (False, "Can't trigger event")
		return (True, "Triggered event(s)")
	
	def trigger_event_and_wait(self, trigger_event_func : Callable, event_trigger_count : int = 1, no_of_cycles : int = 1) -> tuple[bool, str]:
		self.logger.info("Trigerring event(s)...")
		for _ in range(event_trigger_count):
				if not return_bool(trigger_event_func()) : return (False, "Can't trigger event")
		self.logger.info("Waiting for TPG upload cycle(s)...")
		if not self.helper.wait_for_TPG_upload(no_of_cycles=no_of_cycles) : return (False, "TPG upload delayed/failed")
		return (True, "Success")

	def check_event_exist(self, event_name : str, search_start_time : int) -> tuple[bool, str]:
		"""
		[param]
			search_start_time : unix ms
		"""
		self.logger.info(f"Checking {event_name} event existence...")
		status, msg = self.DB_HANDLER.create_execute_close(
			os.path.join(self.Variables.LOGPATH, f'upm_device_events_{self.Variables.user_sid}.db'),
			f"SELECT * FROM EVENTS WHERE EVENT_NAME = '{event_name}' AND EVENT_TIME >= {search_start_time}"
		)
		if not status:
			self.logger.error(msg)
			return (False, msg)
		if not msg:
			self.logger.error(f"{event_name} Event absent in DB after {search_start_time}")
			return (False, f"{event_name} Event absent in DB after {search_start_time}")
		self.logger.info(f"{event_name} Event present")
		return (True, f"{event_name} Event present")

	def check_event_exist_retry(self, event_name : str, stime : int, retry : int = 1) -> tuple[bool, str]:
		'''
		[param]
		retry : number of times to retry the check_event_exist
		stime : time after which the events should be checked
		'''
		self.logger.info(f"Checking {event_name} event existence with {retry} retry...")
		for cur_try in range(retry):
			self.logger.info(f"In {cur_try + 1}/{retry}th attempt")
			status, msg = self.check_event_exist(event_name=event_name,search_start_time=int(stime))
			if status:
				self.logger.info(f"Success : {msg}")
				return (True, msg)
			self.logger.info("Sleeping 5s")
			time.sleep(5)
		etime = int(time.time() * 1000)
		self.logger.error(f"{event_name} Event absent between {stime} and {etime} in DB")
		return (False, f"{event_name} Event absent between {stime} and {etime} in DB")			

	def check_throttle_logs(self, event_name : str, search_start_time : datetime) -> tuple[bool, str]:
		data = self.LOG_HANDLER.get_latest_file_data_with_plugin(self.logger, self.Variables, 'deviceEvents')
		if not data:
			self.logger.error(f"No logs found for the device events plugin")
			return (False, "No logs found for the device events plugin")
		for line in data.splitlines():
			if "ZDEPlugin:getThrottledEvents" in line and event_name in line:
				time_from_log = self.logOps.get_log_line_timestamp(line)
				if time_from_log >= search_start_time:
					self.logger.info(f"Throttle logs found : {line}")
					return (True, line)
		etime = datetime.now()
		self.logger.error(f"Throttle logs absent between {search_start_time} and {etime}")
		return (False, f"Throttle logs absent between {search_start_time} and {etime}")
			
	def check_throttle_upload(self, event_name : str, stime : int, etime : int) -> tuple[bool, str]:
		status, msg, no_of_uploaded_events = self.get_uploaded_events(event_name=event_name)
		if not status : return (False, f"{msg}")
		if not no_of_uploaded_events : return (False, f"Uploaded {event_name} events absent")
		status, msg, no_of_unuploaded_events = self.get_unuploaded_events(event_name=event_name)
		if not status : return (False, f"{msg}")
		if not no_of_unuploaded_events : return (False, f"Unuploaded {event_name} events absent")
		status, msg = self.delete_uploaded_events(event_name=event_name)
		if not status : return (False, f"{msg}")
		self.logger.info("Sleeping 5s...")
		time.sleep(5)
		status, msg = self.helper.wait_for_TPG_upload_cycle_with_callback(no_of_cycles_to_wait=3, callback_func=lambda : self.check_events_upload(event_name=event_name,stime=stime,etime=etime))
		if not status:
			self.logger.error(f"Throttled {event_name} events not uploaded")
			return (False, f"{msg}")
		self.logger.info(f"Throttled {event_name} events uploaded")
		return (True, f"Throttled {event_name} events uploaded")

	def check_throttling(self, event_name : str, trigger_event_func=None,event_stime = None) -> tuple[bool, str]:
		self.logger.info(f"Checking throttling of {event_name} event...")
		status, msg, no_of_uploaded_events = self.get_uploaded_events(event_name=event_name,time_limit=50 * 60 * 1000)
		if not status : return (False, f"{msg}")

		if not no_of_uploaded_events:
			if not return_bool(self.zcc.export_logs(use_zcc_tray=False, wait_time_for_export=180, path_to_be_exported=os.path.join(self.Variables.automationlogslocation, f"DeviceEvents_{event_name}_{(str(datetime.now()).replace(' ', '')).replace(':', '_')}"))) : return (False, "Can't export logs")
			if not return_bool(self.zcc.clear_zdx_data()) : return (False, "Can't clear ZDX data")
			if not return_bool(self.trigger_event(trigger_event_func=trigger_event_func)) : return (False, "Can't trigger event")
			status, msg = self.helper.wait_for_TPG_upload_cycle_with_callback(no_of_cycles_to_wait=2, callback_func=lambda : self.check_uploaded_event_exists(event_name=event_name))
			if not status : return (False, msg)

		stime = time.time()
		if stime is not None:
			stime = event_stime

		if trigger_event_func is not None:
			status, msg = self.trigger_event(trigger_event_func=trigger_event_func)
			if not status : return (False, f"{msg}")

		status, msg = self.check_event_exist_retry(retry=2, event_name=event_name, stime=int(stime * 1000))
		if not status : return (False, f"Event absent : {msg}")
		etime = time.time()

		status, msg = self.helper.wait_for_TPG_upload_cycle_with_callback(callback_func=lambda : self.check_throttle_logs(event_name=event_name,search_start_time=datetime.fromtimestamp(stime)))
		if not status : return (False, f"{msg}")
		
		status, msg = self.check_throttle_upload(event_name=event_name,stime=int(stime * 1000),etime=int(etime * 1000))
		if not status : return (False, f"{msg}")
		self.logger.info(f"{event_name} Event(s) throttled and uploaded")
		return (True, f"{event_name} Event(s) throttled and uploaded")
		
	def check_events_upload(self, event_name : str, stime : int, etime : int) -> tuple[bool, str]:
		"""
			[param]
				stime : (unix ms)
				etime : (unix ms)
		"""
		self.logger.info(f"Checking whether all {event_name} events between {stime} and {etime} are uploaded...")
		status, res = self.DB_HANDLER.create_execute_close(
			os.path.join(self.Variables.LOGPATH, f'upm_device_events_{self.Variables.user_sid}.db'),
			f"SELECT * FROM EVENTS WHERE EVENT_NAME = '{event_name}' AND UPLOAD_TIME IS NULL AND EVENT_TIME BETWEEN {stime} AND {etime}"
		)
		if not status:
			self.logger.error(res)
			return (False, res)
		if res:
			self.logger.error(f"Unuploaded {event_name} events present")
			return (False, f"Unuploaded {event_name} events present")
		self.logger.info(f"{event_name} Events uploaded")
		return (True, f"{event_name} Events uploaded")
	
	def get_event_id(self, event_name : str) -> tuple[bool, str, int]:
		self.logger.info(f"Getting event id for {event_name}...")
		status, res = self.DB_HANDLER.create_execute_close(
			os.path.join(os.path.join(self.Variables.LOGPATH, f'upm_device_events_{self.Variables.user_sid}.db')),
			f"SELECT EVENT_ID FROM EVENTS WHERE EVENT_NAME = '{event_name}'"
		)   
		if not status or not res:
			self.logger.error(f"status : {status}, res : {res}")
			return (False, f"Failed to get event id for '{event_name}' from device event db", -1)
		event_id = res[0][0]
		self.logger.info(f"Event ID for '{event_name}' is {event_id}")
		return (True, f"Event ID for '{event_name}' is {event_id}", event_id)
	    
	def get_data_from_device_db(self,event_name:str,stime:int,etime:int|None=None)->tuple[bool,list|str]:
		query = ""
		if etime:
			query = f"SELECT zevent_metrics, event_time FROM events WHERE event_name='{event_name}' AND event_time BETWEEN {stime} AND {etime}"
		else:
			query = f"SELECT zevent_metrics, event_time FROM events WHERE event_name='{event_name}' AND event_time >= {stime}"
		try:
			self.logger.info(f"Getting data from DB for {event_name} between {stime} and {etime if etime else int(time.time() * 1000)}")
			status, res = self.DB_HANDLER.create_execute_close(
				os.path.join(os.path.join(self.Variables.LOGPATH, f'upm_device_events_{self.Variables.user_sid}.db')),
				query
			)
			if not status:
				self.logger.error("Failed to execute query")
				return (False, "Failed to execute query")
			result = []
			for row in res:
				zevent_metrics, event_time = row[0], row[1]
				if zevent_metrics:
					zevent_metrics = json.loads(zevent_metrics)
					if not zevent_metrics.get('metrics'):
						self.logger.error(f"'metrics' absent in zevent_metrics, event_name : {event_name}, zevent_metrics : {zevent_metrics}")
						return (False, f"'metrics' absent in zevent_metrics, event_name : {event_name}, zevent_metrics : {zevent_metrics}")
					result.append([zevent_metrics['metrics'] ,event_time])
				else:
					result.append([zevent_metrics, row[1]])
			self.logger.info(f"Got result : {result}")
			return (True, result)
		except Exception as e:
			self.logger.error(f"Error : {e}")
			return (False, f"Error : {e}")

	def get_aggregate_metrics(self, data: list[list[dict, int]], split_metrics_keys = [], agg_metrics_key = []) -> dict:
		result = dict()
		MAX_TIME = 1 << 65
		for row in data:
			item, event_time = row[0], row[1]
			rkey = "|".join(str(item[k]) for k in item if k in split_metrics_keys)
			if result.get(rkey) is None:
				result[rkey] = {"agg_num" : 0, "agg_metrics" : {}, "metrics" : {}, "etime" : 0, "stime" : MAX_TIME}
				for k in agg_metrics_key:
					result[rkey]["agg_metrics"][k] = {}
				for k in split_metrics_keys:
					result[rkey]["metrics"][k] = item[k]
			result[rkey]["etime"] = max(result[rkey]["etime"], event_time)
			result[rkey]["stime"] = min(result[rkey]["stime"], event_time)

			result[rkey]["agg_num"] += 1
			for key in item:
				if key in agg_metrics_key:
					result[rkey]["agg_metrics"][key][item[key]] = result[rkey]["agg_metrics"][key].get(item[key], 0) + 1
		result = list(map(lambda key:result[key],result))
		result.sort(key=lambda it:it['agg_num'],reverse=True)
		if result:
			if not split_metrics_keys : del result[0]['metrics']
			if not agg_metrics_key : del result[0]['agg_metrics']
			return result[0]
		return {}
	
	def get_latest_upload_time(self, event_name : str) -> int | None:
		status, res = self.DB_HANDLER.create_execute_close(
			os.path.join(os.path.join(self.Variables.LOGPATH, f'upm_device_events_{self.Variables.user_sid}.db')),
			f"SELECT upload_time FROM events WHERE event_name = '{event_name}' AND upload_time IS NOT NULL ORDER BY upload_time DESC LIMIT 1"
		)
		if not status or not res : return None
		self.logger.info(f"result : {res[0][0]}")
		return res[0][0]

	def get_upload_json_from_db(self, event_id : int, stime : int) -> tuple[bool, str, dict]:
		try:
			status, res = self.DB_HANDLER.create_execute_close(
				os.path.join(self.Variables.DBPATH, 'upm_upload_stats_{}.db'.format(self.Variables.user_sid)),
				"SELECT timestamp, upload_json FROM upload_data"
			)
			if not status : return (False, f"Failed to execute query error : {res}",{})
			for row in res:
				timestamp, upload_json = row
				try:
					upload_json = json.loads(upload_json)
				except Exception as e:
					return (False, f"upload json not in proper format, timestamp in upload_data table : {timestamp}, error: {e}", {})
				events = upload_json.get('device_events', {}).get('events', [])
				for event in events:
					if event.get('id', 0) == event_id  and event.get('stime', 0) == stime:
						return (True, f"Event found : {event} in upload stats db ,timestamp : {timestamp}", event)
			return (False, f"Failed to get event with event id : {event_id}, stime : {stime} from upload stats db",{})
		except Exception as e:
			return (False, f"Unexpected error : {e}",{})

	def compare_json(self,json1, json2):
		"""
		Compare two JSON objects for equality, including nested objects.
		
		:param json1: The first JSON object (could be a dictionary or list).
		:param json2: The second JSON object (could be a dictionary or list).
		:return: True if the two JSON objects are equal, otherwise False.
		"""
		if type(json1) != type(json2):
			return False
		if isinstance(json1, dict):
			if len(json1) != len(json2):
				return False
			for key in json1:
				if key not in json2:
					return False
				if not self.compare_json(json1[key], json2[key]):
					return False
		elif isinstance(json1, list):
			if len(json1) != len(json2):
				return False
			for item1, item2 in zip(json1, json2):
				if not self.compare_json(item1, item2):
					return False
		else:
			return json1 == json2
		return True

	def check_aggregation(self,event_name:str,trigger_event_func,split_metrics_keys=[],agg_metrics_key=[],max_retry=2)->tuple[bool,str]:
		self.logger.info(f"Checking aggregation of {event_name} event with split metrics : {split_metrics_keys} and agg metrics : {agg_metrics_key}...")
		status, msg, no_of_uploaded_events = self.get_uploaded_events(event_name=event_name,time_limit=50 * 60 * 1000)
		if not status : return (False, f"{msg}")

		if not no_of_uploaded_events:
			if not return_bool(self.zcc.export_logs(use_zcc_tray=False, wait_time_for_export=180, path_to_be_exported=os.path.join(self.Variables.automationlogslocation, f"DeviceEvents_{event_name}_{(str(datetime.now()).replace(' ', '')).replace(':', '_')}"))) : return (False, "Can't export logs")
			if not return_bool(self.zcc.clear_zdx_data()) : return (False, "Can't clear ZDX data")
			if not return_bool(self.trigger_event(trigger_event_func=trigger_event_func)) : return (False, "Can't trigger event")
			status, msg = self.helper.wait_for_TPG_upload_cycle_with_callback(no_of_cycles_to_wait=2,callback_func=lambda : self.check_uploaded_event_exists(event_name=event_name))
			if not status : return (False, msg)

		upload_stime = self.get_latest_upload_time(event_name=event_name)
		if not upload_stime : return (False, "Failed to get last upload time")
		self.logger.info(f"upload_stime : {upload_stime}")
		retry = max_retry
		agg_data : dict|None = None
		while retry:
			retry -= 1
			status, message = trigger_event_func()
			if not status : return (False, f"Trigger func failed : {message}")
			status, data_from_db = self.get_data_from_device_db(event_name=event_name,stime=upload_stime)
			if not status: return (False, f"Can't get data from DB : {data_from_db}")
			agg_data = self.get_aggregate_metrics(data=data_from_db,split_metrics_keys=split_metrics_keys,agg_metrics_key=agg_metrics_key)
			self.logger.info(f"Agg Data : {agg_data}")
			if agg_data.get('agg_num', 0) >= 2:
				self.logger.info("Aggregation occured, breaking...")
				break
		if not agg_data or agg_data.get('agg_num',0) < 2 : return (False, f"Failed to get or create aggregation data for '{event_name}' with max_retry : {max_retry}")
		etime = int(time.time() * 1000)
		status, message = self.check_throttle_upload(event_name=event_name,stime=upload_stime,etime=etime)
		if not status : return (False, f"{message}")

		upload_etime = self.get_latest_upload_time(event_name=event_name)
		if not upload_etime: return (False,"Failed to get last upload time")

		status, data_from_db = self.get_data_from_device_db(event_name=event_name,stime=upload_stime,etime=upload_etime)
		if not status : return (False,f"Can't get data from DB : {data_from_db} between {upload_stime} and {upload_etime}")
		agg_data = self.get_aggregate_metrics(data=data_from_db,split_metrics_keys=split_metrics_keys,agg_metrics_key=agg_metrics_key)
		status, msg, event_id = self.get_event_id(event_name=event_name)
		if not status : return (False, f"{msg}")
		agg_data['id'] = event_id
		self.logger.info(f"Agg Data : {agg_data}")
		status, message, upload_json = self.get_upload_json_from_db(event_id=event_id, stime=agg_data['stime'])
		self.logger.info(f"Upload json : {upload_json}")
		if self.compare_json(agg_data, upload_json):
			self.logger.info(f"{event_name} Event Aggregated")
			return (True, f"{event_name} Event Aggregated")
		self.logger.error(f"{event_name} Event not aggregated, expected UploadJson :{agg_data},Actual UploadJSon:{upload_json} not matching")
		return (False, f"{event_name} Event not aggregated, expected UploadJson :{agg_data},Actual UploadJSon:{upload_json} not matching")

	def check_event_attributes(self,event_name,stime=None):
		"""
		Verifies the attributes of a specified event by querying the database and comparing the event's attributes 
		to the expected values. The function checks if the values in the database match the predefined attributes 
		for a given event.

		Args:
			event_name (str): The name of the event whose attributes are to be verified.
			stime (int, optional): The start time (in milliseconds) for filtering events. If `None`, all events are considered.

		Returns:
			tuple: A tuple containing:
				- bool: `True` if the event attributes are successfully verified, `False` otherwise.
				- str: A message indicating the success or failure of the verification, 
					along with any issues encountered, if applicable.
		"""

		Issues , attributes_to_verify = [] ,{"zdx_subscription_standard": {'EVENT_CATEGORY': '103', 'EVENT_PRIORITY': '1'},
            "zdx_subscription_lite": {'EVENT_CATEGORY': '103', 'EVENT_PRIORITY': '1'},
            "zdx_tpg_enabled_lite": {'EVENT_CATEGORY': '103', 'EVENT_PRIORITY': '1'}, 
			"zdx_tpg_disabled_lite": {'EVENT_CATEGORY': '103', 'EVENT_PRIORITY': '1'},
			"zcc_collect_user_info_enabled": {'EVENT_CATEGORY': '103', 'EVENT_PRIORITY': '0'},
			"zcc_collect_machine_info_enabled": {'EVENT_CATEGORY': '103', 'EVENT_PRIORITY': '0'},
            "zcc_collect_user_info_disabled": {'EVENT_CATEGORY': '103', 'EVENT_PRIORITY': '0'},
			"zcc_collect_machine_info_disabled": {'EVENT_CATEGORY': '103', 'EVENT_PRIORITY': '0'}}
		
		query = f"SELECT ROWID AS row_num, * FROM EVENTS WHERE EVENT_NAME = '{event_name}' AND EVENT_TIME >= {stime}"
		if stime == None:
			query = f"""SELECT ROWID AS row_num, * FROM EVENTS WHERE EVENT_NAME = '{event_name}'"""

		status, probes= self.DB_HANDLER.create_execute_close(os.path.join(self.Variables.LOGPATH, f'upm_device_events_{self.Variables.user_sid}.db'),query)
        
		if not probes:
			return False, f"'{event_name}' not found"
		
		for probe in probes:
			try:
				if event_name in attributes_to_verify:
					for item in attributes_to_verify[event_name]:
						if str(probe[item]) == str(attributes_to_verify[event_name][item]):
							self.logger.info(f"SUCCESS: Row {probe['row_num']}, {item} - DB value: {probe[item]}, Expected value: {attributes_to_verify[event_name][item]}")
						else:
							self.logger.error(f"FAILURE: Row {probe['row_num']}, {item} - DB value: {probe[item]}, Expected value: {attributes_to_verify[event_name][item]}")
							Issues.append(f"FAILURE: Row {probe['row_num']}, {item} - DB value: {probe[item]}, Expected value: {attributes_to_verify[event_name][item]}")
			except Exception as e:
				self.logger.error(f"Error processing event row {probe['row_num']}: {e}")
				Issues.append(f"Error processing event row {probe['row_num']}: {e}")

		if Issues:
			return False, str(Issues).replace("[", "").replace("]", "")
		else:
			self.logger.info(f"SUCCESS :: '{event_name}' Verified")
			return True , f"SUCCESS :: '{event_name}' Verified"

	def fetch_app_id_and_mon_id(self, appname=None, tracert=True, web=True):
		"""
		Fetches the application ID and monitor ID for a given appname from the policy logs.

		This function processes the policies retrieved from the log handler to find the 
		application ID (`appid`) and monitor ID (`monid`) associated with the specified 
		`appname`.

		Parameters:
			appname (str): The application name to search for in the policy monitors.
						Default is None, meaning no filtering by appname will occur.
			tracert (bool): If True, include results for the TRACERT monitor type. Default is True.
			web (bool): If True, include results for the WEB monitor type. Default is True.

		Returns:
			tuple: A tuple containing:
				- A boolean indicating success (True) or failure (False).
				- A message string providing either a success message with the result, 
				or a failure message with the details of the failure.
		"""

		try:
			policies = self.LOG_HANDLER.get_policy_json_from_log(self.logger, self.Variables)
			res = {}

			if tracert:
				data = {'TRACERT': {'appid': '', 'monid': '', 'found': False}}
				res.update(data)

			if web:
				data = {'WEB': {'appid': '', 'monid': '', 'found': False}}
				res.update(data)

			for item in policies:
				if 'policy' in item and 'monitors' in item['policy']:
					for mon in item['policy']['monitors']:
						try:
							if mon['type'] in res and appname in mon['appname']:
								res[mon['type']]['appid'] = mon['appid']
								res[mon['type']]['monid'] = mon['id']
								res[mon['type']]['found'] = True
						except KeyError as e:
							self.logger.error(f"KeyError occurred while processing monitor: {mon}. Missing key: {e}")
						except Exception as e:
							self.logger.error(f"Unexpected error occurred while processing monitor: {mon}. Error: {str(e)}")

			for item in res:
				if not res[item]['found']:
					return False, res

			self.logger.info(f"SUCCESS :: res -> {res}")
			return True, res

		except Exception as e:
			self.logger.error(f"An error occurred in fetch_app_id_and_mon_id: {str(e)}")
			return False, f"An error occurred: {str(e)}"

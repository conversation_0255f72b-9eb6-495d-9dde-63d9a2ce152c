
"""
accepted clouds:
    1. zscalerbeta
    2. zscalerone
    3. zscalertwo
    4. zscalerthree
    5. zscloud
    6. zpaqa
"""
import os,json
import subprocess
import platform
import sys

os_version = None
if platform.uname()[0]=='Windows':
    import wmi
    output = subprocess.check_output(['wmic', 'os', 'get', 'Caption'])
    os_version = "WIN"
elif platform.uname()[0]=='Darwin':
    os_version ="MAC"
else:
    os_version = "Windows"
    print("Warning: OS version cannot be determined")


def load_payload(file_path):
    with open(file_path, "r") as file:
        payload = json.load(file)
    return payload


class admin_res():
    def __init__(self, cloud):
        self.cloud = cloud
        self.CLOUD_ADMIN_API_KEY_IV = 'jj7tg80fEGao'
        self.MOBILE_ADMIN_API_KEY = '130c2293-3bc3-4c2e-a9f0-36b4221581de'

        # -------Production Dict creds---------------------------
        # self.production_cloud_dict = {
        #     "beta":{
        #         "username":"<EMAIL>",
        #         "password":"Zrelacs@321"
        #         },
        #     "zscalerthree":{
        #         "username":"<EMAIL>",
        #         "password":"Admin@123"
        #         },
        #     "zscalertwo":{
        #         "username":"<EMAIL>",
        #         "password":"Zscaler@12345"
        #         },
        #     "zscalerone":{
        #         "username":"<EMAIL>",
        #         "password":"Zrelacs@321"
        #         },
        #     "zscloud":{
        #         "username":"<EMAIL>",
        #         "password":"Zscaler@12345"
        #         },
        #     "zscaler":{
        #         "username":"<EMAIL>",
        #         "password":"Zscaler@12345"
        #         },
        #     "zscalergov":{
        #         "username":"<EMAIL>",
        #         "password":"Zscaler123#"
        #         }
        #     }


        self.production_cloud_dict = {
            "zscalerbeta" :{"username" :"<EMAIL>" ,"password" :"Zrelacs@321"},
            "zscalerthree" :{"username" :"<EMAIL>" ,"password" :"Zrelacs@321"},
            "zscalertwo" :{"username" :"<EMAIL>" ,"password" :"Zrelacs@321"},
            "zscalerone" :{"username" :"<EMAIL>" ,"password" :"Zrelacs@321"},
            "zscloud" :{"username" :"<EMAIL>" ,"password" :"Zrelacs@321"},
            "zscaler" :{"username" :"<EMAIL>" ,"password" :"Zrelacs@321"},
            "zscalergov" :{"username" :"<EMAIL>" ,"password" :"Zscaler123#"}
        }


        # -------Authenticate_To_Mobile_Admin--------------------
        self.MOBILE_ADMIN_AUTHENTICATION_URL = "https://mobileadmin.{}.net/webservice/api/auth/login".format(self.cloud)
        self.MOBILE_ADMIN_AUTHENTICATION_PAYLOAD = {'apikey': '130c2293-3bc3-4c2e-a9f0-36b4221581de',
                                                    'loginname': '',
                                                    'role' :"ADMIN"
                                                    }
        self.CREATE_FORWARDING_PROFILE_HEADER = {'Connection': 'keep-alive',
                                                 'Content-Length': '',
                                                 'Pragma': 'no-cache',
                                                 'Cache-Control': 'no-cache',
                                                 'Accept': 'application/json, text/javascript, */*; q=0.01',

                                                 'auth-token': '',
                                                 'X-Requested-With': 'XMLHttpRequest',
                                                 'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36',

                                                 'Accept-Encoding': 'gzip, deflate', 'sdch'
                                                                                     'Accept-Language': 'en-US,en;q=0.8',
                                                 'Cookie': ''
                                                 }
        # -------------------------------------------------------
        # -------Authenticate_To_ZIA-----------------------------
        self.CLOUD_ADMIN_AUTHENTICATION_URL = "https://admin.{}.net/zsapi/v1/authenticatedSession".format(self.cloud)
        self.CLOUD_ADMIN_AUTHENTICATION_PAYLOAD = {'apiKey': "",
                                                   'password': "Zscaler@123",
                                                   'timestamp': "",
                                                   'username': "<EMAIL>"
                                                   }
        self.CLOUD_ADMIN_AUTHENTICATION_HEADERS = {'Connection': 'keep-alive',
                                                   'Content-Length': '',
                                                   'Pragma': 'no-cache',
                                                   'Cache-Control': 'no-cache',
                                                   'Accept': '*/*',

                                                   'X-Requested-With': 'XMLHttpRequest',
                                                   'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36',
                                                   'Content-Type': 'application/json',

                                                   'Accept-Encoding': 'gzip, deflate',
                                                   'Accept-Language': 'en-US,en;q=0.8',
                                                   'Cookie': ''
                                                   }
        # -------------------------------------------------------
        # -------Get Users List --------------------------------------
        self.Get_Users_List = 'https://mobile.{}.net/webservice/api/web/usersByCompany?page=1&pageSize=100&search='.format \
            (self.cloud)
        # -------------------------------------------------------
        # -------Toggle_ZPA--------------------------------------
        self.ZPA_URL = "https://mobile.{}.net/webservice/api/web/updateGroupEntitlementZPA".format(self.cloud)
        self.ZPA_HEADER = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }
        self.ZPA_PAYLOAD = {"groups" :[] ,"enableForAll" :1 ,"machineTunEnabledForAll" :0 ,"deviceGroups" :[]
                            ,"computeDeviceGroups" :0}
        # -------------------------------------------------------
        # -------Toggle_ZDX--------------------------------------
        self.ZDX_URL = "https://mobile.{}.net/webservice/api/web/updateGroupEntitlementUPM".format(self.cloud)
        self.ZDX_HEADER = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }
        self.ZDX_PAYLOAD = {"groups" :[] ,"enableForAll" :1 ,"deviceGroups" :[] ,"computeDeviceGroups" :0
                            ,"logoutZCC" :0}
        # -------------------------------------------------------
        # -------configure_AUP-----------------------------------
        self.configure_aup_url = "https://mobile.{}.net/webservice/api/web/cloud/updateAup".format(self.cloud)
        self.configure_aup_header = {
            "Host": "mobile.{}.net".format(self.cloud),
            "Connection": "keep-alive",
            "Content-Length": "52",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36",
            "Content-Type": "application/json; charset=UTF-8",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "X-Requested-With": "XMLHttpRequest",
            "auth-token": "",
            "sec-ch-ua-platform": "Windows",
            "Origin": "https://mobile.{}.net".format(self.cloud),
            "Referer": "https://mobile.{}.net/index.html".format(self.cloud),
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "en-US,en;q=0.9"
        }
        self.configure_aup_payload = {"aup_type" :3 ,"aup_days" :0
                                      ,"aup_data" :"<b>Acceptable Use Policy is not configured for your company</b>"}
        # -------------------------------------------------------
        # -------toggle_packet_capture---------------------------
        self.toggle_packet_capture_url = "https://mobile.{}.net/webservice/api/web/privacy/setPrivacyInfo".format \
            (self.cloud)
        self.toggle_packet_capture_payload = {
            "id" :"1395",
            "active" :"1",
            "collectUserInfo" :"1",
            "collectMachineHostname" :"0",
            "enablePacketCapture" :"0",
            "disableCrashlytics" :"0",
            "collectZdxLocation" :"0",
            "overrideT2ProtocolSetting" :"0"
        }
        self.toggle_packet_capture_header = {
            "Host": "mobile.{}.net".format(self.cloud),
            "Connection": "keep-alive",
            "Content-Length": "52",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36",
            "Content-Type": "application/json; charset=UTF-8",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "X-Requested-With": "XMLHttpRequest",
            "auth-token": "",
            "sec-ch-ua-platform": "Windows",
            "Origin": "https://mobile.{}.net".format(self.cloud),
            "Referer": "https://mobile.{}.net/index.html".format(self.cloud),
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "en-US,en;q=0.9"
        }
        # -------------------------------------------------------
        # -------App Fail Open--------------------------
        self.Get_Company_Details_Url = 'https://mobile.{}.net/webservice/api/web/failOpenPolicy/listByCompany'.format \
            (self.cloud)
        self.App_Fail_Open_Url = 'https://mobile.{}.net/webservice/api/web/failOpenPolicy/edit'.format(self.cloud)
        self.App_Fail_Open_Payload = {
            "active": "1",
            "captivePortalWebSecDisableMinutes": "10",
            "enableWebSecOnProxyUnreachable": 0,
            "enableWebSecOnTunnelFailure": 1,
            "id": "2877",
            "tunnelFailureRetryCount": 25
        }

        # -------------------------------------------------------
        # -------Toggle_report_an_issue--------------------------
        self.Toggle_Report_an_Issue_URL = "https://mobile.{}.net/webservice/api/web/company/setCompanyInfo".format \
            (self.cloud)
        self.Toggle_Report_an_Issue_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }
        self.Toggle_Report_an_Issue_Payload = {
            "disableSupportability" :"0",
            "supportEnabled" :"1",
            "fetchLogsForAdminsEnabled" :"0",
            "supportAdminEmail" :"",
            "supportTicketEnabled" :"0",
            "enableRectifyUtils" :"1",
            "enableAutofillUsername" :"1",
            "zpaReauthEnabled" :"0",
            "enableZpaAuthUserName" :"0"
        }
        # ---------------------------------------------------------
        # -------Auto-Upgrade-ZCC---------------------------------
        self.Delete_Auto_Upgrade_Rule_Url = "https://mobile.{}.net/webservice/api/web/autoupdate/deleteAutoUpdateGroup?id=".format(self.cloud)
        self.Auto_Upgrade_Url = "https://mobile.{}.net/webservice/api/web/autoupdate/updateApplicationList".format(self.cloud)
        self.Auto_Upgrade_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }
        self.Auto_Upgrade_Payload = {
            "companyAutoUpdateEnabled" :2,
            "windowsEnabledImage" :{},
            "macEnabledImage" :{},
            "linuxEnabledImage" :{}
        }

        # MAKE SURE THIS PAYLOAD ALWAYS IS FOR ALL GROUP PAYLOAD!! DONT FORGET, DONT BE IGNORANT
        self.Auto_Upgrade_Group_Payload = {
            "applyFromTimestamp": "",
            "enableSlowRollout": "0",
            "rolloutPercent": 100,
            "groupBasedApplicationList": [{
                "rowId": -1,
                "windowsEnabledImage": {
                    "applicationVersionId": 0,
                    "groupAutoUpdateEnabled": "2",
                    "use64BitInstallerForWindows": "0"
                },
                "macEnabledImage": {
                    "applicationVersionId": 0,
                    "groupAutoUpdateEnabled": "2",
                    "use64BitInstallerForWindows": "0"
                },
                "linuxEnabledImage": {
                    "applicationVersionId": 0,
                    "groupAutoUpdateEnabled": "2",
                    "use64BitInstallerForWindows": "0"
                },
                "applyFromTimestamp": "",
                "enableSlowRollout": "0",
                "rolloutPercent": 100
            }],
            "companyAutoUpdateEnabled": 3,
            "actionType": -1
        }
        # --------------------------------------------------------
        # -------Client Connector App Store Page ----------------
        self.Client_Connector_App_Store_Page_URL = "https://mobileadmin.{}.net/webservice/api/web/autoupdate/getApplicationList".format \
            (self.cloud)
        self.Client_Connector_App_Store_Page_HEADER = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }
        # -------------------------------------------------------
        # -------get_latest_zapp---------------------------------
        self.latest_zapp_url = "http://mobile-manager.corp.zscaler.com:8080/buildwebservice/api/showbuild?product=ZscalerApp&platform=LINUX&branch={}&buildType=6"
        # branch = "release_Zapp-Linux-from-3.0-rel-1-2"
        # action =  "get"
        self.latest_zapp_header = {
            "Host": "mobile-manager.corp.zscaler.com:8080",
            "Connection": "keep-alive",
            "Accept": "*/*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36",
            "Origin": "http://mobile-manager.corp.zscaler.com",
            "Referer": "http://mobile-manager.corp.zscaler.com/",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "en-US,en;q=0.9"
        }

        self.zapp_download_header = {
            "Host": "mobile-manager.corp.zscaler.com",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
            "Referer": "http://mobile-manager.corp.zscaler.com/",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "en-US,en;q=0.9",
            "Cookie": ""
        }

        # --------------------------------------------------------
        # -------get_zcc_list_from_ma---------------------------------
        self.get_zcc_list_from_ma_url = 'https://mobile.{}.net/webservice/api/web/autoupdate/getApplicationList'.format \
            (self.cloud)
        self.get_zcc_list_from_ma_header = {
            'auth-token' :''
        }
        # ----------------------------------------------------------------------
        self.get_newzcc_list_from_ma_url = 'https://mobile.{}.net/webservice/api/web/autoupdate/getNewApplicationList'.format \
            (self.cloud)
        # ----------------------------------------------------------------------

        # --------------------------------------------------------
        # -------set_Latest_Build_For_App_Update_On_MA---------------------------------
        self.set_Latest_Build_For_App_Update_On_MA_url = "https://mobile.{}.net/webservice/api/web/autoupdate/updateApplicationList".format \
            (self.cloud)
        self.set_Latest_Build_For_App_Update_On_MA_payload = {
            "companyAutoUpdateEnabled": 0,
            "linuxEnabledImage": {
            },
            "macEnabledImage": {
            },
            "windowsEnabledImage": {
            }
        }

        # -------------------------------------------------------
        # -------Register_Device_with_IDP_Username---------------
        self.Register_Device_with_IDP_Username_Url = "https://mobile.{}.net/webservice/api/web/company/setCompanyInfo".format \
            (self.cloud)
        self.Register_Device_with_IDP_Username_Header = {
            'Connection': 'keep-alive',
            'Content-Length': '',
            'Pragma': 'no-cache',
            'Cache-Control': 'no-cache',
            'Accept': 'application/json, text/javascript, */*; q=0.01',

            'auth-token': '',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36',

            'Accept-Encoding': 'gzip, deflate', 'sdch'
                                                'Accept-Language': 'en-US,en;q=0.8',
            'Cookie': ''
        }
        self.Register_Device_with_IDP_Username_Payload = {
            "disableSupportability" :"0",
            "supportEnabled" :"1",
            "supportAdminEmail" :"<EMAIL>",
            "supportTicketEnabled" :"0",
            "enableRectifyUtils" :"1",
            "enableAutofillUsername" :"1",
            "zpaReauthEnabled" :"0",
            "enableZpaAuthUserName" :"0"
        }
        # -------------------------------------------------------
        # -------check audit logs--------------------------------
        self.AUDIT_LOGS_url = "https://mobile.{}.net/webservice/api/web/auditLogEntries".format(self.cloud)
        self.AUDIT_LOGS_payload = {
            "page" :1,
            "pageSize" :100,
            "endTime" :'',
            "startTime" :''
        }
        self.AUDIT_LOGS_header = {
            'Connection': 'keep-alive',
            'Content-Length': '',
            'Pragma': 'no-cache',
            'Cache-Control': 'no-cache',
            'Accept': 'application/json, text/javascript, */*; q=0.01',

            'auth-token': '',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36',

            'Accept-Encoding': 'gzip, deflate', 'sdch'
                                                'Accept-Language': 'en-US,en;q=0.8',
            'Cookie': ''
        }
        # -------Create DLP Rule --------------------------------
        self.Create_ZDP_Rule_URL = "https://admin.{}.net/zsapi/v1/endPointDlpRules".format(cloud)
        self.Edit_ZDP_Rule_URL = "https://admin.{}.net/zsapi/v1/endPointDlpRules/RULE_ID_HERE".format(cloud)
        self.Create_ZDP_Rule_Header = {'Host': 'admin.{}.net'.format(cloud),
                                        'Connection': 'keep-alive',
                                        'ZS_CUSTOM_CODE': '',
                                        'Accept': 'application/json, text/javascript, */*; q=0.01',
                                        'X-Requested-With': 'XMLHttpRequest',
                                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.186 Safari/537.36',
                                        'Referer': 'https://admin.{}.net/'.format(cloud),
                                        'Origin': 'https://admin.{}.net/'.format(cloud),
                                        'Crossdomain': 'true',
                                        'Accept-Encoding': 'gzip, deflate, br',
                                        'Accept-Language': 'en-US,en;q=0.9',
                                        'Content-Type': 'application/json',
                                        'Cookie': ''
                                        }
        self.Create_ZDP_Rule_Payload = {
                                    "action": "ALLOW",
                                    "state": "ENABLED",
                                    "severity": "RULE_SEVERITY_HIGH",
                                    "dataTransferMethod": "REMOVABLE_DRIVE_TRANSFER",
                                    "minSize": 0,
                                    "eunEnabled": True,
                                    "withoutContentInspection": True,
                                    "name": "hahahhaha",
                                    "order": 4,
                                    "description": "hehehehe"
                                }
        self.Delete_ZDP_Rule_URL = "https://admin.{}.net/zsapi/v1/endPointDlpRules/RULE_ID_HERE".format(cloud)
        # 1693475999025
        self.Get_ZDP_Rule_URL = "https://admin.{}.net/zsapi/v1/endPointDlpRules?timestamp=TIME_STAMP_HERE".format(cloud)
        # -------------------------------------------------------
        # -------Create_Forwarding_Profile-----------------------
        self.CREATE_FORWARDING_PROFILE_URL = "https://mobile.{}.net/webservice/api/web/forwardingProfile/edit".format \
            (cloud)

        self.CREATE_FORWARDING_PROFILE_HEADER = {'Connection': 'keep-alive',
                                                 'Content-Length': '',
                                                 'Pragma': 'no-cache',
                                                 'Cache-Control': 'no-cache',
                                                 'Accept': 'application/json, text/javascript, */*; q=0.01',

                                                 'auth-token': '',
                                                 'X-Requested-With': 'XMLHttpRequest',
                                                 'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36',

                                                 'Accept-Encoding': 'gzip, deflate', 'sdch'
                                                                                     'Accept-Language': 'en-US,en;q=0.8',
                                                 'Cookie': ''
                                                 }
        
        self.CREATE_FORWARDING_PROFILE_PAYLOAD ={
            "zscalerbeta": {
                                                "active": 1,
                                                "addCondition": "",
                                                "conditionType": 1,
                                                "dnsSearchDomains": "",
                                                "dnsServers": "",
                                                "enableAllDefaultAdaptersTN": 0,
                                                "enableLWFDriver": 1,
                                                "enableSplitVpnTN": 0,
                                                "forwardingProfileActions": [
                                                    {
                                                        "actionType": 1,
                                                        "allowTLSFallback": 1,
                                                        "blockUnreachableDomainsTraffic": "0",
                                                        "customPac": "",
                                                        "dropIpv6IncludeTrafficInT2": 0,
                                                        "dropIpv6Traffic": "0",
                                                        "DTLSTimeout": 9,
                                                        "enablePacketTunnel": 0,
                                                        "latencyBasedServerEnablement": 0,
                                                        "latencyBasedZenEnablement": "0",
                                                        "lbsProbeInterval": 30,
                                                        "lbsProbeSampleSize": 5,
                                                        "lbsThresholdLimit": 1,
                                                        "mtuForZadapter": 0,
                                                        "networkType": 0,
                                                        "pathMtuDiscovery": 0,
                                                        "primaryTransport": 1,
                                                        "redirectWebTraffic": 0,
                                                        "systemProxyData": {
                                                            "bypassProxyForPrivateIP": 0,
                                                            "enableAutoDetect": 0,
                                                            "enablePAC": 0,
                                                            "enableProxyServer": 0,
                                                            "pacDataPath": "",
                                                            "pacURL": "",
                                                            "performGPUpdate": 0,
                                                            "proxyAction": 1,
                                                            "proxyServerAddress": "",
                                                            "proxyServerPort": 0
                                                        },
                                                        "TLSTimeout": 5,
                                                        "tunnel2FallbackType": 0,
                                                        "UDPTimeout": 9,
                                                        "useTunnel2ForProxiedWebTraffic": 0,
                                                        "zenProbeInterval": 60,
                                                        "zenProbeSampleSize": 5,
                                                        "zenThresholdLimit": 2
                                                    },
                                                    {
                                                        "actionType": 1,
                                                        "allowTLSFallback": 1,
                                                        "blockUnreachableDomainsTraffic": "0",
                                                        "customPac": "",
                                                        "dropIpv6IncludeTrafficInT2": 0,
                                                        "dropIpv6Traffic": "0",
                                                        "DTLSTimeout": 9,
                                                        "enablePacketTunnel": 0,
                                                        "latencyBasedServerEnablement": 0,
                                                        "latencyBasedZenEnablement": "0",
                                                        "lbsProbeInterval": 30,
                                                        "lbsProbeSampleSize": 5,
                                                        "lbsThresholdLimit": 1,
                                                        "mtuForZadapter": 0,
                                                        "networkType": 1,
                                                        "pathMtuDiscovery": 0,
                                                        "primaryTransport": 1,
                                                        "redirectWebTraffic": 0,
                                                        "systemProxyData": {
                                                            "bypassProxyForPrivateIP": 0,
                                                            "enableAutoDetect": 0,
                                                            "enablePAC": 0,
                                                            "enableProxyServer": 0,
                                                            "pacDataPath": "",
                                                            "pacURL": "",
                                                            "performGPUpdate": 0,
                                                            "proxyAction": 1,
                                                            "proxyServerAddress": "",
                                                            "proxyServerPort": 0
                                                        },
                                                        "TLSTimeout": 5,
                                                        "tunnel2FallbackType": 0,
                                                        "UDPTimeout": 9,
                                                        "useTunnel2ForProxiedWebTraffic": 0,
                                                        "zenProbeInterval": 60,
                                                        "zenProbeSampleSize": 5,
                                                        "zenThresholdLimit": 2
                                                    },
                                                    {
                                                        "actionType": 1,
                                                        "allowTLSFallback": 1,
                                                        "blockUnreachableDomainsTraffic": "0",
                                                        "customPac": "",
                                                        "dropIpv6IncludeTrafficInT2": 0,
                                                        "dropIpv6Traffic": "0",
                                                        "DTLSTimeout": 9,
                                                        "enablePacketTunnel": 0,
                                                        "latencyBasedServerEnablement": 0,
                                                        "latencyBasedZenEnablement": "0",
                                                        "lbsProbeInterval": 30,
                                                        "lbsProbeSampleSize": 5,
                                                        "lbsThresholdLimit": 1,
                                                        "mtuForZadapter": 0,
                                                        "networkType": 2,
                                                        "pathMtuDiscovery": 0,
                                                        "primaryTransport": 1,
                                                        "redirectWebTraffic": 0,
                                                        "systemProxyData": {
                                                            "bypassProxyForPrivateIP": 0,
                                                            "enableAutoDetect": 0,
                                                            "enablePAC": 0,
                                                            "enableProxyServer": 0,
                                                            "pacDataPath": "",
                                                            "pacURL": "",
                                                            "performGPUpdate": 0,
                                                            "proxyAction": 1,
                                                            "proxyServerAddress": "",
                                                            "proxyServerPort": 0
                                                        },
                                                        "TLSTimeout": 5,
                                                        "tunnel2FallbackType": 0,
                                                        "UDPTimeout": 9,
                                                        "useTunnel2ForProxiedWebTraffic": 0,
                                                        "zenProbeInterval": 60,
                                                        "zenProbeSampleSize": 5,
                                                        "zenThresholdLimit": 2
                                                    },
                                                    {
                                                        "actionType": 1,
                                                        "allowTLSFallback": 1,
                                                        "blockUnreachableDomainsTraffic": "0",
                                                        "customPac": "",
                                                        "dropIpv6IncludeTrafficInT2": 0,
                                                        "dropIpv6Traffic": "0",
                                                        "DTLSTimeout": 9,
                                                        "enablePacketTunnel": 0,
                                                        "latencyBasedServerEnablement": 0,
                                                        "latencyBasedZenEnablement": "0",
                                                        "lbsProbeInterval": 30,
                                                        "lbsProbeSampleSize": 5,
                                                        "lbsThresholdLimit": 1,
                                                        "mtuForZadapter": 0,
                                                        "networkType": 3,
                                                        "pathMtuDiscovery": 0,
                                                        "primaryTransport": 1,
                                                        "redirectWebTraffic": 0,
                                                        "systemProxyData": {
                                                            "bypassProxyForPrivateIP": 0,
                                                            "enableAutoDetect": 0,
                                                            "enablePAC": 0,
                                                            "enableProxyServer": 0,
                                                            "pacDataPath": "",
                                                            "pacURL": "",
                                                            "performGPUpdate": 0,
                                                            "proxyAction": 1,
                                                            "proxyServerAddress": "",
                                                            "proxyServerPort": 0
                                                        },
                                                        "TLSTimeout": 5,
                                                        "tunnel2FallbackType": 0,
                                                        "UDPTimeout": 9,
                                                        "useTunnel2ForProxiedWebTraffic": 0,
                                                        "zenProbeInterval": 60,
                                                        "zenProbeSampleSize": 5,
                                                        "zenThresholdLimit": 2
                                                    }
                                                ],
                                                "forwardingProfileZpaActions": [
                                                    {
                                                        "actionType": 1,
                                                        "DTLSTimeout": 9,
                                                        "latencyBasedServerEnablement": "0",
                                                        "latencyBasedServerMTEnablement": "0",
                                                        "lbsProbeInterval": 30,
                                                        "lbsProbeSampleSize": 5,
                                                        "lbsThresholdLimit": 1,
                                                        "mtuForZadapter": 0,
                                                        "networkType": 0,
                                                        "partnerInfo": {
                                                            "mtuForZadapter": 0,
                                                            "primaryTransport": 0
                                                        },
                                                        "primaryTransport": 0,
                                                        "TLSTimeout": 5
                                                    },
                                                    {
                                                        "actionType": 0,
                                                        "DTLSTimeout": 9,
                                                        "latencyBasedServerEnablement": 0,
                                                        "latencyBasedServerMTEnablement": 0,
                                                        "lbsProbeInterval": 30,
                                                        "lbsProbeSampleSize": 5,
                                                        "lbsThresholdLimit": 1,
                                                        "mtuForZadapter": 0,
                                                        "networkType": 1,
                                                        "partnerInfo": {
                                                            "mtuForZadapter": 0,
                                                            "primaryTransport": 0
                                                        },
                                                        "primaryTransport": 0,
                                                        "TLSTimeout": 5
                                                    },
                                                    {
                                                        "actionType": 1,
                                                        "DTLSTimeout": 9,
                                                        "latencyBasedServerEnablement": "0",
                                                        "latencyBasedServerMTEnablement": "0",
                                                        "lbsProbeInterval": 30,
                                                        "lbsProbeSampleSize": 5,
                                                        "lbsThresholdLimit": 1,
                                                        "mtuForZadapter": 0,
                                                        "networkType": 2,
                                                        "partnerInfo": {
                                                            "mtuForZadapter": 0,
                                                            "primaryTransport": 0
                                                        },
                                                        "primaryTransport": 0,
                                                        "TLSTimeout": 5
                                                    },
                                                    {
                                                        "actionType": 1,
                                                        "DTLSTimeout": 9,
                                                        "latencyBasedServerEnablement": 0,
                                                        "latencyBasedServerMTEnablement": 0,
                                                        "lbsProbeInterval": 30,
                                                        "lbsProbeSampleSize": 5,
                                                        "lbsThresholdLimit": 1,
                                                        "mtuForZadapter": 0,
                                                        "networkType": 3,
                                                        "partnerInfo": {
                                                            "mtuForZadapter": 0,
                                                            "primaryTransport": 0
                                                        },
                                                        "primaryTransport": 0,
                                                        "sendTrustedNetworkResultToZpa": 0,
                                                        "TLSTimeout": 5
                                                    }
                                                ],
                                                "hostname": "",
                                                "id": "-1",
                                                "lbsProbeInterval": "",
                                                "lbsProbeSampleSize": "",
                                                "lbsThresholdLimit": "",
                                                "name": "test_forward_profile_windows",
                                                "predefinedTnAll": False,
                                                "predefinedTrustedNetworks": False,
                                                "predefinedTrustedNetworksFilter": "",
                                                "probe_interval": "",
                                                "probe_sample_size": "",
                                                "proxyAction": "",
                                                "resolvedIps": "",
                                                "resolvedIpsForHostname": "",
                                                "skipTrustedCriteriaMatch": 0,
                                                "thrashhold_limit": "",
                                                "trustedDhcpServers": "",
                                                "trustedGateways": "",
                                                "trustedNetworkIds": [
                                                ],
                                                "trustedSubnets": "",
                                                "tunnel2Fallback": "",
                                                "undefined": ""
            },
            "zscalergov": {
                "active": 1,
                "addCondition": "",
                "conditionType": 1,
                "dnsSearchDomains": "",
                "dnsServers": "",
                "enableAllDefaultAdaptersTN": 0,
                "enableLWFDriver": 1,
                "enableSplitVpnTN": 0,
                "forwardingProfileActions": [
                    {
                        "actionType": 2,
                        "allowTLSFallback": 1,
                        "blockUnreachableDomainsTraffic": "0",
                        "customPac": "",
                        "dropIpv6IncludeTrafficInT2": 0,
                        "dropIpv6Traffic": 0,
                        "DTLSTimeout": 9,
                        "enablePacketTunnel": 0,
                        "latencyBasedServerEnablement": 0,
                        "latencyBasedZenEnablement": "0",
                        "lbsProbeInterval": 30,
                        "lbsProbeSampleSize": 5,
                        "lbsThresholdLimit": 50,
                        "mtuForZadapter": 0,
                        "networkType": 0,
                        "pathMtuDiscovery": 0,
                        "primaryTransport": 1,
                        "redirectWebTraffic": 0,
                        "systemProxyData": {
                            "bypassProxyForPrivateIP": 0,
                            "enableAutoDetect": 0,
                            "enablePAC": 1,
                            "enableProxyServer": 0,
                            "pacDataPath": "",
                            "pacURL": "",
                            "performGPUpdate": 0,
                            "proxyAction": 1,
                            "proxyServerAddress": "",
                            "proxyServerPort": 0
                        },
                        "TLSTimeout": 5,
                        "tunnel2FallbackType": 0,
                        "UDPTimeout": 9,
                        "useTunnel2ForProxiedWebTraffic": 0,
                        "zenProbeInterval": 60,
                        "zenProbeSampleSize": 5,
                        "zenThresholdLimit": "50"
                    },
                    {
                        "actionType": 2,
                        "allowTLSFallback": 1,
                        "blockUnreachableDomainsTraffic": "0",
                        "customPac": "",
                        "dropIpv6IncludeTrafficInT2": 0,
                        "dropIpv6Traffic": 0,
                        "DTLSTimeout": 9,
                        "enablePacketTunnel": 0,
                        "latencyBasedServerEnablement": 0,
                        "latencyBasedZenEnablement": "0",
                        "lbsProbeInterval": 30,
                        "lbsProbeSampleSize": 5,
                        "lbsThresholdLimit": 50,
                        "mtuForZadapter": 0,
                        "networkType": 1,
                        "pathMtuDiscovery": 0,
                        "primaryTransport": 1,
                        "redirectWebTraffic": 0,
                        "systemProxyData": {
                            "bypassProxyForPrivateIP": 0,
                            "enableAutoDetect": 0,
                            "enablePAC": 1,
                            "enableProxyServer": 0,
                            "pacDataPath": "",
                            "pacURL": "",
                            "performGPUpdate": 0,
                            "proxyAction": 1,
                            "proxyServerAddress": "",
                            "proxyServerPort": 0
                        },
                        "TLSTimeout": 5,
                        "tunnel2FallbackType": 0,
                        "UDPTimeout": 9,
                        "useTunnel2ForProxiedWebTraffic": 0,
                        "zenProbeInterval": 60,
                        "zenProbeSampleSize": 5,
                        "zenThresholdLimit": "50"
                    },
                    {
                        "actionType": 2,
                        "allowTLSFallback": 1,
                        "blockUnreachableDomainsTraffic": "0",
                        "customPac": "",
                        "dropIpv6IncludeTrafficInT2": 0,
                        "dropIpv6Traffic": 0,
                        "DTLSTimeout": 9,
                        "enablePacketTunnel": 0,
                        "latencyBasedServerEnablement": 0,
                        "latencyBasedZenEnablement": "0",
                        "lbsProbeInterval": 30,
                        "lbsProbeSampleSize": 5,
                        "lbsThresholdLimit": 50,
                        "mtuForZadapter": 0,
                        "networkType": 2,
                        "pathMtuDiscovery": 0,
                        "primaryTransport": 1,
                        "redirectWebTraffic": 0,
                        "systemProxyData": {
                            "bypassProxyForPrivateIP": 0,
                            "enableAutoDetect": 0,
                            "enablePAC": 1,
                            "enableProxyServer": 0,
                            "pacDataPath": "",
                            "pacURL": "",
                            "performGPUpdate": 0,
                            "proxyAction": 1,
                            "proxyServerAddress": "",
                            "proxyServerPort": 0
                        },
                        "TLSTimeout": 5,
                        "tunnel2FallbackType": 0,
                        "UDPTimeout": 9,
                        "useTunnel2ForProxiedWebTraffic": 0,
                        "zenProbeInterval": 60,
                        "zenProbeSampleSize": 5,
                        "zenThresholdLimit": "50"
                    },
                    {
                        "actionType": 2,
                        "allowTLSFallback": 1,
                        "blockUnreachableDomainsTraffic": "0",
                        "customPac": "",
                        "dropIpv6IncludeTrafficInT2": 0,
                        "dropIpv6Traffic": 0,
                        "DTLSTimeout": 9,
                        "enablePacketTunnel": 0,
                        "latencyBasedServerEnablement": 0,
                        "latencyBasedZenEnablement": "0",
                        "lbsProbeInterval": 30,
                        "lbsProbeSampleSize": 5,
                        "lbsThresholdLimit": 50,
                        "mtuForZadapter": 0,
                        "networkType": 3,
                        "pathMtuDiscovery": 0,
                        "primaryTransport": 1,
                        "redirectWebTraffic": 0,
                        "systemProxyData": {
                            "bypassProxyForPrivateIP": 0,
                            "enableAutoDetect": 0,
                            "enablePAC": 1,
                            "enableProxyServer": 0,
                            "pacDataPath": "",
                            "pacURL": "",
                            "performGPUpdate": 0,
                            "proxyAction": 1,
                            "proxyServerAddress": "",
                            "proxyServerPort": 0
                        },
                        "TLSTimeout": 5,
                        "tunnel2FallbackType": 0,
                        "UDPTimeout": 9,
                        "useTunnel2ForProxiedWebTraffic": 0,
                        "zenProbeInterval": 60,
                        "zenProbeSampleSize": 5,
                        "zenThresholdLimit": "50"
                    }
                ],
                "forwardingProfileZpaActions": [
                    {
                        "actionType": 1,
                        "DTLSTimeout": 9,
                        "latencyBasedServerEnablement": 0,
                        "lbsProbeInterval": 30,
                        "lbsProbeSampleSize": 5,
                        "lbsThresholdLimit": 50,
                        "mtuForZadapter": 0,
                        "networkType": 0,
                        "partnerInfo": {
                            "mtuForZadapter": 0,
                            "primaryTransport": 0
                        },
                        "primaryTransport": 0,
                        "TLSTimeout": 5
                    },
                    {
                        "actionType": 0,
                        "DTLSTimeout": 9,
                        "latencyBasedServerEnablement": 0,
                        "lbsProbeInterval": 30,
                        "lbsProbeSampleSize": 5,
                        "lbsThresholdLimit": 50,
                        "mtuForZadapter": 0,
                        "networkType": 1,
                        "partnerInfo": {
                            "mtuForZadapter": 0,
                            "primaryTransport": 0
                        },
                        "primaryTransport": 0,
                        "TLSTimeout": 5
                    },
                    {
                        "actionType": 1,
                        "DTLSTimeout": 9,
                        "latencyBasedServerEnablement": 0,
                        "lbsProbeInterval": 30,
                        "lbsProbeSampleSize": 5,
                        "lbsThresholdLimit": 50,
                        "mtuForZadapter": 0,
                        "networkType": 2,
                        "partnerInfo": {
                            "mtuForZadapter": 0,
                            "primaryTransport": 0
                        },
                        "primaryTransport": 0,
                        "TLSTimeout": 5
                    },
                    {
                        "actionType": 1,
                        "DTLSTimeout": 9,
                        "latencyBasedServerEnablement": 0,
                        "lbsProbeInterval": 30,
                        "lbsProbeSampleSize": 5,
                        "lbsThresholdLimit": 50,
                        "mtuForZadapter": 0,
                        "networkType": 3,
                        "partnerInfo": {
                            "mtuForZadapter": 0,
                            "primaryTransport": 0
                        },
                        "primaryTransport": 0,
                        "sendTrustedNetworkResultToZpa": 0,
                        "TLSTimeout": 5
                    }
                ],
                "hostname": "",
                "id": "-1",
                "name": "Testqqqqqq",
                "predefinedTnAll": False,
                "predefinedTrustedNetworks": False,
                "predefinedTrustedNetworksFilter": "",
                "probe_interval": "",
                "probe_sample_size": "",
                "proxyAction": "",
                "resolvedIps": "",
                "resolvedIpsForHostname": "",
                "skipTrustedCriteriaMatch": 0,
                "thrashhold_limit": "50",
                "trustedNetworkIds": [
                ],
                "tunnel2Fallback": "",
                "undefined": ""
            }
                                            
                                            }
        # -------------------------------------------------------
        # -------Get_Forwarding_Profile_List---------------------
        self.GET_FORWARDING_PROFILE_LIST_URL = "https://mobile.{}.net/webservice/api/web/forwardingProfile/listByCompany?page=1&pageSize=100".format \
            (self.cloud)
        self.GET_FORWARDING_PROFILE_LIST_HEADER = {'Accept': 'application/json, text/javascript, */*; q=0.01',
                                                   'Accept-Encoding': 'gzip, deflate, br',
                                                   'Accept-Language': 'en-US,en;q=0.9',
                                                   'auth-token': '',
                                                   'Connection': 'keep-alive',
                                                   'Cookie': '',
                                                   'Host': 'mobile.{}.net'.format(self.cloud),
                                                   'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                                   'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
                                                   'X-Requested-With': 'XMLHttpRequest'
                                                   }
        # -------------------------------------------------------
        # -------Force Remove Device from MA---------------------
        # 'https://mobile.{}.net/webservice/api/web/device/retireDevice?id=512599'
        self.Force_Remove_Device_URL = 'https://mobile.{}.net/webservice/api/web/device/retireDevice?id='.format \
            (self.cloud)
        self.Force_Remove_Device_Header = {'Accept': 'application/json, text/javascript, */*; q=0.01',
                                           'Accept-Encoding': 'gzip, deflate, br',
                                           'Accept-Language': 'en-US,en;q=0.9',
                                           'auth-token': '',
                                           'Connection': 'keep-alive',
                                           'Cookie': '',
                                           'Host': 'mobile.{}.net'.format(self.cloud),
                                           'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                           'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
                                           'X-Requested-With': 'XMLHttpRequest'
                                           }
        self.Force_Remove_Device_Payload = { 'id' :'' }

        # -------Soft Remove Device from MA---------------------
        # https://mobile.zscalerbeta.net/webservice/api/web/device/retireDevicesForCompany
        self.Soft_Remove_Device_URL = 'https://mobile.{}.net/webservice/api/web/device/retireDevicesForCompany'.format \
            (self.cloud)
        self.Soft_Remove_Device_Header = {'Accept': 'application/json, text/javascript, */*; q=0.01',
                                          'Accept-Encoding': 'gzip, deflate, br',
                                          'Accept-Language': 'en-US,en;q=0.9',
                                          'auth-token': '',
                                          'Connection': 'keep-alive',
                                          'Cookie': '',
                                          'Host': 'mobile.{}.net'.format(self.cloud),
                                          'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
                                          'X-Requested-With': 'XMLHttpRequest'
                                          }
        self.Soft_Remove_Device_Payload = { 0 :''}
        # -------Configure---------------------------------------
        self.EDIT_FORWARDING_PROFILE_URL = 'https://mobile.{}.net/webservice/api/web/forwardingProfile/edit'.format(
            self.cloud)
        self.EDIT_FORWARDING_PROFILE_HEADER = {'Accept': 'application/json, text/javascript, */*; q=0.01',
                                               'Accept-Encoding': 'gzip, deflate, br',
                                               'Accept-Language': 'en-US,en;q=0.9',
                                               'auth-token': '',
                                               'Connection': 'keep-alive',
                                               'Content-Length': '',
                                               'Content-Type': 'application/json',
                                               'Cookie': '',
                                               'Host': 'mobile.{}.net'.format(self.cloud),
                                               'Origin': 'https://mobile.{}.net'.format(self.cloud),
                                               'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                               'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
                                               'X-Requested-With': 'XMLHttpRequest'}
        # -------------------------------------------------------
        # -------Delete_Forwarding_Profile-----------------------
        self.Delete_Forwarding_Profile_Url = "https://mobile.{}.net/webservice/api/web/forwardingProfile/delete?id=".format(
            self.cloud)
        self.Delete_Forwarding_Profile_Header = {"Host": "mobile.{}.net".format(self.cloud),
                                                 "Connection": "keep-alive",
                                                 "Accept": "application/json, text/javascript, */*; q=0.01",
                                                 "X-Requested-With": "XMLHttpRequest",
                                                 "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                                                 "auth-token": '',
                                                 "Referer": "https://mobile.{}.net/index.html".format(self.cloud),
                                                 "Accept-Encoding": "gzip, deflate, br",
                                                 "Accept-Language": "en-US,en;q=0.9",
                                                 "Cookie": ''
                                                 }
        # -------------------------------------------------------
        # -------apply_uas---------------------------------------
        self.APPLY_UAS_URL = "https://mobile.{}.net/webservice/api/web/userAgentSuffix/setUserAgentSuffix".format(
            self.cloud)
        self.APPLY_UAS_HEADER = {'Accept': 'application/json, text/javascript, */*; q=0.01',
                                 'Accept-Encoding': 'gzip, deflate, br',
                                 'Accept-Language': 'en-US,en;q=0.9',
                                 'auth-token': '',
                                 'Connection': 'keep-alive',
                                 'Cookie': '',
                                 'Host': 'mobile.{}.net'.format(self.cloud),
                                 'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
                                 'X-Requested-With': 'XMLHttpRequest'
                                 }
        self.APPLY_UAS_PAYLOAD = {'enableUserAgentSuffixForAll': '1', 'userAgentSuffixForAll': ""}
        # -------------------------------------------------------
        # -------Upload_Custom_Cert----------------------------
        self.CUSTOM_CERT_URL = 'https://mobile.{}.net/webservice/api/web/company/setSslCertInfo'.format(cloud)
        self.CUSTOM_CERT_PAYLOAD = {"sslCert": ""}
        self.DELETE_CUSTOM_CERT_URL = 'https://mobile.{}.net/webservice/api/web/company/deleteSslCertInfo'.format(cloud)

        # -------Create_Device_Policy----------------------------
        '''
        self.GET_GROUPS_URL = "https://mobile.{}.net/webservice/api/web/groupsByCompany?page=1&pageSize=1000".format(
            cloud)
        self.GET_GROUPS_HEADER = {'Host': 'mobile.{}.net'.format(self.cloud),
                                  'Connection': 'keep-alive',
                                  'Cache-Control': 'max-age=0',
                                  'Accept': 'application/json, text/javascript, */*; q=0.01',
                                  'X-Requested-With': 'XMLHttpRequest',
                                  'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.98 Safari/537.36',
                                  'auth-token': '',
                                  'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                  'Accept-Encoding': 'gzip, deflate, sdch, br',
                                  'Accept-Language': 'en-US,en;q=0.8',
                                  'Cookie': ''}'''
        self.CREATE_DEVICE_POLICY_URL = "https://mobile.{}.net/webservice/api/web/policy/edit".format(self.cloud)
        self.web_insight = "https://admin.{}.net/zsapi/v1/transactionData/webRequest".format(self.cloud)
        self.CREATE_DEVICE_POLICY_HEADER = {'Connection': 'keep-alive',
                                            'Content-Length': '',
                                            'Pragma': 'no-cache',
                                            'Cache-Control': 'no-cache',
                                            'Accept': 'application/json, text/javascript, */*; q=0.01',

                                            'auth-token': '',
                                            'X-Requested-With': 'XMLHttpRequest',
                                            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36',

                                            'Accept-Encoding': 'gzip, deflate', 'sdch'
                                                                                'Accept-Language': 'en-US,en;q=0.8',
                                            'Cookie': ''
                                            }

        # device policy payload, works for MA <3.20
        # self.CREATE_DEVICE_POLICY_PAYLOAD = {"windowsPolicy":
        #                                          {"logout_password": "",
        #                                          "pacDataPath":"",
        #                                          "pacType":1,
        #                                          "prioritizeIPv4":"1",
        #                                           "cacheSystemProxy": "0",
        #                                           "uninstall_password": "",
        #                                           "disable_password": "",
        #                                           "install_ssl_certs": "0",
        #                                           "disableParallelIpv4andIpv6": 0,
        #                                           "disableLoopBackRestriction": "0",
        #                                           "overrideWPAD": "0",
        #                                           "restartWinHttpSvc": "0"},
        #                                      "appServiceIds": [],
        #                                      "name": "",
        #                                      "active": "1",
        #                                      "disable_password_for_zdx": "",
        #                                     "exit_password": "",
        #                                         "useV8JSEngine": "0",
        #                                      "pac_url": "",
        #                                      "description": "",
        #                                      "tunnelZappTraffic": "0",
        #                                      "groups": "",
        #                                      "groupAll": 0,
        #                                      "highlightActiveControl": "0",
        #                                      "windows-machineToken":"",
        #                                      "policyExtension":{
        #                                         "exitPassword": "",
        #                                         "zpaDisablePassword": "",
        #                                         "useV8JsEngine": "0",
        #                                         "zdxDisablePassword": "",
        #                                          "nonce": "",
        #                                          "packetTunnelDnsExcludeList": "",
        #                                          "packetTunnelDnsIncludeList": "",
        #                                         "vpnGateways": "",
        #                                      "packetTunnelExcludeList": "10.0.0.0/8,**********/12,***********/16,*********/4",
        #                                      "packetTunnelIncludeList": "0.0.0.0/0"
        #                                      },
        #                                      "logMode": 3,
        #                                       "windows-sccmconfig-offtrusted": "",
        #                                     "windows-sccmconfig-ontrusted": "",
        #                                     "windows-sccmconfig-vpntrusted": "",
        #                                     "disable_password_for_zpa": "",
        #                                      "logLevel": 0,
        #                                      "ruleOrder": 1,
        #                                      "users": [],
        #                                      "forwardingProfileId": 0,
        #                                      "logFileSize": 100,
        #                                      "reauth_period":12,
        #                                      "windows-ruleOrder": "",
        #                                      "undefined": "",
        #                                      "windows-onnet-filter": "",
        #                                      "windowslogMode": "",
        #                                      "reactivateWebSecurityMinutes": "0",
        #                                      "device_type": 3}

        # device policy payload for MA 3.21 second upgrade start
        self.CREATE_DEVICE_POLICY_PAYLOAD = {
                                        "active": "1",
                                        "adavnced_zpa_reauth_time": "",
                                        "allowZiaTest": "0",
                                        "allowZpaDisableWithoutPassword": "0",
                                        "allowZpaTest": "0",
                                        "appServiceIds": [
                                        ],
                                        "bypassAppIds": [
                                        ],
                                        "bypassCustomAppIds": [
                                        ],
                                        "dataProtectionService": "0",
                                        "description": "",
                                        "device_type": 3,
                                        "directFlowLoggingToggle": "0",
                                        "disable_password_for_zad": "",
                                        "disable_password_for_zdp": "",
                                        "disable_password_for_zdx": "",
                                        "disable_password_for_zpa": "",
                                        "disableDNSRouteExclusion": "0",
                                        "disasterRecovery": {
                                            "allowZiaTest": False,
                                            "allowZpaTest": False,
                                            "enableZiaDR": False,
                                            "enableZpaDR": False,
                                            "useZiaGlobalDb": False,
                                            "ziaCustomDbUrl": "",
                                            "ziaDomainName": "",
                                            "ziaDRMethod": 0,
                                            "ziaRSAPubKey": "",
                                            "ziaRSAPubKeyName": "",
                                            "zpaDomainName": "",
                                            "zpaRSAPubKey": "",
                                            "zpaRSAPubKeyName": ""
                                        },
                                        "enableAdvanceZpaReauth": "0",
                                        "enableCli": "1",
                                        "enableZCCRevert": "0",
                                        "enableZiaDR": "0",
                                        "enableZpaDR": "0",
                                        "exit_password": "",
                                        "forwardingProfileId": "0",
                                        "groupAll": 1,
                                        "groups": [
                                        ],
                                        "highlightActiveControl": "0",
                                        "installWindowsFirewallInboundRule": "1",
                                        "intranetFlowLoggingToggle": "0",
                                        "logFileSize": 100,
                                        "logLevel": 0,
                                        "logMode": -1,
                                        "machineTunnelIdp": "0",
                                        "name": "test1",
                                        "offTrustedDomainProfileToggle": "0",
                                        "onTrustedDomainProfileToggle": "0",
                                        "pac_url": "",
                                        "policyExtension": {
                                            "advanceZpaReauth": False,
                                            "advanceZpaReauthTime": None,
                                            "disableDNSRouteExclusion": "0",
                                            "enableAntiTampering": "0",
                                            "enableSetProxyOnVPNAdapters": 1,
                                            "enableZCCRevert": "0",
                                            "enableZdpService": "0",
                                            "exitPassword": "",
                                            "fallbackToGatewayDomain": "1",
                                            "followRoutingTable": "1",
                                            "generateCliPasswordContract": {
                                                "allowZpaDisableWithoutPassword": True,
                                                "enableCli": True
                                            },
                                            "interceptZIATrafficAllAdapters": 0,
                                            "machineIdpAuth": False,
                                            "nonce": "",
                                            "overrideATCmdByPolicy": 0,
                                            "packetTunnelDnsExcludeList": "",
                                            "packetTunnelDnsIncludeList": "",
                                            "packetTunnelExcludeList": "10.0.0.0/8,**********/12,***********/16,*********/4,***************,***********/16",
                                            "packetTunnelIncludeList": "0.0.0.0/0",
                                            "packetTunnelIncludeListForIPv6": "",
                                            "purgeKerberosPreferredDCCache": 0,
                                            "reactivateAntiTamperingTime": 0,
                                            "sourcePortBasedBypasses": "3389:*",
                                            "truncateLargeUDPDNSResponse": "0",
                                            "updateDnsSearchOrder": "1",
                                            "useDefaultAdapterForDNS": "1",
                                            "useProxyPortForT1": "0",
                                            "useProxyPortForT2": "0",
                                            "useV8JsEngine": "1",
                                            "useWsaPollForZpa": "0",
                                            "useZscalerNotificationFramework": "0",
                                            "vpnGateways": "",
                                            "zccRevertPassword": "",
                                            "zdDisablePassword": "",
                                            "zdpDisablePassword": "",
                                            "zdxDisablePassword": "",
                                            "zpaAuthExpOnNetIpChange": "0",
                                            "zpaAuthExpOnSleep": "0",
                                            "zpaAuthExpOnSysRestart": "0",
                                            "zpaAuthExpOnWinLogonSession": "1",
                                            "zpaDisablePassword": ""
                                        },
                                        "reactivate_anti_tampering": "0",
                                        "reactivateWebSecurityMinutes": "0",
                                        "reauth_period": 12,
                                        "revert_zcc_password": "",
                                        "ruleOrder": 1,
                                        "sendDisableServiceReason": "0",
                                        "splitVpnTrustedDomainProfileToggle": "0",
                                        "tunnelZappTraffic": "0",
                                        "undefined": "",
                                        "useDefaultAdapterForDNS": "1",
                                        "useRoutingTable": "1",
                                        "users": [
                                        ],
                                        "useV8JSEngine": "1",
                                        "useWsaPollForZpa": "0",
                                        "useZscalerNotificationFramework": "0",
                                        "vpnFlowLoggingToggle": "0",
                                        "vpnTrustedDomainProfileToggle": "0",
                                        "vpnTunnelFlowLoggingToggle": "0",
                                        "wfpDriver": "0",
                                        "windows-machineToken": "",
                                        "windows-onnet-filter": "",
                                        "windows-ruleOrder": "",
                                        "windows-sccmconfig-offtrusted": "",
                                        "windows-sccmconfig-ontrusted": "",
                                        "windows-sccmconfig-splitvpntrusted": "",
                                        "windows-sccmconfig-vpntrusted": "",
                                        "windows-ziaposture-filter": "",
                                        "windowslogMode": "",
                                        "windowsPolicy": {
                                            "allInboundTrafficConfig": None,
                                            "cacheSystemProxy": "0",
                                            "captivePortalConfig": "{\"enableCaptivePortalDetection\":1,\"enableFailOpen\":1,\"captivePortalWebSecDisableMinutes\":10,\"enableEmbeddedCaptivePortal\":0,\"automaticCapture\":1}",
                                            "disable_password": "",
                                            "disableLoopBackRestriction": "0",
                                            "disableParallelIpv4andIpv6": -1,
                                            "domainProfileDetectionConfig": None,
                                            "flowLoggerConfig": "{\"zpa\":0,\"vpn\":0,\"vpn_tunnel\":0,\"direct\":0,\"loopback\":0,\"zcc_blocked_traffic\":0,\"intranet\":0}",
                                            "install_ssl_certs": "0",
                                            "installWindowsFirewallInboundRule": 1,
                                            "logout_password": "",
                                            "overrideWPAD": "0",
                                            "pacDataPath": "",
                                            "pacType": 1,
                                            "prioritizeIPv4": "0",
                                            "removeExemptedContainers": "1",
                                            "restartWinHttpSvc": "0",
                                            "sccmConfig": None,
                                            "uninstall_password": "",
                                            "wfpDriver": 0
                                        },
                                        "windowsziaDRRecoveryMethod": "",
                                        "zccBlockedTrafficToggle": "0",
                                        "ziaDomainName": "",
                                        "ziaPacUrl": "",
                                        "ziaSecretKey": "",
                                        "zpaAuthExpOnNetIpChange": "0",
                                        "zpaAuthExpOnSleep": "0",
                                        "zpaAuthExpOnSysRestart": "0",
                                        "zpaDomainName": "",
                                        "zpaFlowLoggingToggle": "0",
                                        "zpaSecretKey": ""
                                    }
        # -------------------------------------------------------
        # -------Get_App_Profile_List----------------------------
        self.GET_APP_PROFILE_LIST_URL = "https://mobile.{}.net/webservice/api/web/policy/listByCompany?deviceType=DEVICE_TYPE&page=1&pageSize=100".format(
            self.cloud)
        self.GET_APP_PROFILE_LIST_HEADER = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'auth-token': '',
            'Connection': 'keep-alive',
            'Cookie': '',
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest'
        }
        # -------------------------------------------------------
        # -------Validate_Edit_Policy----------------------------
        self.EDIT_POLICY_URL = "https://mobile.{}.net/webservice/api/web/policy/edit".format(self.cloud)
        """
        self.EDIT_DEVICE_POLICY_PAYLOAD = {
                                               "windowsPolicy":{
                                                  "logout_password":"",
                                                  "uninstall_password":"",
                                                  "disable_password":"",
                                                  "install_ssl_certs":"1"
                                               },
                                               "reactivateWebSecurityMinutes":"0",
                                               "highlightActiveControl":0,
                                               "policyExtension":{
                                                  "packetTunnelExcludeList":"10.0.0.0/8,**********/12,***********/16,*********/4,0.0.0.0/0:8801-8810,0.0.0.0/0:3478-3479",
                                                  "packetTunnelIncludeList":"0.0.0.0/0",
                                                  "packetTunnelDnsIncludeList":"",
                                                  "packetTunnelDnsExcludeList":"",
                                                  "vpnGateways":"",
                                                  "exitPassword":"disable",
                                                  "zdxDisablePassword":"",
                                                  "zpaDisablePassword":""
                                               },
                                               "name":"",
                                               "windows-ruleOrder":"",
                                               "active":"1",
                                               "undefined":"",
                                               "pac_url":"",
                                               "windows-onnet-filter":"",
                                               "windowslogMode":"",
                                               "logLevel":0,
                                               "logFileSize":900,
                                               "description":"",
                                               "logMode":3,
                                               "ruleOrder":1,
                                               "forwardingProfileId":0,
                                               "reauth_period":12,
                                               "groupAll":0,
                                               "groups":[],
                                               "users":[

                                               ],
                                               "device_type":5
                                            }
        """
        # MAC device policy payload for MA <3.20
        # self.EDIT_DEVICE_POLICY_PAYLOAD_mac = {
        #                             "active": "1",
        #                             "appServiceIds": [
        #                             ],
        #                             "description": "POLICY_CREATED",
        #                             "device_type": 3,
        #                             "disable_password_for_zpa": "",
        #                             "exit_password": "",
        #                             "forwardingProfileId": 0,
        #                             "groupAll": 1,
        #                             "groups": [
        #                             ],
        #                             "highlightActiveControl": "0",
        #                             "logFileSize": 100,
        #                             "logLevel": 0,
        #                             "logMode": 3,
        #                             "name": "automation_win_10",
        #                             "pac_url": "",
        #                             "policyExtension": {
        #                                 "exitPassword": "",
        #                                 "machineIdpAuth": False,
        #                                 "nonce": "",
        #                                 "packetTunnelDnsExcludeList": "",
        #                                 "packetTunnelDnsIncludeList": "",
        #                                 "packetTunnelExcludeList": "10.0.0.0/8,**********/12,***********/16,*********/4,*************** ",
        #                                 "packetTunnelIncludeList": "0.0.0.0/0",
        #                                 "useV8JsEngine": "0",
        #                                 "vpnGateways": "1.3.5.7,connect.corp.zscaler.com,66.201.57.143",
        #                                 "zdxDisablePassword": "",
        #                                 "zpaDisablePassword": ""
        #                             },
        #                             "reactivateWebSecurityMinutes": "0",
        #                             "reauth_period": 12,
        #                             "ruleOrder": 1,
        #                             "undefined": "",
        #                             "users": [
        #                             ],
        #                             "mac-machineToken": "",
        #                             "mac-onnet-filter": "",
        #                             "mac-ruleOrder": "",
        #                             "mac-sccmconfig-offtrusted": "",
        #                             "mac-sccmconfig-ontrusted": "",
        #                             "mac-sccmconfig-vpntrusted": "",
        #                             "maclogMode": "",
        #                             "macPolicy": {
        #                                 "cacheSystemProxy": "0",
        #                                 "disable_password": "",
        #                                 "disableLoopBackRestriction": "0",
        #                                 "disableParallelIpv4andIpv6": -1,
        #                                 "install_ssl_certs": "1",
        #                                 "logout_password": "",
        #                                 "overrideWPAD": "0",
        #                                 "pacDataPath": "",
        #                                 "pacType": 1,
        #                                 "prioritizeIPv4": "0",
        #                                 "restartWinHttpSvc": "0",
        #                                 "sccmConfig": None,
        #                                 "uninstall_password": ""
        #                             }
        #                             }

        # MAC device policy payload for MA 3.21
        self.EDIT_DEVICE_POLICY_PAYLOAD_mac = {
                    "active": "1",
                    "adavnced_zpa_reauth_time": "",
                    "allowZiaTest": "0",
                    "allowZpaDisableWithoutPassword": "0",
                    "allowZpaTest": "0",
                    "appServiceIds": [
                    ],
                    "bypassAppIds": [
                    ],
                    "bypassCustomAppIds": [
                    ],
                    "dataProtectionService": "0",
                    "description": "",
                    "directFlowLoggingToggle": "0",
                    "disable_password_for_zad": "",
                    "disable_password_for_zdp": "",
                    "disable_password_for_zdx": "",
                    "disable_password_for_zpa": "",
                    "disableDNSRouteExclusion": "0",
                    "disasterRecovery": {
                        "allowZiaTest": False,
                        "allowZpaTest": False,
                        "enableZiaDR": False,
                        "enableZpaDR": False,
                        "useZiaGlobalDb": False,
                        "ziaCustomDbUrl": "",
                        "ziaDomainName": "",
                        "ziaDRMethod": 0,
                        "ziaRSAPubKey": "",
                        "ziaRSAPubKeyName": "",
                        "zpaDomainName": "",
                        "zpaRSAPubKey": "",
                        "zpaRSAPubKeyName": ""
                    },
                    "enableAdvanceZpaReauth": "0",
                    "enableCli": "1",
                    "enableZCCRevert": "0",
                    "enableZiaDR": "0",
                    "enableZpaDR": "0",
                    "exit_password": "",
                    "forwardingProfileId": "0",
                    "groupAll": 1,
                    "groups": [
                    ],
                    "highlightActiveControl": "0",
                    "installWindowsFirewallInboundRule": "1",
                    "intranetFlowLoggingToggle": "0",
                    "logFileSize": 100,
                    "logLevel": 0,
                    "logMode": -1,
                    "machineTunnelIdp": "0",
                    "name": "test1",
                    "offTrustedDomainProfileToggle": "0",
                    "onTrustedDomainProfileToggle": "0",
                    "pac_url": "",
                    "policyExtension": {
                        "advanceZpaReauth": False,
                        "advanceZpaReauthTime": None,
                        "disableDNSRouteExclusion": "0",
                        "enableAntiTampering": "0",
                        "enableSetProxyOnVPNAdapters": 1,
                        "enableZCCRevert": "0",
                        "enableZdpService": "0",
                        "exitPassword": "",
                        "fallbackToGatewayDomain": "1",
                        "followRoutingTable": "1",
                        "generateCliPasswordContract": {
                            "allowZpaDisableWithoutPassword": True,
                            "enableCli": True
                        },
                        "interceptZIATrafficAllAdapters": 0,
                        "machineIdpAuth": False,
                        "nonce": "",
                        "overrideATCmdByPolicy": 0,
                        "packetTunnelDnsExcludeList": "",
                        "packetTunnelDnsIncludeList": "",
                        "packetTunnelExcludeList": "10.0.0.0/8,**********/12,***********/16,*********/4,***************,***********/16",
                        "packetTunnelIncludeList": "0.0.0.0/0",
                        "packetTunnelIncludeListForIPv6": "",
                        "purgeKerberosPreferredDCCache": 0,
                        "reactivateAntiTamperingTime": 0,
                        "sourcePortBasedBypasses": "3389:*",
                        "truncateLargeUDPDNSResponse": "0",
                        "updateDnsSearchOrder": "1",
                        "useDefaultAdapterForDNS": "1",
                        "useProxyPortForT1": "0",
                        "useProxyPortForT2": "0",
                        "useV8JsEngine": "1",
                        "useWsaPollForZpa": "0",
                        "useZscalerNotificationFramework": "0",
                        "vpnGateways": "",
                        "zccRevertPassword": "",
                        "zdDisablePassword": "",
                        "zdpDisablePassword": "",
                        "zdxDisablePassword": "",
                        "zpaAuthExpOnNetIpChange": "0",
                        "zpaAuthExpOnSleep": "0",
                        "zpaAuthExpOnSysRestart": "0",
                        "zpaAuthExpOnWinLogonSession": "1",
                        "zpaDisablePassword": ""
                    },
                    "reactivate_anti_tampering": "0",
                    "reactivateWebSecurityMinutes": "0",
                    "reauth_period": 12,
                    "revert_zcc_password": "",
                    "ruleOrder": 1,
                    "sendDisableServiceReason": "0",
                    "splitVpnTrustedDomainProfileToggle": "0",
                    "tunnelZappTraffic": "0",
                    "undefined": "",
                    "useDefaultAdapterForDNS": "1",
                    "useRoutingTable": "1",
                    "users": [
                    ],
                    "useV8JSEngine": "1",
                    "useWsaPollForZpa": "0",
                    "useZscalerNotificationFramework": "0",
                    "vpnFlowLoggingToggle": "0",
                    "vpnTrustedDomainProfileToggle": "0",
                    "vpnTunnelFlowLoggingToggle": "0",
                    "wfpDriver": "0",
                    "windows-machineToken": "",
                    "windows-onnet-filter": "",
                    "windows-ruleOrder": "",
                    "windows-sccmconfig-offtrusted": "",
                    "windows-sccmconfig-ontrusted": "",
                    "windows-sccmconfig-splitvpntrusted": "",
                    "windows-sccmconfig-vpntrusted": "",
                    "windows-ziaposture-filter": "",
                    "windowslogMode": "",
                    "windowsPolicy": {
                        "allInboundTrafficConfig": None,
                        "cacheSystemProxy": "0",
                        "captivePortalConfig": "{\"enableCaptivePortalDetection\":1,\"enableFailOpen\":1,\"captivePortalWebSecDisableMinutes\":10,\"enableEmbeddedCaptivePortal\":0,\"automaticCapture\":1}",
                        "disable_password": "",
                        "disableLoopBackRestriction": "0",
                        "disableParallelIpv4andIpv6": -1,
                        "domainProfileDetectionConfig": None,
                        "flowLoggerConfig": "{\"zpa\":0,\"vpn\":0,\"vpn_tunnel\":0,\"direct\":0,\"loopback\":0,\"zcc_blocked_traffic\":0,\"intranet\":0}",
                        "install_ssl_certs": "0",
                        "installWindowsFirewallInboundRule": 1,
                        "logout_password": "",
                        "overrideWPAD": "0",
                        "pacDataPath": "",
                        "pacType": 1,
                        "prioritizeIPv4": "0",
                        "removeExemptedContainers": "1",
                        "restartWinHttpSvc": "0",
                        "sccmConfig": None,
                        "uninstall_password": "",
                        "wfpDriver": 0
                    },
                    "windowsziaDRRecoveryMethod": "",
                    "zccBlockedTrafficToggle": "0",
                    "ziaDomainName": "",
                    "ziaPacUrl": "",
                    "ziaSecretKey": "",
                    "zpaAuthExpOnNetIpChange": "0",
                    "zpaAuthExpOnSleep": "0",
                    "zpaAuthExpOnSysRestart": "0",
                    "zpaDomainName": "",
                    "zpaFlowLoggingToggle": "0",
                    "zpaSecretKey": ""
                }
                        # WIN device policy payload for MA <3.20
                        # self.EDIT_DEVICE_POLICY_PAYLOAD_win = {
                        #                             "active": "1",
                        #                             "appServiceIds": [
                        #                             ],
                        #                             "description": "POLICY_CREATED",
                        #                             "device_type": 3,
                        #                             "disable_password_for_zpa": "",
        #                             "exit_password": "",
        #                             "forwardingProfileId": 0,
        #                             "groupAll": 1,
        #                             "groups": [
        #                             ],
        #                             "highlightActiveControl": "0",
        #                             "logFileSize": 100,
        #                             "logLevel": 0,
        #                             "logMode": 3,
        #                             "name": "automation_win_10",
        #                             "pac_url": "",
        #                             "policyExtension": {
        #                                 "exitPassword": "",
        #                                 "machineIdpAuth": False,
        #                                 "nonce": "",
        #                                 "packetTunnelDnsExcludeList": "",
        #                                 "packetTunnelDnsIncludeList": "",
        #                                 "packetTunnelExcludeList": "10.0.0.0/8,**********/12,***********/16,*********/4,*************** ",
        #                                 "packetTunnelIncludeList": "0.0.0.0/0",
        #                                 "useV8JsEngine": "0",
        #                                 "vpnGateways": "1.3.5.7,connect.corp.zscaler.com,66.201.57.143",
        #                                 "zdxDisablePassword": "",
        #                                 "zpaDisablePassword": ""
        #                             },
        #                             "reactivateWebSecurityMinutes": "0",
        #                             "reauth_period": 12,
        #                             "ruleOrder": 1,
        #                             "undefined": "",
        #                             "users": [
        #                             ],
        #                             "windows-machineToken": "",
        #                             "windows-onnet-filter": "",
        #                             "windows-ruleOrder": "",
        #                             "windows-sccmconfig-offtrusted": "",
        #                             "windows-sccmconfig-ontrusted": "",
        #                             "windows-sccmconfig-vpntrusted": "",
        #                             "windowslogMode": "",
        #                             "windowsPolicy": {
        #                                 "cacheSystemProxy": "0",
        #                                 "disable_password": "",
        #                                 "disableLoopBackRestriction": "0",
        #                                 "disableParallelIpv4andIpv6": -1,
        #                                 "install_ssl_certs": "1",
        #                                 "logout_password": "",
        #                                 "overrideWPAD": "0",
        #                                 "pacDataPath": "",
        #                                 "pacType": 1,
        #                                 "prioritizeIPv4": "0",
        #                                 "restartWinHttpSvc": "0",
        #                                 "sccmConfig": None,
        #                                 "uninstall_password": ""
        #                             }
        #                             }

        # Win device policy payload for MA 3.21
        self.EDIT_DEVICE_POLICY_PAYLOAD_win = {
                                                "active": "1",
                                                "adavnced_zpa_reauth_time": "",
                                                "allowZiaTest": "0",
                                                "allowZpaDisableWithoutPassword": "0",
                                                "allowZpaTest": "0",
                                                "appServiceIds": [
                                                ],
                                                "bypassAppIds": [
                                                ],
                                                "bypassCustomAppIds": [
                                                ],
                                                "dataProtectionService": "0",
                                                "description": "",
                                                "directFlowLoggingToggle": "0",
                                                "disable_password_for_zad": "",
                                                "disable_password_for_zdp": "",
                                                "disable_password_for_zdx": "",
                                                "disable_password_for_zpa": "",
                                                "disableDNSRouteExclusion": "0",
                                                "disasterRecovery": {
                                                    "allowZiaTest": False,
                                                    "allowZpaTest": False,
                                                    "enableZiaDR": False,
                                                    "enableZpaDR": False,
                                                    "useZiaGlobalDb": False,
                                                    "ziaCustomDbUrl": "",
                                                    "ziaDomainName": "",
                                                    "ziaDRMethod": 0,
                                                    "ziaRSAPubKey": "",
                                                    "ziaRSAPubKeyName": "",
                                                    "zpaDomainName": "",
                                                    "zpaRSAPubKey": "",
                                                    "zpaRSAPubKeyName": ""
                                                },
                                                "enableAdvanceZpaReauth": "0",
                                                "enableCli": "1",
                                                "enableZCCRevert": "0",
                                                "enableZiaDR": "0",
                                                "enableZpaDR": "0",
                                                "exit_password": "",
                                                "forwardingProfileId": "0",
                                                "groupAll": 1,
                                                "groups": [
                                                ],
                                                "highlightActiveControl": "0",
                                                "installWindowsFirewallInboundRule": "1",
                                                "intranetFlowLoggingToggle": "0",
                                                "logFileSize": 100,
                                                "logLevel": 0,
                                                "logMode": -1,
                                                "machineTunnelIdp": "0",
                                                "name": "test1",
                                                "offTrustedDomainProfileToggle": "0",
                                                "onTrustedDomainProfileToggle": "0",
                                                "pac_url": "",
                                                "policyExtension": {
                                                    "advanceZpaReauth": False,
                                                    "advanceZpaReauthTime": None,
                                                    "disableDNSRouteExclusion": "0",
                                                    "enableAntiTampering": "0",
                                                    "enableSetProxyOnVPNAdapters": 1,
                                                    "enableZCCRevert": "0",
                                                    "enableZdpService": "0",
                                                    "exitPassword": "",
                                                    "fallbackToGatewayDomain": "1",
                                                    "followRoutingTable": "1",
                                                    "generateCliPasswordContract": {
                                                        "allowZpaDisableWithoutPassword":True,
                                                        "enableCli": True
                                                    },
                                                    "interceptZIATrafficAllAdapters": 0,
                                                    "machineIdpAuth": False,
                                                    "nonce": "",
                                                    "overrideATCmdByPolicy": 0,
                                                    "packetTunnelDnsExcludeList": "",
                                                    "packetTunnelDnsIncludeList": "",
                                                    "packetTunnelExcludeList": "10.0.0.0/8,**********/12,***********/16,*********/4,***************,***********/16",
                                                    "packetTunnelIncludeList": "0.0.0.0/0",
                                                    "packetTunnelIncludeListForIPv6": "",
                                                    "purgeKerberosPreferredDCCache": 0,
                                                    "reactivateAntiTamperingTime": 0,
                                                    "sourcePortBasedBypasses": "3389:*",
                                                    "truncateLargeUDPDNSResponse": "0",
                                                    "updateDnsSearchOrder": "1",
                                                    "useDefaultAdapterForDNS": "1",
                                                    "useProxyPortForT1": "0",
                                                    "useProxyPortForT2": "0",
                                                    "useV8JsEngine": "1",
                                                    "useWsaPollForZpa": "0",
                                                    "useZscalerNotificationFramework": "0",
                                                    "vpnGateways": "",
                                                    "zccRevertPassword": "",
                                                    "zdDisablePassword": "",
                                                    "zdpDisablePassword": "",
                                                    "zdxDisablePassword": "",
                                                    "zpaAuthExpOnNetIpChange": "0",
                                                    "zpaAuthExpOnSleep": "0",
                                                    "zpaAuthExpOnSysRestart": "0",
                                                    "zpaAuthExpOnWinLogonSession": "1",
                                                    "zpaDisablePassword": ""
                                                },
                                                "reactivate_anti_tampering": "0",
                                                "reactivateWebSecurityMinutes": "0",
                                                "reauth_period": 12,
                                                "revert_zcc_password": "",
                                                "ruleOrder": 1,
                                                "sendDisableServiceReason": "0",
                                                "splitVpnTrustedDomainProfileToggle": "0",
                                                "tunnelZappTraffic": "0",
                                                "undefined": "",
                                                "useDefaultAdapterForDNS": "1",
                                                "useRoutingTable": "1",
                                                "users": [
                                                ],
                                                "useV8JSEngine": "1",
                                                "useWsaPollForZpa": "0",
                                                "useZscalerNotificationFramework": "0",
                                                "vpnFlowLoggingToggle": "0",
                                                "vpnTrustedDomainProfileToggle": "0",
                                                "vpnTunnelFlowLoggingToggle": "0",
                                                "wfpDriver": "0",
                                                "windows-machineToken": "",
                                                "windows-onnet-filter": "",
                                                "windows-ruleOrder": "",
                                                "windows-sccmconfig-offtrusted": "",
                                                "windows-sccmconfig-ontrusted": "",
                                                "windows-sccmconfig-splitvpntrusted": "",
                                                "windows-sccmconfig-vpntrusted": "",
                                                "windows-ziaposture-filter": "",
                                                "windowslogMode": "",
                                                "windowsPolicy": {
                                                    "allInboundTrafficConfig": None,
                                                    "cacheSystemProxy": "0",
                                                    "captivePortalConfig": "{\"enableCaptivePortalDetection\":1,\"enableFailOpen\":1,\"captivePortalWebSecDisableMinutes\":10,\"enableEmbeddedCaptivePortal\":0,\"automaticCapture\":1}",
                                                    "disable_password": "",
                                                    "disableLoopBackRestriction": "0",
                                                    "disableParallelIpv4andIpv6": -1,
                                                    "domainProfileDetectionConfig": None,
                                                    "flowLoggerConfig": "{\"zpa\":0,\"vpn\":0,\"vpn_tunnel\":0,\"direct\":0,\"loopback\":0,\"zcc_blocked_traffic\":0,\"intranet\":0}",
                                                    "install_ssl_certs": "0",
                                                    "installWindowsFirewallInboundRule": 1,
                                                    "logout_password": "",
                                                    "overrideWPAD": "0",
                                                    "pacDataPath": "",
                                                    "pacType": 1,
                                                    "prioritizeIPv4": "0",
                                                    "removeExemptedContainers": "1",
                                                    "restartWinHttpSvc": "0",
                                                    "sccmConfig": None,
                                                    "uninstall_password": "",
                                                    "wfpDriver": 0
                                                },
                                                "windowsziaDRRecoveryMethod": "",
                                                "zccBlockedTrafficToggle": "0",
                                                "ziaDomainName": "",
                                                "ziaPacUrl": "",
                                                "ziaSecretKey": "",
                                                "zpaAuthExpOnNetIpChange": "0",
                                                "zpaAuthExpOnSleep": "0",
                                                "zpaAuthExpOnSysRestart": "0",
                                                "zpaDomainName": "",
                                                "zpaFlowLoggingToggle": "0",
                                                "zpaSecretKey": ""
                                            }
        
        self.EDIT_DEVICE_POLICY_PAYLOAD_linux = {
                                                "active": "1",
                                                "adavnced_zpa_reauth_time": "",
                                                "allowZiaTest": "0",
                                                "allowZpaDisableWithoutPassword": "0",
                                                "allowZpaTest": "0",
                                                "appServiceIds": [
                                                ],
                                                "bypassAppIds": [
                                                ],
                                                "bypassCustomAppIds": [
                                                ],
                                                "dataProtectionService": "0",
                                                "description": "",
                                                "directFlowLoggingToggle": "0",
                                                "disable_password_for_zad": "",
                                                "disable_password_for_zdp": "",
                                                "disable_password_for_zdx": "",
                                                "disable_password_for_zpa": "",
                                                "disableDNSRouteExclusion": "0",
                                                "disasterRecovery": {
                                                    "allowZiaTest": False,
                                                    "allowZpaTest": False,
                                                    "enableZiaDR": False,
                                                    "enableZpaDR": False,
                                                    "useZiaGlobalDb": False,
                                                    "ziaCustomDbUrl": "",
                                                    "ziaDomainName": "",
                                                    "ziaDRMethod": 0,
                                                    "ziaRSAPubKey": "",
                                                    "ziaRSAPubKeyName": "",
                                                    "zpaDomainName": "",
                                                    "zpaRSAPubKey": "",
                                                    "zpaRSAPubKeyName": ""
                                                },
                                                "enableAdvanceZpaReauth": "0",
                                                "enableCli": "1",
                                                "enableZCCRevert": "0",
                                                "enableZiaDR": "0",
                                                "enableZpaDR": "0",
                                                "exit_password": "",
                                                "forwardingProfileId": "0",
                                                "groupAll": 1,
                                                "groups": [
                                                ],
                                                "highlightActiveControl": "0",
                                                "installWindowsFirewallInboundRule": "1",
                                                "intranetFlowLoggingToggle": "0",
                                                "logFileSize": 100,
                                                "logLevel": 0,
                                                "logMode": -1,
                                                "machineTunnelIdp": "0",
                                                "name": "test1",
                                                "offTrustedDomainProfileToggle": "0",
                                                "onTrustedDomainProfileToggle": "0",
                                                "pac_url": "",
                                                "policyExtension": {
                                                    "advanceZpaReauth": False,
                                                    "advanceZpaReauthTime": None,
                                                    "disableDNSRouteExclusion": "0",
                                                    "enableAntiTampering": "0",
                                                    "enableSetProxyOnVPNAdapters": 1,
                                                    "enableZCCRevert": "0",
                                                    "enableZdpService": "0",
                                                    "exitPassword": "",
                                                    "fallbackToGatewayDomain": "1",
                                                    "followRoutingTable": "1",
                                                    "generateCliPasswordContract": {
                                                        "allowZpaDisableWithoutPassword":True,
                                                        "enableCli": True
                                                    },
                                                    "interceptZIATrafficAllAdapters": 0,
                                                    "machineIdpAuth": False,
                                                    "nonce": "",
                                                    "overrideATCmdByPolicy": 0,
                                                    "packetTunnelDnsExcludeList": "",
                                                    "packetTunnelDnsIncludeList": "",
                                                    "packetTunnelExcludeList": "10.0.0.0/8,**********/12,***********/16,*********/4,***************,***********/16",
                                                    "packetTunnelIncludeList": "0.0.0.0/0",
                                                    "packetTunnelIncludeListForIPv6": "",
                                                    "purgeKerberosPreferredDCCache": 0,
                                                    "reactivateAntiTamperingTime": 0,
                                                    "sourcePortBasedBypasses": "3389:*",
                                                    "truncateLargeUDPDNSResponse": "0",
                                                    "updateDnsSearchOrder": "1",
                                                    "useDefaultAdapterForDNS": "1",
                                                    "useProxyPortForT1": "0",
                                                    "useProxyPortForT2": "0",
                                                    "useV8JsEngine": "1",
                                                    "useWsaPollForZpa": "0",
                                                    "useZscalerNotificationFramework": "0",
                                                    "vpnGateways": "",
                                                    "zccRevertPassword": "",
                                                    "zdDisablePassword": "",
                                                    "zdpDisablePassword": "",
                                                    "zdxDisablePassword": "",
                                                    "zpaAuthExpOnNetIpChange": "0",
                                                    "zpaAuthExpOnSleep": "0",
                                                    "zpaAuthExpOnSysRestart": "0",
                                                    "zpaAuthExpOnWinLogonSession": "1",
                                                    "zpaDisablePassword": ""
                                                },
                                                "reactivate_anti_tampering": "0",
                                                "reactivateWebSecurityMinutes": "0",
                                                "reauth_period": 12,
                                                "revert_zcc_password": "",
                                                "ruleOrder": 1,
                                                "sendDisableServiceReason": "0",
                                                "splitVpnTrustedDomainProfileToggle": "0",
                                                "tunnelZappTraffic": "0",
                                                "undefined": "",
                                                "useDefaultAdapterForDNS": "1",
                                                "useRoutingTable": "1",
                                                "users": [
                                                ],
                                                "useV8JSEngine": "1",
                                                "useWsaPollForZpa": "0",
                                                "useZscalerNotificationFramework": "0",
                                                "vpnFlowLoggingToggle": "0",
                                                "vpnTrustedDomainProfileToggle": "0",
                                                "vpnTunnelFlowLoggingToggle": "0",
                                                "wfpDriver": "0",
                                                "windows-machineToken": "",
                                                "windows-onnet-filter": "",
                                                "windows-ruleOrder": "",
                                                "windows-sccmconfig-offtrusted": "",
                                                "windows-sccmconfig-ontrusted": "",
                                                "windows-sccmconfig-splitvpntrusted": "",
                                                "windows-sccmconfig-vpntrusted": "",
                                                "windows-ziaposture-filter": "",
                                                "windowslogMode": "",
                                                "windowsPolicy": {
                                                    "allInboundTrafficConfig": None,
                                                    "cacheSystemProxy": "0",
                                                    "captivePortalConfig": "{\"enableCaptivePortalDetection\":1,\"enableFailOpen\":1,\"captivePortalWebSecDisableMinutes\":10,\"enableEmbeddedCaptivePortal\":0,\"automaticCapture\":1}",
                                                    "disable_password": "",
                                                    "disableLoopBackRestriction": "0",
                                                    "disableParallelIpv4andIpv6": -1,
                                                    "domainProfileDetectionConfig": None,
                                                    "flowLoggerConfig": "{\"zpa\":0,\"vpn\":0,\"vpn_tunnel\":0,\"direct\":0,\"loopback\":0,\"zcc_blocked_traffic\":0,\"intranet\":0}",
                                                    "install_ssl_certs": "0",
                                                    "installWindowsFirewallInboundRule": 1,
                                                    "logout_password": "",
                                                    "overrideWPAD": "0",
                                                    "pacDataPath": "",
                                                    "pacType": 1,
                                                    "prioritizeIPv4": "0",
                                                    "removeExemptedContainers": "1",
                                                    "restartWinHttpSvc": "0",
                                                    "sccmConfig": None,
                                                    "uninstall_password": "",
                                                    "wfpDriver": 0
                                                },
                                                "windowsziaDRRecoveryMethod": "",
                                                "zccBlockedTrafficToggle": "0",
                                                "ziaDomainName": "",
                                                "ziaPacUrl": "",
                                                "ziaSecretKey": "",
                                                "zpaAuthExpOnNetIpChange": "0",
                                                "zpaAuthExpOnSleep": "0",
                                                "zpaAuthExpOnSysRestart": "0",
                                                "zpaDomainName": "",
                                                "zpaFlowLoggingToggle": "0",
                                                "zpaSecretKey": ""
                                            }
        
        self.EDIT_DEVICE_POLICY_PAYLOAD_android = {
                                                "active": "1",
                                                "adavnced_zpa_reauth_time": "",
                                                "allowZiaTest": "0",
                                                "allowZpaDisableWithoutPassword": "0",
                                                "allowZpaTest": "0",
                                                "appServiceIds": [
                                                ],
                                                "bypassAppIds": [
                                                ],
                                                "bypassCustomAppIds": [
                                                ],
                                                "dataProtectionService": "0",
                                                "description": "",
                                                "directFlowLoggingToggle": "0",
                                                "disable_password_for_zad": "",
                                                "disable_password_for_zdp": "",
                                                "disable_password_for_zdx": "",
                                                "disable_password_for_zpa": "",
                                                "disableDNSRouteExclusion": "0",
                                                "disasterRecovery": {
                                                    "allowZiaTest": False,
                                                    "allowZpaTest": False,
                                                    "enableZiaDR": False,
                                                    "enableZpaDR": False,
                                                    "useZiaGlobalDb": False,
                                                    "ziaCustomDbUrl": "",
                                                    "ziaDomainName": "",
                                                    "ziaDRMethod": 0,
                                                    "ziaRSAPubKey": "",
                                                    "ziaRSAPubKeyName": "",
                                                    "zpaDomainName": "",
                                                    "zpaRSAPubKey": "",
                                                    "zpaRSAPubKeyName": ""
                                                },
                                                "enableAdvanceZpaReauth": "0",
                                                "enableCli": "1",
                                                "enableZCCRevert": "0",
                                                "enableZiaDR": "0",
                                                "enableZpaDR": "0",
                                                "exit_password": "",
                                                "forwardingProfileId": "0",
                                                "groupAll": 1,
                                                "groups": [
                                                ],
                                                "highlightActiveControl": "0",
                                                "installWindowsFirewallInboundRule": "1",
                                                "intranetFlowLoggingToggle": "0",
                                                "logFileSize": 100,
                                                "logLevel": 0,
                                                "logMode": -1,
                                                "machineTunnelIdp": "0",
                                                "name": "test1",
                                                "offTrustedDomainProfileToggle": "0",
                                                "onTrustedDomainProfileToggle": "0",
                                                "pac_url": "",
                                                "policyExtension": {
                                                    "advanceZpaReauth": False,
                                                    "advanceZpaReauthTime": None,
                                                    "disableDNSRouteExclusion": "0",
                                                    "enableAntiTampering": "0",
                                                    "enableSetProxyOnVPNAdapters": 1,
                                                    "enableZCCRevert": "0",
                                                    "enableZdpService": "0",
                                                    "exitPassword": "",
                                                    "fallbackToGatewayDomain": "1",
                                                    "followRoutingTable": "1",
                                                    "generateCliPasswordContract": {
                                                        "allowZpaDisableWithoutPassword":True,
                                                        "enableCli": True
                                                    },
                                                    "interceptZIATrafficAllAdapters": 0,
                                                    "machineIdpAuth": False,
                                                    "nonce": "",
                                                    "overrideATCmdByPolicy": 0,
                                                    "packetTunnelDnsExcludeList": "",
                                                    "packetTunnelDnsIncludeList": "",
                                                    "packetTunnelExcludeList": "10.0.0.0/8,**********/12,***********/16,*********/4,***************,***********/16",
                                                    "packetTunnelIncludeList": "0.0.0.0/0",
                                                    "packetTunnelIncludeListForIPv6": "",
                                                    "purgeKerberosPreferredDCCache": 0,
                                                    "reactivateAntiTamperingTime": 0,
                                                    "sourcePortBasedBypasses": "3389:*",
                                                    "truncateLargeUDPDNSResponse": "0",
                                                    "updateDnsSearchOrder": "1",
                                                    "useDefaultAdapterForDNS": "1",
                                                    "useProxyPortForT1": "0",
                                                    "useProxyPortForT2": "0",
                                                    "useV8JsEngine": "1",
                                                    "useWsaPollForZpa": "0",
                                                    "useZscalerNotificationFramework": "0",
                                                    "vpnGateways": "",
                                                    "zccRevertPassword": "",
                                                    "zdDisablePassword": "",
                                                    "zdpDisablePassword": "",
                                                    "zdxDisablePassword": "",
                                                    "zpaAuthExpOnNetIpChange": "0",
                                                    "zpaAuthExpOnSleep": "0",
                                                    "zpaAuthExpOnSysRestart": "0",
                                                    "zpaAuthExpOnWinLogonSession": "1",
                                                    "zpaDisablePassword": ""
                                                },
                                                "reactivate_anti_tampering": "0",
                                                "reactivateWebSecurityMinutes": "0",
                                                "reauth_period": 12,
                                                "revert_zcc_password": "",
                                                "ruleOrder": 1,
                                                "sendDisableServiceReason": "0",
                                                "splitVpnTrustedDomainProfileToggle": "0",
                                                "tunnelZappTraffic": "0",
                                                "undefined": "",
                                                "useDefaultAdapterForDNS": "1",
                                                "useRoutingTable": "1",
                                                "users": [
                                                ],
                                                "useV8JSEngine": "1",
                                                "useWsaPollForZpa": "0",
                                                "useZscalerNotificationFramework": "0",
                                                "vpnFlowLoggingToggle": "0",
                                                "vpnTrustedDomainProfileToggle": "0",
                                                "vpnTunnelFlowLoggingToggle": "0",
                                                "wfpDriver": "0",
                                                "windows-machineToken": "",
                                                "windows-onnet-filter": "",
                                                "windows-ruleOrder": "",
                                                "windows-sccmconfig-offtrusted": "",
                                                "windows-sccmconfig-ontrusted": "",
                                                "windows-sccmconfig-splitvpntrusted": "",
                                                "windows-sccmconfig-vpntrusted": "",
                                                "windows-ziaposture-filter": "",
                                                "windowslogMode": "",
                                                "windowsPolicy": {
                                                    "allInboundTrafficConfig": None,
                                                    "cacheSystemProxy": "0",
                                                    "captivePortalConfig": "{\"enableCaptivePortalDetection\":1,\"enableFailOpen\":1,\"captivePortalWebSecDisableMinutes\":10,\"enableEmbeddedCaptivePortal\":0,\"automaticCapture\":1}",
                                                    "disable_password": "",
                                                    "disableLoopBackRestriction": "0",
                                                    "disableParallelIpv4andIpv6": -1,
                                                    "domainProfileDetectionConfig": None,
                                                    "flowLoggerConfig": "{\"zpa\":0,\"vpn\":0,\"vpn_tunnel\":0,\"direct\":0,\"loopback\":0,\"zcc_blocked_traffic\":0,\"intranet\":0}",
                                                    "install_ssl_certs": "0",
                                                    "installWindowsFirewallInboundRule": 1,
                                                    "logout_password": "",
                                                    "overrideWPAD": "0",
                                                    "pacDataPath": "",
                                                    "pacType": 1,
                                                    "prioritizeIPv4": "0",
                                                    "removeExemptedContainers": "1",
                                                    "restartWinHttpSvc": "0",
                                                    "sccmConfig": None,
                                                    "uninstall_password": "",
                                                    "wfpDriver": 0
                                                },
                                                "windowsziaDRRecoveryMethod": "",
                                                "zccBlockedTrafficToggle": "0",
                                                "ziaDomainName": "",
                                                "ziaPacUrl": "",
                                                "ziaSecretKey": "",
                                                "zpaAuthExpOnNetIpChange": "0",
                                                "zpaAuthExpOnSleep": "0",
                                                "zpaAuthExpOnSysRestart": "0",
                                                "zpaDomainName": "",
                                                "zpaFlowLoggingToggle": "0",
                                                "zpaSecretKey": ""
                                            }
        
        self.EDIT_DEVICE_POLICY_PAYLOAD_ios = {
                                                "active": "1",
                                                "adavnced_zpa_reauth_time": "",
                                                "allowZiaTest": "0",
                                                "allowZpaDisableWithoutPassword": "0",
                                                "allowZpaTest": "0",
                                                "appServiceIds": [
                                                ],
                                                "bypassAppIds": [
                                                ],
                                                "bypassCustomAppIds": [
                                                ],
                                                "dataProtectionService": "0",
                                                "description": "",
                                                "directFlowLoggingToggle": "0",
                                                "disable_password_for_zad": "",
                                                "disable_password_for_zdp": "",
                                                "disable_password_for_zdx": "",
                                                "disable_password_for_zpa": "",
                                                "disableDNSRouteExclusion": "0",
                                                "disasterRecovery": {
                                                    "allowZiaTest": False,
                                                    "allowZpaTest": False,
                                                    "enableZiaDR": False,
                                                    "enableZpaDR": False,
                                                    "useZiaGlobalDb": False,
                                                    "ziaCustomDbUrl": "",
                                                    "ziaDomainName": "",
                                                    "ziaDRMethod": 0,
                                                    "ziaRSAPubKey": "",
                                                    "ziaRSAPubKeyName": "",
                                                    "zpaDomainName": "",
                                                    "zpaRSAPubKey": "",
                                                    "zpaRSAPubKeyName": ""
                                                },
                                                "enableAdvanceZpaReauth": "0",
                                                "enableCli": "1",
                                                "enableZCCRevert": "0",
                                                "enableZiaDR": "0",
                                                "enableZpaDR": "0",
                                                "exit_password": "",
                                                "forwardingProfileId": "0",
                                                "groupAll": 1,
                                                "groups": [
                                                ],
                                                "highlightActiveControl": "0",
                                                "installWindowsFirewallInboundRule": "1",
                                                "intranetFlowLoggingToggle": "0",
                                                "logFileSize": 100,
                                                "logLevel": 0,
                                                "logMode": -1,
                                                "machineTunnelIdp": "0",
                                                "name": "test1",
                                                "offTrustedDomainProfileToggle": "0",
                                                "onTrustedDomainProfileToggle": "0",
                                                "pac_url": "",
                                                "policyExtension": {
                                                    "advanceZpaReauth": False,
                                                    "advanceZpaReauthTime": None,
                                                    "disableDNSRouteExclusion": "0",
                                                    "enableAntiTampering": "0",
                                                    "enableSetProxyOnVPNAdapters": 1,
                                                    "enableZCCRevert": "0",
                                                    "enableZdpService": "0",
                                                    "exitPassword": "",
                                                    "fallbackToGatewayDomain": "1",
                                                    "followRoutingTable": "1",
                                                    "generateCliPasswordContract": {
                                                        "allowZpaDisableWithoutPassword":True,
                                                        "enableCli": True
                                                    },
                                                    "interceptZIATrafficAllAdapters": 0,
                                                    "machineIdpAuth": False,
                                                    "nonce": "",
                                                    "overrideATCmdByPolicy": 0,
                                                    "packetTunnelDnsExcludeList": "",
                                                    "packetTunnelDnsIncludeList": "",
                                                    "packetTunnelExcludeList": "10.0.0.0/8,**********/12,***********/16,*********/4,***************,***********/16",
                                                    "packetTunnelIncludeList": "0.0.0.0/0",
                                                    "packetTunnelIncludeListForIPv6": "",
                                                    "purgeKerberosPreferredDCCache": 0,
                                                    "reactivateAntiTamperingTime": 0,
                                                    "sourcePortBasedBypasses": "3389:*",
                                                    "truncateLargeUDPDNSResponse": "0",
                                                    "updateDnsSearchOrder": "1",
                                                    "useDefaultAdapterForDNS": "1",
                                                    "useProxyPortForT1": "0",
                                                    "useProxyPortForT2": "0",
                                                    "useV8JsEngine": "1",
                                                    "useWsaPollForZpa": "0",
                                                    "useZscalerNotificationFramework": "0",
                                                    "vpnGateways": "",
                                                    "zccRevertPassword": "",
                                                    "zdDisablePassword": "",
                                                    "zdpDisablePassword": "",
                                                    "zdxDisablePassword": "",
                                                    "zpaAuthExpOnNetIpChange": "0",
                                                    "zpaAuthExpOnSleep": "0",
                                                    "zpaAuthExpOnSysRestart": "0",
                                                    "zpaAuthExpOnWinLogonSession": "1",
                                                    "zpaDisablePassword": ""
                                                },
                                                "reactivate_anti_tampering": "0",
                                                "reactivateWebSecurityMinutes": "0",
                                                "reauth_period": 12,
                                                "revert_zcc_password": "",
                                                "ruleOrder": 1,
                                                "sendDisableServiceReason": "0",
                                                "splitVpnTrustedDomainProfileToggle": "0",
                                                "tunnelZappTraffic": "0",
                                                "undefined": "",
                                                "useDefaultAdapterForDNS": "1",
                                                "useRoutingTable": "1",
                                                "users": [
                                                ],
                                                "useV8JSEngine": "1",
                                                "useWsaPollForZpa": "0",
                                                "useZscalerNotificationFramework": "0",
                                                "vpnFlowLoggingToggle": "0",
                                                "vpnTrustedDomainProfileToggle": "0",
                                                "vpnTunnelFlowLoggingToggle": "0",
                                                "wfpDriver": "0",
                                                "windows-machineToken": "",
                                                "windows-onnet-filter": "",
                                                "windows-ruleOrder": "",
                                                "windows-sccmconfig-offtrusted": "",
                                                "windows-sccmconfig-ontrusted": "",
                                                "windows-sccmconfig-splitvpntrusted": "",
                                                "windows-sccmconfig-vpntrusted": "",
                                                "windows-ziaposture-filter": "",
                                                "windowslogMode": "",
                                                "windowsPolicy": {
                                                    "allInboundTrafficConfig": None,
                                                    "cacheSystemProxy": "0",
                                                    "captivePortalConfig": "{\"enableCaptivePortalDetection\":1,\"enableFailOpen\":1,\"captivePortalWebSecDisableMinutes\":10,\"enableEmbeddedCaptivePortal\":0,\"automaticCapture\":1}",
                                                    "disable_password": "",
                                                    "disableLoopBackRestriction": "0",
                                                    "disableParallelIpv4andIpv6": -1,
                                                    "domainProfileDetectionConfig": None,
                                                    "flowLoggerConfig": "{\"zpa\":0,\"vpn\":0,\"vpn_tunnel\":0,\"direct\":0,\"loopback\":0,\"zcc_blocked_traffic\":0,\"intranet\":0}",
                                                    "install_ssl_certs": "0",
                                                    "installWindowsFirewallInboundRule": 1,
                                                    "logout_password": "",
                                                    "overrideWPAD": "0",
                                                    "pacDataPath": "",
                                                    "pacType": 1,
                                                    "prioritizeIPv4": "0",
                                                    "removeExemptedContainers": "1",
                                                    "restartWinHttpSvc": "0",
                                                    "sccmConfig": None,
                                                    "uninstall_password": "",
                                                    "wfpDriver": 0
                                                },
                                                "windowsziaDRRecoveryMethod": "",
                                                "zccBlockedTrafficToggle": "0",
                                                "ziaDomainName": "",
                                                "ziaPacUrl": "",
                                                "ziaSecretKey": "",
                                                "zpaAuthExpOnNetIpChange": "0",
                                                "zpaAuthExpOnSleep": "0",
                                                "zpaAuthExpOnSysRestart": "0",
                                                "zpaDomainName": "",
                                                "zpaFlowLoggingToggle": "0",
                                                "zpaSecretKey": ""
                                            }
        

        self.MOBILE_ADMIN_HEADER_FULL = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Encoding': 'gzip, deflate', 'br'
                                                'Accept-Language': 'en-US,en;q=0.9',
            'auth-token': '',
            'Connection': 'keep-alive',
            'Content-Length': '61',
            'Content-Type': 'application/json',
            'Cookie': '',
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.181 Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest'
        }
        self.MOBILE_ADMIN_HEADER = {
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'auth-token': '',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36',
            'Accept-Encoding': 'gzip, deflate', 'sdch'
                                                'Accept-Language': 'en-US,en;q=0.8',
            'Cookie': ''
        }
        # --------------------------------------------------------
        # -------Delete_Device_Policy-----------------------------
        self.Delete_Device_Policy_Url = "https://mobile.{}.net/webservice/api/web/policy/delete?id=".format(self.cloud)
        self.Delete_Device_Policy_Header = {"Host": "mobile.{}.net".format(self.cloud),
                                            "Connection": "keep-alive",
                                            "Accept": "application/json, text/javascript, */*; q=0.01",
                                            "X-Requested-With": "XMLHttpRequest",
                                            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.181 Safari/537.36",
                                            "auth-token": "",
                                            "Referer": "https://mobile.{}.net/index.html".format(self.cloud),
                                            "Accept-Encoding": "gzip, deflate, br",
                                            "Accept-Language": "en-US,en;q=0.9",
                                            "Cookie": ""}
        # --------------------------------------------------------
        # -------Create_Trusted_Net/Delete_Trusted_Net------------
        self.MOBILE_ADMIN_FORWARDING_PROFILE_HEADERS = {'Connection': 'keep-alive',
                                                        'Content-Length': '',
                                                        'Pragma': 'no-cache',
                                                        'Cache-Control': 'no-cache',
                                                        'Accept': 'application/json, text/javascript, */*; q=0.01',

                                                        'auth-token': '',
                                                        'X-Requested-With': 'XMLHttpRequest',
                                                        'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36',

                                                        'Accept-Encoding': 'gzip, deflate,sdch',
                                                        'Accept-Language': 'en-US,en;q=0.8',
                                                        'Cookie': ''
                                                        }
        self.Trusted_Network_Url = "https://mobile.{}.net/webservice/api/web/trustedNetwork/create".format(self.cloud)
        self.Trusted_Network_Payload = {"networkName": "", "addCondition": "", "undefined": "",
                                        "conditionTypeTrustedNetwork": "", "dnsServers": "", "dnsSearchDomains": "",
                                        "hostnames": "", "resolvedIpsForHostname": "", "ssid": "", "conditionType": 1,
                                        "resolvedIps": "", "active": ""}
        self.Trusted_Network_List_Url = "https://mobile.{}.net/webservice/api/web/trustedNetwork/listByCompany?page=1&pageSize=100".format(
            self.cloud)

        self.Get_TN_ID_Url = "https://mobile.{}.net/webservice/api/web/trustedNetwork/listByCompany?page=1&pageSize=100".format(
            self.cloud)
        self.Get_TN_ID_Header = {'Host': 'mobile.{}.net'.format(self.cloud),
                                 'Connection': 'keep-alive',
                                 'Cache-Control': 'max-age=0',
                                 'Accept': 'application/json, text/javascript, */*; q=0.01',
                                 'X-Requested-With': 'XMLHttpRequest',
                                 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.98 Safari/537.36',
                                 'auth-token': '',
                                 'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                 'Accept-Encoding': 'gzip, deflate, sdch, br',
                                 'Accept-Language': 'en-US,en;q=0.8',
                                 'Cookie': ''}
        self.Delete_TN_Url = "https://mobile.{}.net/webservice/api/web/trustedNetwork/delete/".format(self.cloud)
        self.Delete_TN_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }
        # --------------------------------------------------------
        # -------Get_Web_Insights---------------------------------
        self.Get_Web_Insights_Url = 'https://admin.{}.net/zsapi/v1/transactionData/webRequest'.format(self.cloud)
        self.Fetch_Web_Insights_Payload = {"startTime": "", "endTime": "", "pageSize": 1000}
        self.Get_Web_Insights_Header = {'Host': 'admin.{}.net'.format(cloud),
                                        'Connection': 'keep-alive',
                                        'ZS_CUSTOM_CODE': '',
                                        'Accept': 'application/json, text/javascript, */*; q=0.01',
                                        'X-Requested-With': 'XMLHttpRequest',
                                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.186 Safari/537.36',
                                        'Referer': 'https://admin.{}.net/'.format(cloud),
                                        'Accept-Encoding': 'gzip, deflate, br',
                                        'Accept-Language': 'en-US,en;q=0.9',
                                        'Content-Type': 'application/json',
                                        'Cookie': ''
                                        }
        self.Final_Get_Web_Insights_Url = 'https://admin.{}.net/zsapi/v1/transactionData/webRequest?_=XXXXX'.format(
            self.cloud)
        # --------------------------------------------------------
        # -------Validate_dashboard_option------------------------
        # --------------------------------------------------------
        # -------Validate_export_button---------------------------
        self.Export_Url = "https://mobile.{}.net/webservice/api/web/device/downloadDeviceUrl".format(cloud)
        self.Export_Header = {'Accept': 'application/json,text/javascript,*/*;q=0.01',
                              'Accept-Encoding': 'gzip,deflate', 'br'
                                                                 'Accept-Language': 'en-S,en;q=0.9',
                              'auth-token': '',
                              'Connection': 'keep-alive',
                              'Cookie': '',
                              'Host': 'mobile.{}.net'.format(cloud),
                              'Referer': 'https://mobile.{}.net/index.html'.format(cloud),
                              'User-Agent': 'Mozilla/5.0(Windows NT 10.0; Win64; x64) AppleWebKit/537.36(KHTML, like Gecko) Chrome/65.0.3325.181 Safari/537.36',
                              'X-Requested-With': 'XMLHttpRequest'}
        # --------------------------------------------------------
        # -------Validate_Updated_Device_Status_After_Upgrade-----
        self.UPDATED_DEVICE_STATUS_URL_FULL = "https://mobile.{}.net/webservice/api/web/device/deviceList?page=1&pageSize=100".format(
            cloud)
        self.UPDATED_DEVICE_STATUS_PAYLOAD = {"type": [1], "osId": [5], "version": "", "configState": [1]}
        # --------------------------------------------------------
        # -------Validate_Outdated_State--------------------------
        self.OUTDATED_DEVICE_URL = "https://mobile.{}.net/webservice/api/web/device/deviceList?page=1&pageSize=100".format(
            cloud)
        self.OUTDATED_DEVICE_PAYLOAD = {"type": [1], "osId": [5], "version": "", "configState": [0]}
        # --------------------------------------------------------
        # -------Validate_Unregistered_State----------------------
        self.UNREGISTERED_DEVICE_URL = "https://mobile.{}.net/webservice/api/web/device/deviceList?page=1&pageSize=100".format(
            cloud)
        self.UNREGISTERED_DEVICE_PAYLOAD = {"type": [4], "osId": [5], "version": "", "configState": []}
        # --------------------------------------------------------
        # -------Check_Status_Registered_Devices------------------
        self.SMUI_REGISTERED_DEVICES_URL = "https://admin.{}.net/zsapi/v1/secureAgent/secureAgentDevices".format(
            cloud)
        self.ZIA_HEADERS = {'Host': 'admin.{}.net'.format(cloud),
                            'Connection': 'keep-alive',
                            'ZS_CUSTOM_CODE': '',
                            'Accept': 'application/json, text/javascript, */*; q=0.01',
                            'X-Requested-With': 'XMLHttpRequest',
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.186 Safari/537.36',
                            'Referer': 'https://admin.{}.net/'.format(cloud),
                            'Accept-Encoding': 'gzip, deflate, br',
                            'Accept-Language': 'en-US,en;q=0.9',
                            'Content-Type': 'application/json',
                            'Cookie': ''
                            }
        # -----------ENABLE/DISABLE SAML BASED LOGIN ON SMUI----------
        self.SAML_ACTIONS_SMUI_URL = 'https://admin.{}.net/zsapi/v1/authSettings'.format(cloud)
        self.SAML_ACTIONS_SMUI_PAYLOAD = {"orgAuthType": "SAFECHANNEL_DIR",
                                          "oneTimeAuth": "OTP_DISABLED",
                                          "samlEnabled": False,
                                          "kerberosEnabled": False,
                                          "authFrequency": "DAILY_COOKIE",
                                          "authCustomFrequency": 30,
                                          "passwordStrength": "NONE",
                                          "passwordExpiry": "NEVER",
                                          "mobileAdminSamlIdpEnabled": False,
                                          "autoProvision": False,
                                          "directorySyncMigrateToScimEnabled": False
                                          }

        self.GET_AVAILABLE_IDP = 'https://admin.{}.net/zsapi/v1/idpConfig?_='.format(self.cloud)
        self.CHANGE_IDP_URL = 'https://admin.{}.net/zsapi/v1/idpConfig/setDefaultIdp/IDP_ID?idpSwitchWarn=true'.format(
            self.cloud)

        self.ACTIVATE_CHANGES_SMUI_URL = 'https://admin.{}.net/zsapi/v1/orgAdminStatus/activate'.format(cloud)
        # ----------------ENABLE/DISABLE BROWSER BASED AUTH-----------

        self.BROWSER_AUTH_MA_URL = 'https://mobile.{}.net/webservice/api/web/company/updateCompanyAuthInfo'.format(
            cloud)
        self.BROWSER_AUTH_MA_PAYLOAD = {"browserAuth": "", "platformType": "", "useDefaultBrowser": "0", "globalLogMode": "3", "installWebView2": "0"}
        self.BROWSER_AUTH_MA_HEADER = {'auth-token': ''}

        # -------Get_ID_with_User_Name----------------------------
        self.Get_ID_with_User_Name_url = "https://mobile.{}.net/webservice/api/web/usersByCompany?page=1&pageSize=100&search=".format(
            self.cloud)
        self.Get_ID_with_User_Name_payload = "page=1&pageSize=100&search="
        self.Get_ID_with_User_Name_header = {
            "Host": "mobile.{}.net".format(self.cloud),
            "Connection": "keep-alive",
            "sec-ch-ua": 'Not A;Brand";v="99", "Chromium";v="96", "Google Chrome";v="96"',
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "X-Requested-With": "XMLHttpRequest",
            "sec-ch-ua-mobile": "?0",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            "auth-token": "",
            "sec-ch-ua-platform": "Windows",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://mobile.{}.net/index.html".format(self.cloud),
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "en-US,en;q=0.9"
        }
        # --------------------------------------------------------
        # -------Client_Connector_IDP_Token----------------------------
        self.Get_Client_Connector_IDP_Token_List = 'https://mobile.{}.net/webservice/api/web/company/getIdpInfo'.format(
            self.cloud)
        self.Create_Client_Connector_IDP_Token_Url = 'https://mobile.{}.net/webservice/api/web/company/setIdpInfo'.format(
            self.cloud)
        self.Create_Client_Connector_IDP_Token_Payload = {"token": "", "description": ""}
        self.Get_Client_Connector_IDP_Token_Header = {'auth-token': ''}
        # --------------------------------------------------------
        # -------ZIA Device Management----------------------------
        self.Get_Device_Management_Devices_List_Url = 'https://admin.{}.net/zsapi/v1/deviceGroups/devices?page=1&pageSize=100&_='.format(
            self.cloud)

        # --------------------------------------------------------
        # -------ZIA/MA Users and Groups and Admins----------------------------
        self.ZIA_Activate_URL = "https://admin.{}.net/zsapi/v1/orgAdminStatus/activate".format(self.cloud)
        self.Get_List_Of_ZIA_Users_URL = "https://admin.{}.net/zsapi/v1/users?page=1&pageSize=100&includeAdminUsers=true&_=ADDEPOCHTIMESTAMPHERE".format(
            self.cloud)
        self.Get_List_Of_ZIA_Groups_URL = "https://admin.{}.net/zsapi/v1/groups?page=1&pageSize=100&_=ADDEPOCHTIMESTAMPHERE".format(
            self.cloud)
        self.Get_List_Of_Departments_ZIA_URL = "https://admin.{}.net/zsapi/v1/departments?page=1&pageSize=100&_=ADDEPOCHTIMESTAMPHERE".format(
            self.cloud)
        self.Create_New_ZIA_User_URL = 'https://admin.{}.net/zsapi/v1/users'.format(self.cloud)
        self.Create_New_ZIA_User_PAYLOAD = {
            "adminUser": False,
            "department": {},
            "disabled": False,
            "email": "",
            "groups": [],
            "name": "",
            "password": ""
        }
        self.Delete_New_ZIA_User_URL = "https://admin.{}.net/zsapi/v1/users/PASTETHEIDHERE".format(self.cloud)

        self.Get_Groups_List_MA_Url = 'https://mobile.{}.net/webservice/api/web/groupsByCompany?page=1&pageSize=100&search='.format(
            self.cloud)
        self.Create_Group_Url = "https://admin.{}.net/zsapi/v1/groups".format(self.cloud)
        self.Create_Group_Payload = {"name": "Automation_Test_Group", "comments": "yo!"}

        self.Delete_Group_Url = "https://admin.{}.net/zsapi/v1/groups/XXXXX".format(self.cloud)

        self.Sync_Groups_Url = "https://mobile.{}.net/webservice/api/web/setSyncInfo".format(self.cloud)
        self.Sync_Groups_Header = {
            'Connection': 'keep-alive',
            'Content-Length': '',
            'Pragma': 'no-cache',
            'Cache-Control': 'no-cache',
            'Accept': 'application/json, text/javascript, */*; q=0.01',

            'auth-token': '',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36',

            'Accept-Encoding': 'gzip, deflate', 'sdch'
                                                'Accept-Language': 'en-US,en;q=0.8',
            'Cookie': ''
        }
        self.Sync_Groups_Payload = {"resync": 1, "initialSync": 0}

        self.Get_Admin_Roles_Url = 'https://admin.{}.net/zsapi/v1/adminRoles?includeAuditorRole=False&includePartnerRole=False&includeApiRole=False&_='.format(
            self.cloud)
        self.Get_Admin_List_Url = 'https://admin.{}.net/zsapi/v1/adminUsers?page=1&pageSize=100&includeAuditorUsers=False&includeAdminUsers=true&search=&_='.format(
            self.cloud)
        self.Create_Admin_Url = 'https://admin.{}.net/zsapi/v1/adminUsers'.format(self.cloud)
        self.Create_Admin_Payload = {
            "adminScopeType": "ORGANIZATION",
            "disabled": False,
            "email": "",
            "isAuditor": False,
            "isDefaultAdmin": False,
            "isPasswordExpired": False,
            "isPasswordLoginAllowed": True,
            "loginName": "",
            "newLocationCreateAllowed": False,
            "password": "",
            "role": {
                "id": 9420,
                "name": "Super Admin"
            },
            "userName": ""
        }
        self.Delete_ZIA_Admin_Revoke_Url = 'https://admin.{}.net/zsapi/v1/adminUsers/revokeToken/'.format(self.cloud)
        self.Delete_ZIA_Admin_Url = 'https://admin.{}.net/zsapi/v1/adminUsers/'.format(self.cloud)

        # --------------------------------------------------------
        # -------Pac Files----------------------------
        self.Create_Pac_File_Url = 'https://admin.{}.net/zsapi/v1/pacFiles'.format(self.cloud)
        self.Create_Pac_File_Payload = {
            "description": "",
            "domain": "",
            "editable": True,
            "name": "",
            "pacCommitMessage": "",
            "pacContent": "\nfunction FindProxyForURL(url, host) {\n\n    var privateIP = /^(0|10|127|192\\.168|172\.1[6789]|172\.2[0-9]|172\.3[01]|169\.254|192\.88\.99)\.[0-9.]+$/;\n    var resolved_ip = dnsResolve(host);\n   // var country = \"${COUNTRY}\";\n\n    /* Don't send non-FQDN or private IP auths to us */\n    if (isPlainHostName(host) || isInNet(resolved_ip, \"*********\",\"*************\") || privateIP.test(resolved_ip))\n        return \"DIRECT\";\n\n    /* FTP goes directly */\n    if (url.substring(0,4) == \"ftp:\")\n        return \"DIRECT\";\n\t\t\t\n    /* test with ZPA */\n    if (isInNet(resolved_ip, \"**********\",\"***********\"))\n        return \"DIRECT\";\n\t\t\t\n    /* Updates are directly accessible */\n    if (((localHostOrDomainIs(host, \"trust.zscaler.com\")) ||\n            (localHostOrDomainIs(host, \"trust.zscaler.net\")) ||\n            (localHostOrDomainIs(host, \"trust.zscalerone.net\")) ||\n            (localHostOrDomainIs(host, \"trust.zscalertwo.net\")) ||\n            (localHostOrDomainIs(host, \"trust.zscalerthree.net\")) ||\n            (localHostOrDomainIs(host, \"trust.zscalergov.net\")) ||\n            (localHostOrDomainIs(host, \"trust.zsdemo.net\")) ||\n            (localHostOrDomainIs(host, \"trust.zscloud.net\")) ||\n            (localHostOrDomainIs(host, \"trust.zsfalcon.net\")) ||\n            (localHostOrDomainIs(host, \"trust.zdxcloud.net\")) ||\n            (localHostOrDomainIs(host, \"trust.zdxpreview.net\")) ||\n            (localHostOrDomainIs(host, \"trust.zdxbeta.net\")) ||\n            (localHostOrDomainIs(host, \"trust.zsdevel.net\")) ||\n            (localHostOrDomainIs(host, \"trust.zsbetagov.net\")) ||\n\t\t\t(localHostOrDomainIs(host, \"trust.zspreview.net\")) ||\n\t\t\t(localHostOrDomainIs(host, \"trust.zscalerten.net\")) || \n\t\t\t(localHostOrDomainIs(host, \"trust.zdxten.net\")) ) &&\n            (url.substring(0,5) == \"http:\" || url.substring(0,6) == \"https:\"))\n        return \"DIRECT\";\n\n\t/* for users of Canada if you want to direct traffic to only canada gateways*/\n//\tif (shExpMatch(country, \"Canada\")) {\n//\t \treturn \"PROXY ${COUNTRY_GATEWAY_FX}:80; PROXY ${COUNTRY_SECONDARY_GATEWAY_FX};DIRECT\";\n//\t}\n\n\t/* for all users if you want to direct traffic to country gateways by default */\n//\treturn \"PROXY ${COUNTRY_GATEWAY_FX}:80; PROXY ${COUNTRY_SECONDARY_GATEWAY_FX};DIRECT\";\n\n\n    /* Default Traffic Forwarding. Forwarding to Zen on port 80, but you can use port 9400 also */\n    return \"PROXY ${GATEWAY_FX}:80; PROXY ${SECONDARY_GATEWAY_FX}:80; DIRECT\";\n}",
            "pacUrlObfuscated": True,
            "pacVerificationStatus": "VERIFY_NOERR",
            "pacVersionStatus": "DEPLOYED"
        }
        self.Get_Pac_Files_List_Url = 'https://admin.{}.net/zsapi/v1/pacFiles?filter=pac_content'.format(self.cloud)
        self.Delete_Pac_File_Url = 'https://admin.{}.net/zsapi/v1/pacFiles/'.format(self.cloud)

        # --------------------------------------------------------
        # -------SSL Policy ZIA----------------------------
        self.Get_Device_Group_ID_List_Url = 'https://admin.{}.net/zsapi/v1/deviceGroups?_='.format(self.cloud)
        self.Get_Url_Category_List_Url = 'https://admin.{}.net/zsapi/v1/urlCategories/lite/ssl?dnsDestinationCategory=False&urlFilteringPolicyCriteria=False&type=ALL&_='.format(
            self.cloud)
        self.Get_SSL_Policy_List_Url = 'https://admin.{}.net/zsapi/v1/sslInspectionRules?_'.format(self.cloud)
        self.Create_SSL_Policy_Url = 'https://admin.{}.net/zsapi/v1/sslInspectionRules'.format(self.cloud)
        self.Create_SSL_Policy_Payload = {
            "action": {
                "overrideDefaultCertificate": False,
                "sslInterceptionCert": {
                    "defaultCertificate": True,
                    "id": 1,
                    "name": "Zscaler Intermediate CA Certificate"
                },
                "type": ""
            },
            "deviceGroups": [],
            "name": "",
            "order": 1,
            "rank": 7,
            "roadWarriorForKerberos": False,
            "state": "ENABLED",
            "urlCategories": []
        }
        self.Delete_SSL_Policy_Url = 'https://admin.{}.net/zsapi/v1/sslInspectionRules/'.format(self.cloud)
        # --------------------------------------------------------
        # -------URL Filter Policy ZIA----------------------------
        self.Get_Url_Policy_List_Url = 'https://admin.{}.net/zsapi/v1/urlFilteringRules?_='.format(self.cloud)
        self.Create_Url_Policy_Url = 'https://admin.{}.net/zsapi/v1/urlFilteringRules'.format(self.cloud)
        self.Edit_Url_Policy_Url = 'https://admin.{}.net/zsapi/v1/urlFilteringRules/'.format(self.cloud)
        self.Create_Url_Policy_Payload = {
            "action": "ALLOW",
            "deviceGroups": [],
            "name": "",
            "order": 1,
            "protocols": [
                "DOHTTPS_RULE",
                "TUNNELSSL_RULE",
                "HTTP_PROXY",
                "FOHTTP_RULE",
                "FTP_RULE",
                "HTTPS_RULE",
                "HTTP_RULE",
                "SSL_RULE",
                "TUNNEL_RULE"
            ],
            "rank": 7,
            "requestMethods": [
                "CONNECT",
                "GET",
                "HEAD"
            ],
            "state": "ENABLED",
            "urlCategories": []
        }
        self.Delete_Url_Policy_Url = 'https://admin.{}.net/zsapi/v1/urlFilteringRules/'.format(self.cloud)

        # --------------------------------------------------------
        # -------Fetch_Enrolled_Devices_Details----------------------------
        self.Fetch_Enrolled_Devices_Details_url = "https://mobile.{}.net/webservice/api/web/device/deviceList?page=1&pageSize=100".format(
            self.cloud)
        self.Fetch_Enrolled_Devices_Details_payload = {
            "type": [0],
            "user": [0],
            "osId": [0],
            "version": "",
            "configState": [],
            "osVersion": "",
            "modelId": 0,
            "activeFromDays": ""
        }
        self.Fetch_Enrolled_Devices_Details_header = {
            "Host": "mobile.{}.net".format(self.cloud),
            "Connection": "keep-alive",
            "sec-ch-ua": 'Not A;Brand";v="99", "Chromium";v="96", "Google Chrome";v="96"',
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "X-Requested-With": "XMLHttpRequest",
            "sec-ch-ua-mobile": "?0",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            "auth-token": "",
            "sec-ch-ua-platform": "Windows",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://mobile.{}.net/index.html".format(self.cloud),
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "en-US,en;q=0.9"
        }

        #

        self.remoteFetchLogsURL = 'https://mobile.{}.net/webservice/api/web/log/fetch'.format(self.cloud)
        self.remoteFetchLogsHeaders = {
            'Connection': 'keep-alive',
            'Content-Length': '',
            'Pragma': 'no-cache',
            'Cache-Control': 'no-cache',
            'Accept': 'application/json, text/javascript, */*; q=0.01',

            'auth-token': '',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36',

            'Accept-Encoding': 'gzip, deflate', 'sdch'
                                                'Accept-Language': 'en-US,en;q=0.8',
            'Cookie': ''
        }
        self.remoteFetchLogsPayload = {
            "deviceId": '',
            "fetch": True
        }

        # Endpoint Integration on MA

        self.getEndpoint_URL = 'https://mobile.{}.net/webservice/api/web/endpointIntegration/getEndpointIntegrationInfo'.format(
            self.cloud)
        self.getEndpoint_Header = {
            "Host": "mobile.{}.net".format(self.cloud),
            "Connection": "keep-alive",
            "sec-ch-ua": 'Not A;Brand";v="99", "Chromium";v="96", "Google Chrome";v="96"',
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "X-Requested-With": "XMLHttpRequest",
            "sec-ch-ua-mobile": "?0",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            "auth-token": "",
            "sec-ch-ua-platform": "Windows",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://mobile.{}.net/index.html".format(self.cloud),
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "en-US,en;q=0.9"
        }

        self.endpoint_URL = 'https://mobile.{}.net/webservice/api/web/endpointIntegration/setEndpointIntegrationInfo'.format(
            self.cloud)
        self.endpoint_Header = {
            "Host": "mobile.{}.net".format(self.cloud),
            "Connection": "keep-alive",
            "sec-ch-ua": 'Not A;Brand";v="99", "Chromium";v="96", "Google Chrome";v="96"',
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "X-Requested-With": "XMLHttpRequest",
            "sec-ch-ua-mobile": "?0",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            "auth-token": "",
            "sec-ch-ua-platform": "Windows",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://mobile.{}.net/index.html".format(self.cloud),
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "en-US,en;q=0.9"
        }
        self.endpoint_Payload = {
            "id": "",
            "tunnelTCPLocalPort": "9000",
            "firefoxIntegrationEnabled": "0",
            "customVpnTrustedNetworkAdapters": ""
        }

        # -- Fetch extra details -------------------------------------------------------------------------------------------------------

        self.Extra_Details_url = "https://mobile.{}.net/webservice/api/web/device/deviceDetail?id=".format(self.cloud)
        self.Extra_Details_payload = {
            'id': ''
        }
        self.Extra_Details_header = {
            "Host": "mobile.{}.net".format(self.cloud),
            "Connection": "keep-alive",
            "sec-ch-ua": 'Not A;Brand";v="99", "Chromium";v="96", "Google Chrome";v="96"',
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "X-Requested-With": "XMLHttpRequest",
            "sec-ch-ua-mobile": "?0",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            "auth-token": "",
            "sec-ch-ua-platform": "Windows",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://mobile.{}.net/index.html".format(self.cloud),
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "en-US,en;q=0.9"
        }

        # --------------------------------------------------------
        # -------toggle_dropnonzscalerpacket---------------------------
        self.toggle_dropnonzscalerpacket_url = "https://mobileadmin.{}.net/webservice/api/web/endpointIntegration/setEndpointIntegrationInfo".format(self.cloud)
        self.toggle_dropnonzscalerpacket_payload = {
                                    "customVpnTrustedNetworkAdapters": "",
                                    "dropNonZscalerPacket": "1",
                                    "firefoxIntegrationEnabled": "1",
                                    "id": "261",
                                    "syntheticIPRange": "**********/16",
                                    "tunnelTCPLocalPort": "9000"
                                }

        self.toggle_dropnonzscalerpacket_header = {
		                            "Accept": "application/json, text/javascript, */*; q=0.01",
                                    "Accept-Encoding": "gzip, deflate, br",
                                    "Accept-Language": "en-US,en;q=0.9",
                                    "API-Id": '',
                                    "auth-token": '',
                                    "Connection": "keep-alive",
                                    "Cookie": '',
                                    "Host": "mobileadmin.{}.net".format(self.cloud),
                                    "Referer": "https://mobileadmin.{}.net/index.html".format(self.cloud),
                                    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",
                                    "X-Requested-With":"XMLHttpRequest"
                                    }
        # -------Export from MA-----------------------------------

        self.Device_Details_url = "https://mobile.{0}.net/webservice/api/web/device/downloadDeviceUrl".format(
            self.cloud)
        self.Service_Status_url = "https://mobile.{0}.net/webservice/api/web/device/downloadDeviceHealthUrl".format(
            self.cloud)
        self.Device_Details_Header = {
            "Host": "mobile.{}.net".format(self.cloud),
            "Connection": "keep-alive",
            "sec-ch-ua": 'Not A;Brand";v="99", "Chromium";v="96", "Google Chrome";v="96"',
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "X-Requested-With": "XMLHttpRequest",
            "sec-ch-ua-mobile": "?0",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            "auth-token": "",
            "sec-ch-ua-platform": "Windows",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://mobile.{}.net/index.html".format(self.cloud),
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "en-US,en;q=0.9"
        }
        self.Device_Details_payload = {
            "id": "",
            "configState": 0,
            "configState": 1,
            "type": 0,
            "osId": 0,
            "user": 0,
            "version": "",
            "searchProperty": ""
        }
        # --------------------------------------------------------
        # -------Create Group in ZIA------------------------------

        # --------------------------------------------------------
        # -------Device Postures----------------------------------
        self.Device_Posture_Payload = {
            "applyToMachineTunnel": "0",
            "description": "",
            "id": -1,
            "name": "",
            "type": 1
        }
        self.Device_Posture_Url = "https://mobile.{}.net/webservice/api/web/postureProfile/edit".format(self.cloud)
        self.Delete_Posture_Url = "https://mobile.{}.net/webservice/api/web/postureProfile/delete?id=".format(cloud)
        self.Get_Posture_Url = "https://mobile.{}.net/webservice/api/web/postureProfile/listByCompany?page=1&pageSize=100".format(
            self.cloud)
        self.Device_Posture_Header = {"auth-token": ""}

        # -------Create_ZIA_Posture_Profile-----------------------
        self.CREATE_ZIA_POSTURE_PROFILE_URL = "https://mobile.{}.net/webservice/api/web/ziapostureProfile/edit".format(
            cloud)

        self.CREATE_ZIA_POSTURE_PROFILE_HEADER = {'Connection': 'keep-alive',
                                                  'Content-Length': '',
                                                  'Pragma': 'no-cache',
                                                  'Cache-Control': 'no-cache',
                                                  'Accept': 'application/json, text/javascript, */*; q=0.01',

                                                  'auth-token': '',
                                                  'X-Requested-With': 'XMLHttpRequest',
                                                  'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36',

                                                  'Accept-Encoding': 'gzip, deflate', 'sdch'
                                                                                      'Accept-Language': 'en-US,en;q=0.8',
                                                  'Cookie': ''
                                                  }

        self.CREATE_ZIA_POSTURE_PROFILE_PAYLOAD = {
            "id": -1,
            "name": "",
            #   "high": '{\\"cs\\":[{\\"cn\\":[ZZZZZ]}]}',
            #   "medium": '{\\"cs\\":[{\\"cn\\":[ZZZZZ]}]}',
            #   "low": '{\\"cs\\":[{\\"cn\\":[ZZZZZ]}]}',
            "high": '{\"cs\":[{\"cn\":[ZZZZZ]}]}',
            "medium": '{\"cs\":[{\"cn\":[ZZZZZ]}]}',
            "low": '{\"cs\":[{\"cn\":[ZZZZZ]}]}',
            "platformType": 3
        }

        # -------Get_ZIA_Posture_Profile_List---------------------
        self.GET_ZIA_POSTURE_PROFILE_LIST_URL = "https://mobile.{}.net/webservice/api/web/ziapostureProfile/listByCompany?page=1&pageSize=100&platformType=3".format(
            self.cloud)
        
        self.GET_ZIA_POSTURE_PROFILE_LIST_URL_MAC = "https://mobile.{}.net/webservice/api/web/ziapostureProfile/listByCompany?page=1&pageSize=100&platformType=4".format(
            self.cloud
        )

        self.GET_ZIA_POSTURE_PROFILE_LIST_HEADER = {'Accept': 'application/json, text/javascript, */*; q=0.01',
                                                    'Accept-Encoding': 'gzip, deflate, br',
                                                    'Accept-Language': 'en-US,en;q=0.9',
                                                    'auth-token': '',
                                                    'Connection': 'keep-alive',
                                                    'Cookie': '',
                                                    'Host': 'mobile.{}.net'.format(self.cloud),
                                                    'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
                                                    'X-Requested-With': 'XMLHttpRequest'
                                                    }

        # -------Edit_ZIA_Posture_Profile---------------------------------------
        self.EDIT_ZIA_POSTURE_PROFILE_URL = 'https://mobile.{}.net/webservice/api/web/ziapostureProfile/edit'.format(
            self.cloud)
        self.EDIT_ZIA_POSTURE_PROFILE_HEADER = {'Accept': 'application/json, text/javascript, */*; q=0.01',
                                                'Accept-Encoding': 'gzip, deflate, br',
                                                'Accept-Language': 'en-US,en;q=0.9',
                                                'auth-token': '',
                                                'Connection': 'keep-alive',
                                                'Content-Length': '',
                                                'Content-Type': 'application/json',
                                                'Cookie': '',
                                                'Host': 'mobile.{}.net'.format(self.cloud),
                                                'Origin': 'https://mobile.{}.net'.format(self.cloud),
                                                'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
                                                'X-Requested-With': 'XMLHttpRequest'}
        # -------------------------------------------------------
        # -------Delete_ZIA_Posture_Profile-----------------------
        self.DELETE_ZIA_POSTURE_PROFILE_URL = "https://mobile.{}.net/webservice/api/web/ziapostureProfile/delete?id=".format(
            self.cloud)
        self.DELETE_ZIA_POSTURE_PROFILE_HEADER = {"Host": "mobile.{}.net".format(self.cloud),
                                                  "Connection": "keep-alive",
                                                  "Accept": "application/json, text/javascript, */*; q=0.01",
                                                  "X-Requested-With": "XMLHttpRequest",
                                                  "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.181 Safari/537.36",
                                                  "auth-token": '',
                                                  "Referer": "https://mobile.{}.net/index.html".format(self.cloud),
                                                  "Accept-Encoding": "gzip, deflate, br",
                                                  "Accept-Language": "en-US,en;q=0.9",
                                                  "Cookie": ''
                                                  }

        # ---------Get Device Trust Level of Enrolled Device-------------
        self.GET_DEVICE_LIST_URL = "https://mobile.{}.net/webservice/api/web/device/deviceList?page=1&pageSize=100".format(
            self.cloud)
        self.GET_DEVICE_LIST_HEADER = {"Host": "mobile.{}.net".format(self.cloud),
                                                  "Connection": "keep-alive",
                                                  "Accept": "application/json, text/javascript, */*; q=0.01",
                                                  "X-Requested-With": "XMLHttpRequest",
                                                  "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.181 Safari/537.36",
                                                  "auth-token": '',
                                                  "Referer": "https://mobile.{}.net/index.html".format(self.cloud),
                                                  "Accept-Encoding": "gzip, deflate, br",
                                                  "Accept-Language": "en-US,en;q=0.9",
                                                  "Cookie": ''
                                                  }
        self.GET_DEVICE_LIST_PAYLOAD ={"type":[0],
                                       "user":[0],
                                       "osId":[0],
                                       "version":"",
                                       "osVersion":"",
                                       "modelId":0,
                                       "activeFromDays":"",
                                       "sortBy":"keepAliveTimestamp"}
        self.GET_DEVICE_DETAILS_URL = "https://mobile.{}.net/webservice/api/web/device/deviceDetail?id=".format(
            self.cloud)
        self.GET_DEVICE_DETAILS_HEADER = {"Host": "mobile.{}.net".format(self.cloud),
                                                  "Connection": "keep-alive",
                                                  "Accept": "application/json, text/javascript, */*; q=0.01",
                                                  "X-Requested-With": "XMLHttpRequest",
                                                  "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.181 Safari/537.36",
                                                  "auth-token": '',
                                                  "Referer": "https://mobile.{}.net/index.html".format(self.cloud),
                                                  "Accept-Encoding": "gzip, deflate, br",
                                                  "Accept-Language": "en-US,en;q=0.9",
                                                  "Cookie": ''
                                                  }

        # -------Posture Based Service - Device Groups-----------------------

        self.DEVICE_GROUP_HEADER = {'Accept': 'application/json, text/javascript, */*; q=0.01',
                                                    'Accept-Encoding': 'gzip, deflate, br',
                                                    'Accept-Language': 'en-US,en;q=0.9',
                                                    'auth-token': '',
                                                    'Connection': 'keep-alive',
                                                    'Cookie': '',
                                                    'Host': 'mobile.{}.net'.format(self.cloud),
                                                    'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
                                                    'X-Requested-With': 'XMLHttpRequest'
                                    }

        self.CREATE_DEVICE_GROUP_URL = "https://mobile.{}.net/webservice/api/web/deviceGroup/edit".format(
            cloud)
        self.GET_DEVICE_GROUP_URL = "https://mobile.{}.net/webservice/api/web/deviceGroup/listByCompany?page=1&pageSize=100&search=&searchType=fileName".format(
            cloud)
        self.DELETE_DEVICE_GROUP_URL = "https://mobile.{}.net/webservice/api/web/deviceGroup/delete?id=".format(
            cloud)
        self.UPDATE_GROUPS_ZPA_ENTITLEMENT_URL = "https://mobile.{}.net/webservice/api/web/updateGroupEntitlementZPA".format(
            cloud)
        self.UPDATE_GROUPS_ZIA_ENTITLEMENT_URL = "https://mobile.{}.net/webservice/api/web/updateGroupEntitlementZIA".format(
            cloud)
        self.UPDATE_GROUPS_ZDX_ENTITLEMENT_URL = "https://mobile.{}.net/webservice/api/web/updateGroupEntitlementUPM".format(
            cloud)

        self.CREATE_DEVICE_GROUP_PAYLOAD = {
            "id": -1,
            "name": "",
            "platformType": [3],
            "devicePosture": "{\"cs\":[{\"cn\":[{\"id\":DG_ID,\"name\":\"DG_NAME\"}]}]}"
        }

        self.ZPA_ENTITLEMENT_PAYLOAD = {
            "groups": [],
            "enableForAll": 1,
            "machineTunEnabledForAll": 1,
            "deviceGroups": [],
            "computeDeviceGroups": 0
        }

        self.ZIA_ENTITLEMENT_PAYLOAD = {
            "groups": [],
            "enableForAll": 1,
            "deviceGroups": [],
            "computeDeviceGroups": 0,
            "logoutZCC": 0
        }

        self.ZDX_ENTITLEMENT_PAYLOAD = {
            "groups": [],
            "enableForAll": 1,
            "deviceGroups": [],
            "computeDeviceGroups": 0,
            "logoutZCC": 0
        }
        # -------------------------------------------------------------------------------------
        # -- App Bypasses ---------------------------------------------------------------------
        self.Create_App_Bypass_Url = "https://mobileadmin.{}.net/webservice/api/web/appIdentity/create".format(self.cloud)
        self.Edit_App_Bypass_Url = "https://mobileadmin.{}.net/webservice/api/web/appIdentity/update".format(self.cloud)
        self.Delete_App_Bypass_Url = "https://mobileadmin.{}.net/webservice/api/web/appIdentity/delete/ADD_ID_HERE".format(self.cloud)
        self.Get_All_App_Bypasses_Url = "https://mobileadmin.{}.net/webservice/api/web/appIdentity/listByCompany?page=1&pageSize=100&search=".format(self.cloud)
        # verify with sucess to be true
        self.App_Bypass_Signature_Payload =   '{\"appName\":\"APP_NAME\",\"appSignature\":\"APP_SIGNATURE\"}'
        self.App_Bypass_Certificate_Payload = '{\"certName\":\"CERTIFICATE_NAME\",\"subject\":\"CERTIFICATE_SUBJECT\",\"thumbprint\":\"CERTIFICATE_SIGNER\",\"appName\":\"APP_NAME\",\"appThumbprint\":\"APP_SIGNATURE\"}'
        self.App_Bypass_Payload = {
            "id":0,
            "appName":"",
            "fileNames":[""],
            "filePaths":[],
            "matchingCriteria":3,
            # "signaturePayload":"{\"signatures\":[{\"appName\":\"zoom\",\"appSignature\":\"de78e314652045e733480f44f99d99e98a109c95d9d47323c007999251764b16\"}]}",
            "signaturePayload": '{\"signatures\":[SIGNATURE_PAYLOAD]}',
            # "certificatePayload":"{\"certificates\":[]}",
            # "certificatePayload": "{\"certificates\":[{\"certName\":\"certname\",\"thumbprint\":\"certthumb\",\"appName\":\"appname\",\"appThumbprint\":\"appthumb\"}]}",
            # "certificatePayload": "{\"certificates\":[{\"certName\":\"certname\",\"subject\":\"certsubject\",\"appName\":\"appname\",\"appThumbprint\":\"appthumb\"}]}",
            "certificatePayload": '{\"certificates\":[CERTIFICATE_PAYLOAD]}',
            "createdBy":"0",
            "editedBy":"0",
            "editedTimestamp":"",
            "signatures": ""

            }
        self.App_Bypass_Header =  {
                    "API-Id": "",
                    "Accept": "application/json, text/javascript, */*; q=0.01",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Cookie": "",
                    "Host": "mobileadmin.{}.net".format(self.cloud),
                    "Origin": "https://mobileadmin.{}.net".format(self.cloud),
                    "Pragma": "no-cache",
                    "Referer": "https://mobileadmin.{}.net/index.html".format(self.cloud),
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    "X-Requested-With": "XMLHttpRequest",
                    "auth-token": "",
                    "sec-ch-ua": '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
                    "sec-ch-ua-mobile": '?0',
                    "sec-ch-ua-platform": "Windows"
        }
        self.Create_Custom_AppBypass_URL = "https://mobileadmin.{}.net/webservice/api/web/customAppService/create".format(cloud)
        self.Edit_Custom_AppBypass_URL = "https://mobileadmin.{}.net/webservice/api/web/customAppService/update".format(cloud)
        self.Custom_AppBypass_HEADER = {
                    "API-Id": "",
                    "Accept": "application/json, text/javascript, */*; q=0.01",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Cookie": "",
                    "Host": "mobileadmin.{}.net".format(self.cloud),
                    "Origin": "https://mobileadmin.{}.net".format(self.cloud),
                    "Pragma": "no-cache",
                    "Referer": "https://mobileadmin.{}.net/index.html".format(self.cloud),
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    "X-Requested-With": "XMLHttpRequest",
                    "auth-token": "",
                    "sec-ch-ua": '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
                    "sec-ch-ua-mobile": '?0',
                    "sec-ch-ua-platform": "Windows"
        }
        self.Create_Custom_AppBypass_PAYLOAD = {
            "id":-1,
            "appName":"Automation_Bypass",
            "appData":
                [
                    {
                        "port":"",
                        "ipaddr":"",
                        "proto":""
                    }
                ],
            "appDataV6":
                [
                    {
                        "port":"",
                        "ipaddr":"",
                        "proto":""
                    }
                ],
            "createdBy":"0",
            "editedBy":"0",
            "editedTimestamp":""
            }
        self.Get_Custom_AppBypass_URL = "https://mobileadmin.{}.net/webservice/api/web/customAppService/listByCompany?page=1&pageSize=1000&search=".format(cloud)
        self.Delete_Custom_App_Bypass_URL = "https://mobileadmin.{}.net/webservice/api/web/customAppService/delete/ADD_ID_HERE".format(cloud)
        

        # -------------------------- Monika -----------------------------------------------------------
        # -- Create Admin Role -----------------------------------------------------------------------------
        self.CREATE_ADMIN_ROLE_URL = "https://mobile.{}.net/webservice/api/web/adminroles/edit".format(cloud)

        self.CREATE_ADMIN_ROLE_HEADER = {'Connection': 'keep-alive',
                                         'Content-Length': '',
                                         'Pragma': 'no-cache',
                                         'Cache-Control': 'no-cache',
                                         'Accept': 'application/json, text/javascript, */*; q=0.01',

                                         'auth-token': '',
                                         'X-Requested-With': 'XMLHttpRequest',
                                         'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36',

                                         'Accept-Encoding': 'gzip, deflate', 'sdch'
                                                                             'Accept-Language': 'en-US,en;q=0.8',
                                         'Cookie': ''
                                         }

        self.CREATE_ADMIN_ROLE_PAYLOAD = {
                                            "id": "-1",
                                            "roleName": "",
                                            "isEditable": "true",
                                            "dashboard": "1",
                                            "enrolledDevicesGroup": "0",
                                            "deviceOverview": "0",
                                            "machineTunnel": "0",
                                            "partnerDeviceOverview": "0",
                                            "appProfileGroup": "0",
                                            "windowsProfile": "0",
                                            "macProfile": "0",
                                            "linuxProfile": "0",
                                            "iosProfile": "0",
                                            "androidProfile": "0",
                                            "administratorGroup": "0",
                                            "clientConnectorAppStore": "0",
                                            "clientConnectorNotifications": "0",
                                            "clientConnectorSupport": "0",
                                            "clientConnectorIdp": "0",
                                            "auditLogs": "0",
                                            "forwardingProfile": "0",
                                            "trustedNetwork": "0",
                                            "zscalerEntitlement": "0",
                                            "devicePosture": "0",
                                            "userAgent": "0",
                                            "appBypass": "0",
                                            "dedicatedProxyPorts": "0",
                                            "zscalerDeception": "0",
                                            "publicApi": "0",
                                            "deviceGroups": "0",
                                            "authSetting": "0",
                                            "adminManagement": "0",
                                            "zpaPartnerLogin": "0"

                                         } 


        # -- Get Admon role -------------------------------------------------------------------------------------
        self.GET_ADMIN_ROLES_LIST_URL = "https://mobile.{}.net/webservice/api/web/adminroles/list?page=1&pageSize=10".format(self.cloud)
        self.GET_ADMIN_ROLES_LIST_HEADER = {'Accept': 'application/json, text/javascript, */*; q=0.01',
                                            'Accept-Encoding': 'gzip, deflate, br',
                                            'Accept-Language': 'en-US,en;q=0.9',
                                            'auth-token': '',
                                            'Connection': 'keep-alive',
                                            'Cookie': '',
                                            'Host': 'mobile.{}.net'.format(self.cloud),
                                            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
                                            'X-Requested-With': 'XMLHttpRequest'
                                            }

        # -------Delete_Admin_Role-----------------------
        self.Delete_Admin_Role_Url = "https://mobile.{}.net/webservice/api/web/adminroles/delete?roleId=".format(self.cloud)
        self.Delete_Admin_Role_Header = {"Host": "mobile.{}.net".format(self.cloud),
                                         "Connection": "keep-alive",
                                         "Accept": "application/json, text/javascript, */*; q=0.01",
                                         "X-Requested-With": "XMLHttpRequest",
                                         "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.181 Safari/537.36",
                                         "auth-token": '',
                                         "Referer": "https://mobile.{}.net/index.html".format(self.cloud),
                                         "Accept-Encoding": "gzip, deflate, br",
                                         "Accept-Language": "en-US,en;q=0.9",
                                         "Cookie": ''
                                         }

        self.Delete_Admin_Role_Payload = {
                                            "roleId":None
                                            }

        #-- Get Admin User List -------------------------------------------------------
        self.GET_ADMIN_USER_LIST_URL = "https://mobile.{}.net/webservice/api/web/adminusers/list?page=1&pageSize=100&userType=1".format(self.cloud)
        self.GET_ADMIN_USER_LIST_HEADER = {'Accept': 'application/json, text/javascript, */*; q=0.01',
                                            'Accept-Encoding': 'gzip, deflate, br',
                                            'Accept-Language': 'en-US,en;q=0.9',
                                            'auth-token': '',
                                            'Connection': 'keep-alive',
                                            'Cookie': '',
                                            'Host': 'mobile.{}.net'.format(self.cloud),
                                            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
                                            'X-Requested-With': 'XMLHttpRequest'
                                            }
        
        self.GET_ADMIN_USER_LIST_PAYLOAD = {
             "page":1,
             "pageSize":100,
             "userType":1
        }

        # -- Sync ZIA Admin -------------------------------------------------------------
        self.Sync_ZIA_Admin_Users_Url = "https://mobileadmin.{}.net/webservice/api/web/adminusers/syncZiaZdxAdmins".format(self.cloud)
                                        
                                        
        self.Sync_ZIA_Admin_Users_Header = {'Connection': 'keep-alive',
                                         'Content-Length': '0',
                                         'Pragma': 'no-cache',
                                         'Cache-Control': 'no-cache',
                                         'Accept': 'application/json, text/javascript, */*; q=0.01',

                                         'auth-token': '',
                                         'X-Requested-With': 'XMLHttpRequest',
                                         'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36',

                                         'Accept-Encoding': 'gzip, deflate', 'sdch'
                                                                             'Accept-Language': 'en-US,en;q=0.8',
                                         'Cookie': ''
                                         }
        # -- Sync ZPA Admin -------------------------------------------------------------
        self.Sync_ZPA_Admin_Users_Url = "https://mobileadmin.{}.net/webservice/api/web/adminusers/syncZpaAdmins".format(self.cloud)
        self.Sync_ZPA_Admin_Users_Header = {
            'Connection': 'keep-alive',
            'Content-Length': '0',
            'Pragma': 'no-cache',
            'Cache-Control': 'no-cache',
            'Accept': 'application/json, text/javascript, */*; q=0.01',

            'auth-token': '',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36',

            'Accept-Encoding': 'gzip, deflate', 'sdch'
                                                'Accept-Language': 'en-US,en;q=0.8',
            'Cookie': ''
        }

        # -------syncinfo Admin --------------------------------------
        self.SyncInfo_ZIA_Admin_Users_Url = "https://mobileadmin.{}.net/webservice/api/web/adminusers/syncInfo".format(self.cloud)
        self.SyncInfo_ZIA_Admin_Users_Header = {
            'Connection': 'keep-alive',
            'Content-Length': '0',
            'Pragma': 'no-cache',
            'Cache-Control': 'no-cache',
            'Accept': 'application/json, text/javascript, */*; q=0.01',

            'auth-token': '',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36',

            'Accept-Encoding': 'gzip, deflate', 'sdch'
                                                'Accept-Language': 'en-US,en;q=0.8',
            'Cookie': ''
        }

        # -- get_zcc_list_from_ma ------------------------------------------------
        self.Get_Application_List_Url = 'https://mobile.{}.net/webservice/api/web/autoupdate/getApplicationList'.format(self.cloud)
        self.Get_Application_List_Header = {'Accept': 'application/json, text/javascript, */*; q=0.01',
                                                        'Accept-Encoding': 'gzip, deflate, br',
                                                        'Accept-Language': 'en-US,en;q=0.9',
                                                        'auth-token': '',
                                                        'Connection': 'keep-alive',
                                                        'Cookie': '',
                                                        'Host': 'mobile.{}.net'.format(self.cloud),
                                                        'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
                                                        'X-Requested-With': 'XMLHttpRequest'
                                                    }


        # -- Auto-Upgrade-ZCC ----------------------------------------------------
        self.Autoupdate_Url = "https://mobile.{}.net/webservice/api/web/autoupdate/getClientApplicationsHavingRegisteredDevices".format(self.cloud)
        self.Autoupdate_Header = {
                                    'Host': 'mobile.{}.net'.format(self.cloud),
                                    'Connection': 'keep-alive',
                                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
                                    'auth-token': '',
                                    'Origin': 'https://mobile.{}.net'.format(self.cloud),
                                    'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                    'Accept-Encoding': 'gzip, deflate, br',
                                    'Accept-Language': 'en-US,en;q=0.9',
                                    'Cookie': ''
                                }

        self.Auto_Upgrade_Payload = {
            "companyAutoUpdateEnabled":2,
            "windowsEnabledImage":{},
            "macEnabledImage":{},
            "linuxEnabledImage":{}
            }

        # -- Auto-Update- List -------------------------------------------------------------
        self.Autoupdate_Application_Url = "https://mobile.{}.net/webservice/api/web/autoupdate/updateApplicationList".format(self.cloud)
        self.Autoupdate_Application_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }

        self.Autoupdate_Application_Payload = {
                        "companyAutoUpdateEnabled": 2,
                        "windowsEnabledImage": {},
                        "macEnabledImage": {},
                        "linuxEnabledImage": {}
                        }

        # -- Get New Application List ------------------------------------------------------
        self.Get_NewApplicationList_Url = "https://mobile.{}.net/webservice/api/web/autoupdate/getNewApplicationList".format(self.cloud)
        self.Get_NewApplicationList_Header = {
                                    'Host': 'mobile.{}.net'.format(self.cloud),
                                    'Connection': 'keep-alive',
                                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
                                    'auth-token': '',
                                    'Origin': 'https://mobile.{}.net'.format(self.cloud),
                                    'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                    'Accept-Encoding': 'gzip, deflate, br',
                                    'Accept-Language': 'en-US,en;q=0.9',
                                    'Cookie': ''
                                }

        
        # -- Update Application List ---------------------------------------------------------------
        self.Autoupdate_NewApplication_Url = "https://mobile.{}.net/webservice/api/web/autoupdate/updateNewApplicationList".format(self.cloud)
        self.Autoupdate_NewApplication_Header = {
                                    'Host': 'mobile.{}.net'.format(self.cloud),
                                    'Connection': 'keep-alive',
                                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
                                    'auth-token': '',
                                    'Origin': 'https://mobile.{}.net'.format(self.cloud),
                                    'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                    'Accept-Encoding': 'gzip, deflate, br',
                                    'Accept-Language': 'en-US,en;q=0.9',
                                    'Cookie': ''
                                }

        self.Autoupdate_NewApplication_Payload = {
    "applications": [
        {
            "clientApplicationId": "3433",
            "buildEnabled": 0,
            "applicationVersion2": "********",
            "clientPlatform": 3
        },
        {
            "clientApplicationId": "3395",
            "buildEnabled": 0,
            "applicationVersion2": "********",
            "clientPlatform": 3
        },
        {
            "clientApplicationId": "3283",
            "buildEnabled": 0,
            "applicationVersion2": "********",
            "clientPlatform": 3
        },
        {
            "clientApplicationId": "3275",
            "buildEnabled": 0,
            "applicationVersion2": "********",
            "clientPlatform": 3
        },
        {
            "clientApplicationId": "3161",
            "buildEnabled": 0,
            "applicationVersion2": "4.0.0.70",
            "clientPlatform": 3
        },
        {
            "clientApplicationId": "3043",
            "buildEnabled": 0,
            "applicationVersion2": "3.9.0.160",
            "clientPlatform": 3
        },
        {
            "clientApplicationId": "2813",
            "buildEnabled": 1,
            "applicationVersion2": "3.8.0.100",
            "clientPlatform": 3
        },
        {
            "clientApplicationId": "2475",
            "buildEnabled": 1,
            "applicationVersion2": "3.7.0.88",
            "clientPlatform": 3
        },
        {
            "clientApplicationId": "2563",
            "buildEnabled": 0,
            "applicationVersion2": "3.6.1.25",
            "clientPlatform": 3
        },
        {
            "clientApplicationId": "3383",
            "buildEnabled": 1,
            "applicationVersion2": "3.9.0.71",
            "clientPlatform": 4
        },
        {
            "clientApplicationId": "3293",
            "buildEnabled": 1,
            "applicationVersion2": "3.7.1.42",
            "clientPlatform": 4
        },
        {
            "clientApplicationId": "3225",
            "buildEnabled": 0,
            "applicationVersion2": "3.7.1.38",
            "clientPlatform": 4
        },
        {
            "clientApplicationId": "3181",
            "buildEnabled": 0,
            "applicationVersion2": "3.7.0.171",
            "clientPlatform": 4
        },
        {
            "clientApplicationId": "3073",
            "buildEnabled": 1,
            "applicationVersion2": "3.7.0.148",
            "clientPlatform": 4
        },
        {
            "clientApplicationId": "2963",
            "buildEnabled": 1,
            "applicationVersion2": "3.6.1.30",
            "clientPlatform": 4
        },
        {
            "clientApplicationId": "2799",
            "buildEnabled": 1,
            "applicationVersion2": "3.6.1.27",
            "clientPlatform": 4
        },
        {
            "clientApplicationId": "3343",
            "buildEnabled": 0,
            "applicationVersion2": "1.4.0.79",
            "clientPlatform": 5
        },
        {
            "clientApplicationId": "3167",
            "buildEnabled": 0,
            "applicationVersion2": "1.4.0.43",
            "clientPlatform": 5
        },
        {
            "clientApplicationId": "2845",
            "buildEnabled": 1,
            "applicationVersion2": "********",
            "clientPlatform": 5
        }
    ]
}


        # -- App service List ---------------------------------------------------------------
        self.Get_appService_List_Url = "https://mobile.{}.net/webservice/api/web/appService/list?page=1&pageSize=100&search=".format(self.cloud)
        self.Get_appService_List_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }

        self.Get_appService_List_Payload = {

            "page": 1,
            "pageSize": 100,
            "search": "" }

        
        # -- Update AUP List ---------------------------------------------------------------
        self.Update_Aup_Url = "https://mobile.{}.net/webservice/api/web/cloud/updateAup".format(self.cloud)
        self.Update_Aup_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }

        self.Update_Aup_Payload = {
    "aup_type": 0,
    "aup_days": 0,
    "aup_data": "<b>Acceptable Use Policy is not configured for your company</b>"
}


        # -- Get AUP List ---------------------------------------------------------------
        self.Get_Cloud_Aup_Url = "https://mobile.{}.net/webservice/api/web/cloud/getAup".format(self.cloud)
        self.Get_Cloud_Aup_Header = {
                                    'Host': 'mobile.{}.net'.format(self.cloud),
                                    'Connection': 'keep-alive',
                                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
                                    'auth-token': '',
                                    'Origin': 'https://mobile.{}.net'.format(self.cloud),
                                    'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                    'Accept-Encoding': 'gzip, deflate, br',
                                    'Accept-Language': 'en-US,en;q=0.9',
                                    'Cookie': ''
                                }
        
         # -- Get Company Info ---------------------------------------------------------------
        self.Get_Company_Info_Url = "https://mobile.{}.net/webservice/api/web/company/getCompanyInfo".format(self.cloud)
        self.Get_Company_Info_Header = {
                                    'Host': 'mobile.{}.net'.format(self.cloud),
                                    'Connection': 'keep-alive',
                                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
                                    'auth-token': '',
                                    'Origin': 'https://mobile.{}.net'.format(self.cloud),
                                    'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                    'Accept-Encoding': 'gzip, deflate, br',
                                    'Accept-Language': 'en-US,en;q=0.9',
                                    'Cookie': ''
                                }
        # -- Get IDP Info ---------------------------------------------------------------
        self.Get_Idp_Info_Url = "https://mobile.{}.net/webservice/api/web/company/getIdpInfo".format(self.cloud)
        self.Get_Idp_Info_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }

         # -- Set IDP Info ---------------------------------------------------------------
        self.Set_Idp_Info_Url = "https://mobile.{}.net/webservice/api/web/company/setIdpInfo".format(self.cloud)
        self.Set_Idp_Info_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }

        self.Set_Idp_Info_Payload = {
            "token": "Admin@123",
            "description": "ajksdbsajbcsdjfbdjsk"
        }

        # Get SSL info ----------------------------------------------------------------------------------------------
        self.Get_SslCert_Info_Url = "https://mobile.{}.net/webservice/api/web/company/getSslCertInfo".format(self.cloud)
        self.Get_SslCert_Info_Header = {
                                    'Host': 'mobile.{}.net'.format(self.cloud),
                                    'Connection': 'keep-alive',
                                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
                                    'auth-token': '',
                                    'Origin': 'https://mobile.{}.net'.format(self.cloud),
                                    'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                    'Accept-Encoding': 'gzip, deflate, br',
                                    'Accept-Language': 'en-US,en;q=0.9',
                                    'Cookie': ''
                                }
        
        # Get Reminder Notification  ----------------------------------------------------------------------------------------------
        self.Get_Reminder_Notification_Url = "https://mobile.{}.net/webservice/api/web/notification/getReminderNotification".format(self.cloud)
        self.Get_Reminder_Notification_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }

        # Update Reminder Notification  ----------------------------------------------------------------------------------------------
        self.Create_Reminder_Notification_Url = "https://mobile.{}.net/webservice/api/web/notification/updateReminderNotification".format(self.cloud)
        self.Create_Reminder_Notification_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }

        self.Create_Reminder_Notification_Payload = {
            "reminder_type": 1,
            "reminder_msg": "Device was inactive from Zscaler Network. Check status and Enable service if required."
        }

        # Get Tray Notification  ----------------------------------------------------------------------------------------------
        self.Get_Tray_Notification_Url = "https://mobile.{}.net/webservice/api/web/notification/getTrayNotification".format(
            self.cloud)
        self.Get_Tray_Notification_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }

        # Update Tray Notification  ----------------------------------------------------------------------------------------------

        self.Create_Tray_Notification_Url = "https://mobile.{}.net/webservice/api/web/notification/updateTrayNotification".format(self.cloud)
        self.Create_Tray_Notification_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }

        self.Create_Tray_Notification_Payload = {
            "id": "457",
            "active": "1",
            "enableClientNotification": "1",
            "enableDlpNotification": "0",
            "enableAppUpdatesNotification": "0",
            "enableServiceStatusNotification": "1",
            "enableNotificationForZPAReauth": "0",
            "zpaReauthNotificationTime": "5",
            "customTimer": "5",
            "enablePersistantNotification": "0",
            "ziaNotificationPersistant": "0"
        }


        # Partner Device List  ----------------------------------------------------------------------------------------------      
        self.Partner_device_list_Url = "https://mobile.{}.net/webservice/api/web/partnerDevice/list".format(self.cloud)
        self.Partner_device_list_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }

        self.Partner_device_list_Payload = {
            "type": [0],
            "user": [0],
            "osId": [0],
            "version": "",
            "osVersion": "",
            "modelId": 0,
            "activeFromDays": "",
            "sortBy": "keepAliveTimestamp"
        }

        # List posture profile by company ---------------------------------------------------------------------------------
        self.Get_Posture_Profile_List_Url = "https://mobile.{}.net/webservice/api/web/postureProfile/listByCompany?page=1&pageSize=100".format(self.cloud)
        self.Get_Posture_Profile_List_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }

        self.Get_Posture_Profile_List_Payload = {

            "page": 1,
            "pageSize": 100
        }

        # Create device posture ---------------------------------------------------------------------------------
        self.CREATE_DEVICE_POSTURE_URL = "https://mobile.{}.net/webservice/api/web/postureProfile/edit".format(self.cloud)
        self.CREATE_DEVICE_POSTURE_HEADER = {'Connection': 'keep-alive',
                                             'Content-Length': '',
                                             'Pragma': 'no-cache',
                                             'Cache-Control': 'no-cache',
                                             'Accept': 'application/json, text/javascript, */*; q=0.01',

                                             'auth-token': '',
                                             'X-Requested-With': 'XMLHttpRequest',
                                             'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36',

                                             'Accept-Encoding': 'gzip, deflate', 'sdch'
                                                                                 'Accept-Language': 'en-US,en;q=0.8',
                                             'Cookie': ''
                                             }

        self.CREATE_DEVICE_POSTURE_PAYLOAD = {
                                                "id": -1,
                                                "name": "test12345",
                                                "description": "",
                                                "type": 3,
                                                "applyToMachineTunnel": "0",
                                                "registries": 
                                                [
                                                    {
                                                        "type": 3,
                                                        "registryKey": "test1",
                                                        "matchType": 2,
                                                        "nameValue": 
                                                        {
                                                            "name": "test1",
                                                            "data": "test1"
                                                        }
                                                    }
                                                ]
                                            }
        # Get posture profile ID -------------------------------------------------------------------------------
        self.Get_Device_Posture_List_Url = "https://mobile.{}.net/webservice/api/web/postureProfile/listByCompany?page=1&pageSize=100&search=&searchType=null".format(self.cloud)
        self.Get_Device_Posture_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }
        self.Get_Device_Posture_Payload = {

            "page": 1,
            "pageSize": 100,
            "search":"",
            "searchType": 'null'
        }

        # Delete posture profile --------------------------------------------------------------------------------
        self.Delete_Device_Posture_Url = "https://mobile.{}.net/webservice/api/web/postureProfile/delete?id=".format(self.cloud)
        self.Delete_Device_Posture_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }

        self.Delete_Device_Posture_Payload = {
                                                "id": ''
                                            }

        #Device Posture OS Version info --------------------------------------------------------------------------------------
        self.Get_Device_Posture_OS_Version_Url = "https://mobileadmin.zscalerbeta.net/webservice/api/web/postureProfile/list/devicePostureOsVersion/0?pageSize=1000".format(self.cloud)
        self.Get_Device_Posture_OS_Version_Healder = {
                                    'Host': 'mobile.{}.net'.format(self.cloud),
                                    'Connection': 'keep-alive',
                                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
                                    'auth-token': '',
                                    'Origin': 'https://mobile.{}.net'.format(self.cloud),
                                    'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                    'Accept-Encoding': 'gzip, deflate, br',
                                    'Accept-Language': 'en-US,en;q=0.9',
                                    'Cookie': ''
                                }
        
        self.Get_Device_Posture_OS_Version_Payload= {
             "pageSize": 1000
        }
        #get Privacy info --------------------------------------------------------------------------------------
        self.Get_Privacy_Info_Url = "https://mobile.{}.net/webservice/api/web/privacy/getPrivacyInfo".format(self.cloud)
        self.Get_Privacy_Info_Header = {
                                    'Host': 'mobile.{}.net'.format(self.cloud),
                                    'Connection': 'keep-alive',
                                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
                                    'auth-token': '',
                                    'Origin': 'https://mobile.{}.net'.format(self.cloud),
                                    'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                    'Accept-Encoding': 'gzip, deflate, br',
                                    'Accept-Language': 'en-US,en;q=0.9',
                                    'Cookie': ''
                                }
        
        # Set Privacy Info -------------------------------------------------------------------------------------------
        self.Set_Privacy_Info_Url = "https://mobile.{}.net/webservice/api/web/privacy/setPrivacyInfo".format(self.cloud)
        self.Set_Privacy_Info_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }

        self.Set_Privacy_Info_Payload = {
    "id": "-1",
    "active": "1",
    "collectUserInfo": "0",
    "collectMachineHostname": "0",
    "enablePacketCapture": "",
    "disableCrashlytics": "0",
    "collectZdxLocation": "1",
    "overrideT2ProtocolSetting": "0"
}

         
        
        # get traceroute policy info ---------------------------------------------------------------------------------
        self.Get_traceroutepoliyinfo_Url = "https://mobile.{}.net/webservice/api/web/traceroutePolicy/getTraceroutePolicyInfo".format(self.cloud)
        self.Get_traceroutepoliyinfo_Header = {
                                    'Host': 'mobile.{}.net'.format(self.cloud),
                                    'Connection': 'keep-alive',
                                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
                                    'auth-token': '',
                                    'Origin': 'https://mobile.{}.net'.format(self.cloud),
                                    'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                    'Accept-Encoding': 'gzip, deflate, br',
                                    'Accept-Language': 'en-US,en;q=0.9',
                                    'Cookie': ''
                                }
        

        # -- Set TraceroutepolicyInfo Info ---------------------------------------------------------------
        self.Set_TraceroutepolicyInfo_Url = "https://mobile.{}.net/webservice/api/web/traceroutePolicy/setTraceroutePolicyInfo".format(self.cloud)
        self.Set_TraceroutepolicyInfo_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }

        self.Set_TraceroutepolicyInfo_Payload = {
                "id": "",
                "active": "1",
                "collectRouteInfoMinutes": "0",
                "collectRouteInfoOnNetworkChangeEnabled": "0",
                "collectRouteInfoOnReportAnIssueEnabled": "0"
        }

        # get ZPA partnet login info ---------------------------------------------------------------------------------
        self.Get_ZpaPartnerLoginInfo_Url = "https://mobile.{}.net/webservice/api/web/zpaPartnerLogin/getZpaPartnerLoginInfo".format(self.cloud)
        self.Get_ZpaPartnerLoginInfo_Header = {
                                    'Host': 'mobile.{}.net'.format(self.cloud),
                                    'Connection': 'keep-alive',
                                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
                                    'auth-token': '',
                                    'Origin': 'https://mobile.{}.net'.format(self.cloud),
                                    'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                    'Accept-Encoding': 'gzip, deflate, br',
                                    'Accept-Language': 'en-US,en;q=0.9',
                                    'Cookie': ''
                                }
        
        # -- Set ZPA partne login Info ---------------------------------------------------------------
        self.Set_ZpaPartnerLoginInfo_Url = "https://mobile.{}.net/webservice/api/web/zpaPartnerLogin/setZpaPartnerLoginInfo".format(self.cloud)
        self.Set_ZpaPartnerLoginInfo_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }

        self.Set_ZpaPartnerLoginInfo_Payload = {
                    "id": "0",
                    "partnersAllowedToLogin": "1",
                    "userAllowedToAddPartner": "0"
                }      

        # get user agent suffix ---------------------------------------------------------------------------------
        self.Get_user_agent_suffix_Url = "https://mobile.{}.net/webservice/api/web/userAgentSuffix/getUserAgentSuffix".format(self.cloud)
        self.Get_user_agent_suffix_Header = {
                                    'Host': 'mobile.{}.net'.format(self.cloud),
                                    'Connection': 'keep-alive',
                                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
                                    'auth-token': '',
                                    'Origin': 'https://mobile.{}.net'.format(self.cloud),
                                    'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                    'Accept-Encoding': 'gzip, deflate, br',
                                    'Accept-Language': 'en-US,en;q=0.9',
                                    'Cookie': ''
                                }
        
        # -- Set user agent suffix ---------------------------------------------------------------
        self.Set_user_agent_suffix_Url = "https://mobile.{}.net/webservice/api/web/userAgentSuffix/setUserAgentSuffix".format(self.cloud)
        self.Set_user_agent_suffix_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }

        self.Set_user_agent_suffix_Payload = {
             
                            "enableUserAgentSuffixForAll": 1,
                            "userAgentSuffixForAll": ""
                        }
        

        # get Domains ---------------------------------------------------------------------------------
        self.Get_UserAgentSuffix_domain_Url = "https://mobile.{}.net/webservice/api/web/userAgentSuffix/getDomains".format(self.cloud)
        self.Get_UserAgentSuffix_domain_Header = {
                                    'Host': 'mobile.{}.net'.format(self.cloud),
                                    'Connection': 'keep-alive',
                                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
                                    'auth-token': '',
                                    'Origin': 'https://mobile.{}.net'.format(self.cloud),
                                    'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                    'Accept-Encoding': 'gzip, deflate, br',
                                    'Accept-Language': 'en-US,en;q=0.9',
                                    'Cookie': ''
                                }
        

        # -- Update Trusted Network ---------------------------------------------------------------
        self.Update_Trusted_Network_Url = "https://mobile.{}.net/webservice/api/web/trustedNetwork/update".format(self.cloud)
        self.Update_Trusted_Network_Header = {
            'Host': 'mobile.{}.net'.format(self.cloud),
            'Connection': 'keep-alive',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
            'auth-token': '',
            'Origin': 'https://mobile.{}.net'.format(self.cloud),
            'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cookie': ''
        }

        self.Update_Trusted_Network_Payload = {
                        "networkName": "new",
                        "addCondition": "",
                        "undefined": "",
                        "conditionTypeTrustedNetwork": "",
                        "dnsServers": "*******",
                        "dnsSearchDomains": "",
                        "hostnames": "",
                        "resolvedIpsForHostname": "",
                        "ssid": "",
                        "conditionType": 1,
                        "id": -1,
                        "guid": -1,
                        "active": "true",
                        "resolvedIps": ""
                }
        
    ############################################################################################# Monika END ##########################################################################################

     #############################        ### Mridul API  URL and PAYLOAD###     ###########################################################
        # -- get sync info url
        self.Get_Sync_Info_URL="https://mobile.{}.net/webservice/api/web/getSyncInfo".format(self.cloud)
        self.Get_Sync_Info_Header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36'
        }

        
        
        # --  get Partner Devices Details 
        self.Partner_User_By_Company_URL="https://mobileadmin.{}.net/webservice/api/web/partnerUsersByCompany?page=1&pageSize=100&search=".format(self.cloud)
        self.Partner_User_By_Company_Header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36'
        }

        # --  Get User Device Details
        self.User_By_Company_URL="https://mobileadmin.{}.net/webservice/api/web/usersByCompany?page=1&pageSize=100&search=".format(self.cloud)
        self.User_By_Company_Header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36'

        }

        # -- Group By Company App Profiles
        self.Groups_By_Comapny_URL="https://mobileadmin.{}.net/webservice/api/web/groupsByCompany?page=1&pageSize=100&search=".format(self.cloud)
        self.Groups_By_Company_Header={
             'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36'
        }

        # -- Get Audit Logs Url
        self.Get_Audit_Logs_URL="https://mobileadmin.{}.net/webservice/api/web/getAuditLogsUrl".format(self.cloud)
        self.Get_Audit_logs_URL_header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36'
        }


        # -- Get ZPA entitlement Url 
        self.Get_ZPA_Entitlement_URL="https://mobileadmin.{}.net/webservice/api/web/getGroupEntitlementEnabled?page=1&pageSize=10000&search=".format(self.cloud)
        self.Get_ZPA_Entitlement_header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36' 
        }


        # -- Get ZDX entitlement Url 
        self.Get_ZDX_Entitlement_URL="https://mobileadmin.{}.net/webservice/api/web/getUpmGroupEntitlementEnabled?page=1&pageSize=10000&search=".format(self.cloud)
        self.Get_ZDX_Entitlement_header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36' 
        }

        # -- Get ZDX entitlement Url 
        self.Get_ZDX_Entitlement_URL="https://mobileadmin.{}.net/webservice/api/web/getUpmGroupEntitlementEnabled?page=1&pageSize=10000&search=".format(self.cloud)
        self.Get_ZDX_Entitlement_header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36' 
        }
        self.SET_ZDX_Entitlement_payload = {"enableForAll":0,"computeDeviceGroups":0,"deviceGroups":[],"groups":[],"logoutZCC":0}
        self.Set_ZDX_Entitlement_URL = "https://mobileadmin.{}.net/webservice/api/web/updateGroupEntitlementUPM".format(self.cloud)
        self.Set_ZDX_Entitlement_header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36' 
        }
        
        # -- Get ZIA entitlement Url 
        self.Get_ZIA_Entitlement_URL="https://mobileadmin.{}.net/webservice/api/web/getZiaGroupEntitlementEnabled?page=1&pageSize=10000&search=".format(self.cloud)
        self.Get_ZIA_Entitlement_header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36' 
        }

        # -- Toggle zia
        self.ZIA_URL = "https://mobileadmin.{}.net/webservice/api/web/updateGroupEntitlementZIA".format(self.cloud)
        self.ZIA_HEADER = {
                                    'Host': 'mobile.{}.net'.format(self.cloud),
                                    'Connection': 'keep-alive',
                                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
                                    'auth-token': '',
                                    'Origin': 'https://mobile.{}.net'.format(self.cloud),
                                    'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                    'Accept-Encoding': 'gzip, deflate, br',
                                    'Accept-Language': 'en-US,en;q=0.9',
                                    'Cookie': ''
                                }
        self.ZIA_PAYLOAD = {"groups": [], "enableForAll": 1, "deviceGroups": [], "computeDeviceGroups": 0, "logoutZCC": 0}

        # -- Get Device CleanUp Info
        self.Get_DeviceCleanUpInfo_URL="https://mobileadmin.{}.net/webservice/api/web/deviceCleanup/getDeviceCleanupInfo".format(self.cloud)
        self.Get_DeviceCleanUpInfo_header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36' 
        }

        # -- Set Device Cleanup enable/disable
        self.set_deviceCleanup_URL="https://mobileadmin.{}.net/webservice/api/web/deviceCleanup/setDeviceCleanupInfo".format(self.cloud)
        self.set_deviceCleanup_header = {
                                    'Host': 'mobile.{}.net'.format(self.cloud),
                                    'Connection': 'keep-alive',
                                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36',
                                    'auth-token': '',
                                    'Origin': 'https://mobile.{}.net'.format(self.cloud),
                                    'Referer': 'https://mobile.{}.net/index.html'.format(self.cloud),
                                    'Accept-Encoding': 'gzip, deflate, br',
                                    'Accept-Language': 'en-US,en;q=0.9',
                                    'Cookie': ''
                                }



        # -- Get Device Group List by Company
        self.Get_DeviceGroup_ListByCompany_URL="https://mobileadmin.{}.net/webservice/api/web/deviceGroup/listByCompany?page=1&pageSize=100&search=&searchType=null".format(self.cloud)
        self.Get_DeviceGroup_ListByCompany_header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36' 
        }


        # -- Get EndPoint Integration info 
        self.Get_EndpointIntregration_Info_URL="https://mobileadmin.{}.net/webservice/api/web/endpointIntegration/getEndpointIntegrationInfo".format(self.cloud)
        self.Get_EndpointIntregration_Info_header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36' 
        }


        # -- Set Endpoint Integration Info
        self.Set_EndpointIntregration_Info_URL="https://mobileadmin.{}.net/webservice/api/web/endpointIntegration/setEndpointIntegrationInfo".format(self.cloud)
        self.Set_EndpointIntregration_Info_header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36' 
        }


        # -- get app fail open id
        self.Get_AppFailOpen_URL="https://mobileadmin.{}.net/webservice/api/web/failOpenPolicy/listByCompany".format(self.cloud)
        self.Get_AppFailOpen_Header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36' 
        }

        # -- edit Fail open Policy
        self.edit_AppFailOpen_URL="https://mobileadmin.{}.net/webservice/api/web/failOpenPolicy/edit".format(self.cloud)
        self.edit_AppFailOpen_Header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36'
        }
        

        # --  Get Machine Tunnel List
        self.get_MachineTunnel_List_URL="https://mobileadmin.{}.net/webservice/api/web/machineTunnel/listByCompany?page=1&pageSize=100&search=&machineState=1&active=1".format(self.cloud)
        self.get_MacineTunnel_List_Header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36'
        }


        # -- Get App Profiles using Machine Token from MA 
        self.get_machineToken_List_URL="https://mobileadmin.{}.net/webservice/api/web/machineToken/list?pageNo=1&pageSize=50".format(self.cloud)
        self.get_machineToken_List_header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36'
        }


        # -- Get Device Groups Info from MA 
        self.get_device_groups_info_URL="https://mobileadmin.{}.net/webservice/api/web/deviceGroup/listByCompany?page=1&pageSize=100&search=".format(self.cloud)
        self.get_device_groups_header={
            'auth-token':'',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.80 Safari/537.36'
        }

######################################################### Mridul ENDS ###########################################################################


# --------------------------------------- ZPA DEFINITIONS ---------------------------------------
class zpa_definition(admin_res):
    def __init__(self, cloud):
        # -------Authenticate_To_ZPA-----------------------------
        if cloud == "zscalerbeta":
            self.cloud = "zpabeta.net"
            self.Get_Zpa_Diag_Url = "https://zpadds1.{0}".format(
                self.cloud) + "/zpadrilldownservice/zpn/diagnostics/v3/customers/{0}/latestUserTxn/startTime/{1}/endTime/{2}/pageSize/60"
        else:
            self.cloud = "private.zscaler.com"
            if cloud == "zscalerone" or cloud == "zscloud":
                self.Get_Zpa_Diag_Url = "https://us4-zpa-dds.{0}".format(
                    self.cloud) + "/zpadrilldownservice/zpn/diagnostics/v3/customers/{0}/latestUserTxn/startTime/{1}/endTime/{2}/pageSize/60"
            else:
                self.Get_Zpa_Diag_Url = "https://us3-zpa-dds.{0}".format(
                    self.cloud) + "/zpadrilldownservice/zpn/diagnostics/v3/customers/{0}/latestUserTxn/startTime/{1}/endTime/{2}/pageSize/60"
        self.ZPA_AUTH_URL = "https://us1-zpa-authn.{}/authn/v1/oauth/token?grant_type=USER".format(self.cloud)
        self.ZPA_AUTH_HEADER = {
            "method": "POST",
            "path": "/authn/v1/oauth/token?grant_type=USER",
            "scheme": "https",
            "accept": "application/json, text/javascript, */*; q=0.01",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "en-US,en;q=0.9",
            "content-length": "",
            "content-type": "application/x-www-form-urlencoded",
            "cookie": "",
            "sec-ch-ua": '" Not A;Brand";v="99", "Chromium";v="90", "Google Chrome";v="90"',
            "sec-ch-ua-mobile": "?0",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-site",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.93 Safari/537.36"
        }
        # -------------------------------------------------------
        # -------ZPA Posture Fetch-----------------------------
        self.ZPA_POSTURE_URL = "https://api.{}/zpn/api/v1/admin/customers/CUST_ID/posture".format(self.cloud)
        self.ZPA_POSTURE_HEADER = {
            'authorization': ''
        }
        # -------ZPA Policy Set ID-----------------------------
        self.GET_POLICY_SET_ID_TIMEOUT_POLICY_URL = "https://api.{}/zpn/api/v1/admin/customers/CUST_ID/policySet/policyType/REAUTH_POLICY".format(self.cloud)
        self.GET_POLICY_SET_ID_ACCESS_POLICY_URL = "https://api.{}/zpn/api/v1/admin/customers/CUST_ID/policySet/policyType/GLOBAL_POLICY".format(self.cloud)
        self.GET_POLICY_SET_ID_BYPASS_POLICY_URL = "https://api.{}/zpn/api/v1/admin/customers/CUST_ID/policySet/policyType/BYPASS_POLICY".format(self.cloud)
        
        # -------ZPA Policy Fetch-----------------------------
        self.GET_ZPA_POLICY_URL = "https://api.{}/zpn/api/v1/admin/customers/CUST_ID/policySet/rules/policyType/POLICY_TYPE/?page=1&pagesize=100".format(
            self.cloud)

        # -------ZPA Rule Order-----------------------------
        self.ZPA_RULE_ORDER_URL = "https://api.{}/zpn/api/v1/admin/customers/CUST_ID/policySet/POLICY_SET_ID/rule".format(
            self.cloud)
        self.ZPA_POLICY_SET_HEADER = {
            'method': 'GET',
            'path': '/zpn/api/v1/admin/customers/CUST_ID/policySet/policyType/GLOBAL_POLICY',
            'scheme': 'https',
            'accept': '*/*',
            'accept-encoding': 'gzip, deflate, br',
            'accept-language': 'en-US,en;q=0.9',
            'authorization': 'Bearer ',
            'cookie': '',
            'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="90", "Google Chrome";v="90"',
            'sec-ch-ua-mobile': '?0',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.93 Safari/537.36'
        }

        # -------ZPA Access Policy-----------------------------
        self.ZPA_ACCESS_POLICY_URL = "https://api.{}/zpn/api/v2/admin/customers/CUST_ID/policySet/POLICY_SET_ID/rule".format(
            self.cloud)

        self.ZPA_ACCESS_POLICY_PAYLOAD = {
            "policySetId": "",
            "conditions": [
                {
                    "operator": "OR",
                    "operands": [
                        {
                            "entryValues": [],
                            "objectType": ""
                        }
                    ]

                }
            ],
            "name": "",
            "description": "",
            "action": "ALLOW",
            "assistantGroups": [],
            "appServerGroups": [],
            "customMsg": ""
        }
        self.ZPA_ACCESS_POLICY_HEADER = {
            'method': 'POST',
            'path': '',
            'scheme': 'https',
            'accept': 'application/json, text/javascript, */*; q=0.01',
            'accept-encoding': 'gzip, deflate, br',
            'accept-language': 'en-US,en;q=0.9',
            'authorization': 'Bearer ',
            'content-length': '',
            'content-type': 'application/json',
            'cookie': '',
            'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="90", "Google Chrome";v="90"',
            'sec-ch-ua-mobile': '?0',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.93 Safari/537.36'
        }

        self.ZPA_POILCYSET_V2_URL = "https://api.{}/zpn/api/v2/admin/customers/CUST_ID/policySet/POLICY_SET_ID/rule".format(
            self.cloud)

        # -------ZPA Delete Policy-----------------------------
        self.ZPA_ACCESS_POLICY_DELETE_URL = "https://api.{}/zpn/api/v2/admin/customers/CUST_ID/policySet/POLICY_SET_ID/rule/RULE_ID".format(
            self.cloud)
        self.ZPA_ACCESS_POLICY_DELETE_HEADER = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Authorization': 'Bearer ',
            'Connection': 'keep-alive',
            'Cookie': '',

        }

        # -------ZPA APP SEGMENT FETCH ----------------------------------------------------
        self.GET_ZPA_APP_SEGMENT_DATA_URL = "https://api.{}/zpn/api/v1/admin/customers/CUST_ID/v2/application?page=1&pagesize=20".format(
            self.cloud)
        # -------ZPA Edit App Segment-----------------------------
        self.EDIT_ZPA_APP_SEGMENT_URL = "https://api.{}/zpn/api/v1/admin/customers/CUST_ID/v2/application/APPLICATION_ID".format(
            self.cloud)
        self.ZPA_ENABLE_DISABLE_PAYLOAD = {
            "applicationGroupId": "72058095475359956",
            "applicationGroupName": "test1_segmentgroup",
            "bypassType": "NEVER",
            "clientlessApps": None,
            "configSpace": "DEFAULT",
            "description": "",
            "domainNames": [
                "enabledisableapp.com"
            ],
            "doubleEncrypt": False,
            "enabled": False,
            "healthReporting": "ON_ACCESS",
            "icmpAccessType": None,
            "id": "72058095475368639",
            "inspectionApps": None,
            "ipAnchored": False,
            "isCnameEnabled": True,
            "name": "enable_disable_app",
            "selectConnectorCloseToApp": False,
            "selectedServerGroups": [
                "72058095475359957"
            ],
            "serverGroups": [
                {
                    "id": "72058095475359957"
                }
            ],
            "sraApps": None,
            "tcpPortRanges": [
                "1",
                "65535"
            ],
            "udpPortRanges": [
            ]
        }
        self.ZPA_ENABLE_DISABLE_HEADER = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Authorization': '',
            'Connection': 'keep-alive',
            'Content-Length': '',
            'Content-Type': 'application/json',
            'Cookie': '',
            'sec-ch-ua': '"Google Chrome";v="93", " Not;A Brand";v="99", "Chromium";v="93"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': "",
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36'
        }

        # -- Timeout Policy -------------------------------------------------------------------------
        self.Timeout_Policy_URL = "https://api.{0}/zpn/api/v2/admin/customers/{1}/policySet/{2}/rule/{3}"
        self.Timeout_Policy_Header = {
            "method": "PUT",
            "scheme": "https",
            "accept": "application/json, text/javascript, */*; q=0.01",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "en-US,en;q=0.9",
            "authorization": "",
            "content-length": "279",
            "content-type": "application/json",
            "cookie": "",
            "sec-ch-ua": '" Not A;Brand";v="99", "Chromium";v="96", "Google Chrome";v="96"',
            "sec-ch-ua-mobile": '?0',
            "sec-ch-ua-platform": "Windows",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-site",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
        }
        self.Timeout_Policy_Payload = {
            "policySetId": "",
            "id": "",
            "conditions": [],
            "name": "Default_Rule",
            "description": "This is the default Timeout Policy rule",
            "action": "RE_AUTH",
            "customMsg": "Your access to internal Applications has expired",
            "reauthTimeout": 600,
            "reauthIdleTimeout": -1
        }
        # -- ZPA DNS SUffix
        self.Add_DNS_Suffix_Url = 'https://api.{}/zpn/api/v1/admin/customers/CUST_ID/v2/associationtype/SEARCH_SUFFIX/domains'.format(
            self.cloud)
        self.Add_DNS_Suffix_Payload = [{"domain": "", "capture": True}]

        #-- ZPA URL'S,Headers And Payload for Creating ZPA Applications --------------------------------------------------------------------------------------------------------------------------------------
        
        
       
        #--  ZPA SIGN URL AND HEADERS
        
        self.ZPA_Sigin_Url = "https://api{}/shift/api/v1/signin".format(
            self.cloud)

        self.ZPA_Headers = {'Content-type': 'application/json', 'Accept': '*/*'}
        self.ZPA_Signin_Payload = {
                'username' : '',
                'password' : ''
            }

        #--  ZPA URLS FOR SERVER , SERVER GROUP , CONNECTOR GROUP , APPLICATIONS GROUP AND APLLICATIONS CREATION
        
        self.Current_Apps_Number_URL = "https://api{}/zpn/api/v1/admin/customers/CUST_ID/v2/application/count/currentAndMaxLimit".format(
        self.cloud)
        self.ZPA_App_Url = "https://api{}/shift/api/v1/admin/customers/CUST_ID/v2/application".format(
        self.cloud)
        self.ZPA_App_Group_Url = "https://api{}/shift/api/v1/admin/customers/CUST_ID/applicationGroup/".format(
        self.cloud)
        self.ZPA_Server_Url = "https://api{}/zpn/api/v1/admin/customers/CUST_ID/server/".format(
        self.cloud)
        self.ZPA_Server_Group_Url = "https://api{}/zpn/api/v1/admin/customers/CUST_ID/serverGroup/".format(
        self.cloud)
        self.Get_Connector_ID_Url ="https://api{}/zpn/api/v1/admin/customers/CUST_ID/assistantGroup?page=1&pagesize=20".format(
        self.cloud)
        self.Get_Servers_List_Url = "https://api{}/zpn/api/v1/admin/customers/CUST_ID/server?page=1&pagesize=500".format(
        self.cloud)
        
        
        

        #--  ZPA PAYLOAD FOR SERVER , SERVER GROUP , CONNECTOR GROUP , APPLICATIONS GROUP AND APLLICATIONS CREATION
        self.ZPA_App_Group_Payload = {"enabled":"true","name":'',"description":"","applications":[]}
        self.ZPA_Server_Payload = {"enabled":"true","name": "" ,"description":"","address":"","appServerGroupIds":[]}
        self.ZPA_ServerGroup_Payload = {"enabled":"true","learningEnabled":"False","name":"","description":"","servers":[{"id":"","name":""}],"assistantGroups":[{"id":"","name":""}]}
        self.ZPA_APP_Payload = {"enabled":"true","applicationGroupId":'',"name":'',"domain":'',"isClientlessDomain":"False","serverGroups":[{"enabled":"true","learningEnabled":"true","id":'',"name":'',"configSpace":"DEFAULT","servers":[]}],"tcpPortRanges":["80","80","443","443"],"udpPortRanges":[],"domainNames":'',"clientlessApps":[]}
        

    

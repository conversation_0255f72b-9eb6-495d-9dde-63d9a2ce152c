import os
import sys
import json
current = os.path.dirname(os.path.realpath(__file__))
parent = os.path.dirname(current)
gparent = os.path.dirname(parent)
sys.path.append(parent)
# sys.path.append(gparent)
# sys.path.append(gparent + "/Config/")

class MAZA_RES:
    def __init__(self, cloudname, Variable=None):
    #----------------------------------Auth to ZDX-Admin------------------------------------------
        self.var = Variable if Variable else None
        self.domain = ""
        if "." in cloudname: self.domain = cloudname
        else: self.domain = f"mobile.{cloudname}.net"
        self.authurl = f"https://{self.domain}/webservice/api/auth/supportLogin"
        self.loadconfigurl = f"https://{self.domain}/webservice/api/web/support/loadConfigProperties"
        self.authheader = { #only meant to be used for fetching x-csrf-token before login, else session header is enough when session is active
           "Content-Type": "application/json",
           "Sec-Fetch-Site": "same-origin",
           "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36 Edg/124.0.0.0"
           }
        self.setmaxpagesize = {'pageSize': 1000}
        self.authtokenPath = os.path.join(self.var.automationloc,"Tokens") if self.var else os.path.join(os.path.dirname(os.path.realpath(__file__)), "Tokens")
        self.authtokenFile = os.path.join(self.authtokenPath, "MAZA_ADMIN_AUTH.txt")
        
        #--------------------------list-companies--------------------------------
        self.listallcompaniesurl = f"https://{self.domain}/webservice/api/web/support/listAllCompanies?pageSize=1000"

        #--------------------------getFeatureEnablement--------------------------
        self.getFeatureEnablementurl = f"https://{self.domain}/webservice/api/web/support/getFeatureEnablementControls"

        self.getFeatureEnablementByIdurl = f"https://{self.domain}/webservice/api/web/support/getFeatureEnablementControl"
        
        self.enableFeaturepayload = {
                                        "id": "",
                                        "featureValue": "1",
                                        "ziaFeature": True,
                                        "zpaFeature": True,
                                        "enableForNewCompany": False,
                                        "enableForAll": False,
                                        "companyIds": []
                                    }
        
        #-----------------------------Edit feature------------------------------------------#
        self.editFeatureEnablementurl = f"https://{self.domain}/webservice/api/web/support/editFeatureEnablementControl"
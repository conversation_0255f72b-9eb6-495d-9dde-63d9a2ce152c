import os,logging,datetime,json,shutil,platform
import hashlib,wmi,subprocess
import winreg
import winreg as wrg 
from winreg import*
from pathlib import Path, PureWindowsPath, PurePosixPath
from zdx.Lib.common_lib.admin_ops import admin_ops
from zdx.Lib.Windows.System import System
import hashlib
import win32security
import os
from packaging.version import Version
import yaml
regkey_paths = ["SOFTWARE\\Zscaler Inc.\\Zscaler\\","SOFTWARE\\WOW6432Node\\Zscaler Inc.\\Zscaler\\"]

class Node:
    def __init__(self, data):
        self.obj = data
        self.next = None
        self.prev = None

class DCLLContinuousIter:
    def __init__(self, datastructure):
        self._datastructure = datastructure
        self._startnode = None
        self.__prevnode =None
    
    def __next__(self):
        if self._startnode == None:
            self._startnode = self._datastructure._global_head
            self.__prevnode = self._startnode
            return self._startnode
        if self._startnode.next is None:
            self._startnode = self.__prevnode
        self.__prevnode = self._startnode
        self._startnode = self._startnode.next
        
        if self._startnode == self._datastructure._global_head:
            raise StopIteration

        return self._startnode
        

class DCLL:
    def __init__(self):
        self._global_head = None
        self.travel_head=None
        self.__len = 0
    def __iter__(self):
        return DCLLContinuousIter(self)

    def __len__(self):
        return self.__len
    
    def append(self, data):
        new_node = Node(data = data)
        if not self.travel_head:
            new_node.next = new_node
            new_node.prev= new_node

            self.travel_head= new_node
            self._global_head= self.travel_head
            self.__len  +=1
        else:
            new_node.next=self._global_head
            new_node.next.prev =new_node
            self.travel_head.next = new_node
            new_node.prev = self.travel_head
            self.travel_head = new_node
            self.__len+=1
        
    def insert(self, data):
        new_node = Node(data = data)
        if not self.travel_head:
            new_node.next = new_node
            new_node.prev = new_node
            self.travel_head = new_node
            self._global_head = self.travel_head
            self.__len +=1
        else:
            new_node.next = self._global_head
            new_node.next.prev =new_node
            self.travel_head.next = new_node
            new_node.prev = self.travel_head
            self._global_head=new_node
            self.__len +=1

    def delete(self,node:Node):
        
        if node.next == node and node.prev == node:
            self.travel_head = None
            self._global_head = None
            node.next=None
            node.prev=None
            node.obj=None
            del(node)
            self.__len -=1
            return
        node.prev.next= node.next
        node.next.prev = node.prev
        node.next=None
        node.prev=None
        node.obj=None
        del(node)
        self.__len -=1
        return
    
class Logger:
    def __init__(self, Log_Level, Log_File_Name=None, automationSessionFileName= "", cleanup=False):
        if Log_File_Name == None: Log_File_Name = f"Automation-{str(datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S'))}.log"
        if cleanup : self.cleanup(Log_File_Name=Log_File_Name[:-4])
        self.logging= self.Initialize_Logger(Log_File_Name=Log_File_Name, Log_Level=Log_Level, automationSessionFileName=automationSessionFileName)
    
    def Initialize_Logger(self,
                          automationSessionFileName,
                          Log_File_Name,  # (str)     Name of the Log file to be created
                          Log_Level,  # (str)     Level of logging (INFO/DEBUG/WARNING/CRICITCAL etc)
                          console_handle_needed=True  # (bool)    print_ on command line if True only
                          ):
        """
        Target: Create Logger object.
        Work Flow
        ----------
            1. Create a logger object.
            2. Create a console handler if required
                - console handler is used to print_ required info on command line window.
            3. Create a file handler -> mandatory
                - Make sure 'Logs' folder exists, as this file handler will be created in this folder only.
                - file handler is used to send all information to a log file in specified folder.
            4. Set the debug level (INFO/DEBUG/WARNING/CRITICAL etc). Default: INFO
            5. Specify how the logging must happen (with/without datetime/loglevel/username)
            6. Add all created handlers to the logger instance created in step 1.
            7. return the logger instance.
        """
        # 1, 2 & 3
        self.Reports_Directory = os.path.join(os.path.dirname(os.path.abspath("trigger_automation_zdx.py")),"zdx","Misc", "Reports", f'{Log_File_Name[:-4]}', f'{automationSessionFileName}')
        # self.Reports_Directory = os.path.join(os.path.dirname(os.path.dirname(os.path.realpath(__file__))),"Misc", "Reports", f'{Log_File_Name[:-4]}')
        self.Logs_Directory = os.path.join(self.Reports_Directory, "Logs")
        logger = logging.getLogger(Log_File_Name)
        if console_handle_needed: self.__console_handler = logging.StreamHandler()
        if not os.path.exists(self.Logs_Directory): os.makedirs(self.Logs_Directory)
        self.__file_handler = logging.FileHandler(os.path.join(self.Logs_Directory, Log_File_Name))

        # 4
        if Log_Level:
            logger.setLevel(eval("logging.{}".format(Log_Level)))
            if console_handle_needed: self.__console_handler.setLevel(eval("logging.{}".format(Log_Level)))
            self.__file_handler.setLevel(eval("logging.{}".format(Log_Level)))

        # 5
        if console_handle_needed: self.__console_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))
        self.__file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))

        # 6
        if console_handle_needed: logger.addHandler(self.__console_handler)
        logger.addHandler(self.__file_handler)
        # 7
        return logger
    
    def cleanup(self, Log_File_Name):
        Reports_Directory = os.path.join(os.path.dirname(os.path.abspath("trigger_automation_zdx.py")),"zdx", "Misc", "Reports")
        #Reports_Directory = os.path.join(os.path.dirname(os.path.dirname(os.path.realpath(__file__))),"Misc", "Reports")
        # Create object of ZipFile
        if os.path.exists(Reports_Directory): 
            #for folder in glob.glob(os.path.join(Reports_Directory, "Automation-*")):
            for folder in Path(Reports_Directory).iterdir():
                if not folder.suffix == ".zip" and not folder.name == Log_File_Name:
                    shutil.make_archive(folder, 'zip', folder)
                    shutil.rmtree(folder)

    def remove_handler(self):
        try:
            self.logging.removeHandler(self.__console_handler)
            self.logging.removeHandler(self.__file_handler)
            return [True, None]
        except Exception as E:
            return [False,f"{E}"]
class Config:
    def __init__(self,ConfigFile,isyaml=False):
        # self.configdir = os.path.join(os.path.dirname(os.path.abspath("trigger_automation_zdx.py")),"zdx", "Config")
        self.configdir = os.path.join(os.path.dirname(os.path.abspath("trigger_automation_zdx.py")), "config")
        self.config=None
        self.get_config(ConfigDir=self.configdir,ConfigFile=ConfigFile.strip(),isyaml=isyaml)
        
    def get_config(self,ConfigDir, ConfigFile, isyaml=False):
        if os.path.exists(os.path.join(ConfigDir, ConfigFile)):
            with open(os.path.join(ConfigDir, ConfigFile)) as config_file:
                self.config = json.load(config_file) if not isyaml else yaml.full_load(config_file)
                return
        for f in os.listdir(ConfigDir):
            path = os.path.join(ConfigDir, f)
            if os.path.isdir(path):
                self.get_config(ConfigDir=path, ConfigFile=ConfigFile)
            if len(self.config) > 0:
                return

class Handle_Registry:
    def __init__(self):
        self.connectkey = None
        self.subkeys = []
        self.user_sid = str(self.get_sid_from_registry())
        
    def get_sid_from_registry(self):
        Registry = ConnectRegistry(None, HKEY_LOCAL_MACHINE)
        for item in regkey_paths:
            try:
                key = OpenKey(Registry, item)
                val = wrg.QueryValueEx(key,"SID") 
                if val[0] != None:
                    return val[0]
            except:
                pass
        
    def connect_registry_key(self, keypath):
        '''Connects to a registry keyconnection
        
        Args:
            keypath (str): HKCU\\\\SOFTWARE\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall
        
        Returns:
            self.connectkey (winreg handle object): This object wraps a Windows HKEY object
        '''
        if keypath.startswith('HKLM'):
            self.connectkey = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, keypath.strip('HKLM\\'))
        elif keypath.startswith('HKCU'):
            self.connectkey = winreg.OpenKey(winreg.HKEY_CURRENT_USER, keypath.strip('HKCU\\'))        
        elif keypath.startswith('HKCR'):
            self.connectkey = winreg.OpenKey(winreg.HKEY_CLASSES_ROOT, keypath.strip('HKCR\\'))
        elif keypath.startswith('HKEY_USERS'):
            self.connectkey = winreg.OpenKey(winreg.HKEY_USERS, keypath.strip('HKU\\'))
        else:
            raise Exception('Cannot find Registry {}'.format(keypath))    
        print('Connected to Registry {}'.format(keypath))   
        
    def connect_subkey(self, subkey):
        '''Connect to a subkey of the already opened registry key object self.connectkey
        
        Args:
            subkey (str): a string representing the subkey's name. i.e: '{224A2C9B-5304-BA73-072A-FED79CD1B7E4}','Google Chrome' 
        
        Returns:
            A winreg handle object for the subkey
        '''    
        return winreg.OpenKey(self.connectkey, subkey)  
        
    def get_subkeys(self):
        '''Get a list of subkeys of the connected registry key

        Returns:
            self.subkeys (list): A list of subkey names
        '''    
        for i in range(winreg.QueryInfoKey(self.connectkey)[0]):
            subkey = winreg.EnumKey(self.connectkey, i)
            self.subkeys.append(subkey)
            
    def get_key_display_name(self, keyconnection):
        '''Get the registry key's DisplayName(value) 
        
        Args:
            keyconnection (A winreg handle object) that is connected to the key or subkey
            
        Returns:
            Empty string if no such value, else return the DisplayName(str)
        '''        
        try:
            return winreg.QueryValueEx(keyconnection, "DisplayName")[0]
        except:
            return ''

    def get_key_publisher(self, keyconnection):
        '''Get the registry key's Publisher(value) 
        
        Args:
            keyconnection (A winreg handle object) that is connected to the key or subkey
         
        Returns:
            Empty string if no such value, else return the Publish(str)
        '''     
        try:
            return winreg.QueryValueEx(keyconnection, "Publisher")[0]
        except:
            return ''  
            
    def get_key_install_location(self, keyconnection):
        '''Get the registry key's InstallLocation(value) 
        
        Args:
            keyconnection (A winreg handle object) that is connected to the key or subkey
         
        Returns:
            Empty string if no such value, else return the InstallLocation(str)
        '''     
        try:
            return winreg.QueryValueEx(keyconnection, "InstallLocation")[0]
        except:
            return ''  

    def get_key_install_source(self, keyconnection):
        '''Get the registry key's InstallSource(value) 
        
        Args:
            keyconnection (A winreg handle object) that is connected to the key or subkey
         
        Returns:
            Empty string if no such value, else return the InstallSource(str)
        '''     
        try:
            return winreg.QueryValueEx(keyconnection, "InstallSource")[0]
        except:
            return ''      

    def get_key_language(self, keyconnection):
        '''Get the registry key's Language(value) 
        
        Args:
            keyconnection (A winreg handle object) that is connected to the key or subkey
         
        Returns:
            Empty string if no such value, else return the Language(str)
        '''     
        try:
            x = winreg.QueryValueEx(keyconnection, "Language")[0]
            if x == 1033:
                return 'English'
            if x == 1028 or x == 4100 or x == 2052 or x == 5124 or x == 3076:
                return 'Chinese'
            if x == 1041:
                return 'Japanese'
        except:
            return ''
            
    def get_key_display_version(self, keyconnection):
        '''Get the registry key's DisplayVersion(value) 
        
        Args:
            keyconnection (A winreg handle object) that is connected to the key or subkey
         
        Returns:
            Empty string if no such value, else return the DisplayVersion(str)
        '''       
        try:
            return winreg.QueryValueEx(keyconnection, "DisplayVersion")[0]
        except:
            return ''      


class VARIABLE:
    def __init__(self, logger=None, config=None, automationloc="", ZCC_ZDX="", arch= None, combination=None, cloud=None,iseveng =False):
        self.logger = logger if logger != None else self.add_logger()
        self.cloud = cloud
        self.config=config
        self.combination = combination
        MACHINE = platform.uname()
        if MACHINE[0] == "Windows" and MACHINE[2]>='07':
            self.sysmgmt = System()
            self.hostfile = PureWindowsPath("C:/Windows/System32/drivers/etc/hosts")
            self.automationlogslocation = os.path.join(os.path.dirname(os.path.abspath("trigger_automation_zdx.py")),"zdx", "Misc", "Reports", f'{automationloc[:-4]}', f'{ZCC_ZDX}')
            self.automationloc = os.path.join(os.path.dirname(os.path.abspath("trigger_automation_zdx.py")),"zdx", "Misc")
            windowsmgmtintf = wmi.WMI()
            sysinfo = windowsmgmtintf.Win32_ComputerSystem()[0]
            self.OS = MACHINE[0]
            self.arch = MACHINE[4]
            self.sysname = sysinfo.Name
            self.sysmanufacturer = sysinfo.Manufacturer
            self.Modelinfo = sysinfo.Model
            self.nproc = sysinfo.NumberOfProcessors
            self.systype = sysinfo.SystemType
            self.sysfamily = sysinfo.SystemFamily
            #fetch details from config about zcc and zdx version
            self.username = os.getlogin()
            self.LOGPATH = PureWindowsPath("C:/ProgramData/Zscaler")
            self.DBPATH = PureWindowsPath("C:/ProgramData/Zscaler")
            if arch == None: arch = self.systype
            if "64" in arch: self.zccdirectory = PureWindowsPath("C:/Program Files/Zscaler"); self.backupzccdirectory = PureWindowsPath("C:/Program Files (x86)/Zscaler")
            else: self.zccdirectory = PureWindowsPath("C:/Program Files (x86)/Zscaler"); self.backupzccdirectory= PureWindowsPath("C:/Program Files/Zscaler")
            self.user_sid=None
            self.locationApiUrl = "https://gateway.zscalerbeta.net/get_loc"
            try:
                
                if not iseveng and len(ZCC_ZDX) > 0 and (ZCC_ZDX.split("-")[2] == "latest" or float(".".join(ZCC_ZDX.split("-")[2].split(".", 2)[:2])) >= 4.2): 
                    #Finding Userid - Method 1
                    for user in windowsmgmtintf.Win32_UserAccount():
                        if user.name == self.username:
                            self.user_sid = user.SID
                            hashid = hashlib.sha1(self.user_sid.encode('utf-8'))
                            hashid = hashid.hexdigest()
                            self.user_sid = hashid.upper()
                            break
                
                    #Finding Userid - Method 2
                    if self.user_sid == None or len(self.user_sid) == 0:
                        user_info = win32security.LookupAccountName(None, os.getlogin())
                        current_user_sid = win32security.ConvertSidToStringSid(user_info[0])
                        hashid = hashlib.sha1(current_user_sid.encode('utf-8'))
                        hashid = hashid.hexdigest()
                        self.user_sid = hashid.upper()

                    #Finding Userid - Method 3
                    if self.user_sid == None or len(self.user_sid) == 0:
                        a = Handle_Registry()
                        self.user_sid = a.get_sid_from_registry()
                    if self.user_sid == None or len(self.user_sid) == 0:
                        raise Exception("Could not find User SID")
                if not iseveng and len(ZCC_ZDX) > 0 and (ZCC_ZDX.split("-")[2] == "latest" or float(".".join(ZCC_ZDX.split("-")[2].split(".", 2)[:2])) > 4.3):
                    self.LOGPATH = PureWindowsPath(f"C:/ProgramData/Zscaler/log-{self.user_sid}")
                    self.DBPATH = PureWindowsPath(f"C:/ProgramData/Zscaler/log-{self.user_sid}")
            except Exception as E:
                self.logger.debug(E)
                raise Exception(E)
        elif MACHINE[0] == "Darwin" and MACHINE[2].split('.', 1)[0] >= '21':
            self.OS = MACHINE[0]
            self.arch = MACHINE[4]
        else:
            self.logger.info("Current System is not supported")
            raise Exception("Current System is not supported")
        self.get_zcc_version()
        self.get_zdx_version()
        
    def add_logger(self):
        logger = logging.getLogger("ZDX-Endpoint-Automation")
        logger.setLevel(logging.DEBUG)
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))
        logger.addHandler(console_handler)
        return logger
    
    def get_zcc_version(self, 
                        boolean: bool =False # return type shall be string and value will be zcc version if set to True.
                        ):
        if self.OS == "Windows":
            version = subprocess.Popen(["powershell",'Get-ItemProperty HKLM:\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\* | sort-object -property DisplayVersion | Select-Object DisplayName, DisplayVersion, Publisher, InstallDate | Where-Object{$_.DisplayName -eq "Zscaler"}'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            version = version.communicate()
            try:
                version = version[0].decode('utf-8').strip().splitlines()[-1].split()
                if boolean: return version[1]
                self.ZCC_VERSION= version[1]
            except:
                version = subprocess.Popen(["powershell",'Get-ItemProperty HKLM:\\Software\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\* | sort-object -property DisplayVersion | Select-Object DisplayName, DisplayVersion, Publisher, InstallDate | Where-Object{$_.DisplayName -eq "Zscaler"}'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                version = version.communicate()
                try:
                    version = version[0].decode('utf-8').strip().splitlines()[-1].split()
                    if boolean : return version[1]
                    self.ZCC_VERSION= version[1]
                except Exception as E: 
                    self.logger.error(f"Variable Module could not find Zscaler or Application Zscaler is not installed\n{E}")
                    #raise Exception(f"Variable Module could not find Zscaler or Application Zscaler is not installed\n{E}")
            
    def get_zdx_version(self,
                        boolean: bool = False
                        ):
        if self.OS == "Windows":
            Fversion = subprocess.Popen(["powershell",f'(Get-Item "{os.path.join(self.zccdirectory, "ZSAUpm", "ZSAUpm.exe")}").VersionInfo.FileVersion'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            Fversion = Fversion.communicate()
            Pversion = subprocess.Popen(["powershell",f'(Get-Item "{os.path.join(self.zccdirectory, "ZSAUpm", "ZSAUpm.exe")}").VersionInfo.ProductVersion'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            Pversion = Pversion.communicate()
            try:
                Fversion = Fversion[0].decode('utf-8').strip()
                Pversion = Pversion[0].decode('utf-8').strip()
                if len(Fversion) == 0 and len(Pversion) == 0:
                    Fversion = subprocess.Popen(["powershell",f'(Get-Item "{os.path.join(self.backupzccdirectory, "ZSAUpm", "ZSAUpm.exe")}").VersionInfo.FileVersion'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    Fversion = Fversion.communicate()
                    Pversion = subprocess.Popen(["powershell",f'(Get-Item "{os.path.join(self.backupzccdirectory, "ZSAUpm", "ZSAUpm.exe")}").VersionInfo.ProductVersion'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    Pversion = Pversion.communicate()
                    Fversion = Fversion[0].decode('utf-8').strip()
                    Pversion = Pversion[0].decode('utf-8').strip()
                if Version(Fversion) < Version(Pversion):
                    if boolean: return Pversion
                    self.ZDX_VERSION = Pversion
                else:
                    if boolean: return Fversion
                    self.ZDX_VERSION = Fversion
            except Exception as E:
                self.logger.error(f"Variable module could not find ZDX module installed on this system.\n{E}")
                #raise Exception(f"Variable module could not find ZDX module installed on this system.\n{E}")
    def resetLogDbPath(self):
        self.get_zcc_version()
        if self.OS == "Windows":
            if Version(self.ZCC_VERSION) >= Version('4.4'):
                self.LOGPATH = PureWindowsPath(f"C:/ProgramData/Zscaler/log-{self.user_sid}")
                self.DBPATH = PureWindowsPath(f"C:/ProgramData/Zscaler/log-{self.user_sid}")
            else:
                self.LOGPATH = PureWindowsPath("C:/ProgramData/Zscaler")
                self.DBPATH = PureWindowsPath("C:/ProgramData/Zscaler")

    def rmtree(self,path, directory=False,expect_deadlock=False):
        """
            path should be a PurePath object(Path/PureWindows etc...) and not string
            removes all subdirectory except root directory if directory is false
            removes a file if the path is file
            removes directory and subdirectory if directory is True and path is a directory
        """
        if Path(path).exists():
            if Path(path).is_file():
                try:Path(path).unlink(); return [True, None]
                except Exception as E: return [False, E]

            if Path(path).is_dir():
                for item in Path(path).iterdir():
                    stats =self.rmtree(path=item, directory=True, expect_deadlock=expect_deadlock)
                    if stats and not stats[0] and not expect_deadlock:self.logger.error(stats[1]); return stats #false and exception
                    if stats and not stats[0] and expect_deadlock:self.logger.error(stats[1])
                if directory:
                    try:
                        Path(path).rmdir(); return [True, None]
                    except Exception as E: 
                        if not expect_deadlock: self.logger.error(E); return [False, E]
                        if expect_deadlock: self.logger.error(E)
        else: self.logger.error(f"Path- {str(path)} does not exist"); return [False, f"Path- {str(path)} does not exist"]

    def block_auto_upgrade(self, cloudname):
        assert cloudname in ["zscaler", "zscalerone", "zscalertwo", "zscalerthree", "zscloud","zscalerbeta"]
        urlToBlock = "d3l44rcogcb7iv.cloudfront.net"
        try:
            with open(self.hostfile ,'r+') as filereader:
                hostentries = filereader.read()
                if urlToBlock in hostentries:
                    self.logger.warning(f"Auto upgrade of zdx ({cloudname}) is already blocked...")
                else:
                    filereader.write(f"\n0.0.0.0\t{urlToBlock}")
                    self.logger.info(f"Auto upgrade of zdx ({cloudname}) is blocked now...")
        except Exception as E:
            self.logger.error(f"Failed to block auto upgrade. Make sure hosts file in etc dir is not being used by any other process. \n{E}")       
    def unblock_auto_upgrade(self, cloudname):
        assert cloudname in ["zscaler", "zscalerone", "zscalertwo", "zscalerthree", "zscloud","zscalerbeta"]
        urlToUnblock = "d3l44rcogcb7iv.cloudfront.net"
        try:
            with open(self.hostfile, 'r+') as filereader:
                hostentries = filereader.readlines()
                filereader.seek(0)
                for host in hostentries:
                    if urlToUnblock not in host:
                        filereader.write(host)
                filereader.truncate()
            self.logger.info(f"Auto upgrade of zdx ({cloudname}) is unblocked now...")
        except Exception as E:
            self.logger.error(f"Failed to unblock auto upgrade. Make sure hosts file in etc dir is not being used by any other process. \n{E}")
class MobileAdmin:
    def __init__(self, cloudname, configpath,logger=None,variable=None):
        self.ma_obj = admin_ops(cloud=cloudname,Config=configpath,logger=logger,variable=variable)
    
    def enable_zdx_from_MA(self):
        return True if self.ma_obj.set_ZDX_Enablement_Action(action=True) else False
    
    def disable_zdx_from_MA(self):
        return True if self.ma_obj.set_ZDX_Enablement_Action(action=False) else False
        
    def Tunnel_1_Apply(self, forwarding_profile_name=None, EnableForParticularUser=False, Policy_Name=None,Operating_System=None):
        try:
            self.ma_obj.Delete_Device_Policy(Policy_Name=Policy_Name, Operating_System=Operating_System)
        except:
            pass
        try:
            self.ma_obj.Delete_Forwarding_Profile(forwarding_profile_name=forwarding_profile_name)      
        except:
            pass
        try:
            self.ma_obj.Create_Forwarding_Profile(forwarding_profile_name=forwarding_profile_name)
        except:
            pass
        try:
            self.ma_obj.Create_Device_Policy(Policy_Name=Policy_Name, EnableForParticularUser=EnableForParticularUser, forwarding_profile_name=forwarding_profile_name, Operating_System=Operating_System)
        except:
            pass
        try:
            self.ma_obj.Edit_Forwarding_Profile(forwarding_profile_name=forwarding_profile_name, tunnel_mode='tunnel_1')
        except:
            pass
        

    def Tunnel_2_Apply(self, forwarding_profile_name=None, EnableForParticularUser=False, Policy_Name=None, Operating_System=None):
        try:
            self.ma_obj.Delete_Device_Policy(Policy_Name=Policy_Name, EnableForParticularUser=EnableForParticularUser, Operating_System=Operating_System)
        except:
            pass
        try:
            self.ma_obj.Delete_Forwarding_Profile(forwarding_profile_name=forwarding_profile_name)      
        except:
            pass
        try:
            self.ma_obj.Create_Forwarding_Profile(forwarding_profile_name=forwarding_profile_name)
        except:
            pass
        try:
            self.ma_obj.Create_Device_Policy(Policy_Name=Policy_Name, EnableForParticularUser=EnableForParticularUser, forwarding_profile_name=forwarding_profile_name, Operating_System=Operating_System)
        except:
            pass
        try:
            self.ma_obj.Edit_Forwarding_Profile(forwarding_profile_name=forwarding_profile_name, tunnel_mode='tunnel_2_dtls')
        except:
            pass

    def get_new_applicationList(self):
        try:
            return self.ma_obj.get_new_application_list()
        
        except Exception as e:
            raise Exception(e)
    
    def get_applicationList(self):
        try:
            return self.ma_obj.get_application_list()
        except Exception as e:
            raise Exception(e)
        
    def get_latest_zcc_version_from_la(self):
        try:
            return self.ma_obj.get_new_application_list()['applications'][0]['applicationVersion']
        
        except Exception as e:
            raise Exception(e)

    def enable_custom_built(self, applicationVersion):
        """
        This function enable custom ZCC built from Mobile Admin.

        :param self: The object itself, applicationVersion: ZCC version

        :return: None
        """
        try:
            payload = self.ma_obj.get_new_application_list()['applications']
            new_applications = []
            for app in payload:
                new_app = {
                    "clientApplicationId": app["clientApplicationId"],
                    "buildEnabled": 1 if app["applicationVersion2"] == applicationVersion else app["buildEnabled"],
                    "applicationVersion2": app["applicationVersion2"],
                    "clientPlatform": app["clientPlatform"]
                }
                new_applications.append(new_app)

            new_payload = {
                "applications": new_applications
            }

            self.ma_obj.update_new_application_list(new_payload)

        except Exception as e:
            raise Exception(e)
        
    def disable_custom_built(self, applicationVersion):
        """
        This function disable custom ZCC built from Mobile Admin.

        :param self: The object itself, applicationVersion: ZCC version

        :return: None
        """
        try:
            payload = self.ma_obj.get_new_application_list()['applications']
            new_applications = []
            for app in payload:
                new_app = {
                    "clientApplicationId": app["clientApplicationId"],
                    "buildEnabled": 0 if app["applicationVersion2"] == applicationVersion else app["buildEnabled"],
                    "applicationVersion2": app["applicationVersion2"],
                    "clientPlatform": app["clientPlatform"]
                }
                new_applications.append(new_app)

            new_payload = {
                "applications": new_applications
            }

            self.ma_obj.update_new_application_list(new_payload)

        except Exception as e:
            raise Exception(e)
    
    def update_settings(self, version_to_install, use64BitInstallerForWindows = False):
        """
        This function update application settings for Windows from Mobile Admin.

        :param self: The object itself, version_to_install: ZCC version(can be either disable, latest, specific ZCC version), 
        use64BitInstallerForWindows: boolean (False(default): disable use64BitInstallerForWindows, True: enable use64BitInstallerForWindows for specific ZCC version)

        :return: None
        """
        try:
            payload = self.ma_obj.get_application_list()
            if version_to_install != "disable" and version_to_install != "latest":
                isBuiltEnable = False
                for data in payload["applications"]:
                    if version_to_install == data["applicationVersion"]:
                        isBuiltEnable = True

                if not isBuiltEnable:
                    self.enable_custom_built(version_to_install)
                    payload = self.ma_obj.get_application_list()

            new_payload = {
                    "companyAutoUpdateEnabled": payload['companyAutoUpdateEnabled'],
                    "actionType": -1,
                    "groupBasedApplicationList":  [
                        {   
                            "rowId": payload['groupBasedApplicationList'][0]["rowId"],
                            "groupId": None,
                            "groupName": "ALL",
                            "windowsEnabledImage": {},
                            "macEnabledImage": payload['groupBasedApplicationList'][0]['macEnabledImage'],
                            "linuxEnabledImage": payload['groupBasedApplicationList'][0]['linuxEnabledImage'],
                            "applyFromTimestamp": payload['groupBasedApplicationList'][0]["applyFromTimestamp"],
                            "enableSlowRollout": payload['groupBasedApplicationList'][0]["enableSlowRollout"]
                        }
                    ]
                }

            windowsEnabledImage = {}

            if(version_to_install == "disable"):
                windowsEnabledImage = {
                    "groupAutoUpdateEnabled": 2,
                    "applicationVersionId": 0,
                    "applicationVersion": "",
                    "use64BitInstallerForWindows": "0"
                }

            elif (version_to_install == "latest"):
                windowsEnabledImage = {
                    "groupAutoUpdateEnabled": 1,
                    "applicationVersionId": 0,
                    "applicationVersion": "",
                    "use64BitInstallerForWindows": "0"
                }
            else:
                for application in payload["applications"]:
                    if version_to_install == application["applicationVersion"]:
                        windowsEnabledImage = {
                            "groupAutoUpdateEnabled": 0,
                            "applicationVersionId": int(application["clientApplicationId"]),
                            "applicationVersion": version_to_install,
                            "use64BitInstallerForWindows": "1" if use64BitInstallerForWindows == True else "0"
                        }
            new_payload["groupBasedApplicationList"][0]["windowsEnabledImage"] = windowsEnabledImage

            self.ma_obj.update_application_list(new_payload)

        except Exception as e:
            raise Exception(e)
        
    def get_current_device_zdx_health_from_ma(self):
        """
        This function get current device ZDX health from Mobile Admin.

        :param self: The object itself.

        :return: current device ZDX health
        """
        try:
            return self.ma_obj.Fetch_Enrolled_Devices_Details(fetch_this_device_details=True)['zdxHealth']
        
        except Exception as e:
            raise Exception(e)

    def Twlp_Apply(self):
            try:
                self.ma_obj.Delete_Device_Policy()
            except:
                pass
            try:
                self.ma_obj.Delete_Forwarding_Profile()      
            except:
                pass
            try:
                self.ma_obj.Create_Forwarding_Profile()
            except:
                pass
            try:
                self.ma_obj.Create_Device_Policy()
            except:
                pass
            try:
                self.ma_obj.Edit_Forwarding_Profile(tunnel_mode='twlp')
            except:
                pass

class DecryptTool:
    def __init__(self,logger, variable):
        self.logger = logger
        self.variable = variable
        self.url = "https://zapplogs.corp.zscaler.com"

    def decrypt_db(self, upload_path:Path, download_path:Path):
        import requests
        if not os.path.exists(upload_path):
            self.logger.critical(f"Upload path - {upload_path} does not exist. Please specify valid file path to be uploaded for decryption")
            return [False, f"Upload path - {upload_path} does not exist. Please specify valid file path to be uploaded for decryption", None]
        if not os.path.exists(download_path):
            self.logger.critical(f"Download path - {upload_path} does not exist. Please specify valid file path to be store decrypted DBs")
            return [False, f"Upload path - {upload_path} does not exist. Please specify valid file path to be store decrypted DBs", None]
        self.logger.info(f"Decrypting - {upload_path}")
        url = "/".join([self.url, "dbdecrypt"])
        files = {'fileUpload': ('encdb.zip', open(upload_path,'rb'))}
        filename= f"DecryptedDB-{str(datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S'))}.zip"
        download_path = Path(os.path.join(download_path, filename))
        response = requests.post(url=url, files=files)
        if response.status_code != 200:
            # self.logger.error(f'failed - {response.status_code} - {response.text}\n url: {url}\n')
            return [False, f'failed - {response.status_code} - {response.text}\n url: {url}\n', response]
        try:
            with open(download_path, "wb") as writer:
                writer.write(response.content)
            self.logger.info(f"Successfully decrypted {upload_path}")
            return [True, f"Successfully decrypted {upload_path}", download_path]
        except Exception as E:
            return [False, f"failed to create decrypted file using response\n{E}", response]
if __name__=="__main__":
    a = Handle_Registry().user_sid
    print(a)
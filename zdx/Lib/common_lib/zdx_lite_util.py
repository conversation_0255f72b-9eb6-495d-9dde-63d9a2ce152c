__author__ = "<PERSON><PERSON><PERSON> (<EMAIL>)"
import pytest
from Lib.common_lib.UI_ZCC import ZCC
from Lib.Windows import System
import time
import allure
import json
from datetime import datetime
import re
import sys
import os
sys.path.append("....")
from common_lib.mobileadmin.serviceentitlement import ServiceEntitlement
from common_lib.common import ops_defines , ops_common , logger
from common_lib.mobileadmin.devicegroups import DeviceGroups
from common_lib.mobileadmin.clientconnectorappstore import ClientConnectorAppStore
from common_lib.adminzia.admingroupuser import AdminGroupUser
from OS_Windows.library.ui_zcc import Zcc
from common_lib.mobileadmin.appprofile import *
from common_lib.mobileadmin.forwardingprofile import *
from common_lib.mobileadmin.clientconnectorsupport import *
#config_path = os.path.join(os.getcwd(),"config", os.environ.get("CONFIG_FILE"))
# with open(os.path.join(os.getcwd(), config_path)) as json_file:
#     variables = json.load(json_file)

class zdx_lite_helper:
    def __init__(self, Loggers,Variables):
        self.LOG_HANDLER  = pytest.helpers.LOG_HANDLER()
        self.DB_HANDLER  = pytest.helpers.DB_HANDLER()
        self.Variable  = Variables
        self.Loggers = Loggers
        self.zcc = Zcc()
        self.zdx_lite_config_data = self.Variable.config.get('zdx')['zdx_lite']
        self.zdx_lite_config_json = 'config_zdx_lite.json'
        self.forwarding_profile_name = 'zdx_lite_fp'
        self.app_profile_name = 'zdx_lite_ap'
        # self.config_path = os.path.join(os.getcwd(),"config",f"{self.zdx_lite_config_json}")
        # with open(self.config_path, 'w') as json_file:
        #     json.dump(self.zdx_lite_config_data, json_file, indent=4)
        self.app_profile = AppProfile(cloud=self.zdx_lite_config_data['CLOUD'],config=self.zdx_lite_config_data)
        self.forwarding_profile = ForwardingProfile(cloud=self.zdx_lite_config_data['CLOUD'],config=self.zdx_lite_config_data)
        self.clientconnector_support = ClientConnectorSupport(cloud=self.zdx_lite_config_data['CLOUD'],config=self.zdx_lite_config_json)
    
    # [QA-268239], [QA-268238] , [QA-268280] , [QA-268287]
    def is_zdx_lite_enabled(self):
        """ This function determines if the ZDX Lite feature is enabled or not using the ZSATraymanager logs.

        Returns:
            List : [enabled(int),lite_log(string)]
        """
        # Variable to check whether ZDX Lite is enabled or not.
        enabled = 0
        lite_log = ""

        # Fetch all data of the ZSATraymanager log file
        latest_traymanager_log_file_data = self.LOG_HANDLER.extract_all_logs_data(self.Variable,'traymanager')
        
        # Look for this pattern in the ZSATraymanager file
        log_to_check_for_zdx_lite_enabled = "upmEntitlementUpdate: Policy Update upmEnabledForUser: 0/1, zdxLiteEnabled: 1"
        pattern = r'upmEntitlementUpdate:\s*Policy\s*Update\s*upmEnabledForUser:\s*\d+,\s*zdxLiteEnabled:\s*\d+'
        
        fetched_logs = self.LOG_HANDLER.find_pattern_in_string(pattern,latest_traymanager_log_file_data)

        if fetched_logs == None or len(fetched_logs) == 0:
            self.Loggers.warning(f"'{log_to_check_for_zdx_lite_enabled}' log not found in the Traymanager log.")
            return [enabled,lite_log]

        log_to_check = ['zdxLiteEnabled: 0','zdxLiteEnabled: 1','upmEnabledForUser: 0','upmEnabledForUser: 1']
        
        if fetched_logs:
            if log_to_check[2] in fetched_logs[-1] and log_to_check[1] in fetched_logs[-1]:
                self.Loggers.info(f"ZDX lite is enabled. Log -> {fetched_logs[-1]}")
                enabled = 1
                
            elif log_to_check[3] in fetched_logs[-1] and log_to_check[1] in fetched_logs[-1]:
                self.Loggers.info(f"ZDX Lite is not enabled, as Standard ZDX & ZDX Lite are both enabled, but since Standard ZDX has more precedence, it took over. Log -> {fetched_logs[-1]}")

            elif log_to_check[0] in fetched_logs[-1]:
                self.Loggers.info(f"ZDX Lite is not enabled. Log -> {fetched_logs[-1]}")
            lite_log = fetched_logs[-1]
            
        return [enabled,lite_log]
    
    def toggle_zdx(self,action):
        service_et_obj = ServiceEntitlement(cloud=self.zdx_lite_config_data['CLOUD'],config=self.zdx_lite_config_data)
        res = service_et_obj.toggle_zdx(action)
        return res
    
    def validate_clear_logs(self):
        files_before_clear_logs = self.LOG_HANDLER.get_logfiles(self.Variable.LOGPATH)
        self.zcc.clear_logs()
        files_after_clear_logs = self.LOG_HANDLER.get_logfiles(self.Variable.LOGPATH)
        status = True
        Issues = []
        for item in ['web','traceroute','deviceStats','deviceInventory','deviceEvents','systemEvents','ssit','activity']:
            if (len(files_before_clear_logs[item]) >=2 and len(files_after_clear_logs[item])) == 1 or len(files_before_clear_logs[item]) <= 1:
                print(f"SUCCESS :: Total '{item}' files before clear logs : {len(files_before_clear_logs[item])} ({files_before_clear_logs[item]}) , Total '{item}' files after clear logs : {len(files_after_clear_logs[item])} ({files_after_clear_logs[item]}) ")
            else:
                status = False
                print(f"FAILURE :: Total '{item}' files before clear logs : {len(files_before_clear_logs[item])} ({files_before_clear_logs[item]}) , Total '{item}' files after clear logs : {len(files_after_clear_logs[item])} ({files_after_clear_logs[item]}) ")
                Issues.append(f"FAILURE :: Total '{item}' files before clear logs : {len(files_before_clear_logs[item])} ({files_before_clear_logs[item]}) , Total '{item}' files after clear logs : {len(files_after_clear_logs[item])} ({files_after_clear_logs[item]}) ")
            print("---------------------------------------------------------------")

        if len(Issues):
            status = False
        else:
            status = True
            
        return (status,"SUCCESS :: Clear Logs Validated",Issues)
    
    def validate_zdx_restart_service(self):
        """
        This function validates whether the ZDX service was successfully restarted by checking 
        specific log messages that indicate the service restart process. 

        It triggers the ZDX restart service, monitors logs for specific entries, and returns the status of the restart process.

        The function performs the following steps:
        1. Triggers the ZDX restart using `self.zcc.restart_zdx_service()`.
        2. Searches through log lines for specific messages related to the ZDX service restart.
        3. If the relevant logs are found, the function marks the restart as successful.
        4. If the relevant logs are not found or the timeout expires, it returns a failure message.

        Returns:
            tuple: A tuple containing the following elements:
                - bool: `True` if the ZDX service restart was successful, `False` if it failed.
                - str: A message indicating success or failure.
                - list: A list of logs that are related to the ZDX service restart.

        """
        status = False
        logs_to_check = [
            'INF ZSATrayManager RPC command: RESTART_ZSAUPM_REQUEST',
            'Restarting ZSAUPM',
            'Done with ZSATrayManager RPC command: RESTART_ZSAUPM_REQUEST',
            'zcc_user_restart_zdx'
        ]
        lines ,data, stime, flag = [] , [] ,0 , 1

        try:
            lines = self.LOG_HANDLER.get_realtime_log_data(self.Loggers, self.Variable, plugin='traymanager')

            for item in lines:
                if flag == 1:
                    self.zcc.restart_zdx_service()
                    flag = 0
                    stime = time.time()

                if any(log.lower() in item.lower() for log in logs_to_check[:2]):
                    self.Loggers.info(item)
                    data.append(item)

                if any(log.lower() in item.lower() for log in logs_to_check[2:]):
                    status = True
                    self.Loggers.info(item)
                    data.append(item)

                if int(time.time()) - stime > 60:
                    break

        except Exception as e:
            error_msg = f"An error occurred during ZDX restart validation: {str(e)}"
            self.Loggers.error(error_msg)
            return (False, error_msg, [])

        if status:
            return (status, "SUCCESS :: 'Restart ZDX' restarted ZDX service", data)
        else:
            return (status, "FAILURE :: 'Restart ZDX' did not restart ZDX service", data)

        
    def validate_zdx_notification(self):
        result = ""
        notification = ""
        try:
            self.zcc.clear_all_notification()
            is_zdx_connected = self.zcc.check_service_connected(service='ZDX')
            if is_zdx_connected[0] == False:
                self.zcc.toggle_service(service='ZDX',action=True)
                result = self.zcc.find_notification(notification='Digital Experience is Enabled.')
                notification = "Digital Experience is Enabled."
            else:
                self.zcc.toggle_service(service='ZDX',action=False)
                result = self.zcc.find_notification(notification='Digital Experience is Disabled.')
                notification = "Digital Experience is Disabled."

        finally:
            is_zdx_connected = self.zcc.check_service_connected(service='ZDX')
            if is_zdx_connected[0] == False:
                self.zcc.toggle_service(service='ZDX',action=True)
            if result[0] == True:
                return result[0],f"SUCCESS :: ZDX Notification Found -> '{notification}'",""
            else:
                return result[0],f"FAILURE :: ZDX Notification not found -> '{notification}'",""
            
    def validate_restart_service_for_zdx(self)-> tuple[bool, str,list[str]]:
        """ This function checks if the ZDX service is restarted when the Restart Service is clicked."""

        logs_to_check = ['ZSAUpm RPC server stopped','Uploader Stopped','Stop plugins called']
        lines = self.LOG_HANDLER.get_realtime_log_data(self.Loggers,self.Variable,plugin='upm')
        stime= 0
        data = []
        flag = 1

        try:
            for item in lines:
                if flag == 1:
                    self.zcc.restart_service()
                    flag = 0
                    stime = time.time()
                    
                for log in logs_to_check:
                    if log.lower() in item.lower():
                        print(item)
                        data.append(item)

                if (int(time.time())- stime) > 60 or len(data) >= len(logs_to_check):
                    break

        except Exception as e:
            print(e)
            return (False,f"Exception : {e}",data)
        
        if len(data):
            return (True,"'Restart Service' restarted ZDX service",data)
        else:
            return (False,"'Restart Service' did not restart ZDX service",data)

    def fetch_end_to_end_diagnostics_from_tunnel_log(self)->tuple[bool,list,str]:

        """
        Extracts end-to-end diagnostics from tunnel log data.

        Retrieves and parses tunnel log entries to extract diagnostics information such as 'trusted', 'trusted-vpn',
        'off-trusted', and 'trusted-split-vpn'. Returns a tuple with a success flag, a list of diagnostic data, and a status message.

        Returns:
            tuple: (bool, list of dicts, str)
                - bool: True if diagnostics data is found, False otherwise.
                - list of dicts: Each dict contains 'timestamp', 'trusted', 'trusted-vpn', 'off-trusted', 'trusted-split-vpn'.
                - str: "SUCCESS" or "FAILURE" indicating the result.
        """
        tunnel_log_data = self.LOG_HANDLER.extract_all_logs_data(self.Variable, 'tunnel')
        log_text = tunnel_log_data
        log_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{6})\(\+\d{4}\)\[.*?\]\s+INF\s+ZDX Lite - End to End Diagnostics:\s*'
                                r'Trusted:\s*(\d+)\s*'
                                r'Trusted-VPN:\s*(\d+)\s*'
                                r'OFF-Trusted:\s*(\d+)\s*'
                                r'Trusted-Split-VPN:\s*(\d+)')
        
        diagnostics_list = []
        
        if not log_text:
            self.Loggers.error("Error: Log text is empty.")
            return False, diagnostics_list, "FAILURE"
        
        pos = 0
        while True:
            log_match = log_pattern.search(log_text, pos)
            if not log_match:
                break
            
            try:
                timestamp = log_match.group(1)
                diagnostics_info = {
                    'timestamp': timestamp,
                    'trusted': int(log_match.group(2)),
                    'trusted-vpn': int(log_match.group(3)),
                    'off-trusted': int(log_match.group(4)),
                    'trusted-split-vpn': int(log_match.group(5))
                }
                diagnostics_list.append(diagnostics_info)
            
            except (ValueError, IndexError) as e:
                self.Loggers.error(f"Error parsing diagnostics data: {e}")
            
            pos = log_match.end()
        
        if diagnostics_list:
            return True, diagnostics_list, "SUCCESS"
        else:
            return False, diagnostics_list, "FAILURE"
        
    def fetch_network_type_from_tunnel_log(self)->tuple[bool,dict,str]:

        """
        Extracts network type and proxy state information from tunnel log data.

        Parses tunnel logs to identify and extract network type and proxy state details from log entries containing 
        specific keywords. Returns a list of dictionaries with timestamp and extracted network information, along with 
        a success flag and status message.

        Returns:
            tuple: (bool, list of dicts, str)
                - bool: True if network information is found, False otherwise.
                - dict: Each dict contains 'timestamp', 'getcurrentnetworktype', and 'getsmeproxystate'.
                - str: "SUCCESS" or "FAILURE" indicating the result.
        """

        # Extract log data
        tunnel_log_data = self.LOG_HANDLER.extract_all_logs_data(self.Variable, 'tunnel')
        #tunnel_log_data = self.LOG_HANDLER.get_latest_file_data_with_plugin(self.Loggers,self.Variable,'tunnel')
        
        # Define regex pattern to match the log entries
        log_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{6})\(\+\d{4}\)\[.*?\]\s+DBG\s+([^\n]+)')
        
        # Dictionary to store results
        final_list = {}
        
        for item in tunnel_log_data.splitlines():
            # Check if the line contains relevant status information
            if 'zapp status' in item.lower() or 'getcurrentnetworktype' in item.lower():
                result = {
                    'timestamp': None,
                    'getcurrentnetworktype': None,
                    'getsmeproxystate': None
                }
                # Search for timestamp and message
                match = log_pattern.search(item)
                if match:
                    # Extract timestamp and log message
                    timestamp = match.group(1)
                    log_message = match.group(2)
                    
                    # Extract specific details from log message
                    network_type_match = re.search(r'getCurrentNetworkType:([^\s]+)', log_message)
                    if network_type_match:
                        result['getcurrentnetworktype'] = network_type_match.group(1).lower()
                    
                    sme_proxy_state_match = re.search(r'getSmeProxyState:([^\s]+)', log_message)
                    if sme_proxy_state_match:
                        result['getsmeproxystate'] = sme_proxy_state_match.group(1).lower()
                    
                    result['timestamp'] = timestamp
                    
                    # Update the dictionary with the latest result for this timestamp
                    final_list[timestamp] = result
                    # self.Loggers.info(item)
                    # self.Loggers.info(result)
                    # self.Loggers.info("-----------------------------------------")
                else:
                    self.Loggers.error(f"Warning: Log line does not match the expected pattern. Line: {item}")
        
        if len(final_list):
            return True, final_list, "SUCCESS"
        else:
            return False, final_list, "FAILURE"

    def toggle_zdx_lite_metrics(self,local_metrics=False,on_trusted_network_end_to_end_diagnostics=False,off_trusted_network_end_to_end_diagnostics=False,vpn_trusted_network_end_to_end_diagnostics=False,split_vpn_trusted_end_to_end_diagnostics=False):
        """
        Toggles ZDX Lite metrics by performing a series of API operations.

        This method:
        1. Creates a forwarding profile using `self.forwarding_profile.create_forwarding_profile`.
        2. Creates an app profile using `self.app_profile.create_app_profile`.
        3. Edits the app profile with the provided metrics settings using `self.app_profile.edit_app_profile`.

        Args:
            local_metrics (bol, optional): Metrics for local. Defaults to False.
            on_trusted_network_end_to_end_diagnostics (bool, optional): Metrics for trusted connections. Defaults to False.
            off_trusted_network_end_to_end_diagnostics (bool, optional): Metrics for off-trusted connections. Defaults to False.
            vpn_trusted_network_end_to_end_diagnostics (bool, optional): Metrics for VPN-trusted connections. Defaults to False.
            split_vpn_trusted_end_to_end_diagnostics (bool, optional): Metrics for split VPN-trusted connections. Defaults to False.

        Returns:
            dict: Response from the `edit_app_profile` API call if successful, otherwise None.

        """

        try:
            res1 = self.forwarding_profile.create_forwarding_profile(forwarding_profile_name=self.forwarding_profile_name)
        except Exception as e:
            self.Loggers.error(f"Error creating forwarding profile: {e}. Response -> {res1}")

        try:
            res2 = self.app_profile.create_app_profile(policy_name=self.app_profile_name,forwarding_profile_name=self.forwarding_profile_name)
        except Exception as e:
            self.Loggers.error(f"Error creating app profile: {e}. Response -> {res2}")

        try:
            res3 = self.app_profile.edit_app_profile(policy_name=self.app_profile_name,forwarding_profile_name=self.forwarding_profile_name,local_metrics=local_metrics,on_trusted_network_end_to_end_diagnostics=on_trusted_network_end_to_end_diagnostics,off_trusted_network_end_to_end_diagnostics=off_trusted_network_end_to_end_diagnostics,vpn_trusted_network_end_to_end_diagnostics=vpn_trusted_network_end_to_end_diagnostics,split_vpn_trusted_end_to_end_diagnostics=split_vpn_trusted_end_to_end_diagnostics)
            if res3[0]:
                return res3
        except Exception as e:
            self.Loggers.error(f"Error editing app profile: {e}. Response -> {res3}")

    def toggle_nw_type(self,nw_type:str)-> tuple[bool,str,dict]:
        """
        Toggles the network type in the forwarding profile based on the specified network type.

        - If `nw_type` is `"trusted"`, it sets the forwarding profile to use a trusted network with specific DNS addresses (Google's DNS).
        - If `nw_type` is `"off-trusted"`, it removes the trusted network settings from the forwarding profile.
        - If `nw_type` is `"split-vpn"`, it configures the profile to enable split VPN with specified DNS servers.
        - If `nw_type` is `"vpn-trusted"`, it sets the profile to use a trusted network with multiple DNS servers.

        Args:
            nw_type (str): The network type to toggle. Must be one of:
                - `"trusted"`: Sets the profile to use trusted network settings with Google's DNS.
                - `"off-trusted"`: Removes trusted network settings from the profile.
                - `"split-vpn"`: Enables split VPN with specific DNS servers (Cloudflare's).
                - `"vpn-trusted"`: Configures the profile to use trusted network settings with multiple DNS addresses.

        Returns:
            tuple[bool, str, dict]: A tuple containing:
                - A boolean indicating whether the operation was successful (`True`) or not (`False`).
                - A string with a status message or description of the result.
                - A dictionary containing additional details about the forwarding profile after the update.
        """

        if nw_type == "trusted":
            res = self.forwarding_profile.edit_forwarding_profile(forwarding_profile_name=self.forwarding_profile_name,trusted_condition_match="any",hostname_ip="dns.google,*******",trusted_network="inforwardprofile")
            return res
        elif nw_type == "off-trusted":
            res = self.forwarding_profile.edit_forwarding_profile(forwarding_profile_name=self.forwarding_profile_name)
            return res
        elif nw_type == "split-vpn":
            res = self.forwarding_profile.edit_forwarding_profile(forwarding_profile_name=self.forwarding_profile_name,trusted_condition_match="any",dns_servers="*******",trusted_network="inforwardprofile",enable_split_vpn=True)
            return res
        elif nw_type == "vpn-trusted":
            res = self.forwarding_profile.edit_forwarding_profile(forwarding_profile_name=self.forwarding_profile_name,trusted_condition_match="any",dns_servers="*******,*******",trusted_network="inforwardprofile")
            return res
        
    def toggle_user_privacy_info(
        self, 
        collect_user_info=True, 
        collection_machine_info=True, 
        collect_location_info=True
    ):
        """
        Toggles user privacy information settings for the client connector.

        This method updates the privacy settings for user, machine, and location information 
        based on the provided boolean flags. 

        Parameters:
        ----------
        collect_user_info : bool, optional
            Indicates whether to collect user information. Defaults to True.
            
        collection_machine_info : bool, optional
            Indicates whether to collect machine information. Defaults to True.
            
        collect_location_info : bool, optional
            Indicates whether to collect location information. Defaults to True.

        Returns:
        -------
        tuple
            Returns a tuple (success: bool, message: str) where `success` indicates 
            whether the privacy settings were updated successfully and `message` provides 
            additional information or error details.
        """
        
        if not all(isinstance(arg, bool) for arg in [collect_user_info, collection_machine_info, collect_location_info]):
            return False, "Invalid parameter types: all parameters must be boolean."

        try:
            res = self.clientconnector_support.set_privacy_info(
                collect_machine_hostname_information=collection_machine_info,
                collect_device_owner_information=collect_user_info,
                collect_zdx_location=collect_location_info
            )
            return (True, "Privacy settings updated successfully.") if res else (False, "Failed to update privacy settings.")
        except Exception as e:
            return False, f"Error occurred: {str(e)}"
  
        
    def get_user_privacy_info(self) -> (bool, dict):
        """
        Extract user, machine, and location information from traymanager log data.

        This method fetches log data and processes each line to identify and
        extract relevant user privacy information based on predefined log
        keywords. It organizes the results into a dictionary with timestamps
        as keys.

        Returns:
            tuple: A tuple where the first element is a boolean indicating
                   success (True if data was extracted) and the second element
                   is a dictionary containing user_info, machine_info,
                   and location_info.
        """
        try:
            traymanager_log_data = self.LOG_HANDLER.extract_all_logs_data(self.Variable, 'upm')
            
            if not traymanager_log_data:
                self.Loggers.error("No log data extracted.")
                return False, {}

            key_mapping = {
                'INF Collect user info': 'user_info',
                'INF Collect machine info': 'machine_info',
                'INF Collect location info': 'location_info',
                'INF ZDX Lite mode:' : 'lite_mode'
            }

            result = {}

            timestamp_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{6})'
            for line in traymanager_log_data.strip().split('\n'):
                line = line.strip()
                if not line:
                    continue
                
                found_key = None
                for keyword, mapped_key in key_mapping.items():
                    if keyword.lower() in line.lower():
                        found_key = mapped_key
                        break

                if found_key:
                    timestamp_match = re.search(timestamp_pattern, line)
                    if timestamp_match:
                        timestamp = timestamp_match.group(1)
                        key_part = line.split()[-1]

                        if timestamp not in result:
                            result[timestamp] = {}
                        result[timestamp][found_key] = key_part
                    else:
                        self.Loggers.error(f"No valid timestamp found in line: {line}")

            self.Loggers.info(f"Extracted user privacy info: {result}")
            return True, result

        except Exception as e:
            self.Loggers.error(f"An error occurred while extracting user privacy info: {e}")
            return False, {}
        
    def enable_zdx_lite(self,restart_zcc_service=True):
        """
        This method checks the current status of ZDX Lite and toggles it if necessary. If ZDX Lite is not already 
        enabled, it attempts to enable it, updates policy, and restart zcc service. The process 
        includes multiple checks and error handling to ensure successful enablement of ZDX Lite.

        Args:
            restart_zcc_service (bool): A flag to determine whether the ZCC service should be restarted 
                                        after enabling ZDX Lite. Defaults to True.

        Returns:
            tuple: A tuple containing a boolean indicating success (True/False) and a message providing details 
                on the operation result.
                - True, "ZDX Lite has been enabled successfully." on success.
                - False, Error message on failure.
        """
        zdx_lite_status = ''
        try:
            zdx_lite_status =  self.is_zdx_lite_enabled()[0]
        except Exception as e:
            self.Loggers.error(f"Error checking ZDX Lite status: {e}")
            return False, f"Error checking ZDX Lite status: {e}"

        if zdx_lite_status == 1:
            if restart_zcc_service:
                try:
                    res = self.zcc.restart_service(sleep_time=45)
                    if not res[0]:
                        return False,f"{res[1]}"
                except Exception as e:
                    self.Loggers.error(f"Error restarting service: {e}")
                    return False, f"Error restarting service: {e}"
            
            return True , "ZDX Lite is already enabled."
        
        elif zdx_lite_status == 0:
            try:
                res = self.toggle_zdx(action=False)
                if not res[0]:
                    return False, f"{res[1]}"
            except Exception as e:
                self.Loggers.error(f"Error toggling ZDX Lite: {e}")
                return False, f"Error toggling ZDX Lite: {e}"

            try:
                res = self.zcc.update_policy()
                if not res[0]:
                    return False, f"{res[1]}"
            except Exception as e:
                self.Loggers.error(f"Error updating policy: {e}")
                return False, f"Error updating policy: {e}"

            try:
                check_lite = self.is_zdx_lite_enabled()
                if check_lite[0] == 0:
                    return False, f"Failed to enable ZDX Lite. Res: {check_lite[1]}"
            except Exception as e:
                self.Loggers.error(f"Error verifying ZDX Lite status: {e}")
                return False, f"Error verifying ZDX Lite status: {e}"

            if restart_zcc_service:
                try:
                    res = self.zcc.restart_service(sleep_time=45)
                    if not res[0]:
                        return False,f"{res[1]}"
                except Exception as e:
                    self.Loggers.error(f"Error restarting service: {e}")
                    return False, f"Error restarting service: {e}"

        return True, "ZDX Lite has been enabled successfully."
    
    def enable_standard_zdx(self,restart_zdx_service = True):
        """
        This method checks the current status of ZDX Lite and toggles it to Standard ZDX if necessary. If ZDX Lite 
        is currently enabled, it disables it and updates the policies. Then, it checks if Standard ZDX has been successfully 
        enabled and restarts the ZDX service if required. 

        Args:
            restart_zdx_service (bool): A flag to determine whether the ZDX service should be restarted 
                                        after enabling Standard ZDX. Defaults to True.

        Returns:
            tuple: A tuple containing a boolean indicating success (True/False) and a message providing details 
                on the operation result.
                - True, "Standard ZDX has been enabled successfully." on success.
                - False, Error message on failure.
        """
        zdx_lite_status = ''
        try:
            zdx_lite_status =  self.is_zdx_lite_enabled()[0]
        except Exception as e:
            self.Loggers.error(f"Error checking ZDX Lite status: {e}")
            return False, f"Error checking ZDX Lite status: {e}"
        
        if zdx_lite_status == 0:
            if restart_zdx_service:
                try:
                    res = self.zcc.restart_zdx_service(sleep_time=45)
                    if not res[0]:
                        return False,f"{res[1]}"
                except Exception as e:
                    self.Loggers.error(f"Error restarting ZDX service: {e}")
                    return False, f"Error restarting ZDX service: {e}"

            return True , "Standard ZDX is already enabled."  
              
        elif zdx_lite_status == 1:
            try:
                res = self.toggle_zdx(action=True)
                if not res[0]:
                    return False,f"{res[1]}"
            except Exception as e:
                self.Loggers.error(f"Error toggling ZDX: {e}")
                return False, f"Error toggling ZDX: {e}"

            try:
                res = self.zcc.update_policy()
                if not res[0]:
                    return False,f"{res[1]}"
            except Exception as e:
                self.Loggers.error(f"Error updating policy: {e}")
                return False, f"Error updating policy: {e}"

        try:
            check_lite = self.is_zdx_lite_enabled()
            if check_lite[0] == 1:
                return (False,f"Failed to enable Regular ZDX. Res: {check_lite[1]}")
        except Exception as e:
            self.Loggers.error(f"Error verifying ZDX status: {e}")
            return False, f"Error verifying ZDX status: {e}"
        
        if restart_zdx_service:
            try:
                res = self.zcc.restart_zdx_service(sleep_time=45)
                if not res[0]:
                    return False,f"{res[1]}"
            except Exception as e:
                self.Loggers.error(f"Error restarting ZDX service: {e}")
                return False, f"Error restarting ZDX service: {e}"
        
        return True, "Standard ZDX has been enabled successfully."

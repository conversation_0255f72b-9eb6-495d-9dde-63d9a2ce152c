__author__ = '{"Monish Prasad V": "<EMAIL>", "<PERSON><PERSON><PERSON>": "<EMAIL>"}'

import pytest
from common_lib.helper_function import return_bool
import allure, time
from typing import Optional, Callable

class General_Helpers:
	def __init__(self, Loggers, Variables) -> None:
		self.logger = type(self).logger = Loggers
		self.Variables = type(self).Variables = Variables
		self.LOG_HANDLER = type(self).LOG_HANDLER = pytest.helpers.LOG_HANDLER()

	@allure.step("This function searches the given string in the copy of the given plugin log for the give time")
	def is_string_in_log(self, str_to_be_searched : str, plugin : str) -> bool:
		'''
		[param]
		plugin : ZDX plugin for which the logs are to be searched
		'''
		plugin_logs = self.LOG_HANDLER.get_latest_file_data_with_plugin(self.logger, self.Variables, plugin)
		if not plugin_logs:
			self.logger.error(f"No logs found for the {plugin} plugin")
			return False
		for line in plugin_logs.splitlines():
			if str_to_be_searched in line:
				self.logger.info(f"{str_to_be_searched} found in {plugin} logs")
				return True
		self.logger.error(f"{str_to_be_searched} not found in {plugin} logs")
		return False

	@allure.step("This function searches the given string in the given plugin log for the give time in real-time")
	def is_string_in_log_realtime(self, plugin : str, str_to_be_searched, search_time : int) -> bool:
		'''
		[param]
		plugin : ZDX plugin for which the logs are to be searched
		search_time : Maximum time limit until which the searching has to be done
		'''
		lines = self.LOG_HANDLER.get_realtime_log_data(Loggers=self.logger, Variables=self.Variables, plugin=plugin)
		stime = time.time()
		self.logger.info(f"Checking for {str_to_be_searched} in {plugin} logs")
		for line in lines:
			if str_to_be_searched in line:
				self.logger.info(f"{str_to_be_searched} found in {line}")
				return True
			if (int(time.time()) - stime) > search_time:
				self.logger.info("broke")
				break
		self.logger.error(f"{str_to_be_searched} not found betweeen {int(stime * 1000)} and {int(time.time() * 1000)}")
		return False

	@allure.step("This function waits for the TPG upload cycle(s)")
	def wait_for_TPG_upload(self, no_of_cycles : int = 1) -> bool:
		'''
		[param]
		no_of_cycles : Number of TPG upload cycles to wait
		'''
		for cycle_num in range(no_of_cycles):
			self.logger.info(f"Waiting for {cycle_num + 1}/{no_of_cycles} TPG cycle")
			if not self.is_string_in_log_realtime(plugin="upm", str_to_be_searched="uploadDataToHost: Sent data to TPG successfully with 200 OK.", search_time=480):
				self.logger.error("TPG upload delayed/failed")
				return False
		return True		

	@allure.step("This function waits for the TPG upload cycle(s) and executes a callback function at the end of every TPG upload cycle")
	def wait_for_TPG_upload_cycle_with_callback(self, no_of_cycles_to_wait : int = 1, callback_func : Optional[Callable] = None) -> tuple[bool, str]:
		for cycle in range(no_of_cycles_to_wait):
			self.logger.info(f"TPG with callback attempt {cycle + 1}/{no_of_cycles_to_wait}")
			if not self.wait_for_TPG_upload() : return (False, "TPG upload delayed/failed")
			if callback_func:
				if return_bool(callback_func()):
					self.logger.info("Done")
					return (True, "Success")
				else : self.logger.error("Callback function failed")
			self.logger.info("Sleeping 5 secs for a new line to be generated in upm logs")
			time.sleep(5)
		if callback_func:
			self.logger.error("Callback function failed - overall")
			return (False, "Callback function failed - overall")
		self.logger.info("TPG with callback - success")
		return (True, "Success")

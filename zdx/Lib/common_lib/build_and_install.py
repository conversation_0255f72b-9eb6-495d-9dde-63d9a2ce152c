import subprocess
import sys
import platform
import os
import hashlib

class BuildManager:
    def __init__(self, build_dir):
        self.build_dir = build_dir
        self.checksum_file = os.path.join(os.getcwd(), "build_checksum.md5")

    def calculate_directory_checksum(self):
        """Calculate the MD5 checksum of all files in a directory."""
        md5 = hashlib.md5()

        for root, _, files in os.walk(self.build_dir):
            for file in sorted(files):
                with open(os.path.join(root, file), "rb") as f:
                    while chunk := f.read(8192):
                        md5.update(chunk)
        return md5.hexdigest()

    def is_binary_built(self):
        """Check if the binary files are already built by comparing file checksums."""
        if not os.path.exists(self.checksum_file):
            return False

        with open(self.checksum_file, "r") as f:
            old_checksum = f.read().strip()

        current_checksum = self.calculate_directory_checksum()

        return old_checksum == current_checksum

    def build_cpp_binaries(self):
        """Build C++ binaries using CMake."""
        os.chdir(self.build_dir)
        if self.is_binary_built():
            print("Binaries are already built. Skipping...")
        else:
            architecture = platform.machine().lower()
            arch_flag = "ARM64" if "arm" in architecture else "x64"
            print(f"Detected {arch_flag} architecture.")

            # Run CMake commands based on the detected architecture
            print(f"Running CMake for {arch_flag}...")
            self.run_command(["cmake", "-S", ".", "-B", ".", "-A", arch_flag])
            self.run_command(["cmake", "--build", ".", "--config", "Release"])

            # Save the checksum of the built files
            checksum = self.calculate_directory_checksum()
            with open(self.checksum_file, "w") as f:
                f.write(checksum)

        os.chdir("../../../../../../")

    def run_command(self, command, shell=False):
        """Run a command in the shell."""
        try:
            subprocess.run(command, shell=shell, check=True)
        except subprocess.CalledProcessError as e:
            print(f"Command failed with error: {e}")
            sys.exit(1)

    def get_installed_packages(self):
        """Get a list of installed pip packages."""
        result = subprocess.run([sys.executable, "-m", "pip", "freeze"], capture_output=True, text=True)
        installed_packages = {}
        for line in result.stdout.splitlines():
            if '==' in line:
                name, version = line.split('==')
                installed_packages[name.lower()] = version
            else:
                installed_packages[line.lower()] = None
        return installed_packages

    def is_package_installed(self, package_name, required_version=None):
        """Check if a specific pip package is installed."""
        command = [sys.executable, "-m", "pip", "show", package_name]
        result = subprocess.run(command, capture_output=True, text=True)

        if result.returncode == 0:
            if required_version:
                for line in result.stdout.splitlines():
                    if line.startswith("Version:"):
                        installed_version = line.split(":")[1].strip()
                        return installed_version == required_version
            else:
                return True
        return False

    def install_requirements(self):
        """Install packages from requirements.txt."""
        print("Checking if any python package needs to be install")
        installed_packages = self.get_installed_packages()

        with open("requirements.txt", "r", encoding="utf-8") as f:
            packages = [line.strip() for line in f if line.strip() and not line.startswith("#")]

        for package in packages:
            if '==' in package:
                package_name, required_version = package.split('==')
                installed_version = installed_packages.get(package_name.lower())
                if installed_version != required_version:
                    if not self.is_package_installed(package_name, required_version):
                        print(f"Installing {package_name}=={required_version}...")
                        self.run_command([sys.executable, "-m", "pip", "install", f"{package_name}=={required_version}"])        
            else:
                package_name = package
                if package_name.lower() not in installed_packages:
                    if not self.is_package_installed(package_name):
                        print(f"Installing {package_name}...")
                        self.run_command([sys.executable, "-m", "pip", "install", package_name])

        os.chdir("zdx")
        
        try:
            with open("requirements.txt", "r", encoding="utf-16") as f:
                zdx_packages = [line.strip() for line in f if line.strip() and not line.startswith("#")]
        except UnicodeDecodeError:
            with open("requirements.txt", "r", encoding="utf-8-sig") as f:
                zdx_packages = [line.strip() for line in f if line.strip() and not line.startswith("#")]

        for package in zdx_packages:
            if '==' in package:
                package_name, required_version = package.split('==')
                installed_version = installed_packages.get(package_name.lower())
                if installed_version != required_version:
                    if not self.is_package_installed(package_name, required_version):
                        print(f"Installing {package_name}=={required_version}...")
                        self.run_command([sys.executable, "-m", "pip", "install", f"{package_name}=={required_version}"])
            else:
                package_name = package
                if package_name.lower() not in installed_packages:
                    if not self.is_package_installed(package_name):
                        print(f"Installing {package_name}...")
                        self.run_command([sys.executable, "-m", "pip", "install", package_name])

        os.chdir("..")

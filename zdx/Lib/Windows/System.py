# from Lib.Windows.Extras.Shared.packages.Release import WMA
import platform, shutil
import ctypes
import datetime
import subprocess
import multiprocessing
import time
import winreg
import psutil
import requests
from tzlocal import get_localzone
import os
import logging
from dateutil import parser
import win32service
import pywintypes
import winreg as wrg

class System:
    def __init__(self) -> None:
        pass

    def get_npcap_version_from_registry(self):
        registry_paths = [
            r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\NpcapInst",
            r"Software\Microsoft\Windows\CurrentVersion\Uninstall\NpcapInst"
        ]
        npcap_version = ""
        for key_path in registry_paths:
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path) as key:
                    try:
                        npcap_version = winreg.QueryValueEx(key, "DisplayVersion")[0]
                        npcap_version = npcap_version.replace("DisplayVersion", "").strip()
                        return npcap_version
                    except FileNotFoundError:
                        pass  
            except FileNotFoundError:
                pass  
            except PermissionError:
                return "Permission denied to access the registry"
            except Exception as e:
                return f"Error: {e}"

        return npcap_version
    
    # def getRunningProcessArch(self,ProcessName: str):
    #     if len(ProcessName) == 0: raise Exception("Process Name is not defined, please define a valid process name")
    #     osArch = True if "64" in platform.architecture()[0] else False
    #     try:
    #         arch=WMA.is64BitProcess(ProcessName, osArch)
    #         if arch: return "64"
    #     except Exception as E:
    #         raise Exception(E)
    #     return "32"
    
    def set_system_time(self, hours_to_add=0, minutes_to_add=0):
        """
        This function alters the system's time according to the specified number of hours,or minutes provided as a parameter, which can be either positive (to advance time) or negative (to regress time).

        :param self: The object itself.
        :hours_to_add: Number of hours to advance or regress
        :minutes_to_add: Number of minutes to advance or regress
        :return: None
        """
        # Get current time
        now = datetime.datetime.now()

        # Calculate the new time
        new_time = now + datetime.timedelta(hours=hours_to_add, minutes=minutes_to_add)

        # Create a SYSTEMTIME structure
        class SYSTEMTIME(ctypes.Structure):
            _fields_ = [
                ('wYear', ctypes.c_uint16),
                ('wMonth', ctypes.c_uint16),
                ('wDayOfWeek', ctypes.c_uint16),
                ('wDay', ctypes.c_uint16),
                ('wHour', ctypes.c_uint16),
                ('wMinute', ctypes.c_uint16),
                ('wSecond', ctypes.c_uint16),
                ('wMilliseconds', ctypes.c_uint16)
            ]

        system_time = SYSTEMTIME()
        system_time.wYear = new_time.year
        system_time.wMonth = new_time.month
        system_time.wDayOfWeek = new_time.weekday() + 1  # 1 for Monday, ..., 7 for Sunday
        system_time.wDay = new_time.day
        system_time.wHour = new_time.hour
        system_time.wMinute = new_time.minute
        system_time.wSecond = new_time.second
        system_time.wMilliseconds = new_time.microsecond // 1000  # Convert microseconds to milliseconds

        # Call SetLocalTime function
        kernel32 = ctypes.WinDLL('kernel32', use_last_error=True)
        if not kernel32.SetLocalTime(ctypes.byref(system_time)):
            return (False, None, ctypes.WinError(ctypes.get_last_error()))
        
        return (True, None, None)
        
    def reset_system_time(self, hours_to_reduce=0):
        """
        This function resets the system's time to correct local time from API and if the API fails, it uses set_system_time()

        :param self: The object itself.
        :hours_to_reduce: number of hours to reduce will be used when the device's timezone doesn't match with the api response
        :return: None
        """

        # Create a SYSTEMTIME structure
        class SYSTEMTIME(ctypes.Structure):
            _fields_ = [
                ('wYear', ctypes.c_uint16),
                ('wMonth', ctypes.c_uint16),
                ('wDayOfWeek', ctypes.c_uint16),
                ('wDay', ctypes.c_uint16),
                ('wHour', ctypes.c_uint16),
                ('wMinute', ctypes.c_uint16),
                ('wSecond', ctypes.c_uint16),
                ('wMilliseconds', ctypes.c_uint16)
            ]        

        # List of timezones from the API

        try:
            api_timezones = set(requests.get("http://worldtimeapi.org/api/timezone").json())

            local_timezone = get_localzone()
            device_timezone = local_timezone.zone

            if device_timezone in api_timezones:
                try:
                    timeapi = requests.get(f"http://worldtimeapi.org/api/timezone/{device_timezone}").json()

                    datetime_str = timeapi['datetime']

                    dt = parser.parse(datetime_str)

                    system_time = SYSTEMTIME()
                    system_time.wYear = dt.year
                    system_time.wMonth = dt.month
                    system_time.wDay = dt.day
                    system_time.wHour = dt.hour
                    system_time.wMinute = dt.minute
                    system_time.wSecond = dt.second
                    system_time.wMilliseconds = dt.microsecond // 1000

                    kernel32 = ctypes.WinDLL('kernel32', use_last_error=True)
                    if not kernel32.SetLocalTime(ctypes.byref(system_time)):
                        return (False, None, ctypes.WinError(ctypes.get_last_error()))
                    else:
                        raise Exception("Set time failed")
                except Exception as e:
                    return self.set_system_time(hours_to_add=(-hours_to_reduce))
            else:
                return self.set_system_time(hours_to_add=(-hours_to_reduce))
        except Exception as e:
            return self.set_system_time(hours_to_add=(-hours_to_reduce))     

    def get_npcap_version(self):
                        # return type shall be string and value will be npcap version if set to True.


            version = subprocess.Popen(["powershell",
                                        'Get-ItemProperty HKLM:\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\* | sort-object -property DisplayVersion | Select-Object DisplayName, DisplayVersion, Publisher, InstallDate | Where-Object{$_.DisplayName -eq "Npcap OEM"}'],
                                       stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            version = version.communicate()
            try:
                version = version[0].decode('utf-8').strip().splitlines()[-1].split()
                return version[2]
            except:
                version = subprocess.Popen(["powershell",
                                            'Get-ItemProperty HKLM:\\Software\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\* | sort-object -property DisplayVersion | Select-Object DisplayName, DisplayVersion, Publisher, InstallDate | Where-Object{$_.DisplayName -eq "Npcap OEM"}'],
                                           stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                version = version.communicate()
                try:
                    version = version[0].decode('utf-8').strip().splitlines()[-1].split()
                    return version[2]
                except Exception as E:
                    raise Exception(f"Variable Module could not find npcap or Application npcap is not installed : {E}")

    #Function to stress the CPU
    @staticmethod
    def consume_cpu():
        while True:
            pass
    
    def run_cpu_consumption(self, duration_minutes):
        """
        This function stress the CPU for n minutes

        :param self: The object itself.
        :duration_minutes: Number of minutes to stress the CPU

        :return: None
        """
        # Get the number of CPU cores
        num_cores = multiprocessing.cpu_count()

        # Create a list to store CPU consuming processes
        processes = []

        # Start a CPU consuming process for each CPU cor
        for _ in range(num_cores):
            process = multiprocessing.Process(target=self.consume_cpu)
            processes.append(process)
            process.start()

        try:
            # Keep the processes running for the specified duration
            time.sleep(duration_minutes * 60)
            for process in processes:
                process.terminate()
        except KeyboardInterrupt:
            # Stop the CPU consuming processes if interrupted
            for process in processes:
                process.terminate()
    
    def get_dll_path_of_process(self, target_process_name):
        """
        This function return dll file of a process like "ZSAUpm.exe"

        :param self: The object itself.
        :target_process_name: process name

        :return: return a tuple contain (status, path/process name)
        """
        target_process = None
        for proc in psutil.process_iter(attrs=["name", "pid"]):
            if proc.info["name"] == target_process_name:
                target_process = psutil.Process(proc.info["pid"])
                break
        dll_path_list = []
        if target_process:
            for dll in target_process.memory_maps():
                dll_path_list.append(dll.path)
            return (True, dll_path_list)
        else:
            return(False, target_process_name)

    def get_process_architecture_by_name(self, process_name: str) -> tuple:
        """
        Retrieves the architecture of a process by its name.

        Args:
            process_name (str): The name of the process to search for.

        Returns:
            tuple: A tuple containing a boolean success flag, a message string, and the result string.
                - If successful, the tuple will be (True, "Success", response).
                - If unsuccessful, the tuple will contain (False, "Error message", None).
        """
        try:
            # Find the process by name
            process_name += ".exe"
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'].lower() == process_name.lower():
                    pid = proc.info['pid']
                    # Open the process
                    process_handle = ctypes.windll.kernel32.OpenProcess(0x1000, False, pid)
                    if not process_handle:
                        return (False, "Failed to open process.", None)
                    
                    # Check if it's a 64-bit process
                    is_wow64 = ctypes.c_int()
                    ctypes.windll.kernel32.IsWow64Process(process_handle, ctypes.byref(is_wow64))
                    
                    # Close the handle
                    ctypes.windll.kernel32.CloseHandle(process_handle)

                    if is_wow64.value:
                        return (True, "Success", "32")
                    else:
                        return (True, "Success", "64")
            
            return (False, f"No process found with the name '{process_name}'.", None)
        
        except Exception as e:
            return (False, f"An error occurred: {str(e)}", None)
                
    def uninstall_winget_application(self, publisher, application, path=None):
        """
        This function uninstalls the given winget application
        """
        if application == "Notepad++":
            try:
                source = os.path.join(os.getenv('APPDATA'), 'Notepad++','backup')
                if os.path.exists(source):
                    dest = os.path.join(path, "Notepad_backup")
                    shutil.move(source, dest)
            except Exception as e:
                return (False, f"Failed to Move the file: {str(e)}", "")
            
        result = subprocess.run(f"winget uninstall --id {publisher}.{application} --accept-source-agreements", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            return (True, f"Uninstalled {application}", result.stdout)
        else:
            return (False, f"Failed to uninstall {application}" , result.stderr)

    def install_winget_application(self,publisher, application,path=None):
        """
        This function installs the given winget application
        """
        result = subprocess.run(f"winget install --id {publisher}.{application} --accept-source-agreements --accept-package-agreements", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            if application == "Notepad++":
                try:
                    source = os.path.join(path, "Notepad_backup")
                    dest = os.path.join(os.getenv('APPDATA'), 'Notepad++','backup')
                    if os.path.exists(source):
                        shutil.move(source, dest)
                except Exception as e:
                        return (False, f"Failed to Move the file: {str(e)}", "")
            return (True, f"Installed {application}", result.stdout)
        else:
            return (False, f"Failed to install {application}.", result.stderr)

    def get_location_from_appname(self, appName) -> str:
        appLocation=None
        isFound=None
        commands=[
            rf'where /R  "C:\Program Files" {appName}*exe',
            rf'where /R  "C:\Program Files (x86)" {appName}*exe',
            rf'where /R "C:\Program Files (Arm)" {appName}*exe',
            rf'where /R "C:\Users" "{appName}"']
        for command in commands:
            if not isFound:
                result=subprocess.run(command,shell=True,capture_output=True, text=True)
                if result.returncode==0 :
                    output=result.stdout.splitlines()
                    if len(output)>0:
                        appLocation=output[0]
                        isFound=True
                        break
        return appLocation
    
        
    def install_zoom_teams_app(self,appname):
        appname = appname.lower()
        download_folder = os.path.join(os.environ['USERPROFILE'], 'Downloads')
        os.makedirs(download_folder, exist_ok=True)

        if appname == 'zoom':
            download_url = 'https://zoom.us/client/latest/ZoomInstaller.exe'
            filename = 'zoom_installer.exe'
            silent_flag = '/quiet'
            success_files = [
                r'C:\Program Files (x86)\Zoom\bin\Zoom.exe',  
                r'C:\ProgramData\Microsoft\Windows\Start Menu\Programs\Zoom\Zoom.lnk',
                os.path.join(os.environ['USERPROFILE'], 'AppData', 'Roaming', 'Microsoft', 'Windows', 'Start Menu', 'Programs', 'Zoom', 'Zoom Workplace.lnk'),
                os.path.join(os.environ['USERPROFILE'], 'AppData', 'Roaming', 'Zoom', 'bin', 'Zoom.exe') 
            ]
        elif appname == 'teams':
            download_url = 'https://teams.microsoft.com/downloads/desktopurl?env=production&plat=windows&arch=x64&download=true'
            filename = 'ms_teams.exe'
            silent_flag = '/silent'
            success_files = [
                r'C:\Program Files (x86)\Microsoft\Teams\current\Teams.exe',
                os.path.join(os.environ['USERPROFILE'], 'AppData', 'Local', 'Microsoft', 'Teams', 'current', 'Teams.exe')  
            ]
        else:
            print(f"Unsupported application: {appname}")
            return False, f"Unsupported application: {appname}"
            

        installer_path = os.path.join(download_folder, filename)
        try:
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            with open(installer_path, 'wb') as file:
                for chunk in response.iter_content(chunk_size=8192):
                    file.write(chunk)
            print(f"{appname.capitalize()} installer downloaded to {installer_path}")
        except requests.RequestException as e:
            print(f"Error downloading the installer: {e}")
            return False, f"Error downloading the installer: {e}"

        try:
            if appname == 'zoom':
                subprocess.run([installer_path, silent_flag], check=True)
            elif appname == 'teams':
                subprocess.run([installer_path, silent_flag], check=True)
            print(f"{appname.capitalize()} installation started.")
        except subprocess.CalledProcessError as e:
            print(f"Installation of {appname} failed: {e}")
            return False, f"Installation of {appname} failed: {e}"

        print(f"Waiting for {appname.capitalize()} installation to complete...")
        timeout = 300 
        start_time = time.time()
        file_found = 0
        while time.time() - start_time < timeout:
            for file in success_files:
                if os.path.exists(file):
                    print(f"{appname.capitalize()} File found: {file}")
                    file_found = 1
                    break

            if file_found:
                print(f"SUCCESS :: {appname.capitalize()} installed successfully.")
                return True,f"SUCCESS :: {appname.capitalize()} installed successfully."
            missing_files = [file for file in success_files if not os.path.exists(file)]
            if not missing_files:
                return
            time.sleep(5)
    
    def find_files(self, file_name, search_path):
        """
        Search for files in a directory.

        :param file_name: The name of the file to search for.
        :param search_path: The directory path to search in.
        :return: A tuple containing a boolean status and a string message or list of file paths.
                If files are found, the status will be True and the message will contain the list of file paths.
                Otherwise, the status will be False and the message will contain the error details.
        """
        result = []
        try:
            for root, dirs, files in os.walk(search_path):
                if file_name in files:
                    result.append(os.path.join(root, file_name))
            if result:
                return (True, "Success", result)
            else:
                return (False, f"File {file_name} not found in {search_path}.", [])
        except Exception as e:
            return (False, f"ERROR :: An error occurred while searching :: {e}", [])

    def rename_file(self, old_name, new_name):
        """
        Rename a file.

        :param old_name: The current name of the file.
        :param new_name: The new name for the file.
        :return: A tuple containing a boolean status and a string message.
                If the operation was successful, the status will be True and the message will be "Success".
                Otherwise, the status will be False and the message will contain the error details.
        """
        try:
            os.rename(old_name, new_name)
            return (True, f"Success :: File renamed from {old_name} to {new_name}", "")
        except FileNotFoundError:
            return (False, f"ERROR :: The file {old_name} does not exist.", "")
        except PermissionError:
            return (False, "ERROR :: Permission denied.", "")
        except Exception as e:
            return (False, f"ERROR :: An error occurred :: {e}", "")
        
    def get_service_startup_type(self, service_name):
        """
        Get the current start type of a Windows service.

        :param service_name: The name of the service.
        :return: A tuple containing a boolean status, the current start type or error message, and startup type or None.
        """
        startup_type_map = {
            win32service.SERVICE_AUTO_START: "automatic",
            win32service.SERVICE_DEMAND_START: "manual",
            win32service.SERVICE_DISABLED: "disabled"
        }

        try:
            scm_handle = win32service.OpenSCManager(None, None, win32service.SC_MANAGER_ALL_ACCESS)
        except Exception as e:
            return (False, f"ERROR :: Failed to open service control manager :: {e}", None)
        
        if not scm_handle:
            return (False, "ERROR :: Failed to open service control manager", None)
        
        try:
            service_handle = win32service.OpenService(scm_handle, service_name, win32service.SERVICE_QUERY_CONFIG)
        except pywintypes.error as e:
            if e.winerror == 1060:  # ERROR_SERVICE_DOES_NOT_EXIST
                return (False, f"ERROR :: The service '{service_name}' does not exist.", None)
            return (False, f"ERROR :: Failed to open service: {service_name} :: {e}", None)
        
        if not service_handle:
            return (False, f"ERROR :: The service '{service_name}' does not exist.", None)
        
        try:
            service_config = win32service.QueryServiceConfig(service_handle)
            start_type = service_config[1]
            start_type_str = startup_type_map.get(start_type, "UNKNOWN")

            return (True, "Success", start_type_str)
        except Exception as e:
            return (False, f"ERROR :: Failed to query service configuration :: {e}", None)
        finally:
            win32service.CloseServiceHandle(service_handle)
            win32service.CloseServiceHandle(scm_handle)  

    def set_service_start_type(self, service_name, start_type)->tuple[bool,str,str]:
        """
        Change the start type of a Windows service.

        :param service_name: The name of the service to change.
        :param start_type: The desired start type ('disabled', 'manual', 'automatic').
        :return: A tuple containing a boolean status and a string message. 
                If the operation was successful, the status will be True and the message will be "Success".
                Otherwise, the status will be False and the message will contain the error details.
        """

        start_type_map = {
            'disabled': win32service.SERVICE_DISABLED,
            'manual': win32service.SERVICE_DEMAND_START,
            'automatic': win32service.SERVICE_AUTO_START
        }

        if start_type not in start_type_map:
            return (False, f"ERROR :: Invalid start type. Please choose 'disabled', 'manual', or 'automatic'.", "")

        # Open the service control manager
        try:
            scm = win32service.OpenSCManager(None, None, win32service.SC_MANAGER_ALL_ACCESS)
        except Exception as e:
            return (False, f"ERROR :: Failed to open service control manager :: {e}", "")

        if not scm:
            return (False, "ERROR :: Failed to open service control manager", "")

        # Attempt to open the service
        try:
            service = win32service.OpenService(scm, service_name, win32service.SERVICE_CHANGE_CONFIG)
        except pywintypes.error as e:
            if e.winerror == 1060:  # ERROR_SERVICE_DOES_NOT_EXIST
                return (False, f"ERROR :: The service '{service_name}' does not exist.", "")
            else:
                return (False, f"ERROR :: Failed to open service: {service_name} :: {e}", "")
        
        if not service:
            return (False, f"ERROR :: Failed to open service: {service_name}", "")

        # Change the start type
        try:
            win32service.ChangeServiceConfig(
                service,
                win32service.SERVICE_NO_CHANGE,
                start_type_map[start_type],
                win32service.SERVICE_NO_CHANGE,
                None,
                None,
                0,
                None,
                None,
                None,
                None
            )
            return (True, f"Success :: Service '{service_name}' start type changed to '{start_type}' successfully.", "")
        except Exception as e:
            return (False, f"ERROR :: Failed to change service start type :: {e}", "")
        finally:
            win32service.CloseServiceHandle(service)
            win32service.CloseServiceHandle(scm)

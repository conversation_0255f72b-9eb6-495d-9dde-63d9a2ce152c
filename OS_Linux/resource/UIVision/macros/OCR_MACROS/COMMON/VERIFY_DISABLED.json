{"Name": "VERIFY_DISABLED", "CreationDate": "2025-6-16", "Commands": [{"Command": "XClickText", "Target": "More", "Value": "", "Description": ""}, {"Command": "XClickText", "Target": "*Security*", "Value": "", "Description": ""}, {"Command": "comment", "Target": "run // WAIT_TUNNEL_LOADING_STOP", "Value": "", "Description": ""}, {"Command": "OCRExtractRelative", "Target": "zcc_tunnel_status_dpi_96.png", "Value": "var", "Description": ""}, {"Command": "echo", "Target": "${var}", "Value": "", "Description": ""}, {"Command": "if", "Target": "${var}.toLowerCase().lastIndexOf(\"disabled\") == -1", "Value": "", "Description": ""}, {"Command": "throwError", "Target": "Tunnel status: not DISABLED", "Value": "", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}]}
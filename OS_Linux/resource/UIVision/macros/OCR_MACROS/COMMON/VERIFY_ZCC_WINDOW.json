{"Name": "VERIFY_ZCC_WINDOW", "CreationDate": "2025-5-12", "Commands": [{"Command": "comment", "Target": "run // Desktop_automation", "Value": "", "Description": ""}, {"Command": "store", "Target": "10", "Value": "!TIMEOUT_WAIT", "Description": ""}, {"Command": "store", "Target": "120", "Value": "!TIMEOUT_MACRO", "Description": ""}, {"Command": "OCRSearch", "Target": "*Client Connector*", "Value": "found", "Description": ""}, {"Command": "while", "Target": "${found} == 0", "Value": "", "Description": ""}, {"Command": "pause", "Target": "2000", "Value": "", "Description": ""}, {"Command": "OCRSearch", "Target": "*Client Connector*", "Value": "found", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}, {"Command": "store", "Target": "80", "Value": "!TIMEOUT_WAIT", "Description": ""}, {"Command": "store", "Target": "0", "Value": "!TIMEOUT_MACRO", "Description": ""}]}
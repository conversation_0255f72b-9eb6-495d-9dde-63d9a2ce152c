{"Name": "VERIFY_ZCC_LOGGEDIN", "CreationDate": "2025-3-4", "Commands": [{"Command": "comment", "Target": "run // Desktop_automation", "Value": "", "Description": ""}, {"Command": "OCRExtractRelative", "Target": "zcc_loggedin_window_title_dpi_96.png", "Value": "var", "Description": ""}, {"Command": "echo", "Target": "${var}", "Value": "", "Description": ""}, {"Command": "if", "Target": "${var}.toLowerCase().lastIndexOf(\"zscaler client\") == -1", "Value": "", "Description": ""}, {"Command": "throwError", "Target": "Unable to find ZCC window", "Value": "", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}]}
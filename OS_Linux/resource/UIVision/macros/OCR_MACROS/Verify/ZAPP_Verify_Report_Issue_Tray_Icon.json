{"Name": "ZAPP_Verify_Report_Issue_Tray_Icon", "CreationDate": "2025-5-12", "Commands": [{"Command": "run", "Target": "../Open_ZAPP", "Value": "", "Description": ""}, {"Command": "XClickText", "Target": "*Security*", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_tray_icon_blue_dpi_96.png", "Value": "", "Description": ""}, {"Command": "pause", "Target": "1000", "Value": "", "Description": ""}, {"Command": "OCRSearch", "Target": "Report", "Value": "var", "Description": ""}, {"Command": "echo", "Target": "${var}", "Value": "", "Description": ""}, {"Command": "if", "Target": "${var}==0", "Value": "", "Description": ""}, {"Command": "throwError", "Target": "Unable to find \"Report an issue\" (in tray menu)", "Value": "", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}, {"Command": "XClickText", "Target": "Open*", "Value": "", "Description": ""}]}
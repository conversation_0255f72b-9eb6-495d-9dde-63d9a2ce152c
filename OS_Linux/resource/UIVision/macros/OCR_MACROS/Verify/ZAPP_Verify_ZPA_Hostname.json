{"Name": "ZAPP_Verify_ZPA_Hostname", "CreationDate": "2025-6-16", "Commands": [{"Command": "run", "Target": "../Open_ZAPP", "Value": "", "Description": ""}, {"Command": "XClickText", "Target": "More", "Value": "", "Description": ""}, {"Command": "XClickText", "Target": "*Access*", "Value": "", "Description": ""}, {"Command": "pause", "Target": "1000", "Value": "", "Description": ""}, {"Command": "run", "Target": "../COMMON/VERIFY_TUNNEL_ON", "Value": "", "Description": ""}, {"Command": "OCRExtractRelative", "Target": "zcc_zpa_client_hostname_dpi_96.png", "Value": "var", "Description": ""}, {"Command": "echo", "Target": "${var}", "Value": "", "Description": ""}, {"Command": "if", "Target": "${var}.toLowerCase().lastIndexOf(\"com\") == -1", "Value": "", "Description": ""}, {"Command": "throwError", "Target": "Hostname is different than expected", "Value": "", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}]}
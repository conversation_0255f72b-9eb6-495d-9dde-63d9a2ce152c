{"Name": "ZAPP_ZPA_Disable", "CreationDate": "2025-6-16", "Commands": [{"Command": "run", "Target": "../Open_ZAPP", "Value": "", "Description": ""}, {"Command": "XClickText", "Target": "More", "Value": "", "Description": ""}, {"Command": "XClickText", "Target": "*Access*", "Value": "", "Description": ""}, {"Command": "pause", "Target": "1000", "Value": "", "Description": ""}, {"Command": "OCRExtractRelative", "Target": "zcc_tunnel_status_dpi_96.png", "Value": "var", "Description": ""}, {"Command": "echo", "Target": "${var}", "Value": "", "Description": ""}, {"Command": "if", "Target": "${var}.toLowerCase().search(/n.*off/) != -1", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_disable_button_dpi_96.png", "Value": "", "Description": ""}, {"Command": "pause", "Target": "2000", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_TAB}", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_TAB}", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}, {"Command": "pause", "Target": "2000", "Value": "", "Description": ""}, {"Command": "run", "Target": "../COMMON/WAIT_TUNNEL_LOADING_STOP", "Value": "", "Description": ""}, {"Command": "OCRExtractRelative", "Target": "zcc_tunnel_status_dpi_96.png", "Value": "var", "Description": ""}, {"Command": "echo", "Target": "${var}", "Value": "", "Description": ""}, {"Command": "if", "Target": "${var}.toLowerCase().search(/n.*on/) == -1", "Value": "", "Description": ""}, {"Command": "throwError", "Target": "Unable to turn off tunnel (status: not OFF)", "Value": "", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}]}
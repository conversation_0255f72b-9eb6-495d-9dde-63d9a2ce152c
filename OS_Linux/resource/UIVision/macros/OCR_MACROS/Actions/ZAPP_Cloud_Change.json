{"Name": "ZAPP_Cloud_Change", "CreationDate": "2025-5-12", "Commands": [{"Command": "run", "Target": "../Open_ZAPP", "Value": "", "Description": ""}, {"Command": "run", "Target": "../COMMON/VERIFY_ZCC_LOGGEDOUT", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_hamburger_menu_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XClickText", "Target": "Cloud Name", "Value": "", "Description": ""}, {"Command": "pause", "Target": "2000", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_TAB}${!cmd_var1}", "Value": "", "Description": ""}, {"Command": "comment", "Target": "XType // ${KEY_TAB}zscalertwo", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_TAB}${KEY_TAB}${KEY_TAB}${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "run", "Target": "../COMMON/VERIFY_INVALID_CLOUD_NAME", "Value": "", "Description": ""}, {"Command": "run", "Target": "../COMMON/VERIFY_ZCC_LOGGEDOUT", "Value": "", "Description": ""}]}
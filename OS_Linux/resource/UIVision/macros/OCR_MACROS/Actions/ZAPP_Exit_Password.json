{"Name": "ZAPP_Exit_Password", "CreationDate": "2025-3-4", "Commands": [{"Command": "run", "Target": "../Open_ZAPP", "Value": "", "Description": ""}, {"Command": "if", "Target": "${!cmd_var3}==\"elux\"", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "elux_zcc_tray_icon_dpi_96.png", "Value": "", "Description": ""}, {"Command": "else", "Target": "", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_tray_icon_blue_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_DOWN}${KEY_DOWN}${KEY_DOWN}${KEY_DOWN}${KEY_DOWN}${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_warning_icon_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_TAB}", "Value": "", "Description": ""}, {"Command": "XType", "Target": "e", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "run", "Target": "../COMMON/VERIFY_WRONG_PASSWORD", "Value": "", "Description": ""}, {"Command": "run", "Target": "../COMMON/VERIFY_ZCC_NO_WINDOW", "Value": "", "Description": ""}]}
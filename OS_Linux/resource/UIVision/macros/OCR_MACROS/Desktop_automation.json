{"Name": "Desktop_automation", "CreationDate": "2025-5-12", "Commands": [{"Command": "pause", "Target": "1000", "Value": "", "Description": ""}, {"Command": "comment", "Target": "selectWindow // tab=open", "Value": "file:///home/<USER>/Downloads/Blank.html", "Description": ""}, {"Command": "comment", "Target": "selectWindow // title=Example Domain", "Value": "", "Description": ""}, {"Command": "selectWindow", "Target": "tab=CLOSEALLOTHER", "Value": "", "Description": ""}, {"Command": "XDesktopAutomation", "Target": "true", "Value": "", "Description": ""}, {"Command": "store", "Target": "80", "Value": "!TIMEOUT_WAIT", "Description": ""}, {"Command": "store", "Target": "0", "Value": "!TIMEOUT_MACRO", "Description": ""}, {"Command": "csvReadArray", "Target": "auto_values.csv", "Value": "values", "Description": ""}, {"Command": "store", "Target": "medium", "Value": "!replayspeed", "Description": ""}, {"Command": "store", "Target": "98", "Value": "!OCREngine", "Description": ""}, {"Command": "store", "Target": "ENG", "Value": "!OCRLanguage", "Description": ""}, {"Command": "store", "Target": "true", "Value": "!OCRScale", "Description": ""}, {"Command": "comment", "Target": "bringBrowserToForeground // ", "Value": "", "Description": ""}]}
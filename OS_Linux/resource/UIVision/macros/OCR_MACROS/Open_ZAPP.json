{"Name": "Open_ZAPP", "CreationDate": "2025-6-16", "Commands": [{"Command": "run", "Target": "Desktop_automation", "Value": "", "Description": ""}, {"Command": "if", "Target": "${!cmd_var3}==\"elux\"", "Value": "", "Description": ""}, {"Command": "run", "Target": "Open_VNC", "Value": "", "Description": ""}, {"Command": "else", "Target": "", "Value": "", "Description": ""}, {"Command": "XRun", "Target": "wmctrl", "Value": "-c \"Zscaler Client Connector\"", "Description": ""}, {"Command": "XType", "Target": "${KEY_CTRL+KEY_O}", "Value": "", "Description": ""}, {"Command": "XType", "Target": "<PERSON><PERSON><PERSON>", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "run", "Target": "COMMON/VERIFY_ZCC_WINDOW", "Value": "", "Description": ""}, {"Command": "comment", "Target": "XType // ${KEY_UP}", "Value": "", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}]}
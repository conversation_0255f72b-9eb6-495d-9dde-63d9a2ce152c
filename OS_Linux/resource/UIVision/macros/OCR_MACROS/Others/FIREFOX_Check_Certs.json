{"Name": "FIREFOX_Check_Certs", "CreationDate": "2025-5-12", "Commands": [{"Command": "comment", "Target": "run // ..\\Desktop_automation", "Value": "", "Description": ""}, {"Command": "run", "Target": "..\\Open_ZAPP", "Value": "", "Description": ""}, {"Command": "XRun", "Target": "killall", "Value": "firefox", "Description": ""}, {"Command": "pause", "Target": "3000", "Value": "", "Description": ""}, {"Command": "echo", "Target": "Opening Firefox", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_CTRL+KEY_O}Firefox${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "pause", "Target": "2000", "Value": "", "Description": ""}, {"Command": "run", "Target": "../COMMON/VERIFY_ZCC_NO_WINDOW", "Value": "var", "Description": ""}, {"Command": "pause", "Target": "2000", "Value": "", "Description": ""}, {"Command": "store", "Target": "slow", "Value": "!replayspeed", "Description": ""}, {"Command": "XType", "Target": "${KEY_CTRL+KEY_L}", "Value": "", "Description": ""}, {"Command": "store", "Target": "about:preferences", "Value": "!CLIPBOARD", "Description": ""}, {"Command": "XType", "Target": "${KEY_CTRL+KEY_V}${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_C}${KEY_E}${KEY_R}${KEY_T}${KEY_I}${KEY_F}${KEY_I}", "Value": "", "Description": ""}, {"Command": "store", "Target": "medium", "Value": "!replayspeed", "Description": ""}, {"Command": "XType", "Target": "${KEY_TAB}${KEY_TAB}${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "XClickText", "Target": "Certificate Name", "Value": "", "Description": ""}, {"Command": "XType", "Target": "Zscaler${KEY_UP}", "Value": "", "Description": ""}, {"Command": "OCRSearch", "Target": "<PERSON><PERSON><PERSON>", "Value": "var", "Description": ""}, {"Command": "if", "Target": "${var}!=1", "Value": "", "Description": ""}, {"Command": "throwError", "Target": "Unable to find <PERSON><PERSON><PERSON> Certs in Firefox", "Value": "", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_ALT+KEY_F4}", "Value": "", "Description": ""}]}
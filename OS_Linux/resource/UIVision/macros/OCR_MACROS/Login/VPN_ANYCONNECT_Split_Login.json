{"Name": "VPN_ANYCONNECT_Split_Login", "CreationDate": "2025-3-4", "Commands": [{"Command": "run", "Target": "../Desktop_automation", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_CTRL+KEY_O}Cisco${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "visualVerify", "Target": "aynconnect_window_logo_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XClickRelative", "Target": "anyconnect_server_input_box_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_CTRL+KEY_A}************${KEY_ENTER}${KEY_TAB}${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "pause", "Target": "3000", "Value": "", "Description": ""}, {"Command": "comment", "Target": "XClickRelative // anyconnect_group_dropdown_box_dpi_96.png", "Value": "", "Description": ""}, {"Command": "comment", "Target": "XType // ${KEY_DOWN}${KEY_DOWN}${KEY_DOWN}${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "XClickRelative", "Target": "aynconnect_username_box_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_CTRL+KEY_A}zscaler", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_TAB}Zrelacs321!", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "pause", "Target": "5000", "Value": "", "Description": ""}]}
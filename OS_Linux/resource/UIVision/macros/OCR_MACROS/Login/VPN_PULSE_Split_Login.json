{"Name": "VPN_PULSE_Split_Login", "CreationDate": "2025-3-4", "Commands": [{"Command": "run", "Target": "../Desktop_automation", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_CTRL+KEY_O}PulseUI${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "visualVerify", "Target": "Pulse_window_logo_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XClickRelative", "Target": "pulse_connect_button_dpi_96.png", "Value": "", "Description": ""}, {"Command": "pause", "Target": "3000", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "XClickRelative", "Target": "pulse_split_select_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "pause", "Target": "3000", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_CTRL+KEY_A}aarti", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_TAB}zscaler123", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_ENTER}", "Value": "", "Description": ""}]}
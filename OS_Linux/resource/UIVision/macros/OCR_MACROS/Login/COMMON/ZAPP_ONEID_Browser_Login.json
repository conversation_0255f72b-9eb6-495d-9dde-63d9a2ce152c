{"Name": "ZAPP_ONEID_Browser_Login", "CreationDate": "2025-4-21", "Commands": [{"Command": "XClickRelative", "Target": "zcc_username_field_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${oneid_user}${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "pause", "Target": "20000", "Value": "", "Description": ""}, {"Command": "XDesktopAutomation", "Target": "false", "Value": "", "Description": ""}, {"Command": "selectWindow", "Target": "title=<PERSON><PERSON><PERSON>", "Value": "", "Description": ""}, {"Command": "type", "Target": "name=password", "Value": "${oneid_pass}", "Targets": ["name=password", "xpath=//*[@id=\"auth-page-body\"]/form/div[2]/div/div/div/input", "xpath=//input[@name='password']", "xpath=//input", "css=#auth-page-body > form > div:nth-child(2) > div.mt-xxl.flex.flex-col.w-[240px] > div > div > input"], "Description": ""}, {"Command": "XDesktopAutomation", "Target": "true", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "pause", "Target": "5000", "Value": "", "Description": ""}, {"Command": "XClickText", "Target": "Open Zscaler*@pos=2", "Value": "", "Description": ""}]}
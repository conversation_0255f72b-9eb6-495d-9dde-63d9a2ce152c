{"Name": "ZAPP_ZPA_Reauth", "CreationDate": "2025-4-1", "Commands": [{"Command": "run", "Target": "../Open_ZAPP", "Value": "", "Description": ""}, {"Command": "XClickText", "Target": "Private Access", "Value": "", "Description": ""}, {"Command": "OCRSearch", "Target": "Authentication Required", "Value": "var", "Description": ""}, {"Command": "echo", "Target": "${var}", "Value": "", "Description": ""}, {"Command": "if", "Target": "${var}==0", "Value": "", "Description": ""}, {"Command": "throwError", "Target": "<PERSON><PERSON> Re<PERSON>h (option not available)", "Value": "", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}, {"Command": "XClickText", "Target": "Reauthenticate", "Value": "", "Description": ""}, {"Command": "pause", "Target": "2000", "Value": "", "Description": ""}, {"Command": "executeScript_Sandbox", "Target": "var str = ${!cmd_var2}; var res = str.split(\"***\"); return res[0];", "Value": "zpa_user", "Description": ""}, {"Command": "executeScript_Sandbox", "Target": "var str = ${!cmd_var2}; var res = str.split(\"***\"); return res[1];", "Value": "zpa_pass", "Description": ""}, {"Command": "run", "Target": "COMMON/ZAPP_Okta_Login", "Value": "", "Description": ""}]}
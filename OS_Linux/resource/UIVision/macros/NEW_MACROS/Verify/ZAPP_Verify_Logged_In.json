{"Name": "ZAPP_Verify_Logged_In", "CreationDate": "2024-9-22", "Commands": [{"Command": "run", "Target": "../Open_ZAPP", "Value": "", "Description": ""}, {"Command": "pause", "Target": "1000", "Value": "", "Description": ""}, {"Command": "store", "Target": "5", "Value": "!TIMEOUT_WAIT", "Description": ""}, {"Command": "store", "Target": "30", "Value": "!TIMEOUT_MACRO", "Description": ""}, {"Command": "visualSearch", "Target": "zcc_internet_access_icon_dpi_96.png", "Value": "found", "Description": ""}, {"Command": "while", "Target": "${found} == 0", "Value": "", "Description": ""}, {"Command": "pause", "Target": "5000", "Value": "", "Description": ""}, {"Command": "visualSearch", "Target": "zcc_internet_access_icon_dpi_96.png", "Value": "found", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}, {"Command": "store", "Target": "80", "Value": "!TIMEOUT_WAIT", "Description": ""}, {"Command": "store", "Target": "0", "Value": "!TIMEOUT_MACRO", "Description": ""}]}
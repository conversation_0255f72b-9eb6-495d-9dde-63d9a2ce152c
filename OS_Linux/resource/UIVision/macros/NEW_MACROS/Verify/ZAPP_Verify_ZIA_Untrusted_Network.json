{"Name": "ZAPP_Verify_ZIA_Untrusted_Network", "CreationDate": "2024-9-22", "Commands": [{"Command": "run", "Target": "../Open_ZAPP", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_internet_access_icon_dpi_96.png", "Value": "", "Description": ""}, {"Command": "pause", "Target": "1000", "Value": "", "Description": ""}, {"Command": "comment", "Target": "visualVerify // zcc_tunnel_status_on_dpi_96.png", "Value": "", "Description": ""}, {"Command": "visualVerify", "Target": "zcc_untrusted_network_dpi_96.png@0.9", "Value": "", "Description": ""}, {"Command": "OCRExtractRelative", "Target": "zcc_trusted_network_box2_dpi_96.png", "Value": "val", "Description": ""}, {"Command": "echo", "Target": "${val}", "Value": "", "Description": ""}, {"Command": "if", "Target": "${val}.lastIndexOf(\"Off\") > -1", "Value": "", "Description": ""}, {"Command": "echo", "Target": "Network Type is Off-Trusted", "Value": "", "Description": ""}, {"Command": "else", "Target": "", "Value": "", "Description": ""}, {"Command": "throwError", "Target": "Network Type is not Off-Trusted", "Value": "", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}]}
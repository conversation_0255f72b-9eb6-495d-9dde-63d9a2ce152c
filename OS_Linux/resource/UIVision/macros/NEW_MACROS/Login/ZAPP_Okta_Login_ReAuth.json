{"Name": "ZAPP_Okta_Login_ReAuth", "CreationDate": "2024-9-22", "Commands": [{"Command": "XClick", "Target": "zcc_okta_logo_dpi_96.png", "Value": "", "Description": ""}, {"Command": "run", "Target": "../Actions/ZAPP_Exit", "Value": "", "Description": ""}, {"Command": "run", "Target": "../Open_ZAPP", "Value": "", "Description": ""}, {"Command": "pause", "Target": "10000", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_private_access_icon_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_disable_button_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_okta_logo2_dpi_96.png", "Value": "", "Description": ""}, {"Command": "comment", "Target": "XType // ${KEY_TAB}${values[3][1]}", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_TAB}${zpa_user}", "Value": "", "Description": ""}, {"Command": "comment", "Target": "XType // ${KEY_TAB}${values[4][1]}", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_TAB}${zpa_pass}", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "pause", "Target": "5000", "Value": "", "Description": ""}]}
{"Name": "ZAPP_ZPA_Reauth", "CreationDate": "2024-9-22", "Commands": [{"Command": "run", "Target": "../Open_ZAPP", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_private_access_icon_dpi_96.png", "Value": "", "Description": ""}, {"Command": "visualAssert", "Target": "zcc_zpa_auth_error_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_reauth_button_dpi_96.png", "Value": "", "Description": ""}, {"Command": "pause", "Target": "10000", "Value": "", "Description": ""}, {"Command": "run", "Target": "ZAPP_Okta_Login", "Value": "", "Description": ""}, {"Command": "pause", "Target": "5000", "Value": "", "Description": ""}, {"Command": "visualAssert", "Target": "zcc_tunnel_status_on_dpi_96.png", "Value": "", "Description": ""}, {"Command": "visualAssert", "Target": "zcc_zpa_authenticated_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_internet_access_icon_dpi_96.png", "Value": "", "Description": ""}]}
{"Name": "ZAPP_ZIA_ZPA_ReAuth_Login", "CreationDate": "2024-9-22", "Commands": [{"Command": "pause", "Target": "5000", "Value": "", "Description": ""}, {"Command": "comment", "Target": "run // ../Actions/ZAPP_Exit", "Value": "", "Description": ""}, {"Command": "run", "Target": "../Open_ZAPP", "Value": "", "Description": ""}, {"Command": "executeScript_Sandbox", "Target": "var str = ${!cmd_var1}; var res = str.split(\"***\"); return res[0];", "Value": "zia_user", "Description": ""}, {"Command": "executeScript_Sandbox", "Target": "var str = ${!cmd_var1}; var res = str.split(\"***\"); return res[1];", "Value": "zia_pass", "Description": ""}, {"Command": "run", "Target": "ZAPP_Form_Login", "Value": "", "Description": ""}, {"Command": "executeScript_Sandbox", "Target": "var str = ${!cmd_var2}; var res = str.split(\"***\"); return res[0];", "Value": "zpa_user", "Description": ""}, {"Command": "executeScript_Sandbox", "Target": "var str = ${!cmd_var2}; var res = str.split(\"***\"); return res[1];", "Value": "zpa_pass", "Description": ""}, {"Command": "run", "Target": "ZAPP_Okta_Login_ReAuth", "Value": "", "Description": ""}, {"Command": "visualAssert", "Target": "zcc_logout_button_dpi_96.png", "Value": "", "Description": ""}]}
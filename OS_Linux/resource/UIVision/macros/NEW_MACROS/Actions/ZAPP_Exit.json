{"Name": "ZAPP_Exit", "CreationDate": "2024-9-22", "Commands": [{"Command": "run", "Target": "../Open_ZAPP", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_tray_icon_blue_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_tray_exit_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_warning_icon_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_TAB}${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "store", "Target": "120", "Value": "!TIMEOUT_MACRO", "Description": ""}, {"Command": "store", "Target": "10", "Value": "!TIMEOUT_WAIT", "Description": ""}, {"Command": "visualSearch", "Target": "ZCC_Window_title_dpi_96.png@0.9", "Value": "found", "Description": ""}, {"Command": "while", "Target": "${found}!=0", "Value": "", "Description": ""}, {"Command": "pause", "Target": "5000", "Value": "", "Description": ""}, {"Command": "visualSearch", "Target": "ZCC_Window_title_dpi_96.png@0.9", "Value": "found", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}, {"Command": "store", "Target": "80", "Value": "!TIMEOUT_WAIT", "Description": ""}, {"Command": "store", "Target": "0", "Value": "!TIMEOUT_MACRO", "Description": ""}]}
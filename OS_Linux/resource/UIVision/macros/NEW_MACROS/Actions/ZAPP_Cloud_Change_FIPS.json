{"Name": "ZAPP_Cloud_Change_FIPS", "CreationDate": "2024-9-22", "Commands": [{"Command": "run", "Target": "../Open_ZAPP", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_hamburger_menu_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_cloudname_icon_dpi_96.png", "Value": "", "Description": ""}, {"Command": "comment", "Target": "XType // ${KEY_TAB}${!cmd_var1}", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_TAB}zscalergov", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_TAB}${KEY_TAB}${KEY_TAB}${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "XClickRelative", "Target": "zcc_username_field_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "pause", "Target": "2000", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "comment", "Target": "visualAssert // zcc_warning_icon_dpi_96.png", "Value": "", "Description": ""}, {"Command": "comment", "Target": "XClick // zcc_ok_button_dpi_96.png@0.9", "Value": "", "Description": ""}, {"Command": "pause", "Target": "5000", "Value": "", "Description": ""}, {"Command": "visualAssert", "Target": "zcc_username_field_dpi_96.png", "Value": "", "Description": ""}]}
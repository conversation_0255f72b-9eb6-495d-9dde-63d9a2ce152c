{"Name": "ZAPP_Exit_Password", "CreationDate": "2024-9-22", "Commands": [{"Command": "run", "Target": "../Open_ZAPP", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_tray_icon_blue_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_tray_exit_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_warning_icon_dpi_96.png", "Value": "", "Description": ""}, {"Command": "comment", "Target": "XType // ${KEY_TAB}${KEY_TAB}", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_TAB}${values[8][1]}${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "store", "Target": "120", "Value": "!TIMEOUT_MACRO", "Description": ""}, {"Command": "store", "Target": "10", "Value": "!TIMEOUT_WAIT", "Description": ""}, {"Command": "visualSearch", "Target": "zcc_invalid_password_warning_dpi_96.png@0.8", "Value": "wrong_password", "Description": ""}, {"Command": "if", "Target": "${wrong_password}!=0", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_ok_button_dpi_96.png@0.8", "Value": "", "Description": ""}, {"Command": "throwError", "Target": "Wrong Passowrd", "Value": "", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}, {"Command": "visualSearch", "Target": "ZCC_Window_title_dpi_96.png", "Value": "found", "Description": ""}, {"Command": "while", "Target": "${found}!=0", "Value": "", "Description": ""}, {"Command": "pause", "Target": "5000", "Value": "", "Description": ""}, {"Command": "visualSearch", "Target": "ZCC_Window_title_dpi_96.png", "Value": "found", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}, {"Command": "store", "Target": "80", "Value": "!TIMEOUT_WAIT", "Description": ""}, {"Command": "store", "Target": "0", "Value": "!TIMEOUT_MACRO", "Description": ""}]}
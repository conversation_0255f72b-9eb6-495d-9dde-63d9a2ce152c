{"Name": "ZAPP_ZIA_Disable_Password", "CreationDate": "2024-9-22", "Commands": [{"Command": "run", "Target": "../Open_ZAPP", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_internet_access_icon_dpi_96.png", "Value": "", "Description": ""}, {"Command": "pause", "Target": "1000", "Value": "", "Description": ""}, {"Command": "visualAssert", "Target": "zcc_tunnel_status_on_dpi_96.png@0.8", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_disable_button_dpi_96.png", "Value": "", "Description": ""}, {"Command": "comment", "Target": "XType // ${KEY_TAB}${KEY_TAB}", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_TAB}${KEY_TAB}${values[5][1]}${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "pause", "Target": "2000", "Value": "", "Description": ""}, {"Command": "store", "Target": "10", "Value": "!TIMEOUT_WAIT", "Description": ""}, {"Command": "store", "Target": "120", "Value": "!TIMEOUT_MACRO", "Description": ""}, {"Command": "visualSearch", "Target": "zcc_invalid_password_warning_dpi_96.png@0.8", "Value": "wrong_password", "Description": ""}, {"Command": "if", "Target": "${wrong_password}!=0", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_ok_button_dpi_96.png@0.8", "Value": "", "Description": ""}, {"Command": "throwError", "Target": "Wrong Passowrd", "Value": "", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}, {"Command": "visualSearch", "Target": "zcc_disable_button_dpi_96.png", "Value": "found", "Description": ""}, {"Command": "while", "Target": "${found} == 0", "Value": "", "Description": ""}, {"Command": "pause", "Target": "5000", "Value": "", "Description": ""}, {"Command": "visualSearch", "Target": "zcc_disable_button_dpi_96.png", "Value": "found", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}, {"Command": "store", "Target": "80", "Value": "!TIMEOUT_WAIT", "Description": ""}, {"Command": "store", "Target": "0", "Value": "!TIMEOUT_MACRO", "Description": ""}, {"Command": "visualAssert", "Target": "zcc_tunnel_status_off_dpi_96.png", "Value": "", "Description": ""}]}
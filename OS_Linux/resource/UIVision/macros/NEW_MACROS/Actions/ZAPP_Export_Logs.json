{"Name": "ZAPP_Export_Logs", "CreationDate": "2024-9-22", "Commands": [{"Command": "run", "Target": "../Open_ZAPP", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_tray_icon_blue_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_tray_export_logs_dpi_96.png", "Value": "", "Description": ""}, {"Command": "pause", "Target": "5000", "Value": "", "Description": ""}, {"Command": "comment", "Target": "XType // ${KEY_CTRL+KEY_A}~/Downloads/teest_file.zip${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_CTRL+KEY_A}${!cmd_var1}${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "store", "Target": "120", "Value": "!TIMEOUT_MACRO", "Description": ""}, {"Command": "store", "Target": "10", "Value": "!TIMEOUT_WAIT", "Description": ""}, {"Command": "visualSearch", "Target": "zcc_loading_screen_dpi_96.png@0.8", "Value": "found", "Description": ""}, {"Command": "while", "Target": "${found}!=0", "Value": "", "Description": ""}, {"Command": "pause", "Target": "5000", "Value": "", "Description": ""}, {"Command": "visualSearch", "Target": "zcc_loading_screen_dpi_96.png@0.8", "Value": "found", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}, {"Command": "store", "Target": "80", "Value": "!TIMEOUT_WAIT", "Description": ""}, {"Command": "store", "Target": "0", "Value": "!TIMEOUT_MACRO", "Description": ""}]}
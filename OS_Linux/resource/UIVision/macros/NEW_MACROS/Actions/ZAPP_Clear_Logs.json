{"Name": "ZAPP_Clear_Logs", "CreationDate": "2024-9-22", "Commands": [{"Command": "run", "Target": "../Open_ZAPP", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_more_icon_dpi_96.png", "Value": "", "Description": ""}, {"Command": "pause", "Target": "2000", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "zcc_clear_logs_dpi_96.png", "Value": "", "Description": ""}, {"Command": "store", "Target": "10", "Value": "!TIMEOUT_WAIT", "Description": ""}, {"Command": "store", "Target": "120", "Value": "!TIMEOUT_MACRO", "Description": ""}, {"Command": "visualSearch", "Target": "ZCC_troubleshoot_menu_dpi_96.png", "Value": "found", "Description": ""}, {"Command": "while", "Target": "${found} == 0", "Value": "", "Description": ""}, {"Command": "pause", "Target": "5000", "Value": "", "Description": ""}, {"Command": "visualSearch", "Target": "ZCC_troubleshoot_menu_dpi_96.png", "Value": "found", "Description": ""}, {"Command": "end", "Target": "", "Value": "", "Description": ""}, {"Command": "store", "Target": "80", "Value": "!TIMEOUT_WAIT", "Description": ""}, {"Command": "store", "Target": "0", "Value": "!TIMEOUT_MACRO", "Description": ""}, {"Command": "XClick", "Target": "zcc_internet_access_icon_dpi_96.png", "Value": "", "Description": ""}]}
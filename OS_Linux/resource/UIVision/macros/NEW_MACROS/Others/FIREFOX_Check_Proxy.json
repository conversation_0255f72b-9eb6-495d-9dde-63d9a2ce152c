{"Name": "FIREFOX_Check_Proxy", "CreationDate": "2024-9-22", "Commands": [{"Command": "run", "Target": "..\\Desktop_automation", "Value": "", "Description": ""}, {"Command": "comment", "Target": "store // slow", "Value": "!replayspeed", "Description": ""}, {"Command": "echo", "Target": "Opening Firefox", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_CTRL+KEY_O}", "Value": "", "Description": ""}, {"Command": "XType", "Target": "Firefox", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "comment", "Target": "XRun // /usr/bin/firefox", "Value": "", "Description": ""}, {"Command": "comment", "Target": "pause // 120000", "Value": "", "Description": ""}, {"Command": "echo", "Target": "Opening preferences in firefox", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "win_firefox_options_dpi_96.png@0.8", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "linux_preferences_dpi_96.png", "Value": "", "Description": ""}, {"Command": "store", "Target": "slow", "Value": "!replayspeed", "Description": ""}, {"Command": "XType", "Target": "Network${KEY_SPACE}Settings", "Value": "", "Description": ""}, {"Command": "echo", "Target": "Opening proxy settings in firefox", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "linux_settings_dpi_96.png", "Value": "", "Description": ""}, {"Command": "store", "Target": "medium", "Value": "!replayspeed", "Description": ""}, {"Command": "XType", "Target": "${KEY_UP}", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_UP}", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_UP}", "Value": "", "Description": ""}, {"Command": "echo", "Target": "Verifying system proxy settings is enabled", "Value": "", "Description": ""}, {"Command": "visualVerify", "Target": "linux_twlp_system_proxy_dpi_96.png", "Value": "", "Description": ""}, {"Command": "echo", "Target": "Verifying no proxy is not checked", "Value": "", "Description": ""}, {"Command": "visualVerify", "Target": "linux_twlp_proxy_dpi_96.png", "Value": "", "Description": ""}, {"Command": "echo", "Target": "Trying to click on No proxy setting", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "linux_twlp_proxy_dpi_96.png@0.7", "Value": "", "Description": ""}, {"Command": "echo", "Target": "Verifying no proxy setting is not enabled", "Value": "", "Description": ""}, {"Command": "visualAssert", "Target": "linux_twlp_proxy_2_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "echo", "Target": "Closing firefox", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "linux_close_dpi_96.png", "Value": "", "Description": ""}]}
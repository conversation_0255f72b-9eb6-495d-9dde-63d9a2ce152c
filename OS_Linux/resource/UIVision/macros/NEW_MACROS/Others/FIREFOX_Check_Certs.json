{"Name": "FIREFOX_Check_Certs", "CreationDate": "2024-9-22", "Commands": [{"Command": "run", "Target": "..\\Desktop_automation", "Value": "", "Description": ""}, {"Command": "comment", "Target": "store // slow", "Value": "!replayspeed", "Description": ""}, {"Command": "echo", "Target": "Opening Firefox", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_CTRL+KEY_O}", "Value": "", "Description": ""}, {"Command": "XType", "Target": "Firefox", "Value": "", "Description": ""}, {"Command": "XType", "Target": "${KEY_ENTER}", "Value": "", "Description": ""}, {"Command": "pause", "Target": "2000", "Value": "", "Description": ""}, {"Command": "echo", "Target": "Opening preferences in firefox", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "win_firefox_options_dpi_96.png@0.8", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "linux_preferences_dpi_96.png", "Value": "", "Description": ""}, {"Command": "store", "Target": "slow", "Value": "!replayspeed", "Description": ""}, {"Command": "XType", "Target": "Certificate", "Value": "", "Description": ""}, {"Command": "XClickRelative", "Target": "linux_firefox_certs_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "linux_cert_click_dpi_96.png", "Value": "", "Description": ""}, {"Command": "XType", "Target": "<PERSON><PERSON><PERSON>", "Value": "", "Description": ""}, {"Command": "visualAssert", "Target": "firefox_zscaler_cert_dpi_96.png", "Value": "", "Description": ""}, {"Command": "echo", "Target": "Closing firefox", "Value": "", "Description": ""}, {"Command": "XClick", "Target": "linux_close_dpi_96.png", "Value": "", "Description": ""}]}
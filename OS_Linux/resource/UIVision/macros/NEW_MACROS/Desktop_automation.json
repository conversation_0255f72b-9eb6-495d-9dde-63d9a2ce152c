{"Name": "Desktop_automation", "CreationDate": "2024-9-22", "Commands": [{"Command": "comment", "Target": "selectWindow // title=New Tab", "Value": "", "Description": ""}, {"Command": "selectWindow", "Target": "tab=CLOSEALLOTHER", "Value": "", "Description": ""}, {"Command": "XDesktopAutomation", "Target": "true", "Value": "", "Description": ""}, {"Command": "store", "Target": "80", "Value": "!TIMEOUT_WAIT", "Description": ""}, {"Command": "store", "Target": "0", "Value": "!TIMEOUT_MACRO", "Description": ""}, {"Command": "csvReadArray", "Target": "auto_values.csv", "Value": "values", "Description": ""}, {"Command": "store", "Target": "medium", "Value": "!replayspeed", "Description": ""}, {"Command": "store", "Target": "98", "Value": "!OCREngine", "Description": ""}, {"Command": "comment", "Target": "bringBrowserToForeground // ", "Value": "", "Description": ""}]}
#!/usr/bin/env python3
import sys
from os.path import exists
from os.path import expanduser

help_string = r"""
__/\\\\\\\\\\\\\\\_        
 _\////////////\\\__       
  ___________/\\\/___      
   _________/\\\/_____     
    _______/\\\/_______     Mocking Microsoft Defender ATP
     _____/\\\/_________   
      ___/\\\/___________  
       __/\\\\\\\\\\\\\\\_ 
        _\///////////////__

Keep in mind:
  It is not:  Microsoft Defender ATP
  Used for:   Dev-Test of MS-Defender posture check on Linux
  Place it:   In the path provided on MA

  TRUE        Create a file "posture" in current user's Downloads folder
  FALSE       Delete file "posture" in current user's Downloads folder
"""

# help function
def help():
    print(help_string)

def health():
    user = expanduser('~')
    file_exists = exists(user + '/Downloads/posture')
    if file_exists:
        print('true')
    else:
        print('false')

if __name__ == '__main__':
    arg = sys.argv[1:]
    if len(arg) == 0:
        help()
    elif arg[0] == 'health':
        health()
    else:
        help()



import datetime
import time
import socket
import distro
import allure
from functools import wraps
from common_lib.common.logger import Logger
from common_lib.common.constants import *


class Zcc:
    def __init__(self, log_handle=None):
        """
        Initialize the class with an optional log handle.

        Args:
            log_handle (logging.Logger, optional): An existing logger instance. Defaults to None.
        """
        # Initialize the logger if no log handle is provided
        self.logger = log_handle if log_handle else Logger.initialize_logger(log_file_name="ZCC_LIB.log", log_level="INFO")
        self.home_folder = os.path.expanduser("~")

        # Get the current distribution ID
        distro_id = distro.id()

        # Set the distribution type based on the ID
        self.distro_type = "debian"
        if distro_id in ["ubuntu", "debian"]:
            self.distro_type = "debian"
        if distro_id in ["rhel", "centos", "fedora"]:
            self.distro_type = "rhel"
        if distro_id in ["arch"]:
            self.distro_type = "arch"
        if distro_id in ["opensuse"]:
            self.distro_type = "opensuse"

    def start_chrome(self, browser_path):
        self.logger.info("Starting Chrome")
        time.sleep(10)
        # Kill any open chrome instances
        try:
            subprocess.run(["pkill", "-f", "chrome"], check=True)
            self.logger.info("Killed all chrome instances")
        except subprocess.CalledProcessError:
            self.logger.error("Failed to kill chrome")
        except:
            self.logger.error("Error during killing chrome")
        time.sleep(10)
        # Open new chrome window
        proc = subprocess.Popen(
            [browser_path, "--disable-gpu", "--disable-software-rasterizer", "https://example.com/"],
            stderr=subprocess.DEVNULL,
        )
        time.sleep(10)

    def run_macro(
        self,
        step_title: str,
        macro_name: str,
        cmd_vars: list = [],
        retry: int = 1,
    ) -> tuple[bool, str, None]:
        """
        Runs a UIVision macro in the browser.

        Args:
            step_title (str): The title of the step.
            macro_name (str): The name of the UIVision macro to run.
            cmd_vars (list): A list of command line variables to pass to macros.
            retry (int): The number of retries.

        Returns:
            A tuple containing a boolean indicating success, a string with the result message, and None.
        """

        # Step 1: Set up the browser and log directories
        with allure.step(step_title):
            log_dir = os.path.join(self.home_folder, "Downloads")

            # Check for Google Chrome installation
            if os.path.exists(r"/usr/bin/google-chrome"):
                browser_path = r"/usr/bin/google-chrome"
                self.logger.info(f"Browser path: {browser_path}")
            elif os.path.exists(r"/usr/bin/google-chrome-stable"):
                browser_path = r"/usr/bin/google-chrome-stable"
                self.logger.info(f"Browser path: {browser_path}")
            else:
                self.logger.error("Google Chrome: Not Found")
                return (False, "Google Chrome: Not Found", {})

            html_path = os.path.join(log_dir, "ui.vision.html")
            if not os.path.exists(html_path):
                self.logger.error("UI Vision Autostart HTML: Not found")
                return (False, "UI Vision Autostart HTML: Not found", {})

            # Set up the macro and log file paths
            macro = macro_name
            timeout_seconds = 360

            # Create log file
            log_name = "log_" + datetime.datetime.now().strftime("%m-%d-%Y_%H_%M_%S") + ".txt"
            log_path = os.path.join(log_dir, log_name)

            macro_path = r"file://" + html_path + r"?macro=" + macro + r"&savelog=" + log_name

            # Add command line variables to the macro path
            if len(cmd_vars) != 0 and len(cmd_vars) <= 3:
                for i in range(0, len(cmd_vars)):
                    macro_path += r"&cmd_var" + str(i + 1) + r"=" + str(cmd_vars[i])
            elif len(cmd_vars) > 3:
                self.logger.error("Command line args limit is 3")
                return (False, "Command line args limit is 3", {})
            else:
                self.logger.info("No command line args given")

            macro_path += r"&closeRPA=1&direct=1&storage=browser"

            self.logger.info("File URL used is: {}".format(macro_path))
            self.logger.info("Log file used is: {}".format(log_name))

            # Call UI vision to run the macro
            time.sleep(2)
            proc = subprocess.Popen(
                [browser_path, "--disable-gpu", "--disable-software-rasterizer", macro_path], stderr=subprocess.DEVNULL
            )

            # Check for the log file and wait for the macro to complete
            status_runtime = 0

            while not os.path.exists(log_path) and status_runtime < timeout_seconds:
                time.sleep(1)
                status_runtime += 1

            # Read the log file whether status is successfull
            if status_runtime < timeout_seconds:
                with open(log_path) as f:
                    status_text = f.readline()
                    full_log = f.read()
                    os.remove(log_path)

                if status_text.find("Status=OK") != -1:
                    # Macro completed successfully
                    self.logger.info("Macro {} completed successfully".format(macro))
                    time.sleep(2)
                    return (True, full_log, None)
                else:
                    # Macro failed but didnt complete (chrome issue) --> will retry
                    self.logger.error("Macro {} failed with error {}".format(macro, status_text))
                    if ("Cannot read properties of undefined (reading 'indexOf')" in status_text) or (" Can't find monitor target with id" in status_text):
                        self.logger.info("Retrying")
                        self.start_chrome(browser_path=browser_path)
                        return self.run_macro(step_title, macro_name, cmd_vars, retry)
                        
                    if retry == 1:
                        self.logger.info("Retrying")
                        self.start_chrome(browser_path=browser_path)
                        subprocess.run(["pkill", "-f", "ZSTray"])
                        return self.run_macro(step_title, macro_name, cmd_vars, 0)
                        
                    self.logger.error(full_log)
                    return (False, "Macro {} failed with error {}".format(macro, status_text), {})
            else:
                # Macro timed out
                self.logger.error("Macro did not complete within given time: {}".format(timeout_seconds))
                time.sleep(2)

                if retry == 1:
                    self.logger.info("Retrying")
                    self.start_chrome(browser_path=browser_path)
                    return self.run_macro(step_title, macro_name, cmd_vars, 0)
                else:
                    return (False, "Macro did not complete within given time", {})

    @allure.step("Check if VM is joined to a domain")
    def domain_join_check(self) -> tuple[bool, str, dict]:
        """
        Checks if the VM is joined to a domain.
        Returns:
            A tuple containing a boolean indicating if the VM is joined to the domain, a string message, and a dictionary containing the FQDN.
        """
        try:
            proc = subprocess.check_output("hostname --fqdn", shell=True) # Get the fully qualified domain name
            fqdn = proc.decode().strip()
            proc = subprocess.check_output("hostname --domain", shell=True) # Get the domain name
            domain_name = proc.decode().strip()

            if domain_name != "":
                self.logger.info("VM is joined to domain")
                return (True, "Joined to domain", {"fqdn": fqdn})
            else:
                self.logger.error("Not joined to domain")
                return (False, "Not joined to domain", {"fqdn": fqdn})

        except Exception as e:
            self.logger.error(f"Error: {e}")
            return (False, str(e), None)

    @allure.step("Checking logs for posture result")
    def read_posture_logs(self, posture_name: str, expected_result: bool) -> tuple[bool, str, dict]:
        """
        This function reads the logs for a given posture and checks if the posture result matches the expected result.

        Args:
            posture_name (str): The name of the posture to check.
            expected_result (bool): The expected result of the posture.

        Returns:
            tuple[bool, str, dict]: A tuple containing a boolean indicating success or failure, a string with a message, and a dictionary with additional data.
        """
        try:
            # Get the path to the log files
            path = os.path.join(self.home_folder, ".Zscaler/Logs")
            filenames = os.listdir(path)
            tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
            if len(tray_logs) == 0:
                path = os.path.join(self.home_folder, ".Zscaler/Logs", str(os.uname()[1]))
                filenames = os.listdir(path)
                tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))

            # Sort the log files in reverse order
            tray_logs.sort(reverse=True)
            last_logs = path + "/" + tray_logs[0]

            found_logs = False

            # Iterate through the log lines in reverse order
            for line in reversed(open(last_logs).readlines()):
                if posture_name in line:
                    posture_result = line.split()[-1][-1]
                    found_logs = True

                    # Compare the posture result with the expected result
                    if (posture_result == "1" and expected_result == True) or (
                        posture_result == "0" and expected_result == False
                    ):
                        self.logger.info("Posture is working as expected")
                        return (True, "Posture is working as expected", None)
                    else:
                        raise Exception("Actual posture result is different than expected result")

            # If no logs are found for the given posture name, raise an exception
            if not found_logs:
                raise Exception("Logs not found for given posture name")
        except Exception as e:
            return (False, str(e), None)  # Returning a tuple with a boolean, string and dictionary.

    @allure.step("Checking if interface is present")
    def check_interface(self, interface_to_check: str = "zcctun0", exists: bool = True) -> tuple[bool, str, dict]:
        """
        Checks if the specified interface is present or not.

        Args:
            interface_to_check: The interface to check for. Default is "zcctun0".
            exists: If True, checks for the interface to exist. If False, checks for the interface to not exist. Default is True.

        Returns:
            A tuple containing a boolean indicating the success of the operation, a string message, and a dictionary containing the output.
        """
        try:
            # Execute the resolvectl command and decode the output
            proc = subprocess.check_output("resolvectl", shell=True)
            output = proc.decode()
            self.logger.info(output)

            # Check if the interface exists or not based on the 'exists' argument
            if exists:
                if "(" + interface_to_check + ")" in output:
                    self.logger.info(interface_to_check + " interface is present")
                    return (True, interface_to_check + " interface is present", {"output": output})
                else:
                    self.logger.error("tun0 interface is not present")
                    return (False, interface_to_check + " interface is not present", {"output": output})
            else:
                if "(" + interface_to_check + ")" in output:
                    self.logger.info(interface_to_check + " interface is present")
                    return (False, interface_to_check + " interface is present", {"output": output})
                else:
                    self.logger.error("tun0 interface is not present")
                    return (True, interface_to_check + " interface is not present", {"output": output})

        except Exception as e:
            self.logger.error(f"An error occurred: {e}")
            return (False, str(e), None)

    @allure.step("Getting DNS servers")
    def get_dns_servers(self, interface_to_check: str = "") -> tuple[bool, str, dict]:
        """
        Get DNS servers for the specified interface or the default one if not specified.

        Args:
            interface_to_check: The interface to check for DNS servers. If not specified, the default interface will be used.

        Returns:
            A tuple containing a boolean indicating success or failure, an error message (if any), and a dictionary containing the DNS servers.
        """
        cmd = "resolvectl dns"
        if interface_to_check == "":
            self.logger.info("Getting DNS servers for first (default) interface")
        else:
            cmd += " " + interface_to_check

        try:
            # Execute the command and decode the output
            proc = subprocess.check_output(cmd, shell=True)
            output = proc.decode().strip()
            self.logger.info(output)

            # Extract the DNS servers from the output
            if interface_to_check == "":
                output = output.split("\n")[1].strip()
            dns_servers = output.split(":")[-1].strip().split(" ")
            self.logger.info(f"DNS: {dns_servers}")
            return (True, "Dns servers returned", {"dns_servers": dns_servers})

        except Exception as e:
            # Log the error and return a tuple with failure information
            self.logger.error(f"An error occurred: {e}")
            return (False, str(e), None)

    @allure.step("Checking proxy settings")
    def check_proxy_settings(self, applied: bool = True) -> tuple[bool, str, dict]:
        """
        Checks the proxy settings and returns a tuple with a boolean status, a message, and a dictionary containing the proxy URL.

        Args:
            applied (bool, optional): Whether the proxy settings should be applied or not. Defaults to True.

        Returns:
            tuple[bool, str, dict]: A tuple containing a boolean status, a message, and a dictionary containing the proxy URL.
        """
        # Get the http_proxy and https_proxy environment variables
        http_proxy = os.environ.get("http_proxy")
        https_proxy = os.environ.get("https_proxy")
        proxy_url = ""

        # Get the autoconfig-url from gsettings
        output = subprocess.check_output("gsettings get org.gnome.system.proxy autoconfig-url", shell=True)
        proxy_url = output.decode().strip().split("'")[1]

        # If empty, Check if both http_proxy and https_proxy are None, and set proxy_url accordingly
        if proxy_url == "":
            if http_proxy == None and https_proxy == None:
                proxy_url = ""
            elif http_proxy != None:
                proxy_url = str(http_proxy)
            else:
                proxy_url = str(https_proxy)

        self.logger.info(f"proxy_url: {proxy_url}")

        # Return the appropriate tuple based on the applied parameter and the presence of a proxy URL
        if applied:
            if proxy_url != "":
                return (True, "Proxy settings found", {"proxy_url": proxy_url})
            else:
                return (False, "No proxy settings found", None)
        else:
            if proxy_url != "":
                return (False, "Proxy settings found", {"proxy_url": proxy_url})
            else:
                return (True, "No proxy settings found", None)

    def check_zcerts_installed(self) -> tuple[bool, str, dict]:
        """
        Checks if Zscaler certs are installed on the system.

        Returns:
            tuple[bool, str, dict]: A tuple containing a boolean indicating if the certs are installed,
                                     a string with a message, and a dictionary containing additional information.
        """
        # Determine the certificate destination based on the distribution type
        if self.distro_type == "debian":
            cert_dest = "/usr/local/share/ca-certificates/"

        if self.distro_type == "rhel":
            cert_dest = "/etc/pki/ca-trust/source/anchors/"

        if self.distro_type == "arch":
            cert_dest = "/etc/ca-certificates/trust-source/anchors/"

        if self.distro_type == "opensuse":
            cert_dest = "/usr/share/pki/trust/anchors/"

        # Check for Zscaler certs in the certificate destination
        p = subprocess.check_output(f"ls {cert_dest} | grep Zscaler", shell=True)
        output = p.decode()

        # Check if Zscaler certs are installed
        if "ZscalerRoot" in output or "ZscalerCustom" in output:
            return (True, "Zscaler certs are installed on system", None)
        else:
            return (False, "Zscaler certs are not installed on system", {"output": output})

    def check_interface_ip(self, interface_to_check: str = "", is_ipv6: bool = False) -> tuple[bool, str, dict]:
        """
        Checks if a given network interface is connected and returns its status, error message, and output.

        Args:
            interface_to_check: The network interface to check.
            is_ipv6: A boolean indicating whether to check for IPv6 or IPv4.

        Returns:
            A tuple containing a boolean indicating the success of the operation, an error message, and a dictionary containing the output.
        """
        # Determine the command to run based on the IP version.
        if is_ipv6:
            cmd = f"ip -f inet6 addr show {interface_to_check}"
        else:
            cmd = f"ip -f inet addr show {interface_to_check}"

        # Check if the interface was provided.
        if interface_to_check == "":
            self.logger.error("No interface provided to check")
            return (False, "No interface provided to check", None)

        try:
            proc = subprocess.check_output(cmd, shell=True)
            output = proc.decode().strip()
            self.logger.info(output)

            # Check if the interface is connected and return the result.
            if output != "":
                return (True, f"Interface: {interface_to_check} is connected", {"output": output})
            else:
                return (False, f"Interface: {interface_to_check} is not connected")

        except Exception as e:
            self.logger.error(f"An error occurred: {e}")
            return (False, str(e), None)

    def check_traffic(
        self, host_to_connect: str = "https://example.com", via_zscaler: bool = False, only_ipv6: bool = False, proxy_url: str = "", retry: int = 1
    ) -> tuple[bool, str, dict]:
        """
        Checks the traffic to a given host.

        Args:
            host_to_connect: The host to connect to. Defaults to "https://example.com".
            via_zscaler: Whether to check if the traffic goes through Zscaler. Defaults to False.
            only_ipv6: Whether to use only IPv6. Defaults to False.
            proxy_url: The proxy URL to use. Defaults to "".

        Returns:
            A tuple containing a boolean indicating success, a string with a message, and a dictionary with additional information.
        """
        # Construct the cURL command
        command = f"curl -vsS {host_to_connect} --head --connect-timeout 30"
        if only_ipv6:
            command = command + " -6"
        
        if proxy_url != "":
            command = command + f" -x '{proxy_url}'"
        else:
            command = command + " --noproxy '*'"

        command = command + " -k 2>&1"
        
        # if via_zscaler:
        #     command = command + " | grep 'O=Zscaler Inc.; OU=Zscaler Inc.'"

        self.logger.info(f"Command: {command}")

        try:
            # Execute the cURL command and capture the output
            proc = subprocess.check_output(command, shell=True)
            output = proc.decode(errors='ignore').strip()
            self.logger.info(f"Output: {output}")

            # Check if the traffic goes through Zscaler and return the appropriate result
            if via_zscaler and 'O=Zscaler Inc' not in output:
                self.logger.error("Traffic is not going through zscaler")
                if retry > 0:
                    self.logger.info("Retrying...")
                    time.sleep(120)
                    return self.check_traffic(host_to_connect=host_to_connect, via_zscaler=via_zscaler, only_ipv6=only_ipv6, proxy_url=proxy_url, retry=retry-1)
                return (False, "Traffic is not going through zscaler", None)

            self.logger.info("Traffic was sent successfully")
            return (True, "Traffic was sent successfully", {"output": output})

        except Exception as e:
            self.logger.error(f"An error occurred: {e}")
            if retry > 0:
                self.logger.info("Retrying...")
                time.sleep(60)
                return self.check_traffic(host_to_connect=host_to_connect, via_zscaler=via_zscaler, only_ipv6=only_ipv6, proxy_url=proxy_url, retry=retry-1)
            return (False, str(e), None)

    def get_random_string(self, str_len: int = 6) -> str:
        command = f"cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w {str_len} | head -n 1"
        output = subprocess.check_output(command, shell=True)
        rand_str = output.decode().strip()
        self.logger.info(f"Random Prefix: {rand_str}")
        return rand_str

    # =================================================================================================================#
    def pulse_check_interface(self)->tuple[bool,str,dict]:
        try:
            call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
            result = str(call.communicate())
            result = result.split("\n")
            interface = 0
            dns = 0
            domain = 0
            dnsdomain = 0
            for line in result:
                if re.search("tun0",line):
                    interface = 1

            if interface :
                self.logger.info("tun0 interface present")
                return (True, "tun0 interface present", None)
            else :
                self.logger.error("tun0 interface not present")
                return (False, "tun0 interface not present", None)
        except Exception as e:
            self.logger.error(f"An error occurred: {e}")
            return (False, str(e), None)

    def calculate_func_execute_time(func):
        '''
        Decorator used for calculating time taken for execution of a function
        Flow: ------------------------------
            1. Func is passed to calculateFuncExecuteTime which calls wrapper func
            2. Wrapper func then first gets the current time(start time) and then make call to the given func as arg in calculateFuncExecuteTime
            3. After func is executed, we then get the current time(end time) and then calculate endtime-starttime and get the time difference
        '''
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            res = func(*args, **kwargs)
            end_time = time.time()
            args[0].file_transfer_time = int(end_time - start_time)
            args[0].logger.critical(f"{'*'*50}")
            args[0].logger.critical(
                f"TIME TAKEN FOR {func.__name__} : {args[0].file_transfer_time} Seconds")
            args[0].logger.critical(f"{'*'*50}")
            return res
        return wrapper

    def open_log_file(self
                    )->tuple[bool,str,None]:
        '''

        '''
        path = "/home/<USER>/.Zscaler/Logs"
        try:
            filename = os.listdir(path)
            self.logger.info(filename)
            # self.logger.info(filename)
            now = datetime.datetime.now()
            fileopen = "ZSTray_" + str(now.year)

            filename.sort(reverse=True)
            for i in filename:
                if re.search(fileopen, i) != None:
                    filetoopen = i;
                    break;
            self.logger.info(filetoopen)
            # self.logger.info(filetoopen)

            f = open(os.path.join(path, filetoopen), "r")
            text = f.readlines()
            f.close() # Close the file after reading
        except FileNotFoundError:
            return (False, "File not found", None)
        except Exception as e:
            return (False, str(e), None)

        try:
            filename = os.listdir(r"/var/log/zscaler/.Zscaler/Logs")
            self.logger.info(filename)
            # self.logger.info(filename)
            now = datetime.datetime.now()
            fileopen = "zsaservice_" + str(now.year)

            filename.sort(reverse=True)
            for i in filename:
                if re.search(fileopen, i) != None:
                    filetoopen = i;
                    break;
            self.logger.info(fileopen)
            # self.logger.info(filetoopen)

            f = open(os.path.join(path, filetoopen), "r")
            text = f.readlines()
            f.close() # Close the file after reading
        except FileNotFoundError:
            return (False, "File not found", None)
        except Exception as e:
            return (False, str(e), None)

        try:
            filename = os.listdir(r"/var/log/zscaler/.Zscaler/Logs")
            self.logger.info(filename)
            # self.logger.info(filename)
            now = datetime.datetime.now()
            fileopen = "zstunnel_" + str(now.year)

            filename.sort(reverse=True)
            for i in filename:
                if re.search(fileopen, i) != None:
                    filetoopen = i;
                    break;
            self.logger.info(fileopen)
            # self.logger.info(filetoopen)

            f = open(os.path.join(path, filetoopen), "r")
            text = f.readlines()
            f.close() # Close the file after reading 
        except FileNotFoundError:
            return (False, "File not found", None)
        except Exception as e:
            return (False, str(e), None) 

        return (True, "Files read successfully", {"ZSTray": text, "zsaservice": text, "zstunnel": text}) # Return status, message and log data in a dictionary 

    def check_log_files(self)->tuple[bool,str,None]:
        logs_dir = r"/var/log/zscaler/.Zscaler/Logs"
        try:
            if os.path.exists(logs_dir):
                files_list_before = os.listdir(logs_dir)
                files_count_before = len(files_list_before)

                self.logger.info("Number of files before clearing logs : {}".format(files_count_before))
                self.logger.info("Calling macro to clear the logs in ZApp")
                self.run_macro("Linux_Ubuntu_ZAPP_Automation_Macros/Linux_Zapp_UI_Verifications/ZAPP_Clear_Logs")
                files_list_after = os.listdir(logs_dir)
                files_count_after = len(files_list_after)
                self.logger.info("Number of files after clearing logs : {}".format(files_count_after))
                if files_count_after <= files_count_before:
                    self.logger.info("Logs were cleared properly from ZAPP clear logs")
                    return (True, "Logs were cleared", {"before": files_count_before, "after": files_count_after})
                else:
                    raise Exception("Logs were not cleared")
            else:
                raise Exception("Logs directory does not exist")
        except Exception as e:
            return (False, str(e), None)

    def read_temp_file(self) -> tuple[bool, str, None]:
        file_name = "Temporary"
        home_dir = os.path.expanduser("~")
        documents_dir = os.path.join(home_dir, "Downloads")
        file_path = os.path.join(documents_dir, file_name)

        try:
            if os.path.exists(file_path):
                with open(file_path) as temp:
                    temp_var = temp.readline()
                os.remove(file_path)
                return (True, temp_var, None) 
            else:
                raise Exception("Temporary file is not created")
        except Exception as e:
            return (False, str(e), None)  # Returning failure, exception message and empty dictionary        

    def check_traffic_bytes(self)->tuple[bool,str,None]:
        try:
            self.logger.info("Capturing the sent bytes on Zapp before sending traffic")
            self.run_macro("Linux_Ubuntu_ZAPP_Automation_Macros/Linux_OTP_And_Traffic/ZAPP_Traffic_Bytes")
            bytes_list = self.read_temp_file().split()
            if bytes_list[1][0] == "M":
                bytes_before = float(bytes_list[0]) * 1024
            elif bytes_list[1][0] == "G":
                bytes_before = float(bytes_list[0]) * 1024 * 1024
            else:
                bytes_before = float(bytes_list[0])
            self.logger.info("{} KB seen in sent bytes on ZAPP before sending traffic".format(bytes_before))
            self.logger.info("Send traffic using packet sender for 5 seconds")
            self.run_macro("Linux_Ubuntu_ZAPP_Automation_Macros/Linux_OTP_And_Traffic/Packet_Sender_Traffic")
            self.logger.info("Capturing the sent bytes on Zapp after sending traffic")
            self.run_macro("Linux_Ubuntu_ZAPP_Automation_Macros/Linux_OTP_And_Traffic/ZAPP_Traffic_Bytes")
            bytes_list_after = self.read_temp_file().split()
            if bytes_list_after[1][0] == "M":
                bytes_after = float(bytes_list_after[0]) * 1024
            elif bytes_list_after[1][0] == "G":
                bytes_after = float(bytes_list_after[0]) * 1024 * 1024
            else:
                bytes_after = float(bytes_list_after[0])
            self.logger.info("{} KB seen in sent bytes on Zapp after sending traffic".format(bytes_after))
            diff_bytes = bytes_after - bytes_before
            self.logger.info("Difference between the bytes is {}".format(diff_bytes))
            if diff_bytes > 5000:
                self.logger.info("Traffic went through the tunnel and bytes were increased")
                return (True, "Traffic went through the tunnel", {"diff": diff_bytes})
            else:
                raise Exception("Bytes were not incremented")
        except Exception as e:
            return (False, str(e), None)

    def check_pac_file(self) -> tuple[bool, str, dict]:
        try:
            file_name = "Temporary"
            home_dir = os.path.expanduser("~")
            documents_dir = os.path.join(home_dir, "Downloads")
            file_path = os.path.join(documents_dir, file_name)

            if os.path.exists(file_path):
                with open(file_path) as pac:
                    pac_url = pac.readline()
                    self.logger.info("Pac url is {}".format(pac_url))
            else:
                raise Exception("Temporary file is not created")

            os.remove(file_path)

            home_dir = os.path.expanduser("~")
            download_dir = os.path.join(home_dir, "Downloads")
            split_list = pac_url.split("/")
            pac_name = split_list.pop()
            pac_path = os.path.join(download_dir, pac_name).strip()

            if os.path.exists(pac_path):
                self.logger.info("Pac file {} is found in the location {}".format(pac_name, download_dir))
                os.remove(pac_path)
                return (True, pac_name, None)  # Added return statement with expected tuple format
            else:
                raise Exception("Pac file is not found")
        except Exception as e:
            return (False, str(e), None)  # Added return statement with expected tuple format

    def kill_chrome(self):
        os.system('ps axf | grep -E \'chrome|firefox\' | grep -v grep | awk \'{print "kill " $1 }\' | sh')

    def check_coredump(self)->tuple[bool,str,dict]:
        try:
            current_time = datetime.datetime.now()
            date_today = str(current_time.year) + "-" + str(current_time.month) + "-" + str(current_time.day)
            date_before = str(current_time.year) + "-" + str(current_time.month) + "-" + str(current_time.day - 1)

            call = subprocess.Popen("coredumpctl list --since={}".format(date_today), shell=True, stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE)
            result = str(call.communicate())

            if re.search("No", result) != None:
                self.logger.info("No coredumps found")
                return (False, "No coredumps found", None)

            else:
                result = result.split('\n')
                result = result[0].split()
                tray = 0
                tunnel = 0
                service = 0
                for line in result:
                    if re.search("ZSTray", line) != None:
                        tray = 1
                    if re.search("zstunnel", line) != None:
                        tunnel = 1
                    if re.search("zsaservice", line) != None:
                        service = 1

                if tray == 1:
                    self.logger.info("Tray dump is seen")
                if tunnel == 1:
                    self.logger.info("Tunnel dump is seen")
                if service == 1:
                    self.logger.info("Service dump is seen")

                raise Exception("Coredumps are seen")
        except Exception as e:
            return (False, str(e), None) # Return False along with the error message and an empty dictionary in case of an exception.

    def check_interface_dep(self)->tuple[bool,str,dict]:
        try:
            call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
            result = str(call.communicate())
            result = result.split("\n")
            interface = 0
            dns = 0
            domain = 0
            dnsdomain = 0
            for line in result:
                if re.search("zcctun0",line):
                    interface = 1
                if re.search("**********",line):
                    dns = 1
                if re.search("~.",line):
                    domain=1
                if re.search("shashank.com",line):
                    dnsdomain=1

            if interface :
                self.logger.info("Tun interface present")
            else :
                raise Exception("Tun interface not present")

            # Add the return statement for the function here
            return (interface, "Tun interface", {"dns": dns, "domain": domain, "dnsdomain": dnsdomain})
        except Exception as e:
            self.logger.error(f"An error occurred: {e}")
            # You can return a specific value or tuple in case of an error, for example:
            return (False, "Error", None)

    def check_dnsserver(self)->tuple[bool,str,dict]:
        try:
            call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
            result = str(call.communicate())

            result = result.split("\n")
            interface = 0
            dns = 0
            domain = 0
            dnsdomain = 0
            for line in result:
                if re.search("zcctun0",line):
                    interface = 1
                if re.search("**********",line):
                    dns = 1
                if re.search("~.",line):
                    domain=1
                if re.search("shashank.com",line):
                    dnsdomain=1

            if dns:
                self.logger.info("Dns server present")
                return (True, "Dns server present", {"interface": interface, "dns": dns, "domain": domain, "dnsdomain": dnsdomain})
            else :
                self.logger.error("Dns server not present")
                return (False, "Dns server not present", {"interface": interface, "dns": dns, "domain": domain, "dnsdomain": dnsdomain})
        except Exception as e:
            self.logger.error(f"An error occurred: {e}")
            return (False, str(e), None)

    def check_domain(self)->tuple[bool,str,dict]:
        try:
            call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
            result = str(call.communicate())

            result = result.split("\n")
            interface = 0
            dns = 0
            domain = 0
            dnsdomain = 0
            for line in result:
                if re.search("zcctun0",line):
                    interface = 1
                if re.search("**********",line):
                    dns = 1
                if re.search("~.",line):
                    domain=1
                if re.search("shashank.com",line):
                    dnsdomain=1

            if domain:
                self.logger.info("Catch all present")
                return (True, "Catch all present", {"interface": interface, "dns": dns, "domain": domain, "dnsdomain": dnsdomain})
            else :
                self.logger.error("Catch all not present")
                return (False, "Catch all not present", {"interface": interface, "dns": dns, "domain": domain, "dnsdomain": dnsdomain})
        except Exception as e:
            self.logger.error(f"Error occurred: {e}")
            return (False, str(e), None)

    def check_zpa_search_domain(self) -> tuple[bool, str, dict]:
        try:
            call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            result = str(call.communicate())

            result = result.split("\n")
            interface = 0
            dns = 0
            domain = 0
            dnsdomain = 0
            for line in result:
                if re.search("zcctun0", line):
                    interface = 1
                if re.search("**********", line):
                    dns = 1
                if re.search("~.", line):
                    domain = 1
                if re.search("shashank.com", line):
                    dnsdomain = 1

            if dnsdomain:
                self.logger.info("Dns search domain present")
                return (True, "Dns search domain present", None) # Added return statement
            else:
                raise Exception("Dns search domain not present")
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return (False, str(e), None) # Added return statement in except block

    def check_zpa_application(self)->tuple[bool,str,dict]:
        try:
            call = subprocess.Popen("resolvectl flush-caches" , shell = True , stdout=subprocess.PIPE,stderr=subprocess.PIPE)
            call = subprocess.Popen("resolvectl query abc.com", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
            result = str(call.communicate())
            result = result.split("\n")

            zpa = 0
            zcctun0=0
            for line in result:
                if re.search("100.64",line):
                    zpa=1
                if re.search("zcctun0",line):
                    zcctun0=1

            if zpa and zcctun0:
                self.logger.info("Zpa app ")
                return (True, "Zpa app found", {"zpa": zpa, "zcctun0": zcctun0})
            else :
                return (False, "Zpa application not found", {"zpa": zpa, "zcctun0": zcctun0})
        except Exception as e:
            return (False, str(e), None)

    def check_normal_application(self)->tuple[bool,str,dict]:
        try:
            call = subprocess.Popen("resolvectl flush-caches" , shell = True , stdout=subprocess.PIPE,stderr=subprocess.PIPE)
            call = subprocess.Popen("resolvectl query amazon.com", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
            result = str(call.communicate())
            result = result.split("\n")

            zpa = 0
            zcctun0=0
            for line in result:
                if re.search("100.64",line):
                    zpa=1
                if re.search("zcctun0",line):
                    zcctun0=1

            if zpa == 0 and zcctun0 == 1:
                self.logger.info("Normal Application going to zcctun0 interface")
                return (True, "Normal Application going to zcctun0 interface", None)
            else:
                return (False, "Normal Application.Request doesnot go to zcctun0 interface", None)
        except Exception as e:
            return (False, str(e), None)

    def check_pulse_application_zcc(self) -> tuple[bool, str, dict]:
        try:
            # Flush caches
            call = subprocess.Popen("resolvectl flush-caches", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            # Query corp.zscaler.com
            call = subprocess.Popen("resolvectl query corp.zscaler.com", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            result = str(call.communicate())
            result = result.split("\n")

            zpa = 0
            zcctun0 = 0
            for line in result:
                if re.search("100.64", line):
                    zpa = 1
                if re.search("zcctun0", line):
                    zcctun0 = 1

            if zpa == 0 and zcctun0 == 1:
                self.logger.info("Pulse Application going to tun0 interface")
                return (True, "Pulse Application going to tun0 interface", None)
            else:
                return (False, "Request does not go to zcctun0 interface", None)
        except Exception as e:
            return (False, str(e), None)

    def check_anyconnect_application(self)->tuple[bool,str,dict]:
        try:
            # Flush caches
            call = subprocess.Popen("resolvectl flush-caches", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            # Query ubuntu.kevaldoshi.com
            call = subprocess.Popen("resolvectl query ubuntu.kevaldoshi.com", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            result = str(call.communicate())
            result = result.split("\n")

            ciscoapp = 0
            cscotun0 = 0
            for line in result:
                if re.search("192.168.100", line):
                    cscotun0 = 1
                if re.search("cscotun0", line):
                    ciscoapp = 1

            if ciscoapp == 1 and cscotun0 == 1:
                self.logger.info("Anyconnect Application going to cscotun0 interface")
                return (True, "Anyconnect Application going to cscotun0 interface", None)
            else:
                self.logger.error("Anyconnect application does not go to cscotun0 interface")
                return (False, "Anyconnect application does not go to cscotun0 interface", None)
        except Exception as e:
            self.logger.error(f"Error: {e}")
        return (False, str(e), None)

    def anyconnect_check_interface(self)->tuple[bool,str,dict]:
        try:
            call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
            result = str(call.communicate())
            result = result.split("\n")
            interface = 0
            dns = 0
            domain = 0
            dnsdomain = 0
            for line in result:
                if re.search("cscotun0",line):
                    interface = 1

            if interface :
                self.logger.info("cscotun0 interface present")
                return (True, "cscotun0 interface present", None) # Added return statement
            else :
                self.logger.error("cscotun0 interface not present")
                return (False, "cscotun0 interface not present", None) # Added return statement
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return (False, str(e), None) # Added return statement in except block

    def pulse_check_interface(self)->tuple[bool,str,dict]:
        try:
            call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
            result = str(call.communicate())
            result = result.split("\n")
            interface = 0
            dns = 0
            domain = 0
            dnsdomain = 0
            for line in result:
                if re.search("tun0",line):
                    interface = 1

            if interface :
                self.logger.info("tun0 interface present")
                return (True, "tun0 interface present", None)
            else :
                self.logger.error("tun0 interface not present")
                return (False, "tun0 interface not present", None)
        except Exception as e:
            self.logger.error(f"An error occurred: {e}")
            return (False, str(e), None)

    def anyconnect_check_dns(self) -> tuple[bool, str, dict]:
        try:
            call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            result = str(call.communicate())
            result = result.split("\n")
            interface = 0
            dns = 0
            domain = 0
            dnsdomain = 0
            for line in result:
                if re.search("cscotun0", line):
                    interface = 1
                if re.search("**************", line):
                    dns = 1

            if interface and dns:
                self.logger.info("cscotun0 dns present")
                return (True, "cscotun0 dns present", None)
            else:
                raise Exception("cscotun0 dns not present")
        except Exception as e:
            return (False, str(e), None)

    def pulse_check_dns(self) -> tuple[bool, str, dict]:
        try:
            call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            result = str(call.communicate())
            result = result.split("\n")
            interface = 0
            dns = 0
            domain = 0
            dnsdomain = 0
            for line in result:
                if re.search("tun0", line):
                    interface = 1
                if re.search("***********", line):
                    dns = 1

            if interface and dns:
                self.logger.info("cscotun0 dns present")
                return (True, "cscotun0 dns present", None)
            else:
                raise Exception("cscotun0 dns not present")
        except Exception as e:
            return (False, str(e), None)

    def run_chrome_tab(self) -> tuple[bool, str, dict]:
        try:
            browser = r"/usr/bin/google-chrome"
            proc = subprocess.Popen([browser, "--disable-gpu", "--disable-software-rasterizer"], stderr=subprocess.DEVNULL)
            time.sleep(10)
            return (True, "Chrome tab executed successfully", None)
        except Exception as e:
            return (False, str(e), None)

    def report_step(self,
                    step_title):
        with allure.step(step_title):
            pass

    @allure.step("Checking logs for Defender Posture")  
    def read_defedner_posture_logs(self) -> tuple[bool, str, dict]:
        try:
            path = "/home/<USER>/.Zscaler/Logs"
            filenames = os.listdir(path)
            tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
            tray_logs.sort(reverse=True)
            last_logs = path + "/" + tray_logs[0]

            search_strings = ["MS_defender_posture_true", "MS_defender_posture_false"]
            to_find = search_strings.copy()
            result = {}
            defender = 0

            for line in reversed(open(last_logs).readlines()):
                for string in search_strings:
                    if string in line:
                        posture_result = line.split()[-1][-1]
                        if (posture_result == '1' and string.split('_')[-1] == 'true') or (posture_result == '0' and string.split('_')[-1] == 'false'):
                            result[string] = True
                        else:
                            result[string] = False   
                        search_strings.remove(string)
                    if len(search_strings) == 0:
                        break
            if len(search_strings) == 0:
                self.logger.info("Logs for posture Found")
            else:
                raise Exception("Logs not found")   
            if result[to_find[0]] and result[to_find[1]]:
                self.logger.info("Posture working as expected")
                return (True, "Posture working as expected", result)
            else:
                return(False,"Posture not working as expected",None)
        except Exception as e:
            return False, str(e), {}      

    @allure.step("Checking logs for Encryption Posture")      
    def read_encryption_posture_logs(self) -> tuple[bool, str, dict]:
        try:
            path = "/home/<USER>/.Zscaler/Logs"
            filenames = os.listdir(path)
            tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
            tray_logs.sort(reverse=True)
            last_logs = path + "/" + tray_logs[0]

            search_strings = ["Disk_encryption_posture_true", "Disk_encryption_posture_false"]
            to_find = search_strings.copy()
            result = {}
            defender = 0

            for line in reversed(open(last_logs).readlines()):
                for string in search_strings:
                    if string in line:
                        posture_result = line.split()[-1][-1]
                        if (posture_result == '1' and string.split('_')[-1] == 'true') or (posture_result == '0' and string.split('_')[-1] == 'false'):
                            result[string] = True
                        else:
                            result[string] = False
                        search_strings.remove(string)

                    if len(search_strings) == 0:
                        break

            if len(search_strings) == 0:
                self.logger.info("Logs for posture Found")
            else:
                raise Exception("Logs not found")

            if result[to_find[0]] and result[to_find[1]]:
                self.logger.info("Posture working as expected")
                return (True, "Posture working as expected", result ) # Added return statement here
            else:
                raise Exception("Posture not working as expected")
        except Exception as e:
            self.logger.error(f"Error occurred: {e}")
            return (False, str(e), None)  # Added return statement here  

    @allure.step("Checking logs for OS Version Posture")  
    def read_os_version_posture_logs(self)->tuple[bool,str,dict]:
        try:
            path = "/home/<USER>/.Zscaler/Logs"
            filenames = os.listdir(path)
            tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
            tray_logs.sort(reverse=True)
            last_logs = path + "/" + tray_logs[0]

            search_strings = ["OS_version_Ubuntu", "OS_version_false"]
            to_find = search_strings.copy()
            result = {}
            defender = 0

            for line in reversed(open(last_logs).readlines()):
                for string in search_strings:
                    if string in line:
                        posture_result = line.split()[-1][-1]
                        if (posture_result == '1' and string.split('_')[-1] == 'Ubuntu') or (posture_result == '0' and string.split('_')[-1] == 'false'):
                            result[string] = True
                        else:
                            result[string] = False

                        search_strings.remove(string)

                    if len(search_strings) == 0:
                        break

            if len(search_strings) == 0:
                self.logger.info("Logs for posture Found")
            else:
                raise Exception("Logs not found")

            if result[to_find[0]] and result[to_find[1]]:
                self.logger.info("Posture working as expected")
                return (True, "Posture working as expected", result)
            else:
                raise Exception("Posture not working as expected")

        except Exception as e:
            return False, str(e), {}

    @allure.step("Checking logs for Process posture")  
    def read_process_posture_logs(self)->tuple[bool,str,dict]:
        try:
            path = "/home/<USER>/.Zscaler/Logs"
            filenames = os.listdir(path)
            tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
            tray_logs.sort(reverse=True)
            last_logs = path + "/" + tray_logs[0]

            search_strings = ["Process_posture_true", "Process_posture_false"]
            to_find = search_strings.copy()
            result = {}
            defender = 0

            for line in reversed(open(last_logs).readlines()):
                for string in search_strings:
                    if string in line:
                        posture_result = line.split()[-1][-1]
                        if (posture_result == '1' and string.split('_')[-1] == 'true') or (posture_result == '0' and string.split('_')[-1] == 'false'):
                            result[string] = True
                        else:
                            result[string] = False

                        search_strings.remove(string)

                    if len(search_strings) == 0:
                        break

            if len(search_strings) == 0:
                self.logger.info("Logs for posture Found")
            else:
                raise Exception("Logs not found")

            if result[to_find[0]] and result[to_find[1]]:
                self.logger.info("Posture working as expected")
                return (True, "Posture working as expected", result)
            else:
                raise Exception("Posture not working as expected")

        except Exception as e:
            return (False, str(e), {}  )

    @allure.step("Checking logs for Cert Posture")       
    def read_cert_posture_logs(self):
        try:
            path = "/home/<USER>/.Zscaler/Logs"
            filenames = os.listdir(path)
            tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
            tray_logs.sort(reverse=True)
            last_logs = path + "/" + tray_logs[0]

            search_strings = ["root_cert_true", "intermediate_cert_true"]
            to_find = search_strings.copy()
            result = {}
            defender = 0

            for line in reversed(open(last_logs).readlines()):
                for string in search_strings:
                    if string in line:
                        posture_result = line.split()[-1][-1]
                        if (posture_result == '1' and string.split('_')[-1] == 'true') or (posture_result == '0' and string.split('_')[-1] == 'false'):
                            result[string] = True
                        else:
                            result[string] = False

                        search_strings.remove(string)

                    if len(search_strings) == 0:
                        break

            if len(search_strings) == 0:
                self.logger.info("Logs for posture Found")
            else:
                raise Exception("Logs not found")

            if result[to_find[0]] and result[to_find[1]]:
                self.logger.info("Posture working as expected")
                return (True, "Posture working as expected", result)
            else:
                raise Exception("Posture not working as expected")
        except Exception as e:
            return (False, str(e), None)  

    @allure.step("Placing CERTS in specified directories")
    def cert_posture_setup(self)->tuple[bool,str,dict]:
        try:
            client_cert_path = "/home/<USER>/Documents/CERTS/client/client-inter.pem"
            client_key_path = "/home/<USER>/Documents/CERTS/client/client-inter.key"
            cert_dest = "/home/<USER>/.Zscaler/certificates/"
            key_dest = "/home/<USER>/.Zscaler/certificates/private/"

            os.system('cp ' + client_cert_path + ' ' + cert_dest)
            time.sleep(2)
            os.system('cp ' + client_key_path + ' ' + key_dest)
            time.sleep(2)
            return True, "Certificate and Key copied successfully", {}
        except Exception as e:
            return False, str(e), {}

    def check_config_file(self) -> tuple[bool, str, dict]:
        try:
            path = "/home/<USER>/.Zscaler/"
            filenames = os.listdir(path)
            config_file = list(filter(lambda x: ".dat" in x, filenames))

            if len(config_file) == 1:
                self.logger.info("Config file exists")
                return (True, "Config file exists", None)
            else:
                raise Exception("Config file not found")
        except Exception as e:
            return False, str(e), {}  # Add return statement here if needed.  

    def remove_config_file(self) -> tuple[bool, str, None]:
        path = "/home/<USER>/.Zscaler/"
        filenames = os.listdir(path)
        config_file = list(filter(lambda x: ".dat" in x, filenames))

        try:
            self.check_config_file()
            os.system("rm " + path + config_file[0])
            time.sleep(5)

            try:
                self.check_config_file()
                self.logger.info("Config file recovered")
                return True, "Config file recovered", {}
            except:
                raise Exception("Config file not recovered")
        except Exception as e:
            return False, str(e), {}

    # if __name__ == '__main__':
    #   cert_posture_setup()

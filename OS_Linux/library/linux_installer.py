################################################################
#              DOCUMENTATION FOR Installer Module              #
#                                                              #
# AUTHOR : YASH KUSHWAHA (<EMAIL>)               #
################################################################

__author__ = "{Yash Kushwaha}"
__credits__ = ["{yash kushwaha"]
__email__ = "{<EMAIL>}"

import os
import subprocess
from common_lib.common import logger

class LinuxInstaller:
    '''
    This class contains methods for LinuxInstaller
    '''

    def __init__(self, sudo_password: str = "", log_handle=None):
        '''
        Args:
            sudo_password (str): The sudo password to be used for privileged operations. Defaults to "".
            isntaller_path (str): Installer path for ZCC
            log_handle : An existing logger to be used. Defaults to None.
        '''

        self.logger = log_handle if log_handle else logger.Logger.initialize_logger(log_file_name="LinuxInstaller.log", log_level="DEBUG")

        # Get the sudo password from environment variable if not provided
        if sudo_password == "" and "sudo_passwd" in os.environ:
            self.sudo_password = str(os.environ.get("sudo_passwd"))
        else:
            self.sudo_password = str(sudo_password)
        self.logger.debug("Sudo Password: " + self.sudo_password)

        

    def check_if_zcc_exists(self) -> bool:
        '''
        Checks if ZCC exists on the system. (if the ZSTray executable exists in the /opt/zscaler/bin directory)
        Returns:
            bool: True if ZCC exists, False otherwise.
        '''
        if os.path.exists("/opt/zscaler/bin/ZSTray"):
            self.logger.info("ZCC exists")
            return True
        else:
            self.logger.info("ZCC does not exists")
            return False
        
    def run_command(self, command: str) -> tuple[bool, str]:
        '''
        Runs the given command using subprocess.check_oput
        Returns:
            bool: True if command executes, False otherwise
            str: output of command
        '''
        # Remove the DISPLAY environment variable to prevent GUI prompts
        my_env = os.environ.copy()
        try:
            del my_env["DISPLAY"]
        except:
            self.logger.info("Cannot delete DISPLAY env-var")

        self.logger.info(f"Running command : {command}")
        try:
            proc = subprocess.check_output(command, shell=True, input=b"Y\n", env=my_env)
            output = proc.decode()
            self.logger.info(output)
            return (True, output)
        except Exception as e:
            self.logger.error(f"Command failed: {e}")
            return (False, f"Command failed: {e}")
        
    def install_zcc_linux(self, zcc_installer_path: str) -> tuple[bool, str]:
        # Get ZCC installer path
        if zcc_installer_path == "" and "zcc_installer_path" in os.environ:
            zcc_installer_path = os.environ.get("zcc_installer_path")
 
        if not os.path.exists(zcc_installer_path):
            self.logger.error(f"Check installer path/file for ZCC, path:{zcc_installer_path}")
            return (False, f"Check installer path/file for ZCC, path:{zcc_installer_path}")

        # Get ZCC installer type
        zcc_installer_extension = zcc_installer_path.split('.')[-1]
        if zcc_installer_extension == "run" or zcc_installer_extension == "deb":
            zcc_installer_type = zcc_installer_extension
        elif zcc_installer_extension == "tar":
            zcc_installer_type = "rpm"
        else:
            self.logger.error(f"Check installer type for ZCC, type: {zcc_installer_type}")
            return (False, f"Check installer type for ZCC, type: {zcc_installer_type}")
            
        self.logger.debug("Installer path: " + zcc_installer_path)
        self.logger.debug("Installer type: " + zcc_installer_type)

        if zcc_installer_type == "run":
            return self.install_zcc_run(zcc_installer_path)
        
        if zcc_installer_type == "deb":
            return self.install_zcc_deb(zcc_installer_path)
        
        if zcc_installer_type == "rpm":
            return self.install_zcc_rpm(zcc_installer_path)
        
    def uninstall_zcc_linux(self) -> tuple[bool, str]:
        # Check if installed
        if not self.check_if_zcc_exists():
            self.logger.info("ZCC not found!!! -- Already uninstalled")
            return (True, "ZCC not found!!! -- Already uninstalled")

        # Determine installation
        installation_type = ""
        ## .run
        if os.path.exists("/opt/zscaler/UninstallApplication"):
            installation_type = "run"

        ## .deb
        check_cmd = "apt list --installed"
        result = self.run_command(check_cmd)
        if result[0] and "zscaler-client" in result[1]:
            installation_type = "deb"

        ## .rpm
        check_cmd = "yum list installed"
        result = self.run_command(check_cmd)
        if result[0] and "zscaler-client" in result[1]:
            installation_type = "rpm"

        # Uninstall Command
        if installation_type == "run":
            uninstall_cmd = f'echo "{self.sudo_password}" | sudo -S /opt/zscaler/UninstallApplication'
        elif installation_type == "deb":
            uninstall_cmd = f'echo "{self.sudo_password}" | sudo -S apt-get -y purge zscaler-client'
        elif installation_type == "rpm":
            uninstall_cmd = f'echo "{self.sudo_password}" | sudo -S yum remove -y zscaler-client'
        else:
            self.logger.error("Cannot Determine installation type")
            return (False, "Cannot Determine installation type")
        
        # Uninstall ZCC
        self.logger.info(f"Unistalling ZCC with command : {uninstall_cmd}")
        result = self.run_command(uninstall_cmd)
        if result[0]:
            self.logger.info("Uninstallation completed")
        else:
            self.logger.error("Uninstallation failed")
            return (False, "Uninstallation failed")
        
        # Verify ZCC
        if not self.check_if_zcc_exists():
            self.logger.info("ZCC not found!!!")
        else:
            self.logger.error("ZCC found")
            return (False, "ZCC found")
        
        return (True, "Uninstallation completed and validted")

#============================================================================================================================================
#============================================================================================================================================

    def install_zcc_run(self, installer_path: str) -> tuple[bool, str]:
        '''
        Install ZCC using .run package
        Returns:
            bool: True if installation is successful, False other
            str: output/error
        '''
        # Command for installation
        install_cmd = f'echo "{self.sudo_password}" | sudo -S {installer_path} --mode unattended --unattendedmodeui none'
        version_check_cmd = f"{installer_path} --version"

        # Check the version of ZCC
        self.logger.info("Checking version of ZCC installer")
        result = self.run_command(version_check_cmd)
        if result[0]:
            self.logger.info(f"Installer is for ZCC version: {result[1]}")
        else:
            self.logger.error("Installer is corrupted")
            return (False, "Installer is corrupted")

        # Install ZCC
        self.logger.info(f"Installing ZCC with command : {install_cmd}")
        result = self.run_command(install_cmd)
        if result[0]:
            self.logger.info("Installation completed")
        else:
            self.logger.error("Installation failed")
            return (False, "Installation failed")
        
        # Verify ZCC
        if self.check_if_zcc_exists():
            self.logger.info("ZCC Found!!!")
        else:
            self.logger.error("Unable to find ZCC")
            return (False, "Unable to find ZCC")
        
        # Success
        return (True, "Installation completed and validted")

    def install_zcc_deb(self, installer_path: str) -> tuple[bool, str]:
        '''
        Install ZCC using .deb package
        Returns:
            bool: True if installation is successful, False other
            str: output/error
        '''

        # Command for installation
        install_cmd = f'echo "{self.sudo_password}" | sudo -S apt-get -y install --reinstall {installer_path}'

        # Install ZCC
        self.logger.info(f"Installing ZCC with command : {install_cmd}")
        result = self.run_command(install_cmd)
        if result[0]:
            self.logger.info("Installation completed")
        else:
            self.logger.error("Installation failed")
            return (False, "Installation failed")
        
        # Verify ZCC
        if self.check_if_zcc_exists():
            self.logger.info("ZCC Found!!!")
        else:
            self.logger.error("Unable to find ZCC")
            return (False, "Unable to find ZCC")
        
        # Success
        return (True, "Installation completed and validted")
    
    def install_zcc_rpm(self, installer_path: str) -> tuple[bool, str]:
        '''
        Install ZCC using .deb package
        Returns:
            bool: True if installation is successful, False other
            str: output/error
        '''

        # Extract files from TAR
        extract_cmd = f"rm -rf /tmp/automation_temp_folder && mkdir /tmp/automation_temp_folder && tar -xf {installer_path} -C /tmp/automation_temp_folder"
        self.logger.info(f"Extracting ZCC installer with command : {extract_cmd}")
        result = self.run_command(extract_cmd)
        if result[0]:
            self.logger.info("Extraction completed")
        else:
            self.logger.error("Extraction failed")
            return (False, "Extraction failed")

        # Get files
        tar_content = os.listdir("/tmp/automation_temp_folder")
        for extracted_file in tar_content:
            if extracted_file.split('.')[-1] == "rpm":
                rpm_installer = os.path.join("/tmp/automation_temp_folder", extracted_file)
            if extracted_file.split('.')[-1] == "sh":
                post_install_script_path = os.path.join("/tmp/automation_temp_folder", extracted_file)

        # Install Command
        install_cmd = f'echo "{self.sudo_password}" | sudo -S yum -y install {rpm_installer}'
        post_install_cmd = f'echo "{self.sudo_password}" | sudo -S {post_install_script_path} install_dependency_libs'

        # Install ZCC
        self.logger.info(f"Installing ZCC with command : {install_cmd}")
        result = self.run_command(install_cmd)
        if result[0]:
            self.logger.info("Installation completed")
        else:
            self.logger.error("Installation failed")
            return (False, "Installation failed")
        
        # Post Install
        self.logger.info(f"Running Post install script with command : {post_install_cmd}")
        result = self.run_command(post_install_cmd)
        if result[0]:
            self.logger.info("Script completed")
        else:
            self.logger.error("Script failed")
            return (False, "Script failed")
        
        # Verify ZCC
        if self.check_if_zcc_exists():
            self.logger.info("ZCC Found!!!")
        else:
            self.logger.error("Unable to find ZCC")
            return (False, "Unable to find ZCC")
        
        # Success
        return (True, "Installation completed and validted")
    

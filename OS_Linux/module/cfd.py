import allure
import os
import subprocess
from common_lib.common import logger


class CFD:
    def __init__(self, sudo_password: str = "", log_handle=None):
        """
        Initialize an instance of the class.

        Args:
            sudo_password (str): The sudo password to be used for privileged operations. Defaults to "".
            log_handle : An existing logger to be used. Defaults to None.
        """

        # Initialize the logger if not provided
        self.logger = log_handle if log_handle else logger.Logger.initialize_logger(log_file_name="cfd.log", log_level="DEBUG")

        # Get the sudo password from environment variable if not provided
        if sudo_password == "" and "sudo_passwd" in os.environ:
            self.sudo_password = os.environ.get("sudo_passwd")
        else:
            self.sudo_password = sudo_password
        self.logger.info("Sudo Password: " + self.sudo_password)

    def run_command(self, command: str, use_sudo: bool = False, failure_expected: bool = False) -> tuple[bool, str]:
        """
        Runs a shell command.

        Args:
            command: The command to run.
            use_sudo: If True, the command will be run with sudo.
            failure_expected: If True, the function will not log an error if the command fails.

        Returns:
            A tuple containing a boolean indicating success and the output of the command.
        """
        # If failure is expected, append "|| true" to the command to ensure it always succeeds
        if failure_expected:
            command = f"{command} || true"

        self.logger.info(f"Command: {command}")

        # If using sudo, prefix the command with the sudo password
        if use_sudo:
            command = f"echo '{self.sudo_password}' | sudo -S {command}"

        try:
            p = subprocess.check_output(command, shell=True)
        except subprocess.CalledProcessError as e:
            # Log and return error from command
            self.logger.error(f"Error while executing command: {e}")
            return (False, e)
        except Exception as e:
            # Log and retrun unexpected error
            self.logger.error(f"Error: {e}")
            return (False, e)

        # Decode and strip the output to return
        output = p.decode().strip()
        self.logger.info(f"Output: {output}")
        return (True, output)

    @allure.step("Check output of given command")
    def check_output(self, command: str, use_sudo: bool = False, failure_expected: bool = False) -> tuple[bool, str, dict]:
        """
        Checks the output of a given command.

        Args:
            self: The instance of the class.
            command (str): The command to run.
            use_sudo (bool): If True, the command will be run with sudo.
            failure_expected (bool): If True, the function will not log an error if the command fails.

        Returns:
            tuple[bool, str, dict: A tuple containing a boolean indicating success, a message, and a dictionary containing either an error or output.
        """
        result = self.run_command(command, use_sudo, failure_expected)
        if not result[0]:
            # The command failed to run
            return (False, f"Failed to run command", {"error": result[1]})

        # The command ran successfully
        return (True, f"Command ran successfully", {"output": result[1]})

    @allure.step("Get IPs from host system")
    def get_system_ips(self) -> tuple[bool, str, dict]:
        """
        Gets the IPs from the host system.

        Returns:
            Tuple[bool, str, Dict[str, List[str]]]: A tuple containing a boolean indicating success, a message, and a dictionary containing the IPs and subnets.
        """
        # Get IPs and subnets from the host system
        command = "ip a | grep inet | grep -v inet6 | grep -v 127 | grep -v 100.64"

        result = self.run_command(command)
        if not result[0]:
            return (False, f"Failed to run command", {"error": result[1]})

        # Process the command output
        output = result[1].split("\n")
        ips = [x.strip().split(" ")[1].split("/")[0] for x in output]
        subnets = [x.strip().split(" ")[1] for x in output]

        self.logger.info(f"IPs: {ips}")
        self.logger.info(f"Subnets: {subnets}")

        return (True, f"Command ran successfully", {"ips": ips, "subnets": subnets})

    @allure.step("Check CPU usage of a process")
    def check_cpu_usage(self, process_name: str, threshold: float = 10.0) -> tuple[bool, str, dict]:
        """
        Checks the CPU usage of a process.

        Args:
            process_name (str): The name of the process to check.
            threshold (float): The threshold for high CPU usage, default is 10.0.

        Returns:
            Tuple[bool, str]: A tuple containing a boolean indicating success and a message about the CPU usage.
        """
        # Run the command to get the CPU usage of the process
        command = f"ps ax -o %cpu,cmd | grep {process_name} | grep -v grep"
        result = self.run_command(command)
        if not result[0]:
            return (False, f"Failed to run command", {"error": result[1]})

        # Extract the CPU usage from the command output
        cpu_usage = float(result[2].split()[0])

        # Check if the CPU usage is under the threshold
        if cpu_usage < threshold:
            self.logger.info(f"CPU usage under threshold - {cpu_usage}")
            return (True, f"CPU usage under threshold - {cpu_usage}")
        else:
            self.logger.error(f"High CPU usage detected - {cpu_usage}")
            return (False, f"High CPU usage detected - {cpu_usage}")

    @allure.step("Add dummy netwrok interfaces")
    def add_dummy_interfaces(self, count: int = 20) -> tuple[bool, str, dict]:
        """
        Adds dummy network interfaces.

        Args:
            count (int): The number of interfaces to add. Default is 20.

        Returns:
            Tuple[bool, str]: A tuple containing a boolean indicating success and a message.
        """
        self.logger.info(f"Adding {count} interfaces")
        interface_name = "auto_dummy_"

        for i in range(count):
            # Command to add interface
            command = f"ip link add {interface_name}{i} type dummy"
            result = self.run_command(command=command, use_sudo=True)
            if not result[0]:
                self.logger.error(f"Error while adding interface {interface_name}{i}")
                return False, f"Error while adding interface {interface_name}{i}", {"error": result[1]}
            else:
                self.logger.info(f"Added interface: {interface_name}{i}")

        self.logger.info(f"Added {count} interfaces successfully")
        return True, "Added interfaces successfully"

    @allure.step("Remove dummy network interfaces")
    def remove_dummy_interfaces(self, count: int = 20) -> tuple[bool, str, dict]:
        """
        Deletes dummy network interfaces.

        Args:
            count (int): The number of interfaces to delete. Default is 20.

        Returns:
            Tuple[bool, str]: A tuple containing a boolean indicating success and a message.
        """
        self.logger.info(f"Deleting {count} interfaces")
        interface_name = "auto_dummy_"

        for i in range(count):
            # Command to delete interface
            command = f"ip link delete {interface_name}{i}"
            result = self.run_command(command=command, use_sudo=True)
            if not result[0]:
                self.logger.error(f"Error whiledeleting interface {interface_name}{i}")
                return False, f"Error while deleting interface {interface_name}{i}", {"error": result[1]}
            else:
                self.logger.info(f"Deleted interface: {interface_name}{i}")

        self.logger.info(f"Deleted {count} interfaces successfully")
        return True, "Deleted interfaces successfully"

    @allure.step("Check if a file exists")
    def check_file_exists(
        self,
        file_path: str = "~/exported_logs_automation.zip",
        delete_after_check: bool = False,
        use_sudo: bool = False,
    ) -> tuple[bool, str, dict]:
        """
        Check if a file exists.

        Args:
            file_path (str): The path of the file to check.
            delete_after_check (bool): If True, the file will be deleted after checking.
            use_sudo (bool): If True, the command will be run with sudo.

        Returns:
            Tuple[bool, str]: A tuple containing a boolean indicating if the file exists and a string describing the result.
        """
        # Check if the file exists
        if os.path.exists(file_path):
            # If the file exists and delete_after_check is True, remove the file
            if delete_after_check:
                return self.remove_file(file_path, use_sudo=use_sudo)
            self.logger.info(f"File - {file_path} exists")
            return True, f"File - {file_path} exists", None
        else:
            self.logger.error(f"File - {file_path} does not exists")
            return False, f"File - {file_path} does not exists", None

    @allure.step("Create symlink for a file")
    def create_symlink(self, org_path: str, new_path: str) -> tuple[bool, str, dict]:
        """
        Creates symlink for a given file at given path.

        Args:
            org_path (str): Original path for file.
            new_path (str): Path where symlink will be created.

        Returns:
            Tuple[bool, str]: A tuple containing a boolean indicating success and the output of the command.
        """

        # Command to create symlinks
        command = f"ln -s {org_path} {new_path}"
        result = self.run_command(command=command, use_sudo=True)
        if not result[0]:
            self.logger.error(f"Error while creating symlinks")
            return True, f"Error while creating symlinks", {"error": result[1]}
        else:
            self.logger.info(f"Symlink created successfully: {org_path} --> {new_path}")
            return True, f"Symlink created successfully: : {org_path} --> {new_path}"

    @allure.step("Remove a file")
    def remove_file(self, file_path: str, might_not_be_there: bool = False, use_sudo: bool = False) -> tuple[bool, str]:
        """
        Remove a file.

        Args:
            file_path (str): The path of the file to remove.
            might_not_be_there (bool): If True, the function will not log an error if the file does not exist.
            use_sudo (bool): If True, the command will be run with sudo.

        Returns:
            tuple[bool, str]: A tuple containing a boolean indicating success and a message about the operation.
        """
        command = f"rm {file_path}"

        # Check if file exists
        if os.path.exists(file_path):
            # Run the command to remove the file
            result = self.run_command(command=command, use_sudo=use_sudo)
            if not result[0]:
                return False, f"File - {file_path} cannot be removed -- {result[1]}"
            else:
                self.logger.info(f"File - {file_path} deleted")
                return True, f"File - {file_path} deleted"
        elif might_not_be_there:
            self.logger.info(f"File - {file_path} already deleted")
            return True, f"File - {file_path} already deleted"
        else:
            self.logger.error(f"File - {file_path} does not exists")
            return False, f"File - {file_path} does not exists"

    @allure.step("Set interface to up")
    def enable_interface(self, interface_to_enable: str) -> tuple[bool, str, dict]:
        """
        Enables a network interface.

        Args:
            interface_to_enable (str): The name of the interface to enable.

        Returns:
            tuple[bool, str, dict]: A tuple containing a boolean indicating success, a message, and a dictionary containing error information if the operation failed.
        """

        # Command to enable interface
        command = f"ip link set {interface_to_enable} up"
        result = self.run_command(command=command, use_sudo=True)
        if not result[0]:
            self.logger.error(f"Failed to enable interface: {interface_to_enable}")
            return (False, f"Failed to enable interface: {interface_to_enable}", {"error": result[1]})
        else:
            self.logger.info(f"Interface: {interface_to_enable} is now up")
            return (True, f"Interface: {interface_to_enable} is now up")

    @allure.step("Set interface to down")
    def disable_interface(self, interface_to_disable: str) -> tuple[bool, str, dict]:
        """
        Disables a network interface.

        Args:
            interface_to_disable (str): The name of the interface to disable.

        Returns:
            tuple[bool, str, dict]: A tuple containing a boolean indicating success, a message, and a dictionary containing error information if the operation failed.
        """

        # Command to disable interface
        command = f"ip link set {interface_to_disable} down"
        result = self.run_command(command=command, use_sudo=True)
        if not result[0]:
            self.logger.error(f"Failed to disable interface: {interface_to_disable}")
            return (False, f"Failed to disable interface: {interface_to_disable}", {"error": result[1]})
        else:
            self.logger.info(f"Interface: {interface_to_disable} is now down")
            return (True, f"Interface: {interface_to_disable} is now down")

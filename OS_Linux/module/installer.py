################################################################
#              DOCUMENTATION FOR Installer Module              #
#                                                              #
# AUTHOR : YASH KUSHWAHA (<EMAIL>)               #
################################################################

__author__ = "{Yash Kushwaha}"
__credits__ = ["{yash kushwaha"]
__email__ = "{<EMAIL>}"

import os
import subprocess
import distro
from common_lib.common import logger


class LinuxInstaller:
    """
    This class contains methods for LinuxInstaller
    """

    def __init__(self, sudo_password: str = "", installer_path: str = "", log_handle=None):
        """
        Args:
            sudo_password (str): The sudo password to be used for privileged operations. Defaults to "".
            isntaller_path (str): Installer path for ZCC
            log_handle : An existing logger to be used. Defaults to None.
        """

        # Initialize the logger if not provided
        self.logger = log_handle if log_handle else logger.Logger.initialize_logger(log_file_name="LinuxInstaller.log", log_level="DEBUG")

        # Get the sudo password from environment variable if not provided
        if sudo_password == "" and "sudo_passwd" in os.environ:
            self.sudo_password = os.environ.get("sudo_passwd")
        else:
            self.sudo_password = sudo_password
        self.logger.debug("Sudo Password: " + self.sudo_password)

        # Get ZCC installer path
        if installer_path == "":
            self.installer_path = os.path.join(os.getcwd(), "zscaler-linux-installer.run")
        else:
            self.installer_path = installer_path

        if not os.path.exists(self.installer_path):
            raise Exception("Check installer path/file for ZCC")

    def check_if_zcc_exists(self) -> bool:
        """
        Checks if ZCC (Zscaler Client Connector) exists on the system.

        Returns:
            bool: True if ZCC exists, False otherwise.
        """
        # Check if the ZSTray executable exists in the /opt/zscaler/bin directory
        if os.path.exists("/opt/zscaler/bin/ZSTray"):
            self.logger.info("ZCC exists")
            return True
        else:
            self.logger.info("ZCC does not exists")
            return False

    def install_zcc_run(self) -> tuple[bool, str]:
        """
        Installs ZCC (Zscaler Client Connector) from a .run file.

        Args:
            None

        Raises:
            Exception: If the installer is not a.run file.

        Returns:
            tuple[bool, str]: A tuple containing a boolean indicating whether the installation was successful and a string with the installation status.
        """
        # Check if the installer is a.run file
        if not self.installer_path.split()[-1] == "run":
            raise Exception("Installer is not a.run file")

        # Construct the command to install ZCC in unattended mode
        install_cmd = (
            'echo "' + str(self.sudo_password) + '" | sudo -S ' + self.installer_path + " --mode unattended --unattendedmodeui none"
        )
        # Construct the command to check the version of ZCC
        version_check_cmd = "" + self.installer_path + " --version"

        # Check the version of ZCC
        self.logger.info("Checking version of ZCC")
        version_check_proc = subprocess.check_output(version_check_cmd, shell=True)
        version_check_out = version_check_proc.decode()
        self.logger.info(f"Installer is for ZCC version: {version_check_out}")

        # Install ZCC
        self.logger.info(f"Installing with command : {install_cmd}")
        try:
            proc = subprocess.check_output(install_cmd, shell=True, input=b"Y\n")
            self.logger.info(proc.decode())
        except Exception as e:
            # Log and return an error if the installation fails
            self.logger.error(f"Installation failed: {e}")
            return (False, f"Installation failed: {e}")

        # Check if ZCC was installed successfully
        if self.check_if_zcc_exists():
            self.logger.info("Installation completed!!!")
        else:
            self.logger.error("Unable to find ZCC")
            return (False, "Unable to find ZCC")

        # Return a success message if the installation was successful
        return (True, "Installation Completed!!!")

    def uninstall_zcc_run(self, force_uninstall: bool = False) -> tuple[bool, str]:
        """
        Uninstalls the ZCC (Zscaler Client Connector) using the .run uninstaller.

        Args:
            force_uninstall (bool): If True, forces the uninstallation even if ZCC is not found. Defaults to False.

        Raises:
            None

        Returns:
            tuple[bool, str]: A tuple containing a boolean indicating whether the uninstallation was successful and a message describing the result.
        """
        # Check if ZCC exists before attempting to uninstall
        if not self.check_if_zcc_exists():
            self.logger.info("ZCC doesn't exist -- Already Uninstalled!")
            if not force_uninstall:
                return (True, "ZCC doesn't exist -- Already Uninstalled!")

        # Check if the uninstaller exists
        if not os.path.exists("/opt/zscaler/UninstallApplication"):
            self.logger.error("Uninstaller not found for .run")
            return (False, "Uninstaller not found for .run")

        # Construct the uninstall command
        cmd = 'echo "' + str(self.sudo_password) + '" | sudo -S /opt/zscaler/UninstallApplication'
        # Start Uninstallation
        self.logger.info(f"Uninstalling with command : {cmd}")
        my_env = os.environ.copy()
        try:
            # Remove the DISPLAY environment variable to prevent GUI prompts
            del my_env["DISPLAY"]
        except:
            self.logger.info("Cannot delete DISPLAY env-var")

        try:
            # Run the uninstall command and capture the output
            proc = subprocess.check_output(cmd, shell=True, input=b"Y\n", env=my_env)
            self.logger.info(proc.decode())
        except Exception as e:
            self.logger.error(f"Uninstallation failed: {e}")
            return (False, f"Uninstallation failed: {e}")

        # Verify that ZCC has been uninstalled
        if not self.check_if_zcc_exists():
            self.logger.info("ZCC doesn't exist -- Uninstalled!!!")
        else:
            self.logger.error("Able to find ZCC")
            return (False, "Able to find ZCC")

        # Return success if ZCC has been uninstalled
        return (True, "Uninstallation Completed!!!")

    def install_zcc_deb(self) -> tuple[bool, str]:
        """
        Installs the Zscaler Client Connector (ZCC) from a.deb file.

        Args:
            None

        Raises:
            Exception: If the installer is not a.deb file.

        Returns:
            tuple[bool, str]: A tuple containing a boolean indicating whether the installation was successful and a string describing the outcome.
        """
        # Check if the installer is a.deb file
        if not self.installer_path.split()[-1] == "deb":
            # Raise an exception if the installer is not a.deb file
            raise Exception("Installer is not a.deb file")

        # Construct the command to install the ZCC
        install_cmd = 'echo "' + str(self.sudo_password) + '" | sudo -S apt-get -y install ' + self.installer_path

        # Start Installation
        self.logger.info(f"Installing with command : {install_cmd}")
        try:
            proc = subprocess.check_output(install_cmd, shell=True, input=b"Y\n")
            self.logger.info(proc.decode())
        except Exception as e:
            self.logger.error(f"Installation failed: {e}")
            return (False, f"Installation failed: {e}")

        # Check if the ZCC exists after installation
        if self.check_if_zcc_exists():
            self.logger.info("Installation completed!!!")
        else:
            self.logger.error("Unable to find ZCC")
            return (False, "Unable to find ZCC")

        # Return success if the installation is successful
        return (True, "Installation Completed!!!")

    def uninstall_zcc_deb(self, force_uninstall: bool = False) -> tuple[bool, str]:
        """
        Uninstalls the Zscaler Client Connector (ZCC) Debian package.

        Args:
            force_uninstall (bool): If True, forces the uninstallation even if ZCC is not found. Defaults to False.

        Returns:
            tuple[bool, str]: A tuple containing a boolean indicating whether the uninstallation was successful and a message describing the result.

        Raises:
            None
        """
        # Check if ZCC exists before attempting to uninstall
        if not self.check_if_zcc_exists():
            self.logger.info("ZCC doesn't exist -- Already Uninstalled!")
            if not force_uninstall:
                return (True, "ZCC doesn't exist -- Already Uninstalled!")

        # Construct the command to check for installed packages and the uninstall command
        check_cmd = "apt list --installed"
        cmd = 'echo "' + str(self.sudo_password) + '" | sudo -S apt-get -y purge zscaler-client'

        # Start uninstallation
        proc = subprocess.check_output(check_cmd, shell=True)
        if not "zscaler-client" in proc.decode().strip():
            self.logger.error("Deb package is not installed")
            return (False, "Deb package is not installed")

        self.logger.info(f"Uninstalling with command : {cmd}")
        try:
            proc = subprocess.check_output(cmd, shell=True)
            self.logger.info(proc.decode())
        except Exception as e:
            self.logger.error(f"Uninstallation failed: {e}")
            return (False, f"Uninstallation failed: {e}")

        # Verify that ZCC has been uninstalled
        if not self.check_if_zcc_exists():
            self.logger.info("ZCC doesn't exist -- Uninstalled!!!")
        else:
            self.logger.error("Able to find ZCC")
            return (False, "Able to find ZCC")

        # If the uninstallation is successful, return a success message
        return (True, "Uninstallation Completed!!!")

    def install_zcc_rpm(self) -> tuple[bool, str]:
        """
        Installs the Zscaler Client Connector (ZCC) RPM package.

        This method first checks if the installer is a.tar file, then extracts it,
        installs the dependencies, and finally installs the ZCC RPM package.

        Args:
            None

        Raises:
            Exception: If the installer is not a.tar file.

        Returns:
            tuple[bool, str]: A tuple containing a boolean indicating whether the
                installation was successful and a string with a corresponding message.
        """

        # Check if the installer is a.tar file
        if not self.installer_path.split()[-1] == "tar":
            raise Exception("Installer is not a .tar file")

        # Commands
        zcc_version = self.installer_path.split("-")[-2]

        # Construct the command to untar the installer
        untar_cmd = "tar -xvf " + self.installer_path

        # Construct the path to the installation script
        script_path = os.path.join(os.getcwd(), "zscaler_package_installation.sh")

        # Construct the path to the ZCC RPM binary
        binary_path = os.path.join(os.getcwd(), f"zscaler-client-{zcc_version}-1.x86_64.rpm")

        # Construct the command to install dependencies
        install_dep_cmd = 'echo "' + str(self.sudo_password) + '" | sudo -S ' + script_path + " install_dependency_libs"

        # Determine the package manager based on the Linux distribution
        if distro.id() in ["rhel", "centos", "fedora"]:
            # Use yum for RHEL, CentOS, and Fedora
            install_zcc_cmd = 'echo "' + str(self.sudo_password) + '" | sudo -S yum -y install ' + binary_path
        else:
            # Use zypper for other distributions
            install_zcc_cmd = 'echo "' + str(self.sudo_password) + '" | sudo -S zypper install -y ' + binary_path

        # Untar the installer
        proc = subprocess.check_output(untar_cmd, shell=True)
        self.logger.info(proc.decode())

        # Install dependencies
        self.logger.info(f"Installing dependencies: {install_dep_cmd}")
        try:
            # Run the installation command with input to simulate user interaction
            proc = subprocess.check_output(install_dep_cmd, shell=True, input=b"Y\n")
            self.logger.info(proc.decode())
        except Exception as e:
            self.logger.error(f"Installation of dependencies failed: {e}")
            return (False, f"Installation of dependencies failed: {e}")

        # Install the ZCC RPM package
        try:
            proc = subprocess.check_output(install_zcc_cmd, shell=True, input=b"Y\n")
            self.logger.info(proc.decode())
        except Exception as e:
            self.logger.error(f"Installation of ZCC failed: {e}")
            return (False, f"Installation of ZCC failed: {e}")

        # Check if ZCC exists after installation
        if self.check_if_zcc_exists():
            self.logger.info("Installation completed!!!")
        else:
            self.logger.error("Unable to find ZCC")
            return (False, "Unable to find ZCC")

        # Return success if installation completes successfully
        return (True, "Installation Completed!!!")

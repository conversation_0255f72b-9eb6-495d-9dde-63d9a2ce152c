import allure
import os
import time
import distro
from common_lib.common import logger


class DevicePosture:
    def __init__(self, sudo_password="", log_handle=None):
        """
        Initialize an instance of the class. and set distro_type and path to certs

        Args:
            sudo_password (str): The sudo password for the system. Defaults to "".
            log_handle (logging.Logger): An optional logger handle. Defaults to None.
        """

        # Initialize logger
        self.logger = log_handle if log_handle else logger.Logger.initialize_logger(log_file_name="device_posture.log", log_level="DEBUG")

        # Get sudo password from environment variable if not provided
        if sudo_password == "" and "sudo_passwd" in os.environ:
            self.sudo_password = os.environ.get("sudo_passwd")
        else:
            self.sudo_password = sudo_password

        self.logger.info("Sudo Password: " + self.sudo_password)

        # Determine the distribution type
        self.distro_id = distro.id()
        self.distro_type = "debian"

        if self.distro_id in ["ubuntu", "debian"]:
            self.distro_type = "debian"

        if self.distro_id in ["rhel", "centos", "fedora"]:
            self.distro_type = "rhel"

        if self.distro_id in ["arch"]:
            self.distro_type = "arch"

        if self.distro_id in ["opensuse"]:
            self.distro_type = "opensuse"

        # Paths of CERTS in resources
        self.client_cert_path = os.path.join(os.getcwd(), "OS_Linux/resource/CERTS/client/client-inter.pem")
        self.client_key_path = os.path.join(os.getcwd(), "OS_Linux/resource/CERTS/client/client-inter.key")
        self.client_cert_unrelated_path = os.path.join(os.getcwd(), "OS_Linux/resource/CERTS/client_unreleated/unreleated_client_cert.pem")
        self.client_key_unrelated_path = os.path.join(os.getcwd(), "OS_Linux/resource/CERTS/client_unreleated/unreleated_client_cert.key")
        self.intermediate_ca_path = os.path.join(os.getcwd(), "OS_Linux/resource/CERTS/intermediate/intermediate.crt")
        self.root_ca_path = os.path.join(os.getcwd(), "OS_Linux/resource/CERTS/root/rootca.crt")

        # Destination path of Client CERTS in ZCC dir
        self.cert_dest = os.path.join(os.path.expanduser("~"), ".Zscaler/certificates/zcc_client.pem")
        self.key_dest = os.path.join(os.path.expanduser("~"), ".Zscaler/certificates/private/zcc_client.key")
        self.private_cert_dest = "/opt/zscaler/client_cert/zcc_client.pem"
        self.private_key_dest = "/opt/zscaler/private_key/zcc_client.key"

        # Destination path of CA Certs on system (depending on distro)
        self.private_inter_cert_dest = "/opt/zscaler/intermediate_ca/zcc_intermediate.crt"

        if self.distro_type == "debian":
            self.root_cert_dest = "/usr/local/share/ca-certificates/zcc_rootca.crt"
            self.inter_cert_dest = "/usr/local/share/ca-certificates/zcc_intermediate.crt"

        if self.distro_type == "rhel":
            self.root_cert_dest = "/etc/pki/ca-trust/source/anchors/zcc_rootca.crt"
            self.inter_cert_dest = "/etc/pki/ca-trust/source/anchors/zcc_intermediate.crt"

        if self.distro_type == "arch":
            self.root_cert_dest = "/etc/ca-certificates/trust-source/anchors/zcc_rootca.crt"
            self.inter_cert_dest = "/etc/ca-certificates/trust-source/anchors/zcc_intermediate.crt"

        if self.distro_type == "opensuse":
            self.root_cert_dest = "/usr/share/pki/trust/anchors/zcc_rootca.crt"
            self.inter_cert_dest = "/usr/share/pki/trust/anchors/zcc_intermediate.crt"

    @allure.step("Removing CA and Client CERTS")
    def reset_certs(self) -> tuple[bool, str, None]:
        """
        This function is used to remove the Certificates and reset the keystore.

        Returns:
            tuple[bool, str, None]: A tuple containing a boolean status, a string message, and None.
        """
        try:
            # Remove CERTS in all possible location
            self.logger.info("Removing Certs")
            os.system("echo '" + str(self.sudo_password) + "' | sudo -S rm " + self.private_cert_dest)
            os.system("echo '" + str(self.sudo_password) + "' | sudo -S rm " + self.private_key_dest)
            os.system("rm " + self.cert_dest)
            os.system("rm " + self.key_dest)

            os.system("echo '" + str(self.sudo_password) + "' | sudo -S rm " + self.root_cert_dest)
            os.system("echo '" + str(self.sudo_password) + "' | sudo -S rm " + self.inter_cert_dest)
            os.system("echo '" + str(self.sudo_password) + "' | sudo -S rm " + self.private_inter_cert_dest)

            # Update keystore
            self.logger.info("Resetting keystore")
            if self.distro_type in ["ubuntu", "opensuse"]:
                os.system("echo '" + str(self.sudo_password) + "' | sudo -S update-ca-certificates --fresh")
            elif self.distro_type in ["rhel", "arch"]:
                os.system("echo '" + str(self.sudo_password) + "' | sudo -S update-ca-trust extract")
            time.sleep(5)

            return True, "Certificates have been reset successfully.", None
        except Exception as e:
            self.logger.error(f"Error while resetting certificates: {e}")
            return False, f"Error while resetting certificates: {e}", None

    @allure.step("Installing CA CERTS")
    def install_ca_certs(self, private_intermediate_ca: bool = False) -> tuple[bool, str, None]:
        """
        Install CA certificates.

        Args:
            private_intermediate_ca: If True, installs the private intermediate CA.

        Returns
            tuple[bool, str, None]: A tuple containing a boolean indicating success or failure, a string with a message, and None.
        """
        try:
            # Place ROOT CA
            self.logger.info("Placing ROOT CA")
            os.system("echo '" + str(self.sudo_password) + "' | sudo -S cp " + self.root_ca_path + " " + self.root_cert_dest)

            # Place Intermediate CA
            self.logger.info("Placing Intermediate CA")
            if private_intermediate_ca:
                os.system("echo '" + str(self.sudo_password) + "' | sudo -S cp " + self.intermediate_ca_path + " " + self.private_inter_cert_dest)
            else:
                os.system("echo '" + str(self.sudo_password) + "' | sudo -S cp " + self.intermediate_ca_path + " " + self.inter_cert_dest)

            # Update keystore
            self.logger.info("Updating keystore")
            if self.distro_type in ["ubuntu", "opensuse"]:
                os.system("echo '" + str(self.sudo_password) + "' | sudo -S update-ca-certificates")
            elif self.distro_type in ["rhel", "arch"]:
                os.system("echo '" + str(self.sudo_password) + "' | sudo -S update-ca-trust")
            time.sleep(5)

            return True, "Certificates installed successfully", None
        except Exception as e:
            return False, f"Failed to install certificates: {str(e)}", None

    @allure.step("Placing CERTS in specified directories")
    def cert_posture_setup(
        self,
        non_exportable_private_key: bool = False,
        negative_case: bool = False,
    ) -> tuple[bool, str, None]:
        """
        This function is used to place the required certs in specified directories.

        Args:
            non_exportable_private_key (bool): If True, it will use non-exportable private key. Default is False.
            negative_case (bool): If True, it will use unrelated paths for certs. Default is False.

        Returns:
            tuple[bool, str, None]: A tuple containing a boolean indicating success or failure, a string with a message, and None.
        """

        # Removing any remains of last run
        self.logger.info("Remving any remains of last run")
        try:
            if non_exportable_private_key:
                os.system("echo '" + str(self.sudo_password) + "' | sudo -S rm " + self.private_cert_dest)
                os.system("echo '" + str(self.sudo_password) + "' | sudo -S rm " + self.private_key_dest)
            else:
                os.system("rm " + self.cert_dest)
                os.system("rm " + self.key_dest)
        except Exception as e:
            return False, f"Failed to remove files: {e}", None

        # Placing certs to destination paths
        self.logger.info("Placing required certs to ZCC dir")
        try:
            if non_exportable_private_key and negative_case:
                os.system("echo '" + str(self.sudo_password) + "' | sudo -S cp " + self.client_cert_unrelated_path + " " + self.private_cert_dest)
                os.system("echo '" + str(self.sudo_password) + "' | sudo -S cp " + self.client_key_unrelated_path + " " + self.private_key_dest)
                os.system("echo '" + str(self.sudo_password) + "' | sudo -S chmod 700 " + self.private_key_dest)

            elif non_exportable_private_key and not negative_case:
                os.system("echo '" + str(self.sudo_password) + "' | sudo -S cp " + self.client_cert_path + " " + self.private_cert_dest)
                os.system("echo '" + str(self.sudo_password) + "' | sudo -S cp " + self.client_key_path + " " + self.private_key_dest)
                os.system("echo '" + str(self.sudo_password) + "' | sudo -S chmod 700 " + self.private_key_dest)

            elif not non_exportable_private_key and negative_case:
                os.system("cp " + self.client_cert_unrelated_path + " " + self.cert_dest)
                os.system("cp " + self.client_key_unrelated_path + " " + self.key_dest)

            else:
                os.system("cp " + self.client_cert_path + " " + self.cert_dest)
                os.system("cp " + self.client_key_path + " " + self.key_dest)
        except Exception as e:
            return False, f"Failed to place files: {e}", None

        return True, "Successfully placed files", None  # Assuming success if no exception is raised.

import allure
import os
import subprocess
import time
from common_lib.common import logger


class IPv6:
    def __init__(self, sudo_password: str = "", log_handle=None):
        """
        Initialize an instance of the class.

        Args:
            sudo_password (str): The sudo password to be used for privileged operations. Defaults to "".
            log_handle : An existing logger to be used. Defaults to None.
        """

        # Initialize the logger if not provided
        self.logger = log_handle if log_handle else logger.Logger.initialize_logger(log_file_name="ipv6.log", log_level="DEBUG")

        # Get the sudo password from environment variable if not provided
        if sudo_password == "" and "sudo_passwd" in os.environ:
            self.sudo_password = os.environ.get("sudo_passwd")
        else:
            self.sudo_password = sudo_password
        self.logger.info("Sudo Password: " + self.sudo_password)

    def run_command(self, command: str, use_sudo: bool = False, failure_expected: bool = False) -> tuple[bool, str]:
        """
        Runs a shell command.

        Args:
            command: The command to run.
            use_sudo: If True, the command will be run with sudo.
            failure_expected: If True, the function will not log an error if the command fails.

        Returns:
            A tuple containing a boolean indicating success and the output of the command.
        """
        # If failure is expected, append "|| true" to the command to ensure it always succeeds
        if failure_expected:
            command = f"{command} || true"

        self.logger.info(f"Command: {command}")

        # If using sudo, prefix the command with the sudo password
        if use_sudo:
            command = f"echo '{self.sudo_password}' | sudo -S {command}"

        try:
            p = subprocess.check_output(command, shell=True)
        except subprocess.CalledProcessError as e:
            # Log and return error from command
            self.logger.error(f"Error while executing command: {e}")
            return (False, e)
        except Exception as e:
            # Log and retrun unexpected error
            self.logger.error(f"Error: {e}")
            return (False, e)

        # Decode and strip the output to return
        output = p.decode().strip()
        self.logger.info(f"Output: {output}")
        return (True, output)
    
    @allure.step("Set interface to up")
    def enable_interface(self, interface_to_enable: str) -> tuple[bool, str, dict]:
        """
        Enables a network interface.

        Args:
            interface_to_enable (str): The name of the interface to enable.

        Returns:
            tuple[bool, str, dict]: A tuple containing a boolean indicating success, a message, and a dictionary containing error information if the operation failed.
        """

        # Command to enable interface
        command = f"ip link set {interface_to_enable} up"
        result = self.run_command(command=command, use_sudo=True)
        if not result[0]:
            self.logger.error(f"Failed to enable interface: {interface_to_enable}")
            return (False, f"Failed to enable interface: {interface_to_enable}", {"error": result[1]})
        else:
            self.logger.info(f"Interface: {interface_to_enable} is now up")
            return (True, f"Interface: {interface_to_enable} is now up")

    @allure.step("Set interface to down")
    def disable_interface(self, interface_to_disable: str) -> tuple[bool, str, dict]:
        """
        Disables a network interface.

        Args:
            interface_to_disable (str): The name of the interface to disable.

        Returns:
            tuple[bool, str, dict]: A tuple containing a boolean indicating success, a message, and a dictionary containing error information if the operation failed.
        """

        # Command to disable interface
        command = f"ip link set {interface_to_disable} down"
        result = self.run_command(command=command, use_sudo=True)
        if not result[0]:
            self.logger.error(f"Failed to disable interface: {interface_to_disable}")
            return (False, f"Failed to disable interface: {interface_to_disable}", {"error": result[1]})
        else:
            self.logger.info(f"Interface: {interface_to_disable} is now down")
            return (True, f"Interface: {interface_to_disable} is now down")

    def check_traffic(
        self,
        host_to_connect: str = "https://example.com",
        via_zscaler: bool = False,
        only_ipv6: bool = False,
        proxy_url: str = "",
        retry: int = 1,
    ) -> tuple[bool, str, dict]:
        """
        Checks the traffic to a given host.

        Args:
            host_to_connect: The host to connect to. Defaults to "https://example.com".
            via_zscaler: Whether to check if the traffic goes through Zscaler. Defaults to True.
            only_ipv6: Whether to use only IPv6. Defaults to False.
            proxy_url: The proxy URL to use. Defaults to "".

        Returns:
            A tuple containing a boolean indicating success, a string with a message, and a dictionary with additional information.
        """
        # Construct the cURL command
        command = f"curl -vsS {host_to_connect} --head --connect-timeout 30"
        if only_ipv6:
            command = command + " -6"
        
        if proxy_url != "":
            command = command + f" -x '{proxy_url}'"
        else:
            command = command + " --noproxy '*'"

        command = command + " -k 2>&1"

        self.logger.info(f"Command: {command}")

        try:
            # Execute the cURL command and capture the output
            proc = subprocess.check_output(command, shell=True)
            output = proc.decode(errors="ignore").strip()
            self.logger.info(f"Output: {output}")

            # Check if the traffic goes through Zscaler and return the appropriate result
            if via_zscaler and "O=Zscaler Inc" not in output:
                self.logger.error("Traffic is not going through zscaler")
                if retry > 0:
                    self.logger.info("Retrying...")
                    time.sleep(120)
                    return self.check_traffic(
                        host_to_connect=host_to_connect, via_zscaler=via_zscaler, only_ipv6=only_ipv6, proxy_url=proxy_url, retry=retry - 1
                    )
                return (False, "Traffic is not going through zscaler", None)

            self.logger.info("Traffic was sent successfully")
            return (True, "Traffic was sent successfully", {"output": output})

        except Exception as e:
            self.logger.error(f"An error occurred: {e}")
            if retry > 0:
                self.logger.info("Retrying...")
                time.sleep(60)
                return self.check_traffic(
                    host_to_connect=host_to_connect, via_zscaler=via_zscaler, only_ipv6=only_ipv6, proxy_url=proxy_url, retry=retry - 1
                )
            return (False, str(e), None)

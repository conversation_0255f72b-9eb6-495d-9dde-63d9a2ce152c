import allure
import os
import subprocess
from common_lib.common import logger


class VncHandler:
    """
    A class to handle VNC sessions.

    Attributes:
        server_ip (str): The IP address of the VNC server.
        server_port (str): The port number of the VNC server.
        server_address (str): The address of the VNC server in the format "IP:Port".
        vnc_session (subprocess.Popen): The VNC session process.
        logger (logger.Logger): The logger object for logging events.
    """

    def __init__(self, server_ip: str, server_port: str, log_handle=None):
        """
        Initializes a VncHandler object.

        Args:
            server_ip (str): The IP address of the VNC server.
            server_port (str): The port number of the VNC server.
            log_handle (logger.Logger, optional): A custom logger object. Defaults to None.

        """
        self.server_ip = server_ip
        self.server_port = server_port
        self.server_address = f"{self.server_ip}:{self.server_port}"
        self.vnc_session = None

        self.logger = log_handle if log_handle else logger.Logger.initialize_logger(log_file_name="VncHandler.log", log_level="DEBUG")

    @allure.step("Starting VNC session")
    def start_session(self, extra_params: list = []) -> tuple[bool, str]:
        """
        Starts a new VNC session.

        Args:
            extra_params (list, optional): A list of extra parameters to pass to the VNC viewer command. Defaults to [].

        Returns:
            tuple[bool, str]: A tuple containing a boolean indicating whether the session was started successfully and a message describing the result.
        """
        # Construct the VNC viewer command with extra parameters
        command = ["xtigervncviewer", "Maximize=1", self.server_address] + extra_params

        try:
            self.vnc_session = subprocess.Popen(command)
            self.logger.info(f"Connected to VNC session for: {self.server_address}")
            return True, f"Connected to VNC session for: {self.server_address}"
        except Exception as e:
            self.logger.error(f"Cannot connect to {self.server_address}: {e}")
            return False, f"Cannot connect to {self.server_address}: {e}"

    @allure.step("Stopping VNC session")
    def stop_session(self) -> tuple[bool, str]:
        """
        Stops the current VNC session.

        Returns:
            tuple[bool, str]: A tuple containing a boolean indicating whether the session was stopped successfully and a message describing the result.
        """
        # Check if a VNC session is active
        if self.vnc_session and self.vnc_session.poll() is None:
            # Terminate the VNC session
            self.vnc_session.terminate()
            self.logger.info(f"Ended VNC session for {self.server_address}")
            return True, f"Ended VNC session for {self.server_address}"
        else:
            self.logger.info(f"No active VNC session for {self.server_address}")
            return False, f"No active VNC session for {self.server_address}"

    @allure.step("Checking if VNC session is up")
    def is_session_running(self) -> tuple[bool, str]:
        """
        Checks if a VNC session is currently running.

        Returns:
            tuple[bool, str]: A tuple containing a boolean indicating whether a session is running and a message describing the result.
        """
        if self.vnc_session:
            # Check if the session is still running
            if self.vnc_session.poll() is None:
                self.logger.info("VNC session is running")
                return True, "VNC session is running"
            else:
                self.logger.info("VNC session has ended")
                return False, "VNC session has ended"

        self.logger.info("VNC session has not started")
        return False, "VNC session has not started"

    @allure.step("Ensuring VNC session is up")
    def ensure_session_running(self) -> tuple[bool, str]:
        """
        Ensures a VNC session is running by starting a new session if necessary.

        Returns:
            tuple[bool, str]: A tuple containing a boolean indicating whether a session is running and a message describing the result.
        """
        # Check if a session is running
        is_running = self.is_session_running()

        if is_running[0]:
            return True, "VNC Session is running"
        else:
            # Otherwise, start a new session and return the result
            return self.start_session()

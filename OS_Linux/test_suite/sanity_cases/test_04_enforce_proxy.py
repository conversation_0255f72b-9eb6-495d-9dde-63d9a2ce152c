import os
import warnings
import allure
import conftest
from common_lib.Custom_Markers import *


class Test04EnforceProxy:
    @allure.title("Setup")
    def setup_class(self):
        self.zcc = conftest.zcc_obj
        conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")

        result = conftest.forwarding_profile_obj.create_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0], result[1]
        result = conftest.forwarding_profile_obj.edit_forwarding_profile(forwarding_profile_name="automation_linux_fp", tunnel_mode="enforce_proxy")
        assert result[0], result[1]

        result = conftest.app_profile_obj.create_app_profile(
            operating_system="Linux", policy_name="automation_linux_dp", forwarding_profile_name="automation_linux_fp"
        )
        assert result[0], result[1]

        result = conftest.service_entitlement_obj.toggle_zpa(action=True)
        assert result[0], result[1]

    # After Login
    @pytest.mark.xray("MR-8889")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.4)
    @allure.title("Test for enforce proxy after ZCC login")
    def test_enforce_proxy_zcc_login(self):
        result = conftest.zcc_obj.run_macro("Login to ZCC (ZIA + ZPA)", conftest.login_macros + "ZAPP_ZIA_ZPA_Login", conftest.login_creds)
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify enforce proxy is established", conftest.verification_macros + "ZAPP_Verify_ZIA_Enforce_Proxy")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for enforce proxy",
                conftest.verification_macros + "CHROME_Verify_No_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

    # ZIA ON/OFF
    @pytest.mark.xray("MR-8890")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.4)
    @allure.title("Test for enforce proxy after ZIA ON/OFF")
    def test_enforce_proxy_zcc_zia_on_off(self):
        result = conftest.zcc_obj.run_macro("Turn OFF ZIA for enforce proxy", conftest.action_macros + "ZAPP_ZIA_Disable_Enforce_Proxy")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZIA status is OFF after ZIA is turned OFF for enforce proxy",
            conftest.verification_macros + "ZAPP_Verify_ZIA_OFF",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Turn ON ZIA for enforce proxy", conftest.action_macros + "ZAPP_ZIA_Enable_Enforce_Proxy")
        assert result[0], result[1]

        result = conftest.zcc_obj.run_macro(
            "Verify enforce proxy is established after ZIA is turned ON for enforce proxy",
            conftest.verification_macros + "ZAPP_Verify_ZIA_Enforce_Proxy",
        )
        assert result[0], result[1]

        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for enforce proxy",
                conftest.verification_macros + "CHROME_Verify_No_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

    # ZPA ON/OFF
    @pytest.mark.xray("MR-8891")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.4)
    @allure.title("Test for enforce proxy with ZPA ON/OFF")
    def test_enforce_proxy_zcc_zpa_on_off(self):
        result = conftest.zcc_obj.run_macro("Turn OFF ZPA for enforce proxy", conftest.action_macros + "ZAPP_ZPA_Disable")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is OFF after ZPA is turned OFF for enforce proxy",
            conftest.verification_macros + "ZAPP_Verify_ZPA_OFF",
        )
        assert result[0], result[1]

        result = conftest.zcc_obj.run_macro("Turn ON ZPA for enforce proxy", conftest.action_macros + "ZAPP_ZPA_Enable")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON after ZPA is turned ON for enforce proxy",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

    # Restart Service
    @pytest.mark.xray("MR-8892")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.4)
    @allure.title("Test for enforce proxy after restarting service")
    def test_enforce_proxy_zcc_restart(self):
        result = conftest.zcc_obj.run_macro("Restart ZIA service for enforce proxy", conftest.action_macros + "ZAPP_Restart_Service")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify enforce proxy is established after restart service for enforce proxy",
            conftest.verification_macros + "ZAPP_Verify_ZIA_Enforce_Proxy",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON after restart service for enforce proxy",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for enforce proxy",
                conftest.verification_macros + "CHROME_Verify_No_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

    # Exit and Relaunch
    @pytest.mark.xray("MR-8893")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.4)
    @allure.title("Test for enforce proxy after exit and relaunch")
    def test_enforce_proxy_zcc_exit_relaunch(self):
        result = conftest.zcc_obj.run_macro("Exit ZAPP while it was logged in", conftest.action_macros + "ZAPP_Exit")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Relaunch ZAPP", "NEW_MACROS/Open_ZAPP")
        assert result[0], result[1]

        result = conftest.zcc_obj.run_macro(
            "Verify enforce proxy is established after exit relaunch for enforce proxy",
            conftest.verification_macros + "ZAPP_Verify_ZIA_Enforce_Proxy",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON after exit relaunch for enforce proxy",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for enforce proxy",
                conftest.verification_macros + "CHROME_Verify_No_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

    @allure.title("Teardown")
    def teardown_class(self):
        result = conftest.zcc_obj.run_macro("Logout From Zapp", conftest.action_macros + "ZAPP_Logout")
        assert result[0], result[1]

        result = conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        assert result[0], result[1]

        result = conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0], result[1]

import os
import warnings
import allure
import conftest
from common_lib.Custom_Markers import *


class Test06Twlp:

    @allure.title("Setup")
    def setup_class(self):
        self.zcc = conftest.zcc_obj
        conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")

        result = conftest.forwarding_profile_obj.create_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0], result[1]
        result = conftest.forwarding_profile_obj.edit_forwarding_profile(forwarding_profile_name="automation_linux_fp", tunnel_mode="twlp")
        assert result[0], result[1]

        result = conftest.app_profile_obj.create_app_profile(
            operating_system="Linux", policy_name="automation_linux_dp", forwarding_profile_name="automation_linux_fp"
        )
        assert result[0], result[1]

        result = conftest.service_entitlement_obj.toggle_zpa(action=True)
        assert result[0], result[1]

    # After Login - with Firefox checks
    @pytest.mark.xray("MR-8898")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.4)
    @allure.title("Test for TWLP after ZCC login")
    def test_twlp_zcc_login(self):
        result = conftest.zcc_obj.run_macro("Login to ZCC (ZIA + ZPA)", conftest.login_macros + "ZAPP_ZIA_ZPA_Login", conftest.login_creds)
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify ZIA status is ON", conftest.verification_macros + "ZAPP_Verify_ZIA_ON")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify TWLP is established", conftest.verification_macros + "ZAPP_Verify_ZIA_Tunnel1")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for tunnel 1 TWLP",
                conftest.verification_macros + "CHROME_Verify_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

        result = conftest.zcc_obj.run_macro(
            "Verify Firefox Certificates",
            conftest.other_macros + "FIREFOX_Check_Certs",
        )
        assert result[0], result[1]

        result = conftest.zcc_obj.run_macro(
            "Verify Firefox Proxy Settings",
            conftest.other_macros + "FIREFOX_Check_Proxy",
        )
        assert result[0], result[1]

    # Check IPV6 is accessible - after login
    @pytest.mark.xray("MR-18140")
    @add_markers("regression", "sanity", "ipSix")
    @zcc_mark_version(3.7)
    @allure.title("Test for TWLP with ipv6 traffic after ZCC login")
    def test_twlp_ipv6_after_login(self):
        result = conftest.zcc_obj.check_proxy_settings(applied=True)
        assert result[0], result[1]
        proxy_url = result[2]["proxy_url"]

        result = conftest.zcc_obj.check_traffic(
            host_to_connect="https://ipv6.google.com", proxy_url=proxy_url
        )
        assert result[0], result[1]

    # ZIA ON/OFF - with Firefox checks
    @pytest.mark.xray("MR-8899")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.4)
    @allure.title("Test for TWLP after ZIA ON/OFF")
    def test_twlp_zcc_zia_on_off(self):
        result = conftest.zcc_obj.run_macro("Turn OFF ZIA for TWLP", conftest.action_macros + "ZAPP_ZIA_Disable")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZIA status is OFF after ZIA is turned OFF for TWLP",
            conftest.verification_macros + "ZAPP_Verify_ZIA_OFF",
        )
        assert result[0], result[1]

        result = conftest.zcc_obj.run_macro("Turn ON ZIA for TWLP", conftest.action_macros + "ZAPP_ZIA_Enable")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZIA status is ON after ZIA is turned ON for TWLP",
            conftest.verification_macros + "ZAPP_Verify_ZIA_ON",
        )
        assert result[0], result[1]

        result = conftest.zcc_obj.run_macro(
            "Verify TWLP is established after ZIA is turned ON for TWLP",
            conftest.verification_macros + "ZAPP_Verify_ZIA_Tunnel1",
        )
        assert result[0], result[1]

        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for tunnel 1 TWLP",
                conftest.verification_macros + "CHROME_Verify_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

        result = conftest.zcc_obj.run_macro(
            "Verify Firefox Certificates",
            conftest.other_macros + "FIREFOX_Check_Certs",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify Firefox Proxy Settings",
            conftest.other_macros + "FIREFOX_Check_Proxy",
        )
        assert result[0], result[1]

    # Check IPV6 is accessible - after ZIA ON/OFF
    @pytest.mark.xray("MR-18141")
    @add_markers("regression", "sanity", "ipSix")
    @zcc_mark_version(3.7)
    @allure.title("Test for TWLP with ipv6 traffic after ZCC ON/OFF")
    def test_twlp_ipv6_after_zia_on_off(self):
        result = conftest.zcc_obj.check_proxy_settings(applied=True)
        assert result[0], result[1]
        proxy_url = result[2]["proxy_url"]

        result = conftest.zcc_obj.check_traffic(
            host_to_connect="https://ipv6.google.com", proxy_url=proxy_url
        )
        assert result[0], result[1]

    # ZPA ON/OFF
    @pytest.mark.xray("MR-8900")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.4)
    @allure.title("Test for TWLP with ZPA ON/OFF")
    def test_twlp_zcc_zpa_on_off(self):
        result = conftest.zcc_obj.run_macro("Turn OFF ZPA for TWLP", conftest.action_macros + "ZAPP_ZPA_Disable")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is OFF after ZPA is turned OFF for TWLP",
            conftest.verification_macros + "ZAPP_Verify_ZPA_OFF",
        )
        assert result[0], result[1]

        result = conftest.zcc_obj.run_macro("Turn ON ZPA for TWLP", conftest.action_macros + "ZAPP_ZPA_Enable")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON after ZPA is turned ON for TWLP",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

    # Restart Service - with Firefox checks
    @pytest.mark.xray("MR-8901")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.4)
    @allure.title("Test for TWLP after restarting service")
    def test_twlp_zcc_restart(self):
        result = conftest.zcc_obj.run_macro("Restart ZIA service for TWLP", conftest.action_macros + "ZAPP_Restart_Service")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZIA status is ON after restart service for TWLP",
            conftest.verification_macros + "ZAPP_Verify_ZIA_ON",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify TWLP is established after restart service for TWLP",
            conftest.verification_macros + "ZAPP_Verify_ZIA_Tunnel1",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON after restart service for TWLP",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for tunnel 1 TWLP",
                conftest.verification_macros + "CHROME_Verify_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

        result = conftest.zcc_obj.run_macro(
            "Verify Firefox Certificates",
            conftest.other_macros + "FIREFOX_Check_Certs",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify Firefox Proxy Settings",
            conftest.other_macros + "FIREFOX_Check_Proxy",
        )
        assert result[0], result[1]

    # Check IPV6 is accessible - after restart service
    @pytest.mark.xray("MR-18142")
    @add_markers("regression", "sanity", "ipSix")
    @zcc_mark_version(3.7)
    @allure.title("Test for TWLP with ipv6 traffic after restart service")
    def test_twlp_ipv6_after_restart(self):
        result = conftest.zcc_obj.check_proxy_settings(applied=True)
        assert result[0], result[1]
        proxy_url = result[2]["proxy_url"]

        result = conftest.zcc_obj.check_traffic(
            host_to_connect="https://ipv6.google.com", proxy_url=proxy_url
        )
        assert result[0], result[1]

    # Exit and Relaunch - with Firefox checks
    @pytest.mark.xray("MR-8902")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.4)
    @allure.title("Test for TWLP after exit and relaunch")
    def test_twlp_zcc_exit_relaunch(self):
        result = conftest.zcc_obj.run_macro("Exit ZAPP while it was logged in", conftest.action_macros + "ZAPP_Exit")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Relaunch ZAPP", "NEW_MACROS/Open_ZAPP")
        assert result[0], result[1]

        result = conftest.zcc_obj.run_macro(
            "Verify ZIA status is ON after exit relaunch for TWLP",
            conftest.verification_macros + "ZAPP_Verify_ZIA_ON",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify TWLP is established after exit relaunch for TWLP",
            conftest.verification_macros + "ZAPP_Verify_ZIA_Tunnel1",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON after exit relaunch for TWLP",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for tunnel 1 TWLP",
                conftest.verification_macros + "CHROME_Verify_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

        result = conftest.zcc_obj.run_macro, (
            "Verify Firefox Certificates",
            conftest.other_macros + "FIREFOX_Check_Certs",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify Firefox Proxy Settings",
            conftest.other_macros + "FIREFOX_Check_Proxy",
        )
        assert result[0], result[1]

    # Check IPV6 is accessible - after exit
    @pytest.mark.xray("MR-18143")
    @add_markers("regression", "sanity", "ipSix")
    @zcc_mark_version(3.7)
    @allure.title("Test for TWLP with ipv6 traffic after exit")
    def test_twlp_ipv6_after_exit(self):
        result = conftest.zcc_obj.check_proxy_settings(applied=True)
        assert result[0], result[1]
        proxy_url = result[2]["proxy_url"]

        result = conftest.zcc_obj.check_traffic(
            host_to_connect="https://ipv6.google.com", proxy_url=proxy_url
        )
        assert result[0], result[1]

    @allure.title("Teardown")
    def teardown_class(self):
        result = conftest.zcc_obj.run_macro("Logout From Zapp", conftest.action_macros + "ZAPP_Logout")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Exit ZAPP while it was logged in", conftest.action_macros + "ZAPP_Exit")
        assert result[0], result[1]

        result = conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        assert result[0], result[1]

        result = conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0], result[1]

import os
import warnings
import allure
import base64
import time
import conftest
from common_lib.Custom_Markers import *


class Test07PosturesC2C:

    cert_path = os.path.join(os.getcwd(), "OS_Linux/resource/CERTS/intermediate/intermediate.pem")
    cert_content = open(cert_path, "rb").read()
    cert_encoded = base64.b64encode(cert_content).decode("utf-8")
    posture_name_temp = "auto_posture_linux_"

    @allure.title("Setup")
    def setup_class(self):
        self.zcc = conftest.zcc_obj
        self.postures = conftest.postures_obj

        self.postures.reset_certs()
        self.postures.install_ca_certs()
        self.postures.cert_posture_setup()
        conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")

        result = conftest.forwarding_profile_obj.create_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0], result[1]
        result = conftest.forwarding_profile_obj.edit_forwarding_profile(
            forwarding_profile_name="automation_linux_fp", tunnel_mode="tunnel_1", drop_ip_v6=True
        )
        assert result[0], result[1]

        result = conftest.app_profile_obj.create_app_profile(
            operating_system="Linux", policy_name="automation_linux_dp", forwarding_profile_name="automation_linux_fp"
        )
        assert result[0], result[1]

        result = conftest.service_entitlement_obj.toggle_zpa(action=True)
        assert result[0], result[1]

    # After Login
    @pytest.mark.xray("MR-8903")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.4)
    @allure.title("Test for tunnel 1 after ZCC login")
    def test_tunnel1_zcc_login(self):
        result = conftest.zcc_obj.run_macro("Login to ZCC (ZIA + ZPA)", conftest.login_macros + "ZAPP_ZIA_ZPA_Login", conftest.login_creds)
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify ZIA status is ON", conftest.verification_macros + "ZAPP_Verify_ZIA_ON")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify tunnel 1 is established", conftest.verification_macros + "ZAPP_Verify_ZIA_Tunnel1"
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for tunnel 1",
                conftest.verification_macros + "CHROME_Verify_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

    # Check IPV6 is not accessible
    @pytest.mark.xray("MR-18144")
    @add_markers("regression", "sanity", "ipSix")
    @zcc_mark_version(3.7)
    @allure.title("Test for tunnel 1 with ipv6 traffic (drop ipv6)")
    def test_tunnel1_drop_ipv6(self):
        # ipv6 via zscaler - dropped
        result = conftest.zcc_obj.check_traffic(host_to_connect="https://example.com", via_zscaler=True, only_ipv6=True)
        assert not result[0], result[1]

        # ipv4 via zscaler - not dropped
        result = conftest.zcc_obj.check_traffic(host_to_connect="https://example.com", via_zscaler=True, only_ipv6=False)
        assert result[0], result[1]

    @pytest.mark.xray("MR-8904")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.4)
    @allure.title("Check Logs for Cert Posture Results")
    def test_cert_posture_logs(self):
        posture_name = self.posture_name_temp + "client_cert"

        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)

        result = conftest.device_posture_obj.create_client_certificate_dp(
            cert_to_be_uploaded=self.cert_encoded, linux=True, posture_name=posture_name, cert_path=self.cert_path
        )
        assert result[0], result[1]
        time.sleep(4)

        result = conftest.zcc_obj.run_macro("Updating Policy", conftest.action_macros + "ZAPP_Update_Policy")
        assert result[0], result[1]
        time.sleep(4)
        result = conftest.zcc_obj.run_macro("Clearing Logs", conftest.action_macros + "ZAPP_Clear_Logs")
        assert result[0], result[1]
        time.sleep(4)
        result = conftest.zcc_obj.read_posture_logs(posture_name=posture_name, expected_result=True)
        assert result[0], result[1]
        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)

    @pytest.mark.xray("MR-8905")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.4)
    @allure.title("Check Logs for Defender Posture Results")
    def test_defender_posture_logs(self):
        posture_name = self.posture_name_temp + "ms_defender"

        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)

        result = conftest.device_posture_obj.create_detect_other_software_dp(
            to_be_detected="msdefender", process_path="/usr/bin/mdatp", linux=True, posture_name=posture_name
        )
        assert result[0], result[1]
        time.sleep(4)

        result = conftest.zcc_obj.run_macro("Updating Policy", conftest.action_macros + "ZAPP_Update_Policy")
        assert result[0], result[1]
        time.sleep(4)
        result = conftest.zcc_obj.run_macro("Clearing Logs", conftest.action_macros + "ZAPP_Clear_Logs")
        assert result[0], result[1]
        time.sleep(4)
        result = conftest.zcc_obj.read_posture_logs(posture_name=posture_name, expected_result=True)
        assert result[0], result[1]
        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)

    @pytest.mark.xray("MR-8906")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.4)
    @allure.title("Check Logs for Encryption Posture Results")
    def test_encryption_posture_logs(self):
        posture_name = self.posture_name_temp + "disk_enc"

        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)

        result = conftest.device_posture_obj.create_disk_enc_dp(file_path_value="/", linux=True, posture_name=posture_name)
        assert result[0], result[1]
        time.sleep(4)

        result = conftest.zcc_obj.run_macro("Updating Policy", conftest.action_macros + "ZAPP_Update_Policy")
        assert result[0], result[1]
        time.sleep(4)
        result = conftest.zcc_obj.run_macro("Clearing Logs", conftest.action_macros + "ZAPP_Clear_Logs")
        assert result[0], result[1]
        time.sleep(4)
        result = conftest.zcc_obj.read_posture_logs(posture_name=posture_name, expected_result=True)
        assert result[0], result[1]
        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)

    @pytest.mark.xray("MR-8907")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.4)
    @allure.title("Check Logs for OS Version Posture Results")
    def test_os_version_posture_logs(self):
        posture_name = self.posture_name_temp + "os_version"

        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)

        result = conftest.device_posture_obj.create_os_version_posture(
            ubuntu="22.04", fedora="35", centos="9", opensuse="15", rhel="8", posture_name=posture_name
        )
        assert result[0], result[1]
        time.sleep(4)

        result = conftest.zcc_obj.run_macro("Updating Policy", conftest.action_macros + "ZAPP_Update_Policy")
        assert result[0], result[1]
        time.sleep(4)
        result = conftest.zcc_obj.run_macro("Clearing Logs", conftest.action_macros + "ZAPP_Clear_Logs")
        assert result[0], result[1]
        time.sleep(4)
        result = conftest.zcc_obj.read_posture_logs(posture_name=posture_name, expected_result=True)
        assert result[0], result[1]
        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)

    @pytest.mark.xray("MR-8908")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.4)
    @allure.title("Check Logs for Process Posture Results")
    def test_process_posture_logs(self):
        posture_name = self.posture_name_temp + "process_check"

        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)

        result = conftest.device_posture_obj.create_process_check_dp(
            posture_name=posture_name,
            linux=True,
            operating_system="Linux",
            process_path="/opt/zscaler/bin/ZSTray",
        )
        assert result[0], result[1]
        time.sleep(4)

        result = conftest.zcc_obj.run_macro("Updating Policy", conftest.action_macros + "ZAPP_Update_Policy")
        assert result[0], result[1]
        time.sleep(4)
        result = conftest.zcc_obj.run_macro("Clearing Logs", conftest.action_macros + "ZAPP_Clear_Logs")
        assert result[0], result[1]
        time.sleep(4)
        result = conftest.zcc_obj.read_posture_logs(posture_name=posture_name, expected_result=True)
        assert result[0], result[1]
        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)

    @pytest.mark.xray("MR-8909")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.4)
    @allure.title("Check ZPA Client Name and Domain (C2C)")
    def test_zpa_clientname_c2c(self):
        result = conftest.zcc_obj.domain_join_check()
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify Hostname is shown in ZPA", conftest.verification_macros + "ZAPP_Verify_ZPA_Hostname"
        )
        assert result[0], result[1]

    @allure.title("Teardown")
    def teardown_class(self):
        result = conftest.zcc_obj.run_macro("Logout From Zapp", conftest.action_macros + "ZAPP_Logout")
        assert result[0], result[1]

        result = conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        assert result[0], result[1]

        result = conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0], result[1]

        conftest.device_posture_obj.delete_posture(posture_name=self.posture_name_temp + "client_cert")
        conftest.device_posture_obj.delete_posture(posture_name=self.posture_name_temp + "ms_defender")
        conftest.device_posture_obj.delete_posture(posture_name=self.posture_name_temp + "disk_enc")
        conftest.device_posture_obj.delete_posture(posture_name=self.posture_name_temp + "os_version")
        conftest.device_posture_obj.delete_posture(posture_name=self.posture_name_temp + "process_check")


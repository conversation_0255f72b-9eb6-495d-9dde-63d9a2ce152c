import os
import time
import warnings
import allure
import conftest
from common_lib.Custom_Markers import *


class Test06NetworkChange:

    @allure.title("Setup")
    def setup_class(self):
        self.current_interface = "ens160"
        self.zcc = conftest.zcc_obj
        conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")

        result = conftest.forwarding_profile_obj.create_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0], result[1]
        result = conftest.forwarding_profile_obj.edit_forwarding_profile(
            forwarding_profile_name="automation_linux_fp", tunnel_mode="tunnel1"
        )
        assert result[0], result[1]

        result = conftest.app_profile_obj.create_app_profile(
            operating_system="Linux", policy_name="automation_linux_dp", forwarding_profile_name="automation_linux_fp"
        )
        assert result[0], result[1]

        result = conftest.service_entitlement_obj.toggle_zpa(action=True)
        assert result[0], result[1]

    # After Login
    @pytest.mark.xray("MR-18134")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Test for Tunnel1 after ZCC login")
    def test_t1_zcc_login(self):
        result = conftest.zcc_obj.run_macro("Login to ZCC (ZIA + ZPA)", conftest.login_macros + "ZAPP_ZIA_ZPA_Login", conftest.login_creds)
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify ZIA status is ON", conftest.verification_macros + "ZAPP_Verify_ZIA_ON")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify T1 is established", conftest.verification_macros + "ZAPP_Verify_ZIA_Tunnel1")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for tunnel 1",
                conftest.verification_macros + "CHROME_Verify_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

    # Network OFF
    @pytest.mark.xray("MR-18135")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Test for T1 with Network OFF")
    def test_t1_zcc_zia_network_off(self):
        result = conftest.cfd_obj.disable_interface(interface_to_disable=self.current_interface)
        assert result[0], result[1]
        time.sleep(30)

        result = conftest.cfd_obj.check_output("curl example.com --max-time 10")
        result = conftest.zcc_obj.run_macro(
            "Verify ZIA status is not ON after Network is down",
            conftest.verification_macros + "ZAPP_Verify_ZIA_ON",
        )
        assert not result[0], result[1]

    # Network ON
    @pytest.mark.xray("MR-18133")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Test for T1 with Network ON")
    def test_t1_zcc_zia_network_off(self):
        result = conftest.cfd_obj.enable_interface(interface_to_enable=self.current_interface)
        assert result[0], result[1]
        time.sleep(60)

        result = conftest.cfd_obj.check_output("curl example.com --max-time 10")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Restart ZIA service for TWLP", conftest.action_macros + "ZAPP_Restart_Service")
        assert result[0], result[1]

        result = conftest.zcc_obj.run_macro(
            "Verify ZIA status is not ON after Network is down",
            conftest.verification_macros + "ZAPP_Verify_ZIA_ON",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for tunnel 1 TWLP",
                conftest.verification_macros + "CHROME_Verify_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

    @allure.title("Teardown")
    def teardown_class(self):
        result = conftest.zcc_obj.run_macro("Logout From Zapp", conftest.action_macros + "ZAPP_Logout")
        assert result[0], result[1]

        result = conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        assert result[0], result[1]

        result = conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0], result[1]

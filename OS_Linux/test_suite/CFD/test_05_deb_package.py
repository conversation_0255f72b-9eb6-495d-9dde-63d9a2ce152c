import os
import subprocess
from unittest import result
import warnings
import allure
import base64
import time
import ipaddress
import conftest
from common_lib.Custom_Markers import *


class Test02Misc:

    deb_installer_path = os.path.join(os.getcwd(), "zscaler-client.deb")

    @allure.title("Setup")
    def setup_class(self):
        self.zcc = conftest.zcc_obj
        self.postures = conftest.postures_obj

        self.postures.reset_certs()
        self.postures.install_ca_certs()
        self.postures.cert_posture_setup(non_exportable_private_key=True)
        conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")

        result = conftest.forwarding_profile_obj.create_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0], result[1]
        result = conftest.forwarding_profile_obj.edit_forwarding_profile(
            forwarding_profile_name="automation_linux_fp", tunnel_mode="tunnel_1"
        )
        assert result[0], result[1]

        result = conftest.app_profile_obj.create_app_profile(
            operating_system="Linux", policy_name="automation_linux_dp", forwarding_profile_name="automation_linux_fp"
        )
        assert result[0], result[1]
        result = conftest.app_profile_obj.edit_app_profile(
            operating_system="Linux", policy_name="automation_linux_dp", forwarding_profile_name="automation_linux_fp"
        )
        assert result[0], result[1]

        result = conftest.service_entitlement_obj.toggle_zpa(action=True)
        assert result[0], result[1]

    @pytest.mark.xray("MR-17221")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Uninstall ZCC and Install Deb package")
    def test_deb_installation(self):
        result = conftest.cfd_obj.check_output("/opt/zscaler/UninstallApplication", use_sudo=True)
        # assert result[0], result[1]

        # Install Deb and check mimetype error in installation
        result = conftest.cfd_obj.check_output(f"apt install {self.deb_installer_path}", use_sudo=True)
        assert result[0], result[1]

        output = result[2]["output"]
        if "Root element <mime-type> is not <mime-info> (in '/usr/share/mime/packages/zstray.xml')" in output:
            assert False, "Mimetype errors found in deb package installation"
        else:
            assert True

    @pytest.mark.xray("MR-17221")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Check mime errors")
    def test_mimetype_errors(self):
        # Check mimetype fix in xml
        result = conftest.cfd_obj.check_output(
            "cat /var/lib/dpkg/info/zscaler-client.list | grep '/usr/share/mime/packages/zstray.xml'", failure_expected=True
        )
        assert result[0], result[1]

        output = result[2]["output"]
        if len(output) == 0:
            assert False, "Fix in not present in this build"
        else:
            assert True

        result = conftest.cfd_obj.check_output(
            "cat /usr/share/mime/packages/zstray.xml | grep '<glob pattern=\"ZSTray.desktop\"'", failure_expected=True
        )
        assert result[0], result[1]

        output = result[2]["output"]
        if len(output) == 0:
            assert False, "Fix in not present in this build"
        else:
            assert True

    @pytest.mark.xray("MR-17221")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Check Owner of ZCC dir")
    def test_zcc_owner(self):
        result = conftest.cfd_obj.check_output("ls -l /opt/zscaler")
        assert result[0], result[1]

        output = result[2]["output"].split("\n")
        for single_line in output:
            if single_line.split(" ")[0] == "total":
                continue

            if single_line.split(" ")[2] != "root":
                assert False, f"Root is not the owner of ZCC dir - {single_line}"

        assert True

    @pytest.mark.xray("MR-17221")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Check Lintian parse errors")
    def test_lintian_parse(self):
        result = conftest.cfd_obj.check_output(f"lintian --check {self.deb_installer_path}")
        assert result[0], result[1]

import os
import subprocess
from unittest import result
import warnings
import allure
import base64
import time
import ipaddress
import conftest
from common_lib.Custom_Markers import *


class Test02Misc:

    cert_path = os.path.join(os.getcwd(), "OS_Linux/resource/CERTS/intermediate/intermediate1.pem")
    cert_content = open(cert_path, "rb").read()
    cert_encoded = base64.b64encode(cert_content).decode("utf-8")
    posture_name_temp = "auto_posture_linux_"

    @allure.title("Setup")
    def setup_class(self):
        self.zcc = conftest.zcc_obj
        self.postures = conftest.postures_obj

        self.postures.reset_certs()
        self.postures.install_ca_certs()
        self.postures.cert_posture_setup(non_exportable_private_key=True)
        conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")

        result = conftest.forwarding_profile_obj.create_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0], result[1]
        result = conftest.forwarding_profile_obj.edit_forwarding_profile(
            forwarding_profile_name="automation_linux_fp", tunnel_mode="tunnel_1"
        )
        assert result[0], result[1]

        result = conftest.app_profile_obj.create_app_profile(
            operating_system="Linux", policy_name="automation_linux_dp", forwarding_profile_name="automation_linux_fp"
        )
        assert result[0], result[1]
        result = conftest.app_profile_obj.edit_app_profile(
            operating_system="Linux", policy_name="automation_linux_dp", forwarding_profile_name="automation_linux_fp"
        )
        assert result[0], result[1]

        result = conftest.service_entitlement_obj.toggle_zpa(action=True)
        assert result[0], result[1]

        result = conftest.cfd_obj.remove_dummy_interfaces(20)
        result = conftest.cfd_obj.add_dummy_interfaces(20)
        assert result[0], result[1]

    # After Login
    @pytest.mark.xray("MR-17196")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Test for tunnel 1 after ZCC login")
    def test_tunnel1_zcc_login(self):
        result = conftest.zcc_obj.run_macro("Login to ZCC (ZIA + ZPA)", conftest.login_macros + "ZAPP_ZIA_ZPA_Login", conftest.login_creds)
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify ZIA status is ON", conftest.verification_macros + "ZAPP_Verify_ZIA_ON")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify tunnel 1 is established", conftest.verification_macros + "ZAPP_Verify_ZIA_Tunnel1")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for tunnel 1 TWLP",
                conftest.verification_macros + "CHROME_Verify_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

    # ZCC Export Logs
    @pytest.mark.xray("MR-17196")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Check zcc export logs")
    def test_export_logs(self):
        exported_log_file_path = "/tmp/exported_logs_automation.zip"
        result = conftest.cfd_obj.remove_file(exported_log_file_path, might_not_be_there=True, use_sudo=True)
        assert result[0], result[1]

        result = conftest.zcc_obj.run_macro("Export Logs from ZCC)", conftest.action_macros + "ZAPP_Export_Logs", [exported_log_file_path])
        assert result[0], result[1]

        result = conftest.cfd_obj.check_file_exists(exported_log_file_path, delete_after_check=True, use_sudo=True)
        assert result[0], result[1]

    # ZCC Certs
    @pytest.mark.xray("MR-17196")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Check zcc certs are installed")
    def test_cert_posture_logs(self):
        result = conftest.zcc_obj.check_zcc_certs_installed()
        assert result[0], result[1]

    # Ping
    @pytest.mark.xray("MR-17196")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Check PING is working for T1")
    def test_ping_t1(self):
        result = conftest.cfd_obj.check_output("ping -c 1 google.com")
        assert result[0], result[1]

        output = result[2]["output"]
        if "0% packet loss" in output:
            assert True, "Ping working without issues"
        else:
            assert False, "Ping failed"

    # Traceroute
    @pytest.mark.xray("MR-17196")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Check traceroute is working for T1")
    def test_traceroute(self):
        result = conftest.cfd_obj.check_output("traceroute google.com")
        assert result[0], result[1]

    # Apparmor status
    @pytest.mark.xray("MR-17196")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Check apparmor status")
    def test_apparmor_status(self):
        result = conftest.cfd_obj.check_output("systemctl status apparmor.service")
        assert result[0], result[1]

        output = result[2]["output"]
        if "Active: active " in output:
            assert True, "Apparmor is running"
        else:
            assert False, f"Apparmor is not running - {output}"

    # Apparmor DENIED
    @pytest.mark.xray("MR-17196")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Check apparmor denied logs")
    def test_apparmor_denied(self):
        result = conftest.cfd_obj.check_output("cat /var/log/syslog | grep apparmor | grep DENIED | grep zscaler", failure_expected=True)
        assert result[0], result[1]

        output = result[2]["output"]
        if len(output) == 0:
            assert True, "No apparmor denied msgs for zscaler"
        else:
            assert False, "Found apparmor denied msgs for zscaler"

    # local subnet
    @pytest.mark.xray("MR-17196")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Check local subnet added in routing table")
    def test_local_subnet(self):
        # get ips
        result = conftest.cfd_obj.get_system_ips()
        assert result[0], result[1]

        ips = result[2]["ips"]
        subnets = result[2]["subnets"]

        # get rule list
        result = conftest.cfd_obj.check_output("ip rule list")
        assert result[0], result[1]

        output = result[2]["output"].split("\n")
        for i in range(len(ips)):
            rule = [x for x in output if ips[i] in x]
            if len(rule) != 0:
                table_id = rule[0].split(" ")[-1]
                table_ip = ips[i]
                table_subnet = subnets[i]
                break

        # check table
        result = conftest.cfd_obj.check_output(f"ip route show table {table_id}")
        assert result[0], result[1]

        output = result[2]["output"]
        subnet_to_find = str(ipaddress.IPv4Network(f"{table_subnet}", strict=False))
        if subnet_to_find in output:
            assert True
        else:
            assert False, f"Subnet {subnet_to_find} not in rule list - {output}"

    # CPU Usage
    @pytest.mark.xray("MR-17196")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("ZSTray CPU usage")
    def test_cpu_usage(self):
        result = conftest.cfd_obj.check_cpu_usage("/opt/zscaler/bin/ZSTray")
        assert result[0], result[1]

    # Syslog check
    @pytest.mark.xray("MR-17196")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Check syslog is not used in zstunnel.service")
    def test_syslog_in_service(self):
        result = conftest.cfd_obj.check_output("cat /lib/systemd/system/zstunnel.service | grep syslog", failure_expected=True)
        assert result[0], result[1]

        output = result[2]["output"]
        if len(output) == 0:
            assert True, "Syslog is not used"
        else:
            assert False, "Syslog in used in service files"

    # nslookup
    @pytest.mark.xray("MR-17196")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Checking for nslookup")
    def test_nslookup(self):
        start_time = time.time()
        result = conftest.cfd_obj.check_output("nslookup google.com **********")
        elapsed_time = int(time.time() - start_time)
        assert result[0], result[1]

        if elapsed_time <= 5:
            assert True, f"nslookup completed witnin 5 sec"
        else:
            assert False, f"nslookup took more than 5 sec - {elapsed_time}"

    # ZPA App IP
    @pytest.mark.xray("MR-17196")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Checking ZPA App IP")
    def test_zpa_app_ip(self):
        # ZPA off
        result = conftest.zcc_obj.run_macro("Turn OFF ZPA for TWLP", conftest.action_macros + "ZAPP_ZPA_Disable")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is OFF after ZPA is turned OFF for TWLP",
            conftest.verification_macros + "ZAPP_Verify_ZPA_OFF",
        )
        assert result[0], result[1]

        # Should get Public IP
        result = conftest.cfd_obj.check_output("resolvectl query ip.zscaler.com -4")
        assert result[0], result[1]

        resolved_ip = result[2]["output"].split("\n")[0].split(" ")[1]
        if "100.64" not in resolved_ip:
            assert True, "Getting correct IP for Public App"
        else:
            assert False, "Getting some other IP for Public App"

        result = conftest.zcc_obj.run_macro("Turn ON ZPA for TWLP", conftest.action_macros + "ZAPP_ZPA_Enable")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON after ZPA is turned ON for TWLP",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

        result = conftest.cfd_obj.check_output("resolvectl query ip.zscaler.com -4")
        assert result[0], result[1]

        resolved_ip = result[2]["output"].split("\n")[0].split(" ")[1]
        if "100.64" in resolved_ip:
            assert True, "Getting correct IP for ZPA App"
        else:
            assert False, "Getting some other IP for ZPA App"

    # Non exportable private key
    @pytest.mark.xray("MR-17196")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Check Logs for Cert Posture Results with non-exportable private key")
    def test_cert_posture_logs(self):
        posture_name = self.posture_name_temp + "client_cert"

        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)
        # assert result[0], result[1]

        result = conftest.device_posture_obj.create_client_certificate_dp(
            cert_to_be_uploaded=self.cert_encoded,
            toggle_crl_check=False,
            toggle_non_exp=True,
            linux=True,
            cert_path=self.cert_path,
            posture_name=posture_name,
        )
        assert result[0], result[1]
        time.sleep(2)

        result = conftest.zcc_obj.run_macro("Updating Policy", conftest.action_macros + "ZAPP_Update_Policy")
        assert result[0], result[1]
        time.sleep(4)
        result = conftest.zcc_obj.run_macro("Clearing Logs", conftest.action_macros + "ZAPP_Clear_Logs")
        assert result[0], result[1]
        result = conftest.zcc_obj.read_posture_logs(posture_name=posture_name, expected_result=True)
        assert result[0], result[1]
        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)
        assert result[0], result[1]

    # OS Version
    @pytest.mark.xray("MR-17196")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Check Logs for OS Version Posture Results")
    def test_os_version_posture_logs(self):
        posture_name = self.posture_name_temp + "os_version"

        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)
        # assert result[0], result[1]

        result = conftest.device_posture_obj.create_os_version_posture(
            ubuntu="22.04", fedora="35", centos="9", opensuse="15", rhel="8", posture_name=posture_name
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Updating Policy", conftest.action_macros + "ZAPP_Update_Policy")
        assert result[0], result[1]
        time.sleep(4)
        result = conftest.zcc_obj.run_macro("Clearing Logs", conftest.action_macros + "ZAPP_Clear_Logs")
        assert result[0], result[1]
        result = conftest.zcc_obj.read_posture_logs(posture_name=posture_name, expected_result=True)
        assert result[0], result[1]
        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)
        assert result[0], result[1]

    # Process Posture
    @pytest.mark.xray("MR-17196")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Check Logs for Process Posture Results")
    def test_process_posture_logs(self):
        posture_name = self.posture_name_temp + "process_check"

        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)
        # assert result[0], result[1]

        result = conftest.device_posture_obj.create_process_check_dp(
            posture_name=posture_name,
            linux=True,
            operating_system="Linux",
            process_path="/opt/zscaler/bin/ZSTray",
        )
        assert result[0], result[1]
        time.sleep(2)

        result = conftest.zcc_obj.run_macro("Updating Policy", conftest.action_macros + "ZAPP_Update_Policy")
        assert result[0], result[1]
        time.sleep(4)
        result = conftest.zcc_obj.run_macro("Clearing Logs", conftest.action_macros + "ZAPP_Clear_Logs")
        assert result[0], result[1]
        result = conftest.zcc_obj.read_posture_logs(posture_name=posture_name, expected_result=True)
        assert result[0], result[1]
        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)
        assert result[0], result[1]

    # Process Posture - Symlink
    @pytest.mark.xray("MR-17196")
    @add_markers("CFD")
    @zcc_mark_version(1.4)
    @allure.title("Check Logs for Process Posture (with symlink) Results")
    def test_process_posture_logs(self):
        posture_name = self.posture_name_temp + "process_check"
        org_process = "/usr/bin/ping"
        new_process_1 = "/tmp/ping_1"
        new_process_2 = "/tmp/ping_2"

        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)
        # assert result[0], result[1]

        # Create symlinks
        result = conftest.cfd_obj.create_symlink(org_process, new_process_1)
        assert result[0], result[1]
        result = conftest.cfd_obj.create_symlink(org_process, new_process_2)
        assert result[0], result[1]

        # run process 1
        proc = subprocess.Popen([new_process_1, "google.com"], stdout=subprocess.DEVNULL)

        # Check posture for process 2
        result = conftest.device_posture_obj.create_process_check_dp(
            posture_name=posture_name,
            linux=True,
            operating_system="Linux",
            process_path=new_process_2,
        )
        assert result[0], result[1]
        time.sleep(2)

        result = conftest.zcc_obj.run_macro("Updating Policy", conftest.action_macros + "ZAPP_Update_Policy")
        assert result[0], result[1]
        time.sleep(4)
        result = conftest.zcc_obj.run_macro("Clearing Logs", conftest.action_macros + "ZAPP_Clear_Logs")
        assert result[0], result[1]
        result = conftest.zcc_obj.read_posture_logs(posture_name=posture_name, expected_result=True)
        assert result[0], result[1]
        result = conftest.device_posture_obj.delete_posture(posture_name=posture_name)
        assert result[0], result[1]

        # Check process is still running and cleanup
        if proc.poll() is None:
            proc.kill()

        result = conftest.cfd_obj.remove_file(new_process_1, might_not_be_there=True)
        assert result[0], result[1]
        result = conftest.cfd_obj.remove_file(new_process_1, might_not_be_there=True)
        assert result[0], result[1]

    @allure.title("Teardown")
    def teardown_class(self):
        result = conftest.zcc_obj.run_macro("Logout From Zapp", conftest.action_macros + "ZAPP_Logout")
        assert result[0], result[1]

        result = conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        assert result[0], result[1]

        result = conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0], result[1]

        if os.path.exists("/tmp/ping_1"):
            os.remove("/tmp/ping_1")

        if os.path.exists("/tmp/ping_2"):
            os.remove("/tmp/ping_2")

        result = conftest.cfd_obj.remove_dummy_interfaces(20)
        assert result[0], result[1]

import os
import pytest
import distro
from pytest_metadata.plugin import metadata_key
from common_lib.mobileadmin.appprofile import *
from common_lib.mobileadmin.forwardingprofile import *
from common_lib.mobileadmin.serviceentitlement import *

from common_lib.mobileadmin.deviceposture import *
from OS_Linux.library.zcc_lib import *
from OS_Linux.module import device_postures
from OS_Linux.module import cfd

config_path = os.path.join(os.getcwd(), "config", os.environ.get("CONFIG_FILE"))  #: The variables loaded from the configuration file.
with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)

zcc_obj = Zcc()

logger_obj = logger.Logger.initialize_logger(log_file_name="feature_name.log", log_level="INFO")
app_profile_obj = AppProfile(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)
forwarding_profile_obj = ForwardingProfile(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)
service_entitlement_obj = ServiceEntitlement(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)
device_posture_obj = DevicePosture(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)
const_obj = VpnInterop.PulseVpnConst
postures_obj = device_postures.DevicePosture(sudo_password="", log_handle=logger_obj)
cfd_obj = cfd.CFD(sudo_password="", log_handle=logger_obj)

action_macros = "NEW_MACROS/Actions/"
verification_macros = "NEW_MACROS/Verify/"
login_macros = "NEW_MACROS/Login/"
other_macros = "NEW_MACROS/Others/"
login_creds = [
    variables["ZIA_USER_ID"] + "***" + variables["ZIA_USER_PASSWORD"],
    variables["ZPA_USER_ID"] + "***" + variables["ZPA_USER_PASSWORD"],
]


# HTML Report
def pytest_html_report_title(report):
    report.title = "ZCC Linux Automation Execution Report"


def pytest_html_results_table_html(report, data):
    if report.passed:
        del data[:]
        data.append("Test Passed, No log output captured")


def pytest_configure(config):
    config.stash[metadata_key]["Distro Name"] = distro.name() + " " + distro.version()
    config.stash[metadata_key]["ZCC Version"] = os.environ.get("zcc_version")
    config.stash[metadata_key]["Test Suite"] = "CFD"


@pytest.mark.optionalhook
def pytest_metadata(metadata):
    metadata.pop("Plugins", None)
    metadata.pop("Packages", None)


@pytest.fixture(autouse=True, scope="class")
def verify_setup():
    # Kill any open chrome instance
    try:
        subprocess.run(["pkill", "-f", "chrome"], check=True)
        print("Killed all chrome instances")
    except subprocess.CalledProcessError:
        print("Failed to kill chrome")
    except:
        print("Error during killing chrome")

    # Open new chrome window
    time.sleep(10)
    proc = subprocess.Popen(
        ["/usr/bin/google-chrome", "--disable-gpu", "--disable-software-rasterizer", "https://example.com/"],
        stderr=subprocess.DEVNULL,
    )

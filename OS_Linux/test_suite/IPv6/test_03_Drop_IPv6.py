import os
import warnings
import allure
import base64
import time
import conftest
from common_lib.Custom_Markers import *


class Test03DropIPv6:

    @allure.title("Setup")
    def setup_class(self):
        self.current_interface = "ens160"
        self.zcc = conftest.zcc_obj
        conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")

        result = conftest.forwarding_profile_obj.create_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0], result[1]
        result = conftest.forwarding_profile_obj.edit_forwarding_profile(forwarding_profile_name="automation_linux_fp", tunnel_mode="tunnel_1", drop_ip_v6=True)
        assert result[0], result[1]

        result = conftest.app_profile_obj.create_app_profile(
            operating_system="Linux", policy_name="automation_linux_dp", forwarding_profile_name="automation_linux_fp"
        )
        assert result[0], result[1]

        result = conftest.service_entitlement_obj.toggle_zpa(action=True)
        assert result[0], result[1]

    # After Login
    @pytest.mark.xray("MR-20651")
    @add_markers("ipSix")
    @zcc_mark_version(3.7)
    @allure.title("Test for tunnel 1 after ZCC login (drop ipv6)")
    def test_tunnel1_zcc_login(self):
        result = conftest.zcc_obj.run_macro("Login to ZCC (ZIA + ZPA)", conftest.login_macros + "ZAPP_ZIA_ZPA_Login", conftest.login_creds)
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify ZIA status is ON", conftest.verification_macros + "ZAPP_Verify_ZIA_ON")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify tunnel 1 is established", conftest.verification_macros + "ZAPP_Verify_ZIA_Tunnel1")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for tunnel 1",
                conftest.verification_macros + "CHROME_Verify_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

    # Check IPV6 is not accessible - after login
    @pytest.mark.xray("MR-20652")
    @add_markers("ipSix")
    @zcc_mark_version(3.7)
    @allure.title("Test for tunnel 1 with ipv6 traffic (drop ipv6)")
    def test_tunnel1_drop_ipv6(self):
        # ipv6 via zscaler - dropped
        result = conftest.zcc_obj.check_traffic(host_to_connect="https://example.com", via_zscaler=True, only_ipv6=True)
        assert not result[0], result[1]

        # ipv4 via zscaler - not dropped
        result = conftest.zcc_obj.check_traffic(host_to_connect="https://example.com", via_zscaler=True, only_ipv6=False)
        assert result[0], result[1]

    # Netwok ON/OFF
    @pytest.mark.xray("MR-20653")
    @add_markers("ipSix")
    @zcc_mark_version(3.7)
    @allure.title("Test for tunnel 1 with network toggle")
    def test_tunnel1_network_toggle(self):
        result = conftest.ipv6_obj.disable_interface(interface_to_disable=self.current_interface)
        assert result[0], result[1]
        time.sleep(30)

        result = conftest.zcc_obj.run_macro("Verify tunnel network error", conftest.verification_macros + "ZAPP_Verify_ZIA_Network_Error")
        assert result[0], result[1]

        result = conftest.ipv6_obj.enable_interface(interface_to_enable=self.current_interface)
        assert result[0], result[1]
        time.sleep(60)

        result = conftest.zcc_obj.run_macro("Verify tunnel 1 is established", conftest.verification_macros + "ZAPP_Verify_ZIA_Tunnel1")
        assert result[0], result[1]

    # Check IPV6 is not accessible - after network toggle
    @pytest.mark.xray("MR-20654")
    @add_markers("ipSix")
    @zcc_mark_version(3.7)
    @allure.title("Test for tunnel 1 with ipv6 traffic after ZCC network toggle (drop ipv6)")
    def test_tunnel1_drop_ipv6_after_network_toggle(self):
        # ipv6 via zscaler - dropped
        result = conftest.zcc_obj.check_traffic(host_to_connect="https://example.com", via_zscaler=True, only_ipv6=True)
        assert not result[0], result[1]

        # ipv4 via zscaler - not dropped
        result = conftest.zcc_obj.check_traffic(host_to_connect="https://example.com", via_zscaler=True, only_ipv6=False)
        assert result[0], result[1]

    # ZIA ON/OFF
    @pytest.mark.xray("MR-20655")
    @add_markers("ipSix")
    @zcc_mark_version(1.4)
    @allure.title("Test for tunnel 1 after ZIA ON/OFF")
    def test_tunnel1_zcc_zia_on_off(self):
        result = conftest.zcc_obj.run_macro("Turn OFF ZIA for tunnel 1", conftest.action_macros + "ZAPP_ZIA_Disable")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZIA status is OFF after ZIA is turned OFF for tunnel 1",
            conftest.verification_macros + "ZAPP_Verify_ZIA_OFF",
        )
        assert result[0], result[1]

        result = conftest.zcc_obj.run_macro("Turn ON ZIA for tunnel 1", conftest.action_macros + "ZAPP_ZIA_Enable")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZIA status is ON after ZIA is turned ON for tunnel 1",
            conftest.verification_macros + "ZAPP_Verify_ZIA_ON",
        )
        assert result[0], result[1]

        result = conftest.zcc_obj.run_macro(
            "Verify tunnel 1 is established after ZIA is turned ON for tunnel 1",
            conftest.verification_macros + "ZAPP_Verify_ZIA_Tunnel1",
        )
        assert result[0], result[1]

        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for tunnel 1",
                conftest.verification_macros + "CHROME_Verify_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

    # Check IPV6 is not accessible - after ZIA ON/OFF
    @pytest.mark.xray("MR-20656")
    @add_markers("ipSix")
    @zcc_mark_version(3.7)
    @allure.title("Test for tunnel 1 with ipv6 traffic after ZCC ON/OFF (drop ipv6)")
    def test_tunnel1_drop_ipv6_after_zia_on_off(self):
        # ipv6 via zscaler - dropped
        result = conftest.zcc_obj.check_traffic(host_to_connect="https://example.com", via_zscaler=True, only_ipv6=True)
        assert not result[0], result[1]

        # ipv4 via zscaler - not dropped
        result = conftest.zcc_obj.check_traffic(host_to_connect="https://example.com", via_zscaler=True, only_ipv6=False)
        assert result[0], result[1]

    # Restart Service
    @pytest.mark.xray("MR-20657")
    @add_markers("ipSix")
    @zcc_mark_version(1.4)
    @allure.title("Test for tunnel 1 after restarting service")
    def test_tunnel1_zcc_restart(self):
        result = conftest.zcc_obj.run_macro("Restart ZIA service for tunnel 1", conftest.action_macros + "ZAPP_Restart_Service")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZIA status is ON after restart service for tunnel 1",
            conftest.verification_macros + "ZAPP_Verify_ZIA_ON",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify tunnel 1 is established after restart service for tunnel 1",
            conftest.verification_macros + "ZAPP_Verify_ZIA_Tunnel1",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON after restart service for tunnel 1",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for tunnel 1",
                conftest.verification_macros + "CHROME_Verify_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

    # Check IPV6 is not accessible - after restart service
    @pytest.mark.xray("MR-20658")
    @add_markers("ipSix")
    @zcc_mark_version(3.7)
    @allure.title("Test for tunnel 1 with ipv6 traffic after restart service (drop ipv6)")
    def test_tunnel1_drop_ipv6_after_restart(self):
        # ipv6 via zscaler - dropped
        result = conftest.zcc_obj.check_traffic(host_to_connect="https://example.com", via_zscaler=True, only_ipv6=True)
        assert not result[0], result[1]

        # ipv4 via zscaler - not dropped
        result = conftest.zcc_obj.check_traffic(host_to_connect="https://example.com", via_zscaler=True, only_ipv6=False)
        assert result[0], result[1]

    # Exit and Relaunch
    @pytest.mark.xray("MR-20659")
    @add_markers("ipSix")
    @zcc_mark_version(1.4)
    @allure.title("Test for tunnel 1 after exit and relaunch")
    def test_tunnel1_zcc_exit_relaunch(self):
        result = conftest.zcc_obj.run_macro("Exit ZAPP while it was logged in", conftest.action_macros + "ZAPP_Exit")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Relaunch ZAPP", "NEW_MACROS/Open_ZAPP")
        assert result[0], result[1]

        result = conftest.zcc_obj.run_macro(
            "Verify ZIA status is ON after exit relaunch for tunnel 1",
            conftest.verification_macros + "ZAPP_Verify_ZIA_ON",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify tunnel 1 is established after exit relaunch for tunnel 1",
            conftest.verification_macros + "ZAPP_Verify_ZIA_Tunnel1",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON after exit relaunch for tunnel 1",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for tunnel 1",
                conftest.verification_macros + "CHROME_Verify_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

    # Check IPV6 is not accessible - after exit
    @pytest.mark.xray("MR-20660")
    @add_markers("ipSix")
    @zcc_mark_version(3.7)
    @allure.title("Test for tunnel 1 with ipv6 traffic after exit (drop ipv6)")
    def test_tunnel1_drop_ipv6_after_exit(self):
        # ipv6 via zscaler - dropped
        result = conftest.zcc_obj.check_traffic(host_to_connect="https://example.com", via_zscaler=True, only_ipv6=True)
        assert not result[0], result[1]

        # ipv4 via zscaler - not dropped
        result = conftest.zcc_obj.check_traffic(host_to_connect="https://example.com", via_zscaler=True, only_ipv6=False)
        assert result[0], result[1]

    @allure.title("Teardown")
    def teardown_class(self):
        result = conftest.zcc_obj.run_macro("Logout From Zapp", conftest.action_macros + "ZAPP_Logout")
        assert result[0], result[1]

        result = conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        assert result[0], result[1]

        result = conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0], result[1]

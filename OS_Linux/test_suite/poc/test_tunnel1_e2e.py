import sys
import pytest
import allure
import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version

sys.path.append("/home/<USER>/Documents/zapp_automation/OS_Linux")


class Test01Tunnel1:
    @allure.title("Setup")
    def setup_method(self):

        self.zcc = conftest.zcc_obj

        try:
            conftest.zcc_obj.report_step("Deleting device policy if it was existing")
            result=conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        except:
            pass
        try:
            self.zcc.report_step("Deleting forwarding profile if it was existing")
            result=conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        except:
            pass

        self.zcc.report_step("Creating forwarding profile")
        result=conftest.forwarding_profile_obj.create_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0],result[1]

        self.zcc.report_step("Creating device policy for the test")
        result=conftest.app_profile_obj.create_app_profile(operating_system="Linux", policy_name="automation_linux_dp",
                                               forwarding_profile_name="automation_linux_fp")
        assert result[0],result[1]

    @pytest.mark.xray("QA-229494")
    @allure.title("Test for tunnel 1 after zcc login")
    @add_markers("sanity")
    @zcc_mark_version(1.4)
    def test_tunnel1_zcc_login(self):

        result=self.zcc.run_macro("Login to zcc",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZPA_Plus_ZIA/ZAPP_ZPA_Login")
        assert result[0],result[1]

        result=self.zcc.run_macro("Verify tunnel 1 is established",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZappDemo/ZAPP_Verify_Tunnel_v1")
        assert result[0],result[1]

        result=self.zcc.run_macro("Verify Internet access by accessing ip.zscaler.com for tunnel version 1",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZappDemo/UI_Verify_Internet_Access")
        assert result[0],result[1]

        result=self.zcc.report_step("Verify Traffic is flowing through the tunnel by using packet sender version 1")
        # self.zcc.check_traffic_bytes()

    @pytest.mark.xray("QA-229495")
    @allure.title("Test for tunnel 1 after performing restart service")
    @add_markers("regression")
    @zcc_mark_version(1.4)
    def test_tunnel1_zcc_restart(self):

        result=self.zcc.run_macro("Restart ZIA service for tunnel version 1",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZPA_Plus_ZIA/ZAPP_Restart_Service")
        assert result[0],result[1]

        result=self.zcc.run_macro("Verify ZIA status is ON after restart service for tunnel version 1",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZappDemo/ZAPP_Verify_Tunnel_v1")
        assert result[0],result[1]

        result=self.zcc.run_macro("Turn Off and turn On ZIA for tunnel version 1",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZPA_Plus_ZIA/ZAPP_ZIA_Turn_Off_On")
        assert result[0],result[1]

        result=self.zcc.run_macro("Verify ZIA status is ON after turn off/on for tunnel version 1",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZappDemo/ZAPP_Verify_Tunnel_v1")
        assert result[0],result[1]

    @pytest.mark.xray("QA-229496")
    @allure.title("Test for tunnel 1 after exit and relaunch")
    @add_markers("regression")
    @zcc_mark_version(1.4)
    def test_tunnel1_zcc_exit_relaunch(self):

        result=self.zcc.run_macro("Exit ZAPP while it was logged in",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_Zapp_Exit_And_Export_Logs/ZAPP_Exit_Without_Password")
        assert result[0],result[1]

        result=self.zcc.run_macro("Relaunch ZAPP", "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZappDemo/ZAPP_Relaunch")
        assert result[0],result[1]

        result=self.zcc.run_macro("Verify ZIA status is ON after exit relaunch for tunnel version 1",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZappDemo/ZAPP_Verify_Tunnel_v1")
        assert result[0],result[1]

        result=self.zcc.run_macro("Logout From Zapp",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_Zapp_Exit_And_Export_Logs/ZAPP_Logout_Without_Password")
        assert result[0],result[1]

    @allure.title("Teardown")
    def teardown_method(self):

        try:
            self.zcc.report_step("Deleting device policy if it was existing")
            conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        except:
            pass
        try:
            self.zcc.report_step("Deleting forwarding profile if it was existing")
            conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        except:
            pass

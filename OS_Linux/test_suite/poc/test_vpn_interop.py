import sys
import pytest
import os
import allure
import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version

sys.path.append("/home/<USER>/Documents/zapp_automation/OS_Linux")


class Test02VPNInterop:
    @allure.title("Setup")
    def setup_method(self):
        # admin_ops_part
        self.const = conftest.const_obj
        self.zcc = conftest.zcc_obj

        try:
            self.zcc.report_step("Deleting device policy if it was existing")
            conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        except:
            pass
        try:
            self.zcc.report_step("Deleting forwarding profile if it was existing")
            conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        except:
            pass

        self.zcc.report_step("Creating forwarding profile")
        result=conftest.forwarding_profile_obj.create_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0],result[1]

        self.zcc.report_step("Creating device policy for the test")
        result=conftest.app_profile_obj.create_app_profile(operating_system="Linux", policy_name="automation_linux_dp",
                                               forwarding_profile_name="automation_linux_fp")
        assert result[0],result[1]

        result=conftest.forwarding_profile_obj.edit_forwarding_profile(forwarding_profile_name="automation_linux_fp",
                                                  tunnel_mode={'offTrusted': 'none', 'vpnTrusted': 'tunnel_1'},
                                                  trusted_network="inforwardprofile", dns_servers=conftest.const_obj.vpn_dns_server)
        assert result[0],result[1]

    @pytest.mark.xray("QA-229498")
    @allure.step("test_set_fp_to_vpn_mode")
    @add_markers("regression")
    @zcc_mark_version(1.4)
    def test_vpn_interop(self):

        result=self.zcc.run_macro("#Login to ZCC", "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZPA_Plus_ZIA/ZAPP_ZPA_Login")
        assert result[0],result[1]

        result=self.zcc.run_macro("Verify ZCC is in disabled state in before pulse is connected in full tunnel",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZappDemo/ZAPP_Verify_None_Disabled")
        assert result[0],result[1]

        result=self.zcc.run_macro("#Connect Pulse application in full tunnel mode",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZappDemo/Pulse_Full_Login")
        assert result[0],result[1]

        result=self.zcc.run_macro("#Verify ZCC now moves to VPN trusted network",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZappDemo/ZAPP_VPN_Trusted")
        assert result[0],result[1]

        result=self.zcc.run_macro("#Disconnect Pulse", "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZappDemo/Pulse_disconnect")
        assert result[0],result[1]

        result=self.zcc.run_macro("#Verify ZCC moves to disabled state in off trusted network",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZappDemo/ZAPP_Verify_None_Disabled")
        assert result[0],result[1]

        result=self.zcc.run_macro("Logout from ZCC",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_Zapp_Exit_And_Export_Logs/ZAPP_Logout_Without_Password")
        assert result[0],result[1]

    @allure.title("Teardown")
    def teardown_method(self):

        try:
            self.zcc.report_step("Deleting device policy if it was existing")
            conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        except:
            pass
        try:
            self.zcc.report_step("Deleting forwarding profile if it was existing")
            conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        except:
            pass

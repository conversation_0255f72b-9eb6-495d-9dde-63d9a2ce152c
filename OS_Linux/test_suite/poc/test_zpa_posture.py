import sys
import pytest
import os
import allure
import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version


sys.path.append("/home/<USER>/Documents/zapp_automation/OS_Linux")


class Test03ZPAPosture:
    @allure.title("Setup")
    def setup_method(self):
        # admin_ops_part
        
        self.zpa_policy = conftest.zpa_policy_obj
        self.zcc = conftest.zcc_obj
        self.device_posture=conftest.device_posture_obj

        try:
            self.zcc.report_step("Deleting device policy if it was existing")
            conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        except:
            pass
        try:
            self.zcc.report_step("Deleting forwarding profile if it was existing")
            conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        except:
            pass
        try:
            self.zcc.report_step("Delete device posture")
            conftest.device_posture_obj.delete_device_posture(name="Linux_Auto_Posture")
        except:
            pass

        self.zcc.report_step("Creating forwarding profile")
        result=conftest.forwarding_profile_obj.create_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0],result[1]

        self.zcc.report_step("Creating device policy for the test")
        result=conftest.app_profile_obj.create_app_profile(operating_system="Linux", policy_name="automation_linux_dp",
                                               forwarding_profile_name="automation_linux_fp")
        assert result[0],result[1]

    @pytest.mark.xray("QA-229497")
    @allure.title("Tests for ZPA device posture")
    @add_markers("regression")
    @zcc_mark_version(1.4)
    def test_zpa_device_posture(self):

        self.zcc.report_step("Creating Device posture ")
        result=conftest.device_posture_obj.create_process_check_dp(name= "Linux_Auto_Posture",
                                                                    operating_system="Linux",
                                                                    process_path="/opt/zscaler/bin/ZSTray",linux=True)
        assert result[0],result[1]

        #self.zcc.report_step("Creating access policy ")
        #result = conftest.app_segment_obj.get_app_segment_data(zpa_app_segment="App for Defender Posture test", return_id=True)
        #assert result[0],result[1]
        # self.zpa_lib.Create_Access_Policy(postureName="new", action="ALLOW", verification=True, criteria="posture",
        #                                   appSegmentCriteria="App for Defender Posture test")
        #conftest.device_posture_obj.get_device_posture_id(fetch_all=True)

        result =self.zcc.run_macro("Login to zcc", "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZPA_Plus_ZIA/ZAPP_ZPA_Login")
        assert result[0],result[1]

        result =self.zcc.run_macro("Verify ZPA status is ON",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZPA_Plus_ZIA/ZAPP_Verify_ZPA_Status_ON")
        assert result[0],result[1]

        result =self.zcc.run_macro("Verify tunnel 1 is established",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZappDemo/ZAPP_Verify_Tunnel_v1")
        assert result[0],result[1]

        result =self.zcc.run_macro("Clear logs on zcc",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_Zapp_UI_Verifications/ZAPP_Clear_Logs")
        assert result[0],result[1]

        self.zcc.report_step("Check MS defender log for posture check")
        # self.zcc.read_defedner_posture_logs()

        #result =self.zcc.run_macro("Verify ZPA app access for ms defender posture",
         #                  "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_ZappDemo/UI_Verifiy_ZPA_APP_Access_defender")
        #assert result[0],result[1]

        result =self.zcc.run_macro("Logout from zcc",
                           "Linux_Ubuntu_ZAPP_Automation_Macros/Linux_Zapp_Exit_And_Export_Logs/ZAPP_Logout_Without_Password")
        assert result[0],result[1]

    @allure.title("Teardown")
    def teardown_method(self):

        conftest.zcc_obj.report_step("Delete device posture")
        result =conftest.device_posture_obj.delete_posture(posture_name="Linux_Auto_Posture")
        assert result[0],result[1]

        try:
            self.zcc.report_step("Deleting device policy if it was existing")
            conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        except:
            pass
        try:
            self.zcc.report_step("Deleting forwarding profile if it was existing")
            conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        except:
            pass
        self.zpa.delete_policy(policy_type="AccessPolicy")

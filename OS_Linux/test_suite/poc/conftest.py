import sys
import os
import pytest
import allure
from common_lib.mobileadmin.appprofile import *
from common_lib.mobileadmin.forwardingprofile import *
from common_lib.mobileadmin.serviceentitlement import *

from common_lib import Constants
from common_lib.adminzpa.accesszpaapps import *
from common_lib.adminzpa.zpapolicy import *
from common_lib.adminzpa.appsegment import *
from common_lib.mobileadmin.deviceposture import *
from OS_Linux.library.zcc_lib import *
#from common_lib.mobileadmin import *

config_path = os.path.join(os.getcwd(), "config", os.environ.get("CONFIG_FILE"))

    #: The variables loaded from the configuration file.
with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)

zcc_obj=Zcc()


logger_obj = logger.Logger.initialize_logger(log_file_name="feature_name.log",log_level="DEBUG")
app_profile_obj = AppProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
forwarding_profile_obj=ForwardingProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
service_entitlement_obj=ServiceEntitlement(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
access_zpa_obj=AccessZpaApps(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
zpa_policy_obj=ZpaPolicy(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
app_segment_obj=AppSegment(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
device_posture_obj=DevicePosture(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
const_obj=Constants.VPN_Interop.Pulse_VPN_Const

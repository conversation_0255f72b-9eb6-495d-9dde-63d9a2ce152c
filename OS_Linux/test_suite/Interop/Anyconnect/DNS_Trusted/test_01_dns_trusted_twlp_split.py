import os
import time
import warnings
import subprocess
import allure
from OS_Linux.test_suite.Interop.Anyconnect import conftest
from common_lib.Custom_Markers import *


class Test01DnsTrustedTwlpSplit:

    @allure.title("Setup")
    def setup_class(self):
        self.zcc = conftest.zcc_obj

        result = conftest.zcc_obj.get_dns_servers()
        assert result[0], result[1]
        curr_dns = result[2]["dns_servers"]

        conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")

        result = conftest.forwarding_profile_obj.create_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0], result[1]
        result = conftest.forwarding_profile_obj.edit_forwarding_profile(
            forwarding_profile_name="automation_linux_fp",
            tunnel_mode="twlp",
            trusted_network="inforwardprofile",
            dns_servers=curr_dns[0],
        )
        assert result[0], result[1]

        result = conftest.app_profile_obj.create_app_profile(
            operating_system="Linux", policy_name="automation_linux_dp", forwarding_profile_name="automation_linux_fp"
        )
        assert result[0], result[1]
        result = conftest.app_profile_obj.edit_app_profile(
            policy_name="automation_linux_dp", vpn_bypass=conftest.vpn_anyconnect_obj.VPN_BYPASS
        )
        assert result[0], result[1]

        result = conftest.service_entitlement_obj.toggle_zpa(action=True)
        assert result[0], result[1]

    @pytest.mark.xray("MR-11982")
    @add_markers("interop", "anyconnect")
    @zcc_mark_version(1.4)
    @allure.title("ZCC login - DNS Trusted")
    def test_twlp_split_zcc_login(self):
        result = conftest.zcc_obj.run_macro("Login to ZCC (ZIA + ZPA)", conftest.login_macros + "ZAPP_ZIA_ZPA_Login", conftest.login_creds)
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify ZIA status is ON", conftest.verification_macros + "ZAPP_Verify_ZIA_ON")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify tunnel 1 is established", conftest.verification_macros + "ZAPP_Verify_ZIA_Tunnel1")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify Network is Trusted", conftest.verification_macros + "ZAPP_Verify_ZIA_Trusted_Network")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for tunnel 1",
                conftest.verification_macros + "CHROME_Verify_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

    @pytest.mark.xray("MR-11982")
    @add_markers("interop", "anyconnect")
    @zcc_mark_version(1.4)
    @allure.title("Anyconnect login - Split")
    def test_twlp_split_anyconnect_login(self):
        result = conftest.zcc_obj.run_macro("Login to ZCC (ZIA + ZPA)", conftest.login_macros + "VPN_ANYCONNECT_Split_Login")
        assert result[0], result[1]

        result = conftest.zcc_obj.check_interface(interface_to_check="cscotun0", exists=True)
        assert result[0], result[1]

    @pytest.mark.xray("MR-11982")
    @add_markers("interop", "anyconnect")
    @zcc_mark_version(1.4)
    @allure.title("Verify ZCC and VPN interfaces when both are up")
    def test_twlp_split_validations(self):

        # ZCC
        result = conftest.zcc_obj.run_macro("Verify ZIA status is ON", conftest.verification_macros + "ZAPP_Verify_ZIA_ON")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify tunnel 1 is established", conftest.verification_macros + "ZAPP_Verify_ZIA_Tunnel1")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify Network is Trusted", conftest.verification_macros + "ZAPP_Verify_ZIA_Trusted_Network")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

        result = conftest.zcc_obj.check_interface(interface_to_check="zcctun0", exists=True)
        assert result[0], result[1]

        result = conftest.zcc_obj.get_dns_servers(interface_to_check="zcctun0")
        assert result[0], result[1]

        for dns_server in result[2]["dns_servers"]:
            if "100.64" not in dns_server:
                assert False, f"Wrong DNS servers in zcctun0 interface: {result[2]['dns_servers']}"
        assert True

        # Anyconnect
        result = conftest.zcc_obj.check_interface(interface_to_check="cscotun0", exists=True)
        assert result[0], result[1]

        result = conftest.zcc_obj.get_dns_servers(interface_to_check="cscotun0")
        assert result[0], result[1]

        if conftest.vpn_anyconnect_obj.VPN_DNS_SERVER["split"] in result[2]["dns_servers"]:
            assert True
        else:
            assert False, f"Wrong DNS servers in tun0 interface: {result[2]['dns_servers']}"

    @pytest.mark.xray("MR-11982")
    @add_markers("interop", "anyconnect")
    @zcc_mark_version(1.4)
    @allure.title("Verify ZCC and it's interface after VPN Logout")
    def test_twlp_split_anyconnect_logout(self):
        result = conftest.zcc_obj.run_macro("Logout From Zapp", conftest.login_macros + "VPN_ANYCONNECT_Logout")
        assert result[0], result[1]

        result = conftest.zcc_obj.check_interface(interface_to_check="cscotun0", exists=False)
        time.sleep(5)

        # ZCC
        result = conftest.zcc_obj.run_macro("Verify ZIA status is ON", conftest.verification_macros + "ZAPP_Verify_ZIA_ON")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify tunnel 1 is established", conftest.verification_macros + "ZAPP_Verify_ZIA_Tunnel1")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify Network is Trusted", conftest.verification_macros + "ZAPP_Verify_ZIA_Trusted_Network")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]

        result = conftest.zcc_obj.check_interface(interface_to_check="zcctun0", exists=True)
        assert result[0], result[1]

        result = conftest.zcc_obj.get_dns_servers(interface_to_check="zcctun0")
        assert result[0], result[1]

        for dns_server in result[2]["dns_servers"]:
            if "100.64" not in dns_server:
                assert False, f"Wrong DNS servers in zcctun0 interface: {result[2]['dns_servers']}"
        assert True

    @allure.title("Teardown")
    def teardown_class(self):
        result = conftest.zcc_obj.run_macro("Logout From Zapp", conftest.action_macros + "ZAPP_Logout")
        assert result[0], result[1]

        result = conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        assert result[0], result[1]

        result = conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0], result[1]

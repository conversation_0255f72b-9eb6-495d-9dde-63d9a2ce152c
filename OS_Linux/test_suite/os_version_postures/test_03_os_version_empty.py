import pytest
import allure
import warnings
import time

import conftest
from common_lib.Custom_Markers import *


class Test13OSVersionEmpty:
    posture_name = "os_version_posture_empty"

    @allure.title("Setup")
    def setup_class(self):
        self.zcc = conftest.zcc_obj
        conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        result = conftest.forwarding_profile_obj.create_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0], result[1]
        result = conftest.forwarding_profile_obj.edit_forwarding_profile(
            forwarding_profile_name="automation_linux_fp", tunnel_mode="tunnel_1"
        )
        assert result[0], result[1]
        result = conftest.app_profile_obj.create_app_profile(
            operating_system="Linux", policy_name="automation_linux_dp", forwarding_profile_name="automation_linux_fp"
        )
        assert result[0], result[1]
        result = conftest.device_posture_obj.create_os_version_posture(posture_name=self.posture_name)
        assert result[0], result[1]
        result = conftest.service_entitlement_obj.toggle_zpa(action=True)
        assert result[0], result[1]

    # After Login
    @pytest.mark.xray("MR-9320")
    @add_markers("postures")
    @zcc_mark_version(1.4)
    @allure.title("Test ZCC login")
    def test_tunnel1_zcc_login(self):
        result = conftest.zcc_obj.run_macro("Login to ZCC (ZIA + ZPA)", conftest.login_macros + "ZAPP_ZIA_ZPA_Login", conftest.login_creds)
        assert result[0], result[1]

        result = conftest.zcc_obj.run_macro(
            "Verify ZIA status is ON",
            conftest.verification_macros + "ZAPP_Verify_ZIA_ON",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Verify tunnel 1 is established", conftest.verification_macros + "ZAPP_Verify_ZIA_Tunnel1")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]
        try:
            result = conftest.zcc_obj.run_macro(
                "Verify Internet access by accessing ip.zscaler.com for tunnel 1",
                conftest.verification_macros + "CHROME_Verify_Internet_Access",
            )
            assert result[0], result[1]
        except:
            warnings.warn(UserWarning("Unable to validate internet access using ip.zscaler.com"))

    # Test os version - it should PASS
    @pytest.mark.xray("MR-9321")
    @add_markers("postures")
    @zcc_mark_version(1.4)
    @allure.title("Test for os version Posture (Positive Case)")
    def test_os_version_posture(self):
        result = conftest.zcc_obj.run_macro("Clearing Logs", conftest.action_macros + "ZAPP_Clear_Logs")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Updating Policy", conftest.action_macros + "ZAPP_Update_Policy")
        assert result[0], result[1]
        time.sleep(10)
        result = conftest.zcc_obj.read_posture_logs(posture_name=self.posture_name, expected_result=True)
        assert result[0], result[1]

    # Test os version after restart service - it should PASS
    @pytest.mark.xray("MR-9322")
    @add_markers("postures")
    @zcc_mark_version(1.4)
    @allure.title("Test for os version Posture after restart service (Positive Case)")
    def test_os_version_posture_restart(self):
        result = conftest.zcc_obj.run_macro("Restart ZIA service for tunnel 1", conftest.action_macros + "ZAPP_Restart_Service")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZIA status is ON after restart service for tunnel 1",
            conftest.verification_macros + "ZAPP_Verify_ZIA_ON",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify tunnel 1 is established after restart service for tunnel 1",
            conftest.verification_macros + "ZAPP_Verify_ZIA_Tunnel1",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro(
            "Verify ZPA status is ON after restart service for tunnel 1",
            conftest.verification_macros + "ZAPP_Verify_ZPA_ON",
        )
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Clearing Logs", conftest.action_macros + "ZAPP_Clear_Logs")
        assert result[0], result[1]
        result = conftest.zcc_obj.run_macro("Updating Policy", conftest.action_macros + "ZAPP_Update_Policy")
        assert result[0], result[1]
        time.sleep(10)
        result = conftest.zcc_obj.read_posture_logs(posture_name=self.posture_name, expected_result=True)
        assert result[0], result[1]

    @allure.title("Teardown")
    def teardown_class(self):
        result = conftest.zcc_obj.run_macro("Logout From Zapp", conftest.action_macros + "ZAPP_Logout")
        assert result[0], result[1]

        result = conftest.device_posture_obj.delete_posture(posture_name=self.posture_name)
        assert result[0], result[1]
        result = conftest.app_profile_obj.delete_app_profile(operating_system="Linux", policy_name="automation_linux_dp")
        assert result[0], result[1]
        result = conftest.forwarding_profile_obj.delete_forwarding_profile(forwarding_profile_name="automation_linux_fp")
        assert result[0], result[1]

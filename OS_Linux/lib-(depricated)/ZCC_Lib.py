<<<<<<<< HEAD:OS_Linux/lib/zcc_lib.py
import os
import datetime
import subprocess
import time
import re
import codecs
import socket
import allure

def open_log_file():
    path = "/home/<USER>/.Zscaler/Logs"
    filename = os.listdir(path)
    print(filename)
    now = datetime.datetime.now()
    fileopen = "ZSTray_" + str(now.year)

    filename.sort(reverse=True)
    for i in filename:
        if re.search(fileopen, i) != None:
            filetoopen = i;
            break;
    print(filetoopen)

    f = open(os.path.join(path, filetoopen), "r")
    text = f.readlines()
    filename = os.listdir(r"/var/log/zscaler/.Zscaler/Logs")
    print(filename)
    now = datetime.datetime.now()
    fileopen = "zsaservice_" + str(now.year)

    filename.sort(reverse=True)
    for i in filename:
        if re.search(fileopen, i) != None:
            filetoopen = i;
            break;
    print(filetoopen)

    filename = os.listdir(r"/var/log/zscaler/.Zscaler/Logs")
    print(filename)
    now = datetime.datetime.now()
    fileopen = "zstunnel_" + str(now.year)

    filename.sort(reverse=True)
    for i in filename:
        if re.search(fileopen, i) != None:
            filetoopen = i;
            break;

    print(filetoopen)


def check_log_files():
    logs_dir = r"/var/log/zscaler/.Zscaler/Logs"
    if os.path.exists(logs_dir):
        files_list_before = os.listdir(logs_dir)
        files_count_before = len(files_list_before)
        print("Number of files before clearing logs : {}".format(files_count_before))
        print("Calling macro to clear the logs in ZApp")
        run_macro("Linux_Ubuntu_ZAPP_Automation_Macros/Linux_Zapp_UI_Verifications/ZAPP_Clear_Logs")
        files_list_after = os.listdir(logs_dir)
        files_count_after = len(files_list_after)
        print("Number of files after clearing logs : {}".format(files_count_after))
        if files_count_after <= files_count_before:
            print("Logs were cleared properly from ZAPP clear logs")
            return 1
        else:
            raise Exception("Logs were not cleared")
    else:
        raise Exception("Logs directory does not exist")


def read_temp_file():
    file_name = "Temporary"
    home_dir = os.path.expanduser("~")
    documents_dir = os.path.join(home_dir, "Downloads")
    file_path = os.path.join(documents_dir, file_name)
    if os.path.exists(file_path):
        with open(file_path) as temp:
            temp_var = temp.readline()
    else:
        raise Exception("Temporary file is not created")
    os.remove(file_path)
    return temp_var


def check_traffic_bytes():
    print("Capturing the sent bytes on Zapp before sending traffic")
    run_macro("Linux_Ubuntu_ZAPP_Automation_Macros/Linux_OTP_And_Traffic/ZAPP_Traffic_Bytes")
    bytes_list = read_temp_file().split()
    if bytes_list[1][0] == "M":
        bytes_before = float(bytes_list[0]) * 1024
    elif bytes_list[1][0] == "G":
        bytes_before = float(bytes_list[0]) * 1024 * 1024
    else:
        bytes_before = float(bytes_list[0])
    print("{} KB seen in sent bytes on ZAPP before sending traffic".format(bytes_before))
    print("Send traffic using packet sender for 5 seconds")
    run_macro("Linux_Ubuntu_ZAPP_Automation_Macros/Linux_OTP_And_Traffic/Packet_Sender_Traffic")
    print("Capturing the sent bytes on Zapp after sending traffic")
    run_macro("Linux_Ubuntu_ZAPP_Automation_Macros/Linux_OTP_And_Traffic/ZAPP_Traffic_Bytes")
    bytes_list_after = read_temp_file().split()
    if bytes_list_after[1][0] == "M":
        bytes_after = float(bytes_list_after[0]) * 1024
    elif bytes_list_after[1][0] == "G":
        bytes_after = float(bytes_list_after[0]) * 1024 * 1024
    else:
        bytes_after = float(bytes_list_after[0])
    print("{} KB seen in sent bytes on Zapp after sending traffic".format(bytes_after))
    diff_bytes = bytes_after - bytes_before
    print("Difference between the bytes is {}".format(diff_bytes))
    if diff_bytes > 5000:
        print("Traffic went through the tunnel and bytes were increased")
    else:
        raise Exception("Bytes were not incremented")


def check_pac_file():
    file_name = "Temporary"
    home_dir = os.path.expanduser("~")
    documents_dir = os.path.join(home_dir, "Downloads")
    file_path = os.path.join(documents_dir, file_name)
    if os.path.exists(file_path):
        with open(file_path) as pac:
            pac_url = pac.readline()
            print("Pac url is {}".format(pac_url))
    else:
        raise Exception("Temporary file is not created")
    os.remove(file_path)
    home_dir = os.path.expanduser("~")
    download_dir = os.path.join(home_dir, "Downloads")
    split_list = pac_url.split("/")
    pac_name = split_list.pop()
    pac_path = os.path.join(download_dir, pac_name).strip()
    if os.path.exists(pac_path):
        print("Pac file {} is found in the location {}".format(pac_name, download_dir))
        os.remove(pac_path)
        return 1
    else:
        raise Exception("Pac file is not found")


def kill_chrome():
    os.system('ps axf | grep -E \'chrome|firefox\' | grep -v grep | awk \'{print "kill " $1 }\' | sh')


def check_coredump():
    current_time = datetime.datetime.now()
    date_today = str(current_time.year) + "-" + str(current_time.month) + "-" + str(current_time.day)
    date_before = str(current_time.year) + "-" + str(current_time.month) + "-" + str(current_time.day - 1)
    # print(date_before)
    call = subprocess.Popen("coredumpctl list --since={}".format(date_today), shell=True, stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    result = str(call.communicate())
    if re.search("No", result) != None:
        print("No coredumps found")

    else:
        result = result.split('\n')
        result = result[0].split()
        tray = 0
        tunnel = 0
        service = 0
        for line in result:
            if re.search("ZSTray", line) != None:
                tray = 1
            if re.search("zstunnel", line) != None:
                tunnel = 1
            if re.search("zsaservice", line) != None:
                service = 1

        if tray == 1:
            print("Tray dump is seen")
        if tunnel == 1:
            print("Tunnel dump is seen")
        if service == 1:
            print("Service dump is seen")

        raise Exception("Coredumps are seen")

def check_interface():
    call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")
    interface = 0
    dns = 0
    domain = 0
    dnsdomain = 0
    for line in result:
        if re.search("zcctun0",line):
            interface = 1
        if re.search("**********",line):
            dns = 1
        if re.search("~.",line):
            domain=1
        if re.search("shashank.com",line):
            dnsdomain=1

    if interface :
        print("Tun interface present")
    else :
        raise Exception("Tun interface not present")

def check_dnsserver():
    call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())

    result = result.split("\n")
    interface = 0
    dns = 0
    domain = 0
    dnsdomain = 0
    for line in result:
        if re.search("zcctun0",line):
            interface = 1
        if re.search("**********",line):
            dns = 1
        if re.search("~.",line):
            domain=1
        if re.search("shashank.com",line):
            dnsdomain=1

    if dns:
        print("Dns server present")
    else :
        raise Exception("Dns server not present")

def check_domain():
    call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())

    result = result.split("\n")
    interface = 0
    dns = 0
    domain = 0
    dnsdomain = 0
    for line in result:
        if re.search("zcctun0",line):
            interface = 1
        if re.search("**********",line):
            dns = 1
        if re.search("~.",line):
            domain=1
        if re.search("shashank.com",line):
            dnsdomain=1

    if domain:
        print("Catch all present")
    else :
        raise Exception("Catch all not present")

def check_zpasearchdomain():
    call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())

    result = result.split("\n")
    interface = 0
    dns = 0
    domain = 0
    dnsdomain = 0
    for line in result:
        if re.search("zcctun0",line):
            interface = 1
        if re.search("**********",line):
            dns = 1
        if re.search("~.",line):
            domain=1
        if re.search("shashank.com",line):
            dnsdomain=1

    if dnsdomain:
        print("Dns search domain present")
    else:
        raise Exception("Dns search domain not present")

def check_zpaapplication():
    call = subprocess.Popen("resolvectl flush-caches" , shell = True , stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    call = subprocess.Popen("resolvectl query abc.com", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")

    zpa = 0
    zcctun0=0
    for line in result:
        if re.search("100.64",line):
            zpa=1
        if re.search("zcctun0",line):
            zcctun0=1

    if zpa and zcctun0:
        print("Zpa app ")
    else :
        raise Exception("Zpa application .Request doesnot go to zcctun0 interface")

def check_normalapplication():
    call = subprocess.Popen("resolvectl flush-caches" , shell = True , stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    call = subprocess.Popen("resolvectl query amazon.com", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")

    zpa = 0
    zcctun0=0
    for line in result:
        if re.search("100.64",line):
            zpa=1
        if re.search("zcctun0",line):
            zcctun0=1

    if zpa == 0 and zcctun0 == 1 :
        print("Normal Application going to zcctun0 interface")
    else :
        raise Exception("Normal Application.Request doesnot go to zcctun0 interface")


def check_pulseapplication_zcc():
    call = subprocess.Popen("resolvectl flush-caches", shell = True , stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    call = subprocess.Popen("resolvectl query corp.zscaler.com", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")

    zpa = 0
    zcctun0=0
    for line in result:
        if re.search("100.64",line):
            zpa=1
        if re.search("zcctun0",line):
            zcctun0=1

    if zpa == 0 and tun0 == 1 :
        print("Pulse Application going to tun0 interface")
    else :
        raise Exception("Request doesnot go to zcctun0 interface")

def check_anyconnectapplication():
    call = subprocess.Popen("resolvectl flush-caches", shell = True , stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    call = subprocess.Popen("resolvectl query ubuntu.kevaldoshi.com", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")

    ciscoapp = 0
    cscotun0=0
    for line in result:
        if re.search("192.168.100",line):
            cscotun0=1
        if re.search("cscotun0",line):
            ciscoapp=1

    if ciscoapp == 1 and cscotun0 == 1 :
           print("Anyconnect Application going to cscotun0 interface")
    else :
        raise Exception("Anyconnect application doesnot go to cscotun0 interface")

def anyconnect_check_interface():
    call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")
    interface = 0
    dns = 0
    domain = 0
    dnsdomain = 0
    for line in result:
        if re.search("cscotun0",line):
            interface = 1

    if interface :
        print("cscotun0 interface present")
    else :
        raise Exception("cscotun0 interface not present")

def pulse_check_interface():
    call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")
    interface = 0
    dns = 0
    domain = 0
    dnsdomain = 0
    for line in result:
        if re.search("tun0",line):
            interface = 1

    if interface :
        print("tun0 interface present")
    else :
        raise Exception("tun0 interface not present")

def anyconnect_check_dns():
    call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")
    interface = 0
    dns = 0
    domain = 0
    dnsdomain = 0
    for line in result:
        if re.search("cscotun0",line):
            interface = 1
        if re.search("**************",line):
            dns = 1

    if interface and dns :
        print("cscotun0 dns present")
    else :
        raise Exception("cscotun0 dns not present")
        
def pulse_check_dns():
    call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")
    interface = 0
    dns = 0
    domain = 0
    dnsdomain = 0
    for line in result:
        if re.search("tun0",line):
            interface = 1
        if re.search("***********",line):
            dns = 1

    if interface and dns :
        print("cscotun0 dns present")
    else :
        raise Exception("cscotun0 dns not present")

def run_chrome_tab():
    browser = r"/usr/bin/google-chrome"
    proc = subprocess.Popen([browser, "--disable-gpu", "--disable-software-rasterizer"], stderr=subprocess.DEVNULL)
    # "--ignore-certificate-errors"
    time.sleep(10)


def report_step(step_title):
    with allure.step(step_title):
        pass
def run_macro(step_title, macro_name,retry=1):
    # Initializing variables
    with allure.step(step_title):

        home_dir = os.path.expanduser("~")
        log_dir = os.path.join(home_dir, "Downloads")
        browser = r"/usr/bin/google-chrome"
        html_path = os.path.join(log_dir, "ui.vision.html")
        macro = macro_name
        timeout_seconds = 360

        # Create log file
        log_name = 'log_' + datetime.datetime.now().strftime('%m-%d-%Y_%H_%M_%S') + '.txt'
        log_path = os.path.join(log_dir, log_name)

        # Initialize the macro path as per UI Vision syntax
        macro_path = r'file://' + html_path + r'?direct=1&macro=' + macro + r'&savelog=' + log_name + r'&loadmacrotree=1&storage=browser'

        print("Log file used is {}".format(log_name))

        # Call UI vision to run the macro
        time.sleep(2)
        proc = subprocess.Popen([browser, "--disable-gpu", "--disable-software-rasterizer", macro_path], stderr=subprocess.DEVNULL)

        # Continuously check the log file for existence
        status_runtime = 1

        while not os.path.exists(log_path) and status_runtime < timeout_seconds:
            # print("Waiting for macro to finish, seconds=%d" % status_runtime)
            time.sleep(1)
            status_runtime += 1

        # Read the log file whether status is successfull
        if status_runtime < timeout_seconds:
            with open(log_path) as f:
                status_text = f.readline()
                full_log = f.read()
                os.remove(log_path)

            if status_text.find('Status=OK') != -1:
                print("Macro {} completed successfully".format(macro))
                print(full_log)
                time.sleep(2)
                return 1
            else:
                print("Macro {} failed with error {}".format(macro, status_text))
                print(full_log)
                time.sleep(2)
                raise Exception("Macro failed to execute")
        else:
            print("Macro did not complete within given time: {}".format(timeout_seconds))
            time.sleep(2)


            if retry==1 :
                print("Retrying")
                # Kill any open chrome instance
                try:
                    subprocess.run(["pkill", "-f", "chrome"], check=True)
                    print("Killed all chrome instances")
                except subprocess.CalledProcessError:
                    print("Failed to kill chrome")
                except:
                    print("Error during killing chrome")

                # Open new chrome window
                time.sleep(10)
                proc = subprocess.Popen(
                    [browser, "--disable-gpu", "--disable-software-rasterizer", "https://example.com/"], stderr=subprocess.DEVNULL
                )
                time.sleep(10)
                run_macro(step_title, macro_name,0);
            else:
                raise Exception("Macro did not execute within time")

@allure.step("Check if VM is joined to a domain")
def domain_join_check():
    fqdn = socket.getfqdn()
        
    if "zccqa.onmicrosoft.com" in fqdn:
        print("VM is joined to domain")
    else :
        raise Exception("Not joined to domain")


@allure.step("Checking logs for posture result")
def read_posture_logs(posture_name: str, expected_result: bool):
    path = "/home/<USER>/.Zscaler/Logs"
    filenames = os.listdir(path)
    tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
    tray_logs.sort(reverse=True)
    last_logs = path + "/" + tray_logs[0]

    found_logs = False

    for line in reversed(open(last_logs).readlines()):
        if posture_name in line:
            posture_result = line.split()[-1][-1]
            found_logs = True
            if (posture_result == '1' and expected_result == True) or (posture_result == '0' and expected_result == False):
                print("Posture is working as expected")
                break
            else:
                raise Exception("Actual posture result is different than expected result")
            
    if not found_logs:
        raise Exception("Logs not found for given posture name")

        
@allure.step("Checking logs for Defender Posture")
def read_defedner_posture_logs():
    path = "/home/<USER>/.Zscaler/Logs"
    filenames = os.listdir(path)
    tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
    tray_logs.sort(reverse=True)
    last_logs = path + "/" + tray_logs[0]
    
    search_strings = ["MS_defender_posture_true", "MS_defender_posture_false"]
    to_find = search_strings.copy()
    result = {}
    defender = 0

    for line in reversed(open(last_logs).readlines()):
        for string in search_strings:
            if string in line:
                posture_result = line.split()[-1][-1]
                if (posture_result == '1' and string.split('_')[-1] == 'true') or (posture_result == '0' and string.split('_')[-1] == 'false'):
                    result[string] = True
                else:
                    result[string] = False
                    
                search_strings.remove(string)
            
            if len(search_strings) == 0:
                break
          
    if len(search_strings) == 0:
        print("Logs for posture Found")
    else:
        raise Exception("Logs not found")
        
    if result[to_find[0]] and result[to_find[1]]:
        print("Posture working as expected")
    else:
        raise Exception("Posture not working as expected")
                

@allure.step("Checking logs for Encryption Posture")
def read_encryption_posture_logs():
    path = "/home/<USER>/.Zscaler/Logs"
    filenames = os.listdir(path)
    tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
    tray_logs.sort(reverse=True)
    last_logs = path + "/" + tray_logs[0]

    search_strings = ["Disk_encryption_posture_true", "Disk_encryption_posture_false"]
    to_find = search_strings.copy()
    result = {}
    defender = 0

    for line in reversed(open(last_logs).readlines()):
        for string in search_strings:
            if string in line:
                posture_result = line.split()[-1][-1]
                if (posture_result == '1' and string.split('_')[-1] == 'true') or (posture_result == '0' and string.split('_')[-1] == 'false'):
                    result[string] = True
                else:
                    result[string] = False
                    
                search_strings.remove(string)
            
            if len(search_strings) == 0:
                break
          
    if len(search_strings) == 0:
        print("Logs for posture Found")
    else:
        raise Exception("Logs not found")
        
    if result[to_find[0]] and result[to_find[1]]:
        print("Posture working as expected")
    else:
        raise Exception("Posture not working as expected")
        

@allure.step("Checking logs for OS Version Posture") 
def read_os_version_posture_logs():
    path = "/home/<USER>/.Zscaler/Logs"
    filenames = os.listdir(path)
    tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
    tray_logs.sort(reverse=True)
    last_logs = path + "/" + tray_logs[0]

    search_strings = ["OS_version_Ubuntu", "OS_version_false"]
    to_find = search_strings.copy()
    result = {}
    defender = 0

    for line in reversed(open(last_logs).readlines()):
        for string in search_strings:
            if string in line:
                posture_result = line.split()[-1][-1]
                if (posture_result == '1' and string.split('_')[-1] == 'Ubuntu') or (posture_result == '0' and string.split('_')[-1] == 'false'):
                    result[string] = True
                else:
                    result[string] = False
                    
                search_strings.remove(string)
            
            if len(search_strings) == 0:
                break
          
    if len(search_strings) == 0:
        print("Logs for posture Found")
    else:
        raise Exception("Logs not found")
        
    if result[to_find[0]] and result[to_find[1]]:
        print("Posture working as expected")
    else:
        raise Exception("Posture not working as expected")
        

@allure.step("Checking logs for Process posture")
def read_process_posture_logs():
    path = "/home/<USER>/.Zscaler/Logs"
    filenames = os.listdir(path)
    tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
    tray_logs.sort(reverse=True)
    last_logs = path + "/" + tray_logs[0]

    search_strings = ["Process_posture_true", "Process_posture_false"]
    to_find = search_strings.copy()
    result = {}
    defender = 0

    for line in reversed(open(last_logs).readlines()):
        for string in search_strings:
            if string in line:
                posture_result = line.split()[-1][-1]
                if (posture_result == '1' and string.split('_')[-1] == 'true') or (posture_result == '0' and string.split('_')[-1] == 'false'):
                    result[string] = True
                else:
                    result[string] = False
                    
                search_strings.remove(string)
            
            if len(search_strings) == 0:
                break
          
    if len(search_strings) == 0:
        print("Logs for posture Found")
    else:
        raise Exception("Logs not found")
        
    if result[to_find[0]] and result[to_find[1]]:
        print("Posture working as expected")
    else:
        raise Exception("Posture not working as expected")


@allure.step("Checking logs for Cert Posture")
def read_cert_posture_logs():
    path = "/home/<USER>/.Zscaler/Logs"
    filenames = os.listdir(path)
    tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
    tray_logs.sort(reverse=True)
    last_logs = path + "/" + tray_logs[0]
    
    search_strings = ["root_cert_true", "intermediate_cert_true"]
    to_find = search_strings.copy()
    result = {}
    defender = 0

    for line in reversed(open(last_logs).readlines()):
        for string in search_strings:
            if string in line:
                posture_result = line.split()[-1][-1]
                if (posture_result == '1' and string.split('_')[-1] == 'true') or (posture_result == '0' and string.split('_')[-1] == 'false'):
                    result[string] = True
                else:
                    result[string] = False
                    
                search_strings.remove(string)
            
            if len(search_strings) == 0:
                break
          
    if len(search_strings) == 0:
        print("Logs for posture Found")
    else:
        raise Exception("Logs not found")
        
    if result[to_find[0]] and result[to_find[1]]:
        print("Posture working as expected")
    else:
        raise Exception("Posture not working as expected")
        

@allure.step("Placing CERTS in specified directories")
def cert_posture_setup(non_exportable_private_key: bool=False, private_intermediate_ca: bool=False, negative_case: bool=False, reset_certs: bool=False):
    client_cert_path = os.path.join(os.getcwd(), "OS_Linux/resource/CERTS/client/client-inter.pem")
    client_key_path = os.path.join(os.getcwd(), "OS_Linux/resource/CERTS/client/client-inter.key")
    client_cert_unrelated_path = os.path.join(os.getcwd(), "OS_Linux/resource/CERTS/client_unreleated/unreleated_client_cert.pem")
    client_key_unrelated_path = os.path.join(os.getcwd(), "OS_Linux/resource/CERTS/client_unreleated/unreleated_client_cert.key")

    intermediate_ca_path = os.path.join(os.getcwd(), "OS_Linux/resource/CERTS/intermediate/intermediate1.crt")
    root_ca_path = os.path.join(os.getcwd(), "OS_Linux/resource/CERTS/root/rootca.crt")
    
    cert_dest = os.path.join(os.path.expanduser('~'), ".Zscaler/certificates/client.pem")
    key_dest = os.path.join(os.path.expanduser('~'), ".Zscaler/certificates/private/client.key")
    private_cert_dest = "/opt/zscaler/client_cert/client.pem"
    private_key_dest = "/opt/zscaler/private_key/client.key"

    root_cert_dest = "/usr/local/share/ca-certificates/rootca.crt"
    inter_cert_dest = "/usr/local/share/ca-certificates/intermediate1.crt"
    private_inter_cert_dest = "/opt/zscaler/intermediate_ca/intermediate1.crt"

    if reset_certs:
        os.system("sudo rm " + private_cert_dest)
        os.system("sudo rm " + private_key_dest)
        os.system("rm " + cert_dest)
        os.system("rm " + key_dest)

        os.system("sudo rm " + root_cert_dest)
        os.system("sudo rm " + inter_cert_dest)
        os.system("sudo rm " + private_inter_cert_dest)

        os.system("sudo update-ca-certificates --fresh")
        time.sleep(5)
        return


    # Copy Client Certs
    if non_exportable_private_key:
        os.system("sudo rm " + private_cert_dest)
        os.system("sudo rm " + private_key_dest)
        os.system("sudo chmod 700 " + private_key_dest)

        if negative_case:
            os.system("sudo cp " + client_cert_unrelated_path + " " + private_cert_dest)
            os.system("sudo cp " + client_key_unrelated_path + " " + private_key_dest)
        else:
            os.system("sudo cp " + client_cert_path + " " + private_cert_dest)
            os.system("sudo cp " + client_key_path + " " + private_key_dest)
    else:
        os.system("rm " + cert_dest)
        os.system("rm " + key_dest)

        if negative_case:
            os.system("cp " + client_cert_unrelated_path + " " + cert_dest)
            os.system("cp " + client_key_unrelated_path + " " + key_dest)
        else:
            os.system("cp " + client_cert_path + " " + cert_dest)
            os.system("cp " + client_key_path + " " + key_dest)

    # Install Certs
    os.system("sudo cp " + root_ca_path + " " + root_cert_dest)

    if private_intermediate_ca:
        os.system("sudo cp " + intermediate_ca_path + " " + private_inter_cert_dest)
    else:
        os.system("sudo cp " + intermediate_ca_path + " " + inter_cert_dest)

    os.system("sudo update-ca-certificates")
    time.sleep(5)

def check_config_file():
    path = "/home/<USER>/.Zscaler/"
    filenames = os.listdir(path)
    config_file = list(filter(lambda x: ".dat" in x, filenames))
    
    if len(config_file) == 1:
        print("Confif file exists")
    else:
        raise Exception("Config file not found")
        
def remove_config_file():
    path = "/home/<USER>/.Zscaler/"
    filenames = os.listdir(path)
    config_file = list(filter(lambda x: ".dat" in x, filenames))
    
    check_config_file()
    
    os.system("rm " + path + config_file[0])
    time.sleep(5)
    
    try:
        check_config_file()
        print("Config file recovered")
    except:
        raise Exception("Config file not recovered")
    
	
if __name__ == '__main__':
    cert_posture_setup(reset_certs=True)
    cert_posture_setup(non_exportable_private_key=True)
    
    
    
    
    
    
    
    
    
  
========
import os
import datetime
import subprocess
import time
import re
import codecs
import socket
import allure

def open_log_file():
    path = "/home/<USER>/.Zscaler/Logs"
    filename = os.listdir(path)
    print(filename)
    now = datetime.datetime.now()
    fileopen = "ZSTray_" + str(now.year)

    filename.sort(reverse=True)
    for i in filename:
        if re.search(fileopen, i) != None:
            filetoopen = i;
            break;
    print(filetoopen)

    f = open(os.path.join(path, filetoopen), "r")
    text = f.readlines()
    filename = os.listdir(r"/var/log/zscaler/.Zscaler/Logs")
    print(filename)
    now = datetime.datetime.now()
    fileopen = "zsaservice_" + str(now.year)

    filename.sort(reverse=True)
    for i in filename:
        if re.search(fileopen, i) != None:
            filetoopen = i;
            break;
    print(filetoopen)

    filename = os.listdir(r"/var/log/zscaler/.Zscaler/Logs")
    print(filename)
    now = datetime.datetime.now()
    fileopen = "zstunnel_" + str(now.year)

    filename.sort(reverse=True)
    for i in filename:
        if re.search(fileopen, i) != None:
            filetoopen = i;
            break;

    print(filetoopen)


def check_log_files():
    logs_dir = r"/var/log/zscaler/.Zscaler/Logs"
    if os.path.exists(logs_dir):
        files_list_before = os.listdir(logs_dir)
        files_count_before = len(files_list_before)
        print("Number of files before clearing logs : {}".format(files_count_before))
        print("Calling macro to clear the logs in ZApp")
        run_macro("Linux_Ubuntu_ZAPP_Automation_Macros/Linux_Zapp_UI_Verifications/ZAPP_Clear_Logs")
        files_list_after = os.listdir(logs_dir)
        files_count_after = len(files_list_after)
        print("Number of files after clearing logs : {}".format(files_count_after))
        if files_count_after <= files_count_before:
            print("Logs were cleared properly from ZAPP clear logs")
            return 1
        else:
            raise Exception("Logs were not cleared")
    else:
        raise Exception("Logs directory does not exist")


def read_temp_file():
    file_name = "Temporary"
    home_dir = os.path.expanduser("~")
    documents_dir = os.path.join(home_dir, "Downloads")
    file_path = os.path.join(documents_dir, file_name)
    if os.path.exists(file_path):
        with open(file_path) as temp:
            temp_var = temp.readline()
    else:
        raise Exception("Temporary file is not created")
    os.remove(file_path)
    return temp_var


def check_traffic_bytes():
    print("Capturing the sent bytes on Zapp before sending traffic")
    run_macro("Linux_Ubuntu_ZAPP_Automation_Macros/Linux_OTP_And_Traffic/ZAPP_Traffic_Bytes")
    bytes_list = read_temp_file().split()
    if bytes_list[1][0] == "M":
        bytes_before = float(bytes_list[0]) * 1024
    elif bytes_list[1][0] == "G":
        bytes_before = float(bytes_list[0]) * 1024 * 1024
    else:
        bytes_before = float(bytes_list[0])
    print("{} KB seen in sent bytes on ZAPP before sending traffic".format(bytes_before))
    print("Send traffic using packet sender for 5 seconds")
    run_macro("Linux_Ubuntu_ZAPP_Automation_Macros/Linux_OTP_And_Traffic/Packet_Sender_Traffic")
    print("Capturing the sent bytes on Zapp after sending traffic")
    run_macro("Linux_Ubuntu_ZAPP_Automation_Macros/Linux_OTP_And_Traffic/ZAPP_Traffic_Bytes")
    bytes_list_after = read_temp_file().split()
    if bytes_list_after[1][0] == "M":
        bytes_after = float(bytes_list_after[0]) * 1024
    elif bytes_list_after[1][0] == "G":
        bytes_after = float(bytes_list_after[0]) * 1024 * 1024
    else:
        bytes_after = float(bytes_list_after[0])
    print("{} KB seen in sent bytes on Zapp after sending traffic".format(bytes_after))
    diff_bytes = bytes_after - bytes_before
    print("Difference between the bytes is {}".format(diff_bytes))
    if diff_bytes > 5000:
        print("Traffic went through the tunnel and bytes were increased")
    else:
        raise Exception("Bytes were not incremented")


def check_pac_file():
    file_name = "Temporary"
    home_dir = os.path.expanduser("~")
    documents_dir = os.path.join(home_dir, "Downloads")
    file_path = os.path.join(documents_dir, file_name)
    if os.path.exists(file_path):
        with open(file_path) as pac:
            pac_url = pac.readline()
            print("Pac url is {}".format(pac_url))
    else:
        raise Exception("Temporary file is not created")
    os.remove(file_path)
    home_dir = os.path.expanduser("~")
    download_dir = os.path.join(home_dir, "Downloads")
    split_list = pac_url.split("/")
    pac_name = split_list.pop()
    pac_path = os.path.join(download_dir, pac_name).strip()
    if os.path.exists(pac_path):
        print("Pac file {} is found in the location {}".format(pac_name, download_dir))
        os.remove(pac_path)
        return 1
    else:
        raise Exception("Pac file is not found")


def kill_chrome():
    os.system('ps axf | grep -E \'chrome|firefox\' | grep -v grep | awk \'{print "kill " $1 }\' | sh')


def check_coredump():
    current_time = datetime.datetime.now()
    date_today = str(current_time.year) + "-" + str(current_time.month) + "-" + str(current_time.day)
    date_before = str(current_time.year) + "-" + str(current_time.month) + "-" + str(current_time.day - 1)
    # print(date_before)
    call = subprocess.Popen("coredumpctl list --since={}".format(date_today), shell=True, stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    result = str(call.communicate())
    if re.search("No", result) != None:
        print("No coredumps found")

    else:
        result = result.split('\n')
        result = result[0].split()
        tray = 0
        tunnel = 0
        service = 0
        for line in result:
            if re.search("ZSTray", line) != None:
                tray = 1
            if re.search("zstunnel", line) != None:
                tunnel = 1
            if re.search("zsaservice", line) != None:
                service = 1

        if tray == 1:
            print("Tray dump is seen")
        if tunnel == 1:
            print("Tunnel dump is seen")
        if service == 1:
            print("Service dump is seen")

        raise Exception("Coredumps are seen")

def check_interface():
    call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")
    interface = 0
    dns = 0
    domain = 0
    dnsdomain = 0
    for line in result:
        if re.search("zcctun0",line):
            interface = 1
        if re.search("**********",line):
            dns = 1
        if re.search("~.",line):
            domain=1
        if re.search("shashank.com",line):
            dnsdomain=1

    if interface :
        print("Tun interface present")
    else :
        raise Exception("Tun interface not present")

def check_dnsserver():
    call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())

    result = result.split("\n")
    interface = 0
    dns = 0
    domain = 0
    dnsdomain = 0
    for line in result:
        if re.search("zcctun0",line):
            interface = 1
        if re.search("**********",line):
            dns = 1
        if re.search("~.",line):
            domain=1
        if re.search("shashank.com",line):
            dnsdomain=1

    if dns:
        print("Dns server present")
    else :
        raise Exception("Dns server not present")

def check_domain():
    call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())

    result = result.split("\n")
    interface = 0
    dns = 0
    domain = 0
    dnsdomain = 0
    for line in result:
        if re.search("zcctun0",line):
            interface = 1
        if re.search("**********",line):
            dns = 1
        if re.search("~.",line):
            domain=1
        if re.search("shashank.com",line):
            dnsdomain=1

    if domain:
        print("Catch all present")
    else :
        raise Exception("Catch all not present")

def check_zpasearchdomain():
    call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())

    result = result.split("\n")
    interface = 0
    dns = 0
    domain = 0
    dnsdomain = 0
    for line in result:
        if re.search("zcctun0",line):
            interface = 1
        if re.search("**********",line):
            dns = 1
        if re.search("~.",line):
            domain=1
        if re.search("shashank.com",line):
            dnsdomain=1

    if dnsdomain:
        print("Dns search domain present")
    else:
        raise Exception("Dns search domain not present")

def check_zpaapplication():
    call = subprocess.Popen("resolvectl flush-caches" , shell = True , stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    call = subprocess.Popen("resolvectl query abc.com", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")

    zpa = 0
    zcctun0=0
    for line in result:
        if re.search("100.64",line):
            zpa=1
        if re.search("zcctun0",line):
            zcctun0=1

    if zpa and zcctun0:
        print("Zpa app ")
    else :
        raise Exception("Zpa application .Request doesnot go to zcctun0 interface")

def check_normalapplication():
    call = subprocess.Popen("resolvectl flush-caches" , shell = True , stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    call = subprocess.Popen("resolvectl query amazon.com", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")

    zpa = 0
    zcctun0=0
    for line in result:
        if re.search("100.64",line):
            zpa=1
        if re.search("zcctun0",line):
            zcctun0=1

    if zpa == 0 and zcctun0 == 1 :
        print("Normal Application going to zcctun0 interface")
    else :
        raise Exception("Normal Application.Request doesnot go to zcctun0 interface")


def check_pulseapplication_zcc():
    call = subprocess.Popen("resolvectl flush-caches", shell = True , stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    call = subprocess.Popen("resolvectl query corp.zscaler.com", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")

    zpa = 0
    zcctun0=0
    for line in result:
        if re.search("100.64",line):
            zpa=1
        if re.search("zcctun0",line):
            zcctun0=1

    if zpa == 0 and tun0 == 1 :
        print("Pulse Application going to tun0 interface")
    else :
        raise Exception("Request doesnot go to zcctun0 interface")

def check_anyconnectapplication():
    call = subprocess.Popen("resolvectl flush-caches", shell = True , stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    call = subprocess.Popen("resolvectl query ubuntu.kevaldoshi.com", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")

    ciscoapp = 0
    cscotun0=0
    for line in result:
        if re.search("192.168.100",line):
            cscotun0=1
        if re.search("cscotun0",line):
            ciscoapp=1

    if ciscoapp == 1 and cscotun0 == 1 :
           print("Anyconnect Application going to cscotun0 interface")
    else :
        raise Exception("Anyconnect application doesnot go to cscotun0 interface")

def anyconnect_check_interface():
    call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")
    interface = 0
    dns = 0
    domain = 0
    dnsdomain = 0
    for line in result:
        if re.search("cscotun0",line):
            interface = 1

    if interface :
        print("cscotun0 interface present")
    else :
        raise Exception("cscotun0 interface not present")

def pulse_check_interface():
    call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")
    interface = 0
    dns = 0
    domain = 0
    dnsdomain = 0
    for line in result:
        if re.search("tun0",line):
            interface = 1

    if interface :
        print("tun0 interface present")
    else :
        raise Exception("tun0 interface not present")

def anyconnect_check_dns():
    call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")
    interface = 0
    dns = 0
    domain = 0
    dnsdomain = 0
    for line in result:
        if re.search("cscotun0",line):
            interface = 1
        if re.search("**************",line):
            dns = 1

    if interface and dns :
        print("cscotun0 dns present")
    else :
        raise Exception("cscotun0 dns not present")
        
def pulse_check_dns():
    call = subprocess.Popen("resolvectl", shell=True, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    result = str(call.communicate())
    result = result.split("\n")
    interface = 0
    dns = 0
    domain = 0
    dnsdomain = 0
    for line in result:
        if re.search("tun0",line):
            interface = 1
        if re.search("***********",line):
            dns = 1

    if interface and dns :
        print("cscotun0 dns present")
    else :
        raise Exception("cscotun0 dns not present")

def run_chrome_tab():
    browser = r"/usr/bin/google-chrome"
    proc = subprocess.Popen([browser, "--disable-gpu", "--disable-software-rasterizer"], stderr=subprocess.DEVNULL)
    # "--ignore-certificate-errors"
    time.sleep(10)


def report_step(step_title):
    with allure.step(step_title):
        pass
def run_macro(step_title, macro_name,retry=1):
    # Initializing variables
    with allure.step(step_title):

        home_dir = os.path.expanduser("~")
        log_dir = os.path.join(home_dir, "Downloads")
        browser = r"/usr/bin/google-chrome"
        html_path = os.path.join(log_dir, "ui.vision.html")
        macro = macro_name
        timeout_seconds = 360

        # Create log file
        log_name = 'log_' + datetime.datetime.now().strftime('%m-%d-%Y_%H_%M_%S') + '.txt'
        log_path = os.path.join(log_dir, log_name)

        # Initialize the macro path as per UI Vision syntax
        macro_path = r'file://' + html_path + r'?direct=1&macro=' + macro + r'&savelog=' + log_name + r'&loadmacrotree=1&storage=browser'

        print("Log file used is {}".format(log_name))

        # Call UI vision to run the macro
        time.sleep(2)
        proc = subprocess.Popen([browser, "--disable-gpu", "--disable-software-rasterizer", macro_path], stderr=subprocess.DEVNULL)

        # Continuously check the log file for existence
        status_runtime = 1

        while not os.path.exists(log_path) and status_runtime < timeout_seconds:
            # print("Waiting for macro to finish, seconds=%d" % status_runtime)
            time.sleep(1)
            status_runtime += 1

        # Read the log file whether status is successfull
        if status_runtime < timeout_seconds:
            with open(log_path) as f:
                status_text = f.readline()
                full_log = f.read()
                os.remove(log_path)

            if status_text.find('Status=OK') != -1:
                print("Macro {} completed successfully".format(macro))
                print(full_log)
                time.sleep(2)
                return 1
            else:
                print("Macro {} failed with error {}".format(macro, status_text))
                print(full_log)
                time.sleep(2)
                raise Exception("Macro failed to execute")
        else:
            print("Macro did not complete within given time: {}".format(timeout_seconds))
            time.sleep(2)


            if retry==1 :
                print("Retrying")
                run_macro(macro_name,0);
            else:
                raise Exception("Macro did not execute within time")

@allure.step("Check if VM is joined to a domain")
def domain_join_check():
    hostname = socket.gethostname()
    fqdn = socket.getfqdn()
    full_name = 0
    host_name = 0

    if hostname == "ubuntu22-desktop" or hostname == "ubuntu22-desktop1.zccqa.onmicrosoft.com":
        host_name = 1
        
    if fqdn == "ubuntu22-desktop1.zccqa.onmicrosoft.com":
        full_name = 1

    if host_name and full_name:
        print("VM is joined to domain")
    else :
        raise Exception("Not joined to domain")


@allure.step("Checking logs for posture result")
def read_posture_logs(posture_name: str, expected_result: bool):
    path = "/home/<USER>/.Zscaler/Logs"
    filenames = os.listdir(path)
    tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
    tray_logs.sort(reverse=True)
    last_logs = path + "/" + tray_logs[0]

    found_logs = False

    for line in reversed(open(last_logs).readlines()):
        if posture_name in line:
            posture_result = line.split()[-1][-1]
            found_logs = True
            if (posture_result == '1' and expected_result == True) or (posture_result == '0' and expected_result == False):
                print("Posture is working as expected")
                break
            else:
                raise Exception("Actual posture result is different than expected result")
            
    if not found_logs:
        raise Exception("Logs not found for given posture name")

        
@allure.step("Checking logs for Defender Posture")
def read_defedner_posture_logs():
    path = "/home/<USER>/.Zscaler/Logs"
    filenames = os.listdir(path)
    tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
    tray_logs.sort(reverse=True)
    last_logs = path + "/" + tray_logs[0]
    
    search_strings = ["MS_defender_posture_true", "MS_defender_posture_false"]
    to_find = search_strings.copy()
    result = {}
    defender = 0

    for line in reversed(open(last_logs).readlines()):
        for string in search_strings:
            if string in line:
                posture_result = line.split()[-1][-1]
                if (posture_result == '1' and string.split('_')[-1] == 'true') or (posture_result == '0' and string.split('_')[-1] == 'false'):
                    result[string] = True
                else:
                    result[string] = False
                    
                search_strings.remove(string)
            
            if len(search_strings) == 0:
                break
          
    if len(search_strings) == 0:
        print("Logs for posture Found")
    else:
        raise Exception("Logs not found")
        
    if result[to_find[0]] and result[to_find[1]]:
        print("Posture working as expected")
    else:
        raise Exception("Posture not working as expected")
                

@allure.step("Checking logs for Encryption Posture")
def read_encryption_posture_logs():
    path = "/home/<USER>/.Zscaler/Logs"
    filenames = os.listdir(path)
    tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
    tray_logs.sort(reverse=True)
    last_logs = path + "/" + tray_logs[0]

    search_strings = ["Disk_encryption_posture_true", "Disk_encryption_posture_false"]
    to_find = search_strings.copy()
    result = {}
    defender = 0

    for line in reversed(open(last_logs).readlines()):
        for string in search_strings:
            if string in line:
                posture_result = line.split()[-1][-1]
                if (posture_result == '1' and string.split('_')[-1] == 'true') or (posture_result == '0' and string.split('_')[-1] == 'false'):
                    result[string] = True
                else:
                    result[string] = False
                    
                search_strings.remove(string)
            
            if len(search_strings) == 0:
                break
          
    if len(search_strings) == 0:
        print("Logs for posture Found")
    else:
        raise Exception("Logs not found")
        
    if result[to_find[0]] and result[to_find[1]]:
        print("Posture working as expected")
    else:
        raise Exception("Posture not working as expected")
        

@allure.step("Checking logs for OS Version Posture") 
def read_os_version_posture_logs():
    path = "/home/<USER>/.Zscaler/Logs"
    filenames = os.listdir(path)
    tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
    tray_logs.sort(reverse=True)
    last_logs = path + "/" + tray_logs[0]

    search_strings = ["OS_version_Ubuntu", "OS_version_false"]
    to_find = search_strings.copy()
    result = {}
    defender = 0

    for line in reversed(open(last_logs).readlines()):
        for string in search_strings:
            if string in line:
                posture_result = line.split()[-1][-1]
                if (posture_result == '1' and string.split('_')[-1] == 'Ubuntu') or (posture_result == '0' and string.split('_')[-1] == 'false'):
                    result[string] = True
                else:
                    result[string] = False
                    
                search_strings.remove(string)
            
            if len(search_strings) == 0:
                break
          
    if len(search_strings) == 0:
        print("Logs for posture Found")
    else:
        raise Exception("Logs not found")
        
    if result[to_find[0]] and result[to_find[1]]:
        print("Posture working as expected")
    else:
        raise Exception("Posture not working as expected")
        

@allure.step("Checking logs for Process posture")
def read_process_posture_logs():
    path = "/home/<USER>/.Zscaler/Logs"
    filenames = os.listdir(path)
    tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
    tray_logs.sort(reverse=True)
    last_logs = path + "/" + tray_logs[0]

    search_strings = ["Process_posture_true", "Process_posture_false"]
    to_find = search_strings.copy()
    result = {}
    defender = 0

    for line in reversed(open(last_logs).readlines()):
        for string in search_strings:
            if string in line:
                posture_result = line.split()[-1][-1]
                if (posture_result == '1' and string.split('_')[-1] == 'true') or (posture_result == '0' and string.split('_')[-1] == 'false'):
                    result[string] = True
                else:
                    result[string] = False
                    
                search_strings.remove(string)
            
            if len(search_strings) == 0:
                break
          
    if len(search_strings) == 0:
        print("Logs for posture Found")
    else:
        raise Exception("Logs not found")
        
    if result[to_find[0]] and result[to_find[1]]:
        print("Posture working as expected")
    else:
        raise Exception("Posture not working as expected")


@allure.step("Checking logs for Cert Posture")
def read_cert_posture_logs():
    path = "/home/<USER>/.Zscaler/Logs"
    filenames = os.listdir(path)
    tray_logs = list(filter(lambda x: "ZSTray_" in x, filenames))
    tray_logs.sort(reverse=True)
    last_logs = path + "/" + tray_logs[0]
    
    search_strings = ["root_cert_true", "intermediate_cert_true"]
    to_find = search_strings.copy()
    result = {}
    defender = 0

    for line in reversed(open(last_logs).readlines()):
        for string in search_strings:
            if string in line:
                posture_result = line.split()[-1][-1]
                if (posture_result == '1' and string.split('_')[-1] == 'true') or (posture_result == '0' and string.split('_')[-1] == 'false'):
                    result[string] = True
                else:
                    result[string] = False
                    
                search_strings.remove(string)
            
            if len(search_strings) == 0:
                break
          
    if len(search_strings) == 0:
        print("Logs for posture Found")
    else:
        raise Exception("Logs not found")
        
    if result[to_find[0]] and result[to_find[1]]:
        print("Posture working as expected")
    else:
        raise Exception("Posture not working as expected")
        

@allure.step("Placing CERTS in specified directories")
def cert_posture_setup():
    client_cert_path = "/home/<USER>/Documents/CERTS/client/client-inter.pem"
    client_key_path = "/home/<USER>/Documents/CERTS/client/client-inter.key"
    cert_dest = "/home/<USER>/.Zscaler/certificates/"
    key_dest = "/home/<USER>/.Zscaler/certificates/private/"

    os.system('cp ' + client_cert_path + ' ' + cert_dest)
    time.sleep(2)
    os.system('cp ' + client_key_path + ' ' + key_dest)
    time.sleep(2)

def check_config_file():
    path = "/home/<USER>/.Zscaler/"
    filenames = os.listdir(path)
    config_file = list(filter(lambda x: ".dat" in x, filenames))
    
    if len(config_file) == 1:
        print("Confif file exists")
    else:
        raise Exception("Config file not found")
        
def remove_config_file():
    path = "/home/<USER>/.Zscaler/"
    filenames = os.listdir(path)
    config_file = list(filter(lambda x: ".dat" in x, filenames))
    
    check_config_file()
    
    os.system("rm " + path + config_file[0])
    time.sleep(5)
    
    try:
        check_config_file()
        print("Config file recovered")
    except:
        raise Exception("Config file not recovered")
    
	
if __name__ == '__main__':
    cert_posture_setup()
    
    
    
    
    
    
    
    
    
  
>>>>>>>> origin/develop-pytest:OS_Linux/lib/ZCC_Lib.py

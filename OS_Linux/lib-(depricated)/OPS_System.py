################################################################
#                       SYSTEM_OPS.PY                          #
#                                                              #
# AUTHOR : SAHIL KUMAR (<EMAIL>)               #
# CREDITS : SRINIVASA BHASWANTH, ABHIMANYU SHARMA              #
#                                                              #
################################################################
'''
Last Changes Made : 25/05/2022 by <PERSON>hil
Details : Added Installed Zscaler Root CA cert support

'''
import subprocess,os,time
import socket
import OpenSSL,ssl
from datetime import date
import shutil 
from zipfile import ZipFile
import os.path
from threading import Thread
import sys
import logging
import psutil
#import Constants
#from log_ops import log_ops
from common_lib import Constants
from common_lib.log_ops import log_ops
import allure
import json
import platform
import dns.resolver
from datetime import datetime
import pytz
if platform.uname()[0]=="windows":
    import winreg

class ThreadWithReturnValue(Thread):
        '''
        This class is a subclass created to get return value from threads
        By default threads dont send return value
        '''
        def __init__(self, group=None, target=None, name=None,
                 args=(), kwargs={}, Verbose=None):
            Thread.__init__(self, group, target, name, args, kwargs)
            self._return = None
        def run(self):
            print(type(self._target))
            if self._target is not None:
                self._return = self._target(*self._args,
                                                    **self._kwargs)
        def join(self, *args):
            Thread.join(self, *args)
            return self._return

class Sys_Ops:
    def __init__(self,logger=None, cloud=None):
        self._logger = (logger if logger else self.Initialize_Logger("Sys_Ops.log", Log_Level="DEBUG"))
        self._os_version = Constants.os_version
        if self._os_version=="Windows":
            try:
                import winreg
            except:
                pass
        self.WinFWRuleName = []
        self.firewallRuleStringMac = []
        self.CONST = Constants.Utils
        self.cloud=cloud
    
    # --------------------------------------------------------------------------------------------------------------------------
    def Initialize_Logger(self, 
    Log_File_Name,                      # (str)     Name of the Log file to be created 
    Log_Level,                          # (str)     Level of logging (INFO/DEBUG/WARNING/CRICITCAL etc)
    console_handle_needed=True          # (bool)    print on command line if True only
    ):
        """
        Target: Create Logger object.
        Work Flow
        ----------
            1. Create a logger object.
            2. Create a console handler if required
                - console handler is used to print required info on command line window.
            3. Create a file handler -> mandatory
                - Make sure 'Logs' folder exists, as this file handler will be created in this folder only.
                - file handler is used to send all information to a log file in specified folder.
            4. Set the debug level (INFO/DEBUG/WARNING/CRITICAL etc). Default: INFO
            5. Specify how the logging must happen (with/without datetime/loglevel/username)
            6. Add all created handlers to the logger instance created in step 1.
            7. return the logger instance.
        """
        # 1, 2 & 3
        self.Logs_Directory = os.path.join(os.path.abspath(os.path.dirname(os.path.abspath("Automation.py"))), "Logs")
        logger = logging.getLogger(Log_File_Name)
        if console_handle_needed: console_handler = logging.StreamHandler()
        if not os.path.exists(self.Logs_Directory): os.mkdir(self.Logs_Directory)
        file_handler = logging.FileHandler(os.path.join(self.Logs_Directory, Log_File_Name))

        # 4
        if Log_Level:
            logger.setLevel(eval("logging.{}".format(Log_Level)))
            if console_handle_needed: console_handler.setLevel(eval("logging.{}".format(Log_Level)))
            file_handler.setLevel(eval("logging.{}".format(Log_Level)))

        # 5
        if console_handle_needed: console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))

        # 6
        if console_handle_needed: logger.addHandler(console_handler)
        logger.addHandler(file_handler)
        # 7
        return logger

    #----------------------------------------------------------------------------------------------------

    def calling_Command(self,
    command,                # (str), command to be ran like curl or ping
    application=None,       # (str), application to be accessed via command
    fetchIP=False,          # (bool), decides to fetch IP of app or not
    powershell = False      # (bool), decides if command is to be ran on powershell only in case of windows, give true if command is powershell command    
    ):
        '''
        Flow:  ------------------------------
            1. For now it does curl or ping to the given application
            2. Then it fetches the ip of the given application
            3. Returns the result of curl or ping command and ip of the application
        '''
        self._logger.info('COMMAND : {}'.format(command))
        try:
            if not powershell:
                p1 = subprocess.Popen(command,shell=True,stdout = subprocess.PIPE,stderr=subprocess.PIPE)
                result=p1.communicate()
            else:
                self._logger.info("POWERSHELL COMMAND !")
                p1 = subprocess.Popen(['powershell.exe',command],shell=True,stdout = subprocess.PIPE,stderr=subprocess.PIPE)
                result=p1.communicate()
            result=list(result)
            for i in range(len(result)):result[i]=result[i].decode('ascii')
        except Exception as E:
            self._logger.error("ERROR WITH TERMINAL COMMAND :: {}".format(E))
            raise Exception("ERROR WITH TERMINAL COMMAND :: {}".format(E))
        if not fetchIP:
            self._logger.info(f"Returned Result")
            return result
        else:
            IP = self.fetch_ip(application)

            self._logger.info("Returned Result & IP")
            return result,IP
    
    def fetch_ip(self,
    application):
        IP=''
        self._logger.info(f"Fetching IP for  : {application}")
        if 'https://' in application:       
                application = application.split('//')[1]
        try:
            IP = socket.gethostbyname(application)
        except socket.gaierror:
            IP = None
        except socket.getaddrinfo:
            IP = None
        except Exception as E:
            raise Exception('No idea what happened : {}'.format(E))
        finally:return IP

    
    #----------------------------------------------------------------------------------------------------
    
    def get_certificate(self,host, port=443, timeout=10, proxy=False):
        '''
        Flow : ------------------------------
            This Function hits traffic onto given host and gets its SSL certificate
        '''
        self._logger.info("Fetching certificate for : {} ".format(host))
        if proxy:
            self._logger.info("Going Via Proxy")
            CONNECT = "CONNECT {}:{} HTTP/1.0\r\nConnection: close\r\n\r\n".format(host,443)
            conn = socket.socket()
            proxy = ('127.0.0.1',9000)
            conn.connect(proxy)
            conn.send(str.encode(CONNECT))
            conn.recv(4096)
            context = ssl.SSLContext(ssl.PROTOCOL_SSLv23)
        else:
            self._logger.info("Not Going Via Proxy")
            context = ssl._create_unverified_context()      #this line is edited from previous code, in previous code context was -> ssl.create_default_context(), but at times zscaler cert is causing ssl verification handshake failed<(_ssl.c:726)>, so we changed context to ssl._create_unverified_context()      
            conn = socket.create_connection((host, port))
        sock = context.wrap_socket(conn, server_hostname=host)
        sock.settimeout(timeout)
        print('Host is {}'.format(host))
        try:
            der_cert = sock.getpeercert(True)
        finally:
            sock.close()
        return ssl.DER_cert_to_PEM_cert(der_cert)
    
    #----------------------------------------------------------------------------------------------------
    def validateRestartService(self):
        from common_lib.GUI import ZCC
        zccObj = ZCC(logger=self._logger)
        if self._os_version=="MAC":
            processName="ZscalerTunnel"
        else:
            processName = "ZSATunnel.exe"
        for process in psutil.process_iter():
            with process.oneshot():
                if processName in process.name():previousTunnelID=process.pid
        
        zccObj.Restart_Service()
        for process in psutil.process_iter():
            with process.oneshot():
                if processName in process.name():afterTunnelID=process.pid

        if previousTunnelID==afterTunnelID:raise Exception("ERROR :: TunnelPid didnt changed on restart service")
        else:
            self._logger.info(f"PreviousTunnelPID : {previousTunnelID}, CurrentTunnelPID : {afterTunnelID}")
            self._logger.info("Restart service validated")
        
        

    #----------------------------------------------------------------------------------------------------
    def Download_Files(self,
    url,
    fileType
    ):
        self._logger.info("Download URL : {}".format(url))
        today = date.today()
        fileName = os.getcwd()+'/'+today.strftime("%b-%d-%Y")+fileType
        command = "curl {} --output {}".format(url,fileName)
        downloadResult = self.calling_Command(command=command)
        print(downloadResult[-1])
    
    #----------------------------------------------------------------------------------------------------

    @allure.step("Access Application")
    def Access_Application(self,
    application,            # (str)     application to be accessed
    accessType='curl',      # (str)     defines whether to curl or ping application, by default curl
    accessFlag=True,        # (bool)    defines whether app should be accessed or not, by default true
    proxy=None,             # (str/bool) defines proxy via which traffic should be sent/in case of cert check via proxy give True  
    isZcert=None,           # (bool)   Give True if cert should be of zscaler, else give false, give none if cert is not to be checked
    SEAccessApp_Block=False, # (bool)    When ZCC is installed in SE mode the Applications/Internet is accessable or not.
    pingSize = None,         # (int) Size of ping/icmp
    ip = None,               # (str) ip of application to access - Used for DNS access
    proxyAgent = 'Zscaler',  # (str) can take values to check if proxy agent is fine.
    sourcePort=None,          # (str) For Curl Request can be bind to local port 
    enforce_v4=False,       # (bool) set it to True, to enforce '-4' switch in curl. Mainly used for dual stack machines which have ipv6 DNS server and user wants to access application via ipv4 only
    dns_server_change = 'None'   # (str) you can give any dns server IP
    ):  
        '''
        Flow : ------------------------------
            1. We check if we want to validate certificate or just hit normal traffic
            2. If validate certificate:
                2.1 We use get_certificate which returns the certificate for given application(host) 
                2.2 If you are sending traffic via proxy and want to validate certifciate, give proxy=True
                2.2 Then according to the value of isZcert(true/false), we check the cert
                    2.2.1 If isZscert==True -> Zscaler Certificate is expected
                    2.2.2 If isZscert==False -> Non Zscaler Certificate is expected
            3. Else we make command according to given traffic type ie. curl or ping
            4. If you want to send traffic via proxy, give proxy in string format like 127.0.0.1:9000
            2. THen we validate results,
                2.1 If curl -> we see if there is 200 OK in response
                2.2 If ping -> we determine packet loss % 
        '''
        if isZcert!=None:                   #Should be True or False
            self._logger.info("Checking for Certificate\n")
            certificate = self.get_certificate(application,proxy=proxy)
            x509 = OpenSSL.crypto.load_certificate(OpenSSL.crypto.FILETYPE_PEM, certificate)
            issuer = dict(x509.get_issuer().get_components())
            print(issuer)
            try:issuer = str(issuer['CN'])
            except:issuer = str(issuer[b'CN'])
            self._logger.info('CN in Issuer is {}, {}'.format(issuer,type(issuer)))

            if (isZcert==True and 'Zscaler' in issuer) or (isZcert==False and 'Zscaler' not in issuer):
                self._logger.info('Matching Certificate Found!')
            if isZcert==True and 'Zscaler' not in issuer:
                raise Exception("Zscaler Certificate was expected for {}, but got {}".format(application,issuer))
            if isZcert==False and 'Zscaler' in issuer:
                raise Exception("Zscaler Certificate was NOT expected for {}, but got {}".format(application,issuer))
        else:
            if 'curl' in accessType:
                if enforce_v4:
                    if proxy:                   #curl -v domain -x 127.0.0.1:9000
                        if proxy == True:
                            command = 'curl -LI -4 {} -x 127.0.0.1:9000'.format(application)
                        else:
                            command = 'curl -LI -4 {} -x {}'.format(application,proxy)
                    else:
                        command = 'curl -LI -k -4 {}'.format(application)
                else:
                    if proxy:                   #curl -v domain -x 127.0.0.1:9000
                        if proxy == True:
                            command = 'curl -LI {} -x 127.0.0.1:9000'.format(application)
                        else:
                            command = 'curl -LI {} -x {}'.format(application,proxy)
                    else:
                        command = 'curl -LI -k {}'.format(application)
                    
                if sourcePort!=None:
                    command=command+" --local-port {}".format(sourcePort)
                
            if 'ping' in accessType or 'icmp' in accessType:
                if self._os_version=="MAC":
                    command = 'ping -c 5 {}'.format(application)
                    if pingSize != None:
                        command = f'{command} -s {pingSize}'
                else:
                    command = 'ping {}'.format(application)
                    if pingSize != None:
                        command = f'{command} -l {pingSize}'
            if 'dns' in accessType or 'nslookup' in accessType:
                if dns_server_change != None:
                    command = f'nslookup {application} {dns_server_change}'
                else:
                    command = f'nslookup {application}'

            result = self.calling_Command(command=command,fetchIP=False)
            self._logger.info("RESULT : \n{}\n*************************\n{}".format(result[0],result[1]))
            if 'curl' in accessType:
                if SEAccessApp_Block == True:
                    if ("HTTP/1.1 403" in result[0] or 'SSL_ERROR_SYSCALL' in result[0] or "SSL/TLS connection failed" in result[0] or len(result[0]) == 0):
                        self._logger.info('Access to Application {} blocked by Strict Enforcement!'.format(application))
                    else:
                        line = 'Request to Application {} made in Strict Enforcement mode::FAIL'.format(application)
                        self._logger.error(line)
                        raise Exception(line)
                elif accessFlag:
                    if "200 OK" in result[0] or "HTTP/2 200" in result[0] or "HTTP/1.1 200" in result[0]:
                        if proxy:
                            if proxyAgent == None:
                                self._logger.info("Proxy check not required! Skipping.")
                                pass

                            #Proxy-Agent: Ztunnel/1.0
                            if f"Proxy-Agent: {proxyAgent}" in result[0]:
                                self._logger.info('App accessed Successfully')
                            else:
                                raise Exception("ERROR :: Proxy Expected, but not found in curl output")
                        else:
                            self._logger.info('App accessed Successfully')
                    elif len(result[1])!=0:
                        self._logger.info(result[1])
                        raise Exception("ERROR :: Access Flag was True, but no result fetched from command")
                    else:
                        raise Exception("ERROR :: 200 OK not found in curl request result")
                else:
                    if len(result[1])==0 or "HTTP/1.1 301 Moved Permanently" in result[1] or "Failed to connect" in result[1] or "Empty reply from server" in result[1] or "SSL_ERROR_SYSCALL" in result[1] or "Connection timed out" in result[1] or 'Could not resolve host:' in result[1] or 'CONNECT tunnel failed, response 403' in result[1]:
                        self._logger.info("App not accessed, case passed")
                    else:
                        raise Exception("ERROR :: Access Flag was False, but result fetched from command")

            if 'icmp' in accessType or 'ping' in accessType:
                FailedPackets=[]
                if self._os_version=="MAC":
                    result = result[0]
                    result= result.split('\n')
                    for item in result[1:]:
                        if 'Request timeout for icmp_seq' in item:
                            FailedPackets.append(item)
                        if 'transmitted' and 'received' in item:
                            item = item.split(',')[-1]
                            packetLoss = int(item.split('.')[0])
                        else:
                            pass
                else:
                    result = result[0]
                    result = result.split('\r\n')
                    for val in result:
                        if 'Request timed out' in val:
                            FailedPackets.append(val)
                        if 'Packets:' in val:
                            self._logger.info(val)
                            val = val.split(',')[-2][11:-1]
                            packetLoss = int(val.split('%')[0])
                        else:
                            pass
                if accessFlag:
                    if packetLoss>0:
                        raise Exception('Error ::ICMP PACKET LOST DETECTED :')
                    else:
                        self._logger.info("ICMP PACKET NOT LOST, LOST % : {}".format(packetLoss))
                else:
                    if packetLoss<100:
                        self._logger.error('ICMP Packet Recieved , LOSS % : {}'.format(packetLoss))
                        raise Exception('ERROR :: ICMP PACKET RECIEVED DETECTED')
                    else:
                        self._logger.info('ICMP PACKETS NOT RECIEVED, LOSS % : {}'.format(packetLoss))
            
            if 'dns' in accessType or 'nslookup' in accessType:
                self._logger.info(f"RESULT : \n{result[0]}\n*************************\n{result[1]}")

                
                toSearch = f'Name:\t{application}'

                if ip != None:
                    toSearch = f'{toSearch}\nAddress: {ip}'

                self._logger.info(f'Searching for {toSearch}')

                if toSearch.lower().replace(' ','').replace('\t','') in result[0].lower().replace(' ',''):
                    if accessFlag == True:
                        self._logger.info(f'DNS access to {application} successful!')
                    else:
                        line = f'DNS access to {application} successful, but it should not be accessable'

                        self._logger.error(line)
                        raise Exception(line)
                else:
                    if accessFlag == True:
                        line = f'DNS access to {application} not successful, but it should be accessable'

                        self._logger.error(line)
                        raise Exception(line)
                    else:
                        self._logger.info(f'DNS access to {application} not successful!')

    #----------------------------------------------------------------------------------------------------
    def Access_IP_Zscaler(self,
        cloudName,
        FailureExpected=False,
        proxy=False,
        retryCount=0
        ):
        returnText = self.calling_Command(command="curl -v https://ip.zscaler.com" if not proxy else "curl -v https://ip.zscaler.com -x 127.0.0.1:9000")
        validateString = 'You are accessing the Internet via a Zscaler' if 'beta' in cloudName.lower() else 'You are accessing the Internet via Zscaler'
        if validateString in str(returnText):
            if not FailureExpected:self._logger.info("Access to ip.zscaler.com validated")
            else:raise Exception("Failure was expected for ip.zscaler.com access, but request went via zscaler")
        else:
            if not FailureExpected:
                if retryCount==0:
                    print('Retrying one more time after 10 second sleep')
                    time.sleep(10)
                    self.Access_IP_Zscaler(cloudName=cloudName,FailureExpected=FailureExpected,retryCount=1)
                else:
                    self._logger.error(f'{returnText}')
                    raise Exception("Access to ip.zscaler.com validation failed")
            else:
                self._logger.info("Access to ip.zscaler.com validation failed ~ expected failure")

    #----------------------------------------------------------------------------------------------------

    def Access_Application_IPv6(
        self,
        accessType = 'curl',        # Access type can be curl, curl full where curl has the command of "curl -LI" and curl full has the command "curl -v" and ping/icmp too
        websiteToAccess = None,      # Which website/application to access, by default "www.cnn.com" is used.
        accessFlag=True,        # (bool)    defines whether app should be accessed or not, by default true
        proxy=None,             # (bool) defines proxy via which traffic should be sent/in case of cert check via proxy give True  
        SEAccessApp_Block=False,  # (bool)    When ZCC is installed in SE mode the Applications/Internet is accessable or not.
        pingSize = None,        # (int) Size of ping/icmp
        proxyAgent = 'Ztunnel'   # (str) can take values to check if proxy agent is fine.
    ):

        if websiteToAccess == None:
            websiteToAccess = 'www.cnn.com'
        
        # if not('www.' in websiteToAccess):
        #     websiteToAccess = f'www.{websiteToAccess}'


        if accessType == 'curl':
            if proxy == True:
                cmd = f'curl -6 -LI https://{websiteToAccess} -k -x [::1]:9000' if 'http' not in websiteToAccess else f'curl -6 -LI {websiteToAccess} -k -x [::1]:9000'
            else:
                cmd = f'curl -6 -LI https://{websiteToAccess} -k' if 'http' not in websiteToAccess else f'curl -6 -LI {websiteToAccess} -k'
        if accessType == 'curl-full':
            if proxy == True:
                cmd = f'curl -6 -v https://{websiteToAccess} -k -x [::1]:9000'
            else:
                cmd = f'curl -6 -v https://{websiteToAccess} -k'

        if 'ping' in accessType or 'icmp' in accessType:
            if self._os_version=="MAC":
                cmd = 'ping6 -c 5 {}'.format(websiteToAccess)
                if pingSize != None:
                    cmd = f'{cmd} -s {pingSize}'
            else:
                cmd = 'ping -6 {}'.format(websiteToAccess)
                if pingSize != None:
                    cmd = f'{cmd} -l {pingSize}'

        result = self.calling_Command(command=cmd,fetchIP=False)
        self._logger.info("RESULT : \n{}\n*************************\n{}".format(result[0],result[1]))

        if 'curl' in accessType:
            if SEAccessApp_Block == True:
                if ("HTTP/1.1 403" in result[0] or 'SSL_ERROR_SYSCALL' in result[0] or "SSL/TLS connection failed" in result[0] or len(result[0]) == 0):
                    self._logger.info('Access to IPv6 Application {} blocked by Strict Enforcement!'.format(websiteToAccess))
                else:
                    line = 'Request to Ipv6 Application {} made in Strict Enforcement mode::FAIL'.format(websiteToAccess)
                    self._logger.error(line)
                    raise Exception(line)
            elif accessFlag:
                if "200 OK" in result[0] or "HTTP/2 200" in result[0] or "HTTP/1.1 200" in result[0]:
                    if proxy:
                        if proxyAgent == None:
                            self._logger.info("Proxy check not required! Skipping.")
                            pass

                        #Proxy-Agent: Ztunnel/1.0
                        if f"Proxy-Agent: {proxyAgent}" in result[0]:
                            self._logger.info('IPv6 App accessed Successfully')
                        else:
                            raise Exception("ERROR :: Proxy Expected, but not found in curl output")
                    else:
                        self._logger.info('App accessed Successfully')
                elif len(result[1])!=0:
                    self._logger.info(result[1])
                    raise Exception("ERROR :: Access Flag was True, but no result fetched from command")
                else:
                    raise Exception("ERROR :: 200 OK not found in curl request result")
            else:
                if len(result[1])==0 or "Empty reply from server" in result[1] or "SSL_ERROR_SYSCALL" in result[1] or "Connection timed out" in result[1] or "403 Forbidden" in result[0]:
                    self._logger.info("IPv6 App not accessed, case passed")
                else:
                    raise Exception("ERROR :: Access Flag was False, but result fetched from command")

        if 'icmp' in accessType or 'ping' in accessType:
            FailedPackets=[]
            if self._os_version=="MAC":
                result = result[0]
                result= result.split('\n')
                for item in result[1:]:
                    if 'Request timeout for icmp_seq' in item:
                        FailedPackets.append(item)
                    if 'transmitted' and 'received' in item:
                        item = item.split(',')[-1]
                        packetLoss = int(item.split('.')[0])
                    else:
                        pass
            else:
                result = result[0]
                result = result.split('\r\n')
                for val in result:
                    if 'Request timed out' in val:
                        FailedPackets.append(val)
                    if 'Packets:' in val:
                        self._logger.info(val)
                        val = val.split(',')[-2][11:-1]
                        packetLoss = int(val.split('%')[0])
                    else:
                        pass
            if accessFlag:
                if packetLoss>0:
                    raise Exception('Error ::ICMP PACKET LOST DETECTED :')
                else:
                    self._logger.info("ICMP PACKET NOT LOST, LOST % : {}".format(packetLoss))
            else:
                if packetLoss<100:
                    self._logger.error('ICMP Packet Recieved , LOSS % : {}'.format(packetLoss))
                    raise Exception('ERROR :: ICMP PACKET RECIEVED DETECTED')
                else:
                    self._logger.info('ICMP PACKETS NOT RECIEVED, LOSS % : {}'.format(packetLoss))

    #----------------------------------------------------------------------------------------------------

    def validateCertificate(self,   # function for validating traffic 
    tunnel_version = None,   #   (str) takes values such as tunnel_1, twlp, tunnel_2_dtls, tunnel_2_tls
    
    ):
        if tunnel_version == None:
            line = "No tunnel Version provided to search!"
            self._logger.error(line)
            raise Exception(line)

        applicationToVisit = "indiatvnews.com"

        if tunnel_version == "tunnel_1" or tunnel_version == "tunnel_2_dtls" or tunnel_version == "tunnel_2_tls":
            
            line = "Checking for {} mode".format(tunnel_version)
            self._logger.info(line)

            try:
                self.Access_Application(applicationToVisit,isZcert=True)
            except Exception as e:
                raise Exception(e)

        else:
            line = "Checking for TWLP mode"
            self._logger.info(line)

            try:
                self.Access_Application(applicationToVisit,isZcert=False)

                self.Access_Application(applicationToVisit,proxy=True,isZcert=True)
            except Exception as e:
                raise Exception(e)          

    #----------------------------------------------------------------------------------------------------

    def get_zcc_installed_certs(self,
    isCertRequired=True,      # (bool)    defines if cert should be present or not
    customCert=False,
    nonAutomationCustomCertString=None
    ):
        '''
        This function validates if zscaler root cert is installed on system
        isCertRequired defines whether cert should be present or not 
        '''
        certFound=False
        
        if self._os_version=="MAC":
            if nonAutomationCustomCertString=='CN=Gurmeet':nonAutomationCustomCertString='zcc'
            command = 'security find-certificate -a'
            certList = self.calling_Command(command=command)
            if len(certList[0])>0 and 'attributes:' in certList[0]:
                certList = certList[0].split('attributes:')
            else: raise Exception("ERROR :: got unexpected value from terminal output")
            #certSearchString = '"labl"<blob>="Zscaler Root CA"' if customCert==None else '"labl"<blob>="Automation_Cert"'
            if customCert:certSearchString = '"labl"<blob>="Automation_Cert"'
            elif nonAutomationCustomCertString:certSearchString=f'"labl"<blob>="{nonAutomationCustomCertString}"'
            else:certSearchString = '"labl"<blob>="Zscaler Root CA"'
            self._logger.info("Searching for {}".format(certSearchString))
            for certs in certList:
                if certSearchString in str(certs):
                    if customCert:self._logger.info("Got Custom Automation Cert")
                    else:self._logger.info("Got Zscaler Root CA Cert")
                    certFound=True
        else:
            import ssl
            from cryptography import x509
            certList=[]
            for store in ["CA", "ROOT", "MY"]:
                for cert, encoding, trust in ssl.enum_certificates(store):
                    certificate = x509.load_der_x509_certificate(cert, backend=None)
                    certList.append(str(certificate.issuer))
            #certSearchString = 'CN=Zscaler Root CA' if customCert==None else 'CN=Automation_Cert'
            certSearchString = 'CN=Automation_Cert' if customCert else 'CN=Zscaler Root CA'
            if nonAutomationCustomCertString:certSearchString=nonAutomationCustomCertString
            self._logger.info("Searching for {}".format(certSearchString))
            for item in certList:

                if 'CN=Gurmeet' in item:print(item)
            for cert in certList:
                if certSearchString in cert:
                    if customCert:self._logger.info("Got Custom Automation Cert")
                    elif nonAutomationCustomCertString:self._logger.info("Got Custom Non Automation Cert")
                    else:self._logger.info("Got Zscaler Root CA Cert")
                    certFound=True
        
        if isCertRequired:
            if certFound:self._logger.info("Required Cert is present on system, validated !")
            else:raise Exception("ERROR :: Required Cert not found on system")
        else:
            if not certFound:self._logger.info("Required Cert is not present on system, validated !")
            else:raise Exception("ERROR :: Required Cert found on system")

    def delete_zcc_installed_certs(self,
    customCert=None,
    nonAutomationCustomCertString=None
    ):
        '''
        
        This function deletes zscaler root certificate installed on system
        #If this function keeps on failing for windows, please fetch the latest thumbprint of Zscaler Root CA cert and update it in the command vairable for windows
        '''

        if self._os_version=="MAC":
            if nonAutomationCustomCertString=='CN=Gurmeet':nonAutomationCustomCertString='zcc'
            if customCert==None :command = 'sudo security delete-certificate -c "Zscaler Root CA"' 
            else : command='sudo security delete-certificate -c "Automation_Cert"'

            if nonAutomationCustomCertString:command = f'sudo security delete-certificate -c "{nonAutomationCustomCertString}"' 
            
            self._logger.info(f"Certificate delete command is : {command}")
            deleteCommand = self.calling_Command(command=command)
            if len(deleteCommand[1])>0:
                err = 'ERROR :: '+str(deleteCommand[1])
                self._logger.info(err)
                self._logger.info('\n\n************************\nCert not deleted, happens if cert is not there on the system\n************************\n\n')
            else:
                self._logger.info("Certificate Deleted from KeyChain Access")
                time.sleep(3)
    
        else:
            #command = "gci cert:\ -Recurse | where{$_.Thumbprint -eq 'd72f47d87420e3f0f9bdcac6f03a566743c481b9'} | Remove-Item -Force -Verbose"
            #63570d675e880d2ed5c6be1412ecdefe0998016a
            #command = "gci cert:\ -Recurse | where{$_.Thumbprint -eq '{}'} | Remove-Item".format('d72f47d87420e3f0f9bdcac6f03a566743c481b9' if customCert==None else 'f4daf527a26a9dd5a8d25c5680230551da053db3')
            if customCert: command = "gci cert:\ -Recurse | where{$_.Thumbprint -eq 'f4daf527a26a9dd5a8d25c5680230551da053db3'} | Remove-Item -Force -Verbose"
            elif nonAutomationCustomCertString:command = "gci cert:\ -Recurse | where{$_.Thumbprint -eq '63570d675e880d2ed5c6be1412ecdefe0998016a'} | Remove-Item -Force -Verbose"
            else:command = "gci cert:\ -Recurse | where{$_.Thumbprint -eq 'd72f47d87420e3f0f9bdcac6f03a566743c481b9'} | Remove-Item -Force -Verbose"
            
            deleteCommand = self.calling_Command(command=command,powershell=True)
            if len(deleteCommand[1])>0:
                err = 'ERROR :: '+deleteCommand[1]
                self._logger.info(err)
            else:
                self._logger.info("Certificate Deleted from Windows Cert Store")
                time.sleep(3)
    #----------------------------------------------------------------------------------------------------
    
    def applyPFMac(self,
    reset=False,            #   (bool)  defines whether pf should be reset or not, if true, applied rules shoule not be present in pf.conf
    skip=False
    ):
        '''
        This function just enables or resets the pf rules on MAC
        '''
        self._logger.info("Now enabling pf\n")
        commands = ['sudo pfctl -f /etc/pf.conf','sudo pfctl -E','sudo pfctl -s rules']    
        for command in commands:
            result = self.calling_Command(command=command)
            self._logger.info('\nSTDOUT :: {}\n\STDERR :: {}n\n'.format(result[0].split('\n'),result[1]))
            if command==commands[0] and 'pfctl: Syntax error in config file: pf rules not loaded' in result[1]:
                raise Exception("ERROR :: Invalid Syntax for PF File, rules not loaded")
            if command==commands[-1] and (not skip):
                if not reset:
                    for blockRule in self.firewallRuleStringMac:
                        self._logger.info("Searching for : {}".format(blockRule))
                        if blockRule in result[0]:
                            self._logger.info("RULE APPLIED - {}".format(blockRule))
                        else:
                            raise Exception("{} NOT FOUND IN pfctl -s rules".format(blockRule))
                else:
                    self._logger.info("PF should be reset")
                    for blockRule in self.firewallRuleStringMac:
                        if blockRule in result[0]:
                            raise Exception("{} found in pfctl -s rules".format(blockRule))
                        else:
                            self._logger.info("{} not found, pf file reset".format(blockRule))
                            self.firewallRuleStringMac = []

     #----------------------------------------------------------------------------------------------------
   
    def resetFirewallRule(self,
    ruleName=None       #   (str)   Name of the rule to be deleted, only used in case of windows
    ):
        '''
        Flow:  ------------------------------
            1. FOR MAC :
                1.1 We pick up data from PFfileCopy.txt in TMP folder and write that it pf.conf in etc folder 
                    and then call applyPFMac to enable updated pf file  
            2. FOR WIN : 
                2.1 We have WinFWRuleName list created when creating firewall rules, we traverse this, get name
                    of the rules, and fire command to delete them
                2.2 If WinFWRuleName is empty, then we check if we have ruleName given, if yes then we delete that
                    rule, else we raise exception
        '''
        self._logger.info('NOW RESETTING FIREWALL')
        temp_path = os.path.join( os.getcwd() ,"TMP" )
        if self._os_version=="MAC":
            originalPFfile = '/etc/pf.conf'
            PFfileCopy = temp_path + 'orignalPfCopy.txt'
            lines = open(PFfileCopy,'r').readlines()
            with open(originalPFfile,'w') as f:
                for line in lines:
                    f.write(line)
            self.applyPFMac(reset=True)
        else:
            removeCounter = 0
            if len(self.WinFWRuleName)==0:
                self._logger.error("No rules found in WinFWRuleName list")
                if ruleName==None:
                    raise Exception("No rules found in WinFWRuleName list and no ruleName given, one of these is required")
                else:
                    self.WinFWRuleName.append(ruleName)
                    self._logger.info("Given RuleName appended in WinFWRuleName list")
            for rules in self.WinFWRuleName:
                command = 'netsh advfirewall firewall delete rule name={}'.format(rules)
                result = self.calling_Command(command=command)
                if 'Deleted' in result[0]:
                    self._logger.info("Rule {} Deleted !".format(rules))
                    time.sleep(5)
                    removeCounter+=1

                else:
                    self._logger.error("Rule {} Not Deleted !".format(rules))
                    self._logger.error(result[1])
                    raise Exception("Rule not deleted")

            if removeCounter==len(self.WinFWRuleName):
                self.WinFWRuleName.clear()
            else:
                self._logger.error("All firewall rules not deleted !!!!")
    #----------------------------------------------------------------------------------------------------   
    def addFirewallRule(self,
    skip=False,
    autoApply=True,    # (bool) Automatically call applyPFMac function, give false when multiple lines to be added and then explicitly call applyPFMac
    resetRuleList=False, # (bool) Reset the applied rule list, used for mac, for working, read note in docstring below
    **kwargs,      #   (key=val)   keyword arguments
    ):           
        '''
        NOTE ::
            resetRuleList : this variable is used for mac only
                            when we add rule to disable dtls and tls on sme ip to fallback to tunnel1 from tunnel2
                            and later block complete IP to block tunnel 1 as well, then on applyPFMac function
                            when we apply pf and check for applied rules, only complete sme block rule is shown
                            i.e rules with protocol and port combination doesnt come in pfctl -s rules command
                            and only block <smeIP> rule is shown. 
                            So to over come this, we reset the list here for tunnel 2 to tunnel 1 fallback rule
        Flow:  ------------------------------
            1. Accepts key=val pairs, these pairs would be different for mac and windows
            2. if os is MAC:
                2.1 We create a copy of the existing pf.conf file and save it in TMP folder
                2.2 Then we add the key,val pairs into the base firewallRuleString
                2.3 At last, we append the created firewallRuleString into original pf.conf
            3. if os is WIN:
                3.1 We check if "name","action","dir" is given in key,val pairs, if not then we raise Exception
                3.2 Then we check if protocol is given in key,val pairs when we want to add remoteport,
                    if protocol is not given, then we raise exception as remoteport can only be given with protocol
                3.3 Then we fire the terminal command with firewallRuleString as command and validate result

        Key,Val Pairs: ----- THESE KEYS NAMES SHOULD BE EXACT AS DESCRIBED -----
            MAC ->  proto : defines protocol
                    remoteip : remoteip for which rule is to be added or any to block all ips
                    remoteport : remoteport for which rule is to be added
                    command : used to give a predefined or custom command
            WIN ->  name : name of firewall rule
                    action : defines whether to block or allow traffic, values are ALLOW,BLOCK
                    dir : defines direction of traffic flow, values are OUT,IN
                    remoteport : port on which rules is to be applied
                    protocol : protocol for which rule is to be applied
                    remoteip : ip for which rule is to be applied
        '''
        if self._os_version=="MAC":
            #******************** FIREWALL RULES FOR MAC ********************
            temp_path = os.path.join( os.getcwd() ,"TMP")
            originalPFfile = '/etc/pf.conf'
            PFfileCopy = temp_path + 'orignalPfCopy.txt'

            if resetRuleList:
                self.firewallRuleStringMac.clear()
                self._logger.info("Firewall rule list for mac cleared")

            if 'command' in kwargs.keys():
                firewallRuleString = kwargs['command']
            if len(kwargs.keys())==0:
                self._logger.info("No rules specified, give somehting like remoteip, remoteport or complete command  etc..")
                self._logger.info("Refer to documentation of addFirewallRule function in System_Ops.py in Lib folder for exact keywords to be passed")
                raise Exception("ERROR :: No command or args given for applying Mac PF Rules")
            else:
                #print(kwargs.keys())
                if 'remoteip' not in kwargs.keys():
                    if 'protocol' not in kwargs.keys() and 'remoteport' not in kwargs.keys():
                        self._logger.info("no protocol,ip or port given")
                        firewallRuleString = "block drop inet from any to any"
                    elif 'protocol' in kwargs.keys() and 'remoteport' in kwargs.keys():
                        self._logger.info("proto and port given, ip not")
                        firewallRuleString = "block drop inet proto {} from any to any port = {}".format(kwargs['protocol'],kwargs['remoteport'])
                    elif 'protocol' in kwargs.keys() and 'remoteport' not in kwargs.keys():
                        self._logger.info("only proto given")
                        firewallRuleString = "block drop inet proto {} from any to any".format(kwargs['protocol'])
                    else:
                        raise Exception("Invalid Command")
                else:
                    if 'protocol' not in kwargs.keys() and 'remoteport' not in kwargs.keys():
                        self._logger.info("only ip given")
                        firewallRuleString = "block drop inet from any to {}".format(kwargs['remoteip'])
                    elif 'protocol' in kwargs.keys() and 'remoteport' in kwargs.keys():
                        self._logger.info("proto,ip and port given")
                        firewallRuleString = "block drop inet proto {} from any to {} port = {}".format(kwargs['protocol'],kwargs['remoteip'],kwargs['remoteport'])
                    elif 'protocol' in kwargs.keys() and 'remoteport' not in kwargs.keys():
                        self._logger.inforint("proto and ip given")
                        firewallRuleString = "block drop inet proto {} from any to {}".format(kwargs['protocol'],kwargs['remoteip'])
                    else:
                        raise Exception("Invalid Command")
                
                
            firewallRuleString += '\n'
            self.firewallRuleStringMac.append(firewallRuleString.lower().replace('  ',''))
            if not os.path.exists(PFfileCopy):
                self._logger.info("{} not exists, creating it !".format(PFfileCopy))
                originalFileData = open(originalPFfile,'r').readlines()
                with open(PFfileCopy,'w') as f:
                    for l in originalFileData:
                        f.write(l)
            print(self.firewallRuleStringMac)
            try:
                with open(originalPFfile,'a') as f:
                    self._logger.info('Line to be appended is: {}'.format(self.firewallRuleStringMac[-1]))
                    f.write(self.firewallRuleStringMac[-1])
                    print("Written")
            except Exception as e:
                self._logger.error('Error Writing in pf.conf file!')
                self._logger.error("ERROR :: {}".format(e))
                raise Exception('Error Writing in pf.conf file!')
            
            if autoApply:self.applyPFMac(skip=skip)

        else:
            #******************** FIREWALL RULES FOR WIN ********************
            firewallRuleString = "netsh advfirewall firewall add rule "
            if 'name' not in kwargs.keys() or 'action' not in kwargs.keys() or 'dir' not in kwargs.keys():
                err=''
                if 'name' not in kwargs.keys():err +='name, '
                if 'action' not in kwargs.keys():err +='action, '
                if 'dir' not in kwargs.keys():err +='dir, '
                err +='not given in firewall command, please provide them in the command'
                raise Exception(err)
            if 'remoteport' in kwargs.keys() and 'protocol' not in kwargs.keys():
                raise Exception("Ports can only be given if Protocols are defined")
            for keys,val in kwargs.items():
                # firewallRuleString +=keys+"="+val+" "
                firewallRuleString = f"{firewallRuleString} {keys} = {val} "
            
            self._logger.info("FIREWALL RULE COMMAND :: {}".format(firewallRuleString))
            result = self.calling_Command(command=firewallRuleString)
            if 'Ok' in result[0]:
                self.WinFWRuleName.append(kwargs['name'])
                self._logger.info("Firewall Rule Added - {} !".format(self.WinFWRuleName))
            else:
                self._logger.error("Firewall Rule Not Added !")
                self._logger.error(result[1])
                raise Exception("Firewall rule not added !")
    #----------------------------------------------------------------------------------------------------

    def add_Host_Entry(self,
    resolveEntry        # (list) list containing string in format -> 'hostname resolvedIP'
    ):
        originaletcfile = Constants.Utils.hosts_path
        etcfileCopy = os.path.join((os.path.join(os.getcwd() ,"Extras")),'orignalPfCopy.txt')
        if not os.path.exists(etcfileCopy):
            self._logger.info("{} not exists, creating it !".format(etcfileCopy))
            originalFileData = open(originaletcfile,'r').readlines()
            with open(etcfileCopy,'w') as f:
                for l in originalFileData:
                    f.write(l)
        try:
            with open(originaletcfile,'a') as f:
                for item in resolveEntry:
                    self._logger.info(f'Line to be appended is: {item}'+'\n')
                    f.write(item+'\n')
                    self._logger.info("written")
        except Exception as e:
            self._logger.error('Error Writing in etc/host file!')
            self._logger.error("ERROR :: {}".format(e))
            raise Exception(f'Error Writing in etc/host file! :: {e}')

    def reset_Host_Entry(self):
        originaletcfile = Constants.Utils.hosts_path
        etcfileCopy = os.path.join((os.path.join(os.getcwd() ,"Extras")),'orignalPfCopy.txt')
        lines = open(etcfileCopy,'r').readlines()
        self._logger.info("Now resetting etc/host file with etcfileCopy from Extras folder")
        with open(originaletcfile,'w') as f:
            for line in lines:
                f.write(line)

    #----------------------------------------------------------------------------------------------------

    def removeDirectory(self,
    path                     #   (str)   path of directory to be deleted
    ):
        '''
        Flow:  ------------------------------
            This function is used to remove a particular directory with files inside it
        '''
        
        for filename in os.listdir(path):
            file_path = os.path.join(path, filename)
            try:
                if os.path.isfile(file_path) or os.path.islink(file_path):
                    os.unlink(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
            except Exception as E:
                self._logger.error(E)
                raise Exception(E)
        os.rmdir(path)
    #----------------------------------------------------------------------------------------------------

    def unZipFiles(self,
    file,
    pathOfZipFile=None
    ):  
        '''
        Flow:  ------------------------------
            This function is used to unzip the files
        '''
        time.sleep(10)
        self._logger.info("FILE TO BE UNZIPPED: {}".format(file))
        
        if not '.zip' in file: raise Exception("ERROR :: Given File not a zip file")
        path = pathOfZipFile if pathOfZipFile else self.CONST.log_path
        for files in sorted(os.listdir(path)):
            if file in files:
                with ZipFile(path+file,'r') as zipObj:
                    zipObj.extractall(path)
                break
        os.remove(path+file)
        file = file.replace('.zip','')
        if os.path.exists(path+file): 
            self._logger.info("{} unzipped at {}".format(file,path))
        else:
            self._logger.info("{} unzipped not found at {}".format(file,path))
            raise Exception("ERROR :: Unzipped File not found")
    #----------------------------------------------------------------------------------------------------

    def read_zstatus_mac(self):
        '''
        Flow:  ------------------------------
            This function is helper function for get_data. It reads zstatus log file which has mac UI data
            like tunnel value, sent recieved bytes etc
        '''
        self._logger.info('START READING ZSTATUS FILE')
        self.log_contents=[]
        zstatusFile = sorted([files for files in os.listdir(self.CONST.log_path) if 'ztstatus_' in files])
        if len(zstatusFile)>0:
            for file in zstatusFile:
                os.remove(self.CONST.log_path+file)
                print(file+' removed\n')
            time.sleep(5)
        else:
            raise Exception('No Zstatus file found')
        zstatusFile = sorted([files for files in os.listdir(self.CONST.log_path) if 'ztstatus_' in files])[0]
        self._logger.info('READING ZSTATUS FILE : {}'.format(zstatusFile))
        with open('{}{}'.format(self.CONST.log_path,zstatusFile),'r') as logFile:
            self.log_contents = logFile.readlines()
        self.log_contents=self.log_contents[4:-2]
        self._logger.info('DONE READING ZSTATUS FILE')
    #----------------------------------------------------------------------------------------------------

    def get_data(self):
        '''
        Flow:  ------------------------------
            This function is used to fetch the UI data on Mac ZCC
            It calls the read_zstatus_mac which is a type of log file containing mac UI data
            Then we get the data and format it and store the data in data_dict dictionary
        '''
        data_dict=dict()
        self.read_zstatus_mac()
        if len(self.log_contents)==0:
            raise Exception('DATA LIST EMPTY')
        else:
            for index,item in enumerate(self.log_contents):
                if index%2==0:
                    item=item.replace('<key>','').replace('</key>','').replace('<string>','').replace('</string>','').replace('\t','').replace('\n','')
                    data_dict[item]=self.log_contents[index+1].replace('<key>','').replace('</key>','').replace('<string>','').replace('</string>','').replace('\t','').replace('\n','')
        return data_dict

    def validateZCCRoutesIPv4(self,
        routeValid = True           # (bool)    If routes should be present or not
    ):
        '''
            This Function Validates ZCC routes on System for TAP driver Forwarding Profile
            **NOTE**: This is Valid for Windows as TAP driver is not supported for MAC
        '''
        routeValidValidationAnd = True
        routeValidValidationOr = False

        routes = {route.strip():False for route in Constants.ZCC_Sanity_Const.tapRoutes}

        print(routes)
        print('-'*20)

        cmd = 'route print'
        routePrint = self.calling_Command('route print')[0]

        print(routePrint)

        routePrint = [route.strip() for route in routePrint.split('\n')]

        for route in routes.keys():
            for r in routePrint:
                if r.find(route) == 0 and routes[route] == False:
                    routes[route] = True

        for key,values in routes.items():
            print('{}\t{}'.format(key,values))
            routeValidValidationAnd = (values and routeValidValidationAnd)
            routeValidValidationOr = (values or routeValidValidationOr)

        if routeValid == True:
            if routeValidValidationAnd == routeValid:
                self._logger.info('Routes are Valid i.e routeValid = {} and routeValidValidationAnd = {}'.format(routeValid,routeValidValidationAnd))
            else:
                line = 'Routes are not Valid i.e routeValid = {} and routeValidValidationAnd = {}'.format(routeValid,routeValidValidationAnd)
                self._logger.error(line)
                raise Exception(line)
        else:
            if routeValidValidationOr == routeValid:
                self._logger.info('Routes are Valid i.e routeValid = {} and routeValidValidationOr = {}'.format(routeValid,routeValidValidationOr))
            else:
                line = 'Routes are not Valid i.e routeValid = {} and routeValidValidationOr = {}'.format(routeValid,routeValidValidationOr)
                self._logger.error(line)
                raise Exception(line)


    def Retrieve_Registry_Values(self,keyLocation=None,keyName=None):
        """this function uses winreg python lib which exposes windows registry API for us to check
            registry key values for ex. under Zscaler registry hive.
            keyLocation takes the registry path in this format: r'SOFTWARE\WOW6432Node\Zscaler Inc.\Zscaler' 
            keyName takes the registry key's name in str format"""
        try:
            get_hkey_location = winreg.HKEY_LOCAL_MACHINE
            get_key_location = winreg.OpenKeyEx(get_hkey_location,keyLocation)
            get_key_value = winreg.QueryValueEx(get_key_location,keyName)
        except Exception as e:
            self._logger.error("Registry access failed")
            raise Exception(e)
        
        if get_key_location:
            winreg.CloseKey(get_key_location)   # to close previously opened registry key

        return get_key_value

    def Edit_Registry_Values(self,
                            hkeyConstant=None,   # It is the Registry Constants: Choose from [HKEY_CLASSES_ROOT, HKEY_CURRENT_USER, HKEY_LOCAL_MACHINE, HKEY_USERS, HKEY_CURRENT_CONFIG]
                            keyLocation=None,    # Enter the complete path excluding the hkeyConstant for the registry key, ex: r"SOFTWARE\WOW6432Node\Zscaler Inc.\Zscaler" & not r"Computer\HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Zscaler Inc.\Zscaler" 
                            mode=None,           # Mode can be any one out of ["create", "modify"]
                            subKeyName=None,     # Enter the name of the subkey to be created.
                            valueName=None,      # The name of the value that you want to modify or create
                            valueType=None,      # The type of value to be created: Choose from [String, Binary, QWORD, DWORD, Multi_String_Value, Expandable_String]
                            valueData=None       # Enter the data that is to be stored in a value, Enter data according to the valueType of the value or key.
    ) -> None:
        """
        This function can be used to edit registry values within [HKEY_CLASSES_ROOT, HKEY_CURRENT_USER, HKEY_LOCAL_MACHINE, HKEY_USERS, HKEY_CURRENT_CONFIG]
        1.  Modes supported: create & modify, only one mode at a time
        2.  IMPORTANT: hkeyConstant & keyLocation is mandatory, keyLocation has to be sent in as a raw string.
        3.  value referes to the data entry within the keyLocation. Ex: "Home" in KEY_LOCAL_MACHINE\SOFTWARE\Zoom\MSI
        4.  subKey refers to the child in the heirarchy of keyLocation. Ex: "MSI" in KEY_LOCAL_MACHINE\SOFTWARE\Zoom\MSI
        Example of Usage:-
            -:- sys_ops.Edit_Registry_Values(mode="create", hkeyConstant="HKEY_LOCAL_MACHINE", keyLocation=r"SOFTWARE\WOW6432Node\Zscaler Inc.", subKeyName="testingCode", valueName="testName", valueType="String", valueData="This is testing") -> Creates a subkey and a value within a subkey
            -:- sys_ops.Edit_Registry_Values(mode="create", hkeyConstant="HKEY_LOCAL_MACHINE", keyLocation=r"SOFTWARE\WOW6432Node\Zscaler Inc."valueName="testName", valueType="QWORD", valueData=1) -> creates only a value
        """
        hKeyConstants={ 
            "HKEY_CLASSES_ROOT":winreg.HKEY_CLASSES_ROOT,
		    "HKEY_CURRENT_USER":winreg.HKEY_CURRENT_USER,
			"HKEY_LOCAL_MACHINE":winreg.HKEY_LOCAL_MACHINE,
			"HKEY_USERS":winreg.HKEY_USERS,
			"HKEY_CURRENT_CONFIG":winreg.HKEY_CURRENT_CONFIG
        }

        registryValueTypes={
            "Binary":winreg.REG_BINARY,
		    "String":winreg.REG_SZ,
			"QWORD":winreg.REG_QWORD,
			"DWORD":winreg.REG_DWORD,
			"Multi_String_Value":winreg.REG_MULTI_SZ,
			"Expandable_String":winreg.REG_EXPAND_SZ
        }

        # Validation on the inputs given so that the function does not break midway due to not having the right inputs.
        if not mode:
            self._logger.error("The expected work is not defined - Please define create or modify !")
            raise Exception("The expected work is not defined - Please define create or modify !")
        
        if not hkeyConstant or not keyLocation:
            self._logger.error("The HKEY constant & Key Location is not defined - Please define it and try again.")
            raise Exception("The HKEY constant & Key Location is not defined - Please define it and try again.")
        
        if hkeyConstant not in hKeyConstants.keys():
            self._logger.error("The given HKEY constant is not accepted, Choose a value within [HKEY_CLASSES_ROOT, HKEY_CURRENT_USER, HKEY_LOCAL_MACHINE, HKEY_USERS, HKEY_CURRENT_CONFIG]")
            raise Exception("The given HKEY constant is not accepted, Choose a value within [HKEY_CLASSES_ROOT, HKEY_CURRENT_USER, HKEY_LOCAL_MACHINE, HKEY_USERS, HKEY_CURRENT_CONFIG]")

        hkeyConstantString=hkeyConstant
        hkeyConstantObject=hKeyConstants[hkeyConstant]
        subKeyLocation=r"{}".format(keyLocation)
        subKeyName=subKeyName
        valueName=valueName
        valueData=valueData
        subKeyHandler=None
        createSubKey=False
        createValue=False
        modifyValue=False
        deleteSubKey=False
        deleteValue=False
        
        if mode=="create":
            if not subKeyName and not valueName:
                self._logger.error("The Expected inputs were not given to create - Please check the subKeyName & valueName")
                raise Exception("The Expected inputs were not given to create - Please check the subKeyName & valueName")
            if subKeyName:
                createSubKey=True
            if valueName:
                createValue=True
        elif mode=="modify":
            if not valueName:
                self._logger.error("The Expected inputs were not given to create - Please check the subKeyName & valueName")
                raise Exception("The Expected inputs were not given to create - Please check the subKeyName & valueName")
            modifyValue=True

        try:
            keyHandler=winreg.OpenKey(hkeyConstantObject, subKeyLocation, 0, winreg.KEY_ALL_ACCESS) # The main Key handler object which will be used to access the keys.
            self._logger.info("Successfully accessed {}\{}".format(hkeyConstantString, subKeyLocation))
        except Exception as e:
            self._logger.error("There is an error in opening {}\{}".format(hkeyConstantString, subKeyLocation))
            self._logger.error(f"{e}")
            raise Exception("There is an error in opening {}\{}: {}".format(hkeyConstantString, subKeyLocation, e))

        if modifyValue:
            if not valueData or not valueName:
                self._logger.error("Some of the expected values are missing - Please check valueData or valueName")
                raise Exception("Some of the expected values are missing - Please check valueData or valueName")
            
            valueType=registryValueTypes[valueType] if valueType else winreg.QueryValueEx(keyHandler, valueName)[1] # Fetch the value type if not specified by the user. If specified, then use the specified valueType
            
            try:
                winreg.SetValueEx(keyHandler, valueName, 0, valueType, valueData)
            except Exception as e:
                self._logger.error("There is an error in modifying '{}' at {}\{}".format(valueName, hkeyConstantString, subKeyLocation))
                self._logger.error(f"{e}")
                raise Exception("There is an error in modifying '{}' at {}\{}: {}".format(valueName, hkeyConstantString, subKeyLocation, e))
            else:
                modifiedValueData=winreg.QueryValueEx(keyHandler, valueName)
                self._logger.info("The value '{}' at {}\{} has been modified to {}".format(valueName, hkeyConstantString, subKeyLocation, modifiedValueData))
        
        if createSubKey:
            if not subKeyName:
                self._logger.error("Some of the expected values are missing - Please check subKeyName")
                raise Exception("Some of the expected values are missing - Please check subKeyName")
            try:
                subKeyHandler=winreg.CreateKeyEx(keyHandler, subKeyName)
                self._logger.info("Successfully created a subkey '{}' at {}\{}".format(subKeyName, hkeyConstantString, subKeyLocation))
            except Exception as e:
                self._logger.error("There is an error in creating subkey '{}' at {}\{}".format(subKeyName, hkeyConstantString, subKeyLocation))
                self._logger.error(f"{e}")
                raise Exception("There is an error in creating subkey '{}' at {}\{}: {}".format(subKeyName, hkeyConstantString, subKeyLocation, e))

        if createValue:
            if not valueName and not valueData and not valueType:
                self._logger.error("Some of the expected values are missing - Please check valueData or valueName or valueType")
                raise Exception("Some of the expected values are missing - Please check valueData or valueName or valueType")

            valueType=registryValueTypes[valueType] # Fetch the value type if not specified by the user. If specified, then use the specified valueType
            
            if subKeyHandler:
                keyHandler=subKeyHandler
            try:
                winreg.SetValueEx(keyHandler, valueName, 0, valueType, valueData)
                if subKeyHandler:
                    self._logger.info("Successfully created a new value '{}' at {}\{}\{}".format(subKeyName, hkeyConstantString, subKeyLocation, subKeyName))
                    subKeyHandler.Close()
                else:
                    self._logger.info("Successfully created a new value '{}' at {}\{}".format(valueName, hkeyConstantString, subKeyLocation))
            except Exception as e:
                
                if subKeyHandler:
                    self._logger.error("There is an error in creating value '{}' at {}\{}\{}".format(subKeyName, hkeyConstantString, subKeyLocation, subKeyName))
                    self._logger.error(f"{e}")
                    raise Exception("There is an error in creating value at '{}' at {}\{}\{}: {}".format(subKeyName, hkeyConstantString, subKeyLocation, subKeyName, e))    
                else:
                    self._logger.error("There is an error in creating value '{}' at {}\{}".format(valueName, hkeyConstantString, subKeyLocation))
                    self._logger.error(f"{e}")
                    raise Exception("There is an error in creating value '{}' at {}\{}: {}".format(valueName, hkeyConstantString, subKeyLocation, e))

        keyHandler.Close()

    def Delete_Registry_Values(self,
                               hkeyConstant=None,   # It is the Registry Constants: Choose from [HKEY_CLASSES_ROOT, HKEY_CURRENT_USER, HKEY_LOCAL_MACHINE, HKEY_USERS, HKEY_CURRENT_CONFIG]
                               keyLocation=None,    # Enter the complete path excluding the hkeyConstant for the registry key, ex: r"SOFTWARE\WOW6432Node\Zscaler Inc.\Zscaler" & not r"Computer\HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Zscaler Inc.\Zscaler" 
                               subKeyName=None,     # Enter the name of the subkey to be deleted.
                               valueName=None,      # The name of the value that you want to delete
    ) -> None:
        """
        This function deals with the deleting of registry keys and values.
        It can perform deletions only on the registry keys present under [HKEY_CLASSES_ROOT, HKEY_CURRENT_USER, HKEY_LOCAL_MACHINE, HKEY_USERS, HKEY_CURRENT_CONFIG]
        Deletion of both the subKey & values is not supported. Use only subKeyName or valueName
        """
        hKeyConstants={ 
            "HKEY_CLASSES_ROOT":winreg.HKEY_CLASSES_ROOT,
		    "HKEY_CURRENT_USER":winreg.HKEY_CURRENT_USER,
			"HKEY_LOCAL_MACHINE":winreg.HKEY_LOCAL_MACHINE,
			"HKEY_USERS":winreg.HKEY_USERS,
			"HKEY_CURRENT_CONFIG":winreg.HKEY_CURRENT_CONFIG
        }

        if not hkeyConstant or not keyLocation:
            self._logger.error("The HKEY constant & Key Location is not defined - Please define it and try again.")
            raise Exception("The HKEY constant & Key Location is not defined - Please define it and try again.")
        
        if hkeyConstant not in hKeyConstants.keys():
            self._logger.error("The given HKEY constant is not accepted, Choose a value within [HKEY_CLASSES_ROOT, HKEY_CURRENT_USER, HKEY_LOCAL_MACHINE, HKEY_USERS, HKEY_CURRENT_CONFIG]")
            raise Exception("The given HKEY constant is not accepted, Choose a value within [HKEY_CLASSES_ROOT, HKEY_CURRENT_USER, HKEY_LOCAL_MACHINE, HKEY_USERS, HKEY_CURRENT_CONFIG]")

        hkeyConstantString=hkeyConstant
        hkeyConstantObject=hKeyConstants[hkeyConstant]
        subKeyLocation=r"{}".format(keyLocation)
        subKeyName=subKeyName
        valueName=valueName
        deleteSubKey=False
        deleteValue=False

        if not subKeyName and not valueName:
            self._logger.error("The Expected inputs were not given to create - Please check the subKeyName & valueName")
            raise Exception("The Expected inputs were not given to create - Please check the subKeyName & valueName")
        if subKeyName:
            deleteSubKey=True
        if valueName:
            deleteValue=True
        
        try:
            keyHandler=winreg.OpenKey(hkeyConstantObject, subKeyLocation, 0, winreg.KEY_ALL_ACCESS) # The main Key handler object which will be used to access the keys.
            self._logger.info("Successfully accessed {}\{}".format(hkeyConstantString, subKeyLocation))
        except Exception as e:
            self._logger.error("There is an error in opening {}\{}".format(hkeyConstantString, subKeyLocation))
            self._logger.error(f"{e}")
            raise Exception("There is an error in opening {}\{}: {}".format(hkeyConstantString, subKeyLocation, e))
        
        if deleteValue:
            if not valueName:
                self._logger.error("Some of the expected values are missing - Please check valueName")
                raise Exception("Some of the expected values are missing - Please check valueName")
            try:
                winreg.DeleteValue(keyHandler, valueName)
                self._logger.info("The Value '{}' at: {}\{} has been deleted successfully".format(valueName, hkeyConstantString, subKeyLocation))
            except Exception as e:
                self._logger.error("Error in deleting the value '{}' at: {}\{}".format(valueName, hkeyConstantString, subKeyLocation))
                self._logger.error("Error Message: {}".format(e))
                raise Exception("Error in deleting the value '{}' at: {}\{}: {}".format(valueName, hkeyConstantString, subKeyLocation, e))

        if deleteSubKey:
            if not subKeyName:
                self._logger.error("Some of the expected values are missing - Please check subKeyName")
                raise Exception("Some of the expected values are missing - Please check subKeyName")
            try:
                winreg.DeleteKey(keyHandler, subKeyName)
                self._logger.info("The Sub Key '{}' at: {}\{} has been deleted successfully".format(subKeyName, hkeyConstantString, subKeyLocation))
            except Exception as e:
                self._logger.error("Error in deleting the Sub Key '{}' at: {}\{}".format(subKeyName, hkeyConstantString, subKeyLocation))
                self._logger.error("Error Message: {}".format(e))
                raise Exception("Error in deleting the Sub Key '{}' at: {}\{}: {}".format(subKeyName, hkeyConstantString, subKeyLocation, e))

        keyHandler.Close()
            
    def iperfTrafficGenerator(self,
    protocol="",                            #default protocol will be Tcp until udp is specified
    network_type="ipv4",                    #default interface to make this request will be ipv4 until v6 is specified
    server_port=Constants.Utils.iperf3_aws_server_port,
    server_ip=Constants.Utils.iperf3_aws_server_address,
    time_for_test="2",                         # this is the time for which this test will run
    local_server_mode=False,
    one_off_mode="",                            # if want to enable one_off_mode then pass "-1"
    thread_name=""
    ):
        """
        This function generate TCP/UDP Data using iperf2 to iperf 3 AWS server. This can be used for both download/Upload.

        Args:
            protocol (str, optional): TCP/UDP. Defaults to "".
            network_type (str, optional): IPv4 or IPv6. Defaults to ipv4.
            server_port (str, optional): Iperf3 Server Port. Defaults to Constants.Utils.iperf3_aws_server_port.
            server_ip (str, optional): IPerf3 Server Address. Defaults to Constants.Utils.iperf3_aws_server_address.
            time_for_test (str, optional): Seconds for which you want to generate TCP?UDP traffic. Defaults to "2"#thisisthetimeforwhichthistestwillrun.
            local_server_mode (bool, optional): If you want to run the Iperf Server on Loopback address. Defaults to False
            one_off_mode (str, optional): If you want the iperf server to exit after handling one Client Connection.

        Functioning:
        iperf3 binary is loaded from the Drivers Folder. Then a connnection based on the options is made.
        if local_server_mode is chosen then using thread a local iperf3 server is started in one_off_mode.
        The thread is called from inside the function before the actual request from Testcase is executed. So thread Creates server and based on thread name server part is executed.
        The actual request from the testcase makes the client request.
        The output from client request and server request is in the form of JSON. Only client request  is parsed to see connection state and Data Transfer.
        If iperf connection succeeds then interval key length won't be zero. As there will be multiple connections opened.
        If interval is zero then we return False, json.
        Else if the connection succeeds we return true, json.

        Return TYPE:
        (Boolean, JSON) ==> Success/Failure + JSON

        """

        
        iperf_connection_success=True
        json_result=""

        if protocol.lower() == "udp":
            protocol="-u"

        if network_type.lower() == "ipv6" or network_type == "-6":
            network_type="-6"
        else:
            network_type="-4"


        #self._logger.info("Start Of Function: \n Protocol: {} \n network_type: {} \n, server_port={} \n , server_ip: {} \n time_for_test: {} \n, local_server_mode: {} \n, one_off_mode: {} \n thread_name:{} \n".format(protocol, network_type, server_port, server_ip, time_for_test, local_server_mode, one_off_mode, thread_name) )

        if local_server_mode == True:
            self._logger.info("Local_server_mode is set as true moving to multi thread mode to start iperf client and server")
            server=Thread(target=self.iperfTrafficGenerator, args= (protocol, network_type, server_port, server_ip,time_for_test, False,"-1","server", ))
            #client=Thread(target=self.iperfTrafficGenerator, args=(protocol, network_type, server_port, server_ip, time_for_test, False, "", "client"  ))
            self._logger.info("Starting Server Thread")
            server.start()
            #self._logger.info("Starting Client Thread")
            #client.start()

        if thread_name.lower() =="server":
            self._logger.info("Recieved request to Start Iperf Server \n")
            self._logger.info("Server_details: \n Protocol: {} \n network_type: {} \n, server_port={} \n , server_ip: {} \n time_for_test: {} \n, local_server_mode: {} \n, one_off_mode: {} \n thread_name:{} \n".format(protocol, network_type, server_port, server_ip, time_for_test, local_server_mode, one_off_mode, thread_name) )
            server_result=subprocess.run([os.path.join(os.getcwd(), Constants.Utils.iperf3_utility_path),"-s" , "-p", server_port, one_off_mode, "-J" ], capture_output=True, text=True)
            self._logger.info("Printing output of Server_result: \n {} ".format(server_result))

        else:
            #iperfCommand = f"{Constants.Utils.iperf3_utility_path} -c {server_ip} -p {server_port} {protocol} "
            self._logger.info("Recieved request to Start Iperf Client trying to connect Iperf Server on this address {}:{} \n".format(server_ip, server_port))
            self._logger.info("Client Details: \n Protocol: {} \n network_type: {} \n, server_port={} \n , server_ip: {} \n time_for_test: {} \n, local_server_mode: {} \n, one_off_mode: {} \n thread_name:{} \n".format(protocol, network_type, server_port, server_ip, time_for_test, local_server_mode, one_off_mode, thread_name) )
            client_result=subprocess.run([os.path.join(os.getcwd(), Constants.Utils.iperf3_utility_path), "-c", server_ip, "-p", server_port, protocol, "-J", "-t", time_for_test], capture_output=True, text=True)
            self._logger.info("Output of IPERF Command \n\n {}".format(client_result))
            json_result=json.loads(client_result.stdout)

        '''

        iperf output is in this format:

        For success case:
        '{\n\t"start":\t{\n\t\t"connected":\t[{\n\t\t\t\t"socket":\t4,\n\t\t\t\t"local_host":\t"*************",\n\t\t\t\t"local_port":\t64136,\n\t\t\t\t"remote_host":\t"*************",\n\t\t\t\t"remote_port":\t10042\n\t\t\t}],\n\t\t"version":\t"iperf 3.1.3",\n\t\t"system_info":\t"CYGWIN_NT-10.0 DESKTOP-S1DG72F 2.5.1(0.297/5/3) 2016-04-21 22:14 x86_64",\n\t\t"timestamp":\t{\n\t\t\t"time":\t"Tue, 11 Apr 2023 18:12:54 GMT",\n\t\t\t"timesecs":\t1681236774\n\t\t},\n\t\t"connecting_to":\t{\n\t\t\t"host":\t"ec2-3-111-188-105.ap-south-1.compute.amazonaws.com",\n\t\t\t"port":\t10042\n\t\t},\n\t\t"cookie":\t"DESKTOP-S1DG72F.1681236774.446866.25",\n\t\t"tcp_mss_default":\t0,\n\t\t"test_start":\t{\n\t\t\t"protocol":\t"TCP",\n\t\t\t"num_streams":\t1,\n\t\t\t"blksize":\t131072,\n\t\t\t"omit":\t0,\n\t\t\t"duration":\t1,\n\t\t\t"bytes":\t0,\n\t\t\t"blocks":\t0,\n\t\t\t"reverse":\t0\n\t\t}\n\t},\n\t"intervals":\t[{\n\t\t\t"streams":\t[{\n\t\t\t\t\t"socket":\t4,\n\t\t\t\t\t"start":\t0,\n\t\t\t\t\t"end":\t1.013039,\n\t\t\t\t\t"seconds":\t1.013039,\n\t\t\t\t\t"bytes":\t524288,\n\t\t\t\t\t"bits_per_second":\t4140317.930413,\n\t\t\t\t\t"omitted":\tfalse\n\t\t\t\t}],\n\t\t\t"sum":\t{\n\t\t\t\t"start":\t0,\n\t\t\t\t"end":\t1.013039,\n\t\t\t\t"seconds":\t1.013039,\n\t\t\t\t"bytes":\t524288,\n\t\t\t\t"bits_per_second":\t4140317.930413,\n\t\t\t\t"omitted":\tfalse\n\t\t\t}\n\t\t}],\n\t"end":\t{\n\t\t"streams":\t[{\n\t\t\t\t"sender":\t{\n\t\t\t\t\t"socket":\t4,\n\t\t\t\t\t"start":\t0,\n\t\t\t\t\t"end":\t1.013039,\n\t\t\t\t\t"seconds":\t1.013039,\n\t\t\t\t\t"bytes":\t524288,\n\t\t\t\t\t"bits_per_second":\t4140317.930413\n\t\t\t\t},\n\t\t\t\t"receiver":\t{\n\t\t\t\t\t"socket":\t4,\n\t\t\t\t\t"start":\t0,\n\t\t\t\t\t"end":\t1.013039,\n\t\t\t\t\t"seconds":\t1.013039,\n\t\t\t\t\t"bytes":\t524288,\n\t\t\t\t\t"bits_per_second":\t4140317.930413\n\t\t\t\t}\n\t\t\t}],\n\t\t"sum_sent":\t{\n\t\t\t"start":\t0,\n\t\t\t"end":\t1.013039,\n\t\t\t"seconds":\t1.013039,\n\t\t\t"bytes":\t524288,\n\t\t\t"bits_per_second":\t4140317.930413\n\t\t},\n\t\t"sum_received":\t{\n\t\t\t"start":\t0,\n\t\t\t"end":\t1.013039,\n\t\t\t"seconds":\t1.013039,\n\t\t\t"bytes":\t524288,\n\t\t\t"bits_per_second":\t4140317.930413\n\t\t},\n\t\t"cpu_utilization_percent":\t{\n\t\t\t"host_total":\t14.685771,\n\t\t\t"host_user":\t6.509650,\n\t\t\t"host_system":\t8.176121,\n\t\t\t"remote_total":\t0.012899,\n\t\t\t"remote_user":\t0.012899,\n\t\t\t"remote_system":\t0\n\t\t}\n\t}\n}\n'
        For Failure case:
        '{\n\t"start":\t{\n\t\t"connected":\t[],\n\t\t"version":\t"iperf 3.1.3",\n\t\t"system_info":\t"CYGWIN_NT-10.0 DESKTOP-S1DG72F 2.5.1(0.297/5/3) 2016-04-21 22:14 x86_64"\n\t},\n\t"intervals":\t[],\n\t"end":\t{\n\t},\n\t"error":\t"error - unable to connect to server: Connection timed out"\n}\n'

        '''

        if len(json_result['intervals'])==0:
            self._logger.error("IPerf Connection Failed with this error: '{}'' ".format(json_result['error']) )
            return not iperf_connection_success, json_result
        
        else:
            self._logger.info("Iperf Connection succeeded returning the json and connection success as True")
            return iperf_connection_success, json_result


    def checkDriverUpgrade(self,
                           driver_name          #LWF,WFP,TAP Driver
                           ):
        
        """
            This function checks whether the latest driver is loaded in memory or not. This is required while testing areas
            around ZCC upgrade, Driver Upgrade and Driver cache issues.
            This function is applicate to Windows OS only as of now but in future can be expanded to other platforms if needed.

            Working:
            1)This function checks the chipset type
            2) Then it checks the whether ZCC is 32 / 64 bit.
            3) Based on this it decides the .sys field path for the driver from zcc binaries.
            4) Then it checks if this driver is supported on this chipset, if not return 2
            5) If the chipset is supported then it checks whether version of .sys file in zcc binary folder and c/windows/system32/driver/<.sys> is running same version or not.


            Parameters:
            driver_name (str) : this parameter should have 3 values WFP/LWF/TAP.
                                zsawdrv.sys --> THis is ZSAWFPDriver.
                                zapprd.sys--> THis is ZSAFilterDriver
                                ztap.sys--> Route Based Driver.

            Returns: (Type int)
                    0: The Latest Driver is Loaded in Memory
                    1: Latest Driver is not loaded in Memory
                    2: Driver is not supported on this architecture.
                    3. Driver not installed
                    4. Incorrect Driver Name sent.
        """

        driver_file_path=""       
        driver_version_from_zcc_binary_path=""
        driver_version_from_windows_path=""

        valid_driver_names={"zsawdrv.sys", "zapprd.sys", "ztap.sys"}

        if driver_name.lower() not in valid_driver_names:
            self._logger.error("Incorrect Driver Name sent {}".format(driver_name))
            return 4

        if platform.machine.lower() == "i386" and driver_name.lower() == "zsawdrv.sys":
            self._logger.error("WFP Driver is not supported on i386 chipsets")
            return 2

        driver_file_path= self.constructDriverPath(driver_name)

        self._logger.info("Checking Driver version from this path: {}".format(driver_file_path))
        result=subprocess.run(["powershell", "-command", "(Get-Item \"{}\").VersionInfo.FileVersionRaw".format(driver_file_path)], capture_output=True, text=True)
        driver_version_from_zcc_binary_path=result.stdout.split("\n")[3].strip()
        print(driver_version_from_zcc_binary_path)

        self._logger.info("Checking Driver version from this path: {}".format(os.path.join(Constants.Utils.Windows_driver_sys_path,driver_name)))
        result=subprocess.run(["powershell", "-command", "(Get-Item \"{}\").VersionInfo.FileVersionRaw".format(os.path.join(Constants.Utils.Windows_driver_sys_path, driver_name))], capture_output=True, text=True)
        
        if result.stdout =="":
            self._logger.error("{} driver not installed".format(driver_name))
            return 3
        
        driver_version_from_windows_path=result.stdout.split("\n")[3].strip()
        print(driver_version_from_windows_path)

        if driver_version_from_windows_path == driver_version_from_zcc_binary_path:
            self._logger.info("Driver Version in Memory and in binary matched")
            return 0
        else:
            self._logger.info("Driver Version in Memory and in binary mismatched. \n IN memmory version {} \n In Binary version {}".format(driver_version_from_windows_path, driver_version_from_zcc_binary_path))
            return 1


    def checkDriverStatus(self,
                          driver_name="zapprd",
                          expected_status=0):
        
        """
        This functions check what is status of the respected driver.

        Working:
        "sc query driver_name" command is executed.  and the output is checked if the driver is installed or not. If installed it checks it status.

        Returns:
        return (bool) True/False

        Driver States:
        0 ==> Driver is installed and in running state
        1 ==> Driver is installed and stopped state
        2 ==> Is installed but it is unknown state
        3 ==> Driver is not installed 
        4 ==> Incorrect Driver Name
            
        """
        
        valid_driver_name={"zsawdrv", "zapprd", "ztap"}
        status=None

        if driver_name.lower() not in valid_driver_name:
            self._logger.error("Incorrect Driver Name sent {}".format(driver_name))
            status=4
        
        else:

            result=subprocess.run(["sc", "query", driver_name], capture_output=True, text=True)
            self._logger.info("Ouput of sc query {} is : {}".format(driver_name, result.stdout))

            if "The specified service does not exist as an installed service." in result.stdout:
                self._logger.error("{} driver is not installed. Output of command is: {} ".format(driver_name,result.stdout))
                status=3
                
            else:

                driver_status=result.stdout.split("\n")[3].strip().split(" ")[-1]

                if driver_status.lower()=="running":
                    self._logger.info("{} is running".format(driver_name))
                    status=0
                
                elif driver_status.lower()=="stopped":
                    self._logger.info("{} is stopped".format(driver_name))
                    status=1
                
                else:
                    self._logger.error("{} is in this state {}".format(driver_name, driver_status))
                    status=2

        if status != expected_status:
            return False
            self._logger.error("Driver Status mismatch Expected {} and observed {}".format(expected_status, status))
            raise Exception("Driver Status mismatch Expected {} and observed {}".format(expected_status, status))
        else:
            self._logger.info("Driver state matched")
            return True          

    def generateDNSRequest(self,
                            domain="example.com",                     
                            tcp=False, 
                            record_type="A",
                            dns_server_ip=Constants.Utils.ipv4_google_dns_server,
                            source_port=Constants.Utils.dns_request_source_port
                            ):
        """_summary_

        Args:
            domain (str, optional): This is the domains for which you want to resolve DNS. Defaults to "example.com".
            tcp (bool, optional): This flag tells whether to use TCP for making DNS request. Defaults to False.
            record_type (str, optional): Which Record Type you want to request. Defaults to "A".
            dns_server_ip (_type_, optional): Server IP to which you want to make DNS requests. Defaults to Constants.Utils.ipv4_google_dns_server.
            source_port (_type_, optional): Source Port with which you want to bind DNS request. Defaults to Constants.Utils.dns_request_source_port.

        Raises:
            Exception: _description_
        """
        self._logger.info("Generating {} record request for domain {} to this DNS Sever {} over {} on this port {}". format(record_type, domain, dns_server_ip, "UDP" if tcp==False else "TCP", source_port ))
        resolver_object=dns.resolver.Resolver(configure=True)
        resolver_object.nameservers.append(dns_server_ip)
        answer=""
        try:
            print("inside try")
            answer=resolver_object.resolve(domain, record_type, tcp=tcp, source_port=source_port)
            self._logger.info("DNS Request made successfully")
        except: 
            self._logger.error(Exception)
            raise Exception
        
    
    def launchZoomMeeting(self,
                          meeting_url=Constants.Utils.zoom_meeting_url,
                          time_for_meeting=1
                          ):
        
        zoom_exe_path=""
        self._logger.info("Checking if zoom is already is installed on system")
        if os.path.exists(Constants.Utils.zoom_32bit_exe_path):
            self._logger.info("32 bit Zoom Binary exists trying to start meeting")
            zoom_exe_path=Constants.Utils.zoom_32bit_exe_path

        elif os.path.exists(Constants.Utils.zoom_64bit_exe_path):
            self._logger.info("64 bit Zoom binary exists trying to start meeting")
            zoom_exe_path=Constants.Utils.zoom_64bit_exe_path
        
        else:
            self._logger.info("Zoom Binary does not exist on system")
            try:
                raise Exception
            except:
                self._logger.error("Zoom Binary not found. Raising Exception for same")

        self._logger.info("Trying to start Zoom Meeting")
        print("--url={}".format(meeting_url))
        print()
        try:
            result=subprocess.run([zoom_exe_path, "--url={}".format(meeting_url)], capture_output=True, text=True,timeout=60)
            
            if result.returncode==0:
                self._logger.info("Zoom Meeting started")
            else:
                self._logger.info("Zoom Meeting Start Failed")

            self._logger.info("Going to {} mins sleep and then will kill the zoom call".format(time_for_meeting))

            time.sleep(time_for_meeting * 60)

        except subprocess.TimeoutExpired:
            self._logger.error("Exception: Seems like Subprocess again got stuck, but no worries timeout is 60 seconds and meeting already started. Will go ahead with killing Zoom.exe")

        for proc in psutil.process_iter():
            if proc.name().lower() == "zoom.exe":
                self._logger.info("Found Zoom.exe process with pid: {}, killing it".format(proc.pid))
                proc.kill()
        
        time.sleep(5)
        
    def getCurrentSystemTime(self,
                             timezone="pytz.utc",
                             time_format="%Y-%m-%d %H:%M:%S.%f",
                            ):
        tz=""

        if timezone=="pytz.utc":
            tz=pytz.utc
        else:
            tz=pytz.timezone(timezone)

        return datetime.now(tz).strftime(time_format)
    
    def constructDriverPath(self,
                            driver_name):
        
        driver_file_path=""

        device_architecture=platform.machine().lower()
        self._logger.info("current Architecture is: {}".format(device_architecture))

        if os.path.exists(Constants.Utils.win_zcc_x64_binary_folder_path):
            self._logger.info("Current ZCC build is 64 bit")
            driver_file_path=os.path.join(driver_file_path,Constants.Utils.win_zcc_x64_binary_folder_path)

        else:
            self._logger.info("Current ZCC build is 32 bit")
            driver_file_path=os.path.join(driver_file_path, Constants.Utils.win_zcc_x86_binary_folder_path)

        if driver_name == "zsawdrv.sys":
            self._logger.info("WFP Driver Version Check is aksed")
            driver_file_path=os.path.join(driver_file_path, os.path.join(os.path.join("ZSAWFPDriver",device_architecture), "zsawdrv.sys"))

        elif driver_name== "zapprd.sys":
            self._logger.info("LWF Driver Version Check is asked")
            driver_file_path=os.path.join(driver_file_path, os.path.join("ZSAFilterDriver", os.path.join("win10",os.path.join(device_architecture, "zapprd.sys"))))

        return driver_file_path
      
    def checkDriverSigning(self,
                           driver_signer=Constants.Utils.WHQL_Driver_Issuer_Name,
                           driver_name="zapprd.sys"):
        """
        This function will check if the driver is signed by WHQL Signing Authority or not.

        Args:
            driver_signer (string, optional): This is signer you need to check in the cert. Defaults to Constants.Utils.WHQL_Driver_Issuer_Name.
            driver_name (str, optional): This is the driver_name for which you need to check the signer. Defaults to "zapprd.sys".

        Raises:
            Exception: We raise exception in three cases, if the Driver name is not valid driver name.
            Secondly if the signer name is not that we expect
            Thirdly if the signer name mismatches.

        """
        driver_file_path=""       
        driver_version_from_zcc_binary_path=""

        valid_driver_names={"zsawdrv.sys", "zapprd.sys", "ztap.sys"}

        if driver_name.lower() not in valid_driver_names:
            self._logger.error("Incorrect Driver Name sent {}".format(driver_name))
            raise Exception("Incorrect Driver Name Passed")

        driver_file_path=self.constructDriverPath(driver_name)

        result=subprocess.run(["powershell", "-command", '(Get-AuthenticodeSignature "{}").SignerCertificate.Subject'.format(driver_file_path)], capture_output=True, text=True)
        result_dict={}

        if result.stdout =="":
            self._logger.error("Driver Signing Check failed. Dumping output of subprocess \n {}".format(result))
            raise Exception("Driver Signer Check Failed")
        else:
            for key_value in result.stdout.split(","):
                key,value=key_value.split("=",1)
                result_dict[key]=value

        if result_dict["CN"] == driver_signer:
            self._logger.info("Driver Signer Matched for {} with {}".format(driver_name, driver_signer))
        
        else:
            self._logger.info("Driver Signer Match failed for {} with {} found this signer from command ".format(driver_name, driver_signer, result_dict["CN"]))
            raise Exception("Signer Match Failed")

    def detectGREORIPSECInNetwork(self):    

        """This function gets cloudname when someone creates object of this class.
        Then we run the gateway.cloudname.net/get_loc function and based on its response we detect whether its GRE/IPSec or Direct case

        Raises:
            Exception: Exception is raised if while creating object of sys_ops we do not pass cloudname
            Secondly if the API fails and have empty response.
            Thirdly there is case where zviam key is missing then we raise the Exception

        Returns:
           Bool: This will return TRUE if GRE/IPSEC is detected otherwise it will return False. 
        """

        if self.cloud ==None:
            raise Exception("No Cloudname Passed")
            self._logger.error("No Cloud is passed")

        else:
            result=subprocess.run(["curl", " https://gateway.{}.net/get_loc".format(self.cloud), "--max-time", "10"], capture_output=True, text=True)

        if result.stdout=="":
            self._logger.error("Exception : Get Loc API Failed, API response is empty")
            raise Exception("Exception : Get Loc API Failed, API response is empty")
        else:
            result_json=json.loads(result.stdout)

        if "zviam" in result_json.keys():
            self._logger.info("Get Loc API is successful and ZVIAM response is {}".format(result_json["zviam"]))
            if result_json["zviam"] == "GRE" or result_json["zviam"]== "IPSEC":
                return True
            else:
                self._logger.error("zviam response is not GRE or IPSec")
                return False
        else:
            self._logger.error("Exeption: zviam key is missing, dumping result: \n {} ".format(result.stdout))
            raise Exception("zviam Key is missinig")
        
        return False

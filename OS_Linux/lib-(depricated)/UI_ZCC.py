from common_lib import UI_Common
import os,sys
import copy
import time
from datetime import datetime
from functools import wraps
import pyautogui
import allure
from common_lib import Constants

class Zcc_Elements_Win:
    """ Class that contains Zscaler Windows app element IDs  """
    # normal build
    APP = "Zscaler"
    ZSCALER_LOGGEDIN_LOGO = "id:ZSAMFZscalerLogoWhite"
    ZSA_TRAY_FORM = 'id:ZscalerApp'
    ZCC_BACK_BUTTON = 'id:ZSAPFBackButton'
    ZCC_BACK_BUTTON_AT_OKTA = 'id:ZSASFBackButton'
    ZCC_LOGGED_IN_TRAY_ICON =  "name:\"Service is enabled.\""
    ZCC_LOGGED_OUT_TRAY_ICON =  "name:\"Service is disabled.\""
    ZCC_EXIT_BUTTON = "name:Exit"
    INTERNET_SECURITY_RETRY_BUTTON = "id:RETRY"
    STRICT_ENFORCEMENT_BANNER_HEADER = "id:ZSAUNFEnforceHdrText"
    STRICT_ENFORCEMENT_BANNER_MESSAGE = "id:ZSAUNFEnforceMsgText"
    # LOGIN ZIA
    USER_NAME = 'id:ZSAUNFUserNameText'
    LOGIN_BUTTON = 'id:ZSAUNFLoginButton'
    EULA_ACCEPT_BUTTON = 'id:ZSAUPFAcceptButton'
    EULA_DECLINE_BUTTON = 'id:ZSAUPFDeclineButton'
    PASSWORD_BOX = 'id:ZSAPFPasswordBox'
    LOGIN_BUTTON_PASSWORD = 'id:ZSAPFLoginButton'
    LOGIN_ERROR_OK_BUTTON = 'id:OkButton'
    # LOGOUT
    LOGOUT_BUTTON = 'id:ZSAMFLogoutButton'
    CONFIRM_ACCEPT_BUTTON = 'id:ZSABWConfirmAcceptButton'
    LOGOUT_OK_BUTTON = "id:ZSABWPasswordOKButton"
    LOGOUT_PASSWORD_BOX = 'id:ZSABWPasswordBox'
    LOGOUT_PASSWORD_ACCEPT_BUTTON = 'id:ZSABWPasswordOKButton'
    LOGOUT_PASSWORD_CANCEL_BUTTON = 'id:ZSABWPasswordCancelButton'
    LOGOUT_PASSWORD_FAILED_OK_BUTTON = 'id:OkButton'
    RESTART_SERVICE_WAIT = 'id:OkButton'
    # MORE
    APP_MORE="id:ZSAMFSettingsTab"
    APP_UPDATE_POLICY='id:ZSAMFSettingsUpdatePolicy'
    UPDATE_POLICY_FAILED_BUTTON = 'id:LOGOUT_PASSWORD_FAILED_OK_BUTTON'
    CLEAR_LOGS='id:ZSAMFSettingsClearLogs'
    RESTART_SERVICE = 'id:ZSAMFSettingsRestartService'
    RESTART_SERVICE_ACCEPT = 'id:ZSABWConfirmAcceptButton'
    START_PACKET_CAPTURE = 'id:ZSAMFPacketCaptureLabel'
    STOP_PACKET_CAPTURE = 'id:ZSAMFPacketCaptureLabel'
    CAPTURE_STATUS = 'id:ZSAMFPacketCaptureStatus'
    EXPORT_LOGS = 'id:ZSAMFSettingsExportLogs'
    EXPORT_LOGS_TRAYBAR_MENU = 'id:Export Logs'
    UPDATE_APP = 'id:ZSAMFSettingsUpdateApp'
    LOG_MODE_DROP_DOWN = "id:ZSAMFSettingsLogModeDropDown"
    REVERT_ZCC = "id:ZSAMFSettingsRevertApp"
    REVERT_ZCC_PASSWORD_BOX = 'id:ZSABWPasswordBox'
    REVERT_ZCC_PASSWORD_ACCEPT_BUTTON = 'id:ZSABWPasswordOKButton'
    REVERT_ZCC_PASSWORD_FAILED_OK_BUTTON = 'id:OkButton'
    # INTERNET SECURITY
    INTERNET_SECURITY_TAB = 'id:ZSAMFWebSecurityTab'
    INTERNET_SECURITY_POWER_BUTTON_ON = 'id:ZSAMFWebSecurityPowerButton'
    INTERNET_SECURITY_POWER_BUTTON_OFF = 'id:ZSAMFWebSecurityPowerButton'
    INTERNET_SECURITY_PASSWORD_BOX = 'id:ZSABWPasswordBox'
    INTERNET_SECURITY_PASSWORD_ACCEPT_BUTTON = 'id:ZSABWPasswordOKButton'
    INTERNET_SECURITY_PASSWORD_CANCEL_BUTTON = 'id:ZSABWPasswordCancelButton'
    INTERNET_SECURITY_PASSWORD_FAILED_OK_BUTTON = 'id:OkButton'
    INTERNET_SECURITY_STATUS = 'id:ZSAMFWebSecurityStatusDetail'
    INTERNET_SECURITY_TUNNEL_STATUS_TEXT = 'id:ZSAMFWebSecurityTunnelStatusText'
    INTERNET_SECURITY_SERVER_IP_TEXT = 'id:ZSAMFWebSecurityServerIPText'
    INTERNET_SECURITY_TUNNEL_PROTOCOL = 'id:ZSAMFWebSecurityTunnelVersionText'
    # ZDP
    DATA_PREVENTION_TAB = "id:ZSAMFDataProtectionTab"
    DATA_PREVENTION_POWER_BUTTON_ON = "id:ZSAMFDataProtectionPowerButton"
    DATA_PREVENTION_POWER_BUTTON_OFF = "id:ZSAMFDataProtectionPowerButton"
    DATA_PREVENTION_PASSWORD_BOX = 'id:ZSABWPasswordBox'
    DATA_PREVENTION_PASSWORD_ACCEPT_BUTTON = 'id:ZSABWPasswordOKButton'
    DATA_PREVENTION_PASSWORD_FAILED_OK_BUTTON = 'id:OkButton'
    DATA_PREVENTION_PASSWORD_CANCEL_BUTTON = 'id:ZSABWPasswordCancelButton'
    DATA_PREVENTION_REQUEST_BYPASS_BUTTON = 'id:ZSAMFDataProtectionRequestBypass'
    # LOGIN ZPA OKTA
    #OKTA_SIGNIN_PAGE = 'name:\"Sign In\r\n\r\n\r\n\r\n\r\nUsername \r\n\r\n\r\n\r\n\r\nPassword \r\n\r\n\r\n\r\n\r\n\r\n\r\nRemember me\r\n\r\n\r\nNeed help signing in?Forgot password?\r\nHelp\"'
    #OKTA_SIGNIN_PAGE = 'id:okta-sign-in'
    OKTA_SIGNIN_PAGE = 'name:\"okta-dev-63623894 logo logo\"'
    OKTA_USERNAME = 'name:\"Username\"'
    OKTA_PASSWORD_BOX = 'name:\"Password\"'
    REAUTH_OKTA_USERNAME = 'name:\"Username\"'
    REAUTH_OKTA_PASSWORD_BOX = 'name:\"Password\"'
    OKTA_SIGN_IN_BUTTON = 'name:\"Sign In\"'
    OKTA_SIGN_IN_BUTTON_ID = 'id:okta-signin-submit\"'
    # PRIVATE ACCESS
    PRIVATE_ACCESS_TAB = 'id:ZSAMFPrivateAccessTab'
    PRIVATE_ACCESS_POWER_BUTTON_ON = 'id:ZSAMFSecureAccessPowerButton'
    PRIVATE_ACCESS_POWER_BUTTON_OFF = 'id:ZSAMFSecureAccessPowerButton'
    PRIVATE_ACCESS_PASSWORD_BOX = 'id:ZSABWPasswordBox'
    PRIVATE_ACCESS_PASSWORD_ACCEPT_BUTTON = 'id:ZSABWPasswordOKButton'
    PRIVATE_ACCESS_PASSWORD_CANCEL_BUTTON = 'id:ZSABWPasswordCancelButton'
    PRIVATE_ACCESS_BYTES_SENT = "id:ZSAMFSecureAccessBytesSentText"
    PRIVATE_ACCESS_BYTES_RECIEVED = "id:ZSAMFSecureAccessBytesReceivedText"
    PRIVATE_ACCESS_REAUTH_BUTTON = "id:ZSAMFSecureAccessAuthButton"
    PRIVATE_ACCESS_EARLY_REAUTH_BUTTON = 'name:\"AUTHENTICATE EARLY\"'
    PRIVATE_ACCESS_AUTH_STATE = "id:ZSAMFSecureAccessAuthStatusText"
    PRIVATE_ACCESS_PASSWORD_FAILED_OK_BUTTON = 'id:OkButton'
    PRIVATE_ACCESS_TUNNEL_PROTOCOL = "id:id:ZSAMFSecureAccessProtocolText"
    PRIVATE_ACCESS_TUNNEL_STATUS_TEXT = 'id:ZSAMFSecureAccessTunnelStatusText'
    PRIVATE_ACCESS_NETWORK_STATUS = 'id:ZSAMFSecureAccessNetworkTypeDetail'
    # REPORT AN ISSUE
    REPORT_AN_ISSUE_BUTTON = "id:ZSAMFSettingsReportIssue"
    REPORT_AN_ISSUE_NAME_BOX = "id:ZSASMReportUserNameTextBox"
    REPORT_AN_ISSUE_SEND_BUTTON = "id:ZSASMReportSendButton"
    REPORT_AN_ISSUE_CC='id:ZSASMReportCCListTextBox'
    REPORT_AN_ISSUE_CANCEL='id:ZSASMReportCancelButton'
    REPAIR_APP = "id:ZSAMFSettingsRepairApp"

    # DIGITAL EXPERIENCE
    DIGITAL_EXPERIENCE_TAB = "id:ZSAMFMonitoringTab"
    DIGITAL_POWER_BUTTON_ON = "id:ZSAMFMonitoringPowerButtonLabel"
    DIGITAL_POWER_BUTTON_OFF = "id:ZSAMFMonitoringPowerButtonLabel"
    DIGITAL_EXPERIENCE_PASSWORD_BOX = 'id:ZSABWPasswordBox'
    DIGITAL_EXPERIENCE_PASSWORD_ACCEPT_BUTTON = 'id:ZSABWPasswordOKButton'
    DIGITAL_EXPERIENCE_PASSWORD_CANCEL_BUTTON = 'id:ZSABWPasswordCancelButton'
    DIGITAL_EXPERIENCE_PASSWORD_FAILED_OK_BUTTON = 'id:OkButton'
    # DISABLE REASON
    PASSWORD_DISABLE_REASON_BOX = "id:ZSABWPasswordDisableReasonBox"
    PASSWORD_DISABLE_REASON_OK_BUTTON = "id:ZSABWPasswordOKButton"

    DISABLE_REASON_BOX = "id:ZSABWConfirmDisableReasonBox"
    DISABLE_REASON_OK_BUTTON = "id:ZSABWConfirmAcceptButton"
    DISABLE_REASON_CANCEL_BUTTON = "id:ZSABWConfirmRejectButton"
    # UNINSTALL PASSWORD
    UNINSTALL_PASSWORD_BOX = ""
    UNINSTALL_PASSWORD_OK_BUTTON = ""
    UNINSTALL_PASSWORD_FAILURE_OK_BUTTON = ""
    SME_IP = "id:ZSAMFWebSecurityServerIPText"
    TUNNEL_STATUS_ZIA = "id:ZSAMFWebSecurityTunnelStatusText"
    ZIA_RETRY_CONNECTION_ERROR = "id:ZSAMFWebSecurityPowerButtonLabel"
    ERROR_DIALOG = "id:id:ErrorDialog"
    ERROR_VALUE = "id:ErrorValue"
    NOTIFICATIONS_TAB = "id:ZSAMFNotificationsTab"
    CLEAR_ALL_NOTIFICATIONS = "id:ClearNotificationButton"
    CLEAR_NOTIFICATIONS_CONTINUE = "id:ZSABWConfirmAcceptButton"
    # STRICT ENFORCEMENT
    STRICT_ENFORCEMENT_BANNER_HEADER = "id:ZSAUNFEnforceHdrText"
    STRICT_ENFORCEMENT_BANNER_MESSAGE = "id:ZSAUNFEnforceMsgText"
    # ZCC burger icon menu for SE and Machine Tunnel checks
    ZCC_SIDE_MENU_ICON = "id:ZSAUNFMenuButton"
    ZCC_UPDATE_POLICY_FROM_SIDE_MENU = "id:ZSASMUpdatePolicyButton"
    
    DISABLE_ANTI_TAMPERING = "id:ZSAMFSettingsTamperProtection"
    ENABLE_ANTI_TAMPERING = "id:ZSAMFSettingsTamperProtection"
    ENABLE_ANTI_TAMPERING_SUCCESS_OK_BUTTON = "id:ZSABWContinueButton"
    DISABLE_ANTI_TAMPERING_PASSWORD_BOX = 'id:ZSABWPasswordBox'
    DISABLE_ANTI_TAMPERING_PASSWORD_BOX_OK_BUTTON = 'id:ZSABWPasswordOKButton'
    DISABLE_ANTI_TAMPERING_PASSWORD_FAILED_OK_BUTTON = 'id:OkButton'

    ADD_PARTNER_TENANT_BUTTON = "id:ZSAMFMainSecureAccessAddPartnerButton"
    PARTNER_TENANT_USER_ID_BOX = "id:ZSAAddTenantUserNameText"
    PARTNER_TENANT_USER_ID_CONFIRM = "id:ZSAAddTenantSubmitButton"
    PARTNER_TENANT_USER_ID_CANCEL = "id:ZSAAddTenantCancelButton"
    OPEN_MAIN_TENANT_PAGE_BUTTON = "id:id:ZSAMFMainSecureAccessAddPartnerButton"
    MAIN_TENANT_SERVICE_STATUS = "id:ZSAMFSecureAccessMainAuthStatusText"
    MAIN_TENANT_POWER_BUTTON = "id:ZSAMFSecureAccessMainPowerButton"
    MAIN_TENANT_POWER_PASSWORD_BOX = "id:ZSABWPasswordBox"
    MAIN_TENANT_POWER_DISABLE_REASON_BOX = "id:ZSABWConfirmDisableReasonBox"
    MAIN_TENANT_POWER_PASSWORD_DISABLE_REASON_BOX = "id:ZSABWPasswordDisableReasonBox"
    MAIN_TENANT_POWER_DISABLE_PASSWORD_OK_BUTTON = "id:ZSABWPasswordOKButton"
    MAIN_TENANT_POWER_DISABLE_CONFIRM = "id:ZSABWConfirmAcceptButton"
    MAIN_TENANT_POWER_DISABLE_CANCEL = "id:ZSABWConfirmRejectButton"
    OPEN_PARTNER_TENANT_PAGE_BUTTON = "id:ZSAMFSecureAccessPartnerTenantUsername"
    PARTNER_TENANT_POWER_BUTTON = "id:ZSAMFSecureAccessMainPagePartnerPowerButton"
    PARTNER_TENANT_POWER_PASSWORD_BOX = "id:ZSABWPasswordBox"
    PARTNER_TENANT_POWER_DISABLE_REASON_BOX = "id:ZSABWPasswordDisableReasonBox"
    PARTNER_TENANT_POWER_DISABLE_CONFIRM = "id:ZSABWConfirmAcceptButton"
    PARTNER_TENANT_POWER_DISABLE_CANCEL = "id:ZSABWConfirmRejectButton"
    REMOVE_PARTNER_TENANT = "id:ZSAMFSecureAccessRemovePartnerButtonLabel" #"ZSAMFSecureAccessRemovePartnerButton"
    REMOVE_PARTNER_TENANT_CONFIRM = "id:ZSARemoveTenantSubmitButton"
    REMOVE_PARTNER_TENANT_CANCEL = "id:ZSARemoveTenantCancelButton"
    if UI_Common.operating_system=="windows":
        notification_chevron = "name:\"Show Hidden Icons\"" if sys.getwindowsversion().build >= 22000 else "name:\"Notification Chevron\""

class Zcc_Elements_Mac_Test:
    """ Class that contains Zscaler Mac test build app element IDs  """
    APP = "Zscaler"
    ZSCALER_LOGGEDIN_LOGO = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']"
    ZSA_TRAY_FORM = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']"
    ZCC_TRAY_ICON = "/AXApplication[@AXTitle='Zscaler']/AXMenuBar[1]/AXMenuBarItem[@AXSubrole='AXMenuExtra']"
    ZCC_EXIT_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXMenuBar[1]/AXMenuBarItem[@AXSubrole='AXMenuExtra']/AXMenu[0]/AXMenuItem[@AXTitle='Exit' and @AXIdentifier='showExitViewController']"
    ZCC_EXIT_CONFIRM_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Exit Zscaler Client Connector' and @AXIdentifier='_NS:8' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_logout']"
    # LOGIN ZIA
    USER_NAME = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXTextField[@AXIdentifier='Username']"
    LOGIN_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='Login' and @AXIdentifier='btn_login']"
    EULA_ACCEPT_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='Accept' and @AXIdentifier='btn_accept_cloud_selection']"
    PASSWORD_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXTextField[@AXIdentifier='Password' and @AXSubrole='AXSecureTextField']"
    LOGIN_BUTTON_PASSWORD = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='Login' and @AXIdentifier='btn_login']"
    LOGIN_ERROR_OK_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXSheet/AXGroup[@AXIdentifier='view_custom_alert']/AXGroup[@AXIdentifier='box_alert_action']/AXButton[@AXTitle='Ok' and @AXIdentifier='_NS:82']"
    ZCC_BACK_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXIdentifier='back_button_login']"
    # LOGOUT
    LOGOUT_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXIdentifier='btn_logout']"
    # CONFIRM_ACCEPT_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    CONFIRM_ACCEPT_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    LOGOUT_OK_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    LOGOUT_PASSWORD_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXTextField[@AXIdentifier='txt_password_turnoff' and @AXSubrole='AXSecureTextField']"
    LOGOUT_PASSWORD_ACCEPT_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    LOGOUT_PASSWORD_CANCEL_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Cancel' and @AXIdentifier='btn_cancel_turnoff']"
    # MORE
    APP_MORE = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXIdentifier='btn_more']"
    APP_UPDATE_POLICY = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box3_settings']/AXButton[@AXTitle='Update Policy' and @AXIdentifier='btn_update_policy_settings']"
    CLEAR_LOGS = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_settings']/AXButton[@AXTitle='Clear Logs' and @AXIdentifier='btn_clear_logs_settings']"
    RESTART_SERVICE = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_settings']/AXButton[@AXTitle='Restart Service' and @AXIdentifier='lbl_restart_service_settings']"
    RESTART_SERVICE_ACCEPT = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    START_PACKET_CAPTURE = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_settings']/AXButton[@AXTitle='Start Packet Capture' and @AXIdentifier='btn_show_packet_settings']"
    STOP_PACKET_CAPTURE = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_settings']/AXButton[@AXTitle='Stop Packet Capture' and @AXIdentifier='btn_show_packet_settings']"
    CAPTURE_STATUS = 'ZSAMFPacketCaptureStatus'
    EXPORT_LOGS = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_settings']/AXButton[@AXTitle='Export Logs' and @AXIdentifier='btn_report_issue_settings']"
    EXPORT_LOGS_TRAYBAR_MENU = "/AXApplication[@AXTitle='Zscaler']/AXMenuBar[1]/AXMenuBarItem[@AXSubrole='AXMenuExtra']/AXMenu[0]/AXMenuItem[@AXTitle='Export Logs' and @AXIdentifier='exportLogs:']"
    UPDATE_APP = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box3_settings']/AXButton[@AXTitle='Update App' and @AXIdentifier='btn_update_app_settings']"
    # INTERNET SECURITY
    INTERNET_SECURITY_TAB = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXIdentifier='btn_internet_security']"
    INTERNET_SECURITY_POWER_BUTTON_ON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_websecurity']/AXButton[@AXTitle='TURN ON' and @AXIdentifier='btn_turn_onoff_web_security']"
    INTERNET_SECURITY_POWER_BUTTON_OFF = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_websecurity']/AXButton[@AXTitle='TURN OFF' and @AXIdentifier='btn_turn_onoff_web_security']"
    INTERNET_SECURITY_PASSWORD_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXTextField[@AXIdentifier='txt_password_turnoff' and @AXSubrole='AXSecureTextField']"
    INTERNET_SECURITY_PASSWORD_ACCEPT_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    INTERNET_SECURITY_PASSWORD_CANCEL_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Cancel' and @AXIdentifier='btn_cancel_turnoff']"
    INTERNET_SECURITY_RETRY_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_websecurity']/AXButton[@AXTitle='RETRY' and @AXIdentifier='btn_turn_onoff_web_security']"
    # ZDP
    DATA_PREVENTION_TAB = ""
    DATA_PREVENTION_POWER_BUTTON_ON = ""
    DATA_PREVENTION_POWER_BUTTON_OFF = ""
    DATA_PREVENTION_PASSWORD_BOX = ""
    DATA_PREVENTION_PASSWORD_ACCEPT_BUTTON = ""
    DATA_PREVENTION_PASSWORD_FAILED_OK_BUTTON = ""
    DATA_PREVENTION_PASSWORD_CANCEL_BUTTON = ""
    # LOGIN ZPA OKTA
    OKTA_SIGNIN_PAGE = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[0]/AXGroup[0]/AXScrollArea[0]/AXWebArea[0]/AXGroup[0]/AXGroup[@AXDOMIdentifier='okta-sign-in' and @AXSubrole='AXLandmarkMain']/AXHeading[@AXTitle='Sign In']/AXStaticText[@AXValue='Sign In']"
    OKTA_SIGNIN_PAGE_2 = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[0]/AXGroup[0]/AXScrollArea[0]/AXWebArea[0]/AXGroup[@AXDOMIdentifier='okta-sign-in' and @AXSubrole='AXLandmarkMain']/AXHeading[@AXTitle='Sign In']/AXStaticText[@AXValue='Sign In']"    
    OKTA_USERNAME = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[0]/AXGroup[0]/AXScrollArea[0]/AXWebArea[0]/AXGroup[@AXDOMIdentifier='okta-sign-in' and @AXSubrole='AXLandmarkMain']/AXGroup[2]/AXStaticText[@AXValue='Username ']/AXStaticText[@AXValue='Username ']"
    OKTA_PASSWORD_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[0]/AXGroup[0]/AXScrollArea[0]/AXWebArea[0]/AXGroup[@AXDOMIdentifier='okta-sign-in' and @AXSubrole='AXLandmarkMain']/AXGroup[4]/AXStaticText[@AXValue='Password ']/AXStaticText[@AXValue='Password ']"
    REAUTH_OKTA_USERNAME = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[0]/AXGroup[0]/AXScrollArea[0]/AXWebArea[0]/AXGroup[@AXDOMIdentifier='okta-sign-in' and @AXSubrole='AXLandmarkMain']/AXGroup[2]/AXStaticText[@AXValue='Username ']/AXStaticText[@AXValue='Username ']"
    REAUTH_OKTA_PASSWORD_BOX ="/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[0]/AXGroup[0]/AXScrollArea[0]/AXWebArea[0]/AXGroup[@AXDOMIdentifier='okta-sign-in' and @AXSubrole='AXLandmarkMain']/AXGroup[4]/AXStaticText[@AXValue='Password ']/AXStaticText[@AXValue='Password ']"
    OKTA_SIGN_IN_BUTTON ="/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[0]/AXGroup[0]/AXScrollArea[0]/AXWebArea[0]/AXGroup[0]/AXGroup[@AXDOMIdentifier='okta-sign-in' and @AXSubrole='AXLandmarkMain']/AXButton[@AXTitle='Sign In' and @AXDOMIdentifier='okta-signin-submit']"
    OKTA_SIGN_IN_BUTTON_ID = 'okta-signin-submit'
    # PRIVATE ACCESS
    PRIVATE_ACCESS_TAB = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXIdentifier='btn_secure_access']"
    PRIVATE_ACCESS_POWER_BUTTON_ON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='TURN ON' and @AXIdentifier='btn_refresh_secure_access']"
    PRIVATE_ACCESS_POWER_BUTTON_OFF = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='TURN OFF' and @AXIdentifier='btn_refresh_secure_access']"
    PRIVATE_ACCESS_PASSWORD_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXTextField[@AXIdentifier='txt_password_turnoff' and @AXSubrole='AXSecureTextField']"
    PRIVATE_ACCESS_PASSWORD_ACCEPT_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    PRIVATE_ACCESS_PASSWORD_CANCEL_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Cancel' and @AXIdentifier='btn_cancel_turnoff']"
    PRIVATE_ACCESS_REAUTH_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='AUTHENTICATE' and @AXIdentifier='btn_re_auth_secure_access']"
    PRIVATE_ACCESS_REAUTH_BUTTON_2 = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='Reauthenticate' and @AXIdentifier='btn_re_auth_secure_access']"
    PRIVATE_ACCESS_EARLY_REAUTH_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='AUTHENTICATE EARLY' and @AXIdentifier='btn_re_auth_secure_access']"
    # REPORT AN ISSUE
    REPORT_AN_ISSUE_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_settings']/AXButton[@AXTitle='Report An Issue' and @AXIdentifier='btn_report_issue_settings']"
    REPORT_AN_ISSUE_NAME_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Report An Issue' and @AXSubrole='AXDialog']/AXTextField[@AXIdentifier='txt_name_report_issue']"
    REPORT_AN_ISSUE_SEND_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Report An Issue' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Send' and @AXIdentifier='btn_send_report_issue']"
    REPORT_AN_ISSUE_CC = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Report An Issue' and @AXSubrole='AXDialog']/AXTextField[@AXIdentifier='txt_email_cc_report_issue']"
    REPORT_AN_ISSUE_CANCEL = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXSheet[0]/AXButton[@AXTitle='Cancel' and @AXIdentifier='btn_cancel_report_issue']"
    # DIGITAL EXPERIENCE
    DIGITAL_EXPERIENCE_TAB = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXIdentifier='btn_upm']"
    DIGITAL_POWER_BUTTON_ON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box_upm']/AXButton[@AXTitle='TURN ON' and @AXIdentifier='btn_refresh_zdx_access']"
    DIGITAL_POWER_BUTTON_OFF = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box_upm']/AXButton[@AXTitle='TURN OFF' and @AXIdentifier='btn_refresh_zdx_access']"
    DIGITAL_EXPERIENCE_PASSWORD_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXTextField[@AXIdentifier='txt_password_turnoff' and @AXSubrole='AXSecureTextField']"
    DIGITAL_EXPERIENCE_PASSWORD_ACCEPT_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    DIGITAL_EXPERIENCE_PASSWORD_CANCEL_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Cancel' and @AXIdentifier='btn_cancel_turnoff']"
    # DISABLE REASON
    DISABLE_REASON_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXScrollArea[@AXIdentifier='_NS:75']/AXTextArea[@AXIdentifier='_NS:79']"
    DISABLE_REASON_OK_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    PASSWORD_DISABLE_REASON_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXScrollArea[@AXIdentifier='_NS:75']/AXTextArea[@AXIdentifier='_NS:79']"        
    PASSWORD_DISABLE_REASON_OK_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    # UNINSTALL PASSWORD
    UNINSTALL_PASSWORD_BOX = "/AXApplication[@AXTitle='osascript']/AXWindow[@AXTitle='Password' and @AXSubrole='AXDialog']/AXTextField[@AXSubrole='AXSecureTextField']"
    UNINSTALL_PASSWORD_OK_BUTTON = "/AXApplication[@AXTitle='osascript']/AXWindow[@AXTitle='Password' and @AXSubrole='AXDialog']/AXButton[@AXTitle='OK']"
    UNINSTALL_PASSWORD_FAILURE_OK_BUTTON = "/AXApplication[@AXTitle='Finder']/AXWindow[@AXSubrole='AXDialog']/AXButton[@AXTitle='Ok']"
    ZIA_RETRY_CONNECTION_ERROR = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector: Test Build' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_websecurity']/AXButton[@AXTitle='RETRY' and @AXIdentifier='btn_turn_onoff_web_security']"
    DISABLE_ANTI_TAMPERING = ""
    ENABLE_ANTI_TAMPERING = ""
    ENABLE_ANTI_TAMPERING_SUCCESS_OK_BUTTON = ""
    DISABLE_ANTI_TAMPERING_PASSWORD_BOX = ""
    DISABLE_ANTI_TAMPERING_PASSWORD_BOX_OK_BUTTON = ""
    DISABLE_ANTI_TAMPERING_PASSWORD_FAILED_OK_BUTTON = ''


class Zcc_Elements_Mac:
    """ Class that contains Zscaler Mac production build app element IDs  """
    APP = "Zscaler"
    ZSCALER_LOGGEDIN_LOGO = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']"
    ZSA_TRAY_FORM = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']"
    ZCC_TRAY_ICON = "/AXApplication[@AXTitle='Zscaler']/AXMenuBar[1]/AXMenuBarItem[@AXSubrole='AXMenuExtra']"
    ZCC_EXIT_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXMenuBar[1]/AXMenuBarItem[@AXSubrole='AXMenuExtra']/AXMenu[0]/AXMenuItem[@AXTitle='Exit' and @AXIdentifier='showExitViewController']"
    ZCC_EXIT_CONFIRM_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Exit Zscaler Client Connector' and @AXIdentifier='_NS:8' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_logout']"
    # LOGIN ZIA
    USER_NAME = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXTextField[@AXIdentifier='Username']"
    LOGIN_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='Login' and @AXIdentifier='btn_login']"
    EULA_ACCEPT_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='Accept' and @AXIdentifier='btn_accept_cloud_selection']"
    PASSWORD_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXTextField[@AXIdentifier='Password' and @AXSubrole='AXSecureTextField']"
    LOGIN_BUTTON_PASSWORD = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='Login' and @AXIdentifier='btn_login']"
    LOGIN_ERROR_OK_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXSheet/AXGroup[@AXIdentifier='view_custom_alert']/AXGroup[@AXIdentifier='box_alert_action']/AXButton[@AXTitle='Ok' and @AXIdentifier='_NS:82']"
    ZCC_BACK_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXIdentifier='back_button_login']"
    # LOGOUT
    LOGOUT_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXIdentifier='btn_logout']"
    CONFIRM_ACCEPT_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    LOGOUT_OK_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    LOGOUT_PASSWORD_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXTextField[@AXIdentifier='txt_password_turnoff' and @AXSubrole='AXSecureTextField']"
    LOGOUT_PASSWORD_ACCEPT_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    LOGOUT_PASSWORD_CANCEL_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Cancel' and @AXIdentifier='btn_cancel_turnoff']"
    # MORE
    APP_MORE = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXIdentifier='btn_more']"
    APP_UPDATE_POLICY = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box3_settings']/AXButton[@AXTitle='Update Policy' and @AXIdentifier='btn_update_policy_settings']"
    CLEAR_LOGS = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_settings']/AXButton[@AXTitle='Clear Logs' and @AXIdentifier='btn_clear_logs_settings']"
    RESTART_SERVICE = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_settings']/AXButton[@AXTitle='Restart Service' and @AXIdentifier='lbl_restart_service_settings']"
    RESTART_SERVICE_ACCEPT = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    START_PACKET_CAPTURE = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_settings']/AXButton[@AXTitle='Start Packet Capture' and @AXIdentifier='btn_show_packet_settings']"
    STOP_PACKET_CAPTURE = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_settings']/AXButton[@AXTitle='Stop Packet Capture' and @AXIdentifier='btn_show_packet_settings']"
    CAPTURE_STATUS = 'ZSAMFPacketCaptureStatus'
    EXPORT_LOGS = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_settings']/AXButton[@AXTitle='Export Logs' and @AXIdentifier='btn_report_issue_settings']"
    EXPORT_LOGS_TRAYBAR_MENU = "/AXApplication[@AXTitle='Zscaler']/AXMenuBar[1]/AXMenuBarItem[@AXSubrole='AXMenuExtra']/AXMenu[0]/AXMenuItem[@AXTitle='Export Logs' and @AXIdentifier='exportLogs:']"
    UPDATE_APP = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box3_settings']/AXButton[@AXTitle='Update App' and @AXIdentifier='btn_update_app_settings']"
    # INTERNET SECURITY
    INTERNET_SECURITY_TAB = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXIdentifier='btn_internet_security']"
    INTERNET_SECURITY_POWER_BUTTON_ON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_websecurity']/AXButton[@AXTitle='TURN ON' and @AXIdentifier='btn_turn_onoff_web_security']"
    INTERNET_SECURITY_POWER_BUTTON_OFF = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_websecurity']/AXButton[@AXTitle='TURN OFF' and @AXIdentifier='btn_turn_onoff_web_security']"
    INTERNET_SECURITY_PASSWORD_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXTextField[@AXIdentifier='txt_password_turnoff' and @AXSubrole='AXSecureTextField']"
    INTERNET_SECURITY_PASSWORD_ACCEPT_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    INTERNET_SECURITY_PASSWORD_CANCEL_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Cancel' and @AXIdentifier='btn_cancel_turnoff']"
    INTERNET_SECURITY_RETRY_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_websecurity']/AXButton[@AXTitle='RETRY' and @AXIdentifier='btn_turn_onoff_web_security']"  
    # ZDP
    DATA_PREVENTION_TAB = ""
    DATA_PREVENTION_POWER_BUTTON_ON = ""
    DATA_PREVENTION_POWER_BUTTON_OFF = ""
    DATA_PREVENTION_PASSWORD_BOX = ""
    DATA_PREVENTION_PASSWORD_ACCEPT_BUTTON = ""
    DATA_PREVENTION_PASSWORD_FAILED_OK_BUTTON = ""
    DATA_PREVENTION_PASSWORD_CANCEL_BUTTON = ""
    # LOGIN ZPA OKTA
    OKTA_SIGNIN_PAGE = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[0]/AXGroup[0]/AXScrollArea[0]/AXWebArea[0]/AXGroup[0]/AXGroup[@AXDOMIdentifier='okta-sign-in' and @AXSubrole='AXLandmarkMain']/AXHeading[@AXTitle='Sign In']/AXStaticText[@AXValue='Sign In']"
    OKTA_SIGNIN_PAGE_2 = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[0]/AXGroup[0]/AXScrollArea[0]/AXWebArea[0]/AXGroup[@AXDOMIdentifier='okta-sign-in' and @AXSubrole='AXLandmarkMain']/AXHeading[@AXTitle='Sign In']/AXStaticText[@AXValue='Sign In']"
    OKTA_USERNAME = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[0]/AXGroup[0]/AXScrollArea[0]/AXWebArea[0]/AXGroup[@AXDOMIdentifier='okta-sign-in' and @AXSubrole='AXLandmarkMain']/AXGroup[4]/AXStaticText[@AXValue='Username ']/AXStaticText[@AXValue='Username ']"
    OKTA_PASSWORD_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[0]/AXGroup[0]/AXScrollArea[0]/AXWebArea[0]/AXGroup[@AXDOMIdentifier='okta-sign-in' and @AXSubrole='AXLandmarkMain']/AXGroup[5]/AXStaticText[@AXValue='Password ']/AXStaticText[@AXValue='Password ']"
    OKTA_SIGN_IN_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[0]/AXGroup[0]/AXScrollArea[0]/AXWebArea[0]/AXGroup[0]/AXGroup[@AXDOMIdentifier='okta-sign-in' and @AXSubrole='AXLandmarkMain']/AXButton[@AXTitle='Sign In' and @AXDOMIdentifier='okta-signin-submit']"
    OKTA_SIGN_IN_BUTTON_ID = 'okta-signin-submit'
    # REAUTH ZPA OKTA
    REAUTH_OKTA_USERNAME = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[0]/AXGroup[0]/AXScrollArea[0]/AXWebArea[0]/AXGroup[@AXDOMIdentifier='okta-sign-in' and @AXSubrole='AXLandmarkMain']/AXGroup[2]/AXStaticText[@AXValue='Username ']/AXStaticText[@AXValue='Username ']"
    REAUTH_OKTA_PASSWORD_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[0]/AXGroup[0]/AXScrollArea[0]/AXWebArea[0]/AXGroup[@AXDOMIdentifier='okta-sign-in' and @AXSubrole='AXLandmarkMain']/AXGroup[4]/AXStaticText[@AXValue='Password ']/AXStaticText[@AXValue='Password ']"
    # PRIVATE ACCESS
    PRIVATE_ACCESS_TAB = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXIdentifier='btn_secure_access']"
    PRIVATE_ACCESS_POWER_BUTTON_ON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='TURN ON' and @AXIdentifier='btn_refresh_secure_access']"
    PRIVATE_ACCESS_POWER_BUTTON_OFF = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='TURN OFF' and @AXIdentifier='btn_refresh_secure_access']"
    PRIVATE_ACCESS_PASSWORD_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXTextField[@AXIdentifier='txt_password_turnoff' and @AXSubrole='AXSecureTextField']"
    PRIVATE_ACCESS_PASSWORD_ACCEPT_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    PRIVATE_ACCESS_PASSWORD_CANCEL_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Cancel' and @AXIdentifier='btn_cancel_turnoff']"
    PRIVATE_ACCESS_REAUTH_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='AUTHENTICATE' and @AXIdentifier='btn_re_auth_secure_access']"
    PRIVATE_ACCESS_REAUTH_BUTTON_2 = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='Reauthenticate' and @AXIdentifier='btn_re_auth_secure_access']"
    PRIVATE_ACCESS_EARLY_REAUTH_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='AUTHENTICATE EARLY' and @AXIdentifier='btn_re_auth_secure_access']"
    # REPORT AN ISSUE
    REPORT_AN_ISSUE_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_settings']/AXButton[@AXTitle='Report An Issue' and @AXIdentifier='btn_report_issue_settings']"
    REPORT_AN_ISSUE_NAME_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Report An Issue' and @AXSubrole='AXDialog']/AXTextField[@AXIdentifier='txt_name_report_issue']"
    REPORT_AN_ISSUE_SEND_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Report An Issue' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Send' and @AXIdentifier='btn_send_report_issue']"
    REPORT_AN_ISSUE_CC = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Report An Issue' and @AXSubrole='AXDialog']/AXTextField[@AXIdentifier='txt_email_cc_report_issue']"
    REPORT_AN_ISSUE_CANCEL = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXSheet[0]/AXButton[@AXTitle='Cancel' and @AXIdentifier='btn_cancel_report_issue']"
    # DIGITAL EXPERIENCE
    DIGITAL_EXPERIENCE_TAB = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXButton[@AXIdentifier='btn_upm']"
    DIGITAL_POWER_BUTTON_ON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box_upm']/AXButton[@AXTitle='TURN ON' and @AXIdentifier='btn_refresh_zdx_access']"
    DIGITAL_POWER_BUTTON_OFF = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box_upm']/AXButton[@AXTitle='TURN OFF' and @AXIdentifier='btn_refresh_zdx_access']"
    DIGITAL_EXPERIENCE_PASSWORD_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXTextField[@AXIdentifier='txt_password_turnoff' and @AXSubrole='AXSecureTextField']"
    DIGITAL_EXPERIENCE_PASSWORD_ACCEPT_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    DIGITAL_EXPERIENCE_PASSWORD_CANCEL_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Cancel' and @AXIdentifier='btn_cancel_turnoff']"
    # DISABLE REASON
    DISABLE_REASON_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXScrollArea[@AXIdentifier='_NS:75']/AXTextArea[@AXIdentifier='_NS:79']"
    DISABLE_REASON_OK_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    PASSWORD_DISABLE_REASON_BOX = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXTextField[@AXIdentifier='txt_password_turnoff' and @AXSubrole='AXSecureTextField']"
    PASSWORD_DISABLE_REASON_OK_BUTTON = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Ok' and @AXIdentifier='btn_ok_turnoff']"
    # UNINSTALL PASSWORD
    UNINSTALL_PASSWORD_BOX = "/AXApplication[@AXTitle='osascript']/AXWindow[@AXTitle='Password' and @AXSubrole='AXDialog']/AXTextField[@AXSubrole='AXSecureTextField']"
    UNINSTALL_PASSWORD_OK_BUTTON = "/AXApplication[@AXTitle='osascript']/AXWindow[@AXTitle='Password' and @AXSubrole='AXDialog']/AXButton[@AXTitle='OK']"
    UNINSTALL_PASSWORD_FAILURE_OK_BUTTON = "/AXApplication[@AXTitle='Finder']/AXWindow[@AXSubrole='AXDialog']/AXButton[@AXTitle='Ok']"
    ZIA_RETRY_CONNECTION_ERROR = "/AXApplication[@AXTitle='Zscaler']/AXWindow[@AXTitle='Zscaler Client Connector' and @AXIdentifier='window_zs' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='box2_websecurity']/AXButton[@AXTitle='RETRY' and @AXIdentifier='btn_turn_onoff_web_security']"
    DISABLE_ANTI_TAMPERING = ""
    ENABLE_ANTI_TAMPERING = ""
    ENABLE_ANTI_TAMPERING_SUCCESS_OK_BUTTON = ""
    DISABLE_ANTI_TAMPERING_PASSWORD_BOX = ""
    DISABLE_ANTI_TAMPERING_PASSWORD_BOX_OK_BUTTON = ""
    DISABLE_ANTI_TAMPERING_PASSWORD_FAILED_OK_BUTTON = ''


class ZCC():
    def __init__(self,
                 handle: str ="easy",
                 dynamic_element_check: bool = True,
                 logger: bool =None):
        self.logger = (logger if logger else UI_Common.CustomLogger.Initialize_Logger(Log_File_Name="ZCC.log", Log_Level="INFO"))
        self.UI = UI_Common.GUI(handle=handle,logger=self.logger)
        self.operating_system = self.UI.operating_system
        if self.operating_system=="windows":self.windows = self.UI.windows
        self.logger.info("Initialized ZCC GUI")
        self.dynamic_element_check = dynamic_element_check
        self.sleep_factor = 1
        self.Initialize_Elements()
        self.startTimeForLogSearch=""
        try:
            from common_lib.log_ops import log_ops
        except Exception as E:
            self.logger.error(f"ERROR :: Unable to import log ops - {E}")
            raise Exception(f"ERROR :: Unable to import log ops - {E}")
        try:
            from common_lib.System_Ops import Sys_Ops
        except Exception as E:
            self.logger.error(f"ERROR :: Unable to import sys ops - {E}")
            raise Exception(f"ERROR :: Unable to import sys ops - {E}")
        self.SysOps = Sys_Ops(self.logger)
        self.logOps = log_ops(console_handle_needed=False)
        try:
            self.zccBuild = self.logOps.get_current_zcc_version()
        except Exception as E:
            self.logger.error(f"Unable to get zcc version :: {E}\n\n")
            self.zccBuild = ''

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def Initialize_Elements(self):  # initialize elements for win/mac
       
        if self.operating_system == "windows":
            self.elements = copy.deepcopy(Zcc_Elements_Win)
            return
        else:
            self.UI.start_app(app=Zcc_Elements_Mac.APP, sleep_time=5)
            if self.UI.check_if_exists(Zcc_Elements_Mac.ZSA_TRAY_FORM, boolean=True):
                self.logger.info("ZCC Mac Build Nature: Normal")
                self.elements = copy.deepcopy(Zcc_Elements_Mac)
            elif self.UI.check_if_exists(Zcc_Elements_Mac_Test.ZSA_TRAY_FORM, boolean=True):
                self.logger.info("ZCC Mac Build Nature: Test")
                self.elements = copy.deepcopy(Zcc_Elements_Mac_Test)
            else:
                self.logger.info("\n\n\nWarning :: ZCC Probably not installed!\n\n\n")
            self.UI.app = self.elements.APP
            return

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def calculateFuncExecuteTime(func):
        '''
        Decorator used for calculating time taken for execution of a function
        Flow: ------------------------------
            1. Func is passed to calculateFuncExecuteTime which calls wrapper func
            2. Wrapper func then first gets the current time(start time) and then make call to the given func as arg in calculateFuncExecuteTime
            3. After func is executed, we then get the current time(end time) and then calculate endtime-starttime and get the time difference
        '''
        @wraps(func)
        def wrapper(*args,**kwargs):
            startTime = time.time()
            func(*args,**kwargs)
            endTime = time.time()
            args[0].FileTransferTime = int(endTime-startTime)
            args[0].logger.critical(f"{'*'*50}")
            args[0].logger.critical(f"TIME TAKEN FOR {func.__name__} : {args[0].FileTransferTime} Seconds")
            args[0].logger.critical(f"{'*'*50}")
        return wrapper

    # ------------------------------------------------------------------------------------------------------------------------------------------------------   
    def Bring_Zcc_To_Focus(self,
                        number_of_trails: int = 2,      # number of times to try to open Zcc before raising exception
                        sleep_before_focus: int = 0,    # int seconds to sleep before bringing zcc to focus
                           ):  
        time.sleep(sleep_before_focus)
        for b in range(2 if self.dynamic_element_check else 1):
            for i in range(number_of_trails):
                try:
                    # open zcc and click on ZSA Tray
                    self.UI.click(self.elements.ZSA_TRAY_FORM, app=self.elements.APP, AEFC="ZSA Tray not Found", max_threshold=10, sleep_time=0)
                    self.logger.info("Success :: ZCC brought to Focus!")
                    return
                except Exception as e:
                    self.logger.info(f"Warning :: trying to bring ZCC to Focus again! :: {e}")
            if self.dynamic_element_check:
                self.logger.info("Dynamic Element Check started. we are in the end game now!")
                self.Initialize_Elements()
        
        self.logger.error("Failed to bring ZCC to Focus")
        raise Exception("Error :: Failed to bring ZCC to Focus")
    
    # ---------------------------------------------------------- #
    #               HELPER FUNCTIONS FOR ZCC LOGIN               #   
    # ---------------------------------------------------------- #
    def Do_Okta_Login(self,     
                      user_id : str,                    # okta username
                      user_password : str,              # okta password
                      search_log_time : str,            # time to be used for log search - datetime.now() should be passed
                      partial_login : bool,             # defines whether to terminate login or do complete login
                      search_file: str = "latest"       # defines whether to search latest file or oldest file
                      ):
        try:
            logsToBeSearched = "Document navigated URL: https://dev-" if self.operating_system=="windows" else "didFinishNavigation:https://dev"
            self.logger.info(f"Logs to be searched :: {logsToBeSearched}")
            self.logOps.search_log_file(file="ZSATray",words_to_find_in_line=[logsToBeSearched],start_timestamp=search_log_time,wait_time=120,start_line=self.logOps.recent_log_line,search_file=search_file)
            self.UI.click(self.elements.ZSA_TRAY_FORM, app=self.elements.APP, AEFC="ZSA Tray not Found", max_threshold=10, sleep_time=0)
        except Exception as E:
            self.logger.error(f"Unable to click on okta sign in :: {E}")
            raise Exception(E)
        if partial_login:
                self.Bring_Zcc_To_Focus()
                self.logger.info("Aborting Okta Login , hitting back button on ZCC")
                self.UI.click(self.elements.ZCC_BACK_BUTTON_AT_OKTA,sleep_time=1, AEFC="Cannot click ZCC Back Button")
                self.startTimeForLogSearch = datetime.now()         
                return
        if self.operating_system=="mac":    # keystrokes for mac
            cmd = """
            osascript -e 'tell application "System Events" to keystroke "a" using {command down}' 
            """
            time.sleep(1)
            self.logger.info(f"Hitting first tab to land on username - mac")
            time.sleep(1)
            pyautogui.press("tab")
            time.sleep(1)
            self.logger.info(f'running {cmd}')
            os.system(cmd)
            time.sleep(1)
            self.logger.info(f"Entering username :: {user_id}")
            pyautogui.write(user_id)
            time.sleep(1)
            self.logger.info(f"Hitting tab to land on password")
            pyautogui.press("tab")
            time.sleep(1)
            self.logger.info(f"Entering password :: {user_password}")
            pyautogui.write(user_password)
            time.sleep(1)
            self.logger.info(f"Hitting enter")
            pyautogui.press('enter')
            # self.logger.info("sleeping for 10 seconds")
            # time.sleep(10)
        else:                               # keystrokes for win
            time.sleep(1)
            self.logger.info(f"Hitting first tab to land on username - windows")
            # self.windows.send_keys(keys="{CTRL}a")
            time.sleep(1)
            self.windows.send_keys(keys="{TAB}")
            #pyautogui.press("tab")
            time.sleep(1)
            self.windows.send_keys(keys="{CTRL}a")
            self.windows.send_keys(keys=user_id)
            time.sleep(1)
            self.logger.info(f"Hitting tab to land on password")
            self.windows.send_keys(keys="{TAB}")
            time.sleep(1)
            self.windows.send_keys(keys="{CTRL}a")
            self.windows.send_keys(keys=user_password)
            time.sleep(1)
            self.logger.info(f"Hitting enter")
            self.windows.send_keys(keys="{ENTER}")
            # self.logger.info("sleeping for 10 seconds")
            # time.sleep(10)
        self.startTimeForLogSearch = datetime.now()         
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------   
    def Initial_ZCC_Login_Window(self,      # helper function for initial userfield at zcc login page
                                 USERNAME: str):
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.type(self.elements.USER_NAME, text=USERNAME, sleep_time=1, AEFC="Something wrong with username")
            self.UI.click(self.elements.LOGIN_BUTTON, sleep_time=0, AEFC="Unable to Click Login Button")
            self.startTimeForLogSearch = datetime.now()         
        except Exception as e:
            self.logger.error(f"Error :: Login Failed at USER_ID Page :: {e}")
            raise Exception(f"Error :: Login Failed at USER_ID Page :: {e}")
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------   
    def Handle_Aup(self,                    # helper function for handling zcc aup login
                   TIME_FOR_LOG_SEARCH,     # datetime.now()
                   ZIA_SAML_LOGIN: bool = False,
                   CANCEL_AUP: bool = False,
                   ):
        try:
            windowsLog = "SAML Request: https://login" if ZIA_SAML_LOGIN else 'Login URL:'
            LoginLogs =  windowsLog if self.operating_system!="mac" else "auppage.html"
            self.logger.info(f"Logs to be searched :: {LoginLogs}")
            
            self.logOps.search_log_file(file="ZSATray" if self.operating_system!="windows" else "ZSATrayManager_",words_to_find_in_line=[LoginLogs],start_timestamp=self.startTimeForLogSearch)  
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.EULA_DECLINE_BUTTON if CANCEL_AUP else self.elements.EULA_ACCEPT_BUTTON, sleep_time=1, AEFC="Cannot click AUP Decline" if CANCEL_AUP else "Cannot click AUP Accept")
            self.startTimeForLogSearch = datetime.now()         
        except Exception as e:
            self.logger.error(f"Error :: Login Failed at AUP Page :: {e}")
            raise Exception(f"Error :: Login Failed at AUP Page :: {e}")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------      
    def Handle_Zia_Saml(self,               # helper function for zia saml login
                        USERNAME: str,
                        PASSWORD : str,
                        TIME_FOR_LOG_SEARCH,        #datetime.now() object
                        CANCEL_ZIA_SAML_LOGIN : bool
                        ):
        try:
            #time.sleep(15)
            self.Do_Okta_Login(user_id=USERNAME,user_password=PASSWORD,search_log_time=TIME_FOR_LOG_SEARCH,partial_login=CANCEL_ZIA_SAML_LOGIN,search_file="oldest" if self.operating_system=="windows" else "latest")
            if CANCEL_ZIA_SAML_LOGIN:
                self.logger.info("Login aborted at ZIA SAML")
                return
        except Exception as e:
            self.logger.error(f"Login failed at ZIA SAML OKTA Page :: {e}")
            raise Exception(f"Login failed at ZIA SAML OKTA Page :: {e}")
        
    # ------------------------------------------------------------------------------------------------------------------------------------------------------   
    def Handle_Zia_Login(self,              # helper function for zia login
                        PASSWORD: str,
                        TIME_FOR_LOG_SEARCH,    #datetime.now() object
                        CANCEL_ZIA_LOGIN: bool):
        
        if CANCEL_ZIA_LOGIN:
            self.logger.info("Aborting Login at ZIA, hitting back button on ZCC")
            self.UI.click(self.elements.ZCC_BACK_BUTTON,sleep_time=1, AEFC="Cannot click ZCC Back Button")
            return
        try:
            #if skipUserDomainPage==True: self.UI.type(self.elements.USER_NAME, text=USER_ID, sleep_time=1, AEFC="Something wrong with username")
            #self.UI.type(self.elements.USER_NAME, text=ZIA_USER, sleep_time=1, AEFC="Something wrong with username")
            self.Bring_Zcc_To_Focus()
            isPasswordBoxUp = False
            LoginLogs = 'Login URL:' if self.operating_system=="windows" else 'https://login.'
            self.logger.info(f"Logs to be searched :: {LoginLogs}")
            
            try:
                self.logOps.search_log_file(file="ZSATray" if self.operating_system!="windows" else "ZSATrayManager_",words_to_find_in_line=[LoginLogs],start_timestamp=TIME_FOR_LOG_SEARCH)
                isPasswordBoxUp=True
            except Exception as E:
                isPasswordBoxUp=False
            
            if isPasswordBoxUp:self.logger.info(f"Login URL log found :: {self.logOps.recent_log_line}")
            else:
                self.logger.error("Login URL log not found")
                raise Exception("Login URL log not found")
            
            self.UI.type(self.elements.PASSWORD_BOX, text=PASSWORD, sleep_time=1, AEFC="Password button not found")
            self.startTimeForLogSearch = datetime.now()
            self.UI.click(self.elements.LOGIN_BUTTON_PASSWORD, sleep_time=0, AEFC="Unable to click Login Button")
        except Exception as e:
            self.logger.error(f"Error :: Login Failed at Form PASSWORD Page :: {e}")
            raise Exception(f"Error :: Login Failed at Form PASSWORD Page :: {e}")
        
    def Handle_Zia_One_Id_Login(self): # place holder for zia one id login
        pass

    # ------------------------------------------------------------------------------------------------------------------------------------------------------   
    def Handle_Zpa_Login(self,              # helper function for zpa login
                        USERNAME: str,
                        PASSWORD : str,
                        TIME_FOR_LOG_SEARCH,        #datetime.now() object
                        CANCEL_ZPA_LOGIN : bool
                        ):
        try:
            self.Do_Okta_Login(user_id=USERNAME,user_password=PASSWORD,search_log_time=TIME_FOR_LOG_SEARCH,partial_login=CANCEL_ZPA_LOGIN)
            if CANCEL_ZPA_LOGIN:
                self.logger.info("Login aborted at ZPA SAML")
                return
        except Exception as e:
            self.logger.error(f"Login failed at ZPA OKTA Page :: {e}")
            raise Exception(f"Login failed at ZPA OKTA Page :: {e}")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------    
    @allure.step("zcc login")       
    @calculateFuncExecuteTime
    def Login(self,                              # PARENT FUNCTION FOR ZCC LOGIN
              ZIA_USER: str = None,              # zia user id
              ZIA_PASSWORD: str = None,          # zia user password
              ZPA_USER: str = None,              # zpa user id
              ZPA_PASSWORD: str = None,          # zpa user password
              AUP: bool= False,                  # Defines AUP enabled or not 
              ZIA_SAML_LOGIN: bool= False,       # Defines ZIA SAML enabled or not
              ZIA_ONE_ID_LOGIN: bool= False,       # Defines ZIA SAML enabled or not
              CANCEL_LOGIN_AT_ZIA: bool= False,  # Defines whether to cancel login at ZIA login page 
              CANCEL_LOGIN_AT_ZPA: bool= False,  # Defines whether to cancel login at ZPA login page
              CANCEL_AUP: bool= False,           # Defines whether to cancel or decline AUP
              sleep_time: int = 10               # Defines sleep time after login is done
              ):        
        
        ZIA,ZPA = False,False

        if ZIA_USER==ZIA_PASSWORD==ZPA_USER==ZPA_PASSWORD==None:
            # no zia user is given, check for zpa user
            self.logger.error("Neither ZIA or ZPA user given to be logged in, cannot login")
            raise Exception("Neither ZIA or ZPA user given to be logged in, cannot login")
        
        if ZIA_USER and ZIA_PASSWORD:ZIA=True
        if ZPA_USER and ZPA_PASSWORD: ZPA=True
        if ZIA and ZPA:self.logger.info("Given user details are ZIA+ZPA")
        if not ZIA:self.logger.info("Given user is ZPA only user")
       
       # Intial Username field -------------------------
        if ZIA or ZPA:
            self.Initial_ZCC_Login_Window(USERNAME=ZIA_USER if ZIA else ZPA_USER)
        
        # AUP -------------------------
        if AUP:
            self.Handle_Aup(TIME_FOR_LOG_SEARCH=self.startTimeForLogSearch,ZIA_SAML_LOGIN=ZIA_SAML_LOGIN,CANCEL_AUP=CANCEL_AUP)
        
        # ZIA -------------------------
        if ZIA:
            if ZIA_SAML_LOGIN:
                self.Handle_Zia_Saml(USERNAME=ZIA_USER,PASSWORD=ZIA_PASSWORD,TIME_FOR_LOG_SEARCH=self.startTimeForLogSearch,CANCEL_ZIA_SAML_LOGIN=CANCEL_LOGIN_AT_ZIA)
            elif ZIA_ONE_ID_LOGIN:
                self.Handle_Zia_One_Id_Login()
            else:
                self.Handle_Zia_Login(PASSWORD=ZIA_PASSWORD,TIME_FOR_LOG_SEARCH=self.startTimeForLogSearch,CANCEL_ZIA_LOGIN=CANCEL_LOGIN_AT_ZIA)
        
        # ZPA -------------------------
        if ZPA:
            self.Handle_Zpa_Login(USERNAME=ZPA_USER,PASSWORD=ZPA_PASSWORD,TIME_FOR_LOG_SEARCH=self.startTimeForLogSearch,CANCEL_ZPA_LOGIN=CANCEL_LOGIN_AT_ZPA)
        self.logger.info("Success :: Logged into ZCC Succesfully!")
        if sleep_time>0:
            print(f'Sleeping for {sleep_time}')
            time.sleep(sleep_time)
            
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @calculateFuncExecuteTime
    @allure.step("Logout ZCC")
    def Logout( self,
                password: str =None,            # Passowrd/OTP to be used for logout
                sleep_time: int =180,           # number of seconds to sleep when executed
                failure_expected: bool=False,   # Defines whether logout should fail - used with incorrect password
                cancel_Logout: bool = False     # Defines whether to cancel logout operation
               ):
        
        if sleep_time==0:sleep_time=30
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.LOGOUT_BUTTON, sleep_time=1 * self.sleep_factor,AEFC="Unable to click Logout Button")

            if cancel_Logout:
                self.logger.info("Aborting Logout")
                self.UI.click(self.elements.LOGOUT_PASSWORD_CANCEL_BUTTON, sleep_time=0, AEFC="Unable to confirm Logout disable")
                return
            if password: 
                self.UI.type(self.elements.LOGOUT_PASSWORD_BOX, text=password, sleep_time=1*self.sleep_factor, AEFC="Unable to enter password")
                self.UI.click(self.elements.LOGOUT_PASSWORD_ACCEPT_BUTTON, sleep_time=0*self.sleep_factor, AEFC="Unable to confirm Logout")
                if failure_expected:    # mostly used when incorrect password is given intentionally 
                    if self.operating_system != "mac":
                        for i in range(4):
                            try:
                                self.UI.click(self.elements.LOGOUT_PASSWORD_FAILED_OK_BUTTON, sleep_time=0, AEFC="Unable to confirm Logout disable")
                                return
                            except:
                                print("Wait for the operation to complete")
                    else:
                        self.UI.click(self.elements.LOGOUT_PASSWORD_CANCEL_BUTTON, sleep_time=0, AEFC="Unable to confirm Logout disable")
                        return

            else:
                startTimeForLogSearch = datetime.now()
                self.UI.click(self.elements.CONFIRM_ACCEPT_BUTTON, sleep_time=0, AEFC="Unable to confirm  ZCC Logout")
            logsToBeSearched = 'Done with zsaTrayManagerEventListener' if self.operating_system=="windows" else 'logout success'
            isLogoutValidated = False
            try:
                self.logOps.search_log_file(file="ZSATray" if self.operating_system!="windows" else "ZSATrayManager_",words_to_find_in_line=[logsToBeSearched],start_timestamp=startTimeForLogSearch)
                isLogoutValidated=True
            except Exception as E: isLogoutValidated=False
            if isLogoutValidated:
                self.Bring_Zcc_To_Focus()
                self.logger.info("Success :: Logged Out !")
            else: raise Exception(f"Logout not validated, max wait time was {sleep_time}")
        except Exception as e: 
            self.logger.error(f"Logout Failed at ZSTray Page :: {e}\n".format(e))
            raise Exception(f"Error :: Logout Failed at ZSTray Page :: {e}\n".format(e))
        assert not self.UI.check_if_exists(self.elements.LOGOUT_OK_BUTTON, boolean=True, AESC="Logout Button not accepted", AEFC="Logged out well")
        self.logger.info("Success :: Logged out!")
    

    def Click_Login_Error_Ok_Button(self):
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.LOGIN_ERROR_OK_BUTTON, sleep_time=0, AEFC="Unable to click More button on ZCC")
        except Exception as e:
            self.logger.error(f"Click Login Error OK Failed :: {e}\n".format(e))
            raise Exception(f"Error :: Click Login Error OK Failed :: {e}\n".format(e))

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("validate zcc logged in")
    def Validate_Zcc_Logged_In(self,
                               return_bool: bool = False     # Defines whether to return bool value to be used by other functions
                               ):
        i=0
        zccStateValidated = False
        while i<2:
            try:
                self.Bring_Zcc_To_Focus()
                self.UI.click(path=self.elements.LOGOUT_BUTTON,sleep_time=0, AEFC="Unable to find logout button on ZCC",do_click=False,max_threshold=1,retry_frequency=1)
                zccStateValidated=True
            except:
                i+=1
                time.sleep(2)
            else:
                break
        if zccStateValidated:
            self.logger.info("Logout button exists, zcc logged in")
            if return_bool:return True
        else:
            self.logger.info("Logout button does not exists, zcc not logged in")
            if return_bool:return False
            else:raise Exception("Logout button does not exists, zcc not logged in")
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("validate zcc logged out")
    def Validate_Zcc_Logged_Out(self,
                               return_bool: bool = False     # Defines whether to return bool value to be used by other functions
                               ):
        i=0
        zccStateValidated = False
        while i<2:
            try:
                self.Bring_Zcc_To_Focus()
                self.UI.click(path=self.elements.LOGIN_BUTTON,sleep_time=0, AEFC="Unable to find login button on ZCC",do_click=False,max_threshold=1,retry_frequency=1)
                zccStateValidated=True
            except:
                i+=1
                time.sleep(2)
            else:
                break
        if zccStateValidated:
            self.logger.info("Login button exists, zcc logged out")
            if return_bool:return True
        else:
            self.logger.info("Login button does not exists, zcc not logged out")
            if return_bool:return False
            else:raise Exception("Login button does not exists, zcc not logged out")
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @calculateFuncExecuteTime
    @allure.step("Enable/disable Service ZIA/ZPA/ZDX")
    def Toggle_Service( self,
                        service: str,                   # Which service to Toggle (ZIA/ZPA/ZDX)
                        action: bool =True,             # Turn on if True, otherwise Turn off
                        password: str =None,            # Password/OTP for disabling service
                        disable_reason: str =None,      # Disable reason to enter while disabling any zscaler service (zia/zpa/zdx)
                        sleep_time=10,                  # Number of seconds to sleep after execution
                        cancel_toggle: bool =False,     # Determines whether to cancel toggle operation or not
                        failure_expected: bool =False   # Expect that password will not be accepted. deal accordingly
                        ):                
        if service=="ZIA":
            TAB = self.elements.INTERNET_SECURITY_TAB
            TOGGLE_ON = self.elements.INTERNET_SECURITY_POWER_BUTTON_ON
            TOGGLE_OFF = self.elements.INTERNET_SECURITY_POWER_BUTTON_OFF
            PASSOWRD_BOX = self.elements.INTERNET_SECURITY_PASSWORD_BOX
            DISABLE_REASON_BOX_WITH_PASSWORD = self.elements.PASSWORD_DISABLE_REASON_BOX
            DISABLE_REASON_BOX = self.elements.DISABLE_REASON_BOX
            PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
            PASSWORD_ACCEPT_BUTTON = self.elements.INTERNET_SECURITY_PASSWORD_ACCEPT_BUTTON
            CONFIRM_ACCEPT_BUTTON = self.elements.CONFIRM_ACCEPT_BUTTON
            FAILED_CONFIRM_BUTTON = self.elements.INTERNET_SECURITY_PASSWORD_FAILED_OK_BUTTON
            CANCEL_BUTTON = self.elements.INTERNET_SECURITY_PASSWORD_CANCEL_BUTTON
        elif service=="ZDP":
            TAB = self.elements.DATA_PREVENTION_TAB
            TOGGLE_ON = self.elements.DATA_PREVENTION_POWER_BUTTON_ON
            TOGGLE_OFF = self.elements.DATA_PREVENTION_POWER_BUTTON_OFF
            PASSOWRD_BOX = self.elements.DATA_PREVENTION_PASSWORD_BOX
            DISABLE_REASON_BOX_WITH_PASSWORD = self.elements.PASSWORD_DISABLE_REASON_BOX
            DISABLE_REASON_BOX = self.elements.DISABLE_REASON_BOX
            PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
            PASSWORD_ACCEPT_BUTTON = self.elements.DATA_PREVENTION_PASSWORD_ACCEPT_BUTTON
            CONFIRM_ACCEPT_BUTTON = self.elements.CONFIRM_ACCEPT_BUTTON
            FAILED_CONFIRM_BUTTON = self.elements.DATA_PREVENTION_PASSWORD_FAILED_OK_BUTTON
            CANCEL_BUTTON = self.elements.DATA_PREVENTION_PASSWORD_CANCEL_BUTTON
        elif service == "ZPA":
            TAB = self.elements.PRIVATE_ACCESS_TAB
            TOGGLE_ON = self.elements.PRIVATE_ACCESS_POWER_BUTTON_ON
            TOGGLE_OFF = self.elements.PRIVATE_ACCESS_POWER_BUTTON_OFF
            PASSOWRD_BOX = self.elements.PRIVATE_ACCESS_PASSWORD_BOX
            DISABLE_REASON_BOX_WITH_PASSWORD = self.elements.PASSWORD_DISABLE_REASON_BOX
            DISABLE_REASON_BOX = self.elements.DISABLE_REASON_BOX
            PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
            PASSWORD_ACCEPT_BUTTON = self.elements.PRIVATE_ACCESS_PASSWORD_ACCEPT_BUTTON
            CONFIRM_ACCEPT_BUTTON = self.elements.CONFIRM_ACCEPT_BUTTON
            FAILED_CONFIRM_BUTTON = self.elements.PRIVATE_ACCESS_PASSWORD_FAILED_OK_BUTTON
            CANCEL_BUTTON = self.elements.PRIVATE_ACCESS_PASSWORD_CANCEL_BUTTON
        elif service == "ZDX":
            TAB = self.elements.DIGITAL_EXPERIENCE_TAB
            TOGGLE_ON = self.elements.DIGITAL_POWER_BUTTON_ON
            TOGGLE_OFF = self.elements.DIGITAL_POWER_BUTTON_OFF
            PASSOWRD_BOX = self.elements.DIGITAL_EXPERIENCE_PASSWORD_BOX
            DISABLE_REASON_BOX_WITH_PASSWORD = self.elements.PASSWORD_DISABLE_REASON_BOX
            DISABLE_REASON_BOX = self.elements.DISABLE_REASON_BOX
            PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
            PASSWORD_ACCEPT_BUTTON = self.elements.DIGITAL_EXPERIENCE_PASSWORD_ACCEPT_BUTTON
            CONFIRM_ACCEPT_BUTTON = self.elements.CONFIRM_ACCEPT_BUTTON
            FAILED_CONFIRM_BUTTON = self.elements.DIGITAL_EXPERIENCE_PASSWORD_CANCEL_BUTTON
            CANCEL_BUTTON = self.elements.DIGITAL_EXPERIENCE_PASSWORD_CANCEL_BUTTON
        
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.click(TAB, sleep_time=1*self.sleep_factor,AEFC=f"Unable to open {service} tab")
            if action:
                self.UI.click(TOGGLE_ON ,sleep_time=1*self.sleep_factor, AEFC=f"Unable to turn on {service}")
            else:
                self.UI.click(TOGGLE_OFF, sleep_time=1*self.sleep_factor, AEFC=f"Unable to turn off {service}")
        
            if password and disable_reason:
                self.UI.type(DISABLE_REASON_BOX_WITH_PASSWORD, text=str(disable_reason),sleep_time=1 * self.sleep_factor, AEFC="Unable to enter disable reason")
                self.UI.type(PASSOWRD_BOX, text=password,sleep_time=1 * self.sleep_factor, AEFC="Unable to enter password")
                # password ok button
                if cancel_toggle:
                    self.logger.info("Aborting Toggle")
                    self.UI.click(CANCEL_BUTTON,sleep_time=1*self.sleep_factor,AEFC=f"Unable to cancel {service} disable")
                else:
                    self.UI.click(PASSWORD_DISABLE_OK_BUTTON,sleep_time=1*self.sleep_factor,AEFC=f"Unable to confirm {service} disable")
            else:
                if disable_reason:
                    self.UI.type(DISABLE_REASON_BOX, text=str(disable_reason),sleep_time=1*self.sleep_factor, AEFC="Unable to enter disable reason")
                    if cancel_toggle:
                        self.logger.info("Aborting Toggle")
                        self.UI.click(self.elements.DISABLE_REASON_CANCEL_BUTTON,sleep_time=1*self.sleep_factor,AEFC=f"Unable to cancel {service} disable")
                if password:
                    self.UI.type(PASSOWRD_BOX, text=password,sleep_time=1 * self.sleep_factor, AEFC="Unable to enter password")
                    # password ok button
                    if cancel_toggle:
                        self.logger.info("Aborting Toggle")
                        self.UI.click(CANCEL_BUTTON,sleep_time=1*self.sleep_factor,AEFC=f"Unable to cancel {service} disable")
                    else:
                        self.UI.click(PASSWORD_ACCEPT_BUTTON,sleep_time=1*self.sleep_factor,AEFC=f"Unable to confirm {service} disable")
                else:
                    if cancel_toggle:
                        self.logger.info("Aborting Toggle")
                        self.UI.click(self.elements.DISABLE_REASON_CANCEL_BUTTON,sleep_time=1*self.sleep_factor,AEFC=f"Unable to cancel {service} disable")
                    else:
                        self.UI.click(CONFIRM_ACCEPT_BUTTON, sleep_time=1*self.sleep_factor,AEFC=f"Unable to confirm {service} disable")
            if failure_expected:
                if self.operating_system != "mac":
                    for i in range(4):
                        try:self.UI.click(FAILED_CONFIRM_BUTTON,sleep_time=2, AEFC=f"Unable to confirm {service} disable")
                        except:pass
                if self.operating_system == "mac": self.UI.click(CANCEL_BUTTON, sleep_time=2,AEFC=f"Unable to confirm {service} disable")
            assert not self.UI.check_if_exists(CANCEL_BUTTON,boolean=True,AESC=f"{service} toggle not accpeted properly")
        except Exception as e:
            self.logger.error(f"Unable to Toggle Service: {service} {action} :: {e}")
            raise Exception(f"Unable to Toggle Service: {service} {action} :: {e}")
        self.Bring_Zcc_To_Focus()

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @calculateFuncExecuteTime
    @allure.step("Exit ZCC")
    def Exit_Zcc(self,
                relaunch_zcc: bool = False      # Defines whether to relaunch zcc after doing exit
                ):
        try:
            if self.operating_system=="mac":
                self.UI.click(self.elements.ZCC_TRAY_ICON, sleep_time=1*self.sleep_factor, AEFC="Unable to click taskbar")
                self.UI.click(self.elements.ZCC_EXIT_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click exit")
                self.UI.click(self.elements.ZCC_EXIT_CONFIRM_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click exit ok")
            else:
                self.Bring_Zcc_To_Focus()
                self.UI.click(self.elements.notification_chevron, sleep_time=1*self.sleep_factor, AEFC="Unable to click notification chevron")
                try:
                    self.UI.click(self.elements.ZCC_LOGGED_OUT_TRAY_ICON, sleep_time=1*self.sleep_factor,max_threshold=1,retry_frequency=1, AEFC="Unable to click on ZCC (logged in) in taskbar")
                except Exception as E:
                    self.logger.error(E)
                    raise Exception(f"Unable to click on zcc from notification chevron :: Error :: {E}")
                self.UI.click(self.elements.ZCC_EXIT_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click exit button on ZCC")
                self.UI.click(self.elements.CONFIRM_ACCEPT_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click conti button on ZCC")
        except Exception as E:
            self.logger.error(E)
            raise Exception(E)
        else:
            if relaunch_zcc:
                self.relaunch_zcc()
            else:
                self.logger.info("Relaunch validated")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Check error notification")
    def Check_Error_Notification(self, notification_text=None):
        if notification_text == None:
            raise Exception("Provide the notification text to be verified")
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.check_if_exists(self.elements.ERROR_DIALOG)
            element = self.UI.click(self.elements.ERROR_VALUE,do_click=False,returnText=True,sleep_time=0)
            assert not self.UI.click(self.elements.LOGIN_ERROR_OK_BUTTON, sleep_time=2,
                                      AEFC="Unable to click on error notification Ok button")
            if element== notification_text:
                self.logger.info(f"Expected error notification exists with message \"{notification_text}\"")
            else:
                self.logger.error(f"Expected error notification with message \"{notification_text}\" does not exist")
                assert not self.UI.click(self.elements.LOGIN_ERROR_OK_BUTTON, sleep_time=2,
                                          AEFC="Unable to click on error notification Ok button")
                raise Exception(
                    f"Error :: Expected error notification with message \"{notification_text}\" does not exist")
        except Exception as e:
            self.logger.error(f"Failed to verify ZCC notification :: {e}\n".format(e))
            raise Exception(f"Error :: Failed to verify ZCC notification :: {e}\n".format(e))

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Clear All Notification")
    def Clear_All_Notification(self):
        self.Bring_Zcc_To_Focus()
        self.UI.click(self.elements.NOTIFICATIONS_TAB, sleep_time=2, AEFC="Unable to open Notifications tab")
        self.UI.click(self.elements.CLEAR_ALL_NOTIFICATIONS, sleep_time=2,
                       AEFC="Unable to click on Clear All button in Notifications tab")
        self.UI.click(self.elements.CLEAR_NOTIFICATIONS_CONTINUE, sleep_time=2,
                       AEFC="Unable to click on Continue button to clear all notifications")
        self.logger.info('Cleared All ZCC Notifications')

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Find Notification")
    def Find_Notification(self, notification):
        self.Bring_Zcc_To_Focus()
        self.UI.click(self.elements.NOTIFICATIONS_TAB, sleep_time=2, AEFC="Unable to open Notifications tab")
        self.UI.click('name:\"DataGridCell\"',do_click=False,sleep_time=1)
        for i in self.UI.element:
            if i.get_attribute('Name') == notification:
                self.logger.info(f'Required notification present in ZCC : \'{notification}\'')
                return True
        raise Exception(f'Required notification not present in ZCC : \'{notification}\'')

    # -----------------ZCC MORE TAB Functions------------------------------------------------------------------------------------------
    @calculateFuncExecuteTime
    @allure.step("Update Policy")
    def Update_Policy(self):

        if self.zccBuild=='':
            self.zccBuild = self.logOps.get_current_zcc_version()
            self.logger.info(f"ZCC BUILD :: {self.zccBuild}\n\n")
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.APP_MORE, sleep_time=1*self.sleep_factor, AEFC="Unable to click More button on ZCC")
            startTimeForLogSearch = datetime.now()
            self.UI.click(self.elements.APP_UPDATE_POLICY, sleep_time=0, AEFC="Unable to click Update Policy button")
        except Exception as e: 
            self.logger.error(f"Update Policy Failed :: {e}")
            raise Exception(f"Error :: Update Policy Failed :: {e}")
        
        isUpdatePolicyValidated = False
        windowsLogs = "ZSATrayManager Event:  SEND_TRAY_POLICY" if float(self.zccBuild[:3])<=3.8 else 'Done with zsaTrayManagerEventListener'
        logsToBeSearched = windowsLogs if self.operating_system=="windows" else 'updateConfig:success'
        try:
            self.logOps.search_log_file(file="ZSATray" if self.operating_system!="windows" else "ZSATrayManager",words_to_find_in_line=[logsToBeSearched],search_mode=None,start_timestamp=startTimeForLogSearch)
            isUpdatePolicyValidated=True
        except Exception as E:
            isUpdatePolicyValidated=False
        
        if isUpdatePolicyValidated:
            windowsError = 'Keep Alive failed:' if float(self.zccBuild[:3])<=4.0 else 'ERR sendKeepAlive'
            self.Bring_Zcc_To_Focus()
            try:
                errorLog = windowsError if self.operating_system=="windows" else 'Keepalive request failed'
                self.logOps.search_log_file(file="ZSATray" if self.operating_system!="windows" else "ZSATrayManager",words_to_find_in_line=[errorLog],wait_time=0)
            except Exception as E:
                self.logger.info("Keep Alive Error log not found, all good")
            else:
                if self.operating_system=="windows":
                    self.windows.click("name:\"Failed to update policy.\"")
                    self.windows.click("name:OkButton")
                self.logger.error(f"Keep Alive Error log {errorLog} found, update policy failed")
                raise Exception(f"Keep Alive Error log {errorLog} found, update policy failed")
            self.logger.info("Success :: Updated Policy!")
        else:
            self.logger.error(f"Update policy not validated")
            raise Exception(f"Update policy not validated")
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------        

    @calculateFuncExecuteTime
    @allure.step("Restart Service")
    def Restart_Service(self,
                        sleep_time: int =60,                    # Number of seconds to sleep after execution
                        validate_zia_connected: bool =False     # Defines whether to check zia connection after restart service
                        ):
        if self.zccBuild=='':
            self.zccBuild = self.logOps.get_current_zcc_version()
            self.logger.info(f"ZCC BUILD :: {self.zccBuild}\n\n")
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.APP_MORE, sleep_time=0, AEFC="Unable to click More button on ZCC")
            self.UI.click(self.elements.RESTART_SERVICE, sleep_time=0,AEFC="Unable to click Restart Service button on ZCC")
            self.UI.click(self.elements.RESTART_SERVICE_ACCEPT, sleep_time=0,AEFC="Unable to confirm ZCC Restart")
        except Exception as e:
            self.logger.error(f"Restart Service Failed :: {e}")
            raise Exception(f"Error :: Restart Service Failed :: {e}")
        
        logDirectory = Constants.Utils.log_path
        version = self.zccBuild.split(".")
        if int(version[0]) >= 4 and int(version[1]) >= 4:
            Hashed_SID = self.logOps.Get_Hashed_User_SID()
            if os.path.exists(os.path.join(Constants.Utils.log_path, "log-{}".format(Hashed_SID))):
                    logDirectory = os.path.join(Constants.Utils.log_path, "log-{}".format(Hashed_SID))
        
        beforeFiles = len([files for files in sorted(os.listdir(logDirectory),reverse=True) if "ZSATunnel" in files])
        currentFiles = beforeFiles

        startTime = int(time.time())
        while(currentFiles==beforeFiles and int(time.time())-startTime<=sleep_time):
            currentFiles = len([files for files in sorted(os.listdir(logDirectory),reverse=True) if "ZSATunnel" in files])
            print(f'tunnelFiles:{currentFiles}, Time:{int(time.time())-startTime} s',end='\r')
        if currentFiles==beforeFiles:
            error = 'Please check manually'
            try:
                error = self.UI.click("ErrorValue",do_click=False,returnText=True,AESC="Tunnel restart rate limited pop up encountered")
                self.logger.error(error)
                self.UI.click('OkButton', sleep_time=0*self.sleep_factor, AEFC="Unable to click Failed Restart service OK Button on ZCC")
            except:
                self.logger.info("NO error box found, might be some different issue")
            finally:
                raise Exception(f"Tunnel files before restart service :: {beforeFiles}, Tunnel files after restart service :: {currentFiles}\nError Reason ::{error}")
        self.logger.info(f"Tunnel files before restart service :: {beforeFiles}")
        self.logger.info(f"Tunnel files after restart service :: {currentFiles}")

        
        if validate_zia_connected:
            isZIAValidated=False
            retryCount=0
            while(isZIAValidated==False and retryCount<3):
                try:
                    self.Validate_Zia_Server_State('connected')
                    self.logger.info("ZIA Conected validated")
                    isZIAValidated=True
                except Exception as E:
                    self.logger.error(f"Error while updating zia state :: {E}")
                    retryCount+=1
                    isZIAValidated=False
            if not isZIAValidated:raise Exception("ZIA not in connected state after restart service")
        self.logger.info("Success :: Restarted Service!")

    @calculateFuncExecuteTime
    @allure.step("Export Logs")
    def Export_Logs(self, 
                    useZccTray: bool = True):     # Defines whether to do export logs from zcc tray or from taskbar
        try:
            startTimeForLogSearch = datetime.now()
            if useZccTray:
                self.Bring_Zcc_To_Focus()
                self.UI.click(self.elements.APP_MORE, sleep_time=0, AEFC="Unable to click More button on ZCC")
                self.UI.click(self.elements.EXPORT_LOGS, sleep_time=1, AEFC="Unable to click on export logs")
                self.logger.info("Now hitting enter button")
                time.sleep(2)
                pyautogui.press('enter')  # press signin button
            else:
                if self.operating_system=="mac":
                    self.UI.click(self.elements.ZCC_TRAY_ICON, sleep_time=1*self.sleep_factor, AEFC="Unable to click taskbar")
                    self.UI.click(self.elements.EXPORT_LOGS_TRAYBAR_MENU, sleep_time=3*self.sleep_factor, AEFC="Unable to click Export Logs")
                    pyautogui.press('enter')  # press signin button
                else:
                    self.Bring_Zcc_To_Focus()
                    self.UI.click(self.elements.notification_chevron, sleep_time=1*self.sleep_factor, AEFC="Unable to click notification chevron")
                    try:
                        self.UI.click(name = self.elements.ZCC_LOGGED_IN_TRAY_ICON, sleep_time=1*self.sleep_factor,max_threshold=1,retry_frequency=1, AEFC="Unable to click on ZCC (logged in) in taskbar")
                    except Exception as E:
                        try:
                            self.UI.click(name = self.elements.ZCC_LOGGED_OUT_TRAY_ICON, sleep_time=1*self.sleep_factor,max_threshold=2,retry_frequency=1, AEFC="Unable to click on ZCC (logged out) in taskbar")
                        except Exception as E:
                            self.logger.error(E)
                            raise Exception(f"Unable to click on zcc from notification chevron :: Error :: {E}")
                    self.UI.click(name = self.elements.EXPORT_LOGS_TRAYBAR_MENU, sleep_time=2*self.sleep_factor, AEFC="Unable to click exit button on ZCC")
                    pyautogui.press('enter')  # press signin button
        except Exception as e: 
            self.logger.error(f"Export Logs Failed :: {e}")
            raise Exception(f"Error :: Export Logs Failed :: {e}")

        isExportLogsValidated = False
        logsToBeSearched = 'Zip Archive have been created' if self.operating_system=="windows" else 'removeServiceAndTunnelLogs:successfully removed logs'
        try:
            self.logOps.search_log_file(file="ZSATray" if self.operating_system!="windows" else "ZSATrayManager",words_to_find_in_line=[logsToBeSearched],start_timestamp=startTimeForLogSearch,wait_time=120,search_mode=None)
            isExportLogsValidated=True
        except Exception as E:
            isExportLogsValidated=False
        if isExportLogsValidated:self.logger.info("Success :: Logs Exported !")
        else:
            self.logger.error("Export Logs not validated")
            raise Exception("Export Logs not validated")
        
    # ---------------------------------------------------------------------------------------------
    @allure.step("Enable/Disable AntiTampering from ZCC")
    def Toggle_Anti_Tampering(self,
                action: bool =True,                 # if True is set, expect "enable_anti_tampering", click on it, else expect "disable_anti_tampering", click on it
                password: str =None,                # password to disable anti tempering
                failure_expected: bool =False,      # expect anti tempering action to be failed
                sleep_time: int =5
        ):
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.APP_MORE, sleep_time=1*self.sleep_factor, AEFC="Unable to click More button on ZCC")
            if action: 
                self.UI.click(self.elements.ENABLE_ANTI_TAMPERING, sleep_time=1*self.sleep_factor, AEFC="Unable to click Enable Anti Tampering button")
                self.UI.click(self.elements.ENABLE_ANTI_TAMPERING_SUCCESS_OK_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click Enable Anti Tampering success ok button")
            else: 
                self.UI.click(self.elements.DISABLE_ANTI_TAMPERING, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable Anti Tampering button")
                if password: 
                    self.UI.type(self.elements.DISABLE_ANTI_TAMPERING_PASSWORD_BOX, text=password, sleep_time=1*self.sleep_factor, AEFC="Unable to enter password")
                self.UI.click(self.elements.DISABLE_ANTI_TAMPERING_PASSWORD_BOX_OK_BUTTON, sleep_time=sleep_time, AEFC="Unable to confirm Anti Tampering disable")
                if failure_expected: 
                    if self.operating_system!="mac": 
                        for i in range(4):
                            try:
                                self.UI.click(self.elements.DISABLE_ANTI_TAMPERING_PASSWORD_FAILED_OK_BUTTON, sleep_time=1, AEFC="Unable to confirm Anti Tampering disable")
                            except:
                                print("jus chill")
        except Exception as e: 
            self.logger.error(f"Anti Tampering Toggle Failed :: {e}")
            raise Exception(f"ERROR :: Anti Tampering Toggle Failed :: {e}")
        self.logger.info("Success :: Anti Tampering Toggle success!")

    # ---------------------------------------------------------------------------------------------
    @allure.step("Verify log mode from zcc ui")
    def Verify_Log_Mode_Drop_Down_State(self,
            is_editable: bool =True ):  
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.APP_MORE, sleep_time=5, AEFC="Unable to select More button on ZCC")
            try:
                self.UI.click(self.elements.LOG_MODE_DROP_DOWN, sleep_time=5,AEFC="Unable to click log mode drop down on ZCC")
                if not is_editable: raise Exception("not supposed to be editable, but is!")
            except:
                if is_editable: raise Exception("supposed to be editable, but isnt!")
                else: self.logger.info("cant click because shouldnt be able to click")
        except Exception as e:
            self.logger.error(f"verify_log_mode_drop_down_state Failed :: {e}")
            raise Exception("Error :: verify_log_mode_drop_down_state Failed :: {e}")
        self.logger.info("Success :: verify_log_mode_drop_down_state done!")

    # ---------------------------------------------------------------------------------------------
    @calculateFuncExecuteTime
    @allure.step("Report An Issue")
    def Report_an_Issue(self,
                        sleep_time: int =120):  # number of seconds to sleep after execution

        if self.zccBuild=='':
            self.zccBuild = self.logOps.get_current_zcc_version()
            self.logger.info(f"ZCC BUILD :: {self.zccBuild}\n\n")
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.APP_MORE, sleep_time=1*self.sleep_factor, AEFC="Unable to click More button on ZCC")
            self.UI.click(self.elements.REPORT_AN_ISSUE_BUTTON, sleep_time=1 * self.sleep_factor,AEFC="Unable to select Report an Issue button on ZCC")
            self.UI.type(self.elements.REPORT_AN_ISSUE_NAME_BOX, text="Automation User",sleep_time=1 * self.sleep_factor, AEFC="Unable to enter name in Report an Issue")
            startTimeForLogSearch = datetime.now()
            self.UI.click(self.elements.REPORT_AN_ISSUE_SEND_BUTTON, sleep_time=0,AEFC="Unable to send report from Report an Issue on ZCC")
        except Exception as e:
            self.logger.error(f"Report an Issue Failed :: {e}")
            raise Exception(f"Error :: Report an Issue Failed :: {e}")
        
        isReportIssueValidated = False
        windowsLogs = "reportIssueV2" if float(self.zccBuild[:3])<=3.8 else 'reportIssueV3: '
        logsToBeSearched = windowsLogs if self.operating_system=="windows" else "executeMobileSupportPostAPI: "
        try:
            self.logOps.search_log_file(file="ZSATray" if self.operating_system!="windows" else "ZSATrayManager",words_to_find_in_line=[logsToBeSearched],start_timestamp=startTimeForLogSearch,search_mode=None,wait_time=sleep_time)
            isReportIssueValidated=True
        except Exception as E:
            isReportIssueValidated=False

        if isReportIssueValidated:
            windowsLogs = "reportIssueV2: Exception:" if float(self.zccBuild[:3])<=3.8 else 'reportIssueV3: Exception:'
            errorLog = windowsLogs  if self.operating_system=="windows" else "executeMobileSupportPostAPI: Exception:"
            try:
                self.logOps.search_log_file(file="ZSATray" if self.operating_system!="windows" else "ZSATrayManager_",words_to_find_in_line=[errorLog],start_timestamp=startTimeForLogSearch,search_mode=None)
            except Exception as E:
                error = f"reportIssue response :: {self.logOps.recent_log_line}"  if self.operating_system=="windows" else f"executeMobileSupportPostAPI: Exception :: {self.logOps.recent_log_line}"
                self.logger.info(f"Report an issue :: {errorLog} log not found, all good")
                self.logger.info(f"Report an Issue log line found :: {self.logOps.recent_log_line}")
            else:
                error = f"reportIssue response :: {self.logOps.recent_log_line}"  if self.operating_system=="windows" else f"executeMobileSupportPostAPI: Exception :: {self.logOps.recent_log_line}"
                self.logger.error(f"Report an issue Error :: {error} log found, report an issue failed")
                if self.operating_system=="windows":
                    self.UI.click(name="Failed to Report An Issue", sleep_time=1*self.sleep_factor, AEFC="Unable to click Failed Report Issue Banner on ZCC")
                    self.UI.click('OkButton', sleep_time=0*self.sleep_factor, AEFC="Unable to click Failed Report Issue OK Button on ZCC")
                raise Exception(f"reportIssue Error log {errorLog} found, report an issue failed")
            self.logger.info("Success :: Report An Issue!")
        else:
            raise Exception(f"Report Issue not validated")
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Revert ZCC")
    def Revert_ZCC(self,  # Function to Revert ZCC on ZCC win/mac
                   password: str =None,             # password to revert zcc
                   failure_expected: bool =False,   # to verify what happens when wrong password is given
                   sleep_time: int =30):            # number of seconds to sleep after execution
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.APP_MORE, sleep_time=1*self.sleep_factor,AEFC="Unable to click More button on ZCC")
            self.UI.click(self.elements.REVERT_ZCC, sleep_time=sleep_time, AEFC="Unable to click Revert ZCC button")
            if password:
                self.UI.type(self.elements.REVERT_ZCC_PASSWORD_BOX, text=password, sleep_time=1 * self.sleep_factor,AEFC="Unable to enter password")
                self.UI.click(self.elements.REVERT_ZCC_PASSWORD_ACCEPT_BUTTON, sleep_time=1 * self.sleep_factor,AEFC="Unable to click Revert ZCC Password OK button")
                if failure_expected:
                    if self.operating_system != "mac":
                        for i in range(4):
                            try:
                                self.UI.click(self.elements.REVERT_ZCC_PASSWORD_FAILED_OK_BUTTON, sleep_time=20, AEFC="Unable to confirm revert zcc wrong password entry")
                            except:
                                pass
                    return
            else:
                self.UI.click(self.elements.REVERT_ZCC_PASSWORD_ACCEPT_BUTTON, sleep_time=1 * self.sleep_factor,AEFC="Unable to click Revert ZCC Password OK button")
        except Exception as e:
            self.logger.error(f"Revert ZCC Failed :: {e}")
            raise Exception(f"Error :: Revert ZCC Failed :: {e}")
        time.sleep(120)
        try:
            self.logOps.search_log_file(file = "ZSATrayManager",search_mode=None,words_to_find_in_line= ["ZSATrayManager Version:"],start_line=None,wait_time=180)
            old_zcc_version = self.logOps.recent_log_line.split("ZSATrayManager Version:")[1].strip()
            if old_zcc_version in Constants.Utils.old_zcc_exe or old_zcc_version in Constants.Utils.old_zcc_msi or old_zcc_version in Constants.Utils.old_zcc_app:
                self.logger.info(f"Success:: Reverted ZCC to {old_zcc_version}")
                return
        except:
            self.logger.info("log not found yet, sleeping anf tryin again")
            time.sleep(30)
        raise Exception("revert zcc failed!!!")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Toggle packet capture")
    def Toggle_Packet_Capture(self,
                              mode: str = "start"    # accepts "start" or "stop" as value
                              ):
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.ZSA_TRAY_FORM, max_threshold=20, sleep_time=1,AEFC="Unable to click on ZCC Tray Form")
            self.UI.click(self.elements.APP_MORE, sleep_time=1, AEFC="Unable to click More button on ZCC")
            if mode == 'start':
                self.UI.click(self.elements.START_PACKET_CAPTURE, sleep_time=1,AEFC="Unable to click start packet capture  button on ZCC")
            elif mode == 'stop':
                self.UI.click(self.elements.STOP_PACKET_CAPTURE, sleep_time=1,AEFC="Unable to click stop packet capture button on ZCC")
            else:
                raise Exception(f"Error :: Invalid Mode Given {mode}, please give start or stop")
        except Exception as e:
            self.logger.error(f"{mode} Packet Failed :: {e}")
            raise Exception(f"Error :: {mode} Packet Failed :: {e}")
        self.logger.info(f"Success :: {mode} Packet Capture Click done!")

    # ----------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Validate Packet Capture State")
    def Validate_Packet_Capture_State(self,
                                      mode='start'
                                      ):
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.ZSA_TRAY_FORM, max_threshold=20, sleep_time=1,AEFC="Unable to click on ZCC Tray Form")
            self.UI.click(self.elements.APP_MORE, sleep_time=1, AEFC="Unable to click More button on ZCC")
            if mode == 'start': self.UI.check_if_exists(self.elements.START_PACKET_CAPTURE)
            elif mode == 'stop': self.UI.check_if_exists(self.elements.STOP_PACKET_CAPTURE)
            else: raise Exception(f"Error :: Invalid Mode Given {mode}, please give start or stop")
        except Exception as e:
            self.logger.error(f"{mode} Packet Failed :: {e}")
            raise Exception(f"Error :: {mode} Packet Failed :: {e}")
        self.logger.info(f"SUCCESS :: {mode} Packet Capture Found")
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Clear Logs")
    def Clear_Logs(self):
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.APP_MORE, sleep_time=0, AEFC="Unable to click More button on ZCC")
            self.UI.click(self.elements.CLEAR_LOGS,sleep_time=0, AEFC="Unable to click Clear Logs button on ZCC")
        except Exception as e: 
            self.logger.error(f"CLEAR LOGS FAILED {e}".format(e))
            raise Exception(f"Error :: CLEAR LOGS FAILED :: {e}")
    
    # ---------------------------MORE TAB FINISHED ----------------------------------------------------------------------------------------------
    def Click_Zcc_Back(self):
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.ZCC_BACK_BUTTON, sleep_time=0, AEFC="Unable to click More button on ZCC")
        except Exception as e:
            self.logger.error(f"Click Back Button Failed :: {e}")
            raise Exception(f"Error :: Click Back Button Failed :: {e}")
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("FInd private access tab")
    def Find_Private_Access_Tab(self,
                            shouldBePresent: bool):    # accepts True if tab is expected else False
        self.Bring_Zcc_To_Focus()    
        self.UI.click(self.elements.PRIVATE_ACCESS_TAB, sleep_time=0,retry_frequency=1, max_threshold=2,AEFC="Unable to open Private Access tab",expect_click_failure=not shouldBePresent)

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Find Internet Access Tab")
    def Find_Internet_Access_Tab(self, 
                            shouldBePresent: bool):    # accepts True if tab is expected else False
        self.Bring_Zcc_To_Focus()
        self.UI.click(self.elements.INTERNET_SECURITY_TAB, sleep_time=0,retry_frequency=1,max_threshold=2,AEFC="Unable to open Internet Access tab",expect_click_failure=not shouldBePresent)

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Find Digital Experience Tab")
    def Find_Digital_Experience_Tab(self,
                                shouldBePresent: bool):    # accepts True if tab is expected else False
        self.Bring_Zcc_To_Focus()
        self.UI.click(self.elements.DIGITAL_EXPERIENCE_TAB, sleep_time=2,retry_frequency=1,max_threshold=2,AEFC="Unable to open Digital Experience tab",expect_click_failure=not shouldBePresent)
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Check Service Connected")
    def Check_Service_Connected(self, 
                            service: str ):   # defines service - ZIA/ZPA
        
        self.Bring_Zcc_To_Focus()
        service = service.upper()
        if service == "ZPA":
            self.UI.click(self.elements.PRIVATE_ACCESS_TAB, sleep_time=2, AEFC="Unable to open Private Access tab")
            try:
                self.UI.check_if_exists(self.elements.PRIVATE_ACCESS_POWER_BUTTON_ON)
            except:
                time.sleep(30)
                self.UI.check_if_exists(self.elements.PRIVATE_ACCESS_POWER_BUTTON_ON)
        if service == "ZIA":
            self.UI.click(self.elements.INTERNET_SECURITY_TAB, sleep_time=2, AEFC="Unable to open Internet Security tab")
            try:
                self.UI.check_if_exists(self.elements.INTERNET_SECURITY_POWER_BUTTON_ON)
            except:
                time.sleep(30)
                self.UI.check_if_exists(self.elements.INTERNET_SECURITY_POWER_BUTTON_ON)

    
    # --------------------------ZPA UI Actions------------------------------------------------------------------------------------------------------------
    @allure.step("validate zpa reauth state")
    def Validate_ZPA_Reauth_State(self):    #this function checks if zcc is in reauth state or not
        
        self.Bring_Zcc_To_Focus()
        self.UI.click(self.elements.PRIVATE_ACCESS_TAB, sleep_time=1, AEFC="Unable to open Private Access tab")
        try:
            if self.operating_system=="windows":
                zpaState = self.UI.click(self.elements.PRIVATE_ACCESS_AUTH_STATE,max_threshold=3,sleep_time=1, AEFC="Unable to select Private Access button on win ZCC",do_click=False,returnText=True)
                if zpaState=="Authentication Required":
                    self.logger.info("ZPA in reauth required state")
                    return True
                else:raise Exception("ZPA NOT IN AUTH REQUIRED STATE")
            else:
                try:
                    self.UI.click(self.elements.PRIVATE_ACCESS_REAUTH_BUTTON,max_threshold=3,sleep_time=1, AEFC="Unable to select Private Access button on mac ZCC",do_click=False)
                except:
                    try:
                        self.UI.click(self.elements.PRIVATE_ACCESS_REAUTH_BUTTON_2,max_threshold=3,sleep_time=1, AEFC="Unable to select Private Access button on ZCC - tried 2 different element id's",do_click=False)
                    except Exception as E:
                        self.logger.error(E)
                        raise Exception(E)
                self.logger.info("ZPA in reauth required state")
                return True
        except:
            self.logger.error("ZPA Reauth button not found")
            return False

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Do ZPA Reauth ")
    def ZPA_Reauth(self,
                   ZPA_USERNAME: str ,          # ZPA user for reauth
                   ZPA_PASSWORD: str ,          # ZPA user password for reauth
                   sleep_time: int =10,         # number of seconds to sleep after execution
                   partial_reauth: bool =False, # defines whether do to partial reauth
                   early_auth: bool =False):    # defines whether to do early reauth
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.ZSA_TRAY_FORM, max_threshold=20, sleep_time=1,AEFC="Unable to click on ZCC Tray Form")
            self.UI.click(self.elements.PRIVATE_ACCESS_TAB, sleep_time=1, AEFC="Unable to open Private Access tab")
            try:
                startTimeForLogSearch = datetime.now()
                self.UI.click(self.elements.PRIVATE_ACCESS_REAUTH_BUTTON if early_auth==False else self.elements.PRIVATE_ACCESS_EARLY_REAUTH_BUTTON,max_threshold=3,sleep_time=0, AEFC="Unable to select Private Access button on ZCC")
            except Exception as E:
                if self.operating_system!="mac" or (self.operating_system=="mac" and early_auth==True):
                    self.logger.error(E)
                    raise Exception(E)
                else:
                    try:
                        self.UI.click(self.elements.PRIVATE_ACCESS_REAUTH_BUTTON_2,max_threshold=3,sleep_time=1, AEFC="Unable to select Private Access button on ZCC - tried 2 different element id's")
                    except Exception as E:
                        self.logger.error(E)
                        raise Exception(E)
        
            self.Do_Okta_Login(ZPA_USERNAME,ZPA_PASSWORD,search_log_time=startTimeForLogSearch,partial_login=partial_reauth)
        except Exception as e:
            self.logger.error(f"ZPA Reauth Failed :: {e}")
            raise Exception(f"Error :: ZPA Reauth Failed :: {e}")
        self.logger.info("Success :: ZPA Reauth Done!")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Get ZPA traffic data from ZCC")
    def Get_ZPA_Traffic_Data(self):  # Function to fetch sent bytes data from ZCC UI
        
        if self.operating_system == "mac":
            time.sleep(5)
            zccUIData = self.SysOps.get_data()
            sent_bytes = zccUIData['zpnTotalBytesSent']
            received_bytes = zccUIData['zpnTotalBytesReceived']
            return sent_bytes, received_bytes
        else:
            try:
                self.Bring_Zcc_To_Focus()
                self.UI.click(self.elements.ZSA_TRAY_FORM, AEFC="App not Found", max_threshold=20, sleep_time=0)
                self.UI.click(self.elements.PRIVATE_ACCESS_TAB, sleep_time=0, AEFC="Unable to select Private Access Tab")
                sent_bytes = self.UI.click(self.elements.PRIVATE_ACCESS_BYTES_SENT, sleep_time=0,do_click=False ,AEFC="Unable to select Private Access Sent Bytes",returnText=True)
                received_bytes = self.UI.click(self.elements.PRIVATE_ACCESS_BYTES_RECIEVED, sleep_time=0,do_click=False, AEFC="Unable to select Private Access Recieved Bytes",returnText=True)
                return sent_bytes, received_bytes
            except Exception as e:
                self.logger.error(f"Unable to get Traffic Info :: {e}")
                raise Exception(f"Error :: Unable to get Traffic Info :: {e}")
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Validate ZPA Tunnel Protocol")
    def Validate_Zpn_Tunnel_Protocol(self,
                                    requiredTunnelVersion: str          # accepted values :: TLS, DTLS, NONE
                                    ):
        if self.operating_system=="mac":
            zccUIData =  self.SysOps.get_data()
            if requiredTunnelVersion.upper()=="TLS":
                if str(zccUIData["zpn"])=="4":
                    if str(zccUIData["zpnTunProto"])=="TLS":self.logger.info(f"ZPA ON TLS Validated on UI, zpn value -> {str(zccUIData['zpn'])}")
                    else:raise Exception("ZPA not on TLS : UI")
                else:raise Exception("ZPA not on TLS : UI")
            elif requiredTunnelVersion.upper()=="DTLS":
                if str(zccUIData["zpn"])=="4":
                    if str(zccUIData["zpnTunProto"])=="DTLS":self.logger.info(f"ZPA ON DTLS Validated on UI, zpn value -> {str(zccUIData['zpn'])}")
                    else:raise Exception("ZPA not on DTLS : UI")
                else:raise Exception("ZPA not on DTLS : UI")
            elif requiredTunnelVersion.upper()=="NONE":
                if str(zccUIData["zpn"])=="3":
                    self.logger.info(f"ZPA ON NONE Validated on UI, zpn value -> {str(zccUIData['zpn'])}")
                else:raise Exception("ZPA not on NONE : UI")
            else:
                raise Exception(f"Invalid tunel version given -> {requiredTunnelVersion}, please give TLS DTLS or NONE")
        else:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.ZSA_TRAY_FORM, AEFC="App not Found", max_threshold=20, sleep_time=0)
            self.UI.click(self.elements.PRIVATE_ACCESS_TAB, sleep_time=0, AEFC="Unable to select Private Access Tab")
            tunnelProtocol = self.UI.click(self.elements.PRIVATE_ACCESS_TUNNEL_PROTOCOL,sleep_time=0, AEFC="Unable to click on PRIVATE_ACCESS_TUNNEL_PROTOCOL",returnText=True)  
            if requiredTunnelVersion.upper()=="TLS":
                if tunnelProtocol.upper()=="TLS":self.logger.info("ZPA ON TLS Validated on UI")
                else:raise Exception("ZPA not on TLS : UI")
            elif requiredTunnelVersion.upper()=="DTLS":
                if tunnelProtocol.upper()=="DTLS":self.logger.info("ZPA ON DTLS Validated on UI")
                else:raise Exception("ZPA not on DTLS : UI")
            elif requiredTunnelVersion.upper()=="NONE":
                if tunnelProtocol=="...":
                    zpaState=self.UI.click(self.elements.PRIVATE_ACCESS_TUNNEL_STATUS_TEXT, sleep_time=0, AEFC="Unable to open Private Access Tunnel Status Text",returnText=True)
                    if 'disabled' in zpaState.lower():
                        self.logger.info("ZPA Disabled validated")
                    else:
                        raise Exception("Disabled tunnel not validated on UI")
                else:
                    self.logger.error(f'... was required in tunnel version, got {tunnelProtocol}')
                    raise Exception(f'... was required in tunnel version, got {tunnelProtocol}')
            else:
                self.logger.error("Invalid required state given, please give DTLS,TLS or NONE")
                raise Exception("Invalid required state given, please give DTLS,TLS or NONE")
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @calculateFuncExecuteTime
    @allure.step("Add ZPA Partner tenant")
    def Add_Partner_Tenant(self, 
        User_ID: str = None,                            # user id for partner tenant
        Password: str = None,                           # password for partner tenant
        Main_Tenant_Disable_ZPA_Password: str = None,   # Add a string here to send disable password if password for disabling ZPA is set for main tenant
        Main_Tenant_Disable_ZPA_Reason: str = None,     # Add a string here to send disable reason if reason for disabling ZPA is set for main tenant
        ):
        """
        Target: To Add Partner Tenant to ZCC
        Work Flow
        ----------
            * The function first opens a driver using the start_driver method of the GUI class. It then brings the ZCC to focus using the Bring_Zcc_To_Focus method. Next, it clicks the "Private Access Tab" button and then the "Add Partner" button. It then types the User_ID into a box and clicks the "Confirm" button.
            * After a 20-second delay, the function clicks the "Okta Username" button and types the User_ID into the username box. It then types the Password into the password box and clicks the "Sign In" button. If the operating system is macOS, the function presses the "Enter" key to sign in.
            * If the Main_Tenant_Disable_ZPA_Password and Main_Tenant_Disable_ZPA_Reason parameters are provided, the function types the password and reason into their respective boxes and clicks the "Disable ZPA Main Tenant ok" button. Finally, the function brings the ZCC to focus again and kills the driver using the kill_driver method of the GUI class.
        """
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.PRIVATE_ACCESS_TAB, sleep_time=1*self.sleep_factor, AEFC="Unable to click Private Access Tab button on ZCC")
            self.UI.click(self.elements.ADD_PARTNER_TENANT_BUTTON, sleep_time=0, AEFC="Unable to click Add Partner button")
            self.UI.type(self.elements.PARTNER_TENANT_USER_ID_BOX, text=User_ID,sleep_time=1, AEFC="Unable to enter User ID in Partner tenant user box")
            self.UI.click(self.elements.PARTNER_TENANT_USER_ID_CONFIRM, sleep_time=20, AEFC="Unable to click Confirm button")

            # okta page
            self.Handle_Zpa_Login(User_ID,PASSWORD=Password,TIME_FOR_LOG_SEARCH=self.startTimeForLogSearch,CANCEL_ZPA_LOGIN=False)
            # self.UI.click(name=self.elements.OKTA_USERNAME, sleep_time=1, AEFC="Unable to click Okta Username")
            # self.UI.type(name=self.elements.OKTA_USERNAME, text=User_ID, sleep_time=1, AEFC="Unable to find/type Okta Username")
            # self.UI.type(name=self.elements.OKTA_PASSWORD_BOX, text=Password, submit=True, sleep_time=1, AEFC="Unable to find/type Okta Password")
            if self.operating_system=="mac": 
                time.sleep(1)
                self.logger.info("Hitting enter for okta sign in button")
                pyautogui.press('enter')
                self.logger.info("Hitting enter for okta sign in button DONE !!!")

            if Main_Tenant_Disable_ZPA_Password and Main_Tenant_Disable_ZPA_Reason:
                        self.UI.type(self.elements.MAIN_TENANT_POWER_PASSWORD_BOX, text=Main_Tenant_Disable_ZPA_Password, sleep_time=1*self.sleep_factor, AEFC="Unable to enter password")
                        self.UI.type(self.elements.MAIN_TENANT_POWER_PASSWORD_DISABLE_REASON_BOX, text= Main_Tenant_Disable_ZPA_Reason, sleep_time=1*self.sleep_factor, AEFC="Unable to enter disable reason")
                        self.UI.click(self.elements.MAIN_TENANT_POWER_DISABLE_PASSWORD_OK_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable ZPA Main Tenant ok button")
            elif Main_Tenant_Disable_ZPA_Password: 
                        self.UI.type(self.elements.MAIN_TENANT_POWER_PASSWORD_BOX, text=Main_Tenant_Disable_ZPA_Password, sleep_time=1*self.sleep_factor, AEFC="Unable to enter password")
                        self.UI.click(self.elements.MAIN_TENANT_POWER_DISABLE_PASSWORD_OK_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable ZPA Main Tenant ok button")
            elif Main_Tenant_Disable_ZPA_Reason: 
                        self.UI.type(self.elements.MAIN_TENANT_POWER_DISABLE_REASON_BOX, text= Main_Tenant_Disable_ZPA_Reason, sleep_time=1*self.sleep_factor, AEFC="Unable to enter disable reason")
                        self.UI.click(self.elements.MAIN_TENANT_POWER_DISABLE_CONFIRM, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable ZPA Main Tenant ok button")
        except Exception as e: 
            self.logger.error(f"Partner Addition Failed :: {e}")
            raise Exception(f"Error :: Partner Addition Failed :: {e}")

        self.logger.info("Success :: Partner Login success!")
        time.sleep(10)

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @calculateFuncExecuteTime
    @allure.step("Edit ZPA Partner tenant")
    def Edit_ZPA_Partner(self,
        main: bool = True,                                  # set this to True for main tenant related actions
        partner: bool = False,                              # set this to True for partner tenant related actions
        action: bool = None,                                # True for disable->enable, False for enable->disable
        Main_Tenant_Disable_ZPA_Password: str = None,       # send password if it is expected
        Main_Tenant_Disable_ZPA_Reason: str = None,         # send disable reason if it is expected
        remove: bool = None,                                # remove partner tenant if set to True
        failure_expected: bool = False                      # expect GUI failure if set to True, for negative scenario testing
        ):
        """
        Target: To Perform actions related to Main and Partner Tenant via GUI
        Work Flow
        ----------
            * The function first starts a GUI driver and then tries to bring the ZCC (ZCC Client Connector) to focus. It then clicks on the "Private Access" tab and, depending on the value of the action parameter, either enables or disables the ZPA main and partner tenants.
            * If the Main_Tenant_Disable_ZPA_Password and Main_Tenant_Disable_ZPA_Reason parameters are provided, the function enters the password and disable reason into the corresponding input fields and clicks the "Disable ZPA Main Tenant" button.
            * If the remove parameter is set to True, the function removes the partner tenant by clicking on the "Remove Partner Tenant" button and then confirming the removal.
            * Finally, the function kills the GUI driver and logs a success message if everything went well.
        """
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.PRIVATE_ACCESS_TAB, sleep_time=1*self.sleep_factor, AEFC="Unable to click Private Access Tab button on ZCC")
            #self.UI.click(self.elements.MAIN, sleep_time=1*self.sleep_factor, AEFC="Unable to click More button on ZCC")
            if action==True or action==False:
                if action==True: 
                    if main: self.UI.click(self.elements.MAIN_TENANT_POWER_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click Enable ZPA Main Tenant button")
                    if partner: self.UI.click(self.elements.PARTNER_TENANT_POWER_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click Enable ZPA Partner Tenant button")
                if action==False: 
                    if main: self.UI.click(self.elements.MAIN_TENANT_POWER_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable ZPA Main Tenant button")
                    if partner: self.UI.click(self.elements.PARTNER_TENANT_POWER_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click Enable ZPA Partner Tenant button")
                # Check for disable password and disable reason on main tenant
                if Main_Tenant_Disable_ZPA_Password and  Main_Tenant_Disable_ZPA_Reason:
                    self.UI.type(self.elements.MAIN_TENANT_POWER_PASSWORD_BOX, text=Main_Tenant_Disable_ZPA_Password, sleep_time=1*self.sleep_factor, AEFC="Unable to enter password")
                    self.UI.type(self.elements.MAIN_TENANT_POWER_PASSWORD_DISABLE_REASON_BOX, text= Main_Tenant_Disable_ZPA_Reason, sleep_time=1*self.sleep_factor, AEFC="Unable to enter disable reason")
                    self.UI.click(self.elements.MAIN_TENANT_POWER_DISABLE_PASSWORD_OK_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable ZPA Main Tenant ok button")
                elif Main_Tenant_Disable_ZPA_Password: 
                    self.UI.type(self.elements.MAIN_TENANT_POWER_PASSWORD_BOX, text=Main_Tenant_Disable_ZPA_Password, sleep_time=1*self.sleep_factor, AEFC="Unable to enter password")
                    self.UI.click(self.elements.MAIN_TENANT_POWER_DISABLE_PASSWORD_OK_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable ZPA Main Tenant ok button")
                elif  Main_Tenant_Disable_ZPA_Reason: 
                    self.UI.type(self.elements.MAIN_TENANT_POWER_DISABLE_REASON_BOX, text=Main_Tenant_Disable_ZPA_Reason, sleep_time=1*self.sleep_factor, AEFC="Unable to enter disable reason")
                    self.UI.click(self.elements.MAIN_TENANT_POWER_DISABLE_CONFIRM, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable ZPA Main Tenant ok button")
                # ok button
                if main: self.UI.click(self.elements.MAIN_TENANT_POWER_DISABLE_CONFIRM, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable ZPA Main Tenant ok button")
                if partner: self.UI.click(self.elements.PARTNER_TENANT_POWER_DISABLE_CONFIRM, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable ZPA Partner Tenant ok button")
            if remove:
                if partner:
                    self.UI.click(self.elements.REMOVE_PARTNER_TENANT, sleep_time=1*self.sleep_factor, AEFC="Unable to click Remove Partner Tenant button")
                    self.UI.click(self.elements.REMOVE_PARTNER_TENANT_CONFIRM, sleep_time=1*self.sleep_factor, AEFC="Unable to click Remove Partner Tenant Confirm button")
                    for i in range(40):
                        try:
                            self.logOps.search_log_file(file = "ZSATrayManager", search_mode=None, words_to_find_in_line= ["removePartnerLogin for user"], start_line=self.logOps.recent_log_line)
                            return
                        except:
                            time.sleep(2)
                    raise Exception("Error :: Remove partner log not found")
        except Exception as e: 
            self.logger.error(f"Tenant Service Toggle Failed :: {e}")
            raise Exception(f"Error :: Tenant Service Toggle Failed :: {e}")

        self.logger.info("Success :: Tenant Service Toggle success!")
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    
    @allure.step("validate zia tunnel protocol")
    def Validate_Zia_Tunnel_Protocol(self,
                            requiredTunnelVersion: str          # TLS or DTLS or V1 or None
    ):  
        if self.operating_system=="mac": 
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.ZSA_TRAY_FORM, AEFC="App not Found", max_threshold=20, sleep_time=0)
            self.UI.click(self.elements.INTERNET_SECURITY_TAB, sleep_time=0, AEFC="Unable to open Internet Security tab")
            zccUIData =  self.SysOps.get_data()
            if requiredTunnelVersion.upper()=="TLS":
                if str(zccUIData["tunVersion"])=="20:2":
                    if str(zccUIData["websecurity"])=="4":
                        self.logger.info(f"ZIA ON TLS Validated on UI,: tunVersion -> {zccUIData['tunVersion']} webSecuirty -> {zccUIData['websecurity']}")
                    else:raise Exception(f"ZIA not on TLS : tunVersion -> {zccUIData['tunVersion']} webSecuirty -> {zccUIData['websecurity']}")
                else:raise Exception("ZIA not on TLS : UI")

            elif requiredTunnelVersion.upper()=="DTLS":
                if str(zccUIData["tunVersion"])=="20:1":
                    if str(zccUIData["websecurity"])=="4":
                        self.logger.info(f"ZIA ON DTLS Validated on UI,: tunVersion -> {zccUIData['tunVersion']} webSecuirty -> {zccUIData['websecurity']}")
                    else:raise Exception(f"ZIA not on DTLS : tunVersion -> {zccUIData['tunVersion']} webSecuirty -> {zccUIData['websecurity']}")
                else:raise Exception("ZIA not on DTLS : UI")

            elif requiredTunnelVersion.upper()=="V1":
                if str(zccUIData["tunVersion"])=="10:0":
                    if str(zccUIData["websecurity"])=="4" or str(zccUIData["websecurity"])=="5" or str(zccUIData["websecurity"])=="6":
                        self.logger.info(f"ZIA ON T1 Validated on UI: tunVersion -> {zccUIData['tunVersion']} webSecuirty -> {zccUIData['websecurity']}")
                    else:raise Exception(f"ZIA not on T1 : tunVersion -> {zccUIData['tunVersion']} webSecuirty -> {zccUIData['websecurity']}")
                else:raise Exception("ZIA not on T1 : UI")

            elif requiredTunnelVersion.upper()=="NONE":
                if str(zccUIData["tunVersion"])=="":
                    if str(zccUIData["websecurity"])=="3":
                        self.logger.info(f"ZIA ON NONE MODE Validated on UI, websecurity value -> {str(zccUIData['websecurity'])}")
                    else:raise Exception(f"ZIA not on None mode : tunVersion -> {zccUIData['tunVersion']} webSecuirty -> {zccUIData['websecurity']}")
                else:raise Exception("ZIA not on None mode : UI")
            else:
                self.logger.error("Invalid required state given, please give DTLS,TLS ,v1 or none")
                raise Exception("Invalid required state given, please give DTLS,TLS ,v1 or none")

        else:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.ZSA_TRAY_FORM, AEFC="App not Found", max_threshold=20, sleep_time=1)
            self.UI.click(self.elements.INTERNET_SECURITY_TAB, sleep_time=0, AEFC="Unable to select Internet Security Tab")
            tunnelProtocol = self.UI.click(self.elements.INTERNET_SECURITY_TUNNEL_PROTOCOL,sleep_time=0, AEFC="Unable to click on Internet_Security_PROTOCOL",returnText=True)  
            if requiredTunnelVersion.upper()=="DTLS":
                if tunnelProtocol=="v2.0 - DTLS":self.logger.info("ZIA ON v2.0 - DTLS Validated on UI")
                else:raise Exception("ZIA not on v2.0 - DTLS  : UI")
            elif requiredTunnelVersion.upper()=="TLS":
                if tunnelProtocol=="v2.0 - TLS":self.logger.info("ZIA ON v2.0 - TLS Validated on UI")
                else:raise Exception("ZIA not on v2.0 - TLS  : UI")
            elif requiredTunnelVersion.upper()=="V1":
                if tunnelProtocol=="v1.0":self.logger.info("ZIA ON v1.0 Validated on UI")
                else:raise Exception("ZIA not on v1.0 : UI")
            elif requiredTunnelVersion.upper()=="NONE":
                if tunnelProtocol=="...":
                    ziaState=self.UI.click(self.elements.INTERNET_SECURITY_TUNNEL_STATUS_TEXT, sleep_time=0, AEFC="Unable to open Internet Security Tunnel Status Text",returnText=True)
                    if 'disabled' in ziaState.lower():
                        self.logger.info("ZIA Disabled validated")
                    else:
                        raise Exception("Disabled tunnel not validated on UI")
                else:
                    self.logger.error(f'... was required in tunnel version, got {tunnelProtocol}')
                    raise Exception(f'... was required in tunnel version, got {tunnelProtocol}')
            else:
                self.logger.error("Invalid required state given, please give DTLS,TLS ,v1 or none")
                raise Exception("Invalid required state given, please give DTLS,TLS ,v1 or none")
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------    
    @allure.step("validate zia server state")
    def Validate_Zia_Server_State(self,               # this function checks zia state or fetches the smeIP if required
                                  requiredState: str  # accepted values :: gateway,connected error,smeIP,smePort
                                  ):
        if self.operating_system == "mac":
            if 'gateway' in requiredState.lower() or 'smeIP' in requiredState.lower() or 'smePort' in requiredState.lower() or 'connected' in requiredState.lower():
                self.Bring_Zcc_To_Focus()
                self.UI.click(self.elements.INTERNET_SECURITY_TAB, sleep_time=0,AEFC="Unable to open Internet Security tab")
                if self.UI.check_if_exists(self.elements.INTERNET_SECURITY_RETRY_BUTTON,AEFC="Unable to click on zia retry button"):
                    raise Exception("ERROR :: Gateway or smeIP expected, got connection error")
                else:
                    zccUIData = self.SysOps.get_data()
                    ziaState = str(zccUIData["serverip"])
                    if 'smePort' in requiredState.lower() : 
                        ziaState = str(zccUIData["smePort"])
                        self.logger.info(f"smePort = {ziaState}")
                    else:
                        self.logger.info(f"ziaState = {ziaState} |||| length = {len(ziaState)}")
            else:
                self.Bring_Zcc_To_Focus()
                self.UI.click(self.elements.ZSA_TRAY_FORM, AEFC="App not Found", max_threshold=20, sleep_time=0)
                self.UI.click(self.elements.INTERNET_SECURITY_TAB, sleep_time=0, AEFC="Unable to open Internet Security tab")
                if self.UI.check_if_exists(self.elements.INTERNET_SECURITY_RETRY_BUTTON, AEFC="Unable to open Internet Security Tunnel Status Text"):
                    self.logger.info("Retry button found")
                    logsToBeSearched = ['getCurrentNetworkType:NON_TRUSTED getSmeProxyState:SERVER_DOWN_ERROR']
                    self.logOps.search_log_file(file="ZSATunnel", words_to_find_in_line=logsToBeSearched,search_mode=None)
                    if 'SERVER_DOWN_ERROR' in self.logOps.recent_log_line.split(':')[-1]:
                        self.logger.info("SERVER_DOWN_ERROR found in logs !")
                        ziaState = 'connection error'
                    else:
                        self.logger.error("SERVER_DOWN_ERROR not found in logs !")
                        raise Exception("SERVER_DOWN_ERROR not found in logs while testing connection error state")
                else:
                    raise Exception("Retry button not found while testing connection error state")
        else:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.ZSA_TRAY_FORM, AEFC="App not Found", max_threshold=20, sleep_time=0)
            self.UI.click(self.elements.INTERNET_SECURITY_TAB, sleep_time=0, AEFC="Unable to open Internet Security tab")
            if 'gateway' in requiredState.lower() or 'smeIP' in requiredState.lower() or 'smePort' in requiredState.lower() or 'connected' in requiredState.lower():
                ziaState = self.UI.click(self.elements.INTERNET_SECURITY_SERVER_IP_TEXT, sleep_time=0,AEFC="Unable to open Internet Security Server IP Text", returnText=True)
            else:
                ziaState = self.UI.click(self.elements.INTERNET_SECURITY_TUNNEL_STATUS_TEXT, sleep_time=0,AEFC="Unable to open Internet Security Tunnel Status Text", returnText=True)

        if requiredState.lower() in ziaState.lower():
            self.logger.info(f"Required State : {requiredState}, Actual State : {ziaState}")
        elif 'smeIP' in requiredState:
            return ziaState.lower()
        elif 'smePort' in requiredState:
            return ziaState.lower().split(':')[1] if self.operating_system=="windows" else ziaState.lower()
        elif 'connected' in requiredState.lower():
            if 'error' in ziaState or 'gateway' in ziaState or '...' in ziaState or len(ziaState)==0:
                raise Exception(f"ZIA not in connected state, current state is : {ziaState}")
            else:
                self.logger.info(f"ZIA Connected :: {ziaState}")
        else:
            raise Exception(f"Required State : {requiredState}, Actual State : {ziaState}")
        
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("validate network type on zcc")
    def Validate_Network_Type_On_Zcc(self,
                                requiredNetwork: str,               # accepted values "trusted" or "off trusted"
                                service: str= "zia",                # service for which netwrok type is to be validated, zia or zpa
                                skipTrayClick: bool =False,         # whether to click on zcc tray or not - as a part of time reduction
                                failExpected: bool =False           # whether we are expecting case to fail or not
    ):
        if self.operating_system=="mac":
            zccUIData =  self.SysOps.get_data()
            if 'on' in requiredNetwork.lower() or requiredNetwork.lower()=='trusted':
                if str(zccUIData['nwstate'])=="0":self.logger.info(f"ZCC UI on trusted network validated, nwstate value -> {str(zccUIData['nwstate'])}")
                else:
                    if not failExpected:raise Exception("ZCC UI not on trusted network")
            elif 'off' in requiredNetwork.lower() or 'off trusted' in requiredNetwork.lower():
                if str(zccUIData['nwstate'])=="2":self.logger.info(f"ZCC UI on off trusted network validated, nwstate value -> {str(zccUIData['nwstate'])}")
                else:
                    if not failExpected:raise Exception("ZCC UI not on off trusted network")
            elif 'split' in requiredNetwork.lower():
                if str(zccUIData['nwstate'])=="3":self.logger.info(f"ZCC UI on split vpn trusted network validated, nwstate value -> {str(zccUIData['nwstate'])}")
                else:
                    if not failExpected:raise Exception("ZCC UI not on split vpn trusted network")
            elif 'split' not in requiredNetwork.lower() and 'vpn' in requiredNetwork.lower():
                if str(zccUIData['nwstate'])=="1":self.logger.info(f"ZCC UI on  vpn trusted network validated, nwstate value -> {str(zccUIData['nwstate'])}")
                else:
                    if not failExpected:raise Exception("ZCC UI not on vpn trusted network")
            else:
                raise Exception(f"Invalid network given -> {requiredNetwork}. Please give on,off,Vpn or splitVPN")
        else:
            if service=="zia":
                if not skipTrayClick:
                    self.Bring_Zcc_To_Focus()
                    self.UI.click(self.elements.ZSA_TRAY_FORM, AEFC="App not Found", max_threshold=20, sleep_time=0)
                    self.UI.click(self.elements.INTERNET_SECURITY_TAB, sleep_time=0, AEFC="Unable to open Internet Security tab")
                networkState = self.UI.click(self.elements.INTERNET_SECURITY_STATUS,sleep_time=0, AEFC="Unable to click on INTERNET_SECURITY_STATUS",returnText=True)
            else:
                if not skipTrayClick:
                    self.Bring_Zcc_To_Focus()
                    self.UI.click(self.elements.ZSA_TRAY_FORM, AEFC="App not Found", max_threshold=20, sleep_time=0)
                    self.UI.click(self.elements.PRIVATE_ACCESS_TAB, sleep_time=0, AEFC="Unable to open Private Access tab")
                networkState = self.UI.click(self.elements.PRIVATE_ACCESS_NETWORK_STATUS,sleep_time=0, AEFC="Unable to click on INTERNET_SECURITY_STATUS",returnText=True)  
            
            if 'on' in requiredNetwork.lower():
                if "on" in networkState.lower():
                    if not failExpected:raise Exception("ZCC UI not on trusted network")
                else:self.logger.info("ZCC UI on trusted network validated")
            elif 'off' in requiredNetwork.lower():
                if "off" in networkState.lower():self.logger.info("ZCC UI on off trusted network validated")
                else:
                    if not failExpected:raise Exception("ZCC UI not on off trusted network")
            elif 'split' in requiredNetwork.lower():
                if 'split' in networkState.lower():self.logger.info("ZCC UI on split vpn trusted network validated")
                else:
                    if not failExpected:raise Exception("ZCC UI not on split VPN trusted network")
            elif 'split' not in requiredNetwork.lower() and 'vpn trusted' in requiredNetwork.lower():
                if 'vpn trusted' in networkState.lower():self.logger.info("ZCC UI on vpn trusted network validated")
                else:
                    if not failExpected:raise Exception("ZCC UI not on VPN trusted network")
            else:
                raise Exception(f"Invalid network given -> {requiredNetwork}. Please give on,off,Vpn or splitVP")
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Validate Strict Enforcement")
    def Validate_Strict_Enforcement(self):
        if self.operating_system=="mac":
            pass
        else:
            try:
                self.Bring_Zcc_To_Focus()
                se_banner_header = self.UI.click(self.elements.STRICT_ENFORCEMENT_BANNER_HEADER,returnText=True)
                se_banner_body = self.UI.clic(self.elements.STRICT_ENFORCEMENT_BANNER_MESSAGE,returnText=True)
                if (se_banner_header=="Internet Access Blocked" and 
                    se_banner_body=="Internet Access is blocked on this computer as per your company's policy unless you sign into Zscaler Client Connector."):self.logger.info("Strict Enforcement banner found on GUI")
                else:raise Exception("Strict Enforcement banner missing on GUI")
            except Exception as e:
                self.logger.error(e)
                raise Exception(e)

    # ------------------------------------------------------------------------------------------------------------------------------------------------------       
    @allure.step("Update Policy From Side menu")
    def Update_Policy_From_Side_Menu(self, 
        sleep_time: int =30):         # number of seconds to sleep after execution
        try:
            self.Bring_Zcc_To_Focus()
            self.UI.click(self.elements.ZCC_SIDE_MENU_ICON, sleep_time=1*self.sleep_factor, AEFC="Unable to click side Menu button on ZCC")
            self.UI.click(self.elements.ZCC_UPDATE_POLICY_FROM_SIDE_MENU, sleep_time=sleep_time, AEFC="Unable to click Update Policy button from side menu")
        except Exception as e: 
            self.logger.error(f"Update Policy Failed :: {e}")
            raise Exception(f"Error :: Update Policy Failed :: {e}")
        self.logger.info("Success :: Updated Policy!")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Uninstall ZCC With Password")
    def Uninstall_With_Password(self,
            password : str,                 # password to be used to Uninstall ZCC
            failure_expected: bool=False    # True/False based on failure expectation
    ):
        time.sleep(2)
        self.logger.info(f"Entering uninstall password : {password}")
        # pyautogui.write(passowrd)
        try:
            from common_lib.Install_Uninstall import Install_Uninstall
            from multiprocessing import Process
            o = Install_Uninstall()

            curl_process = Process(
                target=o.uninstall_zcc
            )
            curl_process.start()
            self.UI.type(self.elements.UNINSTALL_PASSWORD_BOX, text=password, sleep_time=1,AEFC="Unable to enter password")
            self.UI.click(self.elements.UNINSTALL_PASSWORD_OK_BUTTON, sleep_time=1, AEFC="Unable to enter password")
            if failure_expected:
                self.UI.click(self.elements.UNINSTALL_PASSWORD_FAILURE_OK_BUTTON, sleep_time=1,AEFC="Unable to click ok after wrong password")
            curl_process.join(30)
        except Exception as e:
            raise Exception(f"Unable to enter uninstall password!! :: {e}")


################################################################
#               DOCUMENTATION FOR GUI.PY                       #
#                                                              #
# AUTHOR : SRINIVASA BHASWANTH (<EMAIL>)        #
################################################################
__author__ = '{srinivasa bhaswanth}'
__credits__ = ['{srinivasa bhaswanth, abhimanyu sharma, sahil kumar}']
__email__ = '{<EMAIL>}'

######  Module ##############################################################################################################################################
#from concurrent.futures import process
import subprocess
import os
import platform
import copy
import time
import psutil
import shutil
import pyautogui
from threading import Thread
import logging

#import Constants
from common_lib import Constants

# get to know the operating system
if platform.uname()[0]=='Windows': os_version="Windows"
elif platform.uname()[0]=='Darwin': os_version="MAC"
else: raise Exception("OS version cannot be determined")

##############################################################################################################################################################

class Install_Uninstall:
    """
    Base class to be used for MA/ZIA related actions
    any class can import this for performing its own set of MA actions
    Environment
    ------------
        1. operating_system     (str)   name of operating system ('windows'/'mac')
        2. CONST                (class) a class of variables used through out this script
    Work Flow
    ----------
        1. There are 2 important Central Functions
            a. install_zcc()
            b. uninstall_zcc()
        2. these functions can be used with various parameters to install/uninstall zcc on windows/mac
    """
    def __init__(self, verify_installers_exist=False, logger=None):
        self.os_version = os_version
        self.CONST = Constants.Utils
        if logger==None:
            # self.logger = Logger.Logger(os.path.basename("ZAPPAutomation.py").replace(".py", ".log"))
            self.logger = self.Initialize_Logger("Install_Uninstall.log", Log_Level="DEBUG")
        else:
            self.logger = logger
        self.is_zcc_installed = self.check_if_zcc_exists()
        self.architecture = None
        if verify_installers_exist: self.verify_installers_exist_()
        self.recently_installed_zcc_version=None
        assert os.path.exists(self.CONST.zapp_archive_path)
        self.logger.info("in Install_Uninstall")
    

    # --------------------------------------------------------------------------------------------------------------------------
    def Initialize_Logger(self, 
    Log_File_Name,                      # (str)     Name of the Log file to be created 
    Log_Level,                          # (str)     Level of logging (INFO/DEBUG/WARNING/CRICITCAL etc)
    console_handle_needed=True          # (bool)    print on command line if True only
    ):
        """
        Target: Create Logger object.
        Work Flow
        ----------
            1. Create a logger object.
            2. Create a console handler if required
                - console handler is used to print required info on command line window.
            3. Create a file handler -> mandatory
                - Make sure 'Logs' folder exists, as this file handler will be created in this folder only.
                - file handler is used to send all information to a log file in specified folder.
            4. Set the debug level (INFO/DEBUG/WARNING/CRITICAL etc). Default: INFO
            5. Specify how the logging must happen (with/without datetime/loglevel/username)
            6. Add all created handlers to the logger instance created in step 1.
            7. return the logger instance.
        """
        # 1, 2 & 3
        self.Logs_Directory = os.path.join(os.path.abspath(os.path.dirname(os.path.abspath("Automation.py"))), "Logs")
        logger = logging.getLogger(Log_File_Name)
        if console_handle_needed: console_handler = logging.StreamHandler()
        if not os.path.exists(self.Logs_Directory): os.mkdir(self.Logs_Directory)
        file_handler = logging.FileHandler(os.path.join(self.Logs_Directory, Log_File_Name))

        # 4
        if Log_Level:
            logger.setLevel(eval("logging.{}".format(Log_Level)))
            if console_handle_needed: console_handler.setLevel(eval("logging.{}".format(Log_Level)))
            file_handler.setLevel(eval("logging.{}".format(Log_Level)))

        # 5
        if console_handle_needed: console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))

        # 6
        if console_handle_needed: logger.addHandler(console_handler)
        logger.addHandler(file_handler)
        # 7
        return logger
    
    # -----------------------------------------------------------------------------------------------------------------------------------------------------
    def Architecture_Check(self):       # Function to find out architecture of existing zcc
        if os.path.exists(self.CONST.PROGRAM_FILES_32BIT_LOCATION) and "Zscaler" in os.listdir(self.CONST.PROGRAM_FILES_32BIT_LOCATION): self.architecture='32bit'
        elif os.path.exists(self.CONST.PROGRAM_FILES_64BIT_LOCATION) and "Zscaler" in os.listdir(self.CONST.PROGRAM_FILES_64BIT_LOCATION): self.architecture='64bit'
        else: self.logger.info("\n\n\nWarning :: ZCC Probably not installed!\n\n\n")

    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def list_all_available_zcc_installers(self):    # Function to list all available installers in archive
        """
        Work Flow
        ----------
            1. check if zapp_archive folder exists
            2. if yes, return all available installers for that operating system
        """
        assert os.path.exists(self.CONST.zapp_archive_path)
        if self.os_version=="Mac":
            installers =  [files for files in sorted(os.listdir(self.CONST.zapp_archive_path),reverse=True) if (".app" in files)]
        else:
            installers =  [files for files in sorted(os.listdir(self.CONST.zapp_archive_path),reverse=True) if ((".exe" in files) or (".msi" in files))]
        if len(installers)==0:
            self.logger.error("no installers present")
            raise Exception("Error :: no installers present")
        return installers

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def verify_installers_exist_(self):     # Function to make sure all prerequisites exist already
        """
        Work Flow
        ----------
            1. make sure zapp_archive folder exists
            2. make sure all versions of zapp exist as specified in CONST
        """
        assert os.path.exists(self.CONST.zapp_archive_path)
        if os_version=="Windows":
            for each in [self.CONST.latest_zcc_exe,self.CONST.latest_zcc_msi,self.CONST.old_zcc_exe,self.CONST.old_zcc_msi]:
                assert os.path.exists(self.CONST.zapp_archive_path+str(each))
        elif os_version=="MAC":
            for each in [self.CONST.latest_zcc_app, self.CONST.old_zcc_app]:
                assert os.path.exists(self.CONST.zapp_archive_path+str(each))


    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def validate_process_running(self,      # Function to verify process is running in background (currently for windows only)
    process_name):                          # (str)     name of process 
        """
        Work Flow
        ----------
            1. get the process name
            2. Iterate over all running process, to see if requested process can be seen running
            3. raise Exception if not found
        """
        if self.os_version=="MAC":
            return
        else:
            self.process_list = []
            for proc in psutil.process_iter():
                try:
                    # Get process name & pid from process object.
                    self.process_list.append([proc.name(), proc.pid])
                    if process_name==proc.name():
                        self.logger.error("Success ::: Process: {0} running with id: {1}".format(process_name, proc.pid))
                        return
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass
            self.logger.error("requested process not found!!!")
            raise Exception("Error :: requested process not found!!!")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def place_shortcuts(self,       # place shortcuts in a predefined location
    architecture = None             # (str)     defines the architecture of zcc under use ("32bit"/"64bit")
    ):
        """
        Work Flow
        ----------
            1. in latest versions of ZCC win (>=3.9), shortcuts are at a different place.
            2. due to this, we are storing uninstall and app shortcuts.
            3. these shortcuts will placed in required location to help automation locate ZCC.
        """
        if os_version!="MAC":
            if not architecture in ["32bit", "64bit"]:
                raise Exception("Unexpected architecture detected!!!")
            try:
                assert os.path.exists(os.path.join(self.CONST.zapp_archive_path, self.CONST.ShortCutLocation_32bit if architecture=="32bit" else self.CONST.ShortCutLocation_64bit, self.CONST.app_shortcut))
                assert os.path.exists(os.path.join(self.CONST.zapp_archive_path, self.CONST.ShortCutLocation_32bit if architecture=="32bit" else self.CONST.ShortCutLocation_64bit, self.CONST.uninstaller_shortcut))
            except:
                self.logger.error("please put the shortcuts (app/uninstaller) back in zapp_archive")
                raise Exception("Error :: please put the shortcuts (app/uninstaller) back in zapp_archive")
            if not os.path.exists(self.CONST.target_path_1):
                os.makedirs(self.CONST.target_path_1)
            if not os.path.exists(self.CONST.target_path_2):
                os.makedirs(self.CONST.target_path_2)
            shutil.copyfile(os.path.join(self.CONST.zapp_archive_path, self.CONST.ShortCutLocation_32bit if architecture=="32bit" else self.CONST.ShortCutLocation_64bit, self.CONST.app_shortcut), self.CONST.target_path_1+self.CONST.app_shortcut)
            shutil.copyfile(os.path.join(self.CONST.zapp_archive_path, self.CONST.ShortCutLocation_32bit if architecture=="32bit" else self.CONST.ShortCutLocation_64bit, self.CONST.app_shortcut), self.CONST.target_path_2+self.CONST.app_shortcut)
            shutil.copyfile(os.path.join(self.CONST.zapp_archive_path, self.CONST.ShortCutLocation_32bit if architecture=="32bit" else self.CONST.ShortCutLocation_64bit, self.CONST.uninstaller_shortcut), self.CONST.target_path_1+self.CONST.uninstaller_shortcut)
            shutil.copyfile(os.path.join(self.CONST.zapp_archive_path, self.CONST.ShortCutLocation_32bit if architecture=="32bit" else self.CONST.ShortCutLocation_64bit, self.CONST.uninstaller_shortcut), self.CONST.target_path_2+self.CONST.uninstaller_shortcut)
            self.logger.info("Shortcuts placed successfully")
    
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def remove_shortcuts(self,          # Remove shortcuts for 32bit zcc if already placed in location
    architecture=None                   # (str)     defines the architecture of zcc
    ):
        """
        Work Flow
        ----------
            1. in latest versions of ZCC win (>=3.9), shortcuts are at a different place.
            2. due to this, we are storing uninstall and app shortcuts.
            3. these shortcuts will placed in required location to help automation locate ZCC.
            4. these placed shortcuts will be removed by this function to ensure a clean env.
        """
        if os_version!="MAC":
            if not architecture in ["32bit", "64bit"]:
                raise Exception("Unexpected architecture detected!!!")
            if os.path.exists(self.CONST.target_path_1+self.CONST.app_shortcut): os.remove(self.CONST.target_path_1+self.CONST.app_shortcut)
            if os.path.exists(self.CONST.target_path_2+self.CONST.app_shortcut): os.remove(self.CONST.target_path_2+self.CONST.app_shortcut)
            if os.path.exists(self.CONST.target_path_1+self.CONST.uninstaller_shortcut): os.remove(self.CONST.target_path_1+self.CONST.uninstaller_shortcut)
            if os.path.exists(self.CONST.target_path_2+self.CONST.uninstaller_shortcut): os.remove(self.CONST.target_path_2+self.CONST.uninstaller_shortcut)

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def install_zcc(self,               # Function to install zcc on windows/mac
    zcc_version="latest",               # (str)     select version to install zcc (latest/older/custom_zcc_installer_name)
    verify_installer_running=False,     # (bool)    verify installer is running in background (works for windows only)
    exe=True,                           # (bool)    this is to install using an exe installer. if both exe and msi are True, exe is priority
    msi=True,                           # (bool)    this is to install using an msi installer
    sleep_timer=1,                     # (int)     number of seconds to sleep after installation is done
    install_only_if_uninstalled=False,  # (bool)    verify zcc installation doesn't exist before installation
    architecture=None,                  # (str)     specify the architecture of the ZCC build to install (32bit/64bit)
    place_shortcuts=True,               # (bool)    place shortcuts in location accordingly
    **kwargs):                          # (**)      careful using this..
        """
        Work Flow
        ----------
            1. make sure zcc doesn't exist
            2. if zcc_version is "older" or "latest", install as specified by consts
            3. otherwise expect a custom installer to be used for installation
            4. before installation make sure these installers exist in zapp_archive
            5. when required, perform background process check for windows only to see installer is running
            6. sleep for sometime
            7. make sure zcc exists
            8. for recent zcc versions, app/uninstall shortcuts are different. place them in right folders properly
        """
        install_parameters = ['start "" ']
        process_name = None
        if install_only_if_uninstalled: assert not self.check_if_zcc_exists()
        if os_version=="MAC":
            if zcc_version=="latest" or zcc_version=="older":
                self.CONST.current_installed_version =  str(self.CONST.latest_zcc_app if zcc_version=="latest" else self.CONST.old_zcc_app).split("-")[2]
                assert os.path.exists(self.CONST.zapp_archive_path + str(self.CONST.latest_zcc_app if zcc_version=="latest" else self.CONST.old_zcc_app))
                install_parameters = "sudo sh '{}/Contents/MacOS/installbuilder.sh' --mode unattended --unattendedmodeui none".format(
                    self.CONST.zapp_archive_path + str(self.CONST.latest_zcc_app if zcc_version=="latest" else self.CONST.old_zcc_app))
                self.recently_installed_zcc_version = (self.CONST.latest_zcc_app if zcc_version=="latest" else self.CONST.old_zcc_app)
            else:
                assert os.path.exists(self.CONST.zapp_archive_path + str(zcc_version))
                install_parameters = "sudo sh '{}/Contents/MacOS/installbuilder.sh' --mode unattended --unattendedmodeui none".format(
                    self.CONST.zapp_archive_path + str(zcc_version))
                self.recently_installed_zcc_version = zcc_version
            # key word arguments part
            for each_key,each_value in kwargs.items():
                install_parameters = install_parameters + " --" + str(each_key) + " " + str(each_value)
        else:
            if exe==True:
                if zcc_version=="latest" or zcc_version=="older":
                    self.CONST.current_installed_version = str(self.CONST.latest_zcc_exe if zcc_version=="latest" else self.CONST.old_zcc_exe).replace('-gov','').split("-")[2]
                    assert os.path.exists(self.CONST.zapp_archive_path + str(self.CONST.latest_zcc_exe if zcc_version=="latest" else self.CONST.old_zcc_exe))
                    install_parameters.append('"{}"'.format(self.CONST.zapp_archive_path + str(self.CONST.latest_zcc_exe if zcc_version=="latest" else self.CONST.old_zcc_exe)))
                    install_parameters.append("--mode")
                    install_parameters.append("unattended")
                    process_name = self.CONST.latest_zcc_exe if zcc_version=="latest" else self.CONST.old_zcc_exe
                    self.recently_installed_zcc_version = self.CONST.latest_zcc_exe if zcc_version=="latest" else self.CONST.old_zcc_exe
                    self.recently_installed_zcc_version = zcc_version
                else:
                    assert os.path.exists(self.CONST.zapp_archive_path + str(zcc_version))
                    install_parameters.append('"{}"'.format(self.CONST.zapp_archive_path + str(zcc_version)))
                    install_parameters.append("--mode")
                    install_parameters.append("unattended")
                    process_name = zcc_version
            elif msi==True:
                if zcc_version=="latest" or zcc_version=="older":
                    assert os.path.exists(self.CONST.zapp_archive_path + str(self.CONST.latest_zcc_msi if zcc_version=="latest" else self.CONST.old_zcc_msi))
                    install_parameters.append('"{}"'.format(self.CONST.zapp_archive_path + str(self.CONST.latest_zcc_msi if zcc_version=="latest" else self.CONST.old_zcc_msi)))
                    install_parameters.append("/passive")
                    process_name = self.CONST.latest_zcc_msi if zcc_version=="latest" else self.CONST.old_zcc_msi
                    self.recently_installed_zcc_version = self.CONST.latest_zcc_msi if zcc_version=="latest" else self.CONST.old_zcc_msi
                else:
                    assert os.path.exists(self.CONST.zapp_archive_path + str(zcc_version))
                    install_parameters.append('"{}"'.format(self.CONST.zapp_archive_path + str(zcc_version)))
                    install_parameters.append("/passive")
                    process_name = zcc_version
                    self.recently_installed_zcc_version = zcc_version
            
            install_parameters = ' '.join(install_parameters)
            # key word arguments part
            for each_key,each_value in kwargs.items():
                install_parameters = install_parameters + " --" + str(each_key) + " " + str(each_value)
            if verify_installer_running:
                install_parameters+= " && timeout 3 && wmic process get executablepath"

        print(install_parameters)

        output = subprocess.check_output(install_parameters, shell = True)
        if verify_installer_running and self.os_version!="Mac": 
            #self.validate_process_running(process_name)
            assert process_name in output
        #time.sleep(sleep_timer)   
        if install_only_if_uninstalled: assert self.check_if_zcc_exists()  
        self.is_zcc_installed = True
        del install_parameters   
        self.logger.info("\nInstaller Version :: {}".format(self.recently_installed_zcc_version))
        self.logger.info("type :: {}".format(("exe" if exe else "msi") if self.os_version!="Mac" else "app"))

        if architecture==None: 
            self.Architecture_Check()
        else:
            self.architecture = architecture
        self.place_shortcuts(self.architecture)
        if verify_installer_running: self.logger.info("Installer verification :: Success\n")
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def uninstall_zcc(self,                 # Function to uninstall zcc on windows
    verify_uninstaller_running=False,       # (bool)    verify uninstaller is running in background if True (works only on windows)
    sleep_timer=1,                         # (int)     number of seconds to sleep after uninstallation
    uninstall_only_if_installed=False,      # (bool)    verify zcc installation before uninstalling
    remove_shortcuts=True,                  # (bool)    remove unnecessary shortcuts if already created
    architecture=None                       # (bool)    uninstall 64bit zcc instead of 32bit. (if None, dynamically look and uninstall)
    ):                 
        """ 
        Work Flow 
        ----------
            1. find out the operating system (win/mac)
            2. verify if zcc installation exists already
            2. execute the cmd line command from python
            3. verify if zcc installation doesn't exist
        """
        if uninstall_only_if_installed: assert self.check_if_zcc_exists()
        if architecture==None:
            self.Architecture_Check()
        else:
            self.architecture = architecture
        if self.os_version=="MAC":
            cmd = "sudo sh "+self.CONST.mac_zcc_uninstaller_path
            self.logger.info(f"Uninstalling with command : {cmd}")
            subprocess.check_output(cmd, shell=True)
        else: 
            if os.path.exists(self.CONST.windows_zcc_uninstaller_path):
                uninstaller_path = self.CONST.windows_zcc_uninstaller_path
            elif os.path.exists(self.CONST.windows_zcc_uninstaller_path_64bit):
                uninstaller_path = self.CONST.windows_zcc_uninstaller_path_64bit
            else:
                print("uninstaller file doesnt exist. so returning..")
                return
            #print("Uninstall parameters: \n",'start "" "{}" --mode unattended && timeout 3 && wmic process get executablepath')
            self.logger.info("Uninstallating, please wait")
            output = subprocess.check_output('start "" "{}" --mode unattended && timeout 3 && wmic process get executablepath'.format(uninstaller_path), shell=True)
        #time.sleep(sleep_timer)
        if uninstall_only_if_installed: assert not self.check_if_zcc_exists()
        self.is_zcc_installed=False
        self.logger.info("Uninstalled !!!")
        if verify_uninstaller_running and self.os_version!="Mac": 
            assert (r"ZSAInstaller\uninstall.exe" in output) and (r"_uninstall" in output)
        self.logger.info("\nUninstaller Version :: {}".format(self.recently_installed_zcc_version))
        
        if remove_shortcuts: self.remove_shortcuts(self.architecture)
        if verify_uninstaller_running: self.logger.info("Uninstaller verification :: Success\n")

    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def check_if_zcc_exists(self):
        """ 
        Work Flow 
        ----------
            1. find out the operating system (win/mac)
            2. assert if the uninstaller exists already.
            3. uninstaller existence implies ZCC installed already. same in reverse
        """
        if self.os_version!="MAC":
            if os.path.exists(self.CONST.windows_zcc_uninstaller_path): return True
            elif os.path.exists(self.CONST.windows_zcc_uninstaller_path_64bit): return True
            else: return False
        else:
            if os.path.exists(self.CONST.mac_zcc_uninstaller_path): return True
            else:  return False
    # ------------------------------------------------------------------------------------------------------------------------------------------------------

    def uninstall_npcap_ui_actions(self):
        print("Pressing Enter for confirm button")
        pyautogui.press('enter')
        time.sleep(40)
        print("Pressing Enter for done or close button")
        pyautogui.press('enter')
        print("Ui actions done for uninstalling npcap")
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    
    def uninstall_npcap(self):
        uninstaller_exe = Constants.Utils.npcap_uninstaller_exe_path
        uninstall_command = '"'+uninstaller_exe+'"'+r" && timeout 0 && wmic process get executablepath"
        print(uninstall_command)
        if os.path.isfile(uninstaller_exe):
            p1 = subprocess.Popen(uninstall_command,shell=True,stdout = subprocess.PIPE,stderr=subprocess.PIPE)
            hitExeFileResult=p1.communicate()
            time.sleep(5)
            self.uninstall_npcap_ui_actions()
            if len(hitExeFileResult[1])==0:
                if not os.path.isfile(uninstaller_exe):print("NPcap Uninstalled")
                else:raise Exception("ERROR :: Command to uninstall executed, but still found uninstall.exe")
            else:raise Exception("ERROR :: {}".format(hitExeFileResult[1]))

    # ------------------------------------------------------------------------------------------------------------------------------------------------------

    def validate_npcap_installed(self):
        uninstaller_exe = Constants.Utils.npcap_uninstaller_exe_path
        if os.path.isfile(uninstaller_exe):
            print("Found NPcap Directory Installed on system")
        else:raise Exception("ERROR :: Cannot find C->Program Files->Npcap directory")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------



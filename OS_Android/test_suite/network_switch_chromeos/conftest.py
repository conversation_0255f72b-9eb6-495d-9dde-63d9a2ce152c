import os
from common_lib.mobileadmin.appprofile import *
from common_lib.mobileadmin.forwardingprofile import *
from OS_Android.library.ui_zcc import *


config_path = os.path.join(
    os.getcwd(), "config", os.environ.get("CONFIG_FILE")
)  #: The variables loaded from the configuration file.
with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)

zcc_obj = Zcc()

logger_obj = logger.Logger.initialize_logger(log_file_name="feature_name.log", log_level="DEBUG")
app_profile_obj = AppProfile(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)
forwarding_profile_obj = ForwardingProfile(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)


HELPER_MACROS="ChromeOS_ZAPP_Automation_Macros/Helpers/"
NETWORK_SWITCH_MACROS="ChromeOS_ZAPP_Automation_Macros/Network_Switch_Automation/"



# network_switch_chromeos TCs - Requirements
## Prerequisites:
    1) On MA, company and user must be properly configured.
    2) On Android device, proper zcc version should be installed along with test apk.
    3) Install chrome Remote Desktop, UI Vision extensions, and chromeOS must be connected to 2 wifis and one LTE connection. 
    4) ui.vision.html file must be at correct location according to run_macro function. 
    5) Connect chromeOS to your machine using chromeRemote Desktop.
    6) Before running test case, close all the browser except one which have chrome Remote Desktop connected.
    7) Get the wifi dns from which chromeOS is connect and set it in the setup_class of TestNetworkSwitch class.

## If UI vision is unable to find the images, change them according to your environment. Also avoid multiple screen while running the test case.

## Commands to Execute Automation:
**Running All Cases:** python trigger_automation.py --config <config.json> --testcases test_suite/network_switch_chromeos

**Running One Test File from debugging_logs folder:** python trigger_automation.py --config config.json --testcases test_suite/network_switch_chromeos/*.py

import allure
from OS_Android.test_suite.network_switch_chromeos import conftest
from common_lib.Custom_Markers import *


class TestNetworkSwitch:
    @allure.title("Setup Method")
    def setup_class(self):
        '''
        Setup class steps:
        1. Initializing required instances
        2. Editing forwarding profile and adding dns for trusted network.
        '''
        
        self.zcc = conftest.zcc_obj
        self.forwarding_profile=conftest.forwarding_profile_obj
        self.app_profile=conftest.app_profile_obj

        result = self.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        self.app_profile.create_app_profile(operating_system="Android")
        assert result[0], result[1]

        result = self.forwarding_profile.edit_forwarding_profile(trusted_network="inforwardprofile",trusted_condition_match="any",dns_servers="10.38.112.11,10.38.112.10")
        assert result[0], result[1]

    # After Login
    @allure.title("Test Network_Switch")
    @pytest.mark.xray("MR-16782")
    def test_NETWORK_SWITCH_MACROS(self):
        '''
        This function tests the network switch scenario for chromeOS.
        We have implemented 4 macros for network switch
        1. From Wifi to LTE
        2. From LTE to Wifi
        3. From Trusted Wifi to Off Trusted Wifi
        4. From Off Trusted Wifi to Trusted Wifi

        It performs the following steps:
        1. Update the policy using UI vision
        2. Calling each macro one by one.
        '''
        
        result = self.zcc.run_macro("Updating Policy", conftest.HELPER_MACROS + "Update_Policy")
        assert result[0], result[1]

        result = self.zcc.run_macro("Network_Switch_WIFI_to_LTE", conftest.NETWORK_SWITCH_MACROS + "NW_Switch_WIFI_to_LTE")
        assert result[0], result[1]

        result = self.zcc.run_macro("Network_Switch_WIFI_to_LTE", conftest.NETWORK_SWITCH_MACROS + "NW_Switch_LTE_to_WIFI")
        assert result[0], result[1]

        result = self.zcc.run_macro("Network_Switch_WIFI_to_LTE", conftest.NETWORK_SWITCH_MACROS + "NW_Switch_WIFI_Trusted_to_WIFI_OFF_Trusted")
        assert result[0], result[1]

        result = self.zcc.run_macro("Network_Switch_WIFI_to_LTE", conftest.NETWORK_SWITCH_MACROS + "NW_Switch_WIFI_OFF_Trusted_to_WIFI_Trusted")
        assert result[0], result[1]


    @allure.title("Teardown")
    def teardown_class(self):
        '''
        Steps:
        1. Delete App profile
        2. Delete Forwarding profile
        '''
        result = self.app_profile.delete_app_profile(operating_system="Android")
        assert result[0], result[1]
        result = self.forwarding_profile.delete_forwarding_profile()
        assert result[0], result[1]
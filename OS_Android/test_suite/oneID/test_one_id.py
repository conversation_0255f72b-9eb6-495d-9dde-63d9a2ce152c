import pytest,allure
from OS_Android.test_suite.oneID import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.ui_zcc import Zcc

from OS_Android.library.fetch_android_logs import *
logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")


class TestOneId:

    @allure.title("Setup Method")
    def setup_class(self):
        self.logger_obj = conftest.logger_obj
        self.zcc = Zcc(start_app=True,log_handle=self.logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.log_obj = conftest.log_ops
        self.fetch_log = FetchAndroidLogs(logger_obj)
        self.log_handler = conftest.log_handler
        self.device_overview = conftest.device_overview
        self.trusted_network = conftest.trusted_network
        act_net = self.export_logs.appinfo_file_values('Active Network')
        self.logger_obj.info(f"Current Active Network is {act_net}")
        self.logger_obj.info("Fetching ip addr of active network")
        self.ipv4 = self.sys_ops.active_ipv4(act_net)
        self.ipv6 = self.sys_ops.active_ipv6(act_net)
        self.client_conn_support = conftest.client_conn_support
        self.one_id_user = '<EMAIL>'
        self.one_id_password = 'Dana@12345'
        self.aup_flag = True
        

    @allure.title("Verify the registration is successful for ZIA+ZPA+ZDX company")
    @add_markers("regression")
    @add_markers("sanity")
    @pytest.mark.xray(["QA-331189","QA-331193","QA-331194", "QA-331195" ])
    def test_one_zia_zpa_Zdx(self, setup_teardown):
        """
        This test for verify status in enroll device using oneID login flow
        ZIA+ZPA+ZDX

        :param setup_teardown:
        :return:
        """

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        result = self.client_conn_support.toggle_pre_populate_client_connector_username(using_javascript=True,
                                                                                   using_login_hint=True)
        assert result[0], result[1]

        self.zcc.logger.info("login to zcc using oneID login flow")
        result = self.zcc.one_id_login(username=self.one_id_user,
                                       password = self.one_id_password, aup = self.aup_flag)
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.filepath = os.path.join(os.getcwd(), "OS_Android/Temp_Logs/")
        self.files = os.listdir(self.filepath)
        self.fetch_log.unzip_log_files(unzip_path=self.filepath)

        self.android_files = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['tray']

        string_in_file = f'device/ops/api/v1/devices/register/zpa'
        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line=string_in_file,
                                 directory=self.const_obj.LOG_PATH)
            if result[0] is True:
                break

        assert result[0], result[1]

        string_in_file = f'device/ops/api/v1/devices/register/zia'
        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line=string_in_file,
                                 directory=self.const_obj.LOG_PATH)
            if result[0] is True:
                break

        string_in_file = f'device/ops/api/v1/devices/register/zdx'
        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line=string_in_file,
                                 directory=self.const_obj.LOG_PATH)


        assert result[0] is False, result[1]

        result = self.zcc.clear_logs()
        assert result[0], result[1]

        result = self.zcc.logout()
        assert result[0], result[1]

        time.sleep(20)

        result = self.device_overview.fetch_enrolled_devices_details(get_registration_status=True)
        assert result[2] is False, result[1]





















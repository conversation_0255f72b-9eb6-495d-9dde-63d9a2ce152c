import pytest, allure
from OS_Android.test_suite.oneID import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.ui_zcc import Zcc

from OS_Android.library.fetch_android_logs import *
logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")


def logout_if_login(self):
    logged_in = self.zcc.validate_zcc_logged_in()
    if logged_in[0]:
        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Logout from zcc")
        result = self.zcc.logout()
        assert result[0], result[1]


class TestOneIdLog:

    @allure.title("Setup Method")
    def setup_class(self):
        self.logger_obj = conftest.logger_obj
        self.zcc = Zcc(start_app=True, log_handle=self.logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.fetch_log = FetchAndroidLogs(logger_obj)
        self.log_handler = conftest.log_handler
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.device_overview = conftest.device_overview
        self.trusted_network = conftest.trusted_network
        self.client_conn_support = conftest.client_conn_support
        act_net = self.export_logs.appinfo_file_values('Active Network')
        self.logger_obj.info(f"Current Active Network is {act_net}")
        self.logger_obj.info("Fetching ip addr of active network")
        self.one_id_user = '<EMAIL>'
        self.one_id_password = 'Dana@12345'
        self.aup_flag = True

        result = self.client_conn_support.toggle_pre_populate_client_connector_username(using_javascript=True,
                                                                                        using_login_hint=True)
        assert result[0], result[1]

        self.zcc.logger.info("login to zcc using oneID login flow")
        result = self.zcc.one_id_login(username=self.one_id_user,
                                       password=self.one_id_password, aup=self.aup_flag)
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]


        self.company = conftest.variables["ZIA_ADMIN_ID"].split('@')[-1]

        self.zcc.logout()


        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.filepath = os.path.join(os.getcwd(), "OS_Android/Temp_Logs/")
        self.files = os.listdir(self.filepath)
        self.fetch_log.unzip_log_files(unzip_path=self.filepath)

        self.android_files = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['tray']

    @allure.title("Verify registration for ZDX")
    @add_markers("regression")
    @pytest.mark.xray(["QA-331179"])
    def test_zdx_register(self, setup_teardown):
        string_in_file = f'https://{self.company}/device/ops/api/v1/devices/register/zdx'
        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line=string_in_file,
                                 directory=self.const_obj.LOG_PATH)

        assert result[0] is False, result[1]

    @allure.title("Verify deregistration for ZIA")
    @add_markers("regression")
    @pytest.mark.xray(["QA-331193"])
    def test_zia_deregister(self, setup_teardown):

        string_in_file = f'https://{self.company}/device/ops/api/v1/devices/deregister/zia'
        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line=string_in_file,
                                 directory=self.const_obj.LOG_PATH)
            if result[0] is True:
                break

        assert result[0], result[1]

    @allure.title("Verify deregistration for ZPA")
    @add_markers("regression")
    @pytest.mark.xray(["QA-331194"])
    def test_zpa_deregister(self, setup_teardown):

        string_in_file = f'https://{self.company}/device/ops/api/v1/devices/deregister/zpa'
        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line=string_in_file,
                                 directory=self.const_obj.LOG_PATH)
            if result[0] is True:
                break

        assert result[0], result[1]

    @allure.title("Verify deregistration for ZDX")
    @add_markers("regression")
    @pytest.mark.xray(["QA-331195"])
    def test_zdx_deregister(self, setup_teardown):

        string_in_file = f'https://{self.company}/device/ops/api/v1/devices/deregister/zdx'
        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line=string_in_file,
                                 directory=self.const_obj.LOG_PATH)

        assert result[0] is False, result[1]

    @allure.title("Verify the entitlements API being called for the services enabled")
    @add_markers("regression")
    @pytest.mark.xray(["QA-331175"])
    def test_entitlement(self, setup_teardown):

        string_in_file = f'https://{self.company}/device/ops/api/v1/entitlements'
        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line=string_in_file,
                                 directory=self.const_obj.LOG_PATH)
            if result[0] is True:
                break

        assert result[0], result[1]

    @allure.title("Verify the authorization end point and token end point are received from oneid-configuration")
    @add_markers("regression")
    @pytest.mark.xray(["QA-331168"])
    def test_one_id_token_endpoint(self, setup_teardown):


        string_in_file = f'https://{self.company}/oauth2/v1/authorize'
        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line= string_in_file,
                                                   directory=self.const_obj.LOG_PATH)
            if result[0] is True:
                break

        assert result[0], result[1]

    @allure.title("Verify the authentication token while login to ZCC with browser based authentication disabled")
    @add_markers("regression")
    @pytest.mark.xray(["QA-331169"])
    def test_one_auth_token_bba_disable(self, setup_teardown):

        string_in_file = f'One::ID::ZSOneIdUserAuthenticationSvc: fetch user access token done'
        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line=string_in_file,
                                 directory=self.const_obj.LOG_PATH)
            if result[0] is True:
                break

        assert result[0], result[1]


    @allure.title("Verify the MA discovery while login to ZCC")
    @add_markers("regression")
    @pytest.mark.xray(["QA-331171"])
    def test_ma_discovery(self, setup_teardown):

        string_in_file = f'/device/ops/api/v1/clouds'
        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line=string_in_file,
                                 directory=self.const_obj.LOG_PATH)
            if result[0] is True:
                break

        assert result[0], result[1]

    @allure.title("Verify the AUP page configured on MA being loaded")
    @add_markers("regression")
    @pytest.mark.xray(["QA-331172"])
    def test_aup(self, setup_teardown):

        string_in_file = f'api/oneid/v1/preenroll/aup'
        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line=string_in_file,
                                 directory=self.const_obj.LOG_PATH)
            if result[0] is True:
                break

        assert result[0], result[1]

    @allure.title("Verify the device registration with One ID")
    @add_markers("regression")
    @pytest.mark.xray(["QA-331176"])
    def test_device_registration(self, setup_teardown):

        string_in_file = f'https://{self.company}/device/ops/api/v1/devices/register'
        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line=string_in_file,
                                 directory=self.const_obj.LOG_PATH)
            if result[0] is True:
                break

        assert result[0], result[1]


        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line="JWT build token, jwt =",
                                 directory=self.const_obj.LOG_PATH)
            if result[0] is True:
                break

        assert result[0], result[1]

    @allure.title("Verify the device policy is being fetched")
    @add_markers("regression")
    @pytest.mark.xray(["QA-331176"])
    def test_policy_fetch(self, setup_teardown):

        string_in_file = f'start fetching device policy'
        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line=string_in_file,
                                 directory=self.const_obj.LOG_PATH)
            if result[0] is True:
                break

        assert result[0], result[1]

        string_in_file = f'api/oneid/v1/postenroll/policy'
        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=string_in_file,
                                 directory=self.const_obj.LOG_PATH)
            if result[0] is True:
                break

        assert result[0], result[1]







from common_lib.Custom_Markers import add_markers,zcc_mark_version,zdx_mark_version #Required!
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from OS_Android.library.android_element import AndroidElements
import pytest,allure
from OS_Android.test_suite.zcc_migration import conftest
from common_lib.helper_function import * 
from scapy.all import *

class TestQuicProtocol:
    
    @allure.title("Setup Method")
    def setup_class(self):
        self.zcc=conftest.zcc
        self.app_profile=conftest.app_profile_obj
        self.forwarding_profile=conftest.forwarding_profile_obj
        result=self.forwarding_profile.create_forwarding_profile()
        assert result[0],result[1]
        self.app_profile.create_app_profile(operating_system="Android")
        result=self.app_profile.edit_app_profile(quic_protocol="1")
        assert result[0],result[1]
        self.log_obj=conftest.log_ops
        self.pcap_obj=conftest.pcap_ops
        self.sys_ops=conftest.sys_ops
        self.const_obj=conftest.sys_ops
        self.export_logs=conftest.export_logs
        logged_in=self.zcc.validate_zcc_logged_in()
        if not logged_in[0]:
            result=self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0],result[1]

    @add_markers("regression", "sanity")
    @zcc_mark_version(1.10,1.14)
    @allure.title("QuiC protocol config download")
    @pytest.mark.xray("QA-198112")
    def test_quic_protocol_config_download(self):
        
        self.zcc.update_policy()
        self.zcc.restart_service()
        self.zcc.export_logs()
        self.export_logs.read_export_log_mail()
        result=help_return(conftest.log_ops.search_log_file,file="ZCC_Android",words_to_find_in_line=["Quick packet drop enabled : yes"], directory=conftest.const_obj.LOG_PATH)
        assert result[0]
        
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.10)
    @allure.title("QuiC packet drop")
    @pytest.mark.xray("QA-198115")
    def test_quic_packet_drop(self):
        
        #self.zcc.update_policy()
        #self.zcc.restart_service()
        self.zcc.packet_capture(action='start')
        self.sys_ops.adb_commands("adb shell am start com.google.android.youtube")
        time.sleep(5)
        self.zcc.ui.click(AndroidElements.YOUTUBE_SHORTS,sleep_time=1,AEFC="Unable to click on youtube shorts")
        time.sleep(10)
        self.sys_ops.adb_commands("adb shell am start zscaler.com.zscaler/com.zscaler.activities.MainActivity")
        time.sleep(2)
        result=self.zcc.packet_capture(action='stop')
        assert result[0],result[1]
        self.zcc.export_logs()
        self.export_logs.read_export_log_mail()
        packets,file=self.pcap_obj.read_pcap(file_type="Capture",operating_system="android")
        for packet in packets:
            if 'QUIC' in packet:
                for field in packet.quic.field_names:
                    if field=='long_packet_type' and (getattr(packet.quic,field)=='2'):
                        assert False, "QUIC packet not dropped"

    @add_markers("regression", "sanity")
    @zcc_mark_version(1.10)
    @allure.title("QUIC protocol fallback to https")
    @pytest.mark.xray("QA-198113")
    
    def test_traffic_fallback_to_https(self):
        result=help_return(conftest.log_ops.search_log_file,file="ZCC_Android",words_to_find_in_line=["Tunnel to SME for host=youtubei.googleapis.com:443"], directory=conftest.const_obj.LOG_PATH)
        assert result[0]

    @allure.title("teardown method")
    def teardown_class(self):
        result=self.app_profile.delete_app_profile(operating_system="Android")
        assert result[0],result[1]
        result=self.forwarding_profile.delete_forwarding_profile()
        assert result[0],result[1]


                    
                

                
                
                


                
        


    



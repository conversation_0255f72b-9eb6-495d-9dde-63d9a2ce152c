import pytest
import allure
import pytest
import time
import conftest
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers,zcc_mark_version,zdx_mark_version #Required!
class TestLock:
    
    def setup_method(self):
        self.zcc=conftest.zcc
        logged_in=self.zcc.validate_zcc_logged_in()
        if not logged_in[0]:
            result=self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0],result[1]
        self.sys_ops=conftest.sys_ops

    @pytest.mark.xray("QA-197851")
    @add_markers("regression", "sanity")
    @allure.title("Test vpn status after reboot")
    def test_zcc_after_device_unlock(self):
        result=self.sys_ops.screen_lock(lock=True)
        assert result[0],result[1]
        self.zcc.logger.info("Sleep for 120 seconds")
        time.sleep(120)
        result=self.sys_ops.screen_lock()
        assert result[0],result[1]
        self.zcc.explicit_wait(AndroidElements.ZIA_ON_STATUS)
        
    def teardown_method(self):
        conftest.logger_obj.info("Nothing to do in teardown")

        
        

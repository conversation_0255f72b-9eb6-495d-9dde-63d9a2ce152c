import pytest
import allure
import json 
import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version,zdx_mark_version #Required!
from common_lib.helper_function import *

class TestZcc:
    
    def setup_class(self):
        
        self.zcc=conftest.zcc
        self.log_ops=conftest.log_ops
        self.sys_ops=conftest.sys_ops
        self.const_obj=conftest.const_obj
        self.export_logs=conftest.export_logs
        


    @pytest.mark.xray("QA-233943")
    @allure.title("Test ZCC on Android 9")
    @add_markers("regression", "sanity")
    @pytest.mark.skip("Login has been tested on multiple device and cant be executed as too much time consuming")
    def test_zcc_android9(self):
        android_version=self.sys_ops.get_android_version()
        if android_version[1]=="9":
            result=self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0],result[1]
            logged_in=self.zcc.validate_zcc_logged_in()
            assert logged_in[0]
            self.zcc.export_logs()
            self.export_logs.read_export_log_mail()
            result=help_return(conftest.log_ops.search_log_file,file="ZCC_Android",words_to_find_in_line=["deviceRegistration (ZIA) successful"], directory=conftest.const_obj.LOG_PATH)
            assert result[0]
            self.zcc.restart_service()
        else:
            conftest.logger_obj.info("OS version greater than 9")
        
    
    @allure.title("teardown")
    def teardown_class(self):
        conftest.logger_obj.info("Nothing to do in teardown")
        


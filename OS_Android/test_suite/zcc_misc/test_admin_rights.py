
import pytest
import allure
import conftest
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers,zcc_mark_version,zdx_mark_version #Required!


class TestAdminRight:

    def setup_method(self):
        self.zcc=conftest.zcc
        self.sys_ops=conftest.sys_ops
        logged_in=self.zcc.validate_zcc_logged_in()
        if not logged_in[0]:
            result=self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0],result[2]

    @pytest.mark.xray("QA-220163")
    @add_markers("regression", "sanity")
    @allure.title("Test admin right on fresh installation")
    @pytest.mark.skip("This testcase not required 3.8 onwards")
    def test_admin_rights(self):
        manufacturer=self.sys_ops.adb_commands("adb shell getprop ro.product.manufacturer")
        manufacturer=manufacturer[2].decode('utf-8').split()
        self.zcc.logger.info(manufacturer)
        if any('Samsung' in manufacturer_name for manufacturer_name in manufacturer):
            i=0
            while (i<2):
                try:
                    package_names=self.sys_ops.adb_commands("adb shell pm list packages | findStr zscaler.com.zscaler")
                    package_names=package_names[2].decode('utf-8').split(":")
                    if any("zscaler.com.zscaler" in packages for packages in package_names):
                        self.zcc.logger.info("ZAPP already installed")
                    else:
                        self.zcc.ui.start_app(AndroidElements.APP)
                    self.zcc.ui.click(AndroidElements.KLMS_CHECKBOX,sleep_time=1,AEFC="Unable to click on KLMS radio button")
                    self.zcc.ui.click(AndroidElements.KLMS_CONFIRM,sleep_time=1,AEFC="Unable to click on Confirm")
                    
                except:
                    result=self.zcc.uninstall_zcc()
                    assert result[0],result[1]
                i+=1
            device_admin_apps=self.sys_ops.adb_commands("adb shell dumpsys device_policy | findstr zscaler.com.zscaler")
            device_admin_apps=device_admin_apps[2].decode('utf-8').split()
            if any("zscaler.com.zscaler" in packages for packages in device_admin_apps):
                assert False,"Failure:: Zcc detected as device admin app"
        else:
            self.zcc.logger.info("Not samsung")

    def teardown_method(self):
        self.zcc.logger.info("Nothing to do in teardown method")





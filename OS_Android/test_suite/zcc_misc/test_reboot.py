from common_lib.Custom_Markers import add_markers,zcc_mark_version,zdx_mark_version #Required!
from OS_Android.library.android_element import AndroidElements
from OS_Android.library.ui_zcc import Zcc
import pytest
import conftest
import allure,time

class TestReboot:
    

    def setup_method(self):

        self.zcc=conftest.zcc
        self.sys_ops=conftest.sys_ops
        logged_in=self.zcc.validate_zcc_logged_in()
        if not logged_in[0]:
            result=self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0],result[1]

    @pytest.mark.xray("QA-197851")
    @add_markers("regression", "sanity")
    @allure.title("Test vpn status after reboot")
    def test_vpn_reboot(self):
        try:
            self.zcc.ui.driver.quit()
            time.sleep(10)
        except:
            self.zcc.logger.info("No appium session to quit")
        result=self.sys_ops.reboot_device()
        if result[0]:
            conftest.zcc=Zcc(start_app=True,log_handle=conftest.logger_obj)
        else:
            assert False, "Failure::ADB not connected"
        self.sys_ops.adb_commands("adb shell am start -n 'com.android.settings/.Settings$VpnSettingsActivity'")
        self.zcc.explicit_wait(AndroidElements.CONNECTED)
        
    def teardown_method(self):
        self.sys_ops.adb_commands("adb shell am start -n zscaler.com.zscaler/com.zscaler.activities.MainActivity")
        self.zcc.logger.info("Zcc brought in focus")

        
        

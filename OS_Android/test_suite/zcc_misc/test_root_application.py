import os
from common_lib.Custom_Markers import add_markers,zcc_mark_version,zdx_mark_version #Required!
from common_lib.helper_function import *
import pytest
import conftest
import allure

class TestRootApp:

    @allure.title("Setup Method")
    def setup_method(self):
        self.zcc=conftest.zcc
        self.log_ops=conftest.log_ops
        self.sys_ops=conftest.sys_ops
        self.const_obj=conftest.const_obj
        self.export_logs=conftest.export_logs
        logged_in=self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logout()

    @pytest.mark.xray("QA-198106")
    @add_markers("regression", "sanity")
    @allure.title("Test ZCC when root app is installed")
    @pytest.mark.skip("Need to install package in mobile")
    def test_zcc_with_root_app(self):
        packages=self.sys_ops.adb_commands("adb shell pm list packages")
        packages=packages[2].decode('utf-8').splitlines()
        if any("magisk" in package for package in packages):
            self.zcc.logger.info("Magisc app already present")
        else:
            magiskapp=os.path.join('OS_Android','resource','Magisk-v26.4.apk')
            output=self.sys_ops.adb_commands("adb install {}".format(magiskapp))
            output=output[2].decode('utf-8').splitlines()
            if any("Success" in value_to_find for value_to_find in output):
                self.zcc.logger.info("Magisk is installed")
            else:
                assert False,"Failed to install magisc app::Setup method failed"
        result=self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0],result[1]
        self.zcc.export_logs()
        self.export_logs.read_export_log_mail()
        result=help_return(self.log_ops.search_log_file,file="AppInfo",words_to_find_in_line=["Rooted device  : true"], directory=self.const_obj.LOG_PATH)
        assert result[0]
        

    @pytest.mark.xray("QA-198099")
    @add_markers("regression", "sanity")
    @allure.title("Test ZCC when root app is not installed")
    @pytest.mark.skip("Need to install package in mobile")
    def test_zcc_with_non_root_app(self):
        packages=self.sys_ops.adb_commands("adb shell pm list packages")
        packages=packages[2].decode('utf-8').splitlines()
        if any("magisk" in package for package in packages):
            output=self.sys_ops.adb_commands("adb uninstall com.topjohnwu.magisk")
            output=output[2].decode('utf-8').splitlines()
            if any("Success" in value_to_find for value_to_find in output):
                self.zcc.logger.info("Magisk is uninstalled")
        result=self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0],result[1]
        self.zcc.export_logs()
        self.export_logs.read_export_log_mail()
        result=help_return(conftest.log_ops.search_log_file,file="AppInfo",words_to_find_in_line=["Rooted device  : false"], directory=conftest.const_obj.LOG_PATH)
        assert result[0]
       

    def teardown_method(self):
        pass
        


import allure
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers,zcc_mark_version,zdx_mark_version #Required!
from OS_Android.test_suite.pytest_android import conftest
import pytest
from common_lib.helper_function import *

class TestTunnel:
   
    @allure.title("setup")
    def setup_method(self):
    
        self.zcc_obj=conftest.zcc
        self.zcc_obj.explicit_wait(AndroidElements.ZIA_ON_STATUS)
        result=conftest.forwarding_profile_obj.create_forwarding_profile()
        assert result[0],result[1]
        result=conftest.app_profile_obj.create_app_profile(operating_system="Android")
        assert result[0],result[1]
       
    @pytest.mark.xray("QA-58338")
    @allure.title("validate sme server and tunnel status")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.0,1.14)
    def test_tunnel_setup(self):
        result=self.zcc_obj.fetch_proxy_server()
        assert result[0],result[2]
        result=self.zcc_obj.validate_zia_tunnel(zia_status='on')
        assert result[0],result[1]
        result=self.zcc_obj.validate_traffic_going_through_tunnel(is_zia_enable=True,is_zia_disable=False)
        assert result[0],result[1]
        self.zcc_obj.export_logs()
        result=conftest.export_logs.read_export_log_mail()
        assert result[0],result[1]
        result=help_return(conftest.log_obj.search_log_file,file="ZCC_Android",words_to_find_in_line=["ip.zscaler.com"], directory=conftest.const_obj.LOG_PATH)
        assert result[0]


    @allure.title("teardown")
    def teardown_method(self):
        result=conftest.app_profile_obj.delete_app_profile(operating_system="Android")
        assert result[0],result[1]
        result=conftest.forwarding_profile_obj.delete_forwarding_profile()
        assert result[0],result[1]
        
    

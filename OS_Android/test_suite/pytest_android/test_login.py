import time
import pytest
import allure
from OS_Android.test_suite.pytest_android import conftest
from common_lib.Custom_Markers import add_markers, zcc_mark_version, zdx_mark_version #Required!

'''
This testcase file tests the login functionality of a system using the TestLogin class. The class has methods for 
setting up, testing login, and tearing down. The test_login method is marked with decorators for Xray, Allure, 
and custom markers. It validates login and checks if the user remains logged in after a 10-second wait. The 
teardown_class method logs an info message and does not log out the user. 

Generated by <PERSON><PERSON><PERSON><PERSON>, assisted by <PERSON><PERSON><PERSON>r on 08-July-2024 <Please retain this marker>.
'''

class TestLogin:
    def setup_class(self):
        '''
        Initializing the zcc instance in order to perform necessary actions related to ZApp
        '''
        self.zcc_obj=conftest.zcc
        
    @pytest.mark.xray("QA-228903")
    @allure.title("Login into ZCC")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.0)
    def test_login(self):
        self.zcc_obj.logger.info("Logging in to ZApp by passing credentials from config file")
        result = self.zcc_obj.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"], conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]

        self.zcc_obj.logger.info("Sleeping for 10 secs in order to let the enrolling state popup disappears")
        time.sleep(10)

        self.zcc_obj.logger.info("Validating the login state by checking the existence of LOGOUT BUTTON")
        result = self.zcc_obj.validate_zcc_logged_in()
        assert result[0], result[1]

    @allure.title("teardown")
    def teardown_class(self):
        self.zcc_obj.logger.info("Nothing to do in teardown")
        #self.zcc_obj.Logout()
        


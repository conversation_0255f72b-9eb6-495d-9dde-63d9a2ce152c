import pytest
import time
from OS_Android.test_suite.pytest_android import conftest
import allure
from common_lib.Custom_Markers import add_markers,zcc_mark_version,zdx_mark_version


class TestZpaApp: 
    
    @allure.title("setup")
    def setup_method(self):
        self.zpa=conftest.zpa
        self.sys_ops=conftest.sys_ops
        self.zpa.delete_policy(policy_type="AccessPolicy")
        self.zpa.delete_policy(policy_type="AccessPolicy")

        result=self.zpa.create_access_policy(zpa_user_name=None,zpa_password= None,action="ALLOW",criteria=None,posture_name=None,app_segments=None,verification=None,rule_order=1)
        assert result[0],result[1]
        self.zcc=conftest.zcc
        result=self.zcc.validate_zcc_logged_in()
        if not result[0]:
            result=self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0],result[1]

    @pytest.mark.xray("QA-58335")
    @allure.title("Access zpa app and validate bytes sent and recevied")   
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.0)
    def test_zpa_app(self):
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            before_recvd_bytes=result[2][1]
        else:
            assert result[0],result[1]
        self.sys_ops.adb_commands("adb shell am start -a android.intent.action.VIEW -d {}".format(conftest.variables["ZPA_APPLICATION_NAME"]))
        time.sleep(10)
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            after_recvd_bytes=result[2][1]
        if before_recvd_bytes==after_recvd_bytes:
            assert False,"Recvd bytes not increasing, zpa app access failed"


    @allure.title("teardown")
    def teardown_method(self):
        result=self.zpa.delete_policy(policy_type="AccessPolicy")
        assert result[0],result[1]

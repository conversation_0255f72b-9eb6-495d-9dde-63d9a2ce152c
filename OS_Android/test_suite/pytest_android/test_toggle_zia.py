import pytest
from OS_Android.library.android_element import *
import allure
import time 
from common_lib.Custom_Markers import add_markers,zcc_mark_version #Required!
from OS_Android.test_suite.pytest_android import conftest

print("test_toggle")
class TestToggleZia:
    

    def setup_class(self):
        self.zcc_obj=conftest.zcc
        self.zcc_obj.explicit_wait(AndroidElements.ZIA_ON_STATUS)

    @pytest.mark.xray("QA-228904")
    @allure.title("toggle disable zia on zcc")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.0)
    
    def test_disable_zia(self):
        result=self.zcc_obj.toggle_service(service="ZIA",action='off')
        assert result[0],result[1]

    
    @allure.title("toggle enable zia on zcc")
    def test_enable_zia(self):
        result=self.zcc_obj.toggle_service(service="ZIA",action='on')
        assert result[0],result[1]
    
    @allure.title("teardown")
    def teardown_class(self):
        self.zcc_obj.logger.info("Nothing to do in teardown")
    #     self.zcc_obj.Logout()
        




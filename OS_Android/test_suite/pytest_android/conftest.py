import json,os
from common_lib.mobileadmin.appprofile import AppProfile
from common_lib.mobileadmin.forwardingprofile import ForwardingProfile
from common_lib.common import constants , logger
from OS_Android.library import ui_zcc
import pytest
from common_lib.common.log_ops import *
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
from common_lib.adminzpa.zpapolicy import ZpaPolicy
from OS_Android.library.ops_system import SysOps

config_path = os.path.join(os.getcwd(),"config", os.environ.get("CONFIG_FILE"))
with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)    
logger_obj = logger.Logger.initialize_logger(log_file_name="android_poc.log",log_level="DEBUG")
zcc=ui_zcc.Zcc(start_app=True,log_handle=logger_obj)
app_profile_obj = AppProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
forwarding_profile_obj=ForwardingProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
const_obj=constants.Utils()
export_logs=FetchAndroidLogs(logger_obj)
log_obj=LogOps(log_handle=logger_obj)
zpa=ZpaPolicy(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)
sys_ops=SysOps(logger_obj,variables["CLOUD"])

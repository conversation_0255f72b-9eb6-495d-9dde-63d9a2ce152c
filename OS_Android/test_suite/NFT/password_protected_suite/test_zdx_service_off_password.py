import allure
from OS_Android.test_suite.NFT.password_protected_suite import conftest
from common_lib.Custom_Markers import add_markers
from common_lib.helper_function import help_return
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.log_ops import *
import pytest
from scapy.all import *
logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")


class TestZdxPasswordProtect:

    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc = Zcc(start_app=True,log_handle=logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.trusted_network = conftest.trusted_network
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.service_ent = conftest.service_ent
        self.device_overview = conftest.device_overview
        self.trusted_network = conftest.trusted_network

        result = self.app_profile.edit_app_profile(operating_system='Android', zdx_disable_password = 'Zscaler@123', zpa_disable_password= 'Zscaler@123')
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.data = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        self.log_obj.logger.info(f"Device details is {self.data}")
        self.otp = self.data[2]['deviceOtpArray'][6]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]


    @allure.title("Validate while turning off the ZDX , It should ask for the password., "
                  " Validate After clicking the Turn Off Button, Turn On button should be working properly.")
    @pytest.mark.xray(["QA-250405", "QA-250408"])
    @add_markers("regression", "P0")
    def test_zdx_service_off_password(self,setup_teardown):
        """
        This function tests the service off password for ZDX.
        It performs the following steps:
        1. Doing validation zcc app service off password for ZDX.
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        
        self.zcc.logger.info("Turn off ZDX service")
        result = self.zcc.toggle_service(service='ZDX', action=False, password = 'Zscaler@123')
        assert result[0], result[1]

        self.zcc.logger.info("Verify zdx service status")
        result = self.zcc.verify_service_status(service_type='ZDX', service_status='off')
        assert result[0], result[1]

        self.zcc.logger.info("Turn on ZDX service")
        result = self.zcc.toggle_service(service='ZDX', action=True, )
        assert result[0], result[1]

        self.zcc.logger.info("Verify zdx service is on")
        result = self.zcc.verify_service_status(service_type='ZDX', service_status='on')
        assert result[0], result[1]

    @allure.title("Validate Error message when entered wrong password.")
    @pytest.mark.xray("QA-250415")
    @add_markers("regression", "P1")
    def test_zdx_service_incorrect_password(self, setup_teardown):
        """
        This function tests the service off password for ZDX.
        It performs the following steps:
        1. Doing validation zcc app service off password for ZDX with incorrect password.
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.zcc.zdx_disable_incorrect_password('incorrect@123')
        assert result[0], result[1]

    @allure.title("Validate after Disabling ZDX from Service entitlement And re-enabling it , ZDX password should come while Turning OFF")
    @pytest.mark.xray("QA-250409")
    @add_markers("regression", "P2")
    def test_zdx_service_entitlement(self,setup_teardown):
        """
        This function tests the service off password for ZDX.
        It performs the following steps:
        1. Toggling service from MA
        2. Doing validation zcc app service off password for ZDX.
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.service_ent.toggle_zdx(action=False)
        assert result[0], result[1]

        self.zcc.logger.info("Update policy")
        self.zcc.update_policy()

        result = self.service_ent.toggle_zdx(action=True)
        assert result[0], result[1]

        self.zcc.logger.info("Update policy")
        self.zcc.update_policy()
        self.zcc.logger.info("Validate zdx is enabled on zcc client")

        self.zcc.logger.info("Turn off ZDX service")
        self.zcc.toggle_service(service='ZDX', action=False, password ='Zscaler@123')

        self.zcc.logger.info("Verify zdx service status")
        result = self.zcc.verify_service_status(service_type='ZDX', service_status='off')
        assert result[0], result[1]

        self.zcc.logger.info("Turn on ZDX service")
        result = self.zcc.toggle_service(service='ZDX', action=True, )
        assert result[0], result[1]

        self.zcc.logger.info("Verify zdx service is on")
        result = self.zcc.verify_service_status(service_type='ZDX', service_status='on')
        assert result[0], result[1]

    @allure.title("Validate ZIA disable password shouldn't work for ZDX")
    @pytest.mark.xray("QA-250413")
    @add_markers("regression", "P1")
    def test_zdx_service_zia_disable(self,setup_teardown):
        """
        This function tests the service off using password for ZDX.
        It performs the following steps:
        1. Doing validation zcc app service off using OTP for ZDX.
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.app_profile.delete_app_profile(operating_system='Android')
        assert result[0], result[1]

        result = self.app_profile.create_app_profile(operating_system='Android')
        assert result[0], result[1]

        result = self.app_profile.edit_app_profile(operating_system='Android', zia_disable_password='ziapassword',
                                                   zdx_disable_password='Zscaler@123')
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(3)

        self.zcc.logger.info("Turn off ZDX service without password")
        result = self.zcc.zdx_disable_incorrect_password(password='ziapassword')
        assert result[0], result[1]

        self.zcc.logger.info("Verify zdx service is on")
        result = self.zcc.verify_service_status(service_type='ZDX', service_status='on')
        assert result[0], result[1]

        result = self.app_profile.delete_app_profile(operating_system='Android')
        assert result[0], result[1]

        result = self.app_profile.create_app_profile(operating_system='Android')
        assert result[0], result[1]

        result = self.app_profile.edit_app_profile(operating_system='Android', zdx_disable_password ='Zscaler@123',
                                                   zpa_disable_password= 'Zscaler@123')
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(4)

    @allure.title("Validate on Updating the password for Turn off ZDX service in App profile, proper configuration should be saved.")
    @pytest.mark.xray("QA-250407")
    @add_markers("regression", "P1")
    def test_zdx_password_update(self,setup_teardown):
        """
        This function tests the service off using password for ZDX.
        It performs the following steps:
        1. Update policy
        2. Doing validation zcc app service off using OTP for ZDX.
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.app_profile.edit_app_profile(operating_system='Android')
        assert result[0], result[1]

        result = self.app_profile.edit_app_profile(operating_system='Android', zdx_disable_password='Check@123')
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(3)

        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(3)

        result = self.zcc.toggle_service(service='ZDX', action=False, password ='Check@123')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_type='ZDX', service_status='off')
        assert result[0], result[1]

        result = self.zcc.toggle_service(service='ZDX', action=True, )
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_type='ZDX', service_status='on')
        assert result[0], result[1]

        result = self.app_profile.edit_app_profile(operating_system='Android')
        assert result[0], result[1]

        result = self.app_profile.edit_app_profile(operating_system='Android', zdx_disable_password='Zscaler@123')
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(3)

    @allure.title("Verify in ZCC/ZDX logs, this password isn't printed")
    @pytest.mark.xray("QA-250419")
    @add_markers("regression", "P0")
    def test_zdx_service_off_password_not_printed(self,setup_teardown):
        """
        This function tests the service off using password for ZDX.
        It performs the following steps:

        1. Doing validation zcc app service off using OTP for ZDX.
        2. Verifying password is ont printed in zcc logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Turn off ZDX service")
        result = self.zcc.toggle_service(service='ZDX', action=False, password ='Zscaler@123')
        assert result[0], result[1]

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.zcc.logger.info("Verify zdx service status")
        result = self.zcc.verify_service_status(service_type='ZDX', service_status='off')
        assert result[0], result[1]

        self.zcc.logger.info("Turn on ZDX service")
        result = self.zcc.toggle_service(service='ZDX', action=True )
        assert result[0], result[1]

        self.zcc.logger.info("Verify zdx service is on")
        result = self.zcc.verify_service_status(service_type='ZDX', service_status='on')
        assert result[0], result[1]

        result =  help_return(self.log_obj.search_log_file(file="ZCC_Android", words_to_find_in_line="Zscaler@123",failure_expected=True,
                                     directory=self.const_obj.LOG_PATH))

        assert result[0], result[1]

    @allure.title("Validate OTP for ZDX disable password")
    @pytest.mark.xray("QA-250411")
    @add_markers("regression", "P0")
    def test_zdx_service_off_otp(self,setup_teardown):
        """
        This function tests the service off using OTP for ZDX.
        It performs the following steps:
        1. Doing validation zcc app service off using OTP for ZDX.
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info("Turn off ZDX service")
        result = self.zcc.toggle_service(service='ZDX', action=False, password=self.otp)
        assert result[0], result[1]

        self.zcc.logger.info("Verify zdx service status")
        result = self.zcc.verify_service_status(service_type='ZDX', service_status='off')
        assert result[0], result[1]

        self.zcc.logger.info("Turn on ZDX service")
        self.zcc.toggle_service(service='ZDX', action=True, )

        self.zcc.logger.info("Verify zdx service is on")
        result = self.zcc.verify_service_status(service_type='ZDX', service_status='on')
        assert result[0], result[1]

    @allure.title("Validate on disabling again with same OTP, it should fail")
    @pytest.mark.xray("QA-250412")
    @add_markers("regression", "P0")
    def test_zdx_service_off_otp_reused(self,setup_teardown):
        """
        This function tests the service off re-using OTP for ZDX.
        It performs the following steps:
        1. Doing validation zcc app service off re-using OTP for ZDX.
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        result = self.zcc.zdx_disable_incorrect_password(self.otp)
        assert result[0], result[1]


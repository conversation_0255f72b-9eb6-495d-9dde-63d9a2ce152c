import allure
from OS_Android.test_suite.NFT.password_protected_suite import conftest
from common_lib.Custom_Markers import add_markers
from common_lib.helper_function import help_return
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.log_ops import *

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")
import pytest

class TestZpaPasswordProtect:

    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc = Zcc(start_app=True,log_handle=logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.trusted_network = conftest.trusted_network
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.service_ent = conftest.service_ent
        self.device_overview = conftest.device_overview
        self.trusted_network = conftest.trusted_network


        result = self.app_profile.edit_app_profile(operating_system='Android', zdx_disable_password = 'Zscaler@123', zpa_disable_password= 'Zscaler@123')
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.data = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        self.log_obj.logger.info(f"Device details is {self.data}")
        self.otp = self.data[2]['deviceOtpArray'][5]
        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

    @allure.title("Validate while turning off the ZDX , It should ask for the password.")
    @pytest.mark.xray("QA-269990")
    @add_markers("regression","P0")
    def test_zpa_service_off_password(self, setup_teardown):
        """
        This function tests the service off password for ZDX.
        It performs the following steps:
        1. Doing validation zcc app service off password for ZDX.
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info("Turn off ZPA service")
        result = self.zcc.toggle_service(service='ZPA', action=False, password = 'Zscaler@123')
        assert result[0], result[1]

        self.zcc.logger.info("Verify ZPA service status")
        result = self.zcc.verify_service_status(service_type='ZPA', service_status='off')
        assert result[0], result[1]

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.zcc.logger.info("Turn on ZPA service")
        result = self.zcc.toggle_service(service='ZPA', action=True )
        assert result[0], result[1]

        self.zcc.logger.info("Verify ZPA service is on")
        result = self.zcc.verify_service_status(service_type='ZPA', service_status='on')
        assert result[0], result[1]

        result = help_return(self.log_obj.search_log_file(file="ZCC_Android", words_to_find_in_line="password matched",failure_expected=False,
                                     directory=self.const_obj.LOG_PATH))

        assert result[0], result[1]

    @allure.title("Validate after Disabling ZPA from Service entitlement And re-enabling it , ZPA password should come while Turning OFF")
    @pytest.mark.xray("QA-270001")
    @add_markers("regression","P1")
    def test_zpa_service_entitlement(self, setup_teardown):
        """
        This function tests the service off password for ZPA.
        It performs the following steps:
        1. Toggling service from MA
        2. Doing validation zcc app service off password for ZPA.
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.service_ent.toggle_zpa(action=False)
        assert result[0], result[1]

        self.zcc.logger.info("Update policy")
        self.zcc.update_policy()

        result = self.service_ent.toggle_zpa(action=True)
        assert result[0], result[1]

        self.zcc.logger.info("Update policy")
        self.zcc.update_policy()
        self.zcc.logger.info("Validate ZPA is enabled on zcc client")
        result = self.zcc.zpa_re_authentication(user_id=conftest.variables["ZPA_USER_ID"],
                                  user_password=conftest.variables["ZPA_USER_PASSWORD"], )

        result = self.zcc.verify_service_status(service_type='ZPA',service_status='on' )
        assert result[0], result[1]
        self.zcc.logger.info("Turn off ZPA service")
        self.zcc.toggle_service(service='ZPA', action=False, password ='Zscaler@123')

        self.zcc.logger.info("Verify ZPA service status")
        result = self.zcc.verify_service_status(service_type='ZPA', service_status='off')
        assert result[0], result[1]

        self.zcc.logger.info("Turn on ZPA service")
        result = self.zcc.toggle_service(service='ZPA', action=True, )
        assert result[0], result[1]

        self.zcc.logger.info("Verify zdx service is on")
        result = self.zcc.verify_service_status(service_type='ZPA', service_status='on')
        assert result[0], result[1]

    @allure.title("Validate Error message when entered wrong password.")
    @pytest.mark.xray("QA-269996")
    @add_markers("regression", "P1")
    def test_zpa_service_incorrect_password(self, setup_teardown):
        """
        This function tests the service off password for ZDX.
        It performs the following steps:
        1. Doing validation zcc app service off password for ZDX with incorrect password.
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.zcc.zpa_disable_incorrect_password('zscaler@123456789')
        assert result[0], result[1]


    @allure.title("Validate OTP for ZPA disable password")
    @pytest.mark.xray("QA-269993")
    @add_markers("regression","P0")
    def test_zpa_service_off_otp(self,setup_teardown):
        """
        This function tests the service off using OTP for ZPA.
        It performs the following steps:
        1. Doing validation zcc app service off using OTP for ZPA.
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info("Turn off ZPA service")
        result = self.zcc.toggle_service(service='ZPA', action=False, password = self.otp)
        assert result[0], result[1]

        self.zcc.logger.info("Verify zpa service status")
        result = self.zcc.verify_service_status(service_type='ZPA', service_status='off')
        assert result[0], result[1]

        self.zcc.logger.info("Turn on zpa service")
        self.zcc.toggle_service(service='ZPA', action=True, )

        self.zcc.logger.info("Verify zpa service is on")
        result = self.zcc.verify_service_status(service_type='ZPA', service_status='on')
        assert result[0], result[1]

    @allure.title("Validate on disabling again with same OTP, it should fail")
    @pytest.mark.xray("QA-269994")
    @add_markers("regression", "P0")
    def test_zpa_service_off_otp_reused(self,setup_teardown):
        """
        This function tests the service off re-using OTP for ZPA.
        It performs the following steps:
        1. Doing validation zcc app service off re-using OTP for ZPA.
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        result = self.zcc.zpa_disable_incorrect_password(self.otp)
        assert result[0], result[1]



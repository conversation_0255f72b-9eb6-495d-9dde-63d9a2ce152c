import allure
from OS_Android.test_suite.NFT.zia_posture_profile import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.ui_zcc import Zcc
from common_lib.helper_function import help_return
import pytest
logger_obj = conftest.logger_obj


class TestZiaPostureProfile:
    @allure.title("Setup Method")
    def setup_class(self):
        """
          This function performs the setup process for the test class.
          It includes the following steps:
          1. Creation of forward profile and app profile
          2. Logout zcc and clear logs
          3. Login to ZCC
          Args:
              self (object): Instance of the test class.
        Returns:
              None
          Raises:
              AssertionError: If any of the steps fail.

        """

        self.zcc = Zcc(start_app=True,log_handle=logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.device_posture = conftest.device_posture
        self.zia_posture = conftest.zia_posture
        self.device_overview = conftest.device_overview
        self.log_handler = conftest.log_handler

        self.tray_file = self.log_handler.get_log_file_name(plugin_name="tray")
        assert self.tray_file[0], self.tray_file[0]
        self.tray_file = self.tray_file[1]

        self.tunnel_file = self.log_handler.get_log_file_name(plugin_name="tunnel")
        assert self.tunnel_file[0], self.tunnel_file[0]
        self.tunnel_file = self.tunnel_file[1]

        self.zia_posture.delete_zia_posture_profile(profile_name="ZIAPosture")

        self.zcc.logger.info("Creating Device Posture for High Risk Condition")
        result = self.device_posture.create_disk_enc_dp(posture_name="u_high")
        assert result[0], result[1]

        self.zcc.logger.info("Creating Device Posture for Medium Risk Condition")
        result = self.device_posture.create_disk_enc_dp(posture_name="u_med")
        assert result[0], result[1]

        self.zcc.logger.info("Creating Device Posture for Low Risk Condition")
        result = self.device_posture.create_disk_enc_dp(posture_name="u_low")
        assert result[0], result[1]

        self.zcc.logger.info("Creating ZIA Posture Profile")
        result = self.zia_posture.create_zia_posture_profile(high_risk_condition="u_high", med_risk_condition="u_med",
                                                             low_risk_condition="u_low", profile_name="ZIAPosture") #

        assert result[0], result[1]

    @allure.title("Device Trust Level under enrollment details")
    @pytest.mark.xray("QA-317954")
    @add_markers("regression")
    def test_device_trust_level_under_enrollment_details(self):
        """
        The Function checks the details of [deviceTrust] in Enrolled devices section
        It performs the following tests :-

        1.) Check the [deviceTrust] status under enrolled devices

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Editing ZIA Posture Profile")
        result = self.zia_posture.edit_zia_posture_profile(high_risk_condition=None, low_risk_condition=None,
                                                           med_risk_condition="u_low",profile_name="ZIAPosture")

        assert result[0], result[1]

        self.zcc.logger.info("Editing App Profile")
        result = self.app_profile.edit_app_profile(zia_posture="ZIAPosture")
        assert result[0], result[1]

        self.zcc.logger.info("Verifying Update policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("verifying [deviceTrust] from Device Details")
        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[2]['deviceTrust']

    @allure.title("Try creating duplicate ZIA posture profiles")
    @pytest.mark.xray("QA-317948")
    @add_markers("sanity","regression")
    def test_try_creating_duplicate_posture_profile(self):
        """
        The Function tries to make the duplicate ZIA posture Profile
        It performs the following steps:

        1.) Tries to create duplicate ZIA posture profile

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Creating duplicate ZIA Posture Profile")
        result = self.zia_posture.create_zia_posture_profile(high_risk_condition="u_high",med_risk_condition="u_med",
                                                             low_risk_condition="u_low", profile_name="ZIAPosture")

        assert result[0] is False

    @allure.title("Configure ZIA posture profile policy with Low and Medium Risk Types")
    @pytest.mark.xray("QA-317905")
    @add_markers("regression")
    def test_low_and_medium_risk(self):
        """
        The Function creates ZIA Posture Profile with low and medium risk
        It preforms the following steps:-

        1.) Create ZIA posture profiles with low and medium risk

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Editing ZIA Posture Profile")
        result = self.zia_posture.edit_zia_posture_profile(med_risk_condition="u_med",low_risk_condition="u_low",
                                                           profile_name="ZIAPosture",high_risk_condition='')

        assert result[0], result[1]

        self.zcc.logger.info("Verifying Update policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Verifying Export Logs")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Verifying Export Logs read is true or not")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.zcc.logger.info("Verifying log present in the files")
        result = help_return(conftest.log_ops.search_log_file, file=self.tray_file,
                             words_to_find_in_line="Found Trust type as MEDIUM",
                             directory=conftest.const_obj.LOG_PATH)

        assert result[0], result[1]

    @allure.title("Configure ZIA posture profile policy with Low and High Risk Types")
    @pytest.mark.xray("QA-317904")
    @add_markers("sanity","regression")
    def test_configure_low_and_high_risk(self):
        """
        The Function creates ZIA Posture Profile with low and high risk
        It preforms the following steps:-

        1.) Create ZIA posture profiles with low and high risk

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info("Editing ZIA Posture Profile")
        result = self.zia_posture.edit_zia_posture_profile(high_risk_condition="u_high", med_risk_condition='',
                                                           low_risk_condition="u_low", profile_name="ZIAPosture")

        assert result[0], result[1]

        self.zcc.logger.info("Verifying Update Policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("verifying Export Logs")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Verifying Export Logs read is true or not")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.zcc.logger.info("Verifying log present in the files")
        result = help_return(conftest.log_ops.search_log_file, file=self.tray_file,
                             words_to_find_in_line="Found Trust type as HIGH",
                             directory=conftest.const_obj.LOG_PATH)

        assert result[0], result[1]

        self.zcc.logger.info("Verifying Clear Logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

    @allure.title("Configure ZIA posture profile policy with High Risk Type only")
    @pytest.mark.xray("QA-317903")
    @add_markers("sanity","regression")
    def test_configure_high_risk_type_only(self):
        """
        The Function creates ZIA Posture Profile with high risk type only
        It preforms the following steps:-

        1.) Create ZIA posture profiles with high risk type only

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Editing ZIA Posture Profile")
        result = self.zia_posture.edit_zia_posture_profile(high_risk_condition="u_high", profile_name="ZIAPosture",
                                                           med_risk_condition='',low_risk_condition='')

        assert result[0], result[1]

        self.zcc.logger.info("Verifying Update policy")
        result = self.zcc.update_policy()
        assert result[0],result[1]

        self.zcc.logger.info("verifying Export Logs")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Verifying Export Logs read is true or not")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.zcc.logger.info("Verifying log present in the files")
        result = help_return(conftest.log_ops.search_log_file, file=self.tray_file,
                             words_to_find_in_line="Found Trust type as HIGH",
                             directory=conftest.const_obj.LOG_PATH)

        assert result[0], result[1]

        self.zcc.logger.info("Verifying Clear Logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

    @allure.title("Configure ZIA posture profile policy with Medium Risk Type only")
    @pytest.mark.xray("QA-317902")
    @add_markers("sanity","regression")
    def test_zia_posture_with_medium_risk_type_only(self):
        """
        The Function creates ZIA Posture Profile with medium risk only
        It preforms the following steps:-

        1.) Create ZIA posture profiles with  medium risk only

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Editing ZIA Posture Profile")
        result = self.zia_posture.edit_zia_posture_profile(med_risk_condition="u_med", profile_name="ZIAPosture",
                                                           high_risk_condition='', low_risk_condition='')
        assert result[0], result[1]

        self.zcc.logger.info("Verifying Update policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Verifying Export Logs")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Verifying Export Logs read is true or not")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.zcc.logger.info("Verifying log present in the files")
        result = help_return(conftest.log_ops.search_log_file, file=self.tray_file,
                             words_to_find_in_line="Found Trust type as MEDIUM",
                             directory=conftest.const_obj.LOG_PATH)

        assert result[0], result[1]

        self.zcc.logger.info("Verifying Clear Logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

    @allure.title("Configure ZIA posture profile policy with Low Risk Type only")
    @pytest.mark.xray("QA-317901")
    @add_markers("sanity","regression")
    def test_zia_posture_with_low_risk_type_only(self):
        """
        The Function creates ZIA Posture Profile with low risk type only
        It preforms the following steps:-

        1.) Create ZIA posture profiles with low risk type only

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info("Editing ZIA Posture Profile")
        result = self.zia_posture.edit_zia_posture_profile(low_risk_condition="u_low", profile_name="ZIAPosture",
                                                           high_risk_condition='',med_risk_condition='')

        assert result[0], result[1]

        self.zcc.logger.info("Verifying Update policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Verifying Export Logs")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Verifying Export Logs read is true or not")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.zcc.logger.info("Verifying log present in the files")
        result = help_return(conftest.log_ops.search_log_file, file=self.tray_file,
                             words_to_find_in_line="Found Trust type as LOW",
                             directory=conftest.const_obj.LOG_PATH)

        assert result[0], result[1]

    @allure.title("Teardown class")
    def teardown_class(self):
        """
        Using this for deleting ZIA Posture Profile and its device posture

        Returns:
            None
        Raises:
            None
        """
        # Deleting ZIA Posture Profile
        self.zia_posture.delete_zia_posture_profile(profile_name="ZIAPosture")

        # Deleting Device Postures
        self.device_posture.delete_device_posture(delete_all_postures=True)

import pytest,allure
from common_lib.Custom_Markers import add_markers
from OS_Android.test_suite.NFT.disable_reason_suit import conftest
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.log_ops import *

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")

class TestZdxDisableReason:

    @allure.title("Setup Method")
    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc = Zcc(start_app=True,log_handle=logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.device_overview = conftest.device_overview
        self.service_ent = conftest.service_ent
        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]

    @allure.title("Validate disable reason provided by user is getting saved and send as part of keep-alive"
                  "Validate under enrollmet devices, disable reason is available corresponding to this device"
                  "Validate on turning off ZPA when disable password isn't set, disable reason dialog box should prompt"
                  "Validate backward compatibility i.e existing app shouldn't crash because of this.")
    @add_markers("regression", "P0")
    @pytest.mark.xray(["QA-277996","QA-277997","QA-277998","QA-278007"])
    def test_zdx_disable_reason(self, setup_teardown):
        """
        This function tests the service off password for ZDX.
        It performs the following steps:
        1. Doing validation Service disablement after giving password
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        result = self.zcc.disable_reason(service='ZDX', reason='no reason')
        assert result[0], result[1]

        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)

        assert result[0], result[1]
        assert result[2]['serviceDisableReasonList'][-1]['reason'] == 'no reason'

        self.zcc.logger.info("Verify status for ZDX is off")
        result = self.zcc.verify_service_status(service_status='off', service_type='ZDX')
        assert result[0], result[1]


        result = self.zcc.toggle_service(service='ZDX', action=True)
        assert result[0], result[1]

        time.sleep(20)

        self.zcc.logger.info("Verify status for ZDX is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]

    @allure.title("Validate on enabling and disabling again, disable reason dialog box should prompt")
    @add_markers("regression")
    @pytest.mark.xray("QA-277998", "P1")
    def test_zdx_dis_en_dis(self, setup_teardown):
        """
        This function tests the service off password for ZIA.
        It performs the following steps:
        1. Enable and disable config from MA
        2. Doing validation Service disablement after giving password

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.zcc.disable_reason(service='ZDX', reason='no reason')
        assert result[0], result[1]

        self.zcc.logger.info("Verify status for ZDX is off")
        result = self.zcc.verify_service_status(service_status='off', service_type='ZDX')
        assert result[0], result[1]

        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[1]
        assert result[2]['serviceDisableReasonList'][-1]['reason'] == 'no reason'

        result = self.zcc.toggle_service(service='ZDX', action=True)
        assert result[0], result[1]

        result = self.zcc.disable_reason(service='ZDX', reason='no reason')
        assert result[0], result[1]

        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[1]
        assert result[2]['serviceDisableReasonList'][-1]['reason'] == 'no reason'

        result = self.zcc.toggle_service(service='ZDX', action=True)
        assert result[0], result[1]

        self.zcc.logger.info("Verify status for ZDX is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]

    @allure.title("Verify proper timestamps corresponding to the disable reasons")
    @add_markers("regression","P1")
    @pytest.mark.xray("QA-278009")
    def test_zdx_disable_time(self, setup_teardown):
        """
        This function tests the service off password for ZIA.
        It performs the following steps:

        1) Doing validation Service disablement after giving password
        2) Verifying time stamp

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        result = self.zcc.disable_reason(service='ZDX', reason='no reason')
        assert result[0], result[1]

        self.zcc.logger.info("Verify status for ZDX is off")
        result = self.zcc.verify_service_status(service_status='off', service_type='ZDX')
        assert result[0], result[1]

        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[1]
        disable__time = (result[2]['serviceDisableReasonList'][-1]['disableTime'])
        assert disable__time is not None

        result = self.zcc.toggle_service(service='ZDX', action=True)
        assert result[0], result[1]

        self.zcc.logger.info("Verify status for ZDX is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]

    @allure.title("Verify maximum number of disable reasons can be shown on MA")
    @add_markers("regression", "P1")
    @pytest.mark.xray("QA-278011")
    def test_zdx_max_reason(self, setup_teardown):
        """
        This function tests the service off password for ZIA.
        It performs the following steps:

        1. Doing validation Service disablement after giving password of max length

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        for i in range(6):
            result = self.zcc.disable_reason(service='ZDX', reason='no reason')
            assert result[0], result[1]

            self.zcc.logger.info("Verify status for ZDX is off")
            result = self.zcc.verify_service_status(service_status='off', service_type='ZDX')
            assert result[0], result[1]

            result = self.zcc.toggle_service(service='ZDX', action=True)
            assert result[0], result[1]

            self.zcc.logger.info("Verify status for ZDX is on")
            result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
            assert result[0], result[1]

        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[1]
        count = len(result[2]['serviceDisableReasonList'])
        assert count == 5

    @allure.title("Validate disable reason shouldn't prompt for logout and Uninstall")
    @add_markers("regression", "P0")
    @pytest.mark.xray("QA-278005")
    def test_zdx_logout(self, setup_teardown):
        """
        This function tests the service off password for ZIA.
        It performs the following steps:

        1. Doing validation Service disablement message should come for logout

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Logout from zcc and check no password asked")
        result = self.zcc.logout()
        assert result[0], result[1]

        self.zcc.logger.info("login in to zcc")
        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(10)


    @allure.title("Validate Disable reason char limit For ZDX")
    @add_markers("regression", "P1")
    @pytest.mark.xray("QA-278001")
    def test_zdx_disable_reason_max(self):
        """
        This function tests the service off password for ZIA.
        It performs the following steps:

        1. Doing validation Service disablement after giving password of more then max length

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        reason = 210 * 'a'
        reason_verify = 200 * 'a'

        result = self.zcc.disable_reason(service='ZDX', reason=reason)
        assert result[0], result[1]

        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        reason_array = result[2]['serviceDisableReasonList']
        status = False
        for i in range(len(reason_array)):
            if result[2]['serviceDisableReasonList'][i]['reason'] == reason_verify:
                status = True
                break
        assert status, "Disable reason is not correct"

        result = self.zcc.toggle_service(service='ZDX', action=True)
        assert result[0], result[1]

        self.zcc.logger.info("Verify status for ZDX is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]


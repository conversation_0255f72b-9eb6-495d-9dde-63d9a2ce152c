import pytest,allure
from OS_Android.test_suite.NFT.mix_nft import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.log_ops import *
logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")

large_comment = 'a' * 250
issue_priority = [pytest.param("Low", marks=pytest.mark.xray("QA-266863")),
                  pytest.param("Medium", marks=pytest.mark.xray("QA-266864")),
                  pytest.param("High", marks=pytest.mark.xray("QA-266865")),
                  pytest.param("Urgent", marks=pytest.mark.xray("QA-266866")),
                  ]

issue_network_type = [pytest.param("Authentication", marks=pytest.mark.xray("QA-266858")),
                      pytest.param("User Interface", marks=pytest.mark.xray("QA-266859")),
                      pytest.param("Network Connectivity", marks=pytest.mark.xray("QA-266860")),
                      pytest.param("Zscaler Connect Availability", marks=pytest.mark.xray("QA-266861")),
                      pytest.param("Other", marks=pytest.mark.xray("QA-266862"))
                    ]

issue_comment = [
                pytest.param("", marks=pytest.mark.xray("QA-266867")),
                pytest.param(large_comment, marks=pytest.mark.xray("QA-266868")),
                pytest.param("abcded*@!", marks=pytest.mark.xray("QA-266869")),

                ]


class TestReportIssueFullTest:
    @allure.title("Setup Method")
    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc=Zcc(start_app=True,log_handle=logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.trusted_network = conftest.trusted_network
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.client_conn_support = conftest.client_conn_support
        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]
        result = conftest.client_conn_support.toggle_report_an_issue(action=True)
        assert result[0]

        result = self.zcc.update_policy()
        assert result[0], result[1]

    @allure.title("Validate report an issue with network type of zapp.")
    @pytest.mark.xray("QA-58327")
    @add_markers("regression")
    @pytest.mark.parametrize("issue_comment", issue_comment)
    def test_report_issue_comment(self,issue_comment, setup_teardown):
        """
        This function tests the validation of comment of report an issue.
        It performs the following steps:
        1. Doing validation of comments of issue
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        result = self.zcc.report_issue(issue_comment=issue_comment)
        assert result[0], result[1]

    @allure.title("Validate report an issue priority feild functionality of zapp.")
    @pytest.mark.xray("QA-58327")
    @add_markers("regression")
    @pytest.mark.parametrize("priority", issue_priority)
    def test_report_issue_priority(self, priority, setup_teardown):
        """
        This function tests the validation of priority  of report an issue..
        It performs the following steps:
        1. Doing validation of priority of issue
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.zcc.report_issue(issue_priority=priority)
        assert result[0], result[1]

    @allure.title("Validate report an issue with network type of zapp.")
    @pytest.mark.xray("QA-58327")
    @add_markers("regression")
    @pytest.mark.parametrize("issue_type", issue_network_type)
    def test_report_issue_type(self, issue_type, setup_teardown):
        """
        This function tests the validation of issue type of report an issue.
        It performs the following steps:
        1. Doing validation of type of issue
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        result = self.zcc.report_issue(issue_type=issue_type)
        assert result[0], result[1]



    @allure.title("Verify when Report an issue is enabled from MA")
    @pytest.mark.xray("QA-266852", "QA-266853", "QA-266873", "QA-266870")
    @add_markers("regression")
    def test_report_issue(self, setup_teardown):
        """
        This function tests the validation of single email of report an issue.
        It performs the following steps:
        1. Doing validation of single email of issue
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        result = self.client_conn_support.toggle_report_an_issue(action= True)
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        result = self.zcc.report_issue(issue_comment="")
        assert result[0], result[1]

    @allure.title("Verify report an issue when multiple emails are set on MA")
    @pytest.mark.xray("QA-266854")
    @add_markers("regression")
    def test_report_multiple_email(self, setup_teardown):
        """
        This function tests the validation of multiple email of report an issue.
        It performs the following steps:
        1. Doing validation of multiple email of issue
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        result = self.client_conn_support.toggle_report_an_issue(action= True   , email_id="<EMAIL>,<EMAIL>")
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        result = self.zcc.report_issue()
        assert result[0], result[1]

        result = self.client_conn_support.toggle_report_an_issue(action= True)
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

    @allure.title("Verify report an issue when single cc email is set on MA")
    @pytest.mark.xray("QA-266856")
    @add_markers("regression")
    def test_single_cc_email(self, setup_teardown):
        """
        This function tests the validation of cc email of report an issue.
        It performs the following steps:
        1. Doing validation of cc email of issue
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        result = self.zcc.report_issue(cc_email="<EMAIL>")
        assert result[0], result[1]

    @allure.title("Verify report an issue when multiple cc email is set on MA")
    @pytest.mark.xray("QA-266857")
    @add_markers("regression")
    def test_mutlitple_cc_email(self, setup_teardown):
        """
        This function tests the validation of multi cc email of report an issue.
        It performs the following steps:
        1. Doing validation of multiple cc email of issue
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        result = self.zcc.report_issue(cc_email="<EMAIL>" "," "<EMAIL>")
        assert result[0], result[1]

    @allure.title("Verify report an issue without name")
    @pytest.mark.xray("QA-266855")
    @add_markers("regression")
    def test_name(self,setup_teardown):
        """
        This function tests the validation of name of report an issue.
        It performs the following steps:
        1. Doing validation of name of issue
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        result = self.zcc.report_issue(name="")
        assert result[0], result[1]

    @allure.title("Verify when Report an issue is disabled from MA")
    @pytest.mark.xray("QA-266851")
    @add_markers("regression")
    def test_report_issue_disable(self, setup_teardown):
        """
        This function tests the validation of license.
        It performs the following steps:
        1. Doing validation zcc app for license
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        result = self.client_conn_support.toggle_report_an_issue(action=False)
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        result = self.zcc.verify_report_exists()
        assert result[0], result[1]

        result = self.client_conn_support.toggle_report_an_issue(action=True)
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]




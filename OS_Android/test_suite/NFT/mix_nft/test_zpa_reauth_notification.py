import pytest,allure
from OS_Android.test_suite.NFT.mix_nft import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.android_element import AndroidElements
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.log_ops import *
logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")


class TestZpaReAuthNotification:
    @allure.title("Setup Method")
    def setup_class(self):
        self.zcc=Zcc(start_app=True,log_handle=logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.device_overview = conftest.device_overview
        self.trusted_network = conftest.trusted_network
        self.client_notification = conftest.client_notification
        self.audit_logs = conftest.audit_logs
        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        result = self.client_notification.create_tray_notification(enable_notifications_zpa_reauth=True)
        assert result[0], result[1]

        result = conftest.client_conn_support.toggle_pre_populate_client_connector_username(using_javascript=False,
                                                                                            using_login_hint=False)
        assert result[0], result[1]

    @allure.title('Validate that Enable notification for ZPA reauth is getting disabled from mobile admin.')
    @add_markers("regression")
    @pytest.mark.xray("QA-257589")
    def test_kill_zapp(self, setup_teardown):

        result = self.client_notification.create_tray_notification(enable_notifications_zpa_reauth=True)
        assert result, result[1]

        result = self.client_notification.get_tray_notification()
        assert "'enableNotificationForZPAReauth': '1'" in result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(10)

        result = self.zcc.zpa_reauth_notification_status()
        assert result[0], result[1]
        result = self.zcc.kill_zcc()
        assert result[0], result[1]

        result = self.zcc.ui.start_app(app=AndroidElements.APP, sleep_time=10)

        result = self.zcc.zpa_reauth_notification_status()
        assert result[0], result[1]

        result = self.zcc.logout()
        assert result[0], result[1]


    @allure.title('Validate that Enable notification for ZPA reauth   is getting disabled from mobile admin.'
                  'Validate that on enabling this knob from Mobile admin and doing update policy , Show private access knob should be enabled in ZApp.'
                  'Validate that "Show ZPA Reauthentication Notification Every (In Minutes)" should have the proper toggle button'
                  'Validate "Show private access reauthentication notifications" knob should get enabled/disabled from ZApp UI'

                  )
    @add_markers("regression")
    @pytest.mark.xray(["QA-257579", "QA-257585", "QA-257582", "QA-257590"])
    def test_para_reauth(self, setup_teardown):

        result = self.client_notification.create_tray_notification(enable_notifications_zpa_reauth=True)
        assert result, result[1]

        result = self.client_notification.get_tray_notification()
        assert "'enableNotificationForZPAReauth': '1'" in result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(10)

        result = self.zcc.zpa_reauth_notification_status()
        assert result[0], result[1]

        # result = self.audit_logs.check_audit_logs()


        result = self.zcc.logout()
        assert result[0], result[1]

        result = self.client_notification.create_tray_notification(enable_notifications_zpa_reauth=False)
        assert result, result[1]

        result = self.client_notification.get_tray_notification()
        assert "'enableNotificationForZPAReauth': '0'" in result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(10)

        result = self.zcc.zpa_reauth_notification_status(check='false')
        assert result[0], result[1]

        result = self.zcc.logout()
        assert result[0], result[1]


    @allure.title('Validate the limit of "Show ZPA Reauthentication Notification Every (In Minutes)"'
                  'Validate that on enabling this knob from mobile admin below config should be available "Show  ZPA Reauthentication Notification Every (In Minutes)"')
    @add_markers("regression")
    @pytest.mark.xray(["QA-257583", "QA-257581"])
    def test_limit_zpa_reauth(self, setup_teardown):
        result = self.client_notification.create_tray_notification(enable_notifications_zpa_reauth=True, zpa_reauth_notification_time='2')
        assert result, result[1]

        result = self.client_notification.get_tray_notification()
        assert "'enableNotificationForZPAReauth': '1'" in result[1]
        assert "'zpaReauthNotificationTime': 2" in result[1]

        result = self.client_notification.create_tray_notification(zpa_reauth_notification_time='1440', enable_notifications_zpa_reauth=True)
        assert result, result[1]

        result = self.client_notification.get_tray_notification()
        assert "'enableNotificationForZPAReauth': '1'" in result[1]
        assert "'zpaReauthNotificationTime': 1440" in result[1]

        result = self.client_notification.create_tray_notification(enable_notifications_zpa_reauth=True, zpa_reauth_notification_time='2')

        result = self.client_notification.get_tray_notification()
        assert "'enableNotificationForZPAReauth': '1'" in result[1]
        assert "'zpaReauthNotificationTime': 2" in result[1]

        result = self.client_notification.create_tray_notification(enable_notifications_zpa_reauth=False)
        assert result, result[1]

        result = self.client_notification.get_tray_notification()
        assert "'enableNotificationForZPAReauth': '0'" in result[1]

    @allure.title('Validate the "Show Private Access reauthentication notification " knob should not change on Update Policy.')
    @add_markers("regression")
    @pytest.mark.xray("QA-257592")
    def test_knob_reauth(self):

        result = self.client_notification.create_tray_notification(enable_notifications_zpa_reauth=True)
        assert result, result[1]

        result = self.client_notification.get_tray_notification()
        assert "'enableNotificationForZPAReauth': '1'" in result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(10)

        result = self.zcc.zpa_reauth_notification_status()
        assert result[0], result[1]

        result = self.client_notification.create_tray_notification(enable_notifications_zpa_reauth=False)
        assert result, result[1]

        time.sleep(5)

        self.zcc.update_policy()

        result = self.client_notification.get_tray_notification()
        assert "'enableNotificationForZPAReauth': '0'" in result[1]

        result = self.zcc.zpa_reauth_notification_status()
        assert result[0], result[1]

        result = self.zcc.logout()
        assert result[0], result[1]



    @allure.title('Validate on disabling "Show Private Access reauthentication notifications" knob  ZPA reauth notifcation should have proper notifications..')
    @add_markers("regression")
    @pytest.mark.xray("QA-257591")
    def test_enable_disable_zpa_reauth(self,setup_teardown):

        result = self.client_notification.create_tray_notification(enable_notifications_zpa_reauth=True)
        assert result, result[1]

        result = self.client_notification.get_tray_notification()
        assert "'enableNotificationForZPAReauth': '1'" in result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(10)

        result = self.zcc.zpa_reauth_notification_status()
        assert result[0], result[1]


        result = self.zcc.logout()
        assert result[0], result[1]

        result = self.client_notification.create_tray_notification(enable_notifications_zpa_reauth=False)
        assert result, result[1]

        result = self.client_notification.get_tray_notification()
        assert "'enableNotificationForZPAReauth': '0'" in result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(10)

        result = self.zcc.zpa_reauth_notification_status(check='false')
        assert result[0], result[1]

        result = self.zcc.logout()
        assert result[0], result[1]





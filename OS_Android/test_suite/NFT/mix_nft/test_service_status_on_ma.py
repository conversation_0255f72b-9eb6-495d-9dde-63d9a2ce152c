import pytest,allure
from OS_Android.test_suite.NFT.mix_nft import conftest
from common_lib.Custom_Markers import add_markers
from common_lib.helper_function import help_return
from OS_Android.library.android_element import AndroidElements
from common_lib.common.log_ops import *
from OS_Android.library.ui_zcc import Zcc
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")

test_input = [
               pytest.param('ZIA', False,'off', marks=pytest.mark.xray("QA-256315", "QA-256318")),
               pytest.param('ZPA', False,'off', marks=pytest.mark.xray("QA-256316", "QA-256319")),
               pytest.param('ZDX', False,'off', marks=pytest.mark.xray("QA-256317", "QA-256320")),
            ]


test_input_service_ent = [

                pytest.param('ZPA', marks=pytest.mark.xray("QA-256321")),
                pytest.param('ZDX', marks=pytest.mark.xray("QA-256322")),
            ]


class TestServiceStatus:
    @allure.title("Setup Method")
    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.zcc=Zcc(start_app=True,log_handle=logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.device_overview = conftest.device_overview
        self.service_ent = conftest.service_ent
        self.export_logs=FetchAndroidLogs(logger_obj)
        self.log_obj = conftest.log_ops
        self.const_obj = conftest.const_obj
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = conftest.client_conn_support.toggle_pre_populate_client_connector_username(using_javascript=False,
                                                                                   using_login_hint=False)
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]


    @allure.title("Validate in logs the status of the ZIA/ZPA/ZDX")
    @add_markers("regression")
    @pytest.mark.xray("QA-256325")
    def test_service_log_verify(self, setup_teardown):

        """
        This function tests the validation of service on MA.
        It performs the following steps:
        1. Doing validation service status on MA and appinfo logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        device_data = result[2]

        assert device_data['ziaEnabled'] == 'true'
        assert device_data['zpaEnabled'] == 'true'
        assert device_data['zdxEnabled'] == 'true'

        assert device_data['ziaHealth'] == 'Active'
        assert device_data['zpaHealth'] == 'Active'
        assert device_data['zdxHealth'] == 'Active'

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]


        self.zcc.logger.info("Verifying service status and health check for all services")
        result = help_return(self.log_obj.search_log_file, file="AppInfo", words_to_find_in_line=['Proxy Enabled : true'],
                                                  directory=self.const_obj.LOG_PATH)
        assert result[0], result[1]

        result = help_return(self.log_obj.search_log_file, file="AppInfo", words_to_find_in_line=['ZPA Enabled : true'],
                                                  directory=self.const_obj.LOG_PATH)
        assert result[0], result[1]

        result = help_return(self.log_obj.search_log_file, file="AppInfo", words_to_find_in_line=['ZDX Enabled : true'],
                                                  directory=self.const_obj.LOG_PATH)
        assert result[0], result[1]

        result = help_return(self.log_obj.search_log_file, file="AppInfo", words_to_find_in_line=['Proxy State: TUNNEL_FORWARDING'],
                                                  directory=self.const_obj.LOG_PATH)
        assert result[0], result[1]

        result = help_return(self.log_obj.search_log_file, file="AppInfo", words_to_find_in_line=['ZPN State: TUNNEL_FORWARDING'],
                                                  directory=self.const_obj.LOG_PATH)
        assert result[0], result[1]

        result = help_return(self.log_obj.search_log_file, file="AppInfo", words_to_find_in_line=['ZDX state: ON'],
                                                  directory=self.const_obj.LOG_PATH)
        assert result[0], result[1]

    @allure.title("Validate the status of ZIA/ZPA/ZDX service on MA after login")
    @add_markers("regression")
    @pytest.mark.xray("QA-256314")
    def test_service_all(self, setup_teardown):
        """
        This function tests the validation of all service on MA.
        It performs the following steps:
        1. Doing validation service status on MA
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        device_data = result[2]

        assert device_data['ziaEnabled'] == 'true'
        assert device_data['zpaEnabled'] == 'true'
        assert device_data['zdxEnabled'] == 'true'

        assert device_data['ziaHealth'] == 'Active'
        assert device_data['zpaHealth'] == 'Active'
        assert device_data['zdxHealth'] == 'Active'

    @allure.title("Validate after the restart service the behaviour in the Device Details.")
    @add_markers("regression")
    @pytest.mark.xray("QA-256323")
    def test_service_restart(self, setup_teardown):
        """
        This function tests the validation of all service on MA.
        It performs the following steps:
        1. Doing validation service status on MA
        2. restart of service
        3. Doing validation of service after restart
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        device_data = result[2]

        assert device_data['ziaEnabled'] == 'true'
        assert device_data['zpaEnabled'] == 'true'
        assert device_data['zdxEnabled'] == 'true'

        assert device_data['ziaHealth'] == 'Active'
        assert device_data['zpaHealth'] == 'Active'
        assert device_data['zdxHealth'] == 'Active'

        result = self.zcc.restart_service()
        time.sleep(30)
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        device_data = result[2]


        assert device_data['ziaEnabled'] == 'true'
        assert device_data['zpaEnabled'] == 'true'
        assert device_data['zdxEnabled'] == 'true'

        assert device_data['ziaHealth'] == 'Active'
        assert device_data['zpaHealth'] == 'Active'
        assert device_data['zdxHealth'] == 'Active'

    @allure.title("Validate the behaviour after killing the app")
    @add_markers("regression")
    @pytest.mark.xray("QA-256324")
    def test_service_kill_app(self, setup_teardown):
        """
        This function tests the validation of all service on MA.
        It performs the following steps:
        1. Doing validation service status on MA
        2. Killing of zapp s
        3. Doing validation of service after restarting of app
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        device_data = result[2]

        assert device_data['ziaEnabled'] == 'true'
        assert device_data['zpaEnabled'] == 'true'
        assert device_data['zdxEnabled'] == 'true'

        assert device_data['ziaHealth'] == 'Active'
        assert device_data['zpaHealth'] == 'Active'
        assert device_data['zdxHealth'] == 'Active'

        self.zcc.logger.info("Step 2: Killing the ZApp")
        result = self.zcc.kill_zcc()
        assert result[0], result[1]
        time.sleep(1)

        self.zcc.logger.info("Step 3: Restarting the ZApp and verifying ZIA status")
        self.zcc.ui.start_app(app=AndroidElements.APP, sleep_time=10)


        self.zcc.logger.info("Update policy to send latest status to MA")
        result = self.zcc.update_policy()
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_type='ZIA')
        assert result[0], result[1]

        assert device_data['ziaEnabled'] == 'true'
        assert device_data['zpaEnabled'] == 'true'
        assert device_data['zdxEnabled'] == 'true'

        assert device_data['ziaHealth'] == 'Active'
        assert device_data['zpaHealth'] == 'Active'
        assert device_data['zdxHealth'] == 'Active'

    @allure.title("Validate the Status change on Turning Off the ZIA")
    @pytest.mark.parametrize("service, action, status", test_input)
    @add_markers("regression")
    def test_service_off(self, service, action, status, setup_teardown):
        """
        This function tests the validation of all service on MA.
        It performs the following steps:
        1. Doing validation service status on MA
        2. Turning off all service
        3. Doing validation of service after turning off service
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info("Switch off service")
        result = self.zcc.toggle_service(service=service, action=action)
        assert result[0], result[1]

        self.zcc.logger.info("Verify status for ZIA is off")
        result = self.zcc.verify_service_status(service_status=status, service_type=service)
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        device_data = result[2]

        if service == 'ZIA':
            assert device_data['ziaHealth'] == 'Inactive'
            assert device_data['ziaEnabled'] == 'true'

        elif service == 'ZPA':
            assert device_data['zpaHealth'] == 'Inactive'
            assert device_data['zpaEnabled'] == 'true'

        else:
            assert device_data['zdxHealth'] == 'Inactive'
            assert device_data['zdxEnabled'] == 'true'

        self.zcc.logger.info("Switch off service")
        self.zcc.toggle_service(service=service, action=True)

        time.sleep(10)
        self.zcc.logger.info("Verify status for ZIA is off")
        result = self.zcc.verify_service_status(service_status='on', service_type=service)
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        device_data = result[2]

        assert device_data['ziaEnabled'] == 'true'
        assert device_data['zpaEnabled'] == 'true'
        assert device_data['zdxEnabled'] == 'true'

        assert device_data['ziaHealth'] == 'Active'
        assert device_data['zpaHealth'] == 'Active'
        assert device_data['zdxHealth'] == 'Active'

    @allure.title("Validate ZPA service status on MA after enabling/disabling it from service entitlement .")
    @pytest.mark.parametrize("service", test_input_service_ent)
    @add_markers("regression")
    def test_service_entitlement(self, service, setup_teardown):
        """
        This function tests the validation of all service on MA.
        It performs the following steps:
        1. Doing validation service status on MA
        2. Disbale and enable service from MA
        3. Doing validation of service after service status change
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        device_data = result[2]

        assert device_data['ziaEnabled'] == 'true'
        assert device_data['zpaEnabled'] == 'true'
        assert device_data['zdxEnabled'] == 'true'

        assert device_data['ziaHealth'] == 'Active'
        assert device_data['zpaHealth'] == 'Active'
        assert device_data['zdxHealth'] == 'Active'

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        if service == 'ZDX':
            self.zcc.logger.info("Disable service from MA")
            result= self.service_ent.toggle_zdx(action=False)
            assert result[0], result[1]
            time.sleep(3)

            result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                    conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0], result[1]

            time.sleep(20)
            result = self.zcc.update_policy()
            assert result[0], result[1]

            result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
            device_data = result[2]

            assert device_data['zdxHealth'] == 'Inactive'
            assert device_data['zdxEnabled'] == 'false'

        elif service == 'ZPA':
            self.zcc.logger.info("Disable service from MA")
            result = self.service_ent.toggle_zpa(action=False)
            time.sleep(3)
            assert result[0], result[1]

            result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"], "", "")
            assert result[0], result[1]

            time.sleep(20)

            result = self.zcc.update_policy()
            assert result[0], result[1]

            result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
            device_data = result[2]
            assert device_data['zpaHealth'] == 'Inactive'
            assert device_data['zpaEnabled'] == 'false'

        if service == 'ZDX':
            logged_in = self.zcc.validate_zcc_logged_in()
            if logged_in[0]:
                self.zcc.logger.info("Logout from zcc")
                result = self.zcc.logout()
                assert result[0], result[1]

            self.zcc.logger.info("Enable service from MA")
            self.service_ent.toggle_zdx(action=True)
            time.sleep(3)
            assert result[0], result[1]

        elif service == 'ZPA':

            logged_in = self.zcc.validate_zcc_logged_in()
            if logged_in[0]:
                self.zcc.logger.info("Logout from zcc")
                result = self.zcc.logout()
                assert result[0], result[1]
            self.zcc.logger.info("Enable service from MA")
            result = self.service_ent.toggle_zpa(action=True)
            assert result[0], result[1]
            time.sleep(3)

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(10)

        result = self.zcc.update_policy()
        assert result[0], result[1]

        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        device_data = result[2]

        assert device_data['ziaEnabled'] == 'true'
        assert device_data['zpaEnabled'] == 'true'
        assert device_data['zdxEnabled'] == 'true'

        assert device_data['ziaHealth'] == 'Active'
        assert device_data['zpaHealth'] == 'Active'
        assert device_data['zdxHealth'] == 'Active'



















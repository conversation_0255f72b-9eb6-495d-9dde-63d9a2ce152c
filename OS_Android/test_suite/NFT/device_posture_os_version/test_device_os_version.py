import allure
from OS_Android.test_suite.NFT.device_posture_os_version import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.ui_zcc import Zcc
from common_lib.helper_function import help_return
import pytest
logger_obj = conftest.logger_obj


class TestDeviceOsVersion:

    @allure.title("Setup Method")
    def setup_class(self):
        """
                This function performs the setup process for the test class.
                It includes the following steps:
                1. Creation of forward profile and app profile
                2. Logout zcc and clear logs

                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
                """
        self.zcc = Zcc(start_app=True,log_handle=logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.device_posture = conftest.device_posture
        self.log_handler = conftest.log_handler

        self.tray_file = self.log_handler.get_log_file_name(plugin_name="tray")
        assert self.tray_file[0], self.tray_file[0]
        self.tray_file = self.tray_file[1]

        self.tunnel_file = self.log_handler.get_log_file_name(plugin_name="tunnel")
        assert self.tunnel_file[0], self.tunnel_file[0]
        self.tunnel_file = self.tunnel_file[1]

    @allure.title("setup")
    @pytest.fixture(scope="function", autouse=True)
    def delete_postures_android(self):
        self.zcc.logger.info("Deleting Device Posture in starting of each testcase")
        self.device_posture.delete_device_posture(posture_name="Auto_os_posture")

    @allure.title("Validate on passing Two OS Version along with two different patches respectively")
    @pytest.mark.xray("QA-280600")
    @add_markers("regression","P1")
    def test_pass_two_versions_two_patches(self):
        """
        The function creates the OS version posture with 2 Version ID's
        It performs the following steps:

        1. Create the OS version Device Posture with 2 different operating system and validate the posture
           in Tunnel logs

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Create Device Posture OS Version")
        result = self.device_posture.create_os_version_posture_for_android(posture_name="Auto_os_posture",
                                                                           os_version_id=[185,184],os_build_number='2025-03')
        assert result[0], result[1]

        self.zcc.logger.info("Verifying Update Policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = help_return(conftest.log_ops.search_log_file, file= self.tunnel_file,
                             words_to_find_in_line={"name":"Auto_os_posture","postureId":postureId,"result":False},
                             directory=conftest.const_obj.LOG_PATH)

        assert result[0], result[1]

        self.zcc.logger.info("Deleting Device Posture")
        result = self.device_posture.delete_device_posture(posture_name="Auto_os_posture")
        assert result[0], result[1]

        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

    @allure.title("Validate posture when only OS version is provided and not patch")
    @pytest.mark.xray("QA-280599")
    @add_markers("regression", "P1")
    def test_posture_with_os_version_but_not_patch(self):
        """
        The function create OS version Device Posture with no patch Date
        It performs the following steps:

        1. Create Device Posture OS Version with no Patch Date

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Create Device Posture OS Version")
        result = self.device_posture.create_os_version_posture_for_android(posture_name="Auto_os_posture",
                                                                           os_version_id="185",os_build_number="")

        assert result[0], result[1]

        self.zcc.logger.info("Verifying Update Policy")
        result=self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = help_return(conftest.log_ops.search_log_file, file= self.tunnel_file,
                             words_to_find_in_line={"name":"Auto_os_posture","postureId":postureId,"result":True},
                             directory=conftest.const_obj.LOG_PATH)

        assert result[0], result[1]

        self.zcc.logger.info("Deleting Device Posture")
        result = self.device_posture.delete_device_posture(posture_name="Auto_os_posture")
        assert result[0], result[1]

        self.zcc.logger.info("Logging Out Of ZApp")
        result = self.zcc.logout()
        assert result[0], result[1]

    @allure.title("Validate new posture is getting downloaded on relogin")
    @pytest.mark.xray("QA-280597")
    @add_markers("regression", "P0")
    def test_validate_new_posture_getting_downloaded_on_relogin(self):
        """
        The function validate that the posture is getting downloaded on re-login
        It performs the following steps:

        1. Create New Posture and re-login to ZCC

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        self.zcc.logger.info("Create Device Posture OS Version")
        result = self.device_posture.create_os_version_posture_for_android(posture_name="Auto_os_posture",
                                                                           os_version_id="185", os_build_number='2025-03')

        assert result[0], result[1]
        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"],)

        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        result=self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = help_return(conftest.log_ops.search_log_file, file= self.tunnel_file,
                             words_to_find_in_line={"name":"Auto_os_posture","postureId":postureId,"result":True},
                             directory=conftest.const_obj.LOG_PATH)

        assert result[0], result[1]

        result = self.device_posture.delete_device_posture(posture_name="Auto_os_posture")
        assert result[0], result[1]

        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

    @allure.title("Validate new posture is getting downloaded on update policy")
    @pytest.mark.xray("QA-280596")

    @add_markers("regression", "P0")
    def test_validate_new_posture_downloaded_on_update_policy(self):
        """
        The function validates that new posture is getting downloaded on re-login
        It performs the following steps:

        1. Verify that the new posture is getting downloaded on update policy or not

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Create Device Posture OS Version")
        result = self.device_posture.create_os_version_posture_for_android(posture_name="Auto_os_posture",
                                                                           os_version_id="185", os_build_number='2025-03')

        assert result[0], result[1]

        self.zcc.logger.info("Verifying Update Policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = help_return(conftest.log_ops.search_log_file, file= self.tunnel_file,
                             words_to_find_in_line={"name":"Auto_os_posture","postureId":postureId,"result":True},
                             directory=conftest.const_obj.LOG_PATH)

        assert result[0], result[1]

        result = self.device_posture.delete_device_posture(posture_name="Auto_os_posture")
        assert result[0], result[1]

        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

    @allure.title("Validating Patch functionality latest patch (installed) and MA has latest patch configured")
    @pytest.mark.xray("QA-280594")
    @add_markers("regression", "P1")
    def test_verify_patch_functionality_latest_patch(self):
        """
        The function creates the new Device Posture OS Version with latest patch of the phone
        It performs the following steps:

        1. Select the build as same as the patch date and validate that posture is getting saved or not

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        patch = self.sys_ops.get_patch_detail()
        self.zcc.logger.info("Create Device Posture OS Version")
        result = self.device_posture.create_os_version_posture_for_android(posture_name="Auto_os_posture",
                                                                           os_version_id="185", os_build_number=patch)

        assert result[0], result[1]

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = help_return(conftest.log_ops.search_log_file, file=self.tunnel_file,
                             words_to_find_in_line={"name":"Auto_os_posture","postureId":postureId,"result":True},
                             directory=conftest.const_obj.LOG_PATH)

        assert result[0], result[1]

        result = self.device_posture.delete_device_posture(posture_name="Auto_os_posture")
        assert result[0], result[1]

        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

    @allure.title("Validate logs when posture is evaluated as true")
    @pytest.mark.xray("QA-266187")

    @add_markers("regression", "P0")
    def test_validate_logs_when_posture_evaluated_as_true(self):
        """
        The function evaluates the new Posture created is true
        It performs the following steps:

        1. Validate the new Posture created is True

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """
        self.zcc.logger.info("Create Device Posture OS Version")
        result = self.device_posture.create_os_version_posture_for_android(posture_name="Auto_os_posture",
                                                                           os_version_id="185",os_build_number='2025-03')
        assert result[0], result[1]

        self.zcc.logger.info("Verifying Update Policy")
        result= self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = help_return(conftest.log_ops.search_log_file, file= self.tunnel_file,
                             words_to_find_in_line={"name":"Auto_os_posture","postureId":postureId,"result":True},
                             directory=conftest.const_obj.LOG_PATH)

        assert result[0], result[1]

        result = self.device_posture.delete_device_posture(posture_name="Auto_os_posture")
        assert result[0], result[1]

        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

    @allure.title("Validate logs when posture is evaluated as false")
    @pytest.mark.xray("QA-266188")
    @add_markers("regression","P0")
    def test_validate_logs_when_posture_evaluate_false(self):

        """
         The function evaluates the new Posture created is False
        It performs the following steps:

        1. Validate the new Posture created is False

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """
        self.zcc.logger.info("Create Device Posture OS Version")
        result = self.device_posture.create_os_version_posture_for_android(posture_name="Auto_os_posture",
                                                                           os_version_id="185", os_build_number='2025-03')

        assert result[0], result[1]

        self.zcc.logger.info("Verifying Update Policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = help_return(conftest.log_ops.search_log_file, file= self.tunnel_file,
                             words_to_find_in_line={"name":"Auto_os_posture","postureId":postureId,"result":False},
                             directory=conftest.const_obj.LOG_PATH)

        assert result[0], result[1]

        result = self.device_posture.delete_device_posture(posture_name="Auto_os_posture")
        assert result[0], result[1]

        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        result = self.zcc.logout()
        assert result[0], result[1]

import pytest, allure

from OS_Android.test_suite.NFT.disable_reason_suit import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.log_ops import *
logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")

test_input = [
               pytest.param('POCO', 'POCO License', marks=pytest.mark.xray("QA-239074")),
               pytest.param('OpenSSL (SafeLogic)', 'OpenSSL (SafeLogic) License', marks=pytest.mark.xray("QA-239075")),
               pytest.param('V8 (JS Engine)', 'V8 (JS Engine) License', marks=pytest.mark.xray("QA-239076")),
               pytest.param('PCAP', 'PCAP License', marks=pytest.mark.xray("QA-239077")),
               pytest.param('RootBeer', 'RootBeer License', marks=pytest.mark.xray("QA-239078")),
               pytest.param('RxJava', 'RxJava License', marks=pytest.mark.xray("QA-239079")),
               pytest.param('GraphView', 'GraphView License', marks=pytest.mark.xray("QA-239080")),
            ]


class TestLicenseAgreement:

    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc = Zcc(start_app=True,log_handle=logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            result = self.zcc.clear_logs()
            assert result[0], result[1]
            result = self.zcc.logout()
            assert result[0], result[1]

    @allure.title('Validate that back Button is functioning properly when a Single License is opened inZCC UI')
    @pytest.mark.xray("QA-239081")
    @add_markers("regression")
    def test_third_party_back_button(self):
        """
        This function tests the validation of license.
        It performs the following steps:
        1. Doing validation zcc app for license
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.zcc.license_third_party_check()
        assert result[0], result[1]

    @allure.title("Validate that the License Agreement and Third Party Software is divided in the two Fields.")
    @pytest.mark.xray("QA-239073")
    @add_markers("regression")
    def test_license_credits_options(self):
        """
        This function tests the validation of license.
        It performs the following steps:
        1. Doing validation zcc app for credit license
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info("verify license and thrd party software option ")
        result= self.zcc.verify_license_credit_options()
        assert result[0], result[1]

    @allure.title('Validate that after clicking the "Third partySoftware" option POCO is clickable and proper detailed information is displayed onthe ZCC UI.')
    @pytest.mark.parametrize("software_path, software_string", test_input)
    @add_markers("regression")
    def test_third_party(self, software_path, software_string):
        """
        This function tests the validation of third party license.
        It performs the following steps:
        1. Doing validation zcc app for thord party license
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Verify poco license info is present under third party agreements")
        result = self.zcc.verify_third_party_software(software_path=f"//*[@resource-id= 'android:id/text1' and @text='{software_path}']",
                                              software_string=f'//android.widget.TextView[@text="{software_string}"]')
        assert result[0], result[1]

    @allure.title('Validate on More tab in ZCC UI , License andCredits button should be working properly as each button should be properly working like POCO , PCAP , OpenSSL , '
                  'V8 and etc.')
    @pytest.mark.xray("QA-239082")
    @add_markers("regression")
    def test_third_party_flow(self):
        """
        This function tests the validation of all license.
        It performs the following steps:
        1. Doing validation zcc app for all license
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Verify poco license info is present under third party agreements")
        result = self.zcc.verify_third_party_software(software_path="//*[@resource-id= 'android:id/text1' and @text='POCO']",
                                              software_string='//android.widget.TextView[@text="POCO License"]')
        assert result[0], result[1]

        self.zcc.logger.info("Verify OpenSSL license info is present under third party agreements")
        result = self.zcc.verify_third_party_software(software_path="//*[@resource-id= 'android:id/text1' and @text='OpenSSL (SafeLogic)']",
                                              software_string='//android.widget.TextView[@text="OpenSSL (SafeLogic) License"]')
        assert result[0], result[1]

        self.zcc.logger.info("Verify PCAP license info is present under third party agreements")
        result = self.zcc.verify_third_party_software(software_path="//*[@resource-id= 'android:id/text1' and @text='PCAP']",
                                              software_string='//android.widget.TextView[@text="PCAP License"]')
        assert result[0], result[1]

        self.zcc.logger.info("Verify RootBeer license info is present under third party agreements")
        result = self.zcc.verify_third_party_software(software_path="//*[@resource-id= 'android:id/text1' and @text='RootBeer']",
                                              software_string='//android.widget.TextView[@text="RootBeer License"]')
        assert result[0], result[1]

        self.zcc.logger.info("Verify RxJava license info is present under third party agreements")
        result = self.zcc.verify_third_party_software(software_path="//*[@resource-id= 'android:id/text1' and @text='RxJava']",
                                              software_string='//android.widget.TextView[@text="RxJava License"]')
        assert result[0], result[1]

        self.zcc.logger.info("Verify GraphView license info is present under third party agreements")
        result = self.zcc.verify_third_party_software(software_path="//*[@resource-id= 'android:id/text1' and @text='GraphView']",
                                              software_string='//android.widget.TextView[@text="GraphView License"]')
        assert result[0], result[1]


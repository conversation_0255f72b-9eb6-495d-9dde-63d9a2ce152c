import allure, time, copy
from datetime import datetime, timedelta

import pytest

import conftest
from OS_Android.library.log_file_helper_function import *
from common_lib.helper_function import *
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers
from common_lib.common.log_ops import *
from OS_Android.library.ui_zcc import Zcc
logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log", log_level="DEBUG")


class TestVerbose:
    @allure.title("Setup method")
    def setup_class(self):
        '''
        Steps:
        1. Creating instances of required functionalities
        2. Logging out to start with a fresh environment
        3. Creating Forwarding Profile and App Profile
        4. Verifying ZIA connectivity
        5. Fetching deviceId from MA for use in TCs
        '''

        self.zcc = Zcc(start_app=True, log_handle=logger_obj)
        self.log_obj = conftest.log_ops
        self.sys_ops = conftest.sys_ops
        self.export_logs = conftest.export_logs
        self.elements = copy.deepcopy(AndroidElements)


        self.device_id = conftest.device_overview.fetch_enrolled_devices_details(get_device_id=True,
                                                                                 get_registration_status=True)



    @pytest.mark.xray("QA-256293")
    @allure.title("Validate that there should be no app crash on remotely fetching the full debugging logs")
    @add_markers("regression", "P2")
    def test_remote_fetch_logs(self):
        self.zcc.logger.info("Step 1: Clearing logs in order to avoid collision with older logs")
        #result = self.zcc.clear_logs()
        #assert result[0], result[1]

        self.zcc.logger.info("Step 2: Remote Fetch logs initiated from MA")
        result = conftest.device_overview.remote_fetch_logs(enrolled_device_id=self.device_id[2])
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(5)
        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Step 3: Verifying app has not been crashed by locating ZIA service element")
        result = self.zcc.verify_service_status(service_type='ZIA')
        assert result[0], result[1]

    @pytest.mark.xray("QA-256294")
    @allure.title("Validate Export logs have the logs for specific time interval")
    @add_markers("regression", "P1")
    def test_export_logs_time_interval(self):
        self.zcc.logger.info("Step 1: Clearing logs in order to avoid collision with older logs")
        #result = self.zcc.clear_logs()
        #assert result[0], result[1]

        self.zcc.logger.info("Step 2: Exporting logs via Export Logs button in ZApp")
        self.zcc.ui.click(path=self.elements.NOTIFICATION, AEFC="More tab option not clicked")
        time.sleep(1)
        self.zcc.ui.click(path=self.elements.EXPORT_LOGS, AEFC="Export logs button not clicked")
        log_time = datetime.now()
        time.sleep(2)
        self.zcc.app_back()

        self.zcc.logger.info("Step 3: Making time objects to search in logs")
        new_time_obj = log_time - timedelta(hours=5, minutes=31)
        formatted_time_obj = new_time_obj.strftime("%Y-%m-%d %H:%M")

        self.zcc.logger.info("log_time:")
        self.zcc.logger.info(formatted_time_obj)

        self.zcc.logger.info("Step 4: Fetching and exporting logs from android device")
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Step 5: Searching the logs in the corresponding log files")
        files_to_search = log_file_helper()
        to_search = formatted_time_obj

        log_found_check = False
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[to_search],
                                 directory=conftest.const_obj.LOG_PATH)

            if result[0]:
                log_found_check = True
                break

        assert log_found_check



    @pytest.mark.xray("QA-256297")
    @allure.title("Validate That there should be no app crash while testing the time interval for Export logs")
    @add_markers("regression", "P2")
    def test_app_crash_on_export_logs(self):
        self.zcc.logger.info("Step 1: Clearing logs in order to avoid collision with older logs")
        #result = self.zcc.clear_logs()
        #assert result[0], result[1]

        self.zcc.logger.info("Step 2: Reporting an issue process")
        result = self.zcc.report_issue(name="Testing Automation", cc_email="<EMAIL>")
        assert result

        self.zcc.logger.info("Step 3: Verifying that Zapp has not been crashed by locating ZIA elements")
        result = self.zcc.verify_service_status(service_type='ZIA')
        assert result[0], result[1]


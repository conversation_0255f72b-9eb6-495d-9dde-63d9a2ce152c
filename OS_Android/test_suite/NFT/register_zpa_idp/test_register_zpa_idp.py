import pytest,allure
from common_lib.Custom_Markers import add_markers
from OS_Android.test_suite.NFT.register_zpa_idp import conftest
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.log_ops import *

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")

class TestRegisterZpaIdp:

    @allure.title("Setup Method")
    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc = Zcc(start_app=True,log_handle=logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops

        self.client_notification = conftest.client_notification
        self.const_obj = conftest.const_obj
        self.device_overview = conftest.device_overview
        self.service_ent = conftest.service_ent
        self.client_support = conftest.client_conn_support

        # self.zcc.logger.info("Giving adhoc cloud name as f{conftest.variables['ADHOC_CLOUD_NAME']} which validate")
        # self.zcc.enter_adhoc_cloud_name(conftest.variables["ADHOC_CLOUD_NAME"])
        self.logger_obj = conftest.logger_obj
        result = self.client_notification.configure_aup(aup_frequency="never")
        assert result[0], result[1]

        self.zia_saml = conftest.saml_zia

    @allure.title("Verify login to ZApp with correct user name on both enrollment screen and ZPA"
                  " Idp screen with toggle button disabled on MA")
    @add_markers("regression", "P0")
    @pytest.mark.xray(["QA-284863"])
    def test_both_correct_username_toggle_off(self):
        """
        Verify login to ZApp with correct user name on both enrollment screen and ZPA Idp
        screen with following toggle button disabled on MA
        Toggle button on MA: 'Register device with ZPA IDP Username'
        """

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        result = self.client_support.register_device_with_idp_username(action=False)
        assert result[0], result[1]

        result = self.client_notification.configure_aup(aup_frequency="never")
        assert result[0], result[1]

        self.zcc.logger.info("Disabling SAML based authentication from SMUI")
        result = self.zia_saml.saml_action_smui(mode="disable")
        assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"],
                                conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"],
                                conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(20)

        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        result = self.zcc.logout()
        assert result[0], result[1]

    @allure.title("Verify login to ZApp with correct user name on both enrollment screen "
                  "and ZPA Idp screen with toggle button enabled on MA")
    @add_markers("regression", "P0")
    @pytest.mark.xray(["QA-284865"])
    def test_both_correct_username_toggle_on(self):
        """
        Verify login to ZApp with correct user name on both enrollment screen and ZPA
        Idp screen with following toggle button enabled on MA
        Toggle button on MA: 'Register device with ZPA IDP Username'
        """

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        result = self.client_support.register_device_with_idp_username(action=True)
        assert result[0], result[1]

        result = self.client_notification.configure_aup(aup_frequency="never")
        assert result[0], result[1]

        self.zcc.logger.info("Disabling SAML based authentication from SMUI")
        result = self.zia_saml.saml_action_smui(mode="disable")
        assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"],
                                conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"],
                                conftest.variables["ZPA_USER_PASSWORD"])

        assert result[0], result[1]
        time.sleep(20)

        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        result = self.zcc.logout()
        assert result[0], result[1]


    @allure.title("Verify login to ZApp with incorrect user name on enrollment screen "
                  "(domain must be correct) but correct user name on ZPA Idp screen with "
                  "toggle button disabled on MA")
    @add_markers("regression")
    @pytest.mark.xray(["QA-284864"])
    def test_invalid_enrollment_correct_idp_toggle_off(self):
        """
        Verify login to ZApp with incorrect user name on enrollment screen (domain must be correct)
        but correct user name on ZPA Idp screen with following toggle button disabled on MA
        Toggle button on MA: 'Register device with ZPA IDP Username'
        """

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        result = self.client_support.register_device_with_idp_username(action=False)
        assert result[0], result[1]

        result = self.client_notification.configure_aup(aup_frequency="never")
        assert result[0], result[1]

        self.zcc.logger.info("Enabling SAML based authentication from SMUI")
        result = self.zia_saml.saml_action_smui(mode="enable")
        assert result[0], result[1]
        result = self.zcc.initial_zcc_login_window(username="<EMAIL>")
        assert result[0], result[1]
        time.sleep(5)

        result = self.zcc.do_okta_login(user_id=conftest.variables["ZPA_USER_ID"],
                                        user_password= conftest.variables["ZPA_USER_PASSWORD"], partial_login=False)
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        result = self.zcc.logout()
        assert result[0], result[1]

    @allure.title("Verify login to ZApp with incorrect user name on enrollment screen "
                  "(domain must be correct) but correct user name on ZPA Idp screen with "
                  "toggle button enabled on MA")
    @add_markers("regression")
    @pytest.mark.xray(["QA-284866"])
    def test_invalid_enrollment_correct_idp_toggle_on(self):
        """
        Verify login to ZApp with incorrect user name on enrollment screen (domain must be correct)
        but correct user name on ZPA Idp screen with following toggle button enabled on MA
        Toggle button on MA: 'Register device with ZPA IDP Username'
        """

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        result = self.client_support.register_device_with_idp_username(action=True)
        assert result[0], result[1]

        result = self.client_notification.configure_aup(aup_frequency="never")
        assert result[0], result[1]

        self.zcc.logger.info("Enabling SAML based authentication from SMUI")
        result = self.zia_saml.saml_action_smui(mode="enable")
        assert result[0], result[1]
        result = self.zcc.initial_zcc_login_window(username="<EMAIL>")
        assert result[0], result[1]

        time.sleep(5)

        result = self.zcc.do_okta_login(user_id=conftest.variables["ZPA_USER_ID"],
                                        user_password=conftest.variables["ZPA_USER_PASSWORD"], partial_login=False)
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

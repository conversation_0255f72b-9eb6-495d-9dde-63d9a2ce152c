import pytest,allure
from OS_Android.test_suite.NFT.logs_validation_suite import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.ui_zcc import Zcc
from OS_Android.library.fetch_android_logs import *
from common_lib.common.log_ops import *
import re
logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")
test_compressed_file = [
               pytest.param("ZSAUpm_ZWebload.*?.zip", marks=pytest.mark.xray("QA-262805")),
               pytest.param("ZSAUpm_DeviceStats.*?.zip", marks=pytest.mark.xray("QA-262810")),
               pytest.param("ZCC_Tray.*?.zip", marks=pytest.mark.xray("QA-262806")),
               pytest.param("ZCC_Tunnel.*?.zip", marks=pytest.mark.xray("QA-262806")),
               pytest.param("ZSAUpm_ZTraceroute.*?.zip", marks=pytest.mark.xray("QA-262806")),

               ]

test_unzipped_file = [
               pytest.param("ZSAUpm_ZWebload.*?.log", marks=pytest.mark.xray("QA-262803")),
               pytest.param("ZSAUpm_DeviceStats.*?.log", marks=pytest.mark.xray("QA-262803")),
               pytest.param("ZCC_Tunnel.*?.log", marks=pytest.mark.xray("QA-262803")),
               pytest.param("ZCC_Tray.*?.log", marks=pytest.mark.xray("QA-262803")),
               pytest.param("ZSAUpm_ZTraceroute.*?.log", marks=pytest.mark.xray("QA-262803")),

               ]


class TestCompressLogs:
    @allure.title("Setup Method")
    def setup_class(self):
        self.logger_obj = conftest.logger_obj
        self.zcc = Zcc(start_app=True,log_handle=self.logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_handler = conftest.log_handler
        self.fetch_log = FetchAndroidLogs(logger_obj)
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.device_overview = conftest.device_overview
        self.trusted_network = conftest.trusted_network
        self.zsa_upm_files = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['upm']
        self.android_files = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['android']
        self.device_files = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['deviceStats']
        self.filepath = os.path.join(os.getcwd(),"OS_Android/Temp_Logs/")
        self.files = os.listdir(self.filepath)
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail(by_default_unzip=False)
        assert result[0], result[1]


    @allure.title("Validate zip present after export logs")
    @pytest.mark.parametrize("file_name_pattern", test_compressed_file)
    @add_markers("regression")
    def test_zip_files(self, file_name_pattern):
        self.zcc.logger.info(f" {file_name_pattern} in ZSAUpm Log file ")
        result = False
        msg = f'Required Compressed file is not found in folder'
        for file in self.files:
            if re.match(file_name_pattern, file):
                self.zcc.logger.info(f'Required Compressed zip file {file} is found in folder')
                result = True
                msg = f'Compress file {file} is found in folder'
                break

        assert result, msg

    @allure.title("Validate log unzip files present after export logs")
    @pytest.mark.parametrize("file_name_pattern", test_unzipped_file)
    @add_markers("regression")
    def test_unzip_files(self, file_name_pattern):
        self.zcc.logger.info(f" {file_name_pattern} in ZSAUpm Log file ")
        result = False
        msg = f'Required unzip file is not found in folder'
        for file in self.files:
            if re.match(file_name_pattern, file):
                self.zcc.logger.info(f'Required  unzip file {file} is found in folder')
                result = True
                msg = f'Unzip file {file} is found in folder'
                break

        assert result, msg

    @allure.title("Validate the Log size shouldn't exceed presecribed limit (10 MB) barrier approx.")
    @add_markers("regression")
    @pytest.mark.xray("QA-262798")
    def test_files_size(self):
        self.fetch_log.unzip_log_files(unzip_path=self.filepath)
        and_files = os.listdir(self.filepath)

        for file in and_files:
            if ".log" in file:

                file_check = f"{self.filepath}/{file}"
                self.zcc.logger.info(f"Checking size of file {file}")
                size_byte = os.path.getsize(file_check)
                self.zcc.logger.info(f"size of file in byes {size_byte}")
                size_kb = size_byte//1024
                self.zcc.logger.info(f"size of file in kb {size_kb}")
                size_mb = size_kb//1024
                self.zcc.logger.info(f"size of file in m b {size_mb}")
                assert  size_mb < 12

    @allure.title("Validate if the log limit is reached ,then another log file is created.")
    @add_markers("regression")
    @pytest.mark.xray("QA-262799")
    def test_files_count(self):
        and_files = os.listdir(self.filepath)
        count = 0
        for file in and_files:
            if ".log" in file and "ZCC_Tunnel" in file:
                count += 1

        assert count > 1

    @allure.title("Check for the Message NEW_LogZCC_Android at the end of the file whenthe limit exceeds the 10 MB barrier")
    @add_markers("regression")
    @pytest.mark.xray("QA-262801")
    def test_new_files_string(self):
        and_files = os.listdir(self.filepath)
        for file in and_files:
            if ".log" in file and "ZCC_Tunnel" in file:
                result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line= 'New log ZCC_Android',
                                                           directory=self.const_obj.LOG_PATH)
                if result[0] is True:
                    break

        assert result[0], result[1]

    @allure.title("Validate Login/Logout case and check for Zipped file in ZCC_androidLog file")
    @add_markers("regression")
    @pytest.mark.xray("QA-262802")
    def test_logout_logs(self):

        self.zcc.logger.info("Logout from zcc")
        result = self.zcc.logout()
        assert result[0], result[1]
        time.sleep(30)

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"],
                                conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail(by_default_unzip=False)
        assert result[0], result[1]

        self.files = os.listdir(self.filepath)
        status = False
        for file in self.files:
            if '.zip' in file:
                status = True

        assert status, "Zip files not "
        status = False
        for file in self.files:
            if '.log' in file:
                status = True

        assert status, "Log file not present"

    @allure.title("Validate using Export Logs on clicking Clear Logs the Zipped files should beremoved.")
    @add_markers("regression")
    @pytest.mark.xray("QA-262807")
    def test_clear_logs(self):
        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail(by_default_unzip=False)
        assert result[0], result[1]

        self.files = os.listdir(self.filepath)
        status = True
        for file in self.files:
            if '.zip' in file:
                status = False

        assert status























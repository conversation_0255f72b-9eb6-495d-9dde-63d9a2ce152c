import pytest,allure
from OS_Android.test_suite.NFT.logs_validation_suite import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.ui_zcc import Zcc
from OS_Android.library.fetch_android_logs import *
logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")

test_input_upm = [
               pytest.param("Error while getting UDID", marks=pytest.mark.xray("QA-279140")),
               pytest.param("Valid UDID exists", marks=pytest.mark.xray("QA-279139")),
               pytest.param("Valid UDID does not exist, so returning android id", marks=pytest.mark.xray("QA-279138")),
               ]

test_input_android = [
               pytest.param("Error while getting UDID", marks=pytest.mark.xray("QA-279137")),
               pytest.param("Valid UDID exists", marks=pytest.mark.xray("QA-279136")),
               pytest.param("Valid UDID does not exist, so returning android id", marks=pytest.mark.xray("QA-279135")),
            ]

test_input_device_stats = [
               pytest.param("Error while getting UDID", marks=pytest.mark.xray("QA-279141")),
               pytest.param("Valid UDID exists", marks=pytest.mark.xray("QA-279142")),
               pytest.param("Valid UDID does not exist, so returning android id", marks=pytest.mark.xray("QA-279143")),
            ]


class TestUdidCases:

    @allure.title("Setup Method")
    def setup_class(self):
        self.logger_obj = conftest.logger_obj
        self.zcc = Zcc(start_app=True,log_handle=self.logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.fetch_log = FetchAndroidLogs(logger_obj)
        self.log_handler = conftest.log_handler
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.device_overview = conftest.device_overview
        self.trusted_network = conftest.trusted_network
        self.zsa_upm_files = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['upm']
        self.android_files = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['tray']
        self.device_files = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['deviceStats']
        self.filepath = os.path.join(os.getcwd(),"OS_Android/Temp_Logs/")
        self.files = os.listdir(self.filepath)
        self.fetch_log.unzip_log_files(unzip_path=self.filepath)


    @allure.title("Validate username in appinfo file")
    @pytest.mark.parametrize(" string_in_file", test_input_upm)
    @add_markers("regression")
    def test_udid_upm_cases(self, string_in_file):
        self.zcc.logger.info(f"Verifying udid cases for string {string_in_file} in ZSAUpm Log file ")
        for file in self.zsa_upm_files:

            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line= string_in_file,
                                                   directory=self.const_obj.LOG_PATH, )
            if result[0] is True:
                break

        assert result[0] is not True, result[1]

    @allure.title("Validate udid string in android file")
    @pytest.mark.parametrize(" string_in_file", test_input_android)
    @add_markers("regression")
    def test_udid_android_cases(self, string_in_file):
        self.zcc.logger.info(f"Verifying udid cases for string {string_in_file} in Android Log file ")

        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line= string_in_file,
                                                   directory=self.const_obj.LOG_PATH)
            if result[0] is True:
                break

        assert result[0] is not True, result[1]

    @allure.title("Validate udid string in devicestats file")
    @pytest.mark.parametrize(" string_in_file", test_input_device_stats)
    @add_markers("regression")
    def test_udid_device_stats_cases(self, string_in_file):
        self.zcc.logger.info(f"Verifying udid cases for string {string_in_file} in Device stats Log file ")

        for file in self.device_files:
            result = help_return(self.log_obj.search_log_file, file=file,  words_to_find_in_line= string_in_file,
                                                   directory=self.const_obj.LOG_PATH)
            if result[0] is True:
                break

        assert result[0] is not True, result[1]



















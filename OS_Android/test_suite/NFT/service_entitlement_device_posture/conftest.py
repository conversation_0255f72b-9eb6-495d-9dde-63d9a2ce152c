from logging import exception

import pytest
from common_lib.mobileadmin.deviceoverview import DeviceOver<PERSON>
from common_lib.adminzia.admingroupuser import AdminGroupUser
from common_lib.mobileadmin.appprofile import AppProfile
from common_lib.mobileadmin.forwardingprofile import ForwardingProfile
from common_lib.adminzia.ziahelper import *
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.log_ops import *
from OS_Android.library.ops_system import SysOps
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
from common_lib.mobileadmin.trustednetwork import TrustedNetwork
from common_lib.mobileadmin.clientconnectornotifications import ClientConnectorNotifications
from common_lib.mobileadmin.auditlogs import AuditLogs
from common_lib.mobileadmin.serviceentitlement import ServiceEntitlement
from common_lib.mobileadmin.clientconnectorsupport import ClientConnectorSupport
from common_lib.mobileadmin.deviceposture import DevicePosture
from common_lib.mobileadmin.devicegroups import DeviceGroups
from common_lib.mobileadmin.serviceentitlement import ServiceEntitlement

config_path = os.path.join(os.getcwd(),"config", os.environ.get("CONFIG_FILE"))

with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")

device_overview=DeviceOverview(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)

const_obj=constants.Utils()

sys_ops=SysOps(logger_obj,variables["CLOUD"])

export_logs=FetchAndroidLogs(logger_obj)

create_zia_user=AdminGroupUser(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)

zia_activate=ZiaHelper(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)

log_ops=LogOps(log_handle=logger_obj)

pcap_ops=PcapOps(log_handle=logger_obj)


app_profile_obj = AppProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)

forwarding_profile_obj=ForwardingProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
trusted_network = TrustedNetwork(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
client_notification = ClientConnectorNotifications(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
audit_logs =  AuditLogs(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
service_ent = ServiceEntitlement(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
client_conn_support = ClientConnectorSupport(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
device_posture = DevicePosture(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
device_group = DeviceGroups(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)


def enable_all_services(self):
    result = self.service_ent.toggle_zdx(action=True)
    assert result[0], result[1]
    result = self.service_ent.toggle_zpa(action=True)
    assert result[0], result[1]
    result = self.service_ent.toggle_zia(action=True)
    assert result[0], result[1]


def zcc_clear_log_logout(self):
    logged_in = self.zcc.validate_zcc_logged_in()
    if logged_in[0]:
        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]
        self.zcc.logger.info("Logout from zcc")
        result = self.zcc.logout()
        assert result[0], result[1]

@pytest.fixture(scope="package", autouse=True)
def setup_teardown():
    app_profile = app_profile_obj
    forwarding_profile = forwarding_profile_obj
    zcc = Zcc(start_app=True,log_handle=logger_obj)

    result = service_ent.toggle_zdx(action=True)
    assert result[0], result[1]

    result = service_ent.toggle_zpa(action=True)
    assert result[0], result[1]


    zcc.logger.info("Deleting App profile")
    try:
        result = app_profile.delete_app_profile(operating_system="Android")
        assert result[0], result[1]
    except Exception as e:
        zcc.logger.info(str(e))

    zcc.logger.info("Deleting Forwarding profile")
    try:
        result = forwarding_profile.delete_forwarding_profile()
        assert result[0], result[1]
    except Exception as e:
        zcc.logger.info(str(e))

    result = forwarding_profile.create_forwarding_profile()
    assert result[0], result[1]

    result = app_profile.create_app_profile(operating_system="Android")
    assert result[0], result[1]

    result = app_profile.edit_app_profile(operating_system='Android', zdx_disable_password = 'Zscaler@123', zpa_disable_password= 'Zscaler@123')
    assert result[0], result[1]

    result = client_conn_support.toggle_pre_populate_client_connector_username(using_javascript=False, using_login_hint=False)
    assert result[0], result[1]

    logged_in = zcc.validate_zcc_logged_in()
    if logged_in[0]:
        zcc.logger.info("Clear logs from zcc app")
        result = zcc.clear_logs()
        assert result[0], result[1]

        zcc.logger.info("Logout from zcc")
        result = zcc.logout()
        assert result[0], result[1]

    yield

    logged_in = zcc.validate_zcc_logged_in()
    if logged_in[0]:
        zcc.logger.info("Clear logs from zcc app")
        result = zcc.clear_logs()
        assert result[0], result[1]

        zcc.logger.info("Logout from zcc")
        result = zcc.logout()
        assert result[0], result[1]

@pytest.fixture(scope="function" )
def log_service_setup():
    try:
        enable_all_services()
        zcc_clear_log_logout()

    except Exception as e:
        logger_obj.info(e)


import allure
from OS_Android.test_suite.NFT.service_entitlement_device_posture import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.log_ops import *
import pytest

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")


class TestServiceEnt1:

    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc = Zcc(start_app=True,log_handle=logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.trusted_network = conftest.trusted_network
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.service_ent = conftest.service_ent
        self.device_overview = conftest.device_overview
        self.trusted_network = conftest.trusted_network
        self.device_posture = conftest.device_posture
        self.device_group = conftest.device_group
        self.service_ent = conftest.service_ent

        result = self.device_group.delete_device_group()

        # self.device_posture.delete_device_posture(posture_name=[self.pass_device_posture, self.fail_device_posture])
        self.device_posture.delete_device_posture(delete_all_postures=True)

    @allure.title('Validate that the Device Posture is getting saved')
    @pytest.mark.xray(["QA-291180"])
    @add_markers("regression", "P0")
    def test_validate_device_posture(self, setup_teardown):
        """
        This test validate device posture creation and deletion
        """

        result  = self.device_posture.create_disk_enc_dp(posture_name='Auto_device_posture')
        assert result[0], result[1]

        result = self.device_posture.get_posture_details(posture_name='Auto_device_posture')

        assert result[0], result[1]

        result = self.device_posture.delete_device_posture(posture_name='Auto_device_posture')
        assert result[0], result[1]

    @allure.title('Validate that the Device Group is getting saved')
    @pytest.mark.xray(["QA-291181"])
    @add_markers("regression", "P0")
    def test_validate_device_group(self, setup_teardown):

        """
        This test validate device group creation and deletion
        """

        result = self.device_posture.create_disk_enc_dp(posture_name='Auto_device_posture')
        assert result[0], result[1]

        result = self.device_posture.get_posture_details(posture_name='Auto_device_posture')

        assert result[0], result[1]

        result = self.device_group.create_device_group(name='Auto_device_group',
                                                       posture_name='Auto_device_posture')
        assert result[0], result[1]

        result = self.device_group.get_device_group(device_group_name='Auto_device_group')

        assert result[0], result[1]

        result = self.device_group.delete_device_group()
        assert result[0], result[1]

        result = self.device_posture.delete_device_posture()
        assert result[0], result[1]

    @allure.title('Validate "Logout ZCC when ZIA entitlement is enabled " knob is getting enabled from MA under Zscaler Service entitlement (ZIA)'
                  'Validate "Logout ZCC when ZIA entitlement is enabled " knob is getting disable from MA under Zscaler Service entitlement (ZIA)')
    @pytest.mark.xray(["QA-291182", "QA-291183"])
    @add_markers("regression", "P1")
    def test_zia_logout_en_dis(self, setup_teardown):

        """
        This test validate logout option validation for zia service
        """

        result = self.device_posture.create_disk_enc_dp(posture_name='Auto_device_posture')
        assert result[0], result[1]

        result = self.device_posture.get_posture_details(posture_name='Auto_device_posture')

        assert result[0], result[1]

        result = self.device_group.create_device_group(name='Auto_device_group',
                                                       posture_name='Auto_device_posture')

        result = self.device_group.get_device_group(device_group_name='Auto_device_group')

        assert result[0], result[1]


        result = self.service_ent.device_group_service_entitlement(service = 'zia',action = 'Enable', device_group_name='Auto_device_group',
                                                       logout_zcc = True)

        assert result[0], result[1]

        result = self.service_ent.get_service_entitlement_details(service='zia')
        assert result[1]['logoutZCCForZIAService'] == 1

        result = self.service_ent.device_group_service_entitlement(service = 'zia',action = 'Enable', device_group_name='Auto_device_group',
                                                       logout_zcc = False)

        result = self.service_ent.get_service_entitlement_details(service='zia')
        result[1]['logoutZCCForZIAService'] == 0

        result = self.service_ent.toggle_zia(action=True)
        assert result[0], result[1]


        result = self.device_group.delete_device_group()
        assert result[0], result[1]


        result = self.device_posture.delete_device_posture(posture_name='Auto_device_posture')
        assert result[0], result[1]

    @allure.title('Validate "Logout ZCC when ZDX entitlement is enabled " knob is getting enabled from MA under Zscaler Service entitlement (ZDX)'
                  'Validate "Logout ZCC when ZDX entitlement is enabled " knob is getting disable from MA under Zscaler Service entitlement (ZDX)')
    @pytest.mark.xray(["QA-291184","QA-291185" ])
    @add_markers("regression", "P1")
    def test_zdx_logout_en_dis(self, setup_teardown):
        """
        This test validate logout option validation for zdx service
        """

        result = self.device_posture.create_disk_enc_dp(posture_name='Auto_device_posture')
        assert result[0], result[1]

        result = self.device_posture.get_posture_details(posture_name='Auto_device_posture')

        assert result[0], result[1]

        result = self.device_group.create_device_group(name='Auto_device_group',
                                                       posture_name='Auto_device_posture')

        assert result[0], result[1]

        result = self.device_group.get_device_group(device_group_name='Auto_device_group')

        assert result[0], result[1]



        result = self.service_ent.device_group_service_entitlement(service = 'zdx',action = 'Enable', device_group_name='Auto_device_group',
                                                       logout_zcc = True)

        assert result[0], result[1]

        result = self.service_ent.get_service_entitlement_details(service='zdx')
        assert result[1]['logoutZCCForZDXService'] == 1

        result = self.service_ent.device_group_service_entitlement(service = 'zdx',action = 'Enable', device_group_name='Auto_device_group',
                                                       logout_zcc = False)

        result = self.service_ent.get_service_entitlement_details(service='zdx')
        result[1]['logoutZCCForZDXService'] == 0

        result = self.service_ent.toggle_zdx(action=True)
        assert result[0], result[1]


        result = self.device_group.delete_device_group()
        assert result[0], result[1]

        result = self.device_posture.delete_device_posture(posture_name='Auto_device_posture')
        assert result[0], result[1]

    @allure.title('Validate "Device Group" knob is getting enable/disable from Service entitlement under ZIA'
                  'Validate "Device Group" knob is getting enable/disable from Service entitlement under ZPA'
                  'Validate "Device Group" knob is getting enable/disable from Service entitlement under ZDX')
    @pytest.mark.xray(["QA-291187", "QA-291188", "QA-291189"])
    @add_markers("regression", "P0")
    def test_zdx_zia_zpa_device_group(self, setup_teardown):
        """
        This test validate device group working for zia. zpa and zdx
        """

        result = self.device_posture.create_disk_enc_dp(posture_name='Auto_device_posture')
        assert result[0], result[1]

        result = self.device_posture.get_posture_details(posture_name='Auto_device_posture')

        assert result[0], result[1]

        result = self.device_group.create_device_group(name='Auto_device_group',
                                                       posture_name='Auto_device_posture')

        assert result[0], result[1]

        result = self.device_group.get_device_group(device_group_name='Auto_device_group')

        assert result[0], result[1]



        result = self.service_ent.device_group_service_entitlement(service = 'zdx',action = 'Enable', device_group_name='Auto_device_group',
                                                       logout_zcc = True)

        assert result[0], result[1]


        result = self.service_ent.device_group_service_entitlement(service = 'zpa',action = 'Enable', device_group_name='Auto_device_group',
                                                       logout_zcc = True)

        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service = 'zia',action = 'Enable', device_group_name='Auto_device_group',
                                                       logout_zcc = True)


        assert result[0], result[1]

        result = self.service_ent.get_service_entitlement_details(service='zia')
        assert result[1]['ziaDeviceGroupList'][0]['name'] == 'Auto_device_group'

        result = self.service_ent.get_service_entitlement_details(service='zdx')

        assert result[1]['upmDeviceGroupList'][0]['name'] == 'Auto_device_group'

        result = self.service_ent.get_service_entitlement_details(service='zpa')

        assert result[1]['deviceGroupList'][0]['name'] == 'Auto_device_group'

        result = self.service_ent.toggle_zdx(action=True)
        assert result[0], result[1]

        result = self.service_ent.toggle_zia(action=True)
        assert result[0], result[1]

        result = self.service_ent.toggle_zpa(action=True)
        assert result[0], result[1]

        result = self.device_group.delete_device_group()
        assert result[0], result[1]

        result = self.device_posture.delete_device_posture(posture_name='Auto_device_posture')
        assert result[0], result[1]
import allure
from OS_Android.test_suite.NFT.service_entitlement_device_posture import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.log_ops import *
import pytest

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")


class TestServiceZia:

    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc = Zcc(start_app=True,log_handle=logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.trusted_network = conftest.trusted_network
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.service_ent = conftest.service_ent
        self.device_overview = conftest.device_overview
        self.trusted_network = conftest.trusted_network
        self.device_posture = conftest.device_posture
        self.device_group = conftest.device_group
        self.service_ent = conftest.service_ent

        self.pass_device_group = 'Auto_pass_group'
        self.fail_device_group = 'Auto_fail_group'
        self.pass_device_posture = 'Auto_pass_posture'
        self.fail_device_posture = 'Auto_fail_posture'

        result = self.service_ent.toggle_zdx(action=True)
        assert result[0], result[1]
        result = self.service_ent.toggle_zpa(action=True)
        assert result[0], result[1]
        result = self.service_ent.toggle_zia(action=True)
        assert result[0], result[1]

        result = self.device_group.delete_device_group()

        #self.device_posture.delete_device_posture(posture_name=[self.pass_device_posture, self.fail_device_posture])
        self.device_posture.delete_device_posture(delete_all_postures=True)


        result = self.device_posture.create_disk_enc_dp(posture_name=self.pass_device_posture)
        assert result[0], result[1]

        result = self.device_posture.create_os_version_posture_for_android(posture_name=self.fail_device_posture, os_version_id=185, os_build_number='2025-03')
        assert result[0], result[1]

        result = self.device_group.create_device_group(name= self.pass_device_group,
                                                      posture_name=[self.pass_device_posture])
        assert result[0], result[1]

        result = self.device_group.create_device_group(name=self.fail_device_group,
                                                      posture_name=[self.fail_device_posture])
        assert result[0], result[1]

    def clean_up(self):
        try:
            result = self.service_ent.toggle_zdx(action=True)
            assert result[0], result[1]
            result = self.service_ent.toggle_zpa(action=True)
            assert result[0], result[1]
            result = self.service_ent.toggle_zia(action=True)
            assert result[0], result[1]
            logged_in = self.zcc.validate_zcc_logged_in()
            if logged_in[0]:
                result = self.zcc.clear_logs()
                assert result[0], result[1]
                result = self.zcc.logout()
                assert result[0], result[1]

        except Exception as e:
            logger_obj.info(e)

    @allure.title('Validate if "Logout ZCC when ZIA entitlement is enabled" knob is enabled '
                  'then ZCC should be logged out automatically')
    @pytest.mark.xray(["QA-291191"])
    @add_markers("regression","P0")
    def test_zia_logout(self, log_service_setup):
        """
        1.) Login to ZApp
        2.) Check that properly Device groups and User groups are
        selected
        3.) Validate that if  "Logout ZCC when ZIA entitlement is enabled"
        then ZCC should automatically Logout
        """

        self.clean_up()

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"])

        assert result[0], result[1]
        time.sleep(5)

        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zia', action='Enable',
                                                                   device_group_name=self.pass_device_group,
                                                                   logout_zcc=True)

        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]


    @allure.title('Enrollment with ZPA + ZDX company with same user and device groups configured for ZDX'
                  'and ZPA and user/device matches both ZDX and ZPA')
    @pytest.mark.xray(["QA-291199"])
    @add_markers("regression", "P1")
    def test_zdx_zpa_pass_zia_fail(self, log_service_setup):
        """
        1. Create device postures , device groups and configure postures.
        2. Enable ZPA and ZDX posture based service entitlement with
        same user groups and device groups
        3. From a device that matches the device postures configured
        on any device group, login as a user part of any configured user
        group
        """

        self.clean_up()


        result = self.service_ent.device_group_service_entitlement(service='zia', action='Enable',
                                                                   device_group_name=self.fail_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zpa', action='Enable',
                                                                   device_group_name=self.pass_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zdx', action='Enable',
                                                                   device_group_name=self.pass_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])

        assert result[0], result[1]
        time.sleep(5)

        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        result = self.zcc.service_ent_status(service='ZIA', current_status='disable')
        assert result[0], result[1]

    @allure.title('Enrollment with ZPA + ZDX company with same user groups and device  groups configured for ZDX and '
                  'ZPA entitlement and user/device does not  match either ZDX or ZPA user groups/device groups')
    @pytest.mark.xray(["QA-291204"])
    @add_markers("regression", "P1")
    def test_zdx_zpa_zia_fail(self, log_service_setup):
        """
        1. Create device postures , device groups and configure postures.
        2. Enable ZPA and ZDX posture based service entitlement with
        same user groups and device groups

        3. From a device that does not match the device postures
        configured on any device group, login as a user part of any
        configured user group.
        """

        self.clean_up()

        result = self.service_ent.device_group_service_entitlement(service='zia', action='Enable',
                                                                   device_group_name=self.fail_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zpa', action='Enable',
                                                                   device_group_name=self.fail_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zdx', action='Enable',
                                                                   device_group_name=self.fail_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]


        result = self.zcc.no_service_service_enttilement(username=conftest.variables["ZIA_USER_ID"], password=conftest.variables["ZIA_USER_PASSWORD"])
        assert result[0], result[1]

    @allure.title('Enrollment with ZIA + ZDX company when device gets ZIA with selective entitlement and'
                  ' ZDX is enabled by default')
    @pytest.mark.xray(["QA-291200"])
    @add_markers("regression", "P1")
    def test_zdx_default_on_zia_serv_pass_zpa_default_off(self, log_service_setup):
        """
        1. Create device postures. Create device group and configure
        postures. Enable ZIA posture based service entitlement with
        user groups and device groups. Enable ZDX by default

        2. From a device that matches the device postures configured
        on ZIA device groups, login as a user part of any user group
        """

        self.clean_up()

        result = self.service_ent.toggle_zdx(action=True)
        assert result[0], result[1]

        result = self.service_ent.toggle_zpa(action=False)
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zia', action='Enable',
                                                                   device_group_name=self.pass_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],)

        assert result[0], result[1]
        time.sleep(10)

        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = self.zcc.service_ent_status(service='ZPA', current_status='disable')
        assert result[0], result[1]

    @allure.title('Enrollment with ZIA only company with ALL user groups and multiple device groups'
                  ' configured and device does not match any of the device groups')
    @pytest.mark.xray(["QA-291205"])
    @add_markers("regression", "P1")
    def test_zdx_zpa_default_off_zia_servc_fail(self, log_service_setup):
        """
        1. Create device postures, device group and configure postures.
        2. Enable ZIA posture based service entitlement with ALL user
        groups and multiple device groups

        3. From a device that does not match the device postures
        configured on any device group, login as a user part of any user
        group
        """

        self.clean_up()

        result = self.service_ent.toggle_zdx(action=False)
        assert result[0], result[1]

        result = self.service_ent.toggle_zpa(action=False)
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zia', action='Enable',
                                                                   device_group_name=self.fail_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.zcc.no_service_service_enttilement(username=conftest.variables["ZIA_USER_ID"],
                                                         password=conftest.variables["ZIA_USER_PASSWORD"])
        assert result[0], result[1]

    @allure.title("In a ZIA + ZPA company, device enrolled to ZPA only initially with selective enablement. "
                  "With force keepalive, ZIA is dynamically enabled with "
                  "'Logout ZCC when ZIA entitlement is enabled' toggle enabled")
    @pytest.mark.xray(["QA-291207"])
    @add_markers("regression", "P1")
    def test_zdx_default_off_zpa_pass_zia_update(self, log_service_setup):
        """
        1. Create device postures, device group and configure postures.
        2. Enable ZIA and ZPA posture based service entitlement with
        user groups and device groups.

        3. Enable 'Logout ZCC when ZIA entitlement is enabled' toggle on
        ZIA entitlement
        """

        self.clean_up()

        result = self.service_ent.toggle_zdx(action=False)
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zpa', action='Enable',
                                                                   device_group_name=self.pass_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zia', action='Enable',
                                                                   device_group_name=self.fail_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])

        assert result[0], result[1]
        time.sleep(5)

        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        result = self.zcc.service_ent_status(service='ZIA', current_status='disable')
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zia', action='Enable',
                                                                   device_group_name=self.pass_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.zcc.update_policy()
        time.sleep(5)
        logged_in = self.zcc.validate_zcc_logged_in()

        assert logged_in[0] is False, result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])

        assert result[0], result[1]
        time.sleep(5)

        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

    @allure.title("In a ZPA + ZDX company, device enrolled to ZPA only initially with selective enablement. "
                  "With force keepalive, ZDX is dynamically enabled with 'Logout ZCC' disabled")
    @pytest.mark.xray(["QA-291208"])
    @add_markers("regression", "P1")
    def test_zpa_default_off_zia_fail_zdx_update(self, log_service_setup):
        """
        1. Create device postures, device group and configure postures.
        2. Enable ZIA and ZPA posture based service entitlement with
        user groups and device groups.

        3. Enable 'Logout ZCC when ZIA entitlement is enabled' toggle on
        ZIA entitlement

        4. From a device that matches the device postures configured
        only on ZPA, login as a user part of any configured user group

        5. Make device match user groups and device groups
        configured on ZIA as well.

        6. Update policy on ZCC
        """

        self.clean_up()

        result = self.service_ent.device_group_service_entitlement(service='zpa', action='Enable',
                                                                   device_group_name=self.pass_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zdx', action='Enable',
                                                                   device_group_name=self.fail_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zia', action='Enable',
                                                                   device_group_name=self.fail_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])

        assert result[0], result[1]
        time.sleep(5)

        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        result = self.zcc.service_ent_status(service='ZDX', current_status='disable')
        assert result[0], result[1]

        result = self.zcc.service_ent_status(service='ZIA', current_status='disable')
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zdx', action='Enable',
                                                                   device_group_name=self.pass_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.zcc.update_policy()
        time.sleep(5)
        logged_in = self.zcc.validate_zcc_logged_in()

        assert logged_in[0] is False, result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])

        assert result[0], result[1]
        time.sleep(5)

        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]

    @allure.title("Enrollment with ZPA only company with multiple device groups "
                  "configured and device does not match any of the device groups")
    @pytest.mark.xray(["QA-291227"])
    @add_markers("regression", "P1")
    def test_zdx_zia_default_off_zpa_fail(self, log_service_setup):
        """
        1. Create device postures. Create device group and configure
        postures. Enable ZIA, ZPA and ZDX posture based service
        entitlement with user groups and device groups. Enable
        'Logout ZCC when ZIA entitlement is enabled' and
        'Logout ZCC when ZDX entitlement is enabled' toggle on ZIA
        and ZDX entitlement

        2. From a device that matches the device postures configured
        only on ZPA, login as a user part of any configured user group

        3. Make device match user groups and device groups
        configured on ZIA and ZDX as well. Exit and relaunch ZCC to
        trigger keepalive
        """

        self.clean_up()

        result = self.service_ent.toggle_zdx(action=False)
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zpa', action='Enable',
                                                                   device_group_name=self.fail_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zia', action='Enable',
                                                                   device_group_name=self.fail_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.zcc.no_service_service_enttilement(username=conftest.variables["ZIA_USER_ID"],
                                                         password=conftest.variables["ZIA_USER_PASSWORD"])
        assert result[0], result[1]

    @allure.title("Enrollment with ZPA only company with multiple device groups "
                  "configured and device matches all the device groups")
    @pytest.mark.xray(["QA-291228"])
    @add_markers("regression", "P1")
    def test_zdx_zia_default_off_zpa_pass(self, log_service_setup):
        """
        1. Create device postures. Create device group and configure
        postures. Enable ZPA posture based service entitlement with
        multiple device groups

        2. From a device that does not match the device postures
        configured on any device group, login as a user part of any
        user group
        """



        result = self.service_ent.toggle_zdx(action=False)
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zpa', action='Enable',
                                                                   device_group_name=self.pass_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zia', action='Enable',
                                                                   device_group_name=self.fail_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])

        assert result[0], result[1]
        time.sleep(5)

        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        result = self.zcc.service_ent_status(service='ZDX', current_status='disable')
        assert result[0], result[1]

        result = self.zcc.service_ent_status(service='ZIA', current_status='disable')
        assert result[0], result[1]

    @allure.title("Enrollment with ZPA only company with one device group configured with multiple device postures "
                  "and device does not match all the postures")
    @pytest.mark.xray(["QA-291230"])
    @add_markers("regression", "P1")
    def test_zdx_default_off_zia_serv_off_zpa_fail(self, log_service_setup):
        """
        1. Create device postures. Create device groups and configure
         postures. Enable ZPA posture based service entitlement with
        multiple device groups

        2. From a device that matches device postures configured on
        all device groups, login as a user part of any user group
        """

        self.clean_up()

        result = self.service_ent.toggle_zdx(action=False)
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zpa', action='Enable',
                                                                   device_group_name=self.fail_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.service_ent.device_group_service_entitlement(service='zia', action='Enable',
                                                                   device_group_name=self.fail_device_group,
                                                                   logout_zcc=True)
        assert result[0], result[1]

        result = self.zcc.no_service_service_enttilement(username=conftest.variables["ZIA_USER_ID"],
                                                         password=conftest.variables["ZIA_USER_PASSWORD"])
        assert result[0], result[1]

def teardown_class(self):
        '''
        Teardown class steps:
        1. Deleting Timeout policy on ZPA
        2. Logging out of ZApp
        '''

        result = self.device_group.delete_device_group()

        self.device_posture.delete_device_posture(delete_all_postures=True)
        result = self.service_ent.toggle_zdx(action=True)
        assert result[0], result[1]
        result = self.service_ent.toggle_zpa(action=True)
        assert result[0], result[1]
        result = self.service_ent.toggle_zia(action=True)
        assert result[0], result[1]








import pytest
import allure
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers
from OS_Android.test_suite.NFT.zcc_passwords_otp import conftest
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.log_ops import *
logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log", log_level="DEBUG")

class TestSameOtp:
    @allure.title("Setup class method")
    def setup_class(self):
        '''
        Setup class method:
        1. Declaring required objects
        2. Logging into ZApp
        '''

        self.zcc = Zcc(start_app=True, log_handle=logger_obj)
        self.device_overview = conftest.device_overview
        self.logger = conftest.logger_obj

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"],
                                )
        assert result[0], result[1]
        time.sleep(30)
        self.zcc.explicit_wait(AndroidElements.ZIA_ON_STATUS)

    @allure.title("Same OTP should not be reused and Error string should be there in such case")
    @pytest.mark.xray(["QA-287569", "QA-287572"])
    @add_markers("regression")
    def test_same_otp(self):
        """
        This function test service disablement using OTP.
        It performs the following steps:
        1) Fetch device details
        2) store otp and use that OTP for service disablement
        3) Enable service and use same OTP for service disbalement again

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Fetching enrolled device details")
        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[1]

        self.zcc.logger.info("Storing required OTP")
        password = result[-1]['deviceOtpArray'][4]
        self.zcc.logger.info(f"OTP: {password}")

        self.zcc.logger.info("Using the service OTP for disabling service")
        result = self.zcc.toggle_service(service="ZIA", action=False, password=password)
        assert result[0], result[1]
        time.sleep(10)

        self.zcc.logger.info("Enabling the service after disable")
        result = self.zcc.toggle_service(service="ZIA", action=True)
        assert result[0], result[1]

        self.zcc.logger.info("Again disabling the service using same OTP")
        result = self.zcc.toggle_service(service="ZIA", action=False, password=password, failure_expected=True)
        assert result[0], result[1]

    @allure.title("Teardown class method")
    def teardown_class(self):
        '''
        Tear down  class method:
        ZCC logout
        '''

        self.zcc.logger.info("Logging out of ZApp")
        result = self.zcc.logout(password='l')
        assert result[0], result[1]





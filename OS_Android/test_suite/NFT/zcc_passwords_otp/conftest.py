import pytest
from common_lib.mobileadmin.deviceoverview import DeviceOverview
from common_lib.mobileadmin.appprofile import AppProfile
from common_lib.mobileadmin.forwardingprofile import ForwardingProfile
from common_lib.adminzia.ziahelper import *
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.log_ops import *
from OS_Android.library.ops_system import SysOps
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
from common_lib.mobileadmin.auditlogs import AuditLogs
from common_lib.mobileadmin.serviceentitlement import ServiceEntitlement
from common_lib.mobileadmin.clientconnectorsupport import ClientConnectorSupport
from common_lib.common import constants

config_path = os.path.join(os.getcwd(), "config", os.environ.get("CONFIG_FILE"))

with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log", log_level="DEBUG")


zcc = Zcc(start_app=True, log_handle=logger_obj)
device_overview = DeviceOverview(variables["CLOUD"], os.environ.get("CONFIG_FILE"), log_handle=logger_obj)

const_obj = constants.Utils()

sys_ops = SysOps(logger_obj, variables["CLOUD"])

export_logs = FetchAndroidLogs(logger_obj)

log_ops = LogOps(log_handle=logger_obj)

pcap_ops = PcapOps(log_handle=logger_obj)

app_profile_obj = AppProfile(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)

forwarding_profile_obj = ForwardingProfile(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)

audit_logs = AuditLogs(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)

service_ent = ServiceEntitlement(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)

client_conn_support = ClientConnectorSupport(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)


def base_teardown():
    try:
        zcc.logger.info("Deleting App profile")
        result = app_profile_obj.delete_app_profile()
        assert result[0], result[1]

        zcc.logger.info("Deleting Forwarding Profile")
        result = forwarding_profile_obj.delete_forwarding_profile()
        assert result[0], result[1]

    except Exception as e:
        zcc.logger.info(str(e))

    try:
        zcc.logger.info("Logging out of ZApp")
        result = zcc.logout(password='l')
        assert result[0], result[1]

    except Exception as e:
        zcc.logger.info(str(e))


@pytest.fixture(autouse=True, scope="package")
def base_setup(request):
    try:
        zcc = Zcc(start_app=True, log_handle=logger_obj)
        logged_in = zcc.validate_zcc_logged_in()
        if logged_in[0]:
            zcc.logger.info("Clear logs from zcc app")
            result = zcc.clear_logs()
            assert result[0], result[1]

            zcc.logger.info("Logout from zcc")
            result = zcc.logout()
            assert result[0], result[1]

        try:
            result = app_profile_obj.delete_app_profile(operating_system="Android")
            assert result[0], result[1]
        except Exception as e:
            zcc.logger.info(str(e))

        try:
            result = forwarding_profile_obj.delete_forwarding_profile()
            assert result[0], result[1]
        except Exception as e:
            zcc.logger.info(str(e))

        result = forwarding_profile_obj.create_forwarding_profile()
        assert result[0], result[1]

        result = app_profile_obj.create_app_profile(operating_system="Android")
        assert result[0], result[1]

        result = client_conn_support.toggle_pre_populate_client_connector_username(using_javascript=False, using_login_hint=False)
        assert result[0], result[1]

        result = app_profile_obj.edit_app_profile(zpa_disable_password="d",
                                                  zia_disable_password="d",
                                                  zdx_disable_password="d",
                                                  logout_password="l",
                                                  uninstall_password="u")
        assert result[0], result[1]

    except Exception as e:
        request.addfinalizer(base_teardown)
        #pytest.skip(f"setup failed reason:{e}")

    else:
        request.addfinalizer(base_teardown)

# Error Enhancement TCs - Requirements
## Prerequisites:
    1) On MA/ZIA, company and user must be configured properly.
    2) On Android device proper zcc version should be installed. (version>=3.7)
    3) ZIA,ZPA and ZDX services must be on from service entitlement.
    4) User must be ZIA+ZPA user.

## Commands to Execute Automation:
**Running All Cases:** python trigger_automation.py --config <config.json> --testcases OS_Android/test_suite/error_enhancement

**Running One Test File from debugging_logs folder:** python trigger_automation.py --config config.json --testcases OS_Android/test_suite/error_enhancement/*.py

**Testcases of [MR-17612](https://jira.corp.zscaler.com/browse/MR-17612)

import pytest
import allure
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers
from OS_Android.test_suite.NFT.zcc_passwords_otp import conftest
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.log_ops import *
logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log", log_level="DEBUG")

# The values are in the format of ["service_name", "index_in_deviceOtpArray"]
test_input = [pytest.param(["ZIA", 4], marks=pytest.mark.xray("QA-287580")),
              pytest.param(["ZPA", 5], marks=pytest.mark.xray("QA-287581")),
              pytest.param(["ZDX", 6], marks=pytest.mark.xray("QA-287582"))
              ]


class TestLogoutAndUninstall:
    @allure.title("Setup Class method")
    def setup_class(self):
        '''
        Setup class method:
        1. Declaring required objects
        2. Logging into ZApp
        '''

        self.zcc = Zcc(start_app=True, log_handle=logger_obj)
        self.device_overview = conftest.device_overview
        self.logger = conftest.logger_obj

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"],
                                )
        assert result[0], result[1]
        time.sleep(30)
        self.zcc.explicit_wait(AndroidElements.ZIA_ON_STATUS)


    @allure.title("Validate that Uninstall OTP shouldn't be used to Logout the App")
    @pytest.mark.xray("QA-287578")
    @add_markers("regression", "sanity")
    def test_uninstall_otp_on_logout(self):
        """
        This function test service disablement using OTP.
        It performs the following steps:
        1) Fetch device details for unistall OTP
        2) OTP can be used for Uninstallation not for logout


        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info("Fetching enrolled device details")
        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[1]

        self.zcc.logger.info("Storing the required OTP i.e. Uninstall OTP")
        password = result[-1]['deviceOtpArray'][2]
        self.zcc.logger.info(f"Uninstall Password: {password}")

        self.zcc.logger.info("Using the Uninstall OTP for Logout operation")
        result = self.zcc.logout(password=password, failure_expected=True)
        assert result[0], result[1]

    @add_markers("regression", "sanity")
    @pytest.mark.parametrize("service", test_input)
    def test_service_otp_for_ops(self, service):
        """
        This function test service disablement using OTP.
        It performs the following steps:
        1) Fetch device details for unistall OTP
        2) OTP can be used service ops


        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        service_name = service[0]
        service_otp_index = service[1]

        self.zcc.logger.info("Fetching enrolled device details")
        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[1]

        self.zcc.logger.info("Storing the required OTP")
        password = result[-1]['deviceOtpArray'][service_otp_index]
        self.zcc.logger.info(f"{service_name}: {password}")

        self.zcc.logger.info(f"Using the {service_name} OTP for Logout operation")
        result = self.zcc.logout(password=password, failure_expected=True)
        assert result[0], result[1]

    @allure.title("Teardown class method")
    def teardown_class(self):
        '''
        Tear down  class method:
        ZCC logout
        '''
        self.zcc.logger.info("Logging out of ZApp")
        result = self.zcc.logout(password='l')
        assert result[0], result[1]

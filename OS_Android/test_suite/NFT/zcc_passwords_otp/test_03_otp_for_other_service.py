import pytest
import allure
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers
from OS_Android.test_suite.NFT.zcc_passwords_otp import conftest
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.log_ops import *
logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log", log_level="DEBUG")

# The format for values is ["OTP_to_be_used_of", "OTP_to_be_used_for", "OTP_to_be_used_for", "index_of_otp_used_in_deviceOtpArray"]
test_input = [pytest.param(["ZIA", "ZPA", "ZDX", 4], marks=pytest.mark.xray("QA-287574")),
              pytest.param(["ZPA", "ZDX", "ZIA", 5], marks=pytest.mark.xray("QA-287575")),
              pytest.param(["ZDX", "ZIA", "ZPA", 6], marks=pytest.mark.xray("QA-287576"))
              ]


class TestOtpForOtherService:
    @allure.title("Setup Method")
    def setup_class(self):
        '''
        Setup class method:
        1. Declaring required objects
        2. Logging into ZApp
        '''

        self.zcc = Zcc(start_app=True, log_handle=logger_obj)
        self.device_overview = conftest.device_overview
        self.logger = conftest.logger_obj

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"],
                                )
        assert result[0], result[1]
        time.sleep(30)
        self.zcc.explicit_wait(AndroidElements.ZIA_ON_STATUS)

    @pytest.mark.parametrize("services", test_input)
    @add_markers("regression", "sanity")
    def test_otp_for_other_service(self, services):
        """
        This function test service disablement using OTP.
        It performs the following steps:
        1) Fetch device details
        2) store otp and use that OTP for service disablement
        3) Validate to use OTP for other service then actaul one from where OTP taken

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        otp_used_of_service = services[0]
        otp_used_by_service_1 = services[1]
        otp_used_by_service_2 = services[2]
        otp_used_index = services[3]

        self.zcc.logger.info("Fetching details of enrolled device from MA")
        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[1]

        self.zcc.logger.info("Storing required OTP")
        password = result[-1]['deviceOtpArray'][otp_used_index]
        self.zcc.logger.info(f"{otp_used_of_service}: {password}")

        self.zcc.logger.info("Using OTP on other service 1")
        result = self.zcc.toggle_service(service=otp_used_by_service_1, action=False, password=password, failure_expected=True)
        assert result[0], result[1]

        self.zcc.logger.info("Using OTP on other service 2")
        result = self.zcc.toggle_service(service=otp_used_by_service_2, action=False, password=password, failure_expected=True)
        assert result[0], result[1]

    @allure.title("Teardown class method")
    def teardown_class(self):
        '''
        Tear down  class method:
        ZCC logout
        '''
        self.zcc.logger.info("Logging out of ZApp")
        result = self.zcc.logout(password='l')
        assert result[0], result[1]

import pytest
import allure
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers
from OS_Android.test_suite.NFT.zcc_passwords_otp import conftest
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.log_ops import *
logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log", log_level="DEBUG")

# The values are in the format of ["service_name", "index_in_deviceOtpArray"]
test_input = [pytest.param(["ZIA", 4], marks=pytest.mark.xray(["QA-287586", "QA-287591"])),
              pytest.param(["ZPA", 5], marks=pytest.mark.xray(["QA-287587", "QA-287592"])),
              pytest.param(["ZDX", 6], marks=pytest.mark.xray(["QA-287588", "QA-287593"]))
              ]


class TestOtpUpdateAfterUse:
    @allure.title("Setup class method")
    def setup_class(self):
        '''
        Setup class method:
        1. Declaring required objects
        2. Logging into ZApp
        '''

        self.zcc = Zcc(start_app=True, log_handle=logger_obj)
        self.device_overview = conftest.device_overview
        self.logger = conftest.logger_obj

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"],
                                )
        assert result[0], result[1]
        time.sleep(30)
        self.zcc.explicit_wait(AndroidElements.ZIA_ON_STATUS)

    @allure.title("Validating OTPs are getting refreshed on MA once used for ZIA/ZPA/ZDX")
    @pytest.mark.parametrize("service", test_input)
    @add_markers("regression", "sanity")
    def test_otp_update_zia_zpa_zdx(self, service):
        """
        This function test service disablement using OTP.
        It performs the following steps:
        1) Fetch device details for unistall OTP
        2) OTP can be used service
        3) After that new OTP is available


        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        service_name = service[0]
        service_otp_index = service[1]

        self.zcc.logger.info("Fetching enrolled device details")
        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[1]

        self.zcc.logger.info("Storing the required OTP")
        password = result[-1]['deviceOtpArray'][service_otp_index]
        self.zcc.logger.info(f"{service_name}: {password}")

        self.zcc.logger.info("Disabling service using OTP")
        result = self.zcc.toggle_service(service=service_name, action=False, password=password)
        assert result[0], result[1]
        time.sleep(10)

        self.zcc.logger.info(f"Re-enabling the {service_name} service")
        result = self.zcc.toggle_service(service=service_name, action=True)
        assert result[0], result[1]
        self.zcc.explicit_wait(AndroidElements.ZIA_ON_STATUS)

        self.zcc.logger.info("Fetching enrolled device details for refreshed OTPs")
        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[1]

        self.zcc.logger.info("Storing the new OTP")
        new_password = result[-1]['deviceOtpArray'][service_otp_index]
        self.zcc.logger.info(f"OLD OTP of {service_name}: {password}")
        self.zcc.logger.info(f"NEW OTP of {service_name}: {new_password}")

        assert password != new_password

        self.zcc.logger.info("Again disabling service using new otp")
        result = self.zcc.toggle_service(service=service_name, action=False, password=new_password)
        assert result[0], result[1]

    @allure.title("The logout OTP should be properly updated after every use")
    @pytest.mark.xray(["QA-287589", "QA-287594"])
    @add_markers("regression", "sanity")
    def test_otp_update_logout(self):
        """
        This function test service disablement using OTP.
        It performs the following steps:
        1) Fetch device details for unistall OTP
        2) OTP can be used service
        3) After that new OTP is available


        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Fetching enrolled device details")
        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[1]

        self.zcc.logger.info("Storing the required OTP")
        password = result[-1]['deviceOtpArray'][0]
        self.zcc.logger.info(f"Logout OTP: {password}")

        self.zcc.logger.info("Performing operation using OTP")
        result = self.zcc.logout(password=password)
        assert result[0], result[1]

        self.zcc.logger.info("Re-logging in to check for OTP update")
        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"],
                                )
        assert result[0], result[1]
        time.sleep(30)
        self.zcc.explicit_wait(AndroidElements.ZIA_ON_STATUS)

        self.zcc.logger.info("Fetching new OTP")
        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[1]

        self.zcc.logger.info("Storing the required OTP")
        new_password = result[-1]['deviceOtpArray'][0]
        self.zcc.logger.info(f"New Logout OTP: {new_password}")

        assert password != new_password

        self.zcc.logger.info("Performing operation using OTP")
        result = self.zcc.logout(password=new_password)
        assert result[0], result[1]

    @allure.title("Teardown class method")
    def teardown_class(self):
        """
        Empty tear down for reference 

        """
        pass

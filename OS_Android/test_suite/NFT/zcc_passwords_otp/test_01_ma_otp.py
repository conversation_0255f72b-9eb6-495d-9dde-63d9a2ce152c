import pytest
import allure
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers
from OS_Android.test_suite.NFT.zcc_passwords_otp import conftest

exit_password= conftest.constants.AppProfilePasswords.EXIT_PASSWORD
logout_password= conftest.constants.AppProfilePasswords.LOGOUT_PASSWORD
uninstall_password= conftest.constants.AppProfilePasswords.UNINSTALL_PASSWORD
zia_disable_password= conftest.constants.AppProfilePasswords.ZIA_DISABLE_PASSWORD
zpa_disable_password= conftest.constants.AppProfilePasswords.ZPA_DISABLE_PASSWORD
zdx_disable_password= conftest.constants.AppProfilePasswords.ZDX_DISABLE_PASSWORD
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.log_ops import *
logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log", log_level="DEBUG")
# The parameters are in the format of ["service_name", "index_in_deviceOtpArray_json"]
test_input_services = [
              pytest.param(["ZIA", 4], marks=pytest.mark.xray(["QA-287566", "QA-287583"])),
              pytest.param(["ZPA", 5], marks=pytest.mark.xray(["QA-287567", "QA-287584"])),
              pytest.param(["ZDX", 6], marks=pytest.mark.xray(["QA-287568", "QA-287585"]))
              ]

test_input_operations = [
              pytest.param(["logout", 0], marks=pytest.mark.xray(["QA-287670"])),
              pytest.param(["uninstall", 2], marks=pytest.mark.xray(["QA-287571"]))
              ]


class TestMaOtp:
    @allure.title("Setup method")
    def setup_class(self):
        '''
        Setup class method:
        1. Declaring required objects
        2. Logging into ZApp
        '''

        self.zcc = Zcc(start_app=True, log_handle=logger_obj)
        self.device_overview = conftest.device_overview
        self.logger = conftest.logger_obj

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"],
                                )
        assert result[0], result[1]
        time.sleep(30)
        self.zcc.explicit_wait(AndroidElements.ZIA_ON_STATUS)

    @pytest.mark.parametrize("service", test_input_services)
    @add_markers("regression")
    def test_validate_ma_otp_services(self, service):
        """
        This function test service disablement using OTP.
        It performs the following steps:
        1) Fetch device details
        2) store otp and use that OTP for service disablement

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        service_name = service[0]
        service_otp_index = service[1]
        self.zcc.logger.info("Fetching enrolled device details")
        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[1]

        self.zcc.logger.info("Storing the required OTP")
        password = result[-1]['deviceOtpArray'][service_otp_index]
        self.zcc.logger.info(f"{service_name}: {password}")

        self.zcc.logger.info("Disabling service using OTP")
        result = self.zcc.toggle_service(service=service_name, action=False, password=password)
        assert result[0], result[1]

    @pytest.mark.parametrize("service", test_input_operations)
    @add_markers("regression")
    def test_validate_ma_otp_operations(self, service):
        """
        This function test service disablement using OTP.
        It performs the following steps:
        1) Fetch device details
        2) Check OTP is present

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        op_name = service[0]
        op_otp_index = service[1]

        self.zcc.logger.info("Fetching enrolled device details")
        result = self.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert result[0], result[1]

        self.zcc.logger.info("Storing the required OTP")
        password = result[-1]['deviceOtpArray'][op_otp_index]
        self.zcc.logger.info(f"{op_name}: {password}")

        self.zcc.logger.info("Validating the existance of OTP")
        otp_exists = True if password is not None or "" else False
        assert otp_exists

    @allure.title("Teardown class method")
    def teardown_class(self):
        '''
        Tear down  class method:
        ZCC logout
        '''

        self.zcc.logger.info("Logging out of ZApp")
        result = self.zcc.logout(password='l')
        assert result[0], result[1]


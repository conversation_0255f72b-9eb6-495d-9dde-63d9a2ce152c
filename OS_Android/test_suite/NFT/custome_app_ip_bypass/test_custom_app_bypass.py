import time

import pytest,allure

from OS_Android.test_suite.NFT.custome_app_ip_bypass import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.ui_zcc import Zcc
from OS_Android.library.fetch_android_logs import *
logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")
from OS_Android.library.ops_system import SysOps

class TestCustomAppByPass:

    @allure.title("Setup Method")
    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.logger_obj = conftest.logger_obj
        self.zcc = Zcc(start_app=True, log_handle=self.logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.fetch_log = FetchAndroidLogs(logger_obj)
        self.log_handler = conftest.log_handler
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.device_overview = conftest.device_overview
        self.trusted_network = conftest.trusted_network
        self.service_ent = conftest.service_ent
        self.android_files = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['android']
        self.filepath = os.path.join(os.getcwd(), "OS_Android/Temp_Logs/")
        self.files = os.listdir(self.filepath)
        self.fetch_log.unzip_log_files(unzip_path=self.filepath)



    @allure.title("Validate ZCC is able to download the bypass apps")
    @add_markers("regression", "P0")
    @pytest.mark.xray(["QA-328439","QA-328440","QA-328441", "QA-328442","QA-328454" ])
    def test_app_bypass_log_validations(self,setup_teardown):
        """
        This function tests the service off password for ZIA.
        It performs the following steps:
        1. Doing validation Service disablement after giving password
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        check = '{"customAppIdentities":[{"active":false,"appData":[{"ipaddr":"***************,*************,*************,************,*************","port":"*","proto":"*"}]'
        for file in self.android_files:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line=check ,
                                                   directory=self.const_obj.LOG_PATH)

            if result[0] is True:
                break

        assert result[0] is True, result[1]

        self.zcc.packet_capture(action='start', clear_notification=True)
        time.sleep(3)

        self.zcc.clear_logs()
        result = self.zcc.validate_traffic_going_through_tunnel(is_zia_disable=False)

        time.sleep(3)
        self.zcc.packet_capture(action='stop', clear_notification=True)
        time.sleep(3)

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = self.zcc.verify_ip_intercepted(conftest.bypass_list)


        assert result[0] is False, result[1]



    @allure.title("Validate on network switch app bypass persists")
    @add_markers("regression", "P0")
    @pytest.mark.xray(["QA-328444"])
    def test_app_bypass_network_validations(self, setup_teardown):
        """
        This function tests the service off password for ZIA.
        It performs the following steps:
        1. Doing validation Service disablement after giving password
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        pass

        operating_system = self.sys_ops.android_device_type()
        if operating_system == "Android":
            result = sys_ops.toggle_mobile_data(enable=True)
            assert result[0], result[1]

            result = sys_ops.toggle_wifi(disable=True)
            assert result[0], result[1]
            time.sleep(4)

            assert result[0], result[1]
            self.zcc.packet_capture(action='start', clear_notification=True)
            time.sleep(3)

            self.zcc.clear_logs()
            result = self.zcc.validate_traffic_going_through_tunnel(is_zia_disable=False)

            time.sleep(3)
            self.zcc.packet_capture(action='stop', clear_notification=True)
            time.sleep(3)

            self.zcc.logger.info("Fetch logs from android device")
            result = self.zcc.export_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Export logs from android device")
            result = self.export_logs.read_export_log_mail()
            assert result[0], result[1]

            result = self.zcc.verify_ip_intercepted(conftest.bypass_list)

            assert result[0] is False, result[1]

            result = sys_ops.toggle_mobile_data(disable=True)
            assert result[0], result[1]

            result = sys_ops.toggle_wifi(enable=True)
            assert result[0], result[1]
            time.sleep(4)

        else:
           pass
        

    @allure.title(" Validate after restart service app bypasses persists")
    @add_markers("regression", "P1")
    @pytest.mark.xray(["QA-328445"])
    def test_app_bypass_restart_validations(self, setup_teardown):
        """
        This function tests the service off password for ZIA.
        It performs the following steps:
        1. Doing validation Service disablement after giving password
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.zcc.restart_service()
        assert result[0], result[1]
        self.zcc.packet_capture(action='start', clear_notification=True)
        time.sleep(3)

        self.zcc.clear_logs()
        result = self.zcc.validate_traffic_going_through_tunnel(is_zia_disable=False)

        time.sleep(3)
        self.zcc.packet_capture(action='stop', clear_notification=True)
        time.sleep(3)

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = self.zcc.verify_ip_intercepted(conftest.bypass_list)

        assert result[0] is False, result[1]

    @allure.title("Validate on re-login bypasses applied successfully"
                  "Validate on ZCC logout, bypass rules should be reset")
    @add_markers("regression", "P0")
    @pytest.mark.xray(["QA-328449", "QA-328450"])
    def test_app_bypass_relogin_validations(self, setup_teardown):
        """
        This function tests the service off password for ZIA.
        It performs the following steps:
        1. Doing validation Service disablement after giving password
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.zcc.logout()
        assert result[0], result[1]

        time.sleep(5)

        result = sys_ops.no_ip_bypass()
        assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(60)

        self.zcc.packet_capture(action='start', clear_notification=True)
        time.sleep(3)

        result = self.zcc.validate_traffic_going_through_tunnel(is_zia_disable=False)

        time.sleep(3)
        self.zcc.packet_capture(action='stop', clear_notification=True)
        time.sleep(3)

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = self.zcc.verify_ip_intercepted(conftest.bypass_list)

        assert result[0] is False, result[1]



    @allure.title("Validate app bypasses with ZIA only company with ZIA on")
    @add_markers("regression", "P1")
    @pytest.mark.xray(["QA-328451"])
    def test_app_bypass_zia_on_validations(self, setup_teardown):
        """
        This function tests the service off password for ZIA.
        It performs the following steps:
        1. Doing validation Service disablement after giving password
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        logged_in = self.zcc.validate_zcc_logged_in()
        if not logged_in[0]:
            result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                    conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0], result[1]
            time.sleep(60)


        result = self.service_ent.toggle_zpa(action=False)

        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.packet_capture(action='start', clear_notification=True)
        time.sleep(3)

        self.zcc.clear_logs()
        result = self.zcc.validate_traffic_going_through_tunnel(is_zia_disable=False)

        time.sleep(3)
        self.zcc.packet_capture(action='stop', clear_notification=True)
        time.sleep(3)

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = self.zcc.verify_ip_intercepted(conftest.bypass_list)

        assert result[0] is False, result[1]

        result = self.service_ent.toggle_zpa(action=True)

        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(10)

        result = self.zcc.zpa_re_authentication(user_id=conftest.variables["ZPA_USER_ID"],
                                  user_password=conftest.variables["ZPA_USER_PASSWORD"], )

        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]


    @allure.title("Validate app bypasses with ZIA only company with ZIA off")
    @add_markers("regression", "P2")
    @pytest.mark.xray(["QA-328452"])
    def test_app_bypass_zia_off_validations(self, setup_teardown):
        """
        This function tests the service off password for ZIA.
        It performs the following steps:
        1. Doing validation Service disablement after giving password
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        result = self.service_ent.toggle_zpa(action=False)

        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        result = self.zcc.toggle_service(service='ZIA', action=False)
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='off', service_type='ZIA')
        assert result[0], result[1]

        self.zcc.packet_capture(action='start', clear_notification=True)
        time.sleep(3)

        self.zcc.clear_logs()
        result = self.zcc.validate_traffic_going_through_tunnel(is_zia_disable=False)

        time.sleep(3)
        self.zcc.packet_capture(action='stop', clear_notification=True)
        time.sleep(3)

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = self.service_ent.toggle_zpa(action=True)

        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        result = self.zcc.zpa_re_authentication(user_id=conftest.variables["ZPA_USER_ID"],
                                  user_password=conftest.variables["ZPA_USER_PASSWORD"], )

        assert result[0], result[1]

        time.sleep(10)
        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]
        result = self.zcc.verify_ip_intercepted(conftest.bypass_list)

        assert result[0] is False, result[1]

    @allure.title("Validate app bypasses with ZIA + ZPA company with ZIA off")
    @add_markers("regression", "P2")
    @pytest.mark.xray(["QA-328453"])
    def test_app_bypass_zia_zpa_off_validations(self, setup_teardown):
        """
        This function tests the service off password for ZIA.
        It performs the following steps:
        1. Doing validation Service disablement after giving password
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.service_ent.toggle_zpa(action=True)



        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        result = self.zcc.toggle_service(service='ZIA', action=False)
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='off', service_type='ZIA')
        assert result[0], result[1]

        self.zcc.packet_capture(action='start', clear_notification=True)
        time.sleep(3)

        self.zcc.clear_logs()
        result = self.zcc.validate_traffic_going_through_tunnel(is_zia_disable=False)

        time.sleep(3)
        self.zcc.packet_capture(action='stop', clear_notification=True)
        time.sleep(3)

        result = self.zcc.toggle_service(service='ZIA', action=True)
        assert result[0], result[1]

        time.sleep(10)
        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]
        result = self.zcc.verify_ip_intercepted(conftest.bypass_list)

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = self.zcc.verify_ip_intercepted(conftest.bypass_list)

        assert result[0] is False, result[1]


    @allure.title("Validate routes using adb shell")
    @add_markers("regression", "P1")
    @pytest.mark.xray(["QA-328455"])
    def test_app_bypass_routes_validations(self, setup_teardown):
        """
        This function tests the service off password for ZIA.
        It performs the following steps:
        1. Doing validation Service disablement after giving password
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        try:
            result = self.zcc.toggle_service(service='ZIA', action=True)
            assert result[0], result[1]

            result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
            assert result[0], result[1]
            time.sleep(40)
        except Exception:
            self.zcc.logger.info("Already zia is in on state")

        result = self.zcc.verify_service_status(service_status='on', service_type='ZPA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]

        result = sys_ops.validate_bypass_ips(conftest.bypass_list)
        assert result[0], result[1]




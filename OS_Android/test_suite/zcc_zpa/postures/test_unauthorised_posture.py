import pytest
import os, sys
import allure
from common_lib.Custom_Markers import add_markers, zcc_mark_version, zdx_mark_version  # Required!
from OS_Android.test_suite.zcc_migration import conftest
from common_lib.helper_function import *


class TestDevicePosture:

    def setup_method(self):

        self.zcc = conftest.zcc
        self.device_posture = conftest.device_posture
        self.sys_ops = conftest.sys_ops
        self.zpa = conftest.zpa
        self.log_obj = conftest.log_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logout()
            result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                    conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0], result[1]
        else:
            result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                    conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0], result[1]
        self.zpa.delete_policy(policy_type="AccessPolicy")

    @pytest.mark.xray("QA-185405")
    @allure.title("Test unauth posture")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.10)
    def test_unauth_posture(self):
        result = self.device_posture.create_unauthorised_posture()
        assert result[0], result[1]
        self.zpa.create_access_policy(action="ALLOW",
                                      criteria="posture",
                                      posture_name="auto_unauthorised_posture",
                                      verification="True",
                                      rule_order=1)
        self.zcc.update_policy()
        result = self.zcc.validate_zpa_bytes()
        if result[0]:
            before_access_recvd_bytes = result[2][1]
        self.sys_ops.adb_commands(
            "adb shell am start -a android.intent.action.VIEW -d {}".format(conftest.variables["ZPA_APPLICATION_NAME"]))
        result = self.zcc.validate_zpa_bytes()
        if result[0]:
            after_access_recvd_bytes = result[2][1]
        if before_access_recvd_bytes == after_access_recvd_bytes:
            assert False
        self.zcc.export_logs()
        self.export_logs.read_export_log_mail()
        result = help_return(conftest.log_ops.search_log_file, file="ZCC_Android",
                             words_to_find_in_line=['DeviceInfoRetriever:Root check result : false'],
                             directory=conftest.const_obj.LOG_PATH)
        assert result[0]
        result = help_return(conftest.log_ops.search_log_file, file="ZCC_Android",
                             words_to_find_in_line=['PostureMgr:Posture : 8, result : true'],
                             directory=conftest.const_obj.LOG_PATH)
        assert result[0]

    def teardown_method(self):
        result = self.zpa.delete_policy(policy_type="AccessPolicy")
        assert result[0], result[1]
        result = self.device_posture.delete_posture()
        assert result[0], result[1]

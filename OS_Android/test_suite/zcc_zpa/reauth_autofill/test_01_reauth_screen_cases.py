import time
import pytest,allure
import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.android_element import AndroidElements

testcase_input = [pytest.param("on", marks=pytest.mark.xray(["QA-250401", "QA-250402", "QA-250403", "QA-250404"])),
                  pytest.param("off", marks=pytest.mark.xray(["QA-286486", "QA-286482", "QA-286510", "QA-286511"]))
                  ]


class TestReauthScreen:
    @allure.title("Setup Method")
    def setup_class(self):
        """
        Setup method steps:
        1. Declaring required objects
        2. Logging into ZApp
        3. Validating ON states for services
        """

        self.zcc = conftest.zcc
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.sys_ops = conftest.sys_ops
        self.service_entitlement = conftest.service_ent
        self.client_conn_support = conftest.client_conn_support

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"],
                                )
        
        assert result[0], result[1]
        time.sleep(30)
        self.zcc.explicit_wait(AndroidElements.ZIA_ON_STATUS)

        result = self.zcc.verify_zpa_service_status()
        assert result[0], result[1]

    @allure.title("Validate while Disabling ZPA from MA and re-enabling it")
    @pytest.mark.parametrize("reauth_knob", testcase_input)
    @add_markers("regression", "sanity")
    def test_reauth_screen_cases(self, reauth_knob):
        if reauth_knob == "off":
            result = self.client_conn_support.toggle_pre_populate_client_connector_username(using_javascript=False, using_login_hint=False)
            assert result[0], result[1]

        self.zcc.logger.info("Disabling ZPA from MA")
        result = self.service_entitlement.toggle_zpa(action=False)
        assert result[0], result[1]

        self.zcc.logger.info("Updating the policy contract")
        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(10)

        self.zcc.logger.info("Enabling the ZPA from MA")
        result = self.service_entitlement.toggle_zpa(action=True)
        assert result[0], result[1]

        self.zcc.logger.info("Again updating policy contract")
        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(10)

        self.zcc.logger.info("Validating the autofilled username on OKTA page")
        result = self.zcc.validate_zpa_reauth_autofill_username(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"], reauth_knob=reauth_knob)
        assert result[0], result[1]

        self.zcc.logger.info("Validating the same but after pressing back button | QA-250403")
        result = self.zcc.validate_zpa_reauth_autofill_username(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"], conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"], reauth_knob=reauth_knob)
        assert result[0], result[1]

        self.zcc.logger.info("Killing the ZApp")
        result = self.zcc.kill_zcc()
        assert result[0], result[1]

        self.zcc.logger.info("Relaunching the ZApp")
        self.zcc.ui.start_app(app=AndroidElements.APP)

        self.zcc.logger.info("Validating the autofilled username after killing ZApp")
        result = self.zcc.validate_zpa_reauth_autofill_username(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"], conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"], reauth_knob=reauth_knob)
        assert result[0], result[1]

    @allure.title("Teardown class")
    def teardown_class(self):
        """
        Teardown class steps:
        1. Logging out of ZApp
        """

        self.zcc.logger.info("Logging out of ZApp")
        result = self.zcc.logout()
        assert result[0], result[1]

import time
import pytest,allure
import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.android_element import AndroidElements

testcase_input = [pytest.param("on", marks=pytest.mark.xray(["QA-286512", "QA-286514", "QA-286516"])),
                  pytest.param("off", marks=pytest.mark.xray(["QA-286513", "QA-286515", "QA-286517"]))
                  ]


class TestLoginScreen:
    @allure.title("Setup Method")
    def setup_class(self):
        """
        Setup method steps:
        1. Declaring required objects
        2. Logging out of an existing session
        3. Enabling knob from MA for autofill reauth username using JS
        """

        self.zcc = conftest.zcc
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.sys_ops = conftest.sys_ops
        self.service_entitlement = conftest.service_ent
        self.client_conn_support = conftest.client_conn_support

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        result = self.client_conn_support.toggle_pre_populate_client_connector_username(using_javascript=True)
        assert result[0], result[1]

    @allure.title("Validate while Disabling ZPA from MA and re-enabling it")
    @pytest.mark.parametrize("reauth_knob", testcase_input)
    @add_markers("regression", "sanity")
    def test_login_screen_cases(self, reauth_knob):
        if reauth_knob == "off":
            result = self.client_conn_support.toggle_pre_populate_client_connector_username(using_javascript=False, using_login_hint=False)
            assert result[0], result[1]

        self.zcc.logger.info("Validating the autofilled username on IDP login page")
        result = self.zcc.validate_idp_autofill_username(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"], reauth_knob=reauth_knob, formbased=True)
        assert result[0], result[1]

        self.zcc.logger.info("Validating the same but after pressing back button")
        result = self.zcc.validate_idp_autofill_username(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"], conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"], reauth_knob=reauth_knob, formbased=True)
        assert result[0], result[1]

        self.zcc.logger.info("Killing the ZApp")
        result = self.zcc.kill_zcc()
        assert result[0], result[1]

        self.zcc.logger.info("Relaunching the ZApp")
        self.zcc.ui.start_app(app=AndroidElements.APP)

        self.zcc.logger.info("Validating the autofilled username after killing ZApp")
        result = self.zcc.validate_idp_autofill_username(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"], conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"], reauth_knob=reauth_knob, formbased=True)
        assert result[0], result[1]

    @allure.title("Teardown class")
    def teardown_class(self):
        """
        Teardown class steps:
        1. Logging into ZApp for the purpose of unenrolling device from MA.
        """

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"],
                                )
        assert result[0], result[1]
        time.sleep(30)
        self.zcc.explicit_wait(AndroidElements.ZIA_ON_STATUS)

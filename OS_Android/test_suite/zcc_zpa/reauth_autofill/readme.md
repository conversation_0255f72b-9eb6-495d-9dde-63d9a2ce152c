# Username autofill during ZPA Reauth
## Prerequisites:
    1) User must be ZIA+ZPA.
    2) Proper credentials must be configured in config.json file.
    3) The cases are automated by enabling form based auth method. If form based is not enabled on your system, kindly mark the formbased parameter as False in the login call.
    4) Initially ZPA must be on from service entitlement on MA.


## Commands to Execute Automation:
**Running All Cases:** python trigger_automation.py --config <config.json> --testcases test_suite/zcc_zpa/reauth_autofill/test_01_disabling_ma.py


import time

from common_lib.Custom_Markers import add_markers  #Required!
import pytest
import allure

from OS_Android.library.ui_zcc import Zcc
from OS_Android.test_suite.zcc_zpa.zcc_cfd.zcc_misc import conftest


class TestLogin:
    def setup_method(self):
        self.logger_obj = conftest.logger_obj
        self.zcc = Zcc(start_app=True,log_handle=self.logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.device_overview = conftest.device_overview
        self.trusted_network = conftest.trusted_network
        act_net = self.export_logs.appinfo_file_values('Active Network')
        self.logger_obj.info(f"Current Active Network is {act_net}")
        self.zia= conftest.create_zia_user
        self.zia_activate= conftest.zia_activate
        logged_in=self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logout()

    @pytest.mark.xray("QA-197851")
    @add_markers("regression", "sanity")
    @allure.title("Test login with ! username")
    def test_special_username(self):
        #result=self.zia.create_user_zia(username="!automation",password="dana1234",groups=['Service Admin'],department='Service Admin')
        #assert result[0],result[1]
        # self.zia_activate.zia_activate()
        username_part= conftest.variables['ZIA_USER_ID'].split('@')[1]
        new_username='!automation@'+username_part
        result=self.zcc.login(new_username, 'dana1234', conftest.variables["ZPA_USER_ID"],
                              conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0],result[1]

    def teardown_method(self):
        result=self.zcc.logout()
        assert result[0],result[1]
        time.sleep(10)
        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        


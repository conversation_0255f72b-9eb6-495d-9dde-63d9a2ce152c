import allure
import pytest
import time
from common_lib.Custom_Markers import add_markers  #Required!
from common_lib.helper_function import help_return
from OS_Android.library.ui_zcc import Zcc
from OS_Android.test_suite.zcc_zpa.zcc_cfd.zcc_misc import conftest


class TestCarrierName:
    @allure.title("Setup Method")    
    def setup_class(self):
        self.logger_obj = conftest.logger_obj
        self.zcc = Zcc(start_app=True,log_handle=self.logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.device_overview = conftest.device_overview
        self.trusted_network = conftest.trusted_network
        act_net = self.export_logs.appinfo_file_values('Active Network')
        self.logger_obj.info(f"Current Active Network is {act_net}")

    @allure.title("Test carrier name when wi-fi is connected")    
    @pytest.mark.xray("QA-201751")
    @add_markers("regression", "sanity")
    def test_carrier_name_on_ma_wifi_is_connected(self):

        result=self.sys_ops.toggle_wifi(enable=True)
        assert result[0],result[1]

        result=self.sys_ops.toggle_mobile_data(disable=True)
        assert result[0],result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(5)

        result = self.zcc.export_logs()
        assert result[0], result[1]

        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = help_return(self.log_obj.search_log_file, file="AppInfo", words_to_find_in_line=['Carrier : '],
                                                  directory=self.const_obj.LOG_PATH)

        assert result[0], result[1]

        result = conftest.device_overview.fetch_enrolled_devices_details(get_device_id=False, get_zcc_version=False,
                                                                         get_registration_status=False,
                                                                         fetch_this_device_details=True)
        assert result[0],result[1]

        assert result[2]['carrier'] is None, f"Carrier value should be empty but it is showing {result[2]['carrier']}"

    @add_markers("regression", "sanity")
    @allure.title("Test carrier name when mobile-data is connected")    
    @pytest.mark.xray("QA-201750")
    def test_carrier_name_on_ma_mobile_data_is_connected(self):
        result=self.sys_ops.toggle_wifi(disable=True)
        assert result[0],result[1]
        result=self.sys_ops.toggle_mobile_data(enable=True)
        assert result[0],result[1]
        time.sleep(5)

        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(5)

        result = self.zcc.export_logs()
        assert result[0], result[1]

        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = help_return(self.log_obj.search_log_file, file="AppInfo", words_to_find_in_line=['Carrier : airtel'],
                                                  directory=self.const_obj.LOG_PATH)

        assert result[0], result[1]

        result = conftest.device_overview.fetch_enrolled_devices_details(get_device_id=False, get_zcc_version=False,
                                                                         get_registration_status=False,
                                                                         fetch_this_device_details=True)
        assert result[0], result[1]

        assert result[2]['carrier'] == 'airtel', "Carrier value is empty"



        


            


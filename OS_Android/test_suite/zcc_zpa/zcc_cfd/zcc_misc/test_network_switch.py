import pytest
from OS_Android.test_suite.zcc_migration.mix_appinfo_report import conftest
import allure
import os,time
from common_lib.Custom_Markers import add_markers,zcc_mark_version,zdx_mark_version #Required!
from OS_Android.library.android_element import AndroidElements
from common_lib.helper_function import *  
from OS_Android.library.ui_zcc import Zcc

class TestNetworkSwitch:

    @allure.title("Setup Method")    
    def setup_class(self):
        self.logger_obj = conftest.logger_obj
        self.zcc = Zcc(start_app=True,log_handle=self.logger_obj)
        self.log_ops=conftest.log_ops
        self.sys_ops=conftest.sys_ops
        self.const_obj=conftest.const_obj
        self.export_logs=conftest.export_logs
        logged_in=self.zcc.validate_zcc_logged_in()
        if not logged_in[0]:
            result=self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0],result[1]

    @add_markers("regression", "sanity")
    @zcc_mark_version(1.0)
    @allure.title("Validate no crash on network switch")
    @pytest.mark.xray("QA-134879")
    def test_crash_on_nw_switch(self):
        self.sys_ops.adb_commands("adb shell input keyevent KEYCODE_BACK")
        result=self.sys_ops.toggle_mobile_data(enable=True)
        assert result[0],result[1]
        result=self.sys_ops.toggle_wifi(disable=True)
        assert result[0],result[1]
        time.sleep(3)
        self.sys_ops.toggle_wifi(enable=True)
        time.sleep(3)
        self.sys_ops.adb_commands("adb shell am start zscaler.com.zscaler/com.zscaler.activities.MainActivity")
        self.zcc.explicit_wait(AndroidElements.ZIA_ON_STATUS)
        self.zcc.export_logs()
        self.export_logs.read_export_log_mail()
        conftest.log_ops.search_log_file(file="Zscaler-Android-Logcat",words_to_find_in_line=['beginning of crash'], failure_expected=True,directory=conftest.const_obj.LOG_PATH)

    @add_markers("regression", "sanity")
    @zcc_mark_version(1.0)
    @allure.title("Validate traffic on network switch")    
    @pytest.mark.xray("QA-233948")
    def test_traffic_on_nw_switch(self):
        self.sys_ops.adb_commands("adb shell input keyevent KEYCODE_BACK")
        result=self.sys_ops.toggle_mobile_data(enable=True)
        assert result[0],result[1]
        result=self.sys_ops.toggle_wifi(disable=True)
        assert result[0],result[1]
        time.sleep(3)
        self.sys_ops.toggle_wifi(enable=True)
        time.sleep(3)
        result=self.zcc.validate_traffic_going_through_tunnel(is_zia_enable=True,is_zia_disable=False) 
        assert result[0],result[1] 

    @add_markers("regression", "sanity")
    @zcc_mark_version(1.0)
    @allure.title("Validate traffic on lte to wifi switch")
    @pytest.mark.xray("QA-233946")
    def test_traffic_lte_to_wifi_switch(self):
        result=self.sys_ops.toggle_mobile_data(enable=True)
        assert result[0],result[1]
        self.sys_ops.toggle_wifi(disable=True)
        time.sleep(3)
        self.sys_ops.toggle_mobile_data(disable=True)
        self.sys_ops.toggle_wifi(enable=True)
        result=self.zcc.validate_traffic_going_through_tunnel(is_zia_enable=True,is_zia_disable=False)
        assert result[0],result[1]

    # @add_markers("regression", "sanity")
    # @zcc_mark_version(1.0)
    # @allure.title("Validate traffic switch between 2 sims")
    # @pytest.mark.xray("QA-201756")
    # @pytest.mark.skip("Not having 2 sims to execute")
    # def test_nw_switch_between_two_sims(self):
    #     result=self.sys_ops.toggle_wifi(disable=True)
    #     assert result[0],result[1]
    #     self.sys_ops.open_wireless_setting()
    #     self.zcc.explicit_wait(AndroidElements.SIM_CARD_MANAGER)
    #     self.zcc.ui.click(AndroidElements.SIM_CARD_MANAGER)
    #     self.zcc.explicit_wait(AndroidElements.MOBILE_DATA)
    #     self.zcc.ui.click(AndroidElements.MOBILE_DATA)
    #     self.zcc.ui.click(AndroidElements.SIM_2)
    #     self.sys_ops.adb_commands("adb shell am start -n zscaler.com.zscaler/com.zscaler.activities.MainActivity")
    #     self.zcc.explicit_wait(AndroidElements.ZIA_ON_STATUS)
    #     result=self.zcc.validate_traffic_going_through_tunnel(is_zia_enable=True)
    #     assert result[0],result[1]
    #     self.sys_ops.toggle_wifi(enable=True)

    @add_markers("regression", "sanity")
    @zcc_mark_version(1.0)
    @allure.title("Validate multiple nw switch")
    @pytest.mark.xray("QA-201756")
    def test_multiple_nw_switches(self):
        self.sys_ops.adb_commands("adb shell input keyevent KEYCODE_BACK")
        self.sys_ops.toggle_mobile_data(enable=True)
        self.sys_ops.toggle_wifi(disable=True)
        time.sleep(3)
        self.sys_ops.toggle_wifi(enable=True)
        self.sys_ops.toggle_mobile_data(enable=True)
        self.sys_ops.toggle_wifi(disable=True)
        self.sys_ops.toggle_wifi(enable=True)
        self.sys_ops.toggle_mobile_data(enable=True)
        time.sleep(3)
        result=self.zcc.validate_traffic_going_through_tunnel(is_zia_enable=True,is_zia_disable=False)
        assert result[0],result[1]

    def teardown_class(self):
        self.zcc.logger.info("Nothing to do in teardown method")
        

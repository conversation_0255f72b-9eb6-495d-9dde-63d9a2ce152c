import pytest
import os, sys
import allure,json
from OS_Android.test_suite.zcc_migration import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version,zdx_mark_version #Required!
from common_lib.adminzia.sslpolicy import SslPolicy
from common_lib.adminzia.ziahelper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from common_lib.mobileadmin.forwardingprofile import ForwardingProfile
from common_lib.mobileadmin.appprofile import AppProfile
from common_lib.mobileadmin.trustednetwork import TrustedNetwork
from OS_Android.library.android_element import AndroidElements
from common_lib.helper_function import * 
import time


class TestPrivateBroker:
    config_path=os.path.join(os.getcwd(),"config","pse_creds.json") #Provide org creds which has private broker deployed
    with open(os.path.join(os.getcwd(),config_path)) as json_file:
        variables=json.load(json_file)

    def setup_class(self):
        self.zcc=conftest.zcc
        self.export_logs=conftest.export_logs
        self.log_obj=conftest.log_ops
        self.sys_ops=conftest.sys_ops
        self.const_obj=conftest.const_obj
        self.trusted_network=TrustedNetwork(self.variables["CLOUD"],config="pse_creds.json",log_handle=conftest.logger_obj)
        result=self.trusted_network.create_trusted_network(dns_servers_list=['10.38.112.10','192.168.1.1'])
        assert result[0],result[1]
        self.app_profile=AppProfile(self.variables["CLOUD"],config="pse_creds.json",log_handle=conftest.logger_obj)
        self.app_profile.delete_app_profile(operating_system='Android')
        result=self.app_profile.create_app_profile(operating_system='Android')
        assert result[0],result[1]
        self.forwarding_profile=ForwardingProfile(self.variables["CLOUD"],config="pse_creds.json",log_handle=conftest.logger_obj)
        self.forwarding_profile.delete_forwarding_profile()
        self.forwarding_profile.create_forwarding_profile()
        self.ssl_policy=SslPolicy(self.variables["CLOUD"],config="pse_creds.json",log_handle=conftest.logger_obj)
        self.ssl_policy.create_ssl_policy(name="automated_ssl",action="not inspect",device_groups=["Android"],url_categories=["EMAIL_HOST"])
        self.zia_helper=ZiaHelper(self.variables["CLOUD"],"pse_creds.json",log_handle=conftest.logger_obj)
        self.zia_helper.zia_activate()
        logged_in=self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logout()
        
        
    @pytest.mark.xray("QA-220151")
    @allure.title("Test private broker with trusted nw")
    @add_markers("regression","sanity")
    def test_trusted_nw_with_private_broker(self):
        result=self.zcc.login(self.variables["ZIA_USER_ID"], self.variables["ZIA_USER_PASSWORD"],self.variables["ZPA_USER_ID"],self.variables["ZPA_USER_PASSWORD"])
        assert result[0],result[1]
        self.zcc.explicit_wait(AndroidElements.PRIVATE_SECURITY)
        self.zcc.ui.click(AndroidElements.PRIVATE_SECURITY)
        self.zcc.explicit_wait(AndroidElements.ZPA_ON_STATUS)
        self.zcc.export_logs()
        self.export_logs.read_export_log_mail()
        result=help_return(conftest.log_ops.search_log_file,file="ZCC_Android",words_to_find_in_line=[conftest.const_obj.ZPA_PRIVATE_BROKER], directory=conftest.const_obj.LOG_PATH)
        assert result[0]
        
        
    @pytest.mark.xray("QA-220152")
    @allure.title("Test zpa app with private broker")
    @add_markers("regression","sanity")
    @zcc_mark_version(1.12)
    def test_zpa_app_with_private_broker(self):
        self.zcc.explicit_wait(AndroidElements.PRIVATE_SECURITY)
        self.zcc.ui.click(AndroidElements.PRIVATE_SECURITY)
        self.zcc.explicit_wait(AndroidElements.ZPA_ON_STATUS)
        
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            before_access_sent_bytes=result[2][0]
        self.sys_ops.adb_commands("adb shell am start -a android.intent.action.VIEW -d {}".format(self.variables["ZPA_APPLICATION_NAME"]))
        time.sleep(5)
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            after_access_sent_bytes=result[2][0]
        if before_access_sent_bytes==after_access_sent_bytes:
            assert False
    
    def teardown_class(self):
        result=self.app_profile.delete_app_profile(operating_system='Android')
        assert result[0],result[1]
        result=self.forwarding_profile.delete_forwarding_profile()
        assert result[0],result[1]
        self.trusted_network.delete_trusted_network()
        self.ssl_policy.delete_ssl_policy(name="automated_ssl")
        self.zia_helper.zia_activate()
        self.zcc.logout()
        


        
        


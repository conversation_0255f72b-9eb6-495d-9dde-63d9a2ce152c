import pytest
import os, sys
import allure
from OS_Android.test_suite.zcc_migration import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version,zdx_mark_version #Required!

class TestZpaApp:
    
    @allure.title("setup")
    def setup_class(self):
        self.zpa=conftest.zpa
        self.sys_ops=conftest.sys_ops
        self.zpa.delete_policy(policy_type="AccessPolicy")
        self.zpa.create_access_policy(zpa_user_name=None,zpa_password= None,action="ALLOW",criteria=None,posture_name=None,app_segments=None,verification=None,rule_order=1)
        self.zcc=conftest.zcc
        

    @pytest.mark.xray("QA-233944")
    @allure.title("Access zpa app after re-login")   
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.0)
    def test_zpa_app_after_relogin(self):
        login_count=0
        while login_count<2:
            try:
                self.zcc.logout()
            except:
                conftest.logger_obj.info("Already logged out")
            logged_in=self.zcc.validate_zcc_logged_in()
            if not logged_in[0]:
                result=self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"])
                assert result[0],result[1]
                result=self.zcc.validate_zpa_bytes()
                if result[0]:
                    before_access_recvd_bytes=result[2][1]

                self.sys_ops.adb_commands("adb shell am start -a android.intent.action.VIEW -d {}".format(conftest.variables["ZPA_APPLICATION_NAME"]))

                result=self.zcc.validate_zpa_bytes()
                if result[0]:
                    after_access_recvd_bytes=result[2][1]
                if before_access_recvd_bytes==after_access_recvd_bytes:
                    assert False
                else:
                    login_count+=1
                    conftest.logger_obj.info("SUCCESS::ZPA App accessed {} time".format(str(login_count)))
                
    @pytest.mark.xray("QA-220155")
    @allure.title("Access zpa app per access policy")   
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.0)
    def test_zpa_app_per_access_policy(self):
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            before_access_recvd_bytes=result[2][1]
        conftest.logger_obj.info("Started::ZPA App access with Allow Rule")

        self.sys_ops.adb_commands("adb shell am start -a android.intent.action.VIEW -d {}".format(conftest.variables["ZPA_APPLICATION_NAME"]))
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            after_access_recvd_bytes=result[2][1]
        if before_access_recvd_bytes==after_access_recvd_bytes:
            assert False,"Failure::ZAPP not accessed"
        else:
            conftest.logger_obj.info("SUCCESS::ZPA App accessed successfully")
        conftest.logger_obj.info("Started::ZPA App access with DENY Rule")
        self.zpa.create_access_policy(zpa_user_name=None,zpa_password= None,action="DENY",criteria=None,posture_name=None,app_segments=None,verification=None,rule_order=1)
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            before_access_recvd_bytes=result[2][1]
        self.sys_ops.adb_commands("adb shell am start -a android.intent.action.VIEW -d {}".format(conftest.variables["ZPA_APPLICATION_NAME"]))
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            after_access_recvd_bytes=result[2][1]
        if before_access_recvd_bytes==after_access_recvd_bytes:
            conftest.logger_obj.info("SUCCESS::ZPA App access blocked with DENY rule")
        



    @allure.title("teardown")
    def teardown_class(self):

        self.zpa.delete_policy(policy_type="AccessPolicy")

        #self.zpa.delete_policy(policy_type="AccessPolicy",fetch_policy_id=True)


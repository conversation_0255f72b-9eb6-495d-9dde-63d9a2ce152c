import pytest
import os, sys
import allure
from OS_Android.test_suite.zcc_migration.mix_appinfo_report import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version,zdx_mark_version #Required!
from common_lib.adminzpa.zpadiagnostics import ZpaDiagnostics
from OS_Android.library.ui_zcc import Zcc

class TestZpaUserStatusLogs:
    
    @allure.title("Setup Method")
    def setup_class(self):
        self.logger_obj = conftest.logger_obj
        self.zcc = Zcc(start_app=True,log_handle=self.logger_obj)
        self.sys_ops=conftest.sys_ops
        self.zpa=ZpaDiagnostics
        logged_in=self.zcc.validate_zcc_logged_in()
        if not logged_in[0]:
            result=self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0],result[1]
        
    @pytest.mark.xray("QA-181433")
    @allure.title("Test Android ZCC version in user status Logs")
    @add_markers("regression", "sanity")
    def test_version_in_user_status_logs(self):
        #self.zpa.get_zpa_diagnostics_logs()
        result=self.zpa.get_zpa_diagnostics_logs(zpa_username=conftest.variables["ZPA_ADMIN_ID"], zpa_password=conftest.variables["ZPA_ADMIN_PASSWORD"],log_type="user status")
        version=result[2]["response"][0]["otherFields"]["version"]
        if version == os.environ.get("zcc_version"):
            conftest.logger_obj.info("ZCC version matched")
        else:
            assert False, "Failed:: ZCC version not matched::Found {}".format(version)

    @pytest.mark.xray("QA-190236")
    @allure.title("Test Chrome OS zcc version in user status Logs")
    @add_markers("regression", "sanity")
    def test_zcc_chrome_version_in_user_status_logs(self):
        package=self.sys_ops.zcc_package_name()
        if package[1]=="zscaler.com.zschromeosapp":
            result= self.zpa.get_zpa_diagnostics_logs(zpa_username=conftest.variables["ZPA_ADMIN_ID"],zpa_password=conftest.variables["ZPA_ADMIN_PASSWORD"],log_type="user status")
            version=result[2]["response"][0]["otherFields"]["version"]
            if version==os.environ.get("zcc_version"):
                self.zcc.logger.info("ZCC version matched")
            else:
                assert False, "Failed:: ZCC version not matched::Found {}".format(version)
        else:
            conftest.logger_obj.info("Skipped testcase")
    
    @pytest.mark.xray("QA-133331")
    @allure.title("Test platform in user status Logs")
    @add_markers("regression", "sanity")
    def test_platform_in_user_status_logs(self):
        result=self.zpa.get_zpa_diagnostics_logs(zpa_username=conftest.variables["ZPA_ADMIN_ID"],zpa_password=conftest.variables["ZPA_ADMIN_PASSWORD"],log_type="user status")
        platform=result[2]["response"][0]["platform"]
        if platform=="ANDROID":
            conftest.logger_obj.info("SUCCESS::Platform matched")
        else:
            assert False, "Failed:: Platform not matched::Found {}".format(platform)
    
    def teardown_class(self):
        pass
            

    

    
        


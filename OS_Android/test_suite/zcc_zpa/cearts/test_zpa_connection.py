import pytest
import allure
from OS_Android.test_suite.zcc_migration import conftest
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers,zcc_mark_version,zdx_mark_version #Required!


class TestZpaConnection:

    @allure.title("Setup Method")
    def setup_method(self):
        self.zcc=conftest.zcc
        self.sys_ops=conftest.sys_ops
        logged_in=self.zcc.validate_zcc_logged_in()
        if not logged_in[0]:
            result=self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0],result[1]
    @pytest.mark.xray("QA-233945")
    @allure.title("Test ZPA connection after enabling internet")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.0)
    def test_zpa_connection_by_enabling_internet(self):
        result=self.sys_ops.toggle_wifi(disable=True)
        assert result[0],result[1]
        self.sys_ops.adb_commands("adb shell svc data disable")
        try:
            self.zcc.explicit_wait(AndroidElements.INTERNET_UNREACHABLE_ERROR)
        except:
            self.zcc.explicit_wait(AndroidElements.NETWORK_ERROR)
        self.sys_ops.toggle_wifi(enable=True)
        result=self.zcc.explicit_wait(AndroidElements.ZPA_ON_STATUS)
        assert result[0],result[1]

    def teardown_method(self):
       result=self.sys_ops.toggle_wifi(enable=True)
       assert result[0],result[1]













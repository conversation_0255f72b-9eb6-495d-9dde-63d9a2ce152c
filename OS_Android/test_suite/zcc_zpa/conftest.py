import json,os
import time
from common_lib.mobileadmin.deviceoverview import Device<PERSON>ver<PERSON>
from common_lib.adminzia.admingroupuser import AdminGroupUser
from common_lib.mobileadmin.appprofile import AppProfile
from common_lib.mobileadmin.forwardingprofile import ForwardingProfile
from common_lib.mobileadmin.trustednetwork import TrustedNetwork
from common_lib.mobileadmin.deviceposture import DevicePosture
from common_lib.adminzia.ziahelper import *
from common_lib.adminzpa.zpapolicy import ZpaPolicy
from common_lib.adminzpa.zpadiagnostics import ZpaDiagnostics
from OS_Android.library.ui_zcc import Zcc
from common_lib.common import logger,constants
from common_lib.common.log_ops import *
from OS_Android.library.ops_system import SysOps
from OS_Android.library.fetch_android_logs import FetchAndroidLogs




config_path = os.path.join(os.getcwd(),"config", os.environ.get("CONFIG_FILE"))

with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")
zcc=Zcc(start_app=True,log_handle=logger_obj)
device_overview=DeviceOverview(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)
const_obj= constants.Utils()
sys_ops=SysOps(logger_obj,variables["CLOUD"])
export_logs=FetchAndroidLogs(logger_obj)
create_zia_user=AdminGroupUser(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)
zia_activate=ZiaHelper(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)
log_ops=LogOps(log_handle=logger_obj)
pcap_ops=PcapOps(log_handle=logger_obj)
app_profile_obj = AppProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
forwarding_profile_obj=ForwardingProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
zpa=ZpaPolicy(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)
zpa_diagnostics=ZpaDiagnostics(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)
trusted_network=TrustedNetwork(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)
device_posture=DevicePosture(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)

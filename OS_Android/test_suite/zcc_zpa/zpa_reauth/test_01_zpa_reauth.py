import pytest
import os, sys
import allure
from OS_Android.test_suite.zcc_migration import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version,zdx_mark_version #Required!

class TestZpaReauth:

    @allure.title("Setup Method")
    def setup_method(self):
        self.zcc=conftest.zcc
        self.zpa=conftest.zpa
        self.sys_ops=conftest.sys_ops
        self.zpa.delete_policy(policy_type="AccessPolicy")
        self.zpa.create_access_policy(action="ALLOW",criteria=None,posture_name=None,app_segments=None,verification=None,rule_order=1)
        logged_in=self.zcc.validate_zcc_logged_in()
        if not logged_in[0]:
            result=self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0],result[1]
        self.zpa.delete_policy(policy_type="AccessPolicy")
        self.zpa.delete_policy(policy_type="ReAuthPolicy")
        self.zpa.delete_policy(policy_type="BypassPolicy")
        result=self.zpa.create_timeout_policy()
        assert result[0],result[1]
        


    @pytest.mark.xray("QA-233947")
    @allure.title("Test Reauth")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.0)
    def test_zpa_reauth(self):
        
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            before_access_sent_bytes=result[2][0]
        self.sys_ops.adb_commands("adb shell am start -a android.intent.action.VIEW -d {}".format(conftest.variables["ZPA_APPLICATION_NAME"]))
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            after_access_sent_bytes=result[2][0]
        if before_access_sent_bytes==after_access_sent_bytes:
            assert False
        result=self.zcc.zpa_re_authentication(conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0],result[1]

    def teardown_method(self):
        self.zpa.delete_policy(policy_type="ReAuthPolicy")
        self.zcc.logger.info("Nothing to do in teardown method")
        

        

from OS_Android.test_suite.zcc_migration import conftest
import pytest
import allure
import time
from common_lib.helper_function import * 
from common_lib.Custom_Markers import add_markers,zcc_mark_version,zdx_mark_version #Required!



class TestMtn:
    @allure.title("Setup Method")
    def setup_method(self):
        
        self.zpa=conftest.zpa
        self.zcc=conftest.zcc
        self.sys_ops=conftest.sys_ops
        self.app_profile=conftest.app_profile_obj
        self.forwarding_profile=conftest.forwarding_profile_obj
        result=self.forwarding_profile.create_forwarding_profile()
        assert result[0],result[1]
        result=self.app_profile.create_app_profile(operating_system="Android")
        assert result[0],result[1]
        self.trusted_network=conftest.trusted_network
        self.log_ops=conftest.log_ops
        self.const_obj=conftest.const_obj
        self.export_logs=conftest.export_logs
        logged_in=self.zcc.validate_zcc_logged_in()
        if not logged_in[0]:
            result=self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],conftest.variables["ZPA_USER_ID"],conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0],result[1]
        self.zpa.delete_policy(policy_type="AccessPolicy")
        self.zpa.delete_policy(policy_type="ReAuthPolicy")
        self.zpa.delete_policy(policy_type="BypassPolicy")

    @allure.title("Test MTN forwarding profile")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.12)
    @pytest.mark.xray("QA-220148")
    def test_mtn_in_forwarding_profile(self):
        self.trusted_network.create_trusted_network(dns_servers_list=["***********","10.38.151.254"])
        self.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1",trusted_network="predefined")
        self.zcc.update_policy()
        self.zcc.export_logs()
        self.export_logs.read_export_log_mail()
        result=help_return(conftest.log_ops.search_log_file,file="ZCC_Android",words_to_find_in_line=["Automated"], directory=conftest.const_obj.LOG_PATH)
        assert result[0]
        

    @allure.title("Test MTN with Access policy") 
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.12)   
    @pytest.mark.xray("QA-220149")
    def test_mtn_access_policy(self):
        self.trusted_network.create_trusted_network(dns_servers_list=["***********","10.38.151.254"])
        self.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1",trusted_network="predefined")
        self.zpa.create_access_policy(zpa_user_name=None,zpa_password= None,action="ALLOW",criteria="trusted",posture_name=None,app_segments=None,verification=None,rule_order=1) 
        #self.zpa.create_access_policy(zpa_user_name=None,zpa_password= None,action="DENY",criteria="trusted",posture_name=None,app_segments=None,verification=None,rule_order=2) 
        self.zcc.update_policy()
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            before_access_recvd_bytes=result[2][1]
        self.sys_ops.adb_commands("adb shell am start -a android.intent.action.VIEW -d {}".format(conftest.variables["ZPA_APPLICATION_NAME"]))
        time.sleep(5)
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            after_access_recvd_bytes=result[2][1]
        if before_access_recvd_bytes==after_access_recvd_bytes:
            assert False
    
    @pytest.mark.xray("QA-220150")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.12)
    @allure.title("Test mtn with client forward policy")
    @add_markers("regression","sanity") 
    def test_mtn_client_forwarding_policy(self):
        self.trusted_network.create_trusted_network(dns_servers_list=["***********","10.38.151.254"])
        self.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1",trusted_network="predefined")
        self.zpa.create_access_policy(zpa_user_name=None,zpa_password= None,action="ALLOW",criteria="trusted",posture_name=None,app_segments=None,verification=None,rule_order=1) 
        #self.zpa.create_access_policy(zpa_user_name=None,zpa_password= None,action="DENY",criteria="trusted",posture_name=None,app_segments=None,verification=None,rule_order=2) 
        self.zpa.create_client_forwarding_policy(zpa_user_name=None,zpa_password= None,action="INTERCEPT",criteria="trusted",posture_name=None,verification=None)
        self.zcc.update_policy()
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            before_access_recvd_bytes=result[2][1]
        self.sys_ops.adb_commands("adb shell am start -a android.intent.action.VIEW -d {}".format(conftest.variables["ZPA_APPLICATION_NAME"]))
        time.sleep(5)
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            after_access_recvd_bytes=result[2][1]
        if before_access_recvd_bytes==after_access_recvd_bytes:
            assert False

    @pytest.mark.xray("QA-220147")
    @add_markers("regression", "sanity")
    @zcc_mark_version(1.12)
    @allure.title("Test mtn with or rule")
    def test_mtn_with_or_rule(self):
        
        self.trusted_network.create_trusted_network(dns_servers_list=["***********"],hostname="example.com",resolved_ips_for_hostname_list=["*************"])
        self.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1",trusted_network="predefined")
        
        self.zpa.create_access_policy(zpa_user_name=None,zpa_password= None,action="ALLOW",criteria="trusted",posture_name=None,app_segments=None,verification=None,rule_order=1) 
        #self.zpa.create_access_policy(zpa_user_name=None,zpa_password= None,action="DENY",criteria="trusted",posture_name=None,app_segments=None,verification=None,rule_order=2)
        self.zcc.update_policy()
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            before_access_recvd_bytes=result[2][1]
        self.sys_ops.adb_commands("adb shell am start -a android.intent.action.VIEW -d {}".format(conftest.variables["ZPA_APPLICATION_NAME"]))
        time.sleep(5)
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            after_access_recvd_bytes=result[2][1]
        if before_access_recvd_bytes==after_access_recvd_bytes:
            assert False
        
    @pytest.mark.xray("QA-220146")
    @allure.title("Test mtn with and rule")
    @add_markers("regression","sanity")
    @zcc_mark_version(1.12)
    def test_mtn_with_and_rule(self):
        
        self.trusted_network.create_trusted_network(condition="All",dns_servers_list=["***********"],hostname="example.com",resolved_ips_for_hostname_list=["*************"])
        self.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1",trusted_network="predefined")        
        self.zpa.create_access_policy(zpa_user_name=None,zpa_password= None,action="ALLOW",criteria="trusted",posture_name=None,app_segments=None,verification=None,rule_order=1) 
        #self.zpa.create_access_policy(zpa_user_name=None,zpa_password= None,action="DENY",criteria="trusted",posture_name=None,app_segments=None,verification=None,rule_order=2)
        self.zcc.update_policy()
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            before_access_recvd_bytes=result[2][1]
        self.sys_ops.adb_commands("adb shell am start -a android.intent.action.VIEW -d {}".format(conftest.variables["ZPA_APPLICATION_NAME"]))
        time.sleep(5)
        result=self.zcc.validate_zpa_bytes()
        if result[0]:
            after_access_recvd_bytes=result[2][1]
        if before_access_recvd_bytes==after_access_recvd_bytes:
            assert False

    def teardown_method(self):
        result=self.app_profile.delete_app_profile(operating_system="Android")
        assert result[0],result[1]
        result=self.forwarding_profile.delete_forwarding_profile()
        assert result[0],result[1]
        try:
            self.zpa.delete_policy(policy_type="AccessPolicy")
            self.zpa.delete_policy(policy_type="BypassPolicy")
        except:
            pass
        self.trusted_network.delete_trusted_network()
        self.zpa.delete_policy(policy_type="AccessPolicy")

    
        

import allure
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers, zcc_mark_version, zdx_mark_version  # Required!
import conftest, time
import pytest
from common_lib.helper_function import *

class TestFpAp:
    @allure.title("setup class")
    def setup_class(self):
        """
        Setup class steps:
        1. Launching ZCC, AP and FP instance
        2. Logging into ZApp
        3. Waiting for ON service status
        """

        self.zcc = conftest.zcc
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.app_profile = conftest.app_profile_obj

        self.zcc.logger.info("Logging into ZApp")
        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        self.zcc.explicit_wait(AndroidElements.ZIA_ON_STATUS)
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA', sleep=60)
        assert result[0], result[1]

    @allure.title("Create Forwarding Profile")
    @pytest.mark.xray("ChromeOS_POC_11")
    @add_markers("regression")
    def test_create_fp(self):
        """
        This function tests the create_forwarding_profile method of the forwarding_profile object.

        Parameters:
        - self (object): The instance of the class that this function belongs to.

        Returns:
        - None: This function does not return any value.

        Raises:
        - AssertionError: If the create_forwarding_profile fails, an AssertionError will be raised.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 31-July-2024 <Please retain this marker>
        """

        self.zcc.logger.info("Creating Forwarding profile")
        result = self.forwarding_profile.create_forwarding_profile(forwarding_profile_name="Testing_ChromeOS_FP")
        assert result[0], result[1]

    @allure.title("Create App Profile")
    @pytest.mark.xray("ChromeOS_POC_12")
    @add_markers("regression")
    def test_create_ap(self):
        """
        This function tests the create_app_profile method of the app_profile object.

        Parameters:
        - self (object): The instance of the class that this function belongs to.

        Returns:
        - None: This function does not return any value.

        Raises:
        - AssertionError: If the create_app_profile fails, an AssertionError will be raised.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 31-July-2024 <Please retain this marker>
        """

        self.zcc.logger.info("Creating App profile")
        result = self.app_profile.create_app_profile(policy_name="Testing_ChromeOS_AP", forwarding_profile_name="Testing_ChromeOS_FP")
        assert result[0], result[1]

    @allure.title("Validate created profiles")
    @pytest.mark.xray("ChromeOS_POC_13")
    @add_markers("regression")
    def test_profiles_on_ui(self):
        """
        This function tests the update_policy method of the zcc object and checks if the policy name exists.

        Parameters:
        - self (object): The instance of the class that this function belongs to.

        Returns:
        - None: This function does not return any value.

        Raises:
        - AssertionError: If the update_policy fails or the policy name does not exist, an AssertionError will be raised.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 31-July-2024 <Please retain this marker>
        """

        policy_name_locator = '//*[@class="android.widget.TextView" and @text="Testing_ChromeOS_AP"]'

        self.zcc.logger.info("Updating the policy in ZApp")
        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Checking for the policy name")
        policy_name_exists = self.zcc.ui.check_if_exists(path=policy_name_locator, boolean=True)
        assert policy_name_exists

    @allure.title("Delete App Profile")
    @pytest.mark.xray("ChromeOS_POC_14")
    @add_markers("regression")
    def test_delete_ap(self):
        """
        This function tests the deletion of an application profile.

        Parameters:
        - self (object): The instance of the class that this function belongs to.

        Returns:
        - None: This function does not return any value.

        Raises:
        - AssertionError: If the deletion of the application profile fails, an AssertionError will be raised.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 31-July-2024 <Please retain this marker>
        """

        self.zcc.logger.info("Deleting App Profile")
        result = self.app_profile.delete_app_profile(policy_name="Testing_ChromeOS_AP")
        assert result[0], result[1]

    @allure.title("Delete Forwarding Profile")
    @pytest.mark.xray("ChromeOS_POC_15")
    @add_markers("regression")
    def test_delete_fp(self):
        """
        This function tests the deletion of a forwarding profile.

        Parameters:
        - self (object): The instance of the class that this function belongs to.

        Returns:
        - None: This function does not return any value.

        Raises:
        - AssertionError: If the deletion of the forwarding profile fails, an AssertionError will be raised.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 31-July-2024 <Please retain this marker>
        """

        self.zcc.logger.info("Deleting Forwarding Profile")
        result = self.forwarding_profile.delete_forwarding_profile(forwarding_profile_name="Testing_ChromeOS_FP")
        assert result[0], result[1]

    @allure.title("Teardown")
    def teardown_class(self):
        """
        Teardown class steps:
        1. Logging out of ZApp
        """

        result = self.zcc.logout()
        assert result[0], result[1]

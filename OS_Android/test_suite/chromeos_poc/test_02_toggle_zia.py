import pytest
from OS_Android.library.android_element import *
import allure
import time
from common_lib.Custom_Markers import add_markers, zcc_mark_version  # Required!
import conftest


class TestToggleZia:
    @allure.title("setup class")
    def setup_class(self):
        """
        Setup Class steps:
        1. Launching ZCC instance
        2. Logging into ZApp
        3. Waiting for Z<PERSON> to get into turn ON status
        """

        self.zcc = conftest.zcc

        self.zcc.logger.info("Logging into ZApp and waiting for service to get into ON state")
        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        self.zcc.explicit_wait(AndroidElements.ZIA_ON_STATUS)
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA', sleep=60)
        assert result[0], result[1]

    @allure.title("Toggle disable zia on zcc")
    @pytest.mark.xray("ChromeOS_POC_04")
    @add_markers("regression")
    def test_disable_zia(self):
        """
        This function tests the disabling of the ZIA service.

        Parameters:
        - self (object): The instance of the class that this function belongs to.

        Returns:
        - None: This function does not return any value.

        Raises:
        - AssertionError: If the disabling of the ZIA service fails, an AssertionError will be raised.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 31-July-2024 <Please retain this marker>
        """

        self.zcc.logger.info("Toggling ZIA service to OFF")
        result = self.zcc.toggle_service(service="ZIA", action='off')
        assert result[0], result[1]

    @allure.title("Toggle enable zia on zcc")
    @pytest.mark.xray("ChromeOS_POC_05")
    @add_markers("regression")
    def test_enable_zia(self):
        """
        This function tests the enabling of the ZIA service.

        Parameters:
        - self (object): The instance of the class that this function belongs to.

        Returns:
        - None: This function does not return any value.

        Raises:
        - AssertionError: If the enabling of the ZIA service fails, an AssertionError will be raised.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 31-July-2024 <Please retain this marker>
        """

        self.zcc.logger.info("Toggling ZIA service to ON")
        result = self.zcc.toggle_service(service="ZIA", action='on')
        assert result[0], result[1]

    @allure.title("teardown")
    def teardown_class(self):
        """
        Teardown class steps:
        1. Logging out ZApp
        """

        result = self.zcc.logout()
        assert result[0], result[1]

import allure

import OS_Android.library.android_element
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers, zcc_mark_version, zdx_mark_version  # Required!
import conftest, time
import pytest
from common_lib.helper_function import *

class TestZccOperations:
    @allure.title("setup class")
    def setup_class(self):
        """
        Setup Class steps:
        1. Disabling report an issue functionality from MA
        2. Launching ZCC instance
        2. Logging into ZApp
        3. Waiting for ZIA to get into turn ON status
        """

        self.client_conn_support = conftest.client_conn_support_obj

        result = self.client_conn_support.toggle_report_an_issue(action=False)
        assert result[0], result[1]

        self.zcc = conftest.zcc
        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        self.zcc.explicit_wait(AndroidElements.ZIA_ON_STATUS)
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA', sleep=60)
        assert result[0], result[1]

    @allure.title("Update policy in ZApp")
    @pytest.mark.xray("ChromeOS_POC_06")
    @add_markers("regression")
    def test_update_policy(self):
        """
        This function tests the updating of the policy in the application.

        Parameters:
        - self (object): The instance of the class that this function belongs to.

        Returns:
        - None: This function does not return any value.

        Raises:
        - AssertionError: If the updating of the policy fails, an AssertionError will be raised.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 31-July-2024 <Please retain this marker>
        """

        try:
            result = self.zcc.report_issue()
        except:
            self.zcc.logger.info("Report issue failed due to unavailability of button")

        result = self.client_conn_support.toggle_report_an_issue(action=True, email_id='<EMAIL>')
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        result = self.zcc.ui.check_if_exists(path=OS_Android.library.android_element.AndroidElements.REPORT_ISSUE, boolean=True)
        assert result

    @allure.title("Report an Issue in ZApp")
    @pytest.mark.xray("ChromeOS_POC_07")
    @add_markers("regression")
    def test_report_issue(self):
        """
        This function tests the report_issue method of the zcc object.

        Parameters:
        - self (object): The instance of the class that this function belongs to.

        Returns:
        - None: This function does not return any value.

        Raises:
        - AssertionError: If the report_issue fails, an AssertionError will be raised.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 31-July-2024 <Please retain this marker>
        """

        result = self.zcc.report_issue(cc_email="")
        assert result[0], result[1]

    @allure.title("Clear logs in ZApp")
    @pytest.mark.xray("ChromeOS_POC_08")
    @add_markers("regression")
    def test_clear_logs(self):
        """
        This function tests the clear_logs method of the zcc object.

        Parameters:
        - self (object): The instance of the class that this function belongs to.

        Returns:
        - None: This function does not return any value.

        Raises:
        - AssertionError: If the clear_logs fails, an AssertionError will be raised.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 31-July-2024 <Please retain this marker>
        """

        result = self.zcc.clear_logs()
        assert result[0], result[1]

    @allure.title("Restart service in ZApp")
    @pytest.mark.xray("ChromeOS_POC_09")
    @add_markers("regression")
    def test_restart_service(self):
        """
        This function tests the restart_service method of the zcc object.

        Parameters:
        - self (object): The instance of the class that this function belongs to.

        Returns:
        - None: This function does not return any value.

        Raises:
        - AssertionError: If the restart_service fails, an AssertionError will be raised.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 31-July-2024 <Please retain this marker>
        """

        result = self.zcc.restart_service()
        assert result[0], result[1]

    @allure.title("teardown")
    def teardown_class(self):
        """
        Teardown class steps:
        1. Logging out of ZApp
        """

        result = self.zcc.logout()
        assert result[0], result[1]

import allure
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers, zcc_mark_version, zdx_mark_version  # Required!
import conftest, time
import pytest
from common_lib.helper_function import *


class TestOktaLogin:
    @allure.title("setup class")
    def setup_class(self):
        """
        Launching the ZCC instance as a setup step
        """

        self.zcc = conftest.zcc

    @allure.title("Verify OKTA login in ZApp")
    @pytest.mark.xray("ChromeOS_POC_10")
    @add_markers("regression")
    def test_okta_login(self):
        """
        This function tests the custom_okta_login and verify_zpa_service_status methods of the zcc object.

        Parameters:
        - self (object): The instance of the class that this function belongs to.

        Returns:
        - None: This function does not return any value.

        Raises:
        - AssertionError: If the custom_okta_login or verify_zpa_service_status fails, an AssertionError will be raised.

        Generated by <PERSON><PERSON><PERSON><PERSON>, assisted by <PERSON><PERSON><PERSON><PERSON> on 31-July-2024 <Please retain this marker>
        """

        self.zcc.logger.info("Currently hardcoding the creds for POC purpose")
        result = self.zcc.custom_okta_login("<EMAIL>", "Admin@123", "<EMAIL>",
                                            "Zscaler@123#")
        assert result[0], result[1]

        self.zcc.logger.info("Verifing ZPA service")
        time.sleep(15)
        result = self.zcc.verify_zpa_service_status()
        assert result[0], result[1]

    @allure.title("teardown")
    def teardown_class(self):
        """
        Teardown class steps:
        1. Logging out of ZApp
        """

        result = self.zcc.logout(password='l')
        assert result[0], result[1]

# ChromeOS Automation POC
## Prerequisites:
    1) config.json must be configured properly.
    2) On Chromebook, proper ZApp must be installed correspoing to resource folder requirements.
    3) Hardcoded things can be changed as per need.


## Commands to Execute Automation:
**Running All Cases:** python trigger_automation.py --config <config.json> --testcases test_suite/chromeos_poc

**Running One Test File from debugging_logs folder:** python trigger_automation.py --config config.json --testcases test_suite/chromeos_poc/*.py
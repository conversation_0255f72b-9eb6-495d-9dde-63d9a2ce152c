import allure
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers,zcc_mark_version,zdx_mark_version #Required!
import conftest, time
import pytest
from common_lib.helper_function import *
from OS_Android.library.android_element import AndroidElements


class TestLogin():
    @allure.title("setup class")
    def setup_class(self):
        """
        Launching the ZCC instance as a setup step
        """

        self.zcc = conftest.zcc

    @allure.title("Login into ZApp")
    @pytest.mark.xray("ChromeOS_POC_01")
    @add_markers("regression")
    def test_login(self):
        """
        This function tests the login functionality of the application.
        Parameters:
        - self (object): The instance of the class that this function belongs to.
        Returns:
        - None: This function does not return any value.
        Raises:
        - AssertionError: If the login fails, an AssertionError will be raised.

        Generated by <PERSON><PERSON><PERSON><PERSON>, assisted by ZCoder on 31-July-2024 <Please retain this marker>
        """

        self.zcc.logger.info("Logging into ZCC")
        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"], conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]

        self.zcc.logger.info("Waiting for the Logout button to get visible")
        self.zcc.explicit_wait(xpath=AndroidElements.LOGOUT_BUTTON)
        result = self.zcc.validate_zcc_logged_in()
        assert result[0], result[1]

    @allure.title("Verify ZIA service status")
    @pytest.mark.xray("ChromeOS_POC_02")
    @add_markers("regression")
    def test_zia_status(self):
        """
        This function tests the status of the ZIA service.

        Parameters:
        - self (object): The instance of the class that this function belongs to.

        Returns:
        - None: This function does not return any value.

        Raises:
        - AssertionError: If the ZIA service status is not 'on', an AssertionError will be raised.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 31-July-2024 <Please retain this marker>
        """

        time.sleep(15)

        self.zcc.logger.info("Verifying service status whether it is on or not")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA', sleep=60)
        assert result[0], result[1]

    @allure.title("Logout in ZApp")
    @pytest.mark.xray("ChromeOS_POC_03")
    @add_markers("regression")
    def test_logout(self):
        """
        This function tests the logout functionality of the application.

        Parameters:
        - self (object): The instance of the class that this function belongs to.

        Returns:
        - None: This function does not return any value.

        Raises:
        - AssertionError: If the logout fails, an AssertionError will be raised.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 31-July-2024 <Please retain this marker>
        """

        self.zcc.logger.info("Logging out of ZApp")
        result = self.zcc.logout()
        assert result[0], result[1]

    @allure.title("teardown")
    def teardown_class(self):
        """
        Nothing to do in teardown
        """
        pass

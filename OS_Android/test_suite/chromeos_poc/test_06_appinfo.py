import time
import pytest, allure
import conftest
import conftest
from common_lib.Custom_Markers import add_markers
from common_lib.helper_function import help_return
from OS_Android.library.android_element import *
zcc = conftest.zcc
output = zcc.get_zcc_version()

test_input = [
    pytest.param('UserName ', conftest.variables["ZIA_USER_ID"], marks=pytest.mark.xray("QA-58368")),
    pytest.param('CloudName ', conftest.variables["CLOUD"], marks=pytest.mark.xray("QA-58370")),
    pytest.param('IPv6 Address ', 'false', marks=pytest.mark.xray("QA-58373")),
    pytest.param('Active Network ', 'eth5', marks=pytest.mark.xray("QA-58376")),
    pytest.param('PacUrl ', 'https://pac.zscalerbeta.net/y8QGSvW7GdLW/special_character',
                 marks=pytest.mark.xray("QA-58377")),
    pytest.param('Proxy State', 'TUNNEL_FORWARDING', marks=pytest.mark.xray("QA-58378")),
    pytest.param('ZApp Proxy ', '*************:9000', marks=pytest.mark.xray("QA-58379")),
    pytest.param('System DNS servers ', ' , DNS : [**************, ************, ************], Domains : [chd.corp.zscaler.com.]',
                 marks=pytest.mark.xray("QA-58380")),
    pytest.param('VPN Service Active ', 'true', marks=pytest.mark.xray("QA-58382")),
    pytest.param('Zscaler Client Connector version ', output[2].split('=')[1], marks=pytest.mark.xray("QA-58383"))
]


class TestAppinfo:

    @allure.title("Setup Method")
    def setup_class(self):
        """
        Setup class method steps:
        1. Launching required instances
        2. Trying to delete any existing automation created FP/AP
        3. Creating FP and AP
        4. Editing AP with PAC URL
        5. Validating if ZApp is in logged in state or not, if yes then logging out
        6. Logging into ZApp
        7. Verifying ZIA status if on or not
        8. Fetching and exporting logs from ZApp via test.apk
        """
        self.zcc = conftest.zcc
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj

        try:
            result = self.app_profile.delete_app_profile()
            assert result[0], result[1]
            result = self.forwarding_profile.delete_forwarding_profile()
            assert result[0], result[1]
        except Exception as e:
            self.zcc.logger.info(str(e))

        result = self.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        self.app_profile.create_app_profile(operating_system="Android")
        assert result[0], result[1]

        result = self.app_profile.edit_app_profile(pac='https://pac.zscalerbeta.net/y8QGSvW7GdLW/special_character',
                                                   operating_system='Android')
        assert result[0], result[1]

        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(10)

        self.zcc.explicit_wait(AndroidElements.ZIA_ON_STATUS)

        self.zcc.logger.info("Verify status for ZIA is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

    @allure.title("Validate username in appinfo file")
    @pytest.mark.parametrize("appinfo_key, appinfo_value", test_input)
    @add_markers("regression")
    def test_appinfo_parameter(self, appinfo_key, appinfo_value):
        """
        This function tests the verification of a specific parameter from the appinfo file.

        Parameters:
        - self (object): The instance of the class that this function belongs to.
        - appinfo_key (str): The key of the parameter to be verified.
        - appinfo_value (str): The expected value of the parameter.

        Returns:
        - None: This function does not return any value.

        Raises:
        - AssertionError: If the verification of the parameter fails, an AssertionError will be raised.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 31-July-2024 <Please retain this marker>
        """

        self.zcc.logger.info("Verifying username from appinfo file")
        result = help_return(self.log_obj.search_log_file, file="AppInfo",
                             words_to_find_in_line=[f'{appinfo_key}: {appinfo_value}'],
                             directory=self.const_obj.LOG_PATH)

        assert result[0], result[1]

    @allure.title("teardown method")
    def teardown_class(self):
        """
        Teardown steps:
        1. Deleting AP/FP
        2. Logging out of ZApp
        """

        self.zcc.logger.info("Deleting App profile")
        result = self.app_profile.delete_app_profile(operating_system="Android")
        assert result[0], result[1]

        self.zcc.logger.info("Deleting Forwarding profile")
        result = self.forwarding_profile.delete_forwarding_profile()
        assert result[0], result[1]

        self.zcc.logger.info("Logging out of ZApp")
        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

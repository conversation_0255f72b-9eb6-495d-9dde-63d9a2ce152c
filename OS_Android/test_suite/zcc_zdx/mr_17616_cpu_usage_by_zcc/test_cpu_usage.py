import pytest, time
import os, sys, re
import allure, json
import requests, math
from common_lib.Custom_Markers import add_markers, zcc_mark_version, zdx_mark_version  # Required!
from OS_Android.test_suite.zcc_zdx.mr_17616_cpu_usage_by_zcc import conftest
from OS_Android.library.ui_zcc import Zcc
from OS_Android.library.android_element import AndroidElements

inputs = [
    pytest.param('select cpu_user from tbl_mon_processes', "cpu_user",
                 marks=pytest.mark.xray("QA-305556")),
    pytest.param('select cpu_kernel from tbl_mon_processes', "cpu_kernel",
                 marks=pytest.mark.xray("QA-305557")),
    pytest.param("select cpu_total from tbl_mon_processes", "cpu_total",
                 marks=pytest.mark.xray("QA-305558")),
    pytest.param("select cpu_total_pct from tbl_mon_processes", "cpu_total_pct",
                 marks=pytest.mark.xray("QA-305559"))
]

data_validation_inputs = [
    pytest.param("cpuUser", marks=pytest.mark.xray("QA-305560")),
    pytest.param("cpuKernel", marks=pytest.mark.xray("QA-305561")),
    pytest.param("cpu_total", marks=pytest.mark.xray("QA-305562"))
]


def helper_get_zdx_device_stats_retriever_data(log_data: str, cores: int):
    """
    Extracts raw values from the provided log data and calculates device statistics.

    This function parses the log data to extract clock speed, uptime, CPU time, and other relevant metrics.
    It then calculates the average usage percentage based on the extracted values and the number of cores.

    Args:
        log_data (str): The log data to parse.
        cores (int): The number of cores on the device.

    Returns:
        tuple: A tuple containing the status, message, and the extracted device statistics.
            - status (bool): True if the extraction was successful, False otherwise.
            - msg (str): An error message if the extraction failed.
            - result (list): A list of dictionaries containing the extracted device statistics.

    Raises:
        None

    Notes:
        The function assumes that the log data is in the correct format and that the 'tarttime' value is present.
        If the 'starttime' value is not found or if any of the required values are missing, the function returns an error message.
    """

    result = []
    status = True
    msg = ""

    pattern = re.compile(
        r"clockSpeedHz:(.*?) uptimeSec:(.*?)utime:(.*?) stime:(.*?) cutime:(.*?) cstime:(.*?) starttime:(\d+)")
    for item in log_data.split("\n"):
        if "ZDXDeviceStatsRetriever:clockSpeedHz" in item:
            match = pattern.search(item)
            if match:
                values = match.groups()
                if all(len(value.strip()) > 0 for value in values[:-1]):
                    # Extract the values and convert them to the required data types
                    clock_speed_hz = int(values[0].strip())
                    uptime_sec = float(values[1].strip())
                    utime = int(values[2].strip())
                    stime = int(values[3].strip())
                    cutime = int(values[4].strip())
                    cstime = int(values[5].strip())
                    starttime = int(values[6])

                    # Calculate cpuTimeSec and processTimeSec
                    cpu_time_sec = (float(utime + stime + cutime + cstime) / clock_speed_hz)
                    process_time_sec = uptime_sec - float(stime / clock_speed_hz)

                    n = len(result)
                    if n != 0:
                        pre_cpu_time_sec = result[n - 1]["cpuTimeSec"]
                        pre_process_time_sec = result[n - 1]["processTimeSec"]
                        avg_usage_percent = 100 * (
                                (cpu_time_sec - pre_cpu_time_sec) / (process_time_sec - pre_process_time_sec))
                        avg_usage_percent = avg_usage_percent / cores
                        avg_usage_percent = ((int)(avg_usage_percent * 100)) / 100.0
                    else:
                        pre_cpu_time_sec = -1
                        pre_process_time_sec = -1
                        avg_usage_percent = -1

                    if n != 0 and (cpu_time_sec == pre_cpu_time_sec or uptime_sec == result[n - 1]["uptimeSec"]):
                        continue

                    result.append({
                        "clockSpeedHz": clock_speed_hz,
                        "uptimeSec": uptime_sec,
                        "utime": utime,
                        "stime": stime,
                        "cutime": cutime,
                        "cstime": cstime,
                        "starttime": starttime,
                        "cpuTimeSec": cpu_time_sec,
                        "processTimeSec": process_time_sec,
                        "precpuTimeSec": pre_cpu_time_sec,
                        "preprocessTimeSec": pre_process_time_sec,
                        "avgUsagePercent": avg_usage_percent
                    })
                else:
                    msg = "One or more values not found in ZCC_Android logs"
                    status = False
                    return status, msg
            else:
                msg = "'starttime' value not found in ZCC_Android logs"
                status = False
                return status, msg

    return status, msg, result

def helper_get_cpu_data_from_logs(logs_data:str):
    pattern = r"ZDX:setProcessDetails\s*({.*?})"
    matches = re.findall(pattern, logs_data)
    res = []
    # Parse matches as JSON
    for match in matches:
        data = json.loads(match)  # Convert the string into a Python dictionary
        res.append(data)

    if len(matches) ==0:
        return False, "No CPU data found in Tunnel Logs",res
    return True,"Data Found", res


class TestCpuUsageByZcc:

    @allure.title("setup")
    def setup_class(self):
        """
        1. Login into ZCC
        """
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.variables = conftest.variables
        self.logger_obj = conftest.logger_obj
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_device_stats.db"
        self.zcc = Zcc(start_app=True, log_handle=self.logger_obj)
        self.no_of_cores = self.sys_ops.get_no_cpu_cores()
        device_type = self.sys_ops.android_device_type()

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            result = self.zcc.clear_logs()
            assert result[0]
            result = self.zcc.logout()
            assert result[0]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]

        self.zcc.logger.info("Validating if ZIA service status is ON")
        result = self.zcc.verify_service_status(service_type="ZIA", service_status="on")
        assert result[0], result[1]

        self.zcc.logger.info("Validating if ZDX service status is ON")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="on")
        assert result[0], result[1]

        self.zcc.logger.info("Sleep 360s :: ZDX Data Collection")
        time.sleep(360)

        self.logger_obj.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.logger_obj.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        upm_logs_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,plugin='upm')
        tunnel_logs_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,plugin='tunnel')
        if upm_logs_data is None or tunnel_logs_data is None:
            msg = "Not able to fetch data from UPM and Tunnel log files"
            assert False, msg

        self.logger_obj.info("Fetching mon_processes data from Android logs")
        mon_data_status = helper_get_cpu_data_from_logs(logs_data=upm_logs_data)
        assert mon_data_status[0], mon_data_status[1]

        zdx_device_stats_retriever_data = helper_get_zdx_device_stats_retriever_data(log_data=tunnel_logs_data,cores=self.no_of_cores)
        assert zdx_device_stats_retriever_data[0], zdx_device_stats_retriever_data[1]

        self.mon_processes_data = mon_data_status[2][-6:-1]
        self.device_stats_retriever_data = zdx_device_stats_retriever_data[2][-6:-1]

        self.logger_obj.info(f"mon_processes data from Android logs")
        for data in self.mon_processes_data:
            self.logger_obj.info(f"{data}")

        self.logger_obj.info(f"zdx_device_stats_retriever_data data from Android logs")
        for data in self.device_stats_retriever_data:
            self.logger_obj.info(f"{data}")

        self.logger_obj.info("Creating connection to DB")
        self.db_handler.create_db_connection(self.db_path)

    @add_markers("regression")
    @zcc_mark_version(min=3.8)
    @pytest.mark.parametrize("query, columns", inputs)
    def test_tbl_mon_processes_from_device_stats_db(self, query, columns):
        """
            This function verifies tbl_mon_processes includes cpu_user,cpu_kernel,cpu_total,cpu_total_pct
            values in the device_stats db

            It performs the following steps:
            1. Doing validation on all the mentioned columns
            Args:
                self (object): Instance of the test class.
                query to run for the table
                column of the table which needs to validate
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        allure.dynamic.title(f"Verify tbl_mon_processes includes {columns} values in the device_stats db")

        self.logger_obj.info(f"Verify tbl_mon_processes includes {columns} values in the device_stats db")

        column_mapping = {
            "cpu_user": "cpuUser",
            "cpu_kernel": "cpuKernel",
            "cpu_total_pct": "avgUsagePercent",
            "cpu_total": "cpu_total"
        }

        log_key = column_mapping.get(columns)
        mon_proc_data = self.mon_processes_data

        rows_data = self.db_handler.execute_query(query)
        if len(rows_data) < 5:
            assert False, f"Expected at least 5 data points, but got {len(rows_data)}"

        for db_value, log_value in zip(reversed(rows_data), reversed(mon_proc_data)):
            if log_key == "cpu_total":
                log_value = log_value["cpuUser"] + log_value["cpuKernel"]
            else:
                log_value = log_value[log_key]

            self.logger_obj.info(f"Validating {columns} Values from DB::Logs -> {db_value[columns]}::{log_value}")
            assert db_value[
                       columns] == log_value, f"Values from DB::Logs -> {db_value[columns]}::{log_value} are not matching"

    @add_markers("regression")
    @zcc_mark_version(min=3.8)
    @pytest.mark.parametrize("value", data_validation_inputs)
    def test_cpu_user_cpu_kernel_and_cpu_total_values_from_logs(self, value):
        """
            This function Verify cpu_user,cpu_user,cpu_kernel,cpu_total values are calculated correctly from logs

            It performs the following steps:
            1. Doing validation on all the mentioned values
               cpu_user should be equal to utime+cutime
               cpu_kernel should be equal to stime+cstime
               cpu_total should be equal to cpu_user+cpu_kernel
            Args:
                self (object): Instance of the test class.
                query to run for the table
                column of the table which needs to validate
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        allure.dynamic.title(f"Verify {value} values are calculated correctly from logs")

        self.logger_obj.info(f"Verify {value} values are calculated correctly from logs")

        calculated_data_values = self.mon_processes_data
        calculated_data = calculated_data_values[::-1]  # Using reverse list to check from latest data first

        raw_data_values = self.device_stats_retriever_data
        raw_data = raw_data_values[::-1]  # Using reverse list to check from latest data first

        for calculated, raw in zip(calculated_data, raw_data):
            if value == "cpu_total":
                calculated_val = calculated["cpuUser"] + calculated["cpuKernel"]
                raw_val = raw['utime'] + raw['stime'] + raw['cutime'] + raw['cstime']
            elif value == "cpuUser":
                calculated_val = calculated[value]
                raw_val = raw['utime'] + raw['cutime']
            else:
                calculated_val = calculated[value]
                raw_val = raw['stime'] + raw['cstime']

            self.logger_obj.info(
                f"Validating db/log {value}:: Values calculated using raw values --> {calculated_val}::{raw_val}")
            assert calculated_val == raw_val, f"Values from Logs -> {calculated_val}::{raw_val} are not matching"

    @allure.title("Verify cpu_total_pct values are calculated correctly from logs")
    @pytest.mark.xray("QA-305563")
    @add_markers("regression")
    @zcc_mark_version(min=3.8)
    def test_cpu_total_pct_calculation(self):
        """
            This function Verify cpu_total_pct value is calculated correctly from logs

            It performs the following steps:
            1. Doing validation on cpu_total_pct values which is calculated with this formula
               double avgUsagePercent = 100 * ((cpuTimeSec - preCpuTimeSec) /(processTimeSec - preProcessTimeSec.getAsDouble()));
               avgUsagePercent = avgUsagePercent/ numCores;

               where:
               cpuTimeSec = ((double) (utime + stime + cutime + cstime) / clockSpeedHz);
               uptimeSec = SystemClock.elapsedRealtime() / 1000.0;
               processTimeSec = uptimeSec - ((double) starttime / clockSpeedHz);

            Args:
                self (object): Instance of the test class.
                query to run for the table
                column of the table which needs to validate
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(f"Verify cpu_total_pct values are calculated correctly from logs")

        raw_data_values = self.device_stats_retriever_data
        raw_data = raw_data_values[::-1]  # Using reverse list to check from latest data first

        calculated_data_values = self.mon_processes_data
        calculated_data = calculated_data_values[::-1]  # Using reverse list to check from latest data first
        calculated_data.pop()
        for i in range(5, len(raw_data)):
            raw_data.pop()

        for calculated, raw in zip(calculated_data, raw_data):
            avg_usage_percent = round(raw["avgUsagePercent"], 2)
            avg_usage_cal = round(calculated["avgUsagePercent"], 2)

            if avg_usage_percent != avg_usage_cal:
                self.logger_obj.info(f"Calculated avg_usage_percent from raw values = {avg_usage_percent} and "
                                     f"inserted avg avg_usage_percent value = {avg_usage_cal}. Given some buffer for "
                                     f"calculation")
            else:
                self.logger_obj.info(f"Calculated avg_usage_percent from raw values = {avg_usage_percent} and"
                                     f" inserted avg avg_usage_percent value = {avg_usage_cal} ")

            if round(abs(avg_usage_percent - avg_usage_cal), 2) > 0.01:
                assert False, f"Values from Logs -> {avg_usage_percent}::{avg_usage_cal} are not matching"

    @allure.title("Verify setProcessDetails from Android logs have all the values")
    @pytest.mark.xray("QA-305568")
    @add_markers("regression")
    @zcc_mark_version(min=3.8)
    def test_set_processes_details_includes_all_the_values(self):
        """
            This function Verify setProcessDetails includes all the values

            ZDX:setProcessDetails {"pid":12298,"name":"zscaler.com.zscaler","avgUsagePercent":0.18,
            "processTimeSec":279.8500000000022,"cpuUser":2976,"cpuKernel":578,"memSize":156971008,"memUsagePct":2.12}

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(f"Verify setProcessDetails from Android logs have all the required values")
        required_keys = ['pid', 'name', 'avgUsagePercent', 'processTimeSec',
                         'cpuUser', 'cpuKernel', 'memSize', 'memUsagePct']

        for data in self.mon_processes_data:
            if not all(key in data for key in required_keys):
                missing_keys = [key for key in required_keys if key not in data]
                assert False, f"Missing keys in setProcessDetails: {', '.join(missing_keys)}"

        self.logger_obj.info(f"Verified all the key: {', '.join(required_keys)} included in setProcessDetails")

    @allure.title("Verify correct values of mem_wss_bytes and mem_wss_pct (not default -1) in tbl_mon_processes")
    @pytest.mark.xray(["QA-305572", "QA-305565"])
    @zcc_mark_version(min=3.7)
    @add_markers("regression")
    def test_validate_tbl_mon_processes_for_mem_wss_bytes_and_mem_wss_pct(self):
        """
            This function is validating data of ZDX:setProcessDetails  to the device stats db data

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info(
            "Verify correct values of mem_wss_bytes and mem_wss_pct (not default -1) in tbl_mon_processes")
        status = True
        msg = ""

        set_processes_details = self.mon_processes_data
        length = len(set_processes_details)

        query = "select * from tbl_mon_processes"
        row_data = self.db_handler.execute_query(query)

        for index in range(min(length,len(row_data)) - 1, -1, -1):
            # for [timestamp,refid,pid,name,cpu_user,cpu_kernel,cpu_total,cpu_total_pct,mem_wss_bytes,mem_wss_pct]
            timestamp = row_data[index]['timestamp']
            pid_from_db = row_data[index]['pid']
            name_from_db = row_data[index]['name']
            mem_wss_bytes_from_db = row_data[index]['mem_wss_bytes']
            mem_wss_pct_from_db = round(float(row_data[index]['mem_wss_pct']), 2)

            pid_from_log = set_processes_details[index]['pid']
            name_from_log = set_processes_details[index]['name']
            mem_wss_bytes_from_log = set_processes_details[index]['memSize']
            mem_wss_pct_from_log = round(float(set_processes_details[index]['memUsagePct']), 2)

            self.logger_obj.info(
                f"Validating 'pid', 'name', 'mem_wss_bytes', 'mem_wss_pct' values from ZCC_Android "
                f"logs and table tbl_mon_processes of device_stats db for timestamp = {timestamp}")

            if pid_from_log != pid_from_db:
                status = False
                msg = (f"[DB:LOG]:[{pid_from_db}:{pid_from_log}] pid not matching "
                       f"at timestamp: {timestamp}")
                break

            if name_from_log != name_from_db:
                status = False
                msg = (f"[DB:LOG]:[{name_from_db}:{name_from_log}] name of service is not matching "
                       f"at timestamp: {timestamp}")
                break

            if int(mem_wss_bytes_from_db) != int(mem_wss_bytes_from_log):
                status = False
                msg = (f"[DB:LOG]:[{mem_wss_bytes_from_db}:{mem_wss_bytes_from_log}] mem_wss_bytes is not matching "
                       f"at timestamp: {timestamp}")
                break

            if mem_wss_pct_from_db != mem_wss_pct_from_log:
                status = False
                msg = (f"[DB:DB]:[{mem_wss_pct_from_db}:{mem_wss_pct_from_log}] mem_wss_pct is not matching for "
                       f"at timestamp: {timestamp}")
                break

        assert status, msg

    @allure.title(
        "Verify cpu_user, cpu_kernel, cpu_total values keeps on increasing, it is written in a way that it keeps on increasing")
    @pytest.mark.xray("QA-305569")
    @add_markers("regression")
    @zcc_mark_version(min=3.8)
    def test_cpu_user_kernel_and_total_values_are_incrementing(self):
        """
            This function verifies cpu_user, cpu_kernel, cpu_total values keeps on increasing, it is written in a way that it keeps on increasing

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(f"Verify cpu_user, cpu_kernel, cpu_total values keeps on increasing, it is written in"
                             f" a way that it keeps on increasing")

        query = "select * from tbl_mon_processes"
        rows_data = self.db_handler.execute_query(query)

        for index, row in enumerate(rows_data[1:], start=1):
            prev_row = rows_data[index - 1]
            timestamp, _, _, _, cpu_user, cpu_kernel, cpu_total, _, _, _ = row
            prev_timestamp, _, _, _, prev_cpu_user, prev_cpu_kernel, prev_cpu_total, _, _, _ = prev_row

            if any(current < prev for current, prev in
                   zip((cpu_user, cpu_kernel, cpu_total), (prev_cpu_user, prev_cpu_kernel, prev_cpu_total))):
                assert False, f"Current value is lesser than previous value, which is incorrect for timestamp = {timestamp}"
            else:
                self.logger_obj.info(
                    f"Values are increasing between timestamp = {timestamp} and prev timestamp = {prev_timestamp}")

    @allure.title("ZDX-restart Verify mon_process values keeps on increasing after zdx restart ")
    @pytest.mark.xray("QA-305570")
    @add_markers("regression")
    @zcc_mark_version(min=3.8)
    def test_mon_process_values_on_zdx_restart(self):
        """
            This function verifies cpu_user, cpu_kernel values keeps on increasing after zdx restart

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(f"ZDX-restart Verify mon_process values keeps on increasing after zdx restart")

        self.db_handler.close_db_connection()  # Closing already opened db connection
        self.logger_obj.info("Creating connection to DB")
        self.db_handler.create_db_connection(self.db_path)

        query = "select * from tbl_mon_processes"
        rows_data = self.db_handler.execute_query(query)
        row_count_before_restart_zdx = len(rows_data)
        self.db_handler.close_db_connection()

        self.zcc.logger.info(" Restart ZDX ")
        result = self.zcc.restart_zdx_service()
        assert result[0], result[1]

        self.zcc.logger.info(" Sleep 30s:: for ZDX data collection")
        time.sleep(30)

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.logger_obj.info("Creating connection to DB")
        self.db_handler.create_db_connection(self.db_path)
        rows_data = self.db_handler.execute_query(query)
        self.db_handler.close_db_connection()
        row_count_after_restart_zdx = len(rows_data)

        if row_count_before_restart_zdx >= row_count_after_restart_zdx:
            assert False, "No new data inserted after restart ZDX service"

        for index, row in enumerate(rows_data[1:], start=1):
            prev_row = rows_data[index - 1]
            timestamp, _, pid, _, cpu_user, cpu_kernel, cpu_total, _, _, _ = row
            prev_timestamp, _, prev_pid, _, prev_cpu_user, prev_cpu_kernel, prev_cpu_total, _, _, _ = prev_row

            if any(current < prev for current, prev in
                   zip((cpu_user, cpu_kernel, cpu_total), (prev_cpu_user, prev_cpu_kernel, prev_cpu_total))):
                assert False, f"Current value is lesser than previous value, which is incorrect for timestamp = {timestamp}"
            else:
                self.logger_obj.info(
                    f"Values are increasing between timestamp = {timestamp} and prev timestamp = {prev_timestamp}")

            if prev_pid != pid:
                assert False, f"'pid' should not change on restart zdx, prev pid = {prev_pid} and current pid ={pid}"

    @allure.title("Device-restart/ZCC kill Verify mon_process values should restart")
    @pytest.mark.xray("QA-305571")
    @add_markers("regression")
    @zcc_mark_version(min=3.8)
    def test_mon_process_values_on_zcc_kill(self):
        """
            This function verifies mon_process values should restart on Dcevice-restart/ZCC kill

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(f"Device-restart/ZCC kill Verify mon_process values should restart")

        self.logger_obj.info("Creating connection to DB")
        self.db_handler.create_db_connection(self.db_path)
        query = "select * from tbl_mon_processes"
        rows_data = self.db_handler.execute_query(query)
        row_count_before_zcc_kill = len(rows_data)
        self.db_handler.close_db_connection()

        self.logger_obj.info("Killing the ZApp")
        result = self.zcc.kill_zcc()
        assert result[0], result[1]
        time.sleep(2)

        self.logger_obj.info("Restarting the ZApp and verifying ZIA status")
        self.zcc.ui.start_app(app=AndroidElements.APP, sleep_time=15)

        self.logger_obj.info("Validating if ZDX service status is ON")
        result = self.zcc.verify_service_status(service_status="on", service_type='ZDX')
        assert result[0], result[1]

        self.zcc.logger.info(" Sleep 30s:: for ZDX data collection")
        time.sleep(30)

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.logger_obj.info("Creating connection to DB")
        self.db_handler.create_db_connection(self.db_path)
        rows_data = self.db_handler.execute_query(query)
        row_count_after_zcc_kill = len(rows_data)
        self.db_handler.close_db_connection()

        if row_count_before_zcc_kill >= row_count_after_zcc_kill:
            assert False, "No new data inserted afterZCC kill"

        count = 0
        msg = ""

        for index, row in enumerate(rows_data[1:], start=1):
            prev_row = rows_data[index - 1]
            timestamp, _, pid, _, cpu_user, cpu_kernel, cpu_total, _, _, _ = row
            prev_timestamp, _, prev_pid, _, prev_cpu_user, prev_cpu_kernel, prev_cpu_total, _, _, _ = prev_row

            if all(current < prev for current, prev in zip((cpu_user, cpu_kernel, cpu_total), (
                    prev_cpu_user, prev_cpu_kernel, prev_cpu_total))) and prev_pid != pid:
                count += 1
                msg = (f"Current values is lesser than previous value, which is correct for timestamp = {timestamp}"
                       f" when 'pid' change on zcc kill, prev pid = {prev_pid} and current pid = {pid}")
            elif all(current > prev for current, prev in zip((cpu_user, cpu_kernel, cpu_total), (
                    prev_cpu_user, prev_cpu_kernel, prev_cpu_total))) and prev_pid == pid:
                print_msg = (
                    f"Current values is greater than previous value, which is correct for timestamp = {timestamp}"
                    f"when 'pid' is not changed, prev pid = {prev_pid} and current pid = {pid}")
                self.logger_obj.info(f"{print_msg}")
            else:
                assert False, "Values are not as expected"

        assert count == 1, "Values are not reset after ZCC kill and restart "
        self.logger_obj.info(f"{msg}")

    @allure.title("teardown")
    def teardown_class(self):
        """
        1. Logout from zcc
        """

        self.logger_obj.info("Closing connection to DB")
        self.db_handler.close_db_connection()

        self.logger_obj.info("Logout from ZCC")
        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

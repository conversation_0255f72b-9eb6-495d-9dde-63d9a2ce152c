import json, os
import time
import pytest, allure
import os, shutil
import sys
import platform
import requests, socket

from common_lib.mobileadmin.deviceoverview import DeviceOverview
from common_lib.mobileadmin.appprofile import AppProfile
from common_lib.mobileadmin.forwardingprofile import ForwardingProfile
from common_lib.common.log_ops import *
from OS_Android.library.ops_system import SysOps
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
from common_lib.mobileadmin.trustednetwork import TrustedNetwork
from OS_Android.library.database_parse import <PERSON><PERSON><PERSON>and<PERSON>
from OS_Android.library.database_parse import LogHandler
from OS_Android.library.zdx_admin import ZDX_ADMIN
from common_lib.adminzia import ziapac

config_path = os.path.join(os.getcwd(), "config", os.environ.get("CONFIG_FILE"))
custom_config_path = os.getcwd() + (f"\\OS_Android\\test_suite\\zcc_zdx\\zdx_regression\\bypassed_website_cases\\Config\\custom_config.json")

with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)

with open(custom_config_path) as file:
    custom_config_json = json.load(file)

# Proxy ip, squid proxy, running on VM of devraj (<EMAIL>)
external_proxy_ip = "***********"
external_proxy_port = 3128
test_url = "http://www.google.com"

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log", log_level="DEBUG")
device_overview = DeviceOverview(variables["CLOUD"], os.environ.get("CONFIG_FILE"), log_handle=logger_obj)
const_obj = constants.Utils()
sys_ops = SysOps(logger_obj, variables["CLOUD"])
export_logs = FetchAndroidLogs(logger_obj)
log_ops = LogOps(log_handle=logger_obj)
pcap_ops = PcapOps(log_handle=logger_obj)
zia_pac = ziapac.ZiaPac(variables["CLOUD"], os.environ.get("CONFIG_FILE"), log_handle=logger_obj)

app_profile_obj = AppProfile(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)
forwarding_profile_obj = ForwardingProfile(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)
trusted_network = TrustedNetwork(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)
zdx_admin = ZDX_ADMIN(cloudname=variables["CLOUD_DETAILS"]["ZDX"], configfile=variables,
                      logger=logger_obj)

db_handler = DbHandler()
log_handler = LogHandler()

url_direct = 'https://zscaler.zoom.us'
domain_direct = 'zscaler.zoom.us'

pac_content = '''    
        function FindProxyForURL(url, host) {
            if (shExpMatch(host, "*zoom*") || shExpMatch(url, "*********")) {
                return "DIRECT";
            }

            /* Default Traffic Forwarding. Forwarding to Zen on port 80. */
            return "PROXY ${GATEWAY_FX}:80; PROXY ${SECONDARY_GATEWAY_FX}:80; DIRECT";
        } '''

timestamp = time.strftime("%Y%m%d_%H%M%S")
pac_name = f"Automated_Pac_Bypassed_{timestamp}"

@allure.title("Base teardown")
def make_teardown():
    """
    1. Delete the Pac file
    2. Delete App and Forwarding profile
    3. Delete monitors form ZDX admin UI
    """
    logger_obj.info("Deleting the Pac File")
    res = zia_pac.delete_pac_file(name=pac_name)
    logger_obj.info(res[1])

    logger_obj.info("Deleting App profile")
    try:
        result = app_profile_obj.delete_app_profile(operating_system="Android")
        assert result[0], result[1]
    except Exception as e:
        logger_obj.info(str(e))

    logger_obj.info("Deleting Forwarding profile")
    try:
        result = forwarding_profile_obj.delete_forwarding_profile()
        assert result[0], result[1]
    except Exception as e:
        logger_obj.info(str(e))

    logger_obj.info("Deleting webprobe from ZDX ADMIN UI for bypassed_website_cases")
    zdx_admin.delete_application_and_monitors_support_monitor_dropped(configs=custom_config_json)


@allure.title("setup")
@pytest.fixture(autouse=True, scope="package")
def base_setup(request):
    """
    1. Create App and Forwarding profile
    2. Create Pac Fie to bypass the domains
    3. Get the url for the Pac file
    4. Edit Forwarding Profile
    5. Edit App Profile
    6. Create webprobes and traceroute for testing
    """

    try:

        logger_obj.info("Deleting App profile")
        try:
            result = app_profile_obj.delete_app_profile(operating_system="Android")
            assert result[0], result[1]
        except Exception as e:
            logger_obj.info(str(e))

        logger_obj.info("Deleting Forwarding profile")
        try:
            result = forwarding_profile_obj.delete_forwarding_profile()
            assert result[0], result[1]
        except Exception as e:
            logger_obj.info(str(e))

        logger_obj.info("Creating Forwarding profile")
        result = forwarding_profile_obj.create_forwarding_profile()
        assert result[0], result[1]

        logger_obj.info("Creating App profile")
        result = app_profile_obj.create_app_profile(operating_system="Android")
        assert result[0], result[1]

        logger_obj.info("Creating Pac File")
        res = zia_pac.create_pac_file(name=pac_name, pac_content=pac_content)
        logger_obj.info(res[1])
        assert res[0], res[1]

        logger_obj.info("Getting and Storing the URL of PAC file")
        res = zia_pac.get_pac_url(name=pac_name)
        logger_obj.info(res[1])
        assert res[0], res[1]
        pac_url = res[-1]

        logger_obj.info("Editing App Profile")
        result = app_profile_obj.edit_app_profile(pac=pac_url, operating_system='Android')
        logger_obj.info(result[1])
        assert res[0], res[1]

        logger_obj.info("Creating webmonitor and traceroute for bypassed_website_cases ")
        zdx_admin.create_application_and_monitors_support_monitor_dropped(configs=custom_config_json)

    except AssertionError as e:
        request.addfinalizer(make_teardown)
        pytest.skip(f"setup failed reason:{e}")
    else:
        request.addfinalizer(make_teardown)


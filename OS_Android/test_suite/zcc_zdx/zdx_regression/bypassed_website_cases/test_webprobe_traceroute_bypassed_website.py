import pytest, time
import os, sys, re
import allure, json
import requests
from common_lib.Custom_Markers import add_markers, zcc_mark_version, zdx_mark_version  # Required!
from OS_Android.test_suite.zcc_zdx.zdx_regression.bypassed_website_cases import conftest
from OS_Android.library.ui_zcc import Zcc

const_obj = conftest.const_obj
export_logs = conftest.export_logs
db_handler = conftest.db_handler
log_handler = conftest.log_handler
variables = conftest.variables
logger_obj = conftest.logger_obj
webprobe_db_path = const_obj.LOG_PATH + f"\\upm_webload.db"
traceroute_db_path = const_obj.LOG_PATH + f"\\upm_traceroute.db"


direct_url = conftest.url_direct
direct_domain = conftest.domain_direct

test_input_tunnel_mode = [
    pytest.param(f"select Availability from WebData WHERE URL='{direct_url}'", 'Availability', 100,
                 marks=pytest.mark.xray(["QA-149172", "QA-149174"])),
    pytest.param(f"select ProxyHost from WebData WHERE URL='{direct_url}'", 'ProxyHost', "",
                 marks=pytest.mark.xray(["QA-149172", "QA-149174"])),
    pytest.param(f"select ProxyPort from WebData WHERE URL='{direct_url}'", 'ProxyPort', 0,
                 marks=pytest.mark.xray(["QA-149172", "QA-149174"])),
    pytest.param(f"select StatusCode from WebData WHERE URL='{direct_url}'", 'StatusCode', '200',
                 marks=pytest.mark.xray(["QA-149172", "QA-149174"])),
    pytest.param(f"select SMEID from WebData WHERE URL='{direct_url}'", 'SMEID', 0,
                 marks=pytest.mark.xray(["QA-149172", "QA-149174"])),
]

test_input_tunnel_none_mode = [
    pytest.param(f"select Availability from WebData WHERE URL='{direct_url}'", 'Availability', 100,
                 marks=pytest.mark.xray(["QA-149200", "QA-149207"])),
    pytest.param(f"select ProxyHost from WebData WHERE URL='{direct_url}'", 'ProxyHost', "",
                 marks=pytest.mark.xray(["QA-149200", "QA-149207"])),
    pytest.param(f"select ProxyPort from WebData WHERE URL='{direct_url}'", 'ProxyPort', 0,
                 marks=pytest.mark.xray(["QA-149200", "QA-149207"])),
    pytest.param(f"select StatusCode from WebData WHERE URL='{direct_url}'", 'StatusCode', '200',
                 marks=pytest.mark.xray(["QA-149200", "QA-149207"])),
    pytest.param(f"select SMEID from WebData WHERE URL='{direct_url}'", 'SMEID', 0,
                 marks=pytest.mark.xray(["QA-149200", "QA-149207"])),
]
test_input_zia_off_mode = [
    pytest.param(f"select Availability from WebData WHERE URL='{direct_url}'", 'Availability', 100,
                 marks=pytest.mark.xray(["QA-149186", "QA-149190"])),
    pytest.param(f"select ProxyHost from WebData WHERE URL='{direct_url}'", 'ProxyHost', "",
                 marks=pytest.mark.xray(["QA-149186", "QA-149190"])),
    pytest.param(f"select ProxyPort from WebData WHERE URL='{direct_url}'", 'ProxyPort', 0,
                 marks=pytest.mark.xray(["QA-149186", "QA-149190"])),
    pytest.param(f"select StatusCode from WebData WHERE URL='{direct_url}'", 'StatusCode', '200',
                 marks=pytest.mark.xray(["QA-149186", "QA-149190"])),
    pytest.param(f"select SMEID from WebData WHERE URL='{direct_url}'", 'SMEID', 0,
                 marks=pytest.mark.xray(["QA-149186", "QA-149190"])),
]


@pytest.fixture(scope="module")
def helper_functions():
    return HelperFunctions()


class HelperFunctions:

    def verify_upm_webload_bypassed_website(self, query, key, value):
        """
        This function tests the validation of device profile.
        It performs the following steps:
        1. Doing validation on webload db table for zdx
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        logger_obj.info("Sending query of data validation with specific column name")

        logger_obj.info("Creating webload db connection")
        db_handler.create_db_connection(webprobe_db_path)
        output = db_handler.execute_query(query)
        logger_obj.info("Closing db connection after fetching output data")
        db_handler.close_db_connection()

        if len(output) == 0:
            return False, "Data is not collected in the db, wait for longer period"

        db_value = output[0][key]
        logger_obj.info(f"validating {db_value} value in db ")

        if db_value == value:
            return True, ""
        return False, f"Failed to verify: Expected:: db_value - {value}::{db_value}"

    def verify_webprobe_for_bypassed_website_direct(self):
        """
            Verify proxy info is not included in web load json - when bypassed: DIRECT

            It performs the following steps:
            1. Doing validation on webload db for configured URL
               It will check whether given url going via DIRECT, and it will also check proxy Host data for same
            2. Access the application specified in the pac file
            3. Search for the logs to verify that domains specified in the Pac file are bypassing

            Args:
                self (object): Instance of the test class.
                Name of the table which needs to validate
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        status = False
        webprobe_log_data = log_handler.get_latest_file_data_with_plugin(path=const_obj.LOG_PATH,
                                                                              plugin='web')

        query = f"select ProxyHost from WebData WHERE URL='{direct_url}'"

        logger_obj.info("Creating webload db connection")
        db_handler.create_db_connection(webprobe_db_path)
        output = db_handler.execute_query(query)
        logger_obj.info("Closing db connection after fetching output data")
        db_handler.close_db_connection()

        logger_obj.info(f"Validating proxy ip should be empty when webprobe going direct")
        if output[0]['ProxyHost'] != "":
            return False, f"ProxyHost contains value ={output[0]['ProxyHost']} and expected value is empty"

        logger_obj.info(f"Validated proxy ip should be empty when webprobe going direct")

        logger_obj.info(f"Checking webprobe service discovery for url={direct_url} to validate 'Service type' "
                             f"and 'SmeIp' when going Direct")
        for item in webprobe_log_data.split("\n"):
            if direct_url in item and "Service type: DIRECT" in item and "SmeIp:  SmePort" in item:
                status = True
                break

        if not status:
            return False, "Service discovery with Service type: DIRECT not found in logs for bypassed website"

        logger_obj.info(
            f"Verified webprobe service discovery for url={direct_url} to validate 'Service type' and 'SmeIp' when "
            f"webprobe going through Direct")
        return True, "Successfully verified"

    def verify_webprobe_verify_DNSTime_ns_HTTPLatency_ns_PageFetchTime_ns(self):

        """
            This function tests the validation of correct value for: 'DNSTime_ns', 'HTTPLatency_ns','PageFetchTime_ns'
            It performs the following steps:
            1. Doing validation on 'DNSTime_ns', 'HTTPLatency_ns','PageFetchTime_ns' for webload for zdx
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        currentrow = 1
        query = "SELECT * from WebData"
        columns_to_validate = ['DNSTime_ns', 'HTTPLatency_ns', 'PageFetchTime_ns']

        logger_obj.info("Creating webload db connection")
        db_handler.create_db_connection(webprobe_db_path)
        rows = db_handler.execute_query(query)
        logger_obj.info("Closing db connection after fetching output data")
        db_handler.close_db_connection()

        for item in rows:
            for col in columns_to_validate:
                data = item[col]

                logger_obj.info(f"Validating {col} from Webload DB for Webprobe = {currentrow}")

                if len(str(data)) == 0:
                    return False, f"No value found at {col} in row = {currentrow}"

                logger_obj.info(f"Checking values of {col} for Webprobe = {currentrow}")

                if len(str(data)) >= 1 and int(data) <= 0:
                    return False, f"In row = {currentrow} , {col} value is {data} which is invalid"

                currentrow += 1

        return True, "Successfully Verified"

    def verify_maxhopcount_hopstodest_unresponsivehopcount_from_traceroute_db(self):
        """
             This function verifies that in Traceroute probes the value of 'hopstodest'
             should not be greater than 'maxhopcount'. It also verifies that in Traceroute
             probes the value of 'unresponsivehopcount' should not be greater than 'hopstodest'

             It performs the following steps:
             1. It is validating maxhopcount, hopstodest and unresponsivehopcount values in traceroute db
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        query = f"SELECT * from trmain WHERE domain='{direct_domain}'"
        current_row = 1
        complete_traceroute = False

        logger_obj.info("Creating traceroute db connection")
        db_handler.create_db_connection(traceroute_db_path)
        rows_data = db_handler.execute_query(query)
        logger_obj.info("Closing db connection after fetching output data")
        db_handler.close_db_connection()

        logger_obj.info("ZIA TRACEROUTE TUNNEL MODE - Bypassed DIRECT: Verifying that in Traceroute probes"
                             "the value of'hopstodest'should not be greater than 'maxhopcount'")

        if len(rows_data) == 0:
            return False, "No zia traceroute data found in traceroute.db"

        for data in rows_data:
            domain = data['domain']
            status = data['status']
            hopstodest = data['hopstodest']
            maxhopcount = data['maxhopcount']
            unresponsivehopcount = data['unresponsivehopcount']

            if status == 'Traceroute Stopped':
                continue

            if len(domain) == 0:
                return False, f"No value found at 'domain' column in row = {current_row}"

            if len(str(hopstodest)) == 0 or (len(str(hopstodest)) >= 1 and int(hopstodest) <= 0):
                return False, f"No or Invalid value found at 'hopstodest' in row = {current_row}"

            if len(str(maxhopcount)) == 0 or (len(str(maxhopcount)) >= 1 and int(maxhopcount) <= 0):
                return False, f"No or Invalid value found at maxhopcount in row = {current_row}"

            if len(str(unresponsivehopcount)) == 0 or (
                    len(str(unresponsivehopcount)) >= 1 and int(unresponsivehopcount) < 0):
                return False, f"No or Invalid value found at maxhopcount in row = {current_row}"

            if len(str(hopstodest)) >= 1 and len(str(maxhopcount)) >= 1 and int(hopstodest) > 40:
                return False, f"The value of 'hopstodest' = {hopstodest} is greater than 40 in row = {current_row} which in Invalid"

            if len(str(hopstodest)) >= 1 and len(str(maxhopcount)) >= 1 and (int(hopstodest) > int(maxhopcount)):
                msg = (f"The value of 'hopstodest' = {hopstodest} is greater than"
                       f"maxhopcount = {maxhopcount} in row = {current_row} which in Incorrect")
                return False, msg

            if len(str(unresponsivehopcount)) >= 1 and len(str(hopstodest)) >= 1 and int(hopstodest) < int(
                    unresponsivehopcount):
                return False, f"The value of 'unresponsivehopcount' is greater than 'hopstodest' in row = {current_row} which is inalid"

            current_row += 1
            complete_traceroute = True

        logger_obj.info("Checking if any complete traceroute data found in the db or not")
        if not complete_traceroute:
            return False, "No complete traceroute data found in traceroute db"

        logger_obj.info("Validating hopstodest should be less than maxhopcount and"
                             "'unresponsivehopcount' should be less than equal to 'hopstodest'")
        return True, "Successfully verified"

    def verify_totalloss_totallatency_values_from_traceroute_db(self):
        """
             This function verifies that in Traceroute probes the value of 'totalloss'
             and 'totallatency' should not be invalid

             It performs the following steps:
             1. It is validating 'totalloss', and 'totallatency' values in traceroute db
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        logger_obj.info(
            "ZIA TRACEROUTE TUNNEL MODE Bypassed Website: Verifying 'totalloss' and 'totallatency' values from traceroute DB")

        query = f"SELECT * from trmain WHERE domain='{direct_domain}'"
        currentrow = 1
        complete_traceroute = False

        logger_obj.info("Creating traceroute db connection")
        db_handler.create_db_connection(traceroute_db_path)
        rows_data = db_handler.execute_query(query)
        logger_obj.info("Closing db connection after fetching output data")
        db_handler.close_db_connection()

        if len(rows_data) == 0:
            return False, "No zia traceroute data found in traceroute.db"

        for data in rows_data:
            status = data['status']
            totalloss = data['totalloss']
            totallatency = data['totallatency']

            if status == 'Traceroute Stopped':
                continue

            if len(str(totalloss)) == 0 or int(totalloss) < -1 or int(totalloss) >= 101:
                return False, f"No or invalid found at 'totalloss' = {totalloss} in row = {currentrow}"

            if len(str(totallatency)) == 0 or int(totallatency) < -1:
                return False, f"No or value found at 'totallatency' = {totallatency} in row = {currentrow}"

            if int(totallatency) >= 201:
                logger_obj.warning(f"In row = {currentrow}, the 'totallatency' value = {totallatency} is too high")

            complete_traceroute = True
            currentrow += 1

        logger_obj.info("Checking if any complete traceroute data found in the db or not")
        if not complete_traceroute:
            return False, "No complete traceroute data found in traceroute db"

        logger_obj.info("Validated 'totalloss' and 'totallatency' values from traceroute db")
        return True, "Successfully verified"

    def verify_zia_traceroute_probe_json_from_db(self):
        """
             This function verifies ZIA TR JSON of DIRECT case.

             It performs the following steps:
             1. It is validating 'app', 'id', 'hints'
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        currentrow = 0
        compelete_traceroute = False

        logger_obj.info(
            "ZIA TRACEROUTE TUNNEL MODE-Bypassed Website: Verify traceroute probe json from traceroute db")

        query = f"SELECT * from trmain WHERE domain='{direct_domain}'"

        logger_obj.info("Creating traceroute db connection")
        db_handler.create_db_connection(traceroute_db_path)
        rows_data = db_handler.execute_query(query)
        logger_obj.info("Closing db connection after fetching output data")
        db_handler.close_db_connection()

        if len(rows_data) == 0:
            return False, "No zia traceroute data found in traceroute.db"

        for row in rows_data:
            currentrow += 1
            data = ""
            json_data = ""
            data = data + row['json']

            if row['status'] == 'Traceroute Stopped':
                continue

            logger_obj.info(f"Validating JSON format for traceroute = {currentrow}")
            if "app" and "id" not in data:
                return False, f"Invalid JSON format in traceroute json in row = {currentrow}"

            try:
                json_data = json.loads(data)
            except json.JSONDecodeError as e:
                data = re.sub(r'([{,]\s*?"[^"]+"\s*:)\s*,', r'\1 null,', data)
                json_data = json.loads(data)  # {'A': 'a', 'B': None, 'C': 'c'}

            logger_obj.info(f"Validating 'hints' for traceroute = {currentrow}")
            if len(json_data['hints']) == 0 or json_data['hints'] is None:
                return False, f"In row = {currentrow} under 'JSON' column, No legs are present in the 'Hints' section"

            if json_data['hints']['zapp2egress'] is None or json_data['hints']['zapp2egress'] != 0:
                return False, f"In row = {currentrow} under 'JSON' column, No legs is present for 'zapp2egress' "

            if json_data['hints']['egress2dest'] is None or json_data['hints']['egress2dest'] != 1:
                return False, f"In row = {currentrow} under 'JSON' column, No legs is present for 'egress2dest' "

            logger_obj.info(f"Validating 'client_name', 'clt_ha_name','clt_ip','ecode',"
                                 f"'icode', 'etime' fields in json for traceroute = {currentrow}")

            for i in json_data:
                if "client_name" in i or "clt_ha_name" in i or "ecode" in i or "erefid" in i:
                    if len(str(json_data[i])) == 0 or json_data[i] is None:
                        return False, f"Invalid or no value in trace json for = {i} in row = {currentrow} of traceroute db"

                if "clt_ip" in i or "icode" in i or "is_zpa" in i or "etime" in i:
                    if len(str(json_data[i])) == 0 or json_data[i] is None:
                        return False, f"Invalid or no value in trace json for = {i} in row = {currentrow} of traceroute db"

            data = ""
            traceroute_legs = json_data["legs"]
            first_leg_tunnel_type = traceroute_legs[0]['tunnel_type']

            if first_leg_tunnel_type not in [1, 13, 21]:
                msg = (f"ZIA traceroute is not working fine for tunnel NONE mode, showing incorrect tunnel_type ="
                       f"{first_leg_tunnel_type}, it should be in ['1','13','21']")
                return False, msg

            for leg in traceroute_legs:
                source = leg['src']
                destination = leg['dst']
                dst_ip = leg['dst_ip']
                ecode = leg['ecode']
                loss = leg['loss']
                proto = leg['proto']
                src_ip = leg['src_ip']
                tunnel_type = leg['tunnel_type']
                num_hops = leg['num_hops']
                num_unresp_hops = leg['num_unresp_hops']

                logger_obj.info(f"Validating 'src_ip' value in json for traceroute = {currentrow}")
                if src_ip is None or len(str(src_ip)) == 0:
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'src_ip' value is missing")
                    return False, msg

                logger_obj.info(f"Validating 'dts_ip' value in json for traceroute = {currentrow}")
                if len(str(dst_ip)) == 0:
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'dst_ip' value is missing")
                    return False, msg
                else:
                    match = re.match(r"[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}", dst_ip)
                    if not bool(match):
                        msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                               f"'dst_ip' value is Invalid")
                        return False, msg

                logger_obj.info(f"Validating 'ecode' value in json for traceroute = {currentrow}")
                if ecode is None or len(str(ecode)) == 0:
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'ecode' value is missing")
                    return False, msg

                logger_obj.info(f"Validating 'loss' value in json for traceroute = {currentrow}")
                if loss is None or len(str(loss)) == 0:
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'loss' value is missing")
                    return False, msg

                logger_obj.info(f"Validating 'protocol' value in json for traceroute = {currentrow}")
                if proto is None or len(str(proto)) == 0:
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'proto' value is missing")
                    return False, msg

                logger_obj.info(f"Validating 'tunnel_type' value in json for traceroute = {currentrow}")
                if tunnel_type is None or len(str(tunnel_type)) == 0:
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'tunnel_type' value is missing")
                    return False, msg

                logger_obj.info(f"Validating 'num_hops' value in json for traceroute = {currentrow}")
                if num_hops is None or len(str(num_hops)) == 0:
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'nums_hops' value is missing")
                    return False, msg

                logger_obj.info(f"Validating 'num_unresp_hops' value in json for traceroute = {currentrow}")
                if num_unresp_hops is None or len(str(num_unresp_hops)) == 0:
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'nums_unresp_hops' value is missing")
                    return False, msg

            compelete_traceroute = True

        logger_obj.info("Checking if any complete traceroute data is present in the db or not")
        if not compelete_traceroute:
            return False, "No Complete traceroute data found in the db"

        logger_obj.info("Validating correctness of full json data for all zia traceroute from the db")
        return True, "Successfully verified"


class TestWebMtrBypassedWebsiteTunnelMode:

    @pytest.fixture(autouse=True)
    def setup_helper(self, helper_functions):
        self.helper = helper_functions

    @allure.title("setup")
    def setup_class(self):
        """
        1. Login into ZCC
        2. Check ZIA and ZDX on Status
        3. Collecting ZDX data
        4. Fetch and Export logs
        """

        self.forwarding_profile = conftest.forwarding_profile_obj
        self.export_logs = conftest.export_logs
        self.variables = conftest.variables
        self.zcc = Zcc(start_app=True, log_handle=logger_obj)

        logger_obj.info("Edit Forwarding profile")
        result = self.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert result[0], result[1]

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            result = self.zcc.update_policy()
            assert result[0]
            result = self.zcc.restart_zdx_service()
            assert result[0]
            result = self.zcc.clear_zdx_data()
            assert result[0]
        else:
            result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                    conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0], result[1]

        logger_obj.info("Validating if ZIA service status is ON")
        result = self.zcc.verify_service_status(service_type="ZIA", service_status="on")
        assert result[0], result[1]

        logger_obj.info("Validating if ZDX service status is ON")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="on")
        assert result[0], result[1]

        logger_obj.info("Sleep 5 mins :: Collecting ZDX db data data")
        time.sleep(300)

        logger_obj.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        logger_obj.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

    @allure.title("Z-Tunnel 1.0: Route Based: Verify all the web monitor data collected by ZApp in upm_webload.db "
                  "- Bypaased and non bypassed website both")
    @pytest.mark.parametrize("query, key, value", test_input_tunnel_mode)
    @add_markers("regression")
    def test_upm_webload_tunnel_mode_bypassed_website(self, query, key, value):
        """
        This function tests the validation of device profile.
        It performs the following steps:
        1. Doing validation on webload db table for zdx
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.helper.verify_upm_webload_bypassed_website(query=query, key=key, value=value)
        assert result[0], result[1]

    @allure.title("Verify proxy info is not included in web load json - Tunnel 1.0 - when bypassed: DIRECT")
    @add_markers("regression")
    @pytest.mark.xray(["QA-149172", "QA-149174"])
    def test_webprobe_for_bypassed_website_direct(self):
        """
            Verify proxy info is not included in web load json - Tunnel 1.0 - when bypassed: DIRECT

            It performs the following steps:
            1. Doing validation on webload db for configured URL
               It will check whether given url going via DIRECT, and it will also check proxy Host data for same
            2. Access the application specified in the pac file
            3. Search for the logs to verify that domains specified in the Pac file are bypassing

            Args:
                self (object): Instance of the test class.
                Name of the table which needs to validate
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        result = self.helper.verify_webprobe_for_bypassed_website_direct()
        assert result[0], result[1]

    @allure.title("Verify webload db  correct value for: 'DNSTime_ns', 'HTTPLatency_ns','PageFetchTime_ns' ")
    @pytest.mark.xray(["QA-149172", "QA-149174"])
    @add_markers("regression")
    def test_webprobe_verify_DNSTime_ns_HTTPLatency_ns_PageFetchTime_ns(self):

        """
            This function tests the validation of correct value for: 'DNSTime_ns', 'HTTPLatency_ns','PageFetchTime_ns'
            It performs the following steps:
            1. Doing validation on 'DNSTime_ns', 'HTTPLatency_ns','PageFetchTime_ns' for webload for zdx
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        result = self.helper.verify_webprobe_verify_DNSTime_ns_HTTPLatency_ns_PageFetchTime_ns()
        assert result[0], result[1]

    @allure.title("ZIA TRACEROUTE TUNNEL MODE - Bypassed: Verify maxhopcount and hopstodest count from traceroute db")
    @pytest.mark.xray(["QA-149136", "QA-149129"])
    @add_markers("regression")
    def test_maxhopcount_hopstodest_unresponsivehopcount_from_traceroute_db(self):
        """
             This function verifies that in Traceroute probes the value of 'hopstodest'
             should not be greater than 'maxhopcount'. It also verifies that in Traceroute
             probes the value of 'unresponsivehopcount' should not be greater than 'hopstodest'

             It performs the following steps:
             1. It is validating maxhopcount, hopstodest and unresponsivehopcount values in traceroute db
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        result = self.helper.verify_maxhopcount_hopstodest_unresponsivehopcount_from_traceroute_db()
        assert result[0], result[1]

    @allure.title("ZIA TRACEROUTE TUNNEL MODE - Bypassed Direct: Verify totalloss and totallatency from traceroute db")
    @pytest.mark.xray(["QA-149136", "QA-149129"])
    @add_markers("regression")
    def test_totalloss_totallatency_values_from_traceroute_db(self):
        """
             This function verifies that in Traceroute probes the value of 'totalloss'
             and 'totallatency' should not be invalid

             It performs the following steps:
             1. It is validating 'totalloss', and 'totallatency' values in traceroute db
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        result = self.helper.verify_totalloss_totallatency_values_from_traceroute_db()
        assert result[0], result[1]

    @allure.title("ZIA TRACEROUTE TUNNEL MODE Bypaased Website: Verify traceroute probe json from traceroute db")
    @pytest.mark.xray(["QA-149136", "QA-149129"])
    @add_markers("regression")
    def test_zia_traceroute_probe_json_from_db(self):
        """
             This function verifies ZIA TR JSON of DIRECT case (ZIA none mode).

             It performs the following steps:
             1. It is validating 'app', 'id', 'hints'
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        result = self.helper.verify_zia_traceroute_probe_json_from_db()
        assert result[0], result[1]

    @allure.title("teardown")
    def teardown_class(self):
        """
        1. Closing Db connection
        2. Logout from zcc
        """
        logger_obj.info("---------------------Completed for Tunnel 1 Mode-----------------------")


class TestWebMtrBypassedWebsiteZiaOff:

    @pytest.fixture(autouse=True)
    def setup_helper(self, helper_functions):
        self.helper = helper_functions

    @allure.title("setup")
    def setup_class(self):
        """
        1. Login into ZCC
        2. Check ZIA and ZDX on Status
        3. Collecting ZDX data
        4. Fetch and Export logs
        """

        self.export_logs = conftest.export_logs
        self.variables = conftest.variables
        self.zcc = Zcc(start_app=True, log_handle=logger_obj)

        logged_in = self.zcc.validate_zcc_logged_in()
        if not logged_in[0]:
            result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                    conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0], result[1]

        logger_obj.info("Validating if ZIA service status is ON")
        result = self.zcc.verify_service_status(service_type="ZIA", service_status="on")
        assert result[0], result[1]

        logger_obj.info("Turning ZIA off")
        result = self.zcc.toggle_service(service="ZIA", action=False)
        assert result[0], result[1]

        logger_obj.info("Restarting ZDX for fresh logs when ZIA is off")
        result = self.zcc.restart_zdx_service()
        assert result[0], result[1]

        logger_obj.info("Clearing ZDX data")
        result = self.zcc.clear_zdx_data()
        assert result[0], result[1]

        logger_obj.info("Sleep 5 mins :: Collecting ZDX db data tunnel None Mode")
        time.sleep(300)

        logger_obj.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        logger_obj.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

    @allure.title("Z-Tunnel ZIA OFF: Route Based: Verify all the web monitor data collected by ZApp in upm_webload.db "
                  " for Bypaased website ")
    @pytest.mark.parametrize("query, key, value", test_input_zia_off_mode)
    @add_markers("regression")
    def test_upm_webload_for_bypassed_website_zia_off(self, query, key, value):
        """
        This function tests the validation of device profile.
        It performs the following steps:
        1. Doing validation on webload db table for zdx
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.helper.verify_upm_webload_bypassed_website(query=query, key=key, value=value)
        assert result[0], result[1]

    @allure.title("Verify proxy info is not included in web load json - ZIA OFF - when bypassed: DIRECT")
    @add_markers("regression")
    @pytest.mark.xray(["QA-149186", "QA-149190"])
    def test_webprobe_for_bypassed_website_direct(self):
        """
            Verify proxy info is not included in web load json - ZIA OFF - when bypassed: DIRECT

            It performs the following steps:
            1. Doing validation on webload db for configured URL
               It will check whether given url going via DIRECT, and it will also check proxy Host data for same
            2. Access the application specified in the pac file
            3. Search for the logs to verify that domains specified in the Pac file are bypassing

            Args:
                self (object): Instance of the test class.
                Name of the table which needs to validate
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        result = self.helper.verify_webprobe_for_bypassed_website_direct()
        assert result[0], result[1]

    @allure.title("Verify webload db  correct value for: 'DNSTime_ns', 'HTTPLatency_ns','PageFetchTime_ns' ")
    @pytest.mark.xray(["QA-149186", "QA-149190"])
    @add_markers("regression")
    def test_webprobe_verify_DNSTime_ns_HTTPLatency_ns_PageFetchTime_ns(self):

        """
            This function tests the validation of correct value for: 'DNSTime_ns', 'HTTPLatency_ns','PageFetchTime_ns'
            It performs the following steps:
            1. Doing validation on 'DNSTime_ns', 'HTTPLatency_ns','PageFetchTime_ns' for webload for zdx
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        result = self.helper.verify_webprobe_verify_DNSTime_ns_HTTPLatency_ns_PageFetchTime_ns()
        assert result[0], result[1]

    @allure.title(
        "ZIA TRACEROUTE when ZIA is off - Bypassed Website: Verify maxhopcount and hopstodest count from traceroute db")
    @pytest.mark.xray(["QA-149145", "QA-149146"])
    @add_markers("regression")
    def test_maxhopcount_hopstodest_unresponsivehopcount_from_traceroute_db(self):
        """
             This function verifies that in Traceroute probes the value of 'hopstodest'
             should not be greater than 'maxhopcount'. It also verifies that in Traceroute
             probes the value of 'unresponsivehopcount' should not be greater than 'hopstodest'

             It performs the following steps:
             1. It is validating maxhopcount, hopstodest and unresponsivehopcount values in traceroute db
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        result = self.helper.verify_maxhopcount_hopstodest_unresponsivehopcount_from_traceroute_db()
        assert result[0], result[1]

    @allure.title(
        "ZIA TRACEROUTE when ZIA is off - Bypassed Website: Verify totalloss and totallatency from traceroute db")
    @pytest.mark.xray(["QA-149145", "QA-149146"])
    @add_markers("regression")
    def test_totalloss_totallatency_values_from_traceroute_db(self):
        """
             This function verifies that in Traceroute probes the value of 'totalloss'
             and 'totallatency' should not be invalid

             It performs the following steps:
             1. It is validating 'totalloss', and 'totallatency' values in traceroute db
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        result = self.helper.verify_totalloss_totallatency_values_from_traceroute_db()
        assert result[0], result[1]

    @allure.title("ZIA TRACEROUTE when ZIA is off for Bypaased Website: Verify traceroute probe json from traceroute db")
    @pytest.mark.xray(["QA-149145", "QA-149146"])
    @add_markers("regression")
    def test_zia_traceroute_probe_json_from_db(self):
        """
             This function verifies ZIA TR JSON of DIRECT case (ZIA none mode).

             It performs the following steps:
             1. It is validating 'app', 'id', 'hints'
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        result = self.helper.verify_zia_traceroute_probe_json_from_db()
        assert result[0], result[1]

    @allure.title("teardown")
    def teardown_class(self):
        """
        1. Closing Db connection
        2. Logout from zcc
        """

        logger_obj.info("Turning ZIA ON")
        result = self.zcc.toggle_service(service="ZIA", action=True)
        assert result[0], result[1]

        logger_obj.info("-----------------------Completed for Tunnel Mode ZIA Off----------------------------------")


class TestWebMtrBypassedWebsiteTunnelNoneMode:

    @pytest.fixture(autouse=True)
    def setup_helper(self, helper_functions):
        self.helper = helper_functions

    @allure.title("setup")
    def setup_class(self):
        """
        1. Login into ZCC
        2. Check ZIA and ZDX on Status
        3. Collecting ZDX data
        4. Fetch and Export logs
        """

        self.forwarding_profile = conftest.forwarding_profile_obj
        self.export_logs = conftest.export_logs
        self.variables = conftest.variables
        self.zcc = Zcc(start_app=True, log_handle=logger_obj)

        logger_obj.info("Edit Forwarding profile")
        result = self.forwarding_profile.edit_forwarding_profile(tunnel_mode="none")
        assert result[0], result[1]

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            result = self.zcc.update_policy()
            assert result[0]
            result = self.zcc.restart_zdx_service()
            assert result[0]
            result = self.zcc.clear_zdx_data()
            assert result[0]
        else:
            result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                    conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0], result[1]


        logger_obj.info("Validating if ZDX service status is ON")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="on")
        assert result[0], result[1]

        logger_obj.info("Sleep 5 mins :: Collecting ZDX db data tunnel None Mode")
        time.sleep(300)

        logger_obj.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        logger_obj.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

    @allure.title("Z-Tunnel None: Route Based: Verify all the web monitor data collected by ZApp in upm_webload.db "
                  " for Bypaased website ")
    @pytest.mark.parametrize("query, key, value", test_input_tunnel_none_mode)
    @add_markers("regression")
    def test_upm_webload_tunnel_mode_bypassed_website(self, query, key, value):
        """
        This function tests the validation of device profile.
        It performs the following steps:
        1. Doing validation on webload db table for zdx
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result = self.helper.verify_upm_webload_bypassed_website(query=query, key=key, value=value)
        assert result[0], result[1]

    @allure.title("Verify proxy info is not included in web load json - Tunnel None - when bypassed: DIRECT")
    @add_markers("regression")
    @pytest.mark.xray(["QA-149200", "QA-149207"])
    def test_webprobe_for_bypassed_website_direct(self):
        """
            Verify proxy info is not included in web load json - Tunnel None - when bypassed: DIRECT

            It performs the following steps:
            1. Doing validation on webload db for configured URL
               It will check whether given url going via DIRECT, and it will also check proxy Host data for same
            2. Access the application specified in the pac file
            3. Search for the logs to verify that domains specified in the Pac file are bypassing

            Args:
                self (object): Instance of the test class.
                Name of the table which needs to validate
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        result = self.helper.verify_webprobe_for_bypassed_website_direct()
        assert result[0], result[1]

    @allure.title("Verify webload db  correct value for: 'DNSTime_ns', 'HTTPLatency_ns','PageFetchTime_ns' ")
    @pytest.mark.xray(["QA-149200", "QA-149207"])
    @add_markers("regression")
    def test_webprobe_verify_DNSTime_ns_HTTPLatency_ns_PageFetchTime_ns(self):

        """
            This function tests the validation of correct value for: 'DNSTime_ns', 'HTTPLatency_ns','PageFetchTime_ns'
            It performs the following steps:
            1. Doing validation on 'DNSTime_ns', 'HTTPLatency_ns','PageFetchTime_ns' for webload for zdx
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        result = self.helper.verify_webprobe_verify_DNSTime_ns_HTTPLatency_ns_PageFetchTime_ns()
        assert result[0], result[1]

    @allure.title(
        "ZIA TRACEROUTE TUNNEL NONE MODE - Bypassed: Verify maxhopcount and hopstodest count from traceroute db")
    @pytest.mark.xray(["QA-149157", "QA-149164"])
    @add_markers("regression")
    def test_maxhopcount_hopstodest_unresponsivehopcount_from_traceroute_db(self):
        """
             This function verifies that in Traceroute probes the value of 'hopstodest'
             should not be greater than 'maxhopcount'. It also verifies that in Traceroute
             probes the value of 'unresponsivehopcount' should not be greater than 'hopstodest'

             It performs the following steps:
             1. It is validating maxhopcount, hopstodest and unresponsivehopcount values in traceroute db
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        result = self.helper.verify_maxhopcount_hopstodest_unresponsivehopcount_from_traceroute_db()
        assert result[0], result[1]

    @allure.title(
        "ZIA TRACEROUTE TUNNEL None MODE - Bypassed Direct: Verify totalloss and totallatency from traceroute db")
    @pytest.mark.xray(["QA-149157", "QA-149164"])
    @add_markers("regression")
    def test_totalloss_totallatency_values_from_traceroute_db(self):
        """
             This function verifies that in Traceroute probes the value of 'totalloss'
             and 'totallatency' should not be invalid

             It performs the following steps:
             1. It is validating 'totalloss', and 'totallatency' values in traceroute db
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        result = self.helper.verify_totalloss_totallatency_values_from_traceroute_db()
        assert result[0], result[1]

    @allure.title("ZIA TRACEROUTE TUNNEL None MODE Bypaased Website: Verify traceroute probe json from traceroute db")
    @pytest.mark.xray(["QA-149157", "QA-149164"])
    @add_markers("regression")
    def test_zia_traceroute_probe_json_from_db(self):
        """
             This function verifies ZIA TR JSON of DIRECT case (ZIA none mode).

             It performs the following steps:
             1. It is validating 'app', 'id', 'hints'
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        result = self.helper.verify_zia_traceroute_probe_json_from_db()
        assert result[0], result[1]

    @allure.title("teardown")
    def teardown_class(self):
        """
        1. Closing Db connection
        2. Logout from zcc
        """

        logger_obj.info("Logout from ZCC")
        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            logger_obj.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            logger_obj.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]
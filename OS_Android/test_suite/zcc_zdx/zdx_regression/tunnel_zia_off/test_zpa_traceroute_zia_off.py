import time
import pytest, allure
import json, re
from OS_Android.test_suite.zcc_zdx.zdx_regression.tunnel_zia_off import conftest
from common_lib.Custom_Markers import add_markers
from common_lib.helper_function import help_return
from OS_Android.library.traceroute_resource import traceroute_resource

zpa_url = conftest.zpa_webprobe_url
zpa_traceroute = conftest.zpa_traceroute_domain
tunnel_mode = "ZIA_None"



class TestZpaTracerouteModeZiaOff:

    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.service_ent = conftest.service_ent
        self.logger_obj = conftest.logger_obj
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_traceroute.db"
        self.traceroute_resource = traceroute_resource()

        self.db_handler.create_db_connection(self.db_path)

    @allure.title("ZPA TRACEROUTE : Verify maxhopcount and hopstodest count from traceroute db")
    @pytest.mark.xray("QA-283359")
    @add_markers("regression")
    def test_maxhopcount_hopstodest_unresponsivehopcount_for_zpa_traceroute_db(self):
        """
             This function test ZPA traceroute for tunnel none mode for regular zpa Traceroute probes

             This function verifies that in Traceroute probes the value of 'hopstodest'
             should not be greater than 'maxhopcount'. It also verifies that in Traceroute
             probes the value of 'unresponsivehopcount' should not be greater than 'hopstodest'
             Args:

             It performs the following steps:
             1. It is validating maxhopcount, hopstodest and unresponsivehopcount values in traceroute db
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info(f"ZPA traceroute in {tunnel_mode} mode: Verifying that in Traceroute probes"
                             " the value of 'hopstodest' should not be greater than 'maxhopcount'")

        query = f"SELECT * from trmain WHERE domain='{zpa_traceroute}'"
        rows_data = self.db_handler.execute_query(query)
        complete_traceroute = False
        flag = True
        msg = ""
        probecount = 1

        if len(rows_data) == 0:
            flag = False
            msg = "No ZPA traceroute data found in traceroute.db"

        self.logger_obj.info("Checking if any ZPA traceroute data found in the db or not")
        assert flag, msg

        for data in rows_data:
            timestamp = data['timestamp']
            domain = data['domain']
            status = data['status']
            hopstodest = data['hopstodest']
            maxhopcount = data['maxhopcount']
            unresponsivehopcount = data['unresponsivehopcount']

            if status == 'Traceroute Stopped':
                continue

            self.logger_obj.info(f"Validating 'maxhopcount', 'hopstodest' and 'unresponsivehopcount' for"
                                 f" ZPA traceroute in row where timestamp = {timestamp}")

            self.logger_obj.info(f"Validating domain value is present or not for probe = {probecount}")
            if len(domain) == 0:
                flag = False
                msg = f"No value found at 'domain' in row where timestamp = {timestamp}"
                break

            self.logger_obj.info(f"Validating hopstodest value is present or not for probe = {probecount}")
            if len(str(hopstodest)) == 0 or (len(str(hopstodest)) >= 1 and int(hopstodest) <= 0):
                flag = False
                msg = f"No or Invalid value found at 'hopstodest' in row where timestamp = {timestamp}"
                break

            self.logger_obj.info(f"Validating maxhopcount value is present or not for probe = {probecount}")
            if len(str(maxhopcount)) == 0 or (len(str(maxhopcount)) >= 1 and int(maxhopcount) <= 0):
                flag = False
                msg = f"No or Invalid value found at maxhopcount in row where timestamp = {timestamp}"
                break

            self.logger_obj.info(f"Validating unresponsivehopcount value is present or not for probe = {probecount}")
            if len(str(unresponsivehopcount)) == 0 or (
                    len(str(unresponsivehopcount)) >= 1 and int(unresponsivehopcount) < 0):
                flag = False
                msg = f"No or Invalid value found at maxhopcount in row where timestamp = {timestamp}"
                break

            if len(str(hopstodest)) >= 1 and len(str(maxhopcount)) >= 1 and int(hopstodest) > 40:
                flag = False
                msg = f"The value of 'hopstodest' = {hopstodest} is greater than 40 in row where timestamp = {timestamp} which in Invalid"
                break

            if len(str(hopstodest)) >= 1 and len(str(maxhopcount)) >= 1 and (int(hopstodest) > int(maxhopcount)):
                flag = False
                msg = (f"The value of 'hopstodest' = {hopstodest} is greater than"
                       f"maxhopcount = {maxhopcount} in row where timestamp = {timestamp} which in Incorrect")
                break

            if len(str(unresponsivehopcount)) >= 1 and len(str(hopstodest)) >= 1 and int(hopstodest) < int(
                    unresponsivehopcount):
                flag = False
                msg = f"The value of 'unresponsivehopcount' is greater than 'hopstodest' in row where timestamp = {timestamp} which is inalid"
                break

            if flag:
                complete_traceroute = True
            else:
                break

        assert complete_traceroute, "No complete traceroute data found in traceroute db"
        self.logger_obj.info("Validating hopstodest should be less than maxhopcount and"
                             "'unresponsivehopcount' should be less than equal to 'hopstodest' for ZPA traceroute"
                             f" in {tunnel_mode} mode")
        assert flag, msg


    @allure.title("ZPA TRACEROUTE: Verify zpa traceroute probe json from traceroute db")
    @pytest.mark.xray("QA-283359")
    @add_markers("regression")
    def test_zpa_traceroute_json_from_db(self):
        """
             This function verifies ZPA TR JSON of DIRECT case (ZIA none mode)

             It performs the following steps:
             1. It is validating some values for "app", "id", "clt_ha_name","clt_ip","clt_name"
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        compelete_traceroute = False
        current_row = 0
        flag = True
        msg = ""

        self.logger_obj.info(f"ZPA traceroute in {tunnel_mode} mode: Verify traceroute probe json from traceroute db")

        query = f"SELECT * from trmain WHERE domain='{zpa_traceroute}'"
        rows_data = self.db_handler.execute_query(query)

        if len(rows_data) == 0:
            flag = False
            msg = "No ZPA traceroute data found in traceroute.db"

        assert flag, msg

        for row in rows_data:
            current_row +=1
            data = ""
            json_data = ""
            data = data + row['json']
            timestamp = row['timestamp']
            encrypted_metrics_section = ""

            if row['status'] == 'Traceroute Stopped':
                continue

            self.logger_obj.info(f"Validating JSON format for ZPA traceroute in row where timestamp = {timestamp}")
            if "app" and "id" not in data:
                flag = False
                msg = f"Invalid JSON format in traceroute json in row where timestamp= {timestamp}"
                break

            try:
                json_data = json.loads(data)
            except json.JSONDecodeError as e:
                data = re.sub(r'([{,]\s*?"[^"]+"\s*:)\s*,', r'\1 null,', data)
                json_data = json.loads(data)  # {'A': 'a', 'B': None, 'C': 'c'}

            self.logger_obj.info(f"Validating 'client_name', 'clt_ha_name','clt_ip','ecode',"
                                 f"'icode', 'etime' fields in json for zpa traceroute at row where timestamp = {timestamp}")

            for i in json_data:
                if "client_name" in i or "clt_ha_name" in i or "ecode" in i or "erefid" in i:
                    if len(str(json_data[i])) == 0 or json_data[i] is None:
                        flag = False
                        msg = f"Invalid or no value in trace json for = {i} in row where timestamp= {timestamp}"
                        break
                    else:
                        self.logger_obj.info(f"Validated {i}:{json_data[i]} from traceroute json db:\n {json_data}")

                if "clt_ip" in i or "icode" in i or "is_zpa" in i or "etime" in i:
                    if len(str(json_data[i])) == 0 or json_data[i] is None:
                        flag = False
                        msg = f"Invalid or no value in trace json for = {i} in row where timestamp= {timestamp}"
                        break
                    else:
                        self.logger_obj.info(f"Validated {i}:{json_data[i]} from traceroute json db:\n {json_data}")

                if i == "is_zpa" and json_data[i] != int(1):
                    flag = False
                    msg = f" 'is_zpa' value is = {json_data[i]}, but it should be = 1 in row where timestamp= {timestamp}"
                    break
                else:
                    self.logger_obj.info(f"Validated {i}:{json_data[i]} from traceroute json db:\n {json_data}")

                if not flag:
                    break

            if flag:
                compelete_traceroute = True
                self.logger_obj.info(
                    f"#####################   Successfully validated for Traceroute json for probe {current_row}   #####################")
            else:
                break

        assert compelete_traceroute, "No Complete ZPA traceroute data found in the db"
        self.logger_obj.info("Validating correctness of full json data for all ZPA traceroute from the db")
        assert flag, msg

    @allure.title("ZPA TRACEROUTE: Verify encrypted blob in zpa traceroute probe json from traceroute db")
    @pytest.mark.xray("QA-283359")
    @add_markers("regression")
    def test_encrypted_blob_zpa_traceroute_json_from_db(self):
        """
             This function verifies ZPA TR JSON of DIRECT case (ZIA none mode)

             It performs the following steps:
             1. It is validating some values encrypted blob for zpa traceroute
             2. It is also validating encrypted metric section
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        compelete_traceroute = False
        flag = True
        msg = ""

        self.logger_obj.info(f"ZPA traceroute in {tunnel_mode} mode: Verify traceroute probe json from traceroute db")

        query = f"SELECT * from trmain WHERE domain='{zpa_traceroute}'"
        rows_data = self.db_handler.execute_query(query)

        if len(rows_data) == 0:
            flag = False
            msg = "No ZPA traceroute data found in traceroute.db"

        assert flag, msg

        for row in rows_data:
            data = ""
            json_data = ""
            data = data + row['json']
            timestamp = row['timestamp']
            encrypted_metrics_section = ""

            if row['status'] == 'Traceroute Stopped':
                continue

            try:
                json_data = json.loads(data)
            except json.JSONDecodeError as e:
                data = re.sub(r'([{,]\s*?"[^"]+"\s*:)\s*,', r'\1 null,', data)
                json_data = json.loads(data)  # {'A': 'a', 'B': None, 'C': 'c'}

            self.logger_obj.info(f"Validating 'encrypted' blob in json for zpa traceroute at row where timestamp = {timestamp}")

            try:
                self.logger_obj.info("Fetching 'encrypted_metrics_section' data from traceroute json")
                encrypted_metrics_section = json_data['encrypted_metrics_section']
            except Exception as e:
                self.logger_obj.info(str(e))
                raise Exception(f"Error :: Fetch encrypted_metrics_section from traceroute json failed :: {e}")

            self.logger_obj.info(
                f"Validating 'encrypted_metrics_section' for ZPA traceroute in row where timestamp = {timestamp}")

            encrypted_metrics_section = json_data["encrypted_metrics_section"]
            sha_public_key = encrypted_metrics_section["SHA-public-key"]
            combiner_id = encrypted_metrics_section["combiner_id"]
            encrypted_key = encrypted_metrics_section["encrypted_key"]
            metrics = encrypted_metrics_section["metrics"]

            if sha_public_key is None or len(str(sha_public_key)) == 0:
                flag = False
                msg = f"'sha_public_key' is invalid in zpa traceroute json in row where timestamp = {timestamp}"
                break

            if combiner_id is None or len(str(combiner_id)) == 0:
                flag = False
                msg = f"'combiner_id' is invalid in zpa traceroute json in row where timestamp = {timestamp}"
                break

            if encrypted_key is None or len(str(encrypted_key)) == 0:
                flag = False
                msg = f"'encrypted_key' is invalid in zpa traceroute json in row where timestamp = {timestamp}"
                break

            if metrics is None or len(str(metrics)) == 0:
                flag = False
                msg = f"Encrypted 'metrics' is invalid in zpa traceroute json in row where timestamp = {timestamp}"
                break

            if flag:
                compelete_traceroute = True
            else:
                break

        assert compelete_traceroute, "No Complete ZPA traceroute data found in the db"
        self.logger_obj.info("Validating correctness of encrypted blob data for all ZPA traceroute json from the db")
        assert flag, msg

    @allure.title("ZPA TRACEROUTE: Verify encrypted blob in zpa traceroute probe json from traceroute db")
    @pytest.mark.xray("QA-283359")
    @add_markers("regression")
    def test_tpg_upload_of_encrypted_blob_zpa_traceroute_json(self):
        """
             This function verifies ZPA TR JSON of DIRECT case (ZIA none mode)

             It performs the following steps:
             1. It is validating if encrypted blob upload is successful or not
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        #Clsoing db connection from previous testcases and creating a new one with different db
        self.logger_obj.info("Closing device_stats_db connection")
        self.db_handler.close_db_connection()

        self.logger_obj.info("Creating upload_stats_db connection")
        upload_db_path = self.const_obj.LOG_PATH + f'\\upm_upload_stats.db'
        self.db_handler.create_db_connection(upload_db_path)

        compelete_traceroute = False
        flag = True
        msg = ""
        currentrow = 0

        self.logger_obj.info(f"ZPA traceroute in {tunnel_mode} mode: Verify traceroute probe json from traceroute db")

        query = "SELECT * from upload_data"
        rows_data = self.db_handler.execute_query(query)
        self.db_handler.close_db_connection()

        if len(rows_data) == 0:
            flag = False
            msg = "No upload data found in upload_stats.db"

        assert flag, msg

        for row in rows_data:
            currentrow += 1
            # first row of upload stats will not have zpa traceroute data, expected behaviour
            if currentrow == 1:
                continue

            try:
                timestamp = row['timestamp']
                self.logger_obj.info(f"Fetching upload json data from db at row where timestamp = {timestamp}")
                upload_json = json.loads(row['upload_json'])

                for app in upload_json['mtr']['probes']:
                    probe_type = app["app"]["type"]
                    is_zpa = app["is_zpa"]

                    if int(is_zpa) == 1:
                        self.logger_obj.info(f"Fetching zpa traceroute upload data from db at row where timestamp = {timestamp}")
                    else:
                        self.logger_obj.info(
                            f"Skipping check because current traceroute data is of ZIA not ZPA")

                    if len(probe_type) != 0 and probe_type == "mtr" and int(is_zpa) == 1:
                        encrypted_metrics_section = app ["encrypted_metrics_section"]
                        self.logger_obj.info(f"found encrypted_metrics_section  = {encrypted_metrics_section}")  # debug logger
                        sha_public_key = encrypted_metrics_section["SHA-public-key"]
                        combiner_id = encrypted_metrics_section["combiner_id"]
                        encrypted_key = encrypted_metrics_section["encrypted_key"]
                        metrics = encrypted_metrics_section["metrics"]

                        self.logger_obj.info(f"Validating 'encrypted_metrics_section' for ZPA traceroute in upload stats in row"
                                             f" where timestamp = {timestamp}")

                        if sha_public_key is None or len(str(sha_public_key)) == 0:
                            flag = False
                            msg = f"'sha_public_key' is invalid for zpa traceroute in upload json in row where timestamp = {timestamp}"
                            break

                        if combiner_id is None or len(str(combiner_id)) == 0:
                            flag = False
                            msg = f"'combiner_id' is invalid for zpa traceroute in upload json in row where timestamp = {timestamp}"
                            break

                        if encrypted_key is None or len(str(encrypted_key)) == 0:
                            flag = False
                            msg = f"'encrypted_key' is invalid for zpa traceroute in upload json in row where timestamp = {timestamp}"
                            break

                        if metrics is None or len(str(metrics)) == 0:
                            flag = False
                            msg = f"Encrypted 'metrics' is invalid for zpa traceroute in upload json in row where timestamp = {timestamp}"
                            break

                        if flag:
                            compelete_traceroute = True
                        else:
                            break

            except Exception as e:
                status = False
                msg = "upload_json in upload_data table is not in a proper format for ZPA traceroute"

        self.logger_obj.info("Checking if any complete traceroute data is uploaded to tpg or not")
        assert compelete_traceroute, "No ZPA traceroute data found in the upload json of upload_stats db"

        self.logger_obj.info("Validating correctness of encrypted blob data for all ZPA traceroute from upload stats")
        assert flag, msg

    @allure.title("Validate ZPA traceroute attributes from upm_traceroute DB")
    @pytest.mark.xray("QA-283354")
    @add_markers("regression")
    def test_zpa_traceroute_columns(self):
        """
        Test to validate key performance-related columns in the 'trmain' or 'trltmain' table
        of the 'upm_traceroute' database.

        The columns checked include DNS time, availability, time-to-first-byte, total time,
        TCP time, HTTP latency, and page fetch duration. This ensures that all expected fields
        are present and correctly populated.

        Parameters:
            islt (int):
                Flag to determine which table to validate.
                - 0: Validates 'trmain' table.
                - 1: Validates 'trltmain' table.

        Fails the test if any column validation fails.
        """
        islt = 0
        table_name = 'trmain' if islt == 0 else 'trltmain'
        columns = ['appid', 'monid', 'domain', 'hopstodest', 'unresponsivehopcount', 'totalloss', 'totallatency', 'egress', 'protocol', 'mtunnelid', 'resolvedip', 'brokerResponse', 'SMEResponse']
        res, msg = self.traceroute_resource.verify_and_validate_traceroute_data(columns=columns,islt=islt,domain=zpa_traceroute)

        if res:
            self.logger_obj.info(f"SUCCESS :: Column validation passed for table '{table_name}' with columns {columns} (islt={islt}).")
        else:
            self.logger_obj.error(f"FAILED :: Column validation failed for table '{table_name}' with columns {columns} (islt={islt}).")
            assert False, "Traceroute probe verification failed:\n\n" + msg


    @allure.title("ZPA Traceroute: Verify Regular ZPA Traceroute tunnel Mode ZIA OFF")
    @pytest.mark.xray("QA-283359")
    @add_markers("regression")
    def test_zpa_traceroute_probes(self):
        """
        Test to validate the presence and correctness of ZIA 1.0, ZIA 2.0, and ZPA traceroute probes
        by parsing the ZSAUpm_Traceroute logs. It ensures all expected fields are present
        and correct based on the probe type (Regular or LT).

        Args:
            service_type (int): Type of service to verify.
                                1 - ZIA 1.0
                                2 - ZIA 2.0
                                3 - ZPA
            is_lt (int): Specifies if the probe is an LT probe.
                        0 - Regular
                        1 - LT

        Raises:
            pytest.fail: If the probe validation fails due to missing or incorrect fields.
        """
        service_type = 3
        is_lt = 0

        service_type_map = {1: "ZIA_1",2: "ZIA_2",3: "ZPA"}
        status, msg = self.traceroute_resource.verify_traceroute_probes(service_type=service_type, is_lt=is_lt)

        if not status:
            self.logger_obj.error(f"Traceroute probe verification failed:\n\n{msg}")
            assert False, f"Traceroute Probe verification failed:\n\n{msg}"
        else:
            self.logger_obj.info(f"SUCCESS ::Traceroute Probe verification completed.")

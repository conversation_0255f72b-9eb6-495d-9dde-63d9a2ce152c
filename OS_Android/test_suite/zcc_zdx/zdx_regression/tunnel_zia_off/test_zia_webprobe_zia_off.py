import time
import pytest,allure
from common_lib.Custom_Markers import add_markers
from OS_Android.test_suite.zcc_zdx.zdx_regression.tunnel_zia_off import conftest
from OS_Android.library.webload_resource import webload_resource

test_input_none_mode = [
                           pytest.param(f"select URL from WebData WHERE URL='{conftest.zia_webprobe_url}'",'URL',conftest.zia_webprobe_url,  marks=pytest.mark.xray("QA-149215")),
                           pytest.param(f"select Availability from WebData WHERE URL='{conftest.zia_webprobe_url}'",'Availability',100,   marks=pytest.mark.xray("QA-58316")),
                           pytest.param(f"select ProxyHost from WebData WHERE URL='{conftest.zia_webprobe_url}'",'ProxyHost',"", marks=pytest.mark.xray(["QA-149151","QA-283293"])),
                           pytest.param(f"select ProxyPort from WebData WHERE URL='{conftest.zia_webprobe_url}'",'ProxyPort',0,  marks=pytest.mark.xray("QA-149153")),

                        ]


class TestZiaWebprobeModeZiaOff:
    @allure.title("Setup Method")
    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.logger_obj = conftest.logger_obj
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.log_ops = conftest.log_ops
        self.service_ent = conftest.service_ent
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_webload.db"
        self.webload_resource = webload_resource()
        self.zia_url = conftest.zia_webprobe_url

    @allure.title("Tunnel Mode : ZIA OFF: Verify ZApp is writing the web monitor data to data base file: upm_webload.db")
    @pytest.mark.parametrize("query, key, value", test_input_none_mode)
    @add_markers("regression")
    def test_upm_webload(self, query, key, value):
        """
        This function tests the validation of device profile.
        It performs the following steps:
        1. Doing validation on webload db table for zdx
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("Making connection to DB")
        self.db_handler.create_db_connection(self.db_path)

        self.logger_obj.info(f"Validating value for key '{key}' with value {value}")
        output = self.db_handler.execute_query(query)
        self.logger_obj.info("Closing connection to DB")
        self.db_handler.close_db_connection()

        assert output[0][key] == value

    @allure.title("Validate web attributes from upm_webload DB")
    @add_markers("regression")
    @pytest.mark.xray("")
    def test_webdata_columns(self):
        """
        Test to validate key performance-related columns in the 'WebData' or 'WebDataLT' table
        of the 'upm_webload' database.

        The columns checked include DNS time, availability, time-to-first-byte, total time,
        TCP time, HTTP latency, and page fetch duration. This ensures that all expected fields
        are present and correctly populated.

        Parameters:
            islt (int):
                Flag to determine which table to validate.
                - 0: Validates 'WebData' table.
                - 1: Validates 'WebDataLT' table.

        Fails the test if any column validation fails.
        """
        islt = 0
        table_name = 'WebDataLT' if islt == 1 else 'WebData'
        columns = ['dns_tm', 'Availability', 'DNSTime_ns', 'ttfb_tm', 'ttlb_tm', 'total_tm', 'tcp_tm', 'HTTPLatency_ns',
                   'PageFetchTime_ns']

        res, msg = self.webload_resource.verify_and_validate_webload_data(columns=columns, islt=islt,probe_url=self.zia_url)

        if res:
            self.logger_obj.info(
                f"SUCCESS :: Column validation passed for table '{table_name}' with columns {columns} (islt={islt}).")
        else:
            self.logger_obj.error(
                f"FAILED :: Column validation failed for table '{table_name}' with columns {columns} (islt={islt}).")
            assert False, "Validation issues:\n\n" + msg

    @allure.title("Ensure Web Probes complete without HTTP errors")
    @add_markers("regression")
    @pytest.mark.xray("")
    def test_web_probe_success(self):
        """
        Test to ensure web probes in the 'WebData' or 'WebDataLT' table complete without HTTP errors.

        This validation checks that no error HTTP status codes are present in the 'StatusCode'
        or 'http_status_code' columns, indicating successful probe execution.

        Parameters:
            islt (int):
                Flag to determine which table to validate.
                - 0: Validates 'WebData' table.
                - 1: Validates 'WebDataLT' table.

        Fails the test if any HTTP errors are detected in the probe results.
        """
        islt = 0
        res, msg = self.webload_resource.verify_web_probe_success(columns=['StatusCode', 'http_status_code'], islt=islt,probe_url=self.zia_url)

        if res:
            self.logger_obj.info("Web probe validation passed: No HTTP error responses detected.")
        else:
            assert False, "Web probe validation failed due to HTTP errors:\n\n" + msg

    @allure.title("Validate MTunnelId and mtunnel_tm fields")
    @add_markers("regression")
    @pytest.mark.xray("")
    def test_mtunnel_tm_and_mtunnel_id(self):
        """
        Test to validate the 'MTunnelId' and 'mtunnel_tm' fields in the 'WebData' or 'WebDataLT'
        table of the upm_webload database.

        This ensures that both the tunnel identifier and the tunnel timing values are
        correctly populated and within expected parameters for ZDX probe entries.

        Parameters:
            islt (int):
                Flag to determine which table to validate.
                - 0: Validates 'WebData' table.
                - 1: Validates 'WebDataLT' table.

        Fails the test if either 'MTunnelId' or 'mtunnel_tm' values are invalid or missing.
        """

        islt = 0
        table_name = "WebDataLT" if islt else "WebData"
        self.logger_obj.info(f"Starting validation of 'MTunnelId' & 'mtunnel_tm' values for table: {table_name}")
        res, msg = self.webload_resource.verify_mtunnel(islt=islt,probe_url=self.zia_url)

        if res:
            self.logger_obj.info(f"Validation passed for '{table_name}': All 'MTunnelId' & 'mtunnel_tm' values are valid.")
        else:
            self.logger_obj.error(
                f"Validation failed for '{table_name}': Issues found in 'MTunnelId' & 'mtunnel_tm'.\n\n{msg}")
            assert False,f"Validation failed for '{table_name}':\n\n{msg}"


import time
import pytest, allure
import json, re
import os
from OS_Android.test_suite.zcc_zdx.zdx_regression.tunnel_zia_off import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.traceroute_resource import traceroute_resource

zia_traceroute = conftest.zia_traceroute_domain
zpa_traceroute = conftest.zpa_traceroute_domain


class TestTracerouteLogZiaOffMode:

    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.service_ent = conftest.service_ent
        self.log_handler = conftest.log_handler
        self.logger_obj = conftest.logger_obj
        self.traceroute_resource = traceroute_resource()
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_traceroute.db"
        self.db_handler.create_db_connection(self.db_path)

    # Helper Function
    def verify_differential_values(self, data: list):
        """
        Extracts avg, adj_avg, and diff_avg values from the given data.

        Example data 'DBG TRACEROUTE:: Hop[1] IP=***********,[19,4,25,21,26,18,9,16,20,17,9],
        sent=11, received=11, loss=0.00, min=4, max=26, avg=16, adj_avg =11 diff_avg=11 sd=6.55"'

        Args:
            data (list): A list of strings containing the traceroute data.

        Returns:
            list: A list of tuples containing the extracted avg, adj_avg, and diff_avg values.
        """
        pattern = r"avg\s*=\s*(\d+),\s*adj_avg\s*=\s*(\d+)\s*diff_avg\s*=\s*(\d+)"
        extracted_values = []

        for line in data:
            match = re.search(pattern, line)
            if match:
                avg, adj_avg, diff_avg = map(int, match.groups())
                extracted_values.append([avg, adj_avg, diff_avg])

        # Calculate adj_avg from logs

        calculated_values = []
        n = len(extracted_values)
        prev_cal = -1
        for i in range(n - 1, -1, -1):
            if i == len(extracted_values) - 1:
                calculated_adj_avg = extracted_values[i][0]
            else:
                calculated_adj_avg = min(prev_cal, extracted_values[i][0])

            prev_cal = calculated_adj_avg

            calculated_values.append([calculated_adj_avg, 0])

        # Reverse the list to maintain the original order
        calculated_values.reverse()

        # Calculate diff_avg values from logs
        for i in range(n):
            if i == 0:
                calculated_diff_avg = calculated_values[i][0]
            else:
                calculated_diff_avg = abs(calculated_values[i - 1][0] - calculated_values[i][0])

            calculated_values[i][1] = calculated_diff_avg

        # Validating values
        self.logger_obj.info(f"------------------------------------------------------------------------------------")
        self.logger_obj.info(f"Extracted values from traceroute logs of avg, adj_avg, and diff_avg are:")
        for line in extracted_values:
            self.logger_obj.info(f" 'avg', 'adj_avg', 'diff_avg' -> {line[0]}, {line[1]}, {line[2]}")

        self.logger_obj.info(f"------------------------------------------------------------------------------------")
        self.logger_obj.info(f"Calculated values of 'adj_avg' and 'diff_avg' from logs values of 'avg' are:")
        for line in calculated_values:
            self.logger_obj.info(f" 'adj_avg', 'diff_avg' -> {line[0]}, {line[1]}")

        for i in range(n):
            if extracted_values[i][1] != calculated_values[i][0] or extracted_values[i][2] != calculated_values[i][1]:
                return False, f"Incorrect value in line = {data[i]}"

        return True, "Successfully verified"

    @allure.title("Traceroute: Verify Regular ZIA Traceroute tunnel Mode ZIA OFF")
    @pytest.mark.xray(["QA-149143", "QA-149151", "QA-149153","QA-151948"])
    @add_markers("regression")
    def test_zia_traceroute_probes_from_log(self):
        """
             This function test ZIA traceroute for tunnel mode ZIA off for regular Traceroute probes

             Args:

             It performs the following steps:
             1. It is validating zia traceroute logs
                It will check service discovery response and validate it
                It will check are we calculating computeDifferential
                It will check DBG TRACEROUTE:: Hops after adjustment Done
                It will check Saving response into the database
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        traceroute_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                plugin='traceroute')

        status = True
        msg = ""
        sd_type = "DIRECT"
        sd_tunnel = "None"
        thread_list = set([])
        current_probe = 0

        for item in traceroute_log_data.split("\n"):
            if "ZUpmTracerouteThread:: run - In" in item and "isLT=0" in item:
                res = re.findall(r"\[([^\[\]]*)\]", item)
                temp = str(res[0])
                thread_list.add(temp)
                if len(thread_list) == 20:
                    self.logger_obj.info(thread_list)
                    break

        if len(thread_list) == 0:
            status = False
            msg = f"Traceroute ZIA probes are not available in the traceroute log file tunnel None Mode = {sd_tunnel}"

        assert status, msg

        res = ""
        for i in thread_list:
            current_thread_id = str(i)
            flag = 0
            sd_response_found = 0
            hop_count = int(0)
            run_out = 0
            run_in = 0
            can_count = 0
            insert_in_database = 0
            check_sd_url = 0
            check_sd_ip = 0
            check_sd_port = 0
            hops_data_collected = False
            compute_diff = []

            for item in traceroute_log_data.split("\n"):
                if current_thread_id in item and "SD response" in item and flag == 0 and "ZPA" not in item and "Service type: {}".format(
                        sd_type) in item and "ip.zscaler.com" not in item:
                    current_thread_id = i
                    flag = 1
                    run_in = 1
                    current_probe += 1
                    self.logger_obj.info(f"Validating logs for Probe = {current_probe}")

                if str(current_thread_id) in item and flag == 1:
                    if "ZPA" in item:
                        self.logger_obj.info(f"Skipping check for ZPA traceroute")
                        break

                    if "SD response" in item and "ip.zscaler.com" not in item:
                        if "{}".format(sd_type) in item:
                            sd_response_found = 1
                        else:
                            flag = 0
                            continue

                        sd_url = re.search("result=Url: (.*?) Success:", item).group(1)
                        sd_url = sd_url.strip()
                        if len(sd_url) > 1:
                            check_sd_url = 1
                        self.logger_obj.info(f"Service discovery Url found in the logs : {sd_url}")

                        sd_ip = re.search("Destination Ips: (.*?) Destination Ports:", item).group(1)
                        sd_ip = sd_ip.strip()
                        if len(sd_ip) > 1:
                            check_sd_ip = 1
                        self.logger_obj.info(f"Service discovery IP found in the logs : {sd_ip}")

                        sd_port = re.search("Destination Ports: (.*?) SmeIp:", item).group(1)
                        sd_port = sd_port.strip()
                        if len(sd_port) > 1:
                            check_sd_port = 1
                        self.logger_obj.info(f"Service discovery Port found in the logs : {sd_port}")

                    if flag == 1 and str(current_thread_id) in item and sd_response_found == 1:
                        self.logger_obj.info(item)

                    if "TRACEROUTE:: Differentiation Done" in item:
                        can_count = 1

                    if can_count == 1 and "TRACEROUTE:: Hop[" in item:
                        hop_count = hop_count + 1
                        compute_diff.append(item)

                    if "ZUpmTracerouteThread:: saveToDatabase - Saving response into the database" in item and can_count == 1 and flag == 1:
                        can_count = 0
                        hops_data_collected = True

                    if "ZUpmTracerouteThread:: saveToDatabase - Saving response into the database":
                        insert_in_database = 1


                    if "ZUpmTracerouteThread:: run - Out" in item and run_in == 1 and flag == 1:
                        run_out = 1
                        self.logger_obj.info(
                            f"Validating if data inserted to the db or not for probe = {current_probe}")
                        if insert_in_database == 0:
                            status = False
                            msg = (f"ZUpmTracerouteDb:insertInDatabase not found for current"
                                   f" probe with thread ID = ") + str(current_thread_id)
                        assert status, msg

                        self.logger_obj.info(
                            f"Checking Service discovery (SD) found or not for probe = {current_probe}")
                        if sd_response_found == 0:
                            status = False
                            msg = ("Service discovery (SD) not found for current"
                                   " probe with thread ID = ") + str(current_thread_id)
                        assert status, msg

                        self.logger_obj.info(f"Checking 'sd_ip'and 'sd_url' for probe = {current_probe}")
                        if check_sd_ip == 0 or check_sd_url == 0:
                            status = False
                            msg = ("Some attribute (sd_ip or sd_url or sd_port is missing or invalid"
                                   " in the service discovery with thread ID = ") + str(current_thread_id)
                        assert status, msg


                        self.logger_obj.info(
                            f"Checking ZUpmTracerouteThread:: run - In Thread found or not for probe = {current_probe}")
                        if run_in == 0:
                            status = False
                            msg = ("DBG ZUpmTracerouteThread:: run In Thread not found in the"
                                   " probe with thread ID = ") + str(current_thread_id)
                        assert status, msg

                        self.logger_obj.info(
                            f"Checking ZUpmTracerouteThread:: run Out Thread found or not for probe = {current_probe}")
                        if run_out == 0:
                            status = False
                            msg = ("DBG ZUpmTracerouteThread:: run Out Thread not found in the"
                                   " probe with thread ID = ") + str(current_thread_id)
                        assert status, msg

                        self.logger_obj.info(
                            f"ZIA traceroute Probe = {current_probe} in tunnel none mode"
                            f" is Successful with Thread ID = " + str(current_thread_id))

                        if hops_data_collected and run_in == 1 and flag == 1:
                            hops_data_collected = False

                            result = self.verify_differential_values(compute_diff)
                            assert result[0], result[1]
                            self.logger_obj.info(f"Successfully verified for traceroute monitor compute= {current_probe} ")

                            if insert_in_database == 0:
                                status = False
                                msg = f"ZUpmTracerouteDb:insertInDatabase not found for current probe with thread ID = " + str(
                                    current_thread_id)
                            assert status, msg

                        if insert_in_database == 1:
                            self.logger_obj.info(
                                f"ZIA traceroute Probe = {current_probe} in tunnel none mode"
                                f" is Successful with Thread ID = " + str(current_thread_id))
                            self.logger_obj.info(
                                "#######################################################################################################################")

                        flag = 0
                        sd_response_found = 0
                        run_out = 0
                        run_in = 0
                        can_count = 0
                        hops_data_collected = False
                        insert_in_database = 0
                        check_sd_url = 0
                        check_sd_ip = 0
                        check_sd_port = 0
                        compute_diff = []

        self.logger_obj.info("Verified complete traceroute log for ZIA traceroute in tunnel Mode ZIA OFF")
        assert status, msg

    @allure.title("ZIA TRACEROUTE when ZIA is OFF: Verify maxhopcount and hopstodest count from traceroute db")
    @pytest.mark.xray(["QA-149143","QA-149151","QA-149153"])
    @add_markers("regression")
    def test_maxhopcount_hopstodest_unresponsivehopcount_from_traceroute_db(self):
        """
             This function verifies that in Traceroute probes the value of 'hopstodest'
             should not be greater than 'maxhopcount'. It also verifies that in Traceroute
             probes the value of 'unresponsivehopcount' should not be greater than 'hopstodest'

             It performs the following steps:
             1. It is validating maxhopcount, hopstodest and unresponsivehopcount values in traceroute db
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("ZIA TRACEROUTE NONE MODE: Verifying that in Traceroute probes"
                             "the value of'hopstodest'should not be greater than 'maxhopcount'")

        query = f"SELECT * from trmain WHERE domain='{zia_traceroute}'"
        rows_data = self.db_handler.execute_query(query)
        current_row = 1
        complete_traceroute = False
        flag = True
        msg = ""

        if len(rows_data) == 0:
            flag = False
            msg = "No zia traceroute data found in traceroute.db"

        self.logger_obj.info("Checking if any zia traceroute data found in the db or not")
        assert flag, msg

        for data in rows_data:
            domain = data['domain']
            status = data['status']
            hopstodest = data['hopstodest']
            maxhopcount = data['maxhopcount']
            unresponsivehopcount = data['unresponsivehopcount']

            if status == 'Traceroute Stopped':
                continue

            if len(domain) == 0:
                flag = False
                msg = f"No value found at 'domain' column in row = {current_row}"

            if len(str(hopstodest)) == 0 or (len(str(hopstodest)) >= 1 and int(hopstodest) <= 0):
                flag = False
                msg = f"No or Invalid value found at 'hopstodest' in row = {current_row}"

            if len(str(maxhopcount)) == 0 or (len(str(maxhopcount)) >= 1 and int(maxhopcount) <= 0):
                flag = False
                msg = f"No or Invalid value found at maxhopcount in row = {current_row}"

            if len(str(unresponsivehopcount)) == 0 or (
                    len(str(unresponsivehopcount)) >= 1 and int(unresponsivehopcount) < 0):
                flag = False
                msg = f"No or Invalid value found at maxhopcount in row = {current_row}"

            if len(str(hopstodest)) >= 1 and len(str(maxhopcount)) >= 1 and int(hopstodest) > 40:
                flag = False
                msg = f"The value of 'hopstodest' = {hopstodest} is greater than 40 in row = {current_row} which in Invalid"

            if len(str(hopstodest)) >= 1 and len(str(maxhopcount)) >= 1 and (int(hopstodest) > int(maxhopcount)):
                flag = False
                msg = (f"The value of 'hopstodest' = {hopstodest} is greater than"
                       f"maxhopcount = {maxhopcount} in row = {current_row} which in Incorrect")

            if len(str(unresponsivehopcount)) >= 1 and len(str(hopstodest)) >= 1 and int(hopstodest) < int(
                    unresponsivehopcount):
                flag = False
                msg = f"The value of 'unresponsivehopcount' is greater than 'hopstodest' in row = {current_row} which is inalid"

            current_row += 1
            if flag:
                complete_traceroute = True
            else:
                break

        self.logger_obj.info("Checking if any complete traceroute data found in the db or not")
        assert complete_traceroute, "No complete traceroute data found in traceroute db"

        self.logger_obj.info("Validating hopstodest should be less than maxhopcount and"
                             "'unresponsivehopcount' should be less than equal to 'hopstodest'")
        assert flag, msg

    @allure.title("ZIA TRACEROUTE when ZIA is OFF: Verify totalloss and totallatency from traceroute db")
    @pytest.mark.xray(["QA-149143","QA-149151","QA-149153"])
    @add_markers("regression")
    def test_totalloss_totallatency_values_from_traceroute_db(self):
        """
             This function verifies that in Traceroute probes the value of 'totalloss'
             and 'totallatency' should not be invalid

             It performs the following steps:
             1. It is validating 'totalloss', and 'totallatency' values in traceroute db
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(
            "ZIA TRACEROUTE NONE MODE: Verifying 'totalloss' and 'totallatency' values from traceroute DB")

        query = f"SELECT * from trmain WHERE domain='{zia_traceroute}'"
        rows_data = self.db_handler.execute_query(query)
        currentrow = 1
        flag = True
        complete_traceroute = False
        msg = ""

        if len(rows_data) == 0:
            flag = False
            msg = "No zia traceroute data found in traceroute.db"

        assert flag, msg

        for data in rows_data:
            status = data['status']
            domain = data['domain']
            totalloss = data['totalloss']
            totallatency = data['totallatency']

            if status == 'Traceroute Stopped':
                continue

            if len(str(totalloss)) == 0 or int(totalloss) < -1 or int(totalloss) >= 101:
                flag = False
                msg = f"No or invalid found at 'totalloss' = {totalloss} in row = {currentrow}"

            if len(str(totallatency)) == 0 or int(totallatency) < -1:
                flag = False
                msg = f"No or value found at 'totallatency' = {totallatency} in row = {currentrow}"

            if int(totallatency) >= 201:
                self.logger_obj.warning(f"In row = {currentrow}, the 'totallatency' value = {totallatency} is too high")

            if flag:
                complete_traceroute = True
            else:
                break

            currentrow += 1

        self.logger_obj.info("Checking if any complete traceroute data found in the db or not")
        assert complete_traceroute, "No complete traceroute data found in traceroute db"

        self.logger_obj.info("Validating 'totalloss' and 'totallatency' values from traceroute db")
        assert flag, msg

    @allure.title("ZIA TRACEROUTE when ZIA is OFF: Verify traceroute probe json from traceroute db")
    @pytest.mark.xray(["QA-149143","QA-149151","QA-149153"])
    @add_markers("regression")
    def test_zia_traceroute_probe_json_from_db(self):
        """
             This function verifies ZIA TR JSON of DIRECT case (ZIA none mode).

             It performs the following steps:
             1. It is validating 'app', 'id', 'hints'
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        currentrow = 0
        compelete_traceroute = False
        flag = True
        msg = ""

        self.logger_obj.info("ZIA TRACEROUTE NONE MODE: Verify traceroute probe json from traceroute db")

        query = f"SELECT * from trmain WHERE domain='{zia_traceroute}'"
        rows_data = self.db_handler.execute_query(query)

        if len(rows_data) == 0:
            flag = False
            msg = "No zia traceroute data found in traceroute.db"

        assert flag, msg

        for row in rows_data:
            currentrow += 1
            data = ""
            json_data = ""
            data = data + row['json']

            if row['status'] == 'Traceroute Stopped':
                continue

            self.logger_obj.info(f"Validating JSON format for traceroute = {currentrow}")
            if "app" and "id" not in data:
                flag = False
                msg = f"Invalid JSON format in traceroute json in row = {currentrow}"
                break

            try:
                json_data = json.loads(data)
            except json.JSONDecodeError as e:
                data = re.sub(r'([{,]\s*?"[^"]+"\s*:)\s*,', r'\1 null,', data)
                json_data = json.loads(data)  # {'A': 'a', 'B': None, 'C': 'c'}

            self.logger_obj.info(f"Validating 'hints' for traceroute = {currentrow}")
            if len(json_data['hints']) == 0 or json_data['hints'] is None:
                flag = False
                msg = f"In row = {currentrow} under 'JSON' column, No legs are present in the 'Hints' section"
                break

            self.logger_obj.info(f"Validating 'client_name', 'clt_ha_name','clt_ip','ecode',"
                                 f"'icode', 'etime' fields in json for traceroute = {currentrow}")

            for i in json_data:
                if "client_name" in i or "clt_ha_name" in i or "ecode" in i or "erefid" in i:
                    if len(str(json_data[i])) == 0 or json_data[i] is None:
                        flag = False
                        msg = f"Invalid or no value in trace json for = {i} in row = {currentrow} of traceroute db"
                        break

                if "clt_ip" in i or "icode" in i or "is_zpa" in i or "etime" in i:
                    if len(str(json_data[i])) == 0 or json_data[i] is None:
                        flag = False
                        msg = f"Invalid or no value in trace json for = {i} in row = {currentrow} of traceroute db"
                        break

            if not flag:
                break

            data = ""
            traceroute_legs = json_data["legs"]
            first_leg_tunnel_type = traceroute_legs[0]['tunnel_type']


            if first_leg_tunnel_type not in [1, 13, 21]:
                flag = False
                msg = (f"ZIA traceroute is not working fine for tunnel NONE mode, showing incorrect tunnel_type ="
                       f"{first_leg_tunnel_type}, it should be in ['1','13','21']")
                break


            for leg in traceroute_legs:
                source = leg['src']
                destination = leg['dst']
                dst_ip = leg['dst_ip']
                ecode = leg['ecode']
                loss = leg['loss']
                proto = leg['proto']
                src_ip = leg['src_ip']
                tunnel_type = leg['tunnel_type']
                num_hops = leg['num_hops']
                num_unresp_hops = leg['num_unresp_hops']

                self.logger_obj.info(f"Validating 'src_ip' value in json for traceroute = {currentrow}")
                if src_ip is None or len(str(src_ip)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'src_ip' value is missing")
                    break

                self.logger_obj.info(f"Validating 'dts_ip' value in json for traceroute = {currentrow}")
                if len(str(dst_ip)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'dst_ip' value is missing")
                    break
                else:
                    match = re.match(r"[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}", dst_ip)
                    if not bool(match):
                        flag = False
                        msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                               f"'dst_ip' value is Invalid")
                        break

                self.logger_obj.info(f"Validating 'ecode' value in json for traceroute = {currentrow}")
                if ecode is None or len(str(ecode)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'ecode' value is missing")
                    break

                self.logger_obj.info(f"Validating 'loss' value in json for traceroute = {currentrow}")
                if loss is None or len(str(loss)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'loss' value is missing")
                    break

                self.logger_obj.info(f"Validating 'protocol' value in json for traceroute = {currentrow}")
                if proto is None or len(str(proto)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'proto' value is missing")
                    break

                self.logger_obj.info(f"Validating 'tunnel_type' value in json for traceroute = {currentrow}")
                if tunnel_type is None or len(str(tunnel_type)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'tunnel_type' value is missing")
                    break

                self.logger_obj.info(f"Validating 'num_hops' value in json for traceroute = {currentrow}")
                if num_hops is None or len(str(num_hops)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'nums_hops' value is missing")
                    break

                self.logger_obj.info(f"Validating 'num_unresp_hops' value in json for traceroute = {currentrow}")
                if num_unresp_hops is None or len(str(num_unresp_hops)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'nums_unresp_hops' value is missing")
                    break

            if flag:
                compelete_traceroute = True
            else:
                break

        self.logger_obj.info("Checking if any complete traceroute data is present in the db or not")
        assert compelete_traceroute, "No Complete traceroute data found in the db"

        self.logger_obj.info("Validating correctness of full json data for all zia traceroute from the db")
        assert flag, msg

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Deletion of forward profile and app profile
        2. Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Closing db connection after verification")
        self.db_handler.close_db_connection()
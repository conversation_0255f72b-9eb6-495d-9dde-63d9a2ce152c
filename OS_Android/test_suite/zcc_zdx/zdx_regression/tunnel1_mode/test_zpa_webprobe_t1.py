import time
import pytest, allure
from OS_Android.test_suite.zcc_zdx.zdx_regression.tunnel1_mode import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.webload_resource import webload_resource


class TestZpaWebProbe:

    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        4. ZPA Webporbe should be configured before running the testcase
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.service_ent = conftest.service_ent
        self.logger_obj = conftest.logger_obj
        self.zpa_url = conftest.zpa_webprobe_url
        self.webload_resource = webload_resource()
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_webload.db"


    @allure.title("Validate web attributes from upm_webload DB")
    @add_markers("regression")
    @pytest.mark.xray("QA-149177")
    def test_webdata_columns(self):
        """
        Test to validate key performance-related columns in the 'WebData' or 'WebDataLT' table
        of the 'upm_webload' database.

        The columns checked include DNS time, availability, time-to-first-byte, total time,
        TCP time, HTTP latency, and page fetch duration. This ensures that all expected fields
        are present and correctly populated.

        Parameters:
            islt (int):
                Flag to determine which table to validate.
                - 0: Validates 'WebData' table.
                - 1: Validates 'WebDataLT' table.

        Fails the test if any column validation fails.
        """
        islt = 0
        table_name = 'WebDataLT' if islt == 1 else 'WebData'
        columns = ['dns_tm', 'Availability', 'DNSTime_ns', 'ttfb_tm', 'ttlb_tm', 'total_tm', 'tcp_tm', 'HTTPLatency_ns',
                   'PageFetchTime_ns']

        res, msg = self.webload_resource.verify_and_validate_webload_data(columns=columns, islt=islt,probe_url=self.zpa_url)

        if res:
            self.logger_obj.info(
                f"SUCCESS :: Column validation passed for table '{table_name}' with columns {columns} (islt={islt}).")
        else:
            self.logger_obj.error(
                f"FAILED :: Column validation failed for table '{table_name}' with columns {columns} (islt={islt}).")
            assert False, "Validation issues:\n\n" + msg

    @allure.title("Ensure Web Probes complete without HTTP errors")
    @add_markers("regression")
    @pytest.mark.xray("QA-149177")
    def test_web_probe_success(self):
        """
        Test to ensure web probes in the 'WebData' or 'WebDataLT' table complete without HTTP errors.

        This validation checks that no error HTTP status codes are present in the 'StatusCode'
        or 'http_status_code' columns, indicating successful probe execution.

        Parameters:
            islt (int):
                Flag to determine which table to validate.
                - 0: Validates 'WebData' table.
                - 1: Validates 'WebDataLT' table.

        Fails the test if any HTTP errors are detected in the probe results.
        """
        islt = 0
        res, msg = self.webload_resource.verify_web_probe_success(columns=['StatusCode', 'http_status_code'], islt=islt,probe_url=self.zpa_url)

        if res:
            self.logger_obj.info("Web probe validation passed: No HTTP error responses detected.")
        else:
            assert False, "Web probe validation failed due to HTTP errors:\n\n" + msg

    @allure.title("Validate http_connect_tm field")
    @add_markers("regression")
    @pytest.mark.xray("QA-149177")
    def test_http_connect_tm(self):
        """
        Test to validate the 'http_connect_tm' field for ZIA and ZPA probes in the
        'WebData' or 'WebDataLT' table of the upm_webload database.

        Ensures that the HTTP connect time values are present and within expected limits,

        Parameters:
            islt (int):
                Flag to determine which table to validate.
                - 0: Validates 'WebData' table.
                - 1: Validates 'WebDataLT' table.

        Fails the test if invalid or missing 'http_connect_tm' values are detected.
        """
        islt = 0
        table_name = "WebDataLT" if islt else "WebData"
        self.logger_obj.info(f"Starting validation of 'http_connect_tm' for table: {table_name}")
        res, msg = self.webload_resource.verify_http_connect_tm(islt=islt,probe_url=self.zpa_url)

        if res:
            self.logger_obj.info(f"Validation passed for '{table_name}': All 'http_connect_tm' values are valid.")
        else:
            self.logger_obj.error(
                f"Validation failed for '{table_name}': Issues found in 'http_connect_tm' values.\n\n{msg}")
            assert False, f"Validation failed for '{table_name}':\n\n{msg}"

    @allure.title("Validate MTunnelId and mtunnel_tm fields")
    @add_markers("regression")
    @pytest.mark.xray("QA-149177")
    def test_mtunnel_tm_and_mtunnel_id(self):
        """
        Test to validate the 'MTunnelId' and 'mtunnel_tm' fields in the 'WebData' or 'WebDataLT'
        table of the upm_webload database.

        This ensures that both the tunnel identifier and the tunnel timing values are
        correctly populated and within expected parameters for ZDX probe entries.

        Parameters:
            islt (int):
                Flag to determine which table to validate.
                - 0: Validates 'WebData' table.
                - 1: Validates 'WebDataLT' table.

        Fails the test if either 'MTunnelId' or 'mtunnel_tm' values are invalid or missing.
        """

        islt = 0
        table_name = "WebDataLT" if islt else "WebData"
        self.logger_obj.info(f"Starting validation of 'MTunnelId' & 'mtunnel_tm' values for table: {table_name}")
        res, msg = self.webload_resource.verify_mtunnel(islt=islt,probe_url=self.zpa_url)

        if res:
            self.logger_obj.info(f"Validation passed for '{table_name}': All 'MTunnelId' & 'mtunnel_tm' values are valid.")
        else:
            self.logger_obj.error(
                f"Validation failed for '{table_name}': Issues found in 'MTunnelId' & 'mtunnel_tm'.\n\n{msg}")
            assert False,f"Validation failed for '{table_name}':\n\n{msg}"

    @allure.title("Validate ssl_tm and SMEID fields")
    @add_markers("regression")
    @pytest.mark.xray("QA-149177")
    def test_ssl_tm_and_smeid(self):
        """
        Test to validate the 'ssl_tm' and 'SMEID' fields in the 'WebData' or 'WebDataLT'
        table of the upm_webload database.

        Validation rules:
        - For HTTP URLs, 'ssl_tm' must be 0.
        - For HTTPS URLs, at least one entry must have 'ssl_tm' > 0.
        - For ZIA tunnel types, 'SMEID' must be greater than 0.
        - For ZPA tunnel types, 'SMEID' must be 0.

        Parameters:
            islt (int):
                Flag to determine which table to validate.
                - 0: Validates 'WebData' table.
                - 1: Validates 'WebDataLT' table.

        Fails the test if any validation rule is violated.
        """
        islt = 0
        table_name = "WebDataLT" if islt else "WebData"
        self.logger_obj.info(f"Starting validation of 'ssl_tm' & 'SMEID' values for table: {table_name}")

        res, msg = self.webload_resource.verify_ssl_tm_and_smeid(islt=islt,probe_url=self.zpa_url)

        if res:
            self.logger_obj.info(f"Validation passed for '{table_name}': All 'ssl_tm' & 'SMEID' values are valid.")
        else:
            self.logger_obj.error(f"Validation failed for '{table_name}': Issues found in 'ssl_tm' & 'SMEID'.\n\n{msg}")
            assert False, f"Validation failed for '{table_name}':\n\n{msg}"

    @allure.title("ZPA WEBLOAD: Check Web Probe from Webload logs")
    @add_markers("regression")
    @pytest.mark.xray("QA-149177")
    def test_web_probes(self):
        """
        Test to validate the presence and correctness of ZIA 1.0, ZIA 2.0, and ZPA web probes
        by parsing the ZSAUpm_Webload logs. It ensures all expected fields and headers are present
        and correct based on the probe type (Regular or LT).

        Args:
            service_type (int): Type of service to verify.
                                1 - ZIA 1.0
                                2 - ZIA 2.0
                                3 - ZPA
            is_lt (int): Specifies if the probe is an LT probe.
                        0 - Regular
                        1 - LT

        Raises:
            pytest.fail: If the probe validation fails due to missing or incorrect fields.
        """
        service_type_map = {1: "ZIA_1", 2: "ZIA_2", 3: "ZPA"}
        is_lt = 0
        service_type = 3
        status, msg = self.webload_resource.verify_web_probes(service_type=service_type, is_lt=is_lt)

        if not status:
            self.logger_obj.error(f"Web probe verification failed:\n\n{msg}")
            assert False, f"Web Probe verification failed:\n\n{msg}"

    @allure.title("Validate statsV2 attribute - {attribute_to_check}")
    @pytest.mark.parametrize("attribute_to_check", ['tot_payload_bytes', 'statsV2'])
    @add_markers("regression")
    def test_web_json_attributes(self, attribute_to_check):
        """
        Validates presence and correctness of specific attributes in Webload JSON logs for DIRECT, ZIA, and ZPA traffic.
        Parameters:
            attribute_to_check (str): The JSON attribute to validate, e.g., 'tot_payload_bytes' or 'statsV2'.

        Assertions:
            - Fails the test if the specified attribute is missing or invalid in any relevant log entry.
        """
        status, msg = self.webload_resource.verify_json_attribute_from_log(attribute_to_check=attribute_to_check,probe_url=self.zpa_url)
        if status:
            self.logger_obj.info(f"'{attribute_to_check}' verified successfully in Webload JSON logs.")
        else:
            self.logger_obj.error(f"Verification failed for '{attribute_to_check}':\n\n{msg}")
            pytest.fail(f"Attribute check failed for '{attribute_to_check}':\n\n{msg}")

    @allure.title("ZPA WEBLOAD: Verify Status Code from Webload DB for ZPA webprobe when ZIA is in tunnel1 mode")
    @pytest.mark.xray("QA-149177")
    @add_markers("regression")
    def test_verify_status_code_for_zpa_webprobe_from_db(self):

        """
            This function tests the validation of status code for zpa_webprobe in webload db when ZIA is in tunnel1 mode.
            It performs the following steps:
            1. Doing validation on status code for ZPA webload for zdx
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("Making connection to DB")
        self.db_handler.create_db_connection(self.db_path)
        query = f"SELECT * from WebData WHERE URL='{self.zpa_url}'"
        rows = self.db_handler.execute_query(query)
        currentrow = 0
        if len(rows) == 0:
            assert False, "No webprobe data found in webload.db"

        for item in rows:
            currentrow += 1
            statusCode = item['StatusCode']

            self.logger_obj.info(f"Verifying Status Code:{statusCode} for ZPA webprobe = {currentrow}")
            assert len(str(statusCode)) != 0, f"No value found at 'StatusCode' in row {currentrow}"

            if statusCode not in ['200', '301', '302']:
                status = False
                assert status, "Status code is not in ['200', '301', '302]"

            currentrow+= 1

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Deletion of forward profile and app profile
        2. Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Closing db connection after verification")
        self.db_handler.close_db_connection()

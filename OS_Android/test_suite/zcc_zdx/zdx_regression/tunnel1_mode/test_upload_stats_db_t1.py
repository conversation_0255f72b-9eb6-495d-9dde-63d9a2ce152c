import time
import pytest, allure
import json
from OS_Android.test_suite.zcc_zdx.zdx_regression.tunnel1_mode import conftest
from common_lib.Custom_Markers import add_markers
from common_lib.helper_function import help_return


class TestUploadStats:

    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.service_ent = conftest.service_ent
        self.logger_obj = conftest.logger_obj
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_upload_stats.db"

        self.logger_obj.info("Creating db connection")
        self.db_handler.create_db_connection(self.db_path)

    @allure.title("UploadStatsDB: Test Upload db data insertion frequency")
    @pytest.mark.xray("QA-149259")
    @add_markers("regression")
    def test_verify_zdx_data_upload_frequency_from_upload_stats_db(self):
        """
             This function checks whether the data is being uploaded in every 5 minutes or not.
             It is also checking for invalid/NULL timestamp ,status, json values.
             It performs the following steps:
             1. It is validating upload data frequency which is 5mins
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("UploadStatsDB: Verifying data insertion frequency for upload stats db")

        query = "SELECT * from upload_data"
        rows_data = self.db_handler.execute_query(query)
        current_row = 1
        current_timestamp = 0
        flag = True

        self.logger_obj.info("Validation if data is present or not in the db")
        if len(rows_data) == 0:
            flag = False

        assert flag, "No upload data found in upload stats db"

        for probes in rows_data:
            timestamp = probes['timestamp']
            status = probes['status']
            upload_json = str(probes['upload_json'])

            self.logger_obj.info(f"Validating timestamp and frequency for probe = {current_row}")
            if current_timestamp == 0:
                current_timestamp = int(timestamp)
            else:
                if current_timestamp + 300000 != int(timestamp):
                    flag = False

                current_timestamp = timestamp

            assert flag, (f"The difference between previous timestamp and"
                          f" current timestamp is not 5 minutes, in row  = {current_row}")

            if timestamp == '' or int(timestamp) <= 0:
                flag = False
            assert flag, f"Timestamp is invalid at row = {current_row}"

            self.logger_obj.info(f"Validating upload status from upload db for probe = {current_row}")
            if status == '' or int(status) != 1:
                flag = False
            assert flag, f"'status' value is invalid at timestamp = {timestamp}"

            self.logger_obj.info(f"Validating upload json from upload db for probe = {current_row}")

            if upload_json == '' or len(upload_json) <= 20:
                flag = False
            assert flag, f"Upload_json data is not available at row = {current_row}"

            current_row += 1

        self.logger_obj.info("Checking sendResultToTPG is successful or not")
        upm_file = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['upm'][-1]
        result = help_return(self.log_obj.search_log_file, file=upm_file,
                             words_to_find_in_line=["uploadDataToHost: Sent data to TPG successfully with 200 OK"],
                             directory=self.const_obj.LOG_PATH)
        assert result[0], result[1]

    @allure.title("UploadStatsDB: Verify device Profile Sent to TPG when user login to ZCC")
    @pytest.mark.xray(["QA-151970", "QA-149251"])
    @add_markers("regression")
    def test_device_profile_upload_to_tpg_when_user_login(self):
        """
             This function checks whether the device profile data is being uploaded to tpg when user login to zcc.
             It performs the following steps:
             1. It is validating device profile data from upload data json
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        flag = True
        msg = "No data found in upload stats json for device profile"

        self.logger_obj.info("UploadStatsDB: Verify device Profile Sent to TPG when user login to ZCC")

        try:
            query = "SELECT * from upload_data"
            rows_data = self.db_handler.execute_query(query)
            if len(rows_data) == 0:assert False, "No upload data found in upload stats json"

            timestamp = rows_data[0]['timestamp']
            status = rows_data[0]['status']
            upload_json = json.loads(rows_data[0]['upload_json'])
            device_profile = upload_json['profile']

            self.logger_obj.info("Validating if upload json data is available or not")
            if upload_json == '' or len(str(upload_json)) <= 20:
                flag = False
                msg = "Upload_json data is not available at timestamp at 1st row which is incorrect"

            self.logger_obj.info("Validating if device profile data is available or not in upload_stats json")
            if flag and (device_profile == '' or len(device_profile) == 0):
                flag = False
                msg = "Failed to send device profile data to the tpg, check 1st row data of upload stats json "

        except Exception as e:
            flag = False
            msg = f"Some error occured while looking for device profile data after login {e}"

        assert flag, msg

    @allure.title("UploadStatsDB: Verify frequency of device stats monitor data upload to TPG")
    @add_markers("regression")
    @pytest.mark.xray("QA-149259")
    def test_verify_device_stats_data_upload_frequency_to_tpg(self):
        """
           This function checks whether the device stats data is being uploaded in every 5 minutes or not.
           This function checks type from upm_device_stats.db It is checking for Invalid type in the 'upload_data' table.
           It performs the following steps:
           1. It is validating device stats data frequency to the tpg which is 5mins
           Args:
              self (object): Instance of the test class.
           Returns:
              None
           Raises:
               AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("UploadStatsDB: Verify frequency of device stats monitor data upload to TPG")

        device_stat_type = ["cpu", "mem", "disk", "ifstat", "wifi", "battery"]
        query = "SELECT * from upload_data"
        rows_data = self.db_handler.execute_query(query)
        current_row = 0
        status = True
        msg = ''

        self.logger_obj.info("Validating upload data is present or not")
        assert len(rows_data) != 0, "No upload data found in upload stats db"

        if len(rows_data) == 1:
            pytest.skip("Not enough data in upload stats db")

        for index, probes in enumerate(rows_data, start=1):
            try:
                timestamp = probes['timestamp']
                upload_json = json.loads(probes['upload_json'])

                for app in upload_json['apps']['probes']:
                    probe_type = app["app"]["type"]

                    if probe_type == "web" or probe_type == "mtr":
                        continue

                    if probe_type not in device_stat_type:
                        msg = f"{probe_type} is not a valid device stats type for row timestmap = {timestamp}"
                        assert False, msg
                    else:
                        self.logger_obj.info(f"{probe_type} is not a valid device stats type for  timestamp = {timestamp}")
                self.logger_obj.info(f"Successfully Validated for data for the row where timestamp : {timestamp}")

            except Exception as e:
                status = False
                msg = "upload_json in upload_data table is not in a proper json format"

        self.logger_obj.info("Validating device stats data type in upload json of upload stats db")
        assert status, msg

    @allure.title("UploadStatsDB: Verify frequency of webload data upload to TPG")
    @add_markers("regression")
    @pytest.mark.xray("QA-149210")
    def test_webprobe_data_upload_frequency_to_tpg(self):

        """
           This function checks whether the webload data is being uploaded in every 5 minutes or not.

           It performs the following steps:
           1. It is validating webprobe data frequency to the tpg which is 5mins
           Args:
              self (object): Instance of the test class.
           Returns:
              None
           Raises:
               AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("UploadStatsDB: Verifying frequency of webload data upload to TPG")

        query = "SELECT * from upload_data"
        rows_data = self.db_handler.execute_query(query)
        status = True
        msg = "Webprobe data not found in the upload_json of upload stats db"

        self.logger_obj.info("Validating upload data is present or not")
        assert len(rows_data) != 0, "No upload data found in upload stats db"

        if len(rows_data) == 1:
            pytest.skip("Not enough data in upload stats db to verify webprobe data")

        for index, probes in enumerate(rows_data, start=1):
            try:
                timestamp = probes['timestamp']
                upload_json = json.loads(probes['upload_json'])

                for app in upload_json['apps']['probes']:
                    probe_type = app["app"]["type"]

                    if probe_type in ["cpu", "mem", "disk", "ifstat", "wifi", "battery", "mtr"]:
                        continue

                    if probe_type != "web":
                        msg = f"{probe_type} is not a valid web probe type"
                        assert False, msg
                    else:
                        self.logger_obj.info(f"Valid {probe_type} type found in upload json of upload stats db for timestamp = {timestamp}")

            except Exception as e:
                status = False
                msg = "upload_json in upload_data table is not in a proper json format"

        assert status, msg

    @allure.title("UploadStatsDB: Verify frequency of traceroute data upload to TPG")
    @add_markers("regression")
    @pytest.mark.xray("QA-149166")
    def test_traceroute_data_upload_frequency_to_tpg(self):

        """
           This function checks whether the traceroute data is being uploaded in every 5 minutes or not.

           It performs the following steps:
           1. It is validating traceroute data upload frequency which is 5mins
           Args:
              self (object): Instance of the test class.
           Returns:
              None
           Raises:
               AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Verifying frequency of traceroute data upload to TPG which is 5min")

        query = "SELECT * from upload_data"
        rows_data = self.db_handler.execute_query(query)
        status = True
        msg = "upload_json in upload_data table is not having traceroute data"

        self.logger_obj.info("Validating upload data is present or not")
        assert len(rows_data) != 0, "No upload data found in upload stats db"

        if len(rows_data) == 1:
            pytest.skip("Not enough data in upload stats for traceroute db")

        for index, probes in enumerate(rows_data, start=1):
            try:
                timestamp = probes['timestamp']
                upload_json = json.loads(probes['upload_json'])

                for app in upload_json['mtr']['probes']:
                    probe_type = app["app"]["type"]

                    if len(probe_type) != 0 and probe_type == "mtr":
                        status = True
                        self.logger_obj.info(f"Valid {probe_type} type found in upload json of upload stats db for timestamp = {timestamp}")
                    else:
                        msg = f"{probe_type} is not a valid type for cloud Path probe in upload json of upload stats db"
                        assert False, msg

            except Exception as e:
                status = False
                msg = "upload_json in upload_data table is not in a proper json format"

        assert status, msg

    @allure.title("Verify the final upload json contains all the details of webload, traceroute and devicestats")
    @add_markers("regression")
    @pytest.mark.xray("QA-149339")
    def test_final_upload_json_data(self):

        """
           This function Verify the final upload json contains all the correct details of webload, traceroute and devicestats

           It performs the following steps:
           1. It is validating the final upload json contains all the correct details of webload, traceroute and devicestats
           Args:
              self (object): Instance of the test class.
           Returns:
              None
           Raises:
               AssertionError: If any of the steps fail.

        """

        self.logger_obj.info(
            "Verify the final upload json contains all the correct details of webload, traceroute and devicestats")

        query = "SELECT * from upload_data"
        rows_data = self.db_handler.execute_query(query)

        if len(rows_data) == 0:
            assert False, "No upload data found in upload stats db"

        upload_data = rows_data[-1]
        uploadjson = json.loads(upload_data['upload_json'])

        self.logger_obj.info("Validating final upload json contains 'mtr' data or not ")
        result = self.log_handler.validate_type_data_presence_lt_upload_json(stime =0, etime=0,upload_json=uploadjson, type='mtr')
        assert result[0],result[1]
        self.logger_obj.info("------------------------------------Successfully Validated ------------------------------------")

        self.logger_obj.info("Validating final upload json contains 'web' data or not ")
        result = self.log_handler.validate_type_data_presence_lt_upload_json(stime=0, etime=0,upload_json=uploadjson, type='web')
        assert result[0], result[1]
        self.logger_obj.info("------------------------------------Successfully Validated ------------------------------------")

        self.logger_obj.info("Validating final upload json contains 'deviceStats' data or not ")
        result = self.log_handler.validate_type_data_presence_lt_upload_json(stime=0, etime=0,upload_json=uploadjson, type='device_stats')
        assert result[0], result[1]
        self.logger_obj.info( "------------------------------------Successfully Validated ------------------------------------")

    @allure.title("Verify that device profile upload now includes Time Zone of the device")
    @pytest.mark.xray("QA-283278")
    @add_markers("regression")
    def test_device_profile_upload_includes_device_time_zone(self):
        """
             This function checks whether the device profile data contains "tzone" data
             It performs the following steps:
             1. It is validating device profile data from upload data json
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        flag = True
        msg = "No data found in upload stats json for 'tzone' in device profile"

        self.logger_obj.info("Verify that device profile upload now includes Time Zone of the device")

        self.logger_obj.info("Fetching tzone info from device")
        time_zone_info = self.sys_ops.get_android_time_zone_info()
        assert time_zone_info[0], time_zone_info[1]
        self.logger_obj.info(f"Successfully fetched Timezone Info from Device: {time_zone_info[1]}")

        try:
            query = "SELECT * from upload_data"
            rows_data = self.db_handler.execute_query(query)

            timestamp = rows_data[0]['timestamp']
            status = rows_data[0]['status']
            upload_json = json.loads(rows_data[0]['upload_json'])
            device_profile = upload_json['profile']
            tzone_from_db = device_profile['tzone']

            self.logger_obj.info("Validating if upload json data is available or not")
            if upload_json == '' or len(str(upload_json)) <= 20:
                flag = False
                msg = "Upload_json data is not available at timestamp at 1st row which is incorrect"

            self.logger_obj.info("Validating if device profile data is available or not in upload_stats json")
            if flag and (device_profile == '' or len(device_profile) == 0):
                flag = False
                msg = "Failed to send device profile data to the tpg, check 1st row data of upload stats json "

            self.logger_obj.info("Validating if device profile data is having 'tzone' data or not")
            if flag and (tzone_from_db == '' or len(tzone_from_db) == 0):
                flag = False
                msg = " 'tzone' value in device profile from upload json is empty, which is incorrect "

        except Exception as e:
            flag = False
            msg = f"Some error occurred while looking for device profile data from db {e}"

        assert flag, msg
        assert tzone_from_db == time_zone_info[1], (f"'tzone' from device:{time_zone_info[1]} "
                                                    f"'tzone' from db:{tzone_from_db}, It is not same which is incorrect")
        self.logger_obj.info(
            f"Successfully validated 'tzone' from device = {time_zone_info[1]} and db = {tzone_from_db}"
            f"which is correct")

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Deletion of forward profile and app profile
        2. Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Closing db connection after verification")
        self.db_handler.close_db_connection()

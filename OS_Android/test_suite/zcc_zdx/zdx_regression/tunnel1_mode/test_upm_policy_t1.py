import time, re
import pytest, allure
from datetime import datetime
from OS_Android.test_suite.zcc_zdx.zdx_regression.tunnel1_mode import conftest
from common_lib.Custom_Markers import add_markers
from common_lib.helper_function import help_return
from typing import Any


class TestUpmPolicy:

    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.service_ent = conftest.service_ent
        self.logger_obj = conftest.logger_obj
        self.log_handler = conftest.log_handler
        self.zdx_cloud = conftest.zdx_cloud
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_device_stats.db"

        self.logger_obj.info("Fetching upm log file data to validate persistent connection for policy download")
        self.upm_log_file_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                   plugin='upm')

    # Helper Function
    def validate_ip_format(self, ip_addr: str):
        """
        Checks if a given IP address is in the correct IPv4 or IPv6 format.

        Args:
            ip (str): The IP address to check.

        Returns:
            str: A message indicating whether the IP address is in the correct format.
        """

        ipv4_pattern = r"^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
        ipv6_pattern = r"^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^([0-9a-fA-F]{1,4}:){1,7}:$|^([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}$|^([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}$|^([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}$|^([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}$|^([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}$|^([0-9a-fA-F]{1,4}:){1}(:[0-9a-fA-F]{1,4}){1,6}$|^([0-9a-fA-F]{1,4}:){0}(:[0-9a-fA-F]{1,4}){1,7}$"

        if re.match(ipv4_pattern, ip_addr):
            return True, f"{ip_addr} is a valid IPv4 address."
        elif re.match(ipv6_pattern, ip_addr):
            return True, f"{ip_addr} is a valid IPv6 address."
        else:
            return False, f"{ip_addr} is not a valid IP address."

    def validate_fields(self,data: dict[str, Any], expected_structure: dict[str, Any], policy_type:str, parent_key: str = "") -> (bool, str):
        """
        Validates the fields of a given monitor data against the expected structure.

        :param data: The monitor data to validate
        :param expected_structure: The expected structure (types) for validation
        :param parent_key: The parent key for nested structure (used for readable error messages)
        :return False, str: If any field is missing or has the wrong type
        """
        for key, expected_type in expected_structure.items():
            full_key = f"{parent_key}.{key}" if parent_key else key

            # Check if the key exists
            if key not in data:
                self.logger_obj.error(f"Validation failed: Missing key '{full_key}'")
                return False, f"Missing key: {full_key}"

            value = data[key]

            # Check for nested dictionaries
            if isinstance(expected_type, dict):
                if not isinstance(value, dict):
                    self.logger_obj.error(
                        f"Validation failed: Key '{full_key}' expected a dictionary, but got: {type(value).__name__}")
                    return False, f"Key '{full_key}' expected a dictionary, but got: {type(value).__name__}"

                # Log recursive validation
                self.logger_obj.info(f"------ Recursively validating nested key '{full_key}'-------")
                valid, message = self.validate_fields(value, expected_type,policy_type, full_key)
                if not valid:
                    return False, message
            else:
                # Validate the type of the field
                if not isinstance(value, expected_type):
                    self.logger_obj.error(
                        f"Validation failed: Key '{full_key}' expected type '{expected_type.__name__}', but got: {type(value).__name__}")
                    return False, f"Key '{full_key}' expected type '{expected_type.__name__}', but got: {type(value).__name__}"

            # Log success for this key
            self.logger_obj.info(f"Valid key '{full_key}' with value '{value}'")

        self.logger_obj.info(f"All fields successfully validated for this Monitor type :{policy_type}\n")
        return True, "All fields successfully validated"

    def validate_webprobe_traceroute_policy(self, policy: dict[str, Any]) -> (bool, str):
        """ Validates a webprobe or traceroute.
            Checks if the policy is either a TRACERT WEB type and validates its against the expected fields.
    
            Args:
               policy (NestedPolicyType The policy to be.

            Returns:
              bool: True if the policy is valid, False otherwise.
        """
        # Define the expected fields for a traceroute and webprobe policy
        expected_policy_fields = {
        "TRACERT": {
            "id": int,
            "type": str,
            "version": int,
            "flags": str,
            "ip_type": int,
            "timeout": int,
            "max_attempts": int,
            "modtime": int,
            "metrics": str,
            "appid": int,
            "appname": str,
            "apptype": str,
            "monitor_name": str,
            "frequency": int,
            "expiry": int,
            "traceroute_monitor": {
                "domain": str,
                "protocol": str,
                "hop_count": int,
                "packet_count": int,
                "request_interval": int,
                "port": int,
                "path_web_monid": int,
                "packet_timeout": int,
                "udp_port": int,
                "tcp_port": int,
            }
        },
        "WEB": {
            "id": int,
            "type": str,
            "version": int,
            "flags": str,
            "ip_type": int,
            "timeout": int,
            "max_attempts": int,
            "modtime": int,
            "metrics": str,
            "appid": int,
            "appname": str,
            "apptype": str,
            "monitor_name": str,
            "frequency": int,
            "expiry": int,
            "web_monitor": {
                "request_type": str,
                "request_timeout": int,
                "max_redirects": int,
                "max_inbytes": int,
                "url": str,
                "response_codes": str,
                "proxy_port": int,
            }
        },
        "DEVICESTATS": {
            "id": int,
            "type": str,
            "version": int,
            "ip_type": int,
            "timeout": int,
            "max_attempts": int,
            "modtime": int,
            "appid": int,
            "apptype": str,
            "frequency": int,
            "expiry": int,
            "device_stats": {
                "stats": str,
            }
        }
         }

        policy_type = policy.get("type")
        if not policy_type:
            self.logger_obj.error("Validation failed: 'type' not found in policy")
            return False, "Policy type is missing"

        self.logger_obj.info(f"Validating policy type '{policy_type}'")
        expected_fields = expected_policy_fields.get(policy_type)

        if not expected_fields:
            self.logger_obj.error(f"Validation failed: Unknown policy type '{policy_type}'")
            return False, f"Unknown policy type: {policy_type}"

        # Use `validate_fields` to validate the policy fields
        self.logger_obj.info(f"Starting validation for type '{policy_type}'")
        valid, message = self.validate_fields(policy, expected_fields,policy_type)

        if valid:
            self.logger_obj.info(f"Validation successful for type '{policy_type}'")
        else:
            self.logger_obj.error(f"Validation failed for type '{policy_type}': {message}")

        return valid, message
    

    @allure.title("---- Upm: Verify Downloader uses persistent connection for downloading policy -----")
    @add_markers("regression")
    @pytest.mark.xray("QA-149386")
    def test_policy_download_uses_persistent_connection(self):
        """
             This function Verify Downloader uses persistent connection for downloading policy

            It performs the following steps:
            1. Doing validation on all the columns
            Args:
                self (object): Instance of the test class.
                Name of the table which needs to validate
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(f"Verify Downloader uses persistent connection for downloading policy")
        status = True
        msg = ""

        if self.upm_log_file_data is None:
            status = False
            msg = "Not able to fetch UPM log file data"
            assert status, msg

        pattern1 = re.compile(r'ZTCM: downloadPolicy: TPG session already exists')
        pattern2= re.compile(r'ZTCM: downloadPolicy: session is null. calling createTPGSession')
        matches1 = pattern1.findall(self.upm_log_file_data)
        matches2 = pattern2.findall(self.upm_log_file_data)

        # ZPD: policy downloader is not using persistent connection
        if len(matches1) < 20:
            msg = f"{pattern1} occurence is less, means is not using persistent connection"
            assert False,msg
        elif len(matches2) > 1:
            msg = f"Tpg created more than 2 session, which is invalid in ideal case\n {pattern2} -> it should be using persistent connection"
            assert False,msg
        else:
            msg = "ZTCM: downloadPolicy: TPG session already exists, ZTCM uses persistent connection"
            status = True

        return status, msg

    @allure.title("---- Verify that Device Stats plugin is started by Scheduler as soon as the UPM service is started -----")
    @add_markers("regression")
    @pytest.mark.xray("QA-149252")
    def test_verify_device_stats_plugin_start_as_soon_upm_service_started(self):
        """
        This function Verify that Device Stats plugin is started by Scheduler as soon as the UPM service is started
        """
        self.logger_obj.info(
            "\n---------------------------- DeviceStats: Verify that Device Stats plugin is started by Scheduler as soon as the UPM service is started ----------------------------\n")

        data = self.upm_log_file_data
        if data is None:
            self.logger_obj.error("Not able to fetch data from upm log file")
            assert False, "Not able to fetch data from upm log file"

        pattern1 = "Setting upm service state to: 1"
        pattern2 = "ZPC: Device Stats Plugin regular session started successfully"
        upm_stime = self.log_handler.find_log_line_timestamp_in_unix(data=data,pattern=pattern1,first_hit_exit=True)[0]
        device_stats_stime = self.log_handler.find_log_line_timestamp_in_unix(data=data,pattern=pattern2,first_hit_exit=True)[0]

        assert upm_stime and device_stats_stime, f"Not able to fetch {pattern1} || {pattern2} data timestamp from upm log file"

        self.logger_obj.info(f"UPM service started at timestamp: {upm_stime}\nDevice Stats Plugin regular session started at timestamp: {device_stats_stime}")
        if device_stats_stime > upm_stime +1:
            error_message = "Device Stats plugin is not started by Scheduler as soon as the UPM service is started"
            self.logger_obj.error(error_message)
            assert False, error_message

        self.logger_obj.info("Verified Device Stats plugin is  started by Scheduler as soon as the UPM service is started")

    @allure.title("Verify downloader sends SD discovery first before connecting to TPG")
    @pytest.mark.xray("QA-149376")
    @add_markers("regression")
    def test_policy_downloader_sends_sd_before_connecting_to_tpg(self):

        """
        Verify downloader sends SD discovery first before connecting to TPG

        It performs the following steps:
        1. When UPM starts, downloader plugin also starts and do a SD on TPG hostname.
        2. Downloader connects to TPG using the TPG endpoint given by PAC server. Once connected to TPG, the returned code is 200.
        3. Downloader receives the policy from TPG.
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        upm_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')
        thread_id = ""
        policy_downloader_logs = ""
        count = 0

        self.logger_obj.info("Fetching thread id for policy downloader thread")
        for item in upm_log_data.split("\n"):
            if "ZPD: UPM policy downloader thread started" in item:
                res = re.findall(r"\[([^\[\]]*)\]", item)
                thread_id = str(res[0])
                self.logger_obj.info(f"Thread id = {thread_id} found for policy downloader")
                break

        if len(thread_id) == 0:
            msg = f"No logs found for policy downloader"
            assert False, msg

        self.logger_obj.info("Collecting policy downloader thread data from logs")
        for item in upm_log_data.split("\n"):
            if thread_id in item:
                policy_downloader_logs = policy_downloader_logs + '\n' + item
            if "ZPD: downloadUpmPolicy succeeded with 200 OK, response str" in item:
                break

        self.logger_obj.info(f"Fetched policy downloader logs \n {policy_downloader_logs} \n")
        self.logger_obj.info("--- Validating policy download data ---")
        for item in policy_downloader_logs.split("\n"):

            if "ZSD: Service Discovery Response" in item:
                self.logger_obj.info(f"Service discovery response = {item} found for policy downloader")
                count += 1

            if f"getAvailableTPG: SD result: Url: https://smres.{self.zdx_cloud}.net" in item:
                self.logger_obj.info(f"Service discovery result = {item} found for policy downloader")
                count += 1

            if f"ZTCM: createTPGSession: policy download succeeded with host = smres.{self.zdx_cloud}.net. Returned code:200" in item:
                self.logger_obj.info(f"Found {item} for policy download")
                count += 1

            if "ZPD: downloadUpmPolicy succeeded with 200 OK" in item:
                self.logger_obj.info("downloadUpmPolicy succeeded with 200 OK")
                count += 1
                break

        assert count >= 4, "Something is missing in policy downloader, check captured log call to get full idea"

    @allure.title("ZDX Enabled from MA - Once ZDX is connected to TPG, all ZDX plugins should be started")
    @pytest.mark.xray(["QA-149414","QA-149378"])
    @add_markers("regression")
    def test_plugin_started(self):

        """
        This function tests the validation of ZDX plugins.
        It performs the following steps:
        1. Checking TPG plugin for zdx is started
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        upm_file = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['upm'][-1]
        patterns = [
            "Traceroute Plugin regular session started successfully",
            "Webprobe Plugin regular session started successfully",
            "Device Stats Plugin regular session started successfully"
        ]

        for pattern in patterns:
            self.logger_obj.info(f"Validating pattern = {pattern} from upm logs")
            result = help_return(self.log_obj.search_log_file, file=upm_file, words_to_find_in_line=pattern,
                               directory=self.const_obj.LOG_PATH)
            assert result[0], result[1] + f"\nPattern '{pattern}' not found in upm logs"
            self.logger_obj.info(f"Pattern = {pattern} found in the upm logs")


    @allure.title("Verify downloader gets TPG endpoints from PAC server https://pac.<zdxcloudname>.net/gettpgendpoints")
    @pytest.mark.xray(["QA-149375","QA-149399"])
    @add_markers("regression")
    def test_downloader_gets_tpg_endpoint_from_pac_server(self):

        """
        This function Verify downloader gets TPG endpoints from PAC server https://pac.<zdxcloudname>.net/gettpgendpoints
        It performs the following steps:
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("---------------- Verify downloader gets TPG endpoints from PAC server --------------------------\n"
                             f"ZUpmUtils::getUpmCloudEndpointUrl: endpoint URL https://pac.{self.zdx_cloud}.net/gettpgendpoints")

        required_lines = [
            f"ZUpmUtils::getUpmCloudEndpointUrl: endpoint URL https://pac.{self.zdx_cloud}.net/gettpgendpoints",
            f"ZUpmPacDownloader::downloading pac from https://pac.{self.zdx_cloud}.net/gettpgendpoints",
            f"populateTPGListInfo: appended smres.{self.zdx_cloud}.net to the TPG lists",
            f"ZTCM: createTPGSession: everything is fine"
        ]

        if self.upm_log_file_data is None:
            assert False, "Not able to fetch UPM log file data"

        # Maintain a pointer to track the current required line being matched
        current_index = 0

        for line in self.upm_log_file_data.splitlines():
            # If we haven't matched all required lines yet
            if current_index < len(required_lines):
                # Compile the current regex pattern
                pattern = re.compile(required_lines[current_index])
                match = pattern.search(line)

                # If the current line matches
                if match:
                    current_index += 1
                    self.logger_obj.info(f"Pattern = {pattern} found in the upm logs")

        # After processing all lines, ensure all required lines were found in order
        assert current_index == len(
            required_lines), "Log file is missing one or more required lines or they are out of order"


    @allure.title("Verify downloader is started when UPM starts")
    @pytest.mark.xray("QA-149374")
    @add_markers("regression")
    def test_downloader_started_when_upm_starts(self):

        """
        This function Verify downloader is started when UPM starts
        It performs the following steps:
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        pattern = f"ZPD: Start downloading UPM policy"
        upm_file = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['upm'][-1]

        self.logger_obj.info("Validating 'ZPD: Start downloading UPM policy' from upm logs")
        result = help_return(self.log_obj.search_log_file, file=upm_file, break_after_first_hit=True,
                             words_to_find_in_line=pattern, directory=self.const_obj.LOG_PATH, wait_time=5)
        assert result[0], result[1]

    @allure.title("Verify policy is downloaded every 15 seconds (according to policy_pull_interval)")
    @pytest.mark.xray("QA-149381")
    @add_markers("regression")
    def test_policy_download_pooled_at_15s_from_upm_log(self):
        """
             This function Verify policy is downloaded every 15 seconds (according to policy_pull_interval)

             Args:

             It performs the following steps:
             1. It will check time difference between policy fetch for LT session
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        status = True
        msg = ""

        if self.upm_log_file_data is None:
            assert False, "Not able to fetch UPM log file data"

        self.logger_obj.info("Verifying that the LT policy will be pooled at an interval of 15 seconds from upm logs")
        pattern_to_search = "ZPD: downloadUpmPolicy called"
        result = self.log_handler.find_log_line_timestamp_in_unix(self.upm_log_file_data, pattern=pattern_to_search)

        status = True
        msg = ""
        issues = []
        invalid_count=0

        # Check if any timestamps were found
        if len(result) == 0:
            status = False
            msg = "No policy interval fetched from logs"
            assert status,msg

        # Skipping first data as first policy can arrive at any time interval, after that we will have 15s gaps
        # Check if the time difference between timestamps is 15 seconds
        for index in range(2, len(result)):
            prev_timestamp = result[index - 1]
            curr_timestamp = result[index]

            time_difference = abs(curr_timestamp - prev_timestamp)
            if time_difference > 17 or time_difference < 13:
                invalid_count+=1
                msg = (f"Time difference between two policy downloads is {time_difference}"
                       f" which is incorrect, it should be 15 seconds")
                issues.append(msg)
            else:
                self.logger_obj.info(
                    f"Time difference found between two policy download {prev_timestamp} and {curr_timestamp} is 15s")

        if len(issues) * 100 >= 5 * len(result):
            status = False

        assert status, "\n\n\n".join(issues)

    @allure.title("Verify that device Profile is generated every time when Uploader wakes up to collect metrics JSON from each plugin (ie, every 5 minutes)")
    @pytest.mark.xray(["QA-149249","QA-149250"])
    @add_markers("regression")
    def test_device_profile_is_generated_every_5min_when_uploader_wakes_up(self):
        """
             Verify that device Profile is generated every time when Uploader wakes up to collect
             metrics JSON from each plugin (ie, every 5 minutes)

             Args:

             It performs the following steps:
             1. It will check time difference between device Profile is 5 mins
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        status = True
        msg = ""

        if self.upm_log_file_data is None:
            assert False, "Not able to fetch UPM log file data"

        self.logger_obj.info("Verify that device Profile is generated every time when Uploader wakes up to collect "
                             "metrics JSON from each plugin (ie, every 5 minutes)")

        pattern_to_search = "ZSU: Generating Device Profile"
        result = self.log_handler.find_log_line_timestamp_in_unix(self.upm_log_file_data, pattern=pattern_to_search)

        # Check if any timestamps were found
        if len(result) == 0:
            assert False,"No 'ZSU: Generating Device Profile' interval fetched from logs"
        elif len(result)<=2:
            pytest.skip("Not enough data to do data validation")

        for index in range(1, len(result) - 1):
            prev_timestamp = result[index]
            curr_timestamp = result[index+1]

            time_difference = abs(curr_timestamp - prev_timestamp)
            if time_difference > 301 or time_difference < 209:
                status = False
                msg = (f"Time difference between two 'ZSU: Generating Device Profile' is {time_difference}"
                       f" which is incorrect, it should be 300 seconds")
                break
            else:
                self.logger_obj.info(
                    f"Time difference found between two 'ZSU: Generating Device Profile' {prev_timestamp} and {curr_timestamp} is 300s")

        assert status, msg

    @allure.title("Verify the policy downloaded contains all the monitors: web, tr, devicestats")
    @pytest.mark.xray("QA-149379")
    @add_markers("regression")
    def test_policy_download_contains_all_the_upm_plugins(self):
        """
             Verify the policy downloaded contains all the monitors: web, tr, devicestats

             Args:

             It performs the following steps:
             1. Check in TPG policy that device stats are getting the following fields:
                type : “DEVICESTATS”,
                type: "TRACERT"
                type : "WEB"
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        if self.upm_log_file_data is None:
            assert False, "Not able to fetch UPM log file data"

        self.logger_obj.info("Fetching upm policy data from upm logs")
        policy_status = self.log_handler.get_regular_policy_json_from_log(path=self.const_obj.LOG_PATH)
        assert policy_status[0], policy_status[1]

        #Checking First policy
        policy_json = policy_status[2][0]
        try:
            policy = policy_json['policy']
            monitors = policy['monitors']

            self.logger_obj.info(f" ############### Fetched {len(monitors)} monitors from the policy  ############### ")

            for monitor in monitors:
                self.logger_obj.info(f"-----------Validating monitor of type '{monitor.get('type', 'UNKNOWN')}'-----------")
                valid, message = self.validate_webprobe_traceroute_policy(monitor)
                assert valid, f"Validation failed for monitor {monitor['type']}: {message}"

            self.logger_obj.info("||||||||||||||    Policy validation successful for all monitors    ||||||||||||||")

        except Exception as e:
            self.logger_obj.error(f"Unexpected error occurred during policy validation: {e}")
            assert False, f"Unexpected error occurred: {e}"

    @allure.title("Verify the device stats plugin policy")
    @pytest.mark.xray("QA-149385")
    @add_markers("regression")
    def test_device_stats_plugin_policy_from_upm_logs(self):
        """
        Verify device stats plugin policy from UPM log file.

        Steps:
        1. Ensure policy log file data can be fetched.
        2. Extract and validate the "DEVICESTATS" policy with the following fields:
           - type: "DEVICESTATS"
           - id: always "1"
           - appid: always "101"
           - apptype: always "PREDEFINED"
           - frequency: 20
           - device_stats > stats: "CPU,MEMORY,DISK,NETWORK,BATTERY,WIFI"

        Args:
            self (object): Instance of the test class.

        Raises:
            AssertionError: If any step fails or incorrect values are found.
        """

        # Step 1: Fetch raw policy data from logs
        if self.upm_log_file_data is None:
            self.logger_obj.error("Not able to fetch UPM log file data")
            assert False, "Not able to fetch UPM log file data"

        self.logger_obj.info("Fetching UPM policy data from UPM logs")
        policy_status = self.log_handler.get_regular_policy_json_from_log(path=self.const_obj.LOG_PATH)
        assert policy_status[0], policy_status[1]

        # Step 2: Extract the policy JSON and ensure correct structure
        policy_json = policy_status[2][0]
        try:
            policy = policy_json.get('policy', {})
            monitors = policy.get('monitors', [])

            if not monitors:
                self.logger_obj.error("No monitors found in policy JSON")
                assert False, "No monitors found in policy JSON"

            # Step 3: Locate the "DEVICESTATS" policy from the monitors list
            self.logger_obj.info("Searching for 'DEVICESTATS' policy in the policy JSON")
            device_stats_policy = next((mtr for mtr in monitors if mtr['type'] == 'DEVICESTATS'), None)

            if not device_stats_policy:
                self.logger_obj.error("No 'DEVICESTATS' policy found in policy JSON")
                assert False, "No 'DEVICESTATS' policy found in policy JSON"

            self.logger_obj.info(f"Fetched 'DEVICESTATS' policy: {device_stats_policy}")

            # Step 4: Validate individual fields of the "DEVICESTATS" policy
            self.logger_obj.info("Validating key attributes of 'DEVICESTATS' policy")

            validations = [
                (device_stats_policy['id'] == 1, "'id' should be 1"),
                (device_stats_policy['appid'] == 101, "'appid' should be 101"),
                (device_stats_policy['apptype'] == "PREDEFINED", "'apptype' should be 'PREDEFINED'"),
                (device_stats_policy['frequency'] == 20, "'frequency' should be 20"),
                (device_stats_policy['device_stats']['stats'] == "CPU,MEMORY,DISK,NETWORK,BATTERY,WIFI",
                 "'device_stats > stats' should be 'CPU,MEMORY,DISK,NETWORK,BATTERY,WIFI'")
            ]

            invalid_fields = [msg for valid, msg in validations if not valid]
            if invalid_fields:
                error_message = f"Validation failures in 'DEVICESTATS' policy: {', '.join(invalid_fields)}"
                self.logger_obj.error(error_message)
                assert False, error_message

            self.logger_obj.info("All validations for 'DEVICESTATS' policy passed successfully")

        except Exception as e:
            self.logger_obj.exception(f"Unexpected error during policy validation: {e}")
            assert False, f"Unexpected error during policy validation: {e}"

    @allure.title('Verify other than monitor policies, downloader gets the following policies: "version", '
                  '"deviceid", "from_zscaler", "public_ip", "upload_frequency", "policy_pull_interval", '
                  '"metrics_request_timeout", "device_inventory_policy", "global_virtual_ips" ')
    @pytest.mark.xray("QA-149380")
    @add_markers("regression")
    def test_policy_from_upm_logs(self):
        """
        Test policy from UPM logs.

        Steps:
        1. Verify downloader gets the following policies beyond monitor policies:
           - "version"
           - "deviceid"
           - "public_ip"
           - "upload_frequency"
           - "policy_pull_interval"
           - "metrics_request_timeout"
           - "device_inventory_policy"
           - "global_virtual_ips"

        Args:
            self (object): Instance of the test class.

        Raises:
            AssertionError: If any step fails.
        """

        # Step 1: Verify UPM log file data availability
        if self.upm_log_file_data is None:
            self.logger_obj.error("Not able to fetch UPM log file data")
            assert False, "Not able to fetch UPM log file data"

        self.logger_obj.info("Fetching UPM policy data from UPM logs")
        policy_status = self.log_handler.get_regular_policy_json_from_log(path=self.const_obj.LOG_PATH)
        assert policy_status[0], policy_status[1]

        # Step 2: Extract the policy JSON and validate structure
        policy_json = policy_status[2][0]
        try:
            policy = policy_json.get("policy", {})
            required_fields = [
                "version",
                "deviceid",
                "public_ip",
                "upload_frequency",
                "policy_pull_interval",
                "metrics_request_timeout",
                "device_inventory_policy",
                "global_virtual_ips",
            ]

            # Ensure all required fields exist in the policy
            missing_fields = [field for field in required_fields if field not in policy]
            if missing_fields:
                self.logger_obj.error(f"Missing required fields in policy JSON: {missing_fields}")
                assert False, f"Missing required fields in policy JSON: {missing_fields}"

            # Step 3: Validate individual fields
            self.logger_obj.info(f"Validating required field {required_fields}")
            validations = [
                (policy["version"] is not None and int(policy["version"]) > 0, "policy_version", policy["version"]),
                (policy["metrics_request_timeout"] is not None and int(policy["metrics_request_timeout"]) > 0,
                 "metrics_request_timeout", policy["metrics_request_timeout"]),
                (policy["deviceid"] is not None and int(policy["deviceid"]) > 0, "device_id", policy["deviceid"]),
                (policy["upload_frequency"] == 300, "upload_frequency", policy["upload_frequency"]),
                (policy["policy_pull_interval"] is not None and int(policy["policy_pull_interval"]) == 15,
                 "policy_pull_interval", policy["policy_pull_interval"]),
                (self.validate_ip_format(policy["public_ip"])[0], "public_ip", policy["public_ip"]),
                (policy["device_inventory_policy"] is not None and len(str(policy["device_inventory_policy"])) >= 20,
                 "device_inventory_policy", policy["device_inventory_policy"]),
            ]

            invalid_fields = [
                f"'{field_name}' validation failed with value '{value}'" for valid, field_name, value in validations if
                not valid
            ]
            if invalid_fields:
                error_message = f"Validation failures: {', '.join(invalid_fields)}"
                self.logger_obj.error(error_message)
                assert False, error_message

            # Step 4: Validate 'global_virtual_ips'
            self.logger_obj.info("Validating 'global_virtual_ips'")
            for global_virtual_ip in policy["global_virtual_ips"]:
                result = self.validate_ip_format(global_virtual_ip)
                if not result[0]:
                    self.logger_obj.error(f"Validation failed for global_virtual_ip: {global_virtual_ip}")
                    assert False, result[1]

            self.logger_obj.info("All validations passed successfully for policy from UPM logs")

        except Exception as e:
            self.logger_obj.exception(f"Unexpected error occurred during policy validation: {e}")
            assert False, f"Unexpected error occurred during policy validation: {e}"

    @allure.title("Verify that the ZDX related log files have proper naming conventions")
    @pytest.mark.xray("QA-149356")
    @add_markers("regression")
    def test_zdx_related_log_files_have_proper_naming_convention(self):
        """
        Verify that the ZDX related log files have proper naming conventions.

        Steps:
        1. Check if the following log files exist with the proper naming conventions:
           - ZSAUpm_* with pattern 'ZSAUpm_[0-9-_.].log'
           - ZSAUpm-Webload_* with pattern 'ZSAUpm-Webload_.+.log'
           - ZSAUpm-ZTraceroute_* with pattern 'ZSAUpm-ZTraceroute_.+.log'
           - ZSAUpm-DeviceStats_* with pattern 'ZSAUpm-DeviceStats_.+.log'
        2. If any log file is missing, the test fails.

        Args:
            self (object): Instance of the test class.

        Raises:
            AssertionError: If any log file is missing or not following the naming convention.
        """

        # Step 1: Get the latest log file dictionary
        log_file_dict = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)

        # Log file categories to validate, with their expected log count and error messages
        log_categories = {
            'upm': {
                'pattern': 'ZSAUpm_[0-9-_.]+.log',
                'error_message': "No UPM log file present in the logs with pattern 'ZSAUpm_[0-9-_.].log'",
                'success_message': "'ZSAUpm_' file has proper naming convention"
            },
            'web': {
                'pattern': 'ZSAUpm_ZWebload_.+.log',
                'error_message': "No ZSAUpm_ZWebload log file present in the logs with pattern 'ZSAUpm-Webload_.+.log'",
                'success_message': "'ZSAUpm_ZWebload' file has proper naming convention"
            },
            'traceroute': {
                'pattern': 'ZSAUpm_ZTraceroute_.+.log',
                'error_message': "No ZSAUpm_ZTraceroute log file present in the logs with pattern 'ZSAUpm-ZTraceroute_.+.log'",
                'success_message': "'ZSAUpm_ZTraceroute' file has proper naming convention"
            },
            'deviceStats': {
                'pattern': 'ZSAUpm_DeviceStats_.+.log',
                'error_message': "No ZSAUpm_DeviceStats log file present in the logs with pattern 'ZSAUpm-DeviceStats_.+.log'",
                'success_message': "'ZSAUpm_DeviceStats' file has proper naming convention"
            },
        }

        # Step 2: Validate the presence of log files and their naming conventions
        try:
            for category, info in log_categories.items():
                self.logger_obj.info(f"Verify that the '{category}' related log files have proper naming conventions")
                log_files = log_file_dict.get(category, [])

                if len(log_files) == 0:
                    self.logger_obj.error(info['error_message'])
                    assert False, info['error_message']
                else:
                    self.logger_obj.info(info['success_message'])

        except Exception as e:
            self.logger_obj.exception(f"Unexpected error occurred while validating log files: {e}")
            assert False, f"Unexpected error occurred while validating log files: {e}"

        self.logger_obj.info("All ZDX related log files have proper naming conventions")

    @allure.title("Verify downloader creates TPG session with TPG hostname")
    @pytest.mark.xray(["QA-149377","QA-149378","QA-149413"])
    @add_markers("regression")
    def test_zdx_tpg_session(self):
        """
        This function tests the validation of zdx tgp session.
        It performs the following steps:
        1. Checking TPG session created
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        upm_file = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['upm']
        if len(upm_file) == 0:
            assert False, "No UPM log file present in the exported logs"

        result = help_return(self.log_obj.search_log_file, file=upm_file[-1],directory=self.const_obj.LOG_PATH,
                             words_to_find_in_line=["ZTCM: createTPGSession: metrics upload succeeded with host" ])
        assert result[0], result[1]

        result = help_return(self.log_obj.search_log_file, file=upm_file[-1],directory=self.const_obj.LOG_PATH,
                             words_to_find_in_line=["ZTCM: createTPGSession: policy download succeeded with host"])
        assert result[0], result[1]

    @allure.title("New Policy - All plugins will be stopped and started when there is new policy version number")
    @pytest.mark.xray("QA-149383")
    @pytest.mark.skip("Required helper function changes")
    @add_markers("regression")
    def test_plugin_restart_when_new_policy_arrived(self):
        """
        This function tests the validation of zdx tgp session.
        It performs the following steps:
        1. test_plugin_restart_when_new_policy_arrived
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        status, data, msg = self.log_handler.verify_plugins_restart_when_policy_received()

        if not status:
            if data:
                error_message = f"Plugin restart failed during policy update: {str(data)}"
                self.logger_obj.info(error_message)
                assert False, error_message

            else:
                error_message = "Plugin restart failed during policy update without detailed data."
                self.logger_obj.info(error_message)
                assert False, error_message

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Deletion of forward profile and app profile
        2. Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Closing db connection after verification")

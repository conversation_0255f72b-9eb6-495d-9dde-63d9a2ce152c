import time
import pytest, allure
import os
from common_lib.Custom_Markers import add_markers
from OS_Android.test_suite.zcc_zdx.zdx_regression.tunnel1_mode import conftest

test_input_tbl_sys_info = [
    pytest.param('select os_name from tbl_system_info', 'os_name', marks=pytest.mark.xray("QA-149215")),
    pytest.param('select os_version from tbl_system_info', 'os_version', marks=pytest.mark.xray("QA-149216")),
    pytest.param('select os_build from tbl_system_info', 'os_build', marks=pytest.mark.xray("QA-149217")),
    pytest.param('select sys_type from tbl_system_info', 'sys_type', marks=pytest.mark.xray("QA-149220")),
    pytest.param('select sys_model from tbl_system_info', 'sys_model', marks=pytest.mark.xray("QA-149219")),
    pytest.param('select sys_mfg from tbl_system_info', 'sys_mfg', marks=pytest.mark.xray("QA-149218")),
    pytest.param('select hw_serial_no from tbl_system_info', 'hw_serial_no', marks=pytest.mark.xray("QA-149222")),
    pytest.param('select hostname from tbl_system_info', 'hostname', marks=pytest.mark.xray("QA-149221")),
    pytest.param('select username from tbl_system_info', 'username', marks=pytest.mark.xray("QA-149225")),
    pytest.param('select gpu from tbl_system_info', 'gpu', marks=pytest.mark.xray("QA-149230")),
    pytest.param('select last_bootup_time from tbl_system_info', 'last_bootup_time', marks=pytest.mark.xray("QA-149226"))
]

test_input_tbl_disk_info = [
    pytest.param('select size from tbl_disk_info','size', "128000000000", marks=pytest.mark.xray("QA-151966")),
    pytest.param('select type from tbl_disk_info','type', "Internal shared storage", marks=pytest.mark.xray("QA-151966")),
]

test_input_tbl_cpu_info = [
    pytest.param('select num_of_cores from tbl_cpu_info','num_of_cores', marks=pytest.mark.xray("QA-151965")),
    pytest.param('select num_logical_processors from tbl_cpu_info','num_logical_processors', marks=pytest.mark.xray("QA-151965")),
    pytest.param('select max_speed_mhz from tbl_cpu_info','max_speed_mhz', marks=pytest.mark.xray("QA-151965")),
    pytest.param('select cur_speed_mhz from tbl_cpu_info','cur_speed_mhz', marks=pytest.mark.xray("QA-151965")),
    pytest.param('select manufacturer from tbl_cpu_info','manufacturer', marks=pytest.mark.xray("QA-151965")),
]

test_input_tbl_interface_details = [
    pytest.param('select adapter_name from tbl_interface_details order by timestamp desc','adapter_name', marks=pytest.mark.xray("QA-58368")),
    pytest.param('select ifname from tbl_interface_details order by timestamp desc','ifname',  marks=pytest.mark.xray("QA-58368")),
    pytest.param('select type from tbl_interface_details order by timestamp desc','type', marks=pytest.mark.xray("QA-58368")),
    pytest.param('select status from tbl_interface_details order by timestamp desc','status', marks=pytest.mark.xray("QA-58368")),
    pytest.param('select ipv4 from tbl_interface_details order by timestamp desc','ipv4', marks=pytest.mark.xray("QA-58368")),
    pytest.param('select ipv6 from tbl_interface_details order by timestamp desc','ipv6', marks=pytest.mark.xray("QA-58368")),
    pytest.param('select dns_svrs from tbl_interface_details order by timestamp desc','dns_svrs', marks=pytest.mark.xray("QA-58368"))
]


class TestTblSystemInfo:
    @allure.title("Setup Method")
    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.db_handler = conftest.db_handler
        self.logger_obj = conftest.logger_obj
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_device_profile.db"
        self.value_dict = {}

        username = self.export_logs.appinfo_file_values('UserName')
        user = username.split("@")[-1]
        hardware_id = self.export_logs.appinfo_file_values('Hardware Id')
        os_version = self.sys_ops.and_os_version()
        os_build = self.export_logs.appinfo_file_values('Build Id')
        sys_model = self.export_logs.appinfo_file_values('Model')
        sys_mfg = self.export_logs.appinfo_file_values('Manufacturer')
        hw_serial_no = self.export_logs.appinfo_file_values('Hardware Id')
        hostname = f"{hardware_id}@{user}"
        username = f"{hardware_id}@{user}"
        sys_type = 'Android'
        os_name = 'Android'
        gpu = "ARM Mali-G715"

        device_type = self.sys_ops.android_device_type()
        if device_type.lower() == "chromeos":
            sys_type = "Chrome OS"
            os_name = "Chrome OS"

        self.value_dict['os_name'] = os_name
        self.value_dict['os_version'] = os_version
        self.value_dict['os_build'] = os_build
        self.value_dict['sys_type'] = sys_type
        self.value_dict['sys_model'] = sys_model
        self.value_dict['sys_mfg'] = sys_mfg
        self.value_dict['hw_serial_no'] = hw_serial_no
        self.value_dict['hostname'] = hostname
        self.value_dict['username'] = username
        self.value_dict['gpu'] = gpu

        self.logger_obj.info("Making connection to DB")
        self.db_handler.create_db_connection(self.db_path)

    @allure.title("Validate device profile db data")
    @pytest.mark.parametrize("query, key", test_input_tbl_sys_info)
    @add_markers("regression")
    def test_tbl_system_info(self, query, key ):
        """
        This function tests the validation of tbl_system_info table of device profile.
        It performs the following steps:
        1. Doing validation on device_profile table for zdx
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(f"Sending query to fetch {key} value from db")
        current_row = 0
        output = self.db_handler.execute_query(query)

        self.logger_obj.info(f"Validating {key} value = {output[0][key]} value in db with device value ")
        if key == "gpu":
            # currently there no way to fetch gpu value, so validating for non empty value
            if len(str(output[0][key])) == 0:
                status = False
                msg = f"No value found at 'gpu' in table tbl_system_info"
                assert False, msg
            else:
                self.value_dict[key] = output[0][key]
        elif key == "last_bootup_time":
            last_bootup_time = self.sys_ops.get_last_bootup_time()
            if not last_bootup_time[0]:
                pytest.skip(f"Failed {last_bootup_time[1]}")

            last_bootup_time = last_bootup_time[1]
            last_bootup_time = int(last_bootup_time)

            value_from_db = int(output[0][key]/1000)
            buffer = abs(value_from_db - last_bootup_time)
            if buffer > 5:
                assert False, f"Value mismatched of last_bootup_time. Db::From device = {value_from_db}::{last_bootup_time}"
            else:
                self.value_dict['last_bootup_time'] = output[0][key]
        else:
            assert output[0][key] == self.value_dict[key]

        for row in output:
            current_row+=1
            if row[0] != self.value_dict[key]:
                assert False, "Value not matched in db and actual system value"
            else:
                self.logger_obj.info(f"Validating {key} value = {row[0]} from row = {current_row} ")

    @allure.title("teardown method")
    def teardown_class(self):
        self.logger_obj.info("Closing connection to DB")
        self.db_handler.close_db_connection()


class TestTblDiskInfo:

    @allure.title("Setup Method")
    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.db_handler = conftest.db_handler
        self.logger_obj = conftest.logger_obj
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_device_profile.db"
        self.value_dict = {}

        self.logger_obj.info("Making connection to DB")
        self.db_handler.create_db_connection(self.db_path)


    @allure.title("Validate tbl_disk_info table from device profile db ")
    @pytest.mark.parametrize("query, key, value", test_input_tbl_disk_info)
    @add_markers("regression")
    def test_tbl_disk_info(self, query, key, value):

        """
        This function tests the validation tbl_disk_info table in device profile db
        It performs the following steps:
        1. Doing validation on diskinfo table for zdx
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        self.logger_obj.info(f"Sending query to fetch {key} value from db")
        current_row =0
        output = self.db_handler.execute_query(query)

        self.logger_obj.info(f"validating {key} value = {output[0][key]} value in db ")
        assert output[0][key] == value

        self.value_dict[key] = value
        for row in output:
            current_row += 1
            if row[0] != self.value_dict[key]:
                assert False, "Value not matched in db and actual system value"
            else:
                self.logger_obj.info(f"Validating {key} value = {row[0]} from row = {current_row} ")

    @allure.title("teardown method")
    def teardown_class(self):
        self.logger_obj.info("Closing connection to DB")
        self.db_handler.close_db_connection()


class TestTblCpuInfo:

    @allure.title("Setup Method")
    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.db_handler = conftest.db_handler
        self.logger_obj = conftest.logger_obj
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_device_profile.db"

        num_of_cores = self.sys_ops.get_no_cpu_cores()
        num_logical_processors = num_of_cores
        max_speed_mhz = self.sys_ops.get_max_freq()
        cur_speed_mhz = self.sys_ops.get_avg_freq()
        manufacturer = 'Kirin '

        self.value_dict = {}
        self.value_dict['num_of_cores'] = num_of_cores
        self.value_dict['num_logical_processors'] = num_logical_processors
        self.value_dict['max_speed_mhz'] = str(max_speed_mhz)
        self.value_dict['cur_speed_mhz'] = str(cur_speed_mhz)
        self.value_dict['manufacturer'] = manufacturer

        self.logger_obj.info("Making connection to DB")
        self.db_handler.create_db_connection(self.db_path)

    @allure.title("Validate tbl_cpu_info table")
    @pytest.mark.parametrize("query, key", test_input_tbl_cpu_info)
    @add_markers("regression")
    def test_tbl_cpu_info(self, query, key):
        """
        This function tests the validation ZDX db tables
        It performs the following steps:
        1. Doing validation on cpu table for zdx
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """
        self.logger_obj.info(f"Sending query to fetch {key} value from db")
        output = self.db_handler.execute_query(query)
        current_row = 0

        self.logger_obj.info(f"validating {key} value = {output[0][key]} value in db ")
        if key == "manufacturer":
            # currently there no way to fetch manufacturer value, so validating for non-empty value
            if len(str(output[0][key])) == 0:
                status = False
                msg = f"No value found at 'manufacturer' in table tbl_cpu_info"
                assert False, msg
            else:
                self.value_dict[key] = output[0][key]

        assert output[0][key] == self.value_dict[key]

        for row in output:
            current_row+=1
            if row[0] != self.value_dict[key]:
                assert False , "Value not matched in db and actual system value"
            else:
                self.logger_obj.info(f"Validating {key} value = {row[0]} from row = {current_row} ")

    @allure.title("teardown method")
    def teardown_class(self):
        self.logger_obj.info("Closing connection to DB")
        self.db_handler.close_db_connection()


class TestTblIntDetails:

    @allure.title("Setup Method")
    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.db_handler = conftest.db_handler
        self.logger_obj = conftest.logger_obj
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_device_profile.db"

        device_type = self.sys_ops.android_device_type()
        act_net = self.export_logs.appinfo_file_values('Active Network')
        if act_net == "wlan0":
            nw_type = "Wireless"
        elif device_type.lower() == "chromeos" and act_net == "eth5":
            nw_type = "Wireless"
        else:
            nw_type = "Cellular"
        ipv4 = self.sys_ops.active_ipv4(act_net)
        self.ipv6_list = self.sys_ops.list_of_active_ipv6(act_net)

        adapter_name = act_net
        ifname = act_net
        status = 'Connected'
        network_info_dict = self.sys_ops.get_network_info(ifname)
        assert network_info_dict[0],network_info_dict[1]
        data_dict = network_info_dict[2]
        self.logger_obj.info(f"##########################  Network Info Details start ##########################\n")
        for key, value in data_dict.items():
            if isinstance(value, list):
                print(f"{key}: {', '.join(value)}")
            else:
                print(f"{key}: {value}")

        self.logger_obj.info(f"\n##########################  Network Info Details ends ##########################")
        dns = network_info_dict[2]['DnsAddresses']

        self.value_dict = {}
        self.value_dict['adapter_name'] = adapter_name
        self.value_dict['ifname'] = ifname
        self.value_dict['type'] = nw_type
        self.value_dict['status'] = status
        self.value_dict['ipv4'] = ipv4
        self.value_dict['dns_svrs'] = dns

        self.logger_obj.info("Making connection to DB")
        self.db_handler.create_db_connection(self.db_path)


    @allure.title("Validate test_interface_details table from device profile")
    @pytest.mark.parametrize("query, key", test_input_tbl_interface_details)
    @add_markers("regression")
    def test_interface_details(self, query, key):

        """
        This function tests the validation ZDX db tables
        It performs the following steps:
        1. Doing validation on interface table for zdx
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """
        self.logger_obj.info(f"Sending query to fetch {key} value from db")
        output = self.db_handler.execute_query(query)
        current_row = 0

        self.logger_obj.info(f"validating {output[0][key]} value in db ")
        if key == 'ipv6':
            if output[0][key] in self.ipv6_list:
                self.value_dict[key] = output[0][key]
            else:
                assert False, "Incorrect value of ipv6 column in tbl_interface_details"

        assert output[0][key] in self.value_dict[key], "Data doesn't match in db"

        for row in output:
            current_row+=1
            if row[0] not in self.value_dict[key]:
                assert False , "Value not matched in db and actual system value"
            else:
                self.logger_obj.info(f"Validating {key} value = {row[0]} from row = {current_row} ")

    @allure.title("teardown method")
    def teardown_class(self):
        self.logger_obj.info("Closing connection to DB")
        self.db_handler.close_db_connection()

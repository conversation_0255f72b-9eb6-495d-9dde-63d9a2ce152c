import time, re, json
import pytest, allure
from OS_Android.test_suite.zcc_zdx.zdx_regression.tunnel1_mode import conftest
from common_lib.Custom_Markers import add_markers
from datetime import datetime, timedelta
import numpy as np

device_stats_tables = ["tbl_battery_status", 'tbl_cpu_usage', 'tbl_disk_io', 'tbl_memory_usage',
                       'tbl_network_interface', 'tbl_mon_processes', 'tbl_wifi_info']


class TestDeviceStats:

    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.service_ent = conftest.service_ent
        self.logger_obj = conftest.logger_obj
        self.log_handler = conftest.log_handler
        self.device_type = self.sys_ops.android_device_type()
        self.act_net = self.export_logs.appinfo_file_values('Active Network')
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_device_stats.db"

        self.db_handler.create_db_connection(self.db_path)

    def verify_and_validate_devicestats_data(self, table_name, columns, islt):
        """
        Validates specified columns in a database table against predefined rules.

        Parameters:
        - table_name (str): Base name of the table (prefix handled internally).
        - columns (list): List of columns to validate.
        - islt (int): Flag to determine if table is long-term; adds 'lt_session_id' if set.

        Returns:
        - (bool, str): True with "SUCCESS" if all validations pass, else False with issues listed.
        """

        actual_table_name = f"tbl_{table_name}" if islt == 0 else f"tbl_lt_{table_name}"
        columns.append('lt_session_id') if islt == 1 else None

        self.logger_obj.info(f"Verifying data from {actual_table_name}...")
        rows_data = self.db_handler.execute_query(f"SELECT * FROM {actual_table_name}")

        if not rows_data:
            pytest.skip(f"'{actual_table_name}' is empty.")
        column_rules = {
            'pct_total': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'pct_kernel': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'pct_user': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'pct_idle': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'rssi_dbm': {'allow_empty': False, 'data_type': 'float', 'min_value': -100, 'max_value': 100},
            'tx_pkts': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100000000},
            'rx_pkts': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100000000},
            'rxmit_pkts': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100000000},
            'rxmit_pct': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'snr': {'allow_empty': False, 'data_type': 'float', 'min_value': -1, 'max_value': 100},
            'mcs': {'allow_empty': False, 'data_type': 'float', 'min_value': -1, 'max_value': 100},
            'signal_quality': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'rd_bps': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100000000000},
            'wt_bps': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100000000000},
            'free_mb': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 10000000000},
            'avg_diskq_len_scale_1000': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 10000000000},
            'pct_used': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'cpu_total_pct': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'mem_wss_pct': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'isWifi': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 1},
            'is_active': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 1},
            'bandwidth_mbps': {'allow_empty': False, 'data_type': 'float', 'min_value': -1, 'max_value': 10000000},
            'recv_bps': {'allow_empty': False, 'data_type': 'float', 'min_value': -1, 'max_value': 10000000},
            'send_bps': {'allow_empty': False, 'data_type': 'float', 'min_value': -1, 'max_value': 10000000},
            'refid': {'allow_empty': True, 'data_type': 'string'},
            'ifname': {'allow_empty': True, 'data_type': 'string'},
            'timestamp': {'allow_empty': False, 'data_type': 'string'},
            'ssid': {'allow_empty': True, 'data_type': 'string'},
            'total': {'allow_empty': False, 'data_type': 'string'},
            'free': {'allow_empty': False, 'data_type': 'string'},
            'used': {'allow_empty': False, 'data_type': 'string'},
            'swap_used': {'allow_empty': False, 'data_type': 'string'},
            'pid': {'allow_empty': False, 'data_type': 'string'},
            'name': {'allow_empty': False, 'data_type': 'string'},
            'cpu_user': {'allow_empty': False, 'data_type': 'string'},
            'cpu_kernel': {'allow_empty': False, 'data_type': 'string'},
            'cpu_total': {'allow_empty': False, 'data_type': 'string'},
            'mem_wss_bytes': {'allow_empty': False, 'data_type': 'string'},
            'guid': {'allow_empty': False, 'data_type': 'string'},
            'disk_name': {'allow_empty': False, 'data_type': 'string'}
        }

        all_issues = []

        try:
            schema_query = f"PRAGMA table_info({actual_table_name});"
            schema = self.db_handler.execute_query(schema_query)
            db_columns = [col['name'] for col in schema]
        except Exception as e:
            self.logger_obj.error(f"Error fetching table schema for '{actual_table_name}': {e}")
            return False, f"Error fetching table schema for '{actual_table_name}'."

        invalid_columns = [col for col in columns if col not in db_columns]
        if invalid_columns:
            invalid_columns_str = ", ".join(invalid_columns)
            self.logger_obj.warn(f"Columns {invalid_columns_str} do not exist in the table '{actual_table_name}'. Skipping these columns.")
            columns = [col for col in columns if col in db_columns]

        for row_idx, row_data in enumerate(rows_data, start=1):
            row_issues = []
            self.logger_obj.info(f"{'#' * 20} Verifying row {row_idx}, table: '{actual_table_name}' {'#' * 20}\n")

            if row_idx ==1 and table_name in ["network_interface","mon_processes"]:
                self.logger_obj.info(f"Skipping first row check for {table_name} because 1st value is most of the time negative\n")
                continue

            for col in columns:
                value = str(row_data[col])
                self.logger_obj.info(f"Verifying column '{col}': {value}")

                if col not in column_rules:
                    self.logger_obj.info(f"Skipping column '{col}' as it is not defined in column rules.")
                    continue

                rules = column_rules[col]

                # Special check for network_interface table
                if table_name == "network_interface" and col in ['bandwidth_mbps', 'send_bps', 'recv_bps']:
                    try:
                        is_active = float(row_data['is_active'])
                        value_float = float(value)
                        if is_active == 1 and value_float < 0:
                            msg = f"Invalid value '{value}' for '{col}' in row {row_idx}, table: '{actual_table_name}'. Should be non-negative when is_active=1."
                            self.logger_obj.error(msg)
                            row_issues.append(msg)
                    except (ValueError, TypeError) as e:
                        msg = f"Error validating '{col}' in row {row_idx}, table: '{actual_table_name}': {e}"
                        self.logger_obj.error(msg)
                        row_issues.append(msg)
                    continue  # skip normal rule check for these columns

                if not value and not rules.get('allow_empty', True):
                    msg = f"Empty value found at '{col}' in row {row_idx}, table: '{actual_table_name}'. It should not be empty."
                    self.logger_obj.error(msg)
                    row_issues.append(msg)

                elif rules['data_type'] in ['float', 'int']:
                    try:
                        value_float = float(value)
                    except ValueError:
                        msg = f"Invalid value '{value}' for column '{col}' in row {row_idx}, table: '{actual_table_name}'. It should be a number."
                        self.logger_obj.error(msg)
                        row_issues.append(msg)
                        continue

                    if 'min_value' in rules and value_float < rules['min_value']:
                        msg = f"Invalid value '{value}' for column '{col}' in row {row_idx}, table: '{actual_table_name}'. It must be >= {rules['min_value']}."
                        self.logger_obj.error(msg)
                        row_issues.append(msg)
                    if 'max_value' in rules and value_float > rules['max_value']:
                        msg = f"Invalid value '{value}' for column '{col}' in row {row_idx}, table: '{actual_table_name}'. It must be <= {rules['max_value']}."
                        self.logger_obj.error(msg)
                        row_issues.append(msg)

                elif rules['data_type'] == 'string':
                    if not isinstance(value, str):
                        msg = f"Invalid value '{value}' for column '{col}' in row {row_idx}, table: '{actual_table_name}'. It should be a string."
                        self.logger_obj.error(msg)
                        row_issues.append(msg)

            if not row_issues:
                self.logger_obj.info(f"Row {row_idx} successfully verified with no issues, table: '{actual_table_name}'.")

            if row_issues:
                all_issues.extend(row_issues)

        if all_issues:
            return False, "\n".join(all_issues)
        else:
            self.logger_obj.info(f"Successfully verified all rows in table '{actual_table_name}'. No issues found.\n")
            return True, "SUCCESS"

    @allure.title("Get one Timestamp before pattern timestamp where data is present")
    def create_timestamp_dict(self, pattern_data: dict[int, dict])->dict [int,int]:
        """
        Creates a dictionary where the keys are the current timestamps and the values are the
        corresponding previous timestamps.

        Args:
        pattern_data (dict[int, dict]): A dictionary of timestamps where each key is a timestamp and each value is a dictionary of data.

        Returns:
        dict[int, int]: A dictionary where the keys are the previous timestamps and the values are the corresponding current timestamps.

        """
        timestamp_dict = {}
        timestamps = sorted(pattern_data.keys())
        for i in range(len(timestamps)):
            if i > 0:
                timestamp_dict[timestamps[i - 1]] = timestamps[i]
        return timestamp_dict

    @allure.title("Calculate the specified percentiles of the given data.")
    def calculate_percentiles(self, data: list) -> list:
        """
        Calculate the specified percentiles of the given data.
    
        Args:
            data (list): A list of numbers.
        Returns:
            list: A list of calculated percentiles.
    
        Raises:
            ValueError: If the input data is empty.
        """
        if not data:
            raise ValueError("Input data is empty")
    
        ret = []
        # Calculate the values at the specified percentiles
        for percentile in [50, 75, 90, 95, 99]:
            ret.append(np.percentile(data, percentile))
        # Round the calculated percentiles to two decimal places
        ret = [round(val, 2) for val in ret]
        return ret
    # Generated by Devraj ., assisted by ZCoder on 01-July-2025 <Please retain this marker>
    


    @allure.title("DeviceStats: Verify Battery Stats from DeviceStats db")
    @pytest.mark.xray(["QA-149258", "QA-149246", "QA-149247"])
    @add_markers("regression")
    def test_tbl_battery_status_db(self):
        """
            This function tests the validation of tbl_battery_stats table from device_stats db
            It performs the following steps:
            1. Doing validation on all the columns and also checking correct battery percentage
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("Verifying tbl_battery_status data from device_stats_db")
        query = "SELECT level_pct from tbl_battery_status order by timestamp desc"
        rows_data = self.db_handler.execute_query(query)
        status = True
        msg = ""

        last_value_pct_inserted_in_db = -1
        result = self.sys_ops.get_battery_percentage()
        assert result[0],result[1]

        device_battery = result[2]

        for data in rows_data:
            if len(str(data['level_pct'])) >= 1:
                last_value_pct_inserted_in_db = int(data['level_pct'])
                break

        self.logger_obj.info(f"Current battery pct from the device using adb command = {device_battery}")
        self.logger_obj.info(f"Latest battery pct in upm_device_stats.db = {last_value_pct_inserted_in_db}")

        self.logger_obj.info("Checking if latest battery pct from the logs and the db is same")
        if not (device_battery - 2 <= last_value_pct_inserted_in_db <= device_battery +2):
           status = False
           msg = "Incorrect value of the 'level_pct' in the device stats db file"

        assert status, msg

        query = "Select * from tbl_battery_status"
        rows_data = self.db_handler.execute_query(query)

        self.logger_obj.info("Validating all the columns data of tbl_battery_status from device_stats db")

        for data in rows_data:
            columns = []
            columns.extend(['timestamp', 'refid', 'level_pct'])

            for i in columns:
                if len(str(data[i])) == 0:
                    msg = f"No value found at column = {i} in row timestamp = {data['timestamp']}"
                    assert False, msg
                else:
                    if re.search('[a-zA-Z ]', str(data[i])) is None:
                        if i == 'level_pct':
                            if int(data['level_pct']) >= 101 or int(data['level_pct']) <= -2:
                                msg = f"For timestamp = {data['timestamp']}, The 'level_pct' value is = {data['level_pct']} which is Invalid"
                                assert False, msg
                            else:
                                self.logger_obj.info(
                                    f"Validated timestamp = {data['timestamp']}, the 'level_pct' value is = {data['level_pct']}, which is correct")
                        else:
                            if float(data[i]) <= 0:
                                msg = f"Value at colum = {i} in row timestamp = {data['timestamp']} is {data[i]} which is invalid"
                                assert False, msg

        assert status, msg

    @allure.title("---- DeviceStats: Verifying device_stats data insertion frequency -----")
    @add_markers("regression")
    @pytest.mark.xray(["QA-265189"])
    @pytest.mark.parametrize("table_name", device_stats_tables)
    def test_device_stats_data_insertion_frequency(self, table_name):
        """
             This function Verify data is written to upm_device_stats.db in every 20s for regular session

            It performs the following steps:
            1. Doing validation on all the columns
            Args:
                self (object): Instance of the test class.
                Name of the table which needs to validate
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(f"DeviceStatsDb: Verifying device_stats data insertion frequency for {table_name} table")
        status = True
        msg = ""
        query = f"select distinct timestamp from {table_name} order by timestamp limit 30"
        output = self.db_handler.execute_query(query)

        if len(output) <= 1:
            status = False
            msg = f"Failed to get data for table '{table_name}' "

        assert status, msg

        for ind in range(0, len(output) - 1):
            # here curr_timestamp < prev_timestamp
            # timestamp is in unix ms
            curr_timestamp = output[ind][0]
            prev_timestamp = output[ind + 1][0]

            difference = int(round(abs(curr_timestamp - prev_timestamp) / 1000, 0))

            if not (57 <= difference <= 63):
                status = False
                msg = (f"Failed to collect data for {table_name} in 1min at currentime "
                       f":{datetime.fromtimestamp(int(curr_timestamp / 1000))} and prevtime "
                       f":{datetime.fromtimestamp(int(prev_timestamp / 1000))} diff "
                       f":{curr_timestamp - prev_timestamp} {int(round((curr_timestamp - prev_timestamp) / 1000, 0))} ")
            else:
                self.logger_obj.info(f"Validating time difference between current_row = {curr_timestamp} and "
                                     f"prev_row ={prev_timestamp}, Difference is {difference}s which is correct")

        assert status, msg
        self.logger_obj.info(f"Validation completed for table = {table_name}")

    @allure.title("Validating device stats table [tbl_wifi_info] data from DeviceStats DB")
    @pytest.mark.xray(["QA-149257", "QA-149243"])
    @add_markers("regression")
    def test_tbl_wifi_info_devicestats_db(self):
        """
        Test to verify the integrity of WiFi statistics in the 'wifi_info' table of the 'upm_device_stats' database.

        This test is parameterized with `islt=0` (regular mode) and `islt=1` (LT mode), and performs two key validations:

        1. `verify_wifi(islt)`: Validates the live WiFi statistics collected from the system for the specified mode.
        2. `verify_and_validate_devicestats_data(...)`: Checks the correctness and completeness of expected columns
        in the 'wifi_info' table, including: 'timestamp', 'refid', 'ifname', 'ssid', 'signal_quality', 'rssi_dbm',
        'snr', 'tx_pkts', 'rx_pkts', 'rxmit_pkts', 'rxmit_pct', and 'mcs'.

        The test logs detailed information and fails if any verification step encounters mismatches or missing data.
        """

        errors = []
        columns = ['timestamp', 'refid', 'ifname', 'ssid', 'signal_quality', 'rssi_dbm', 'snr', 'tx_pkts', 'rx_pkts',
                   'rxmit_pkts', 'rxmit_pct', 'mcs']

        result = self.verify_and_validate_devicestats_data(table_name='wifi_info',columns=columns,islt=0)

        if not result[0]:
            errors.append(f"verify_data_from_db failed: {result[1]}")
            self.logger_obj.error(f"verify_and_validate_devicestats_data verification failed. Details: {result[1]}")
        else:
            self.logger_obj.info(f"verify_and_validate_devicestats_data verification passed. {result[1]}")

        if errors:
            assert not errors, "\n".join(errors)
        else:
            self.logger_obj.info(
                "Test Passed: Both verify_wifi and verify_and_validate_devicestats_data verifications succeeded with no issues.")

    @allure.title("Verify data from device info should correctly inserted to the tbl_wifi_info table device_stats db")
    @pytest.mark.xray(["QA-149242","QA-149320"])
    @add_markers("regression")
    def test_validate_tbl_wifi_info_from_upm_logs_to_db_data(self):
        """
            This function is validating data of ZDX:setWifiDetails  to the device stats db data

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Verify data from device info should correctly inserted to the"
                             " tbl_wifi_info table device_stats db")
        status = True
        msg = ""

        self.logger_obj.info("Fetching ZCC_Android logs data for validation")
        upm_logs_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                              plugin='upm')
        if upm_logs_data is None:
            msg = "Not able to fetch data from 'ZCC_Android' log file"
            assert False, msg

        sp = "ZDX:setWifiDetails"
        ep = "ZDX:setMemoryDetails"

        self.logger_obj.info("Fetching setWifiDetails from ZCC_Android logs")
        set_wifi_details = self.log_handler.fetch_device_stats_info(data=upm_logs_data, start_pattern=sp,
                                                                    end_pattern=ep)
        assert set_wifi_details[0], set_wifi_details[1]
        length = len(set_wifi_details[2])
        set_wifi_details = set_wifi_details[2]

        self.logger_obj.info(f"--------------setWifiDetails data from logs--------------")
        for key, value in set_wifi_details.items():
            self.logger_obj.info(f"Value with timestamp = {key} : {value}")

        self.logger_obj.info(f"---------------------------------------------------------")

        previous_timestamp = self.create_timestamp_dict(set_wifi_details)

        query = "select * from tbl_wifi_info"
        row_data = self.db_handler.execute_query(query)
        # if self.device_type.lower() == "chromeos":
        #     set_wifi_details = set_wifi_details[1:]
        # length = min(length, len(row_data))
        length = len(row_data)

        for index in range(1, length):
            # for [timestamp,refid,ifname,ssid,signal_quality,rssi_dbm]
            timestamp = row_data[index]['timestamp']
            timestamp = int(timestamp/1000)
            timestamp = previous_timestamp[timestamp]
            db_if_name = row_data[index]['ifname']
            db_signal_quality = row_data[index]['signal_quality']
            db_rssi_dbm = row_data[index]['rssi_dbm']
            signal_quality_from_rssi = 0

            if db_rssi_dbm == 0:
                signal_quality_from_rssi = 0
            elif db_rssi_dbm <= -100:
                signal_quality_from_rssi = 0
            elif db_rssi_dbm >= -50:
                signal_quality_from_rssi = 100
            elif -50 > db_rssi_dbm > -100:
                signal_quality_from_rssi = 2 * (db_rssi_dbm + 100)

            ifname_from_log = set_wifi_details[timestamp]['ifDescription']
            rssi_dbm_from_log = set_wifi_details[timestamp]['rssi']

            self.logger_obj.info(
                f"Validating 'ifname', 'rssi_dbm', 'signal_quality' values from ZCC_Android "
                f"logs and table tbl_wifi_info of device_stats db for timestamp = {timestamp}")

            if db_if_name != ifname_from_log:
                status = False
                msg = (f"[DB:LOG]:[{db_if_name}:{ifname_from_log}] ifname not matching "
                       f"for INTERFACE: {db_if_name} at timestamp: {timestamp}")
                break

            if int(db_rssi_dbm) != int(rssi_dbm_from_log):
                status = False
                msg = (f"[DB:LOG]:[{rssi_dbm_from_log}:{db_rssi_dbm}] RSSI_dbm not matching "
                       f"for INTERFACE: {db_if_name} in timestamp: {timestamp}")
                break

            if int(db_signal_quality) != int(signal_quality_from_rssi):
                status = False
                msg = (f"[DB:DB]:[{db_signal_quality}:{signal_quality_from_rssi}] Signal quality is not matching for "
                       f"INTERFACE: {db_if_name} at timestamp: {timestamp} with formula => SignalQuality = 2 * (RSSI+100)")
                break

        assert status, msg

    @allure.title("Validating device stats table [tbl_cpu_usage] data from DeviceStats DB")
    @pytest.mark.xray(["QA-149253", "QA-149236", "QA-149233","QA-149320"])
    @add_markers("regression")
    def test_cpu_usage_devicestats_db(self):
        """
        Test to validate the CPU usage statistics stored in the 'cpu_usage' table of the 'upm_device_stats' database.

        This test runs for both regular (`islt=0`)
        It verifies the presence and correctness of the following columns in the 'cpu_usage' table:
        - 'timestamp'
        - 'refid'
        - 'pct_total'
        - 'pct_kernel'
        - 'pct_user'
        - 'pct_idle'

        The test fails and logs an error if any validation discrepancies are found in the database records.
        """
        columns = ['timestamp', 'refid', 'pct_total', 'pct_kernel', 'pct_user', 'pct_idle']
        result = self.verify_and_validate_devicestats_data(table_name='cpu_usage',columns=columns,islt=0 )

        if not result[0]:
            assert False, f"verify_and_validate_devicestats_data failed for CPU usage: {result[1]}"
        else:
            self.logger_obj.info(f"CPU usage verification passed. {result[1]}")

    @allure.title("Validating device stats table [tbl_disk_io] data from DeviceStats DB")
    @pytest.mark.xray(["QA-149255","QA-149320"])
    def test_disk_io_devicestats_db(self):
        """
        Test to validate disk I/O statistics in the 'disk_io' table of the 'upm_device_stats' database.

        - 'timestamp'
        - 'refid'
        - 'disk_name'
        - 'rd_bps' (read bytes per second)
        - 'wt_bps' (write bytes per second)
        - 'free_mb' (available space in MB)
        - 'pct_used' (percentage of disk used)
        - 'avg_diskq_len_scale_1000' (scaled average disk queue length)

        The test logs a failure with details if any validation check does not pass.
        """
        columns = ['timestamp', 'refid', 'disk_name', 'rd_bps', 'wt_bps', 'free_mb', 'pct_used',
                   'avg_diskq_len_scale_1000']
        res = self.verify_and_validate_devicestats_data(table_name='disk_io',columns=columns,islt=0)

        if not res[0]:
            assert False, f"verify_and_validate_devicestats_data failed for disk_io: {res[1]}"
        else:
            self.logger_obj.info(f"disk_io verification passed. {res[1]}")


    @allure.title("Verify data from device info should correctly inserted to the tbl_disk_io table device_stats db")
    @pytest.mark.xray(["QA-149238", "QA-149235"])
    @add_markers("regression")
    def test_validate_tbl_disk_io_data_from_upm_logs_to_db_data(self):
        """
            This function is validating data of ZDX:setDiskDetails to the device stats db data

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Verify data from device info should correctly inserted to the"
                             " tbl_wifi_info table device_stats db")
        status = True
        msg = ""

        self.logger_obj.info("Fetching ZCC_Android logs data for validation")
        upm_logs_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                          plugin='upm')
        if upm_logs_data is None:
            msg = "Not able to fetch data from 'ZCC_Android' log file"
            assert False, msg

        sp = "ZDX:setDiskDetails"
        ep = "ZDX:setWifiDetails"

        self.logger_obj.info("Fetching setDiskDetails from ZCC_Android logs")
        set_disk_details = self.log_handler.fetch_device_stats_info(data=upm_logs_data, start_pattern=sp,
                                                                    end_pattern=ep)
        assert set_disk_details[0], set_disk_details[1]
        length = len(set_disk_details[2])
        set_disk_details = set_disk_details[2]

        self.logger_obj.info(f"--------------setDiskDetails data from logs--------------")
        for key, value in set_disk_details.items():
            self.logger_obj.info(f"Value with timestamp = {key} : {value}")

        self.logger_obj.info(f"---------------------------------------------------------")

        query = "select * from tbl_disk_io"
        row_data = self.db_handler.execute_query(query)
        length =  len(row_data)
        # if self.device_type.lower() == "chromeos":
        #     set_disk_details = set_disk_details[1:]

        for index in range(1, length):
            # for [timestamp,refid,disk_name,rd_bps,wt_bps,avd_diskq_len_scale_1000, free_mb, pct_used]
            timestamp = row_data[index]['timestamp']
            timestamp = int(timestamp / 1000)
            disk_name = row_data[index]['disk_name']
            free_mb_from_db = round(float(row_data[index]['free_mb']), 2)
            pct_used_from_db = round(float(row_data[index]['pct_used']), 2)

            disk_name_from_log = set_disk_details[timestamp]['name']
            pct_used_from_log = round(set_disk_details[timestamp]['usedPercent'], 2)
            free_mb_from_log = round(set_disk_details[timestamp]['freeMB'], 2)

            self.logger_obj.info(
                f"Validating 'free_mb_db':'free_mb_log' = {free_mb_from_db} : {free_mb_from_log} \n'pct_used_db':'pct_used_log' = {pct_used_from_db} : {pct_used_from_log}"
                f"\n'disk_name_db':'disk_name_log' = {disk_name} : {disk_name_from_log}\nvalues from Upm "
                f"logs and table tbl_disk_io of device_stats db for timestamp = {timestamp}")

            if float(free_mb_from_db) != float(free_mb_from_log):
                status = False
                msg = (f"[{free_mb_from_db}:{free_mb_from_log}]free_mb from DB and log are not "
                       f"matching for Disk Name:{disk_name} at timestamp:{timestamp}")
                assert False, msg

            if float(pct_used_from_db) != float(pct_used_from_log):
                status = False
                msg = (f"[{pct_used_from_db}:{pct_used_from_log}]pct_used from DB and log are not "
                       f"matching for Disk Name:{disk_name} at timestamp:{timestamp}")
                assert False, msg

            if disk_name != disk_name_from_log:
                status = False
                msg = (f"[{disk_name}:{disk_name_from_log}] diskName from DB and log are not "
                       f"matching  at timestamp:{timestamp}")
                assert False, msg

        assert status, msg

    @allure.title("Validating device stats table [tbl_mem_usage] data from DeviceStats DB")
    @pytest.mark.xray(["QA-149254", "QA-149237"])
    def test_memory_usage_devicestats_db(self):
        """
        Test to validate memory usage statistics in the 'memory_usage' table of the 'upm_device_stats' database.

        It checks for the presence and correctness of the following columns:
        - 'timestamp'
        - 'refid'
        - 'total' (total memory)
        - 'free' (free memory)
        - 'used' (used memory)
        - 'pct_used' (percentage of memory used)
        - 'swap_used' (swap memory used)

        The test fails with a detailed log message if any of the validations do not succeed.
        """
        columns = ['timestamp', 'refid', 'total', 'free', 'used', 'pct_used', 'swap_used']
        res = self.verify_and_validate_devicestats_data(table_name='memory_usage',columns = columns,islt=0)

        if not res[0]:
            assert False, f"verify_and_validate_devicestats_data failed for memory_usage: {res[1]}"
        else:
            self.logger_obj.info(f"memory_usage verification passed. {res[1]}")

    @allure.title("Verify data from device info should correctly inserted to the tbl_mem_usage table device_stats db")
    @pytest.mark.xray(["QA-149234","QA-149320"])
    @add_markers("regression")
    def test_validate_tbl_mem_usage_data_from_upm_logs_to_db_data(self):
        """
            This function is validating data of ZDX:setMemoryDetails to the device stats db data

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info(
            "Verify data from device info should correctly inserted to the tbl_mem_usage table device_stats db")

        status = True
        msg = ""

        self.logger_obj.info("Fetching ZCC_Android logs data for validation")
        upm_logs_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                          plugin='upm')
        if upm_logs_data is None:
            msg = "Not able to fetch data from 'ZCC_Android' log file"
            assert False, msg

        sp = "ZDX:setMemoryDetails"
        ep = "ZDX:setProcessDetails"

        self.logger_obj.info("Fetching setMemoryDetails from ZCC_Android logs")
        set_memory_details = self.log_handler.fetch_device_stats_info(data=upm_logs_data, start_pattern=sp,
                                                                      end_pattern=ep)
        assert set_memory_details[0], set_memory_details[1]
        length = len(set_memory_details[2])
        set_memory_details = set_memory_details[2]

        self.logger_obj.info(f"--------------setMemoryDetails data from logs--------------")
        for key, value in set_memory_details.items():
            self.logger_obj.info(f"Value with timestamp = {key} : {value}")
        self.logger_obj.info(f"---------------------------------------------------------")

        query = "select * from tbl_memory_usage"
        row_data = self.db_handler.execute_query(query)
        # if self.device_type.lower() == "chromeos":
        #     set_memory_details = set_memory_details[1:]
        # length = min(length, len(row_data))

        for index in range(1 , length):
            # for [timestamp,refid,total,free,used,pct_used, swap_used]
            timestamp = row_data[index]['timestamp']
            timestamp = int(timestamp / 1000)
            total_from_db = row_data[index]['total']
            free_from_db = row_data[index]['free']
            used_from_db = row_data[index]['used']
            pct_used_from_db = row_data[index]['pct_used']

            total_from_log = set_memory_details[timestamp]['totalMem']
            free_from_log = set_memory_details[timestamp]['availMem']
            used_from_log = set_memory_details[timestamp]['usedMem']
            pct_used_from_log = set_memory_details[timestamp]['memLoad']

            self.logger_obj.info(
                f"Validating 'total' = {total_from_db}, 'free' = {free_from_db}, 'used' = {used_from_db}, 'pct_used' = {pct_used_from_db} values from Upm"
                f"logs and table tbl_memory_usage of device_stats db for timestamp = {timestamp}")

            if int(total_from_db) != int(total_from_log):
                status = False
                msg = (f"[{total_from_db}:{total_from_log}]total from DB and log are not "
                       f"matching for [Memory]: at timestamp:{timestamp}")
                break

            if int(free_from_db) != int(free_from_log):
                status = False
                msg = (f"[{free_from_db}:{free_from_log}]free from DB and log are not "
                       f"matching for [Memory]: at timestamp:{timestamp}")
                break

            if int(used_from_db) != int(used_from_log):
                status = False
                msg = (f"[{used_from_db}:{used_from_log}]used from DB and log are not "
                       f"matching for [Memory]: at timestamp:{timestamp}")
                break

            if int(pct_used_from_db) != int(pct_used_from_log):
                status = False
                msg = (f"[{pct_used_from_db}:{pct_used_from_log}]pct_used from DB and log are not "
                       f"matching for[Memory]: at timestamp:{timestamp}")
                break

        assert status, msg

    @allure.title("Validating device stats table [tbl_mem_usage] data from DeviceStats DB")
    @pytest.mark.xray(["QA-58368","QA-149320"])
    def test_mon_processes_devicestats_db(self):
        """
        Test to validate monitored process statistics in the 'mon_processes' table of the 'upm_device_stats' database.

        Checks for correctness and completeness of the following columns:
        - 'timestamp'
        - 'pid'
        - 'name'
        - 'cpu_user'
        - 'cpu_kernel'
        - 'cpu_total'
        - 'cpu_total_pct'
        - 'mem_wss_bytes'
        - 'mem_wss_pct'

        If the validation fails, the test raises a detailed error via pytest and logs the failure context.
        """

        columns = ['timestamp', 'pid', 'name', 'cpu_user', 'cpu_kernel', 'cpu_total', 'cpu_total_pct',
                   'mem_wss_bytes', 'mem_wss_pct']
        res = self.verify_and_validate_devicestats_data(table_name='mon_processes',columns=columns, islt=0)

        if not res[0]:
            assert False, f"verify_and_validate_devicestats_data failed for mon_processes: {res[1]}"
        else:
            self.logger_obj.info(f"mon_processes verification passed. {res[1]}")

    @allure.title("Validating device stats table [tbl_network_interface] data from DeviceStats DB")
    @pytest.mark.xray(["QA-149256","QA-149320"])
    def test_network_interface_devicestats_db(self):
        """
        Test to verify the integrity of network interface statistics in the 'network_interface' table
        within the 'upm_device_stats' database.

        This test is esuring comprehensive validation across different data collection contexts.

        It validates the presence and correctness of the following columns:
        - 'timestamp'
        - 'refid'
        - 'ifname'
        - 'guid'
        - 'recv_bps'
        - 'send_bps'
        - 'is_active'
        - 'bandwidth_mbps'

        The test will fail and log an error if the validation fails, ensuring visibility into any data quality issues.
        """

        columns = ['timestamp', 'refid', 'ifname', 'guid', 'recv_bps', 'send_bps', 'is_active', 'bandwidth_mbps']
        res = self.verify_and_validate_devicestats_data(table_name='network_interface',columns=columns, islt=0)


        if not res[0]:
            assert False, f"verify_and_validate_devicestats_data failed for network_interface: {res[1]}"
        else:
            self.logger_obj.info(f"Network_interface verification passed. {res[1]}")

    @allure.title("Verify raw data collection and insertion into tbl_network_interface")
    @pytest.mark.xray("QA-149241")
    @add_markers("regression")
    def test_verify_monitor_data_for_network_interface(self):
        """
            This function is validating data for 'tbl_network_interface'

            It performs the following steps:
            1. It will check if data is present or not and also validates data from the logs
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Verify & Comparing data for table network interface for regular session")
        status = True
        msg = ""

        device_stats_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                  plugin='deviceStats')
        if len(device_stats_log_data) == 0:
            msg = "Not able to fetch data from devicestats log file"
            assert False, msg

        query = "select * from tbl_network_interface order by timestamp desc limit 30"
        row_data = self.db_handler.execute_query(query)
        if len(row_data) == 0:
            assert False, "No data found in device stats db"

        for row in row_data:
            # for table [timestamp,refid,ifname,guid,bandwidth_mbps,revc_bps,send_bps,isWifi,isactive]
            timestamp_from_db = row['timestamp']
            guid_from_db = row['guid']
            bandwidth_from_db = row['bandwidth_mbps']
            recv_from_db = row['recv_bps']
            send_from_db = row['send_bps']
            is_wifi_from_db = row['isWifi']
            is_active_from_db = row['is_active']
            pattern = re.compile(
                fr'(?im)LT sessionId:-1.*?timestamp:\s+{timestamp_from_db}.*?GUID:\[{guid_from_db}].*?isConnected:([0-9]).*?Bandwidth:\[(.*?)\s+.*?\].*?Receive:\[(.*?)\s+.*?\].*?Send:\[(.*?)\s+.*?\]',
                re.DOTALL)
            res = pattern.findall(device_stats_log_data)
            if len(res) > 0:
                if len(res[0]) != 4:
                    status = False
                    msg = (f"Unable to fetch [Network Interface] field for interface with GUID:{guid_from_db} "
                           f"at timestamp :{timestamp_from_db}")
                    assert status, msg
                else:
                    self.logger_obj.info(
                        f"Validating 'is_connected', 'bandwidth', 'recv_bps' and 'send_bps' values from "
                        f"device_stats logs and table tbl_lt_network_interface db for timestamp = {timestamp_from_db}")

                    is_connected_from_log, bandwidth_from_log, recv_from_log, send_from_log = res[0]

                    if int(is_connected_from_log) != int(is_active_from_db):
                        status = False
                        msg = (
                            f"[{is_active_from_db}:{is_connected_from_log}]IsActive(isConnected) from DB and log not "
                            f"matching for Interface with GUID:{guid_from_db} at timestamp:{timestamp_from_db}")
                        assert status, msg

                    if float(bandwidth_from_db) != float(bandwidth_from_log):
                        status = False
                        msg = (f"[{bandwidth_from_db}:{bandwidth_from_log}]Bandwidth from DB and log not "
                               f"matching for Interface with GUID:{guid_from_db} at timestamp:{timestamp_from_db}")
                        assert status, msg

                    if float(send_from_db) != float(send_from_log):
                        status = False
                        msg = (f"[{send_from_db}:{send_from_log}]Send Field from DB and log not matching for "
                               f"Interface with GUID:{guid_from_db} at timestamp:{timestamp_from_db}")
                        assert status, msg

                    if float(recv_from_db) != float(recv_from_log):
                        status = False
                        msg = (f"[{recv_from_db}:{recv_from_log}]Recv Field from DB and log not matching for "
                               f"Interface with GUID:{guid_from_db} at timestamp:{timestamp_from_db}")
                        assert status, msg
            else:
                status = False
                msg = "Unable to fetch tbl_network_interface data from logs "
                break

        assert status, msg

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Deletion of forward profile and app profile
        2. Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Closing db connection after verification")
        self.db_handler.close_db_connection()


@pytest.mark.mr_36057
@pytest.mark.zdx_4_0
class Test_Device_Stats_Min_Max_Avg:

    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.const_obj = conftest.const_obj
        self.db_handler = conftest.db_handler
        self.logger_obj = conftest.logger_obj
        self.log_handler = conftest.log_handler
        self.device_stats_db_path = self.const_obj.LOG_PATH + f"\\upm_device_stats.db"
        self.upload_stats_db_path = self.const_obj.LOG_PATH + f"\\upm_upload_stats.db"

        # Take start time - 3sec because it takes time to reflect to db, test_start_time(unix timestamp in ms.)
        self.test_start_time = int(time.time() * 1000 - 3 * 1000)

        data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin="upm")
        if data is None:
            pytest.skip("Not able to fetch data from upm log file")
        pattern = re.compile(r'(?is)stime=([\d]*) etime=([\d]*) currentTime=([\d]*).*?Probes are ready')
        res = pattern.findall(data)

        unique_tuples = {}
        for first, second, third in res:
            if first not in unique_tuples:
                unique_tuples[first] = second

        # Convert the dictionary back to a list of tuples
        self.res = list(unique_tuples.items())


    def calculate_percentiles(self, data):
        ret = []
        for percentile in [50, 75, 90, 95, 99]:
            ret.append(np.percentile(data, percentile))
        # Calculate the values at the specified percentiles
        for i in range(len(ret)):
            ret[i] = round(ret[i], 2)
        return ret

    @allure.title(
        "Verify correctness of min/max/avg of all the column value of tbl_memory_usage table of upm_device_stats in upload json of upm_upload_stats.db for regular session")
    @pytest.mark.parametrize("check_field", [('used'), ('free'), ('total'), ('pct_used'), ('swap_used')])
    def test_verify_mem_usage_data(self, check_field):
        """
        This function Verify correctness of min/max/avg of all the column value of tbl_memory_usage table of upm_device_stats in upload json of upm_upload_stats.db for regular session
        """
        self.logger_obj.info(
            "\n---------------------------- DeviceStats:Verify and compare the device stats mem_usage data from device stats and upload stats db ----------------------------\n")

        Issues = []
        current_row = 0
        for i, it in enumerate(self.res):
            startTime, endTime = int(it[0]), int(it[1])

            self.db_handler.create_db_connection(self.device_stats_db_path)
            query_for_devicestats = rf'select min({check_field}), max({check_field}), avg({check_field}) from tbl_memory_usage where timestamp between {startTime} AND {endTime}'
            res_device_stats = self.db_handler.execute_query(query_for_devicestats)
            self.db_handler.close_db_connection()

            self.db_handler.create_db_connection(self.upload_stats_db_path)
            query_for_uploadstats = rf"select * from upload_data where timestamp={startTime} order by timestamp desc"
            res_upload_stats = self.db_handler.execute_query(query_for_uploadstats)
            self.db_handler.close_db_connection()

            if not res_device_stats:
                self.logger_obj.error(f"Query Failed to fetch fields for stime: {startTime}")
                continue

            d_min_field, d_max_field, d_avg_field = res_device_stats[0]
            if d_min_field is None or d_max_field is None or d_avg_field is None:
                continue

            d_min_field = round(d_min_field, 2)
            d_max_field = round(d_max_field, 2)
            d_avg_field = round(d_avg_field, 2)
            self.logger_obj.info(f"Verifying data for time range #{current_row + 1}: stime={startTime}, etime={endTime} for field '{check_field}'")
            self.logger_obj.info(f"From device_stats.db for '{check_field}': min={d_min_field}, max={d_max_field}, avg={d_avg_field}")
            current_row += 1

            for row in res_upload_stats:
                timestamp = row[0]
                uploadJsonData = json.loads(row[2])
                for probe in uploadJsonData["apps"]["probes"]:

                    if probe["app"]["type"] != 'mem' or probe.get("tags") is not None:
                        continue

                    stime = probe["stime"]
                    etime = probe["etime"]
                    if etime - stime > 300 or probe["stats"].get(f"{check_field}") is None:
                        continue
                    stats_data_from_upload = probe["stats"][f"{check_field}"]
                    u_avg_field = stats_data_from_upload["avg"]
                    u_min_field = stats_data_from_upload["min"]
                    u_max_field = stats_data_from_upload["max"]
                    self.logger_obj.info(f"From upload_stats.db for '{check_field}': min={u_min_field}, max={u_max_field}, avg={u_avg_field}")
                    self.logger_obj.info(f"Comparing values for '{check_field}'...")
                    if u_avg_field != d_avg_field:
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_avg_field}::{d_avg_field}] Avg Mem {check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_avg_field}::{d_avg_field}] Avg Mem {check_field} not matching at timestamp :{timestamp}")
                    if u_max_field != d_max_field:
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_max_field}::{d_max_field}] Max Mem {check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_max_field}::{d_max_field}] Max Mem {check_field} not matching at timestamp :{timestamp}")
                    if u_min_field != d_min_field:
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_min_field}::{d_min_field}]Max Mem {check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_min_field}::{d_min_field}]Max Mem {check_field} not matching at timestamp :{timestamp}")

                    if len(Issues) >= 1:
                        pytest.fail(str(Issues))

    @allure.title(
        "Verify correctness of min/max/avg value of all the columns tbl_disk_io table of upm_device_stats in upload json of upm_upload_stats.db for regular session")
    @pytest.mark.parametrize("d_check_field,u_check_field",
                             [('wt_bps', 'wt_bps'), ('free_mb', 'free_mb'), ('pct_used', 'pct_used'),
                              ('rd_bps', 'rd_bps'), ('avg_diskq_len_scale_1000', 'avg_diskq_len')])
    def test_verify_disk_io_data(self, d_check_field, u_check_field):
        """
        This function Verify correctness of min/max/avg value of all the columns tbl_disk_io table of upm_device_stats in upload json of upm_upload_stats.db for regular session
        Args:
            d_check_field is the name of the field in Devicestats DB to check for min,max,avg in uploadStats DB,
            u_check_field is the name of the field in UploadStats DB to check for min,max,avg with devicestats data
        """
        self.logger_obj.info(
            "\n---------------------------- DeviceStats:Verify and compare the device stats disk io data from device stats and upload stats db ----------------------------\n")

        Issues = []
        current_row = 0

        for i, it in enumerate(self.res):
            startTime, endTime = int(it[0]), int(it[1])

            self.db_handler.create_db_connection(self.device_stats_db_path)
            query_for_devicestats = rf'select min({d_check_field}), max({d_check_field}), avg({d_check_field}) from tbl_disk_io where timestamp between {startTime} AND {endTime}'
            res_device_stats = self.db_handler.execute_query(query_for_devicestats)
            self.db_handler.close_db_connection()

            self.db_handler.create_db_connection(self.upload_stats_db_path)
            query_for_uploadstats = rf"select * from upload_data where timestamp={startTime} order by timestamp desc"
            res_upload_stats = self.db_handler.execute_query(query_for_uploadstats)
            self.db_handler.close_db_connection()

            if not res_device_stats:
                self.logger_obj.error(f"Query Failed to fetch fields for stime: {startTime}")
                continue

            d_min_field, d_max_field, d_avg_field = res_device_stats[0]
            if d_min_field is None or d_max_field is None or d_avg_field is None:
                continue

            d_min_field = round(d_min_field, 2) if u_check_field != 'avg_diskq_len' else round(d_min_field / 1000, 3)
            d_max_field = round(d_max_field, 2) if u_check_field != 'avg_diskq_len' else round(d_max_field / 1000, 3)
            d_avg_field = round(d_avg_field, 2) if u_check_field != 'avg_diskq_len' else round(d_avg_field / 1000, 3)
            self.logger_obj.info(f"Verifying data for time range #{current_row + 1}: stime={startTime}, etime={endTime} for field '{d_check_field}'")
            self.logger_obj.info(f"From device_stats.db for '{d_check_field}': min={d_min_field}, max={d_max_field}, avg={d_avg_field}")
            current_row+=1

            for row in res_upload_stats:
                timestamp = row[0]
                uploadJsonData = json.loads(row[2])
                for probe in uploadJsonData["apps"]["probes"]:

                    if probe["app"]["type"] != 'disk':
                        continue
                    stime = probe["stime"]
                    etime = probe["etime"]
                    if etime - stime > 300 or probe["stats"].get(f"{u_check_field}") is None:
                        continue
                    stats_data_from_upload = probe["stats"][f"{u_check_field}"]
                    u_avg_field = stats_data_from_upload["avg"]
                    u_min_field = stats_data_from_upload["min"]
                    u_max_field = stats_data_from_upload["max"]
                    self.logger_obj.info(f"From upload_stats.db for '{u_check_field}': min={u_min_field}, max={u_max_field}, avg={u_avg_field}")
                    self.logger_obj.info(f"Comparing values for '{d_check_field}' and '{u_check_field}'...")

                    if round(abs(u_avg_field - d_avg_field), 3) > (0.001 if d_check_field != 'free_mb' else 1):
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_avg_field}::{d_avg_field}] Diff :{abs(u_avg_field - d_avg_field)}Avg disk io field : {u_check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_avg_field}::{d_avg_field}]Diff :{abs(u_avg_field - d_avg_field)} Avg disk io field : {u_check_field} not matching at timestamp :{timestamp}")
                    if round(abs(u_max_field - d_max_field), 3) > (0.001 if d_check_field != 'free_mb' else 1):
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_max_field}::{d_max_field}] Max disk io field : {u_check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_max_field}::{d_max_field}] Max disk io field : {u_check_field} not matching at timestamp :{timestamp}")
                    if round(abs(u_min_field - d_min_field), 3) > (0.001 if d_check_field != 'free_mb' else 1):
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_min_field}::{d_min_field}]Max disk io field : {u_check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_min_field}::{d_min_field}]Max disk io field : {u_check_field} not matching at timestamp :{timestamp}")

                    if len(Issues) >= 1:
                        pytest.fail(str(Issues))

    @allure.title(
        "Verify correctness of min/max/avg of all the columns value of tbl_battery_status table of upm_device_stats in upload json of upm_upload_stats.db for regular session")
    @pytest.mark.parametrize("check_field", [('level_pct')])
    def test_verify_battery_status_data(self, check_field):
        """
        This function Verify correctness of min/max/avg of all the columns value of tbl_battery_status table of upm_device_stats in upload json of upm_upload_stats.db for regular session
        """
        self.logger_obj.info(
            "\n------------------------DeviceStats: Verify correctness of the device stats battery status data from device stats and upload stats db for regular session----------------------------\n")

        Issues = []
        current_row = 0

        for i, it in enumerate(self.res):
            startTime, endTime = int(it[0]), int(it[1])

            self.db_handler.create_db_connection(self.device_stats_db_path)
            query_for_devicestats = rf'select min({check_field}), max({check_field}), avg({check_field}) from tbl_battery_status where timestamp between {startTime} AND {endTime}'
            res_device_stats = self.db_handler.execute_query(query_for_devicestats)
            self.db_handler.close_db_connection()

            self.db_handler.create_db_connection(self.upload_stats_db_path)
            query_for_uploadstats = rf"select * from upload_data where timestamp={startTime} order by timestamp desc"
            res_upload_stats = self.db_handler.execute_query(query_for_uploadstats)
            self.db_handler.close_db_connection()

            if not res_device_stats:
                self.logger_obj.error(f"Query Failed to fetch fields for stime: {startTime}")
                continue

            d_min_field, d_max_field, d_avg_field = res_device_stats[0]
            if d_min_field is None or d_max_field is None or d_avg_field is None:
                continue

            d_min_field = round(d_min_field, 2)
            d_max_field = round(d_max_field, 2)
            d_avg_field = round(d_avg_field, 2)
            self.logger_obj.info(f"Verifying data for time range #{current_row + 1}: stime={startTime}, etime={endTime} for field '{check_field}'")
            self.logger_obj.info(f"From device_stats.db for '{check_field}': min={d_min_field}, max={d_max_field}, avg={d_avg_field}")
            current_row += 1

            for row in res_upload_stats:
                timestamp = row[0]
                uploadJsonData = json.loads(row[2])
                for probe in uploadJsonData["apps"]["probes"]:

                    if probe["app"]["type"] != 'battery':
                        continue
                    stime = probe["stime"]
                    etime = probe["etime"]
                    # etime-stime should be within 300s(5min) range
                    if etime - stime > 300 or probe["stats"].get(f"{check_field}") is None:
                        continue
                    stats_data_from_upload = probe["stats"][f"{check_field}"]
                    u_avg_field = stats_data_from_upload["avg"]
                    u_min_field = stats_data_from_upload["min"]
                    u_max_field = stats_data_from_upload["max"]
                    self.logger_obj.info(f"From upload_stats.db for '{check_field}': min={u_min_field}, max={u_max_field}, avg={u_avg_field}")
                    self.logger_obj.info(f"Comparing values for '{check_field}'...")
                    if u_avg_field != d_avg_field:
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_avg_field}::{d_avg_field}] Avg Battery status field: {check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_avg_field}::{d_avg_field}] Avg Battery status field: {check_field} not matching at timestamp :{timestamp}")
                    if u_max_field != d_max_field:
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_max_field}::{d_max_field}] Max Battery status field: {check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_max_field}::{d_max_field}] Max Battery status field: {check_field} not matching at timestamp :{timestamp}")
                    if u_min_field != d_min_field:
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_min_field}::{d_min_field}]Max Battery status field: {check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_min_field}::{d_min_field}]Max Battery status field: {check_field} not matching at timestamp :{timestamp}")

        if len(Issues) >= 1:
            pytest.fail(str(Issues))

    @allure.title(
        "Verify correctness of min/max/avg value of tbl_wifi_info table  of upm_device_stats in upload json of upm_upload_stats.db for regular session")
    @pytest.mark.parametrize("check_field", [('signal_quality'), ('rssi_dbm'), ('snr')])
    def test_verify_correctness_wifi_info_data(self, check_field):
        """
        This function Verify correctness of min/max/avg value of tbl_wifi_info table  of upm_device_stats in upload json of upm_upload_stats.db for regular session
        """
        self.logger_obj.info(
            "\n---------------------------- DeviceStats: Verify correctness of min/max/avg of  the device stats wifi  data from device stats and upload stats db ----------------------------\n")

        Issues = []
        current_row = 0

        self.db_handler.create_db_connection(self.device_stats_db_path)
        query = 'select distinct ifname from tbl_wifi_info'
        tmpres = self.db_handler.execute_query(query)
        ifname = tmpres[0][0] if len(tmpres) > 0 else None
        self.db_handler.close_db_connection()

        for i, it in enumerate(self.res):
            startTime, endTime = int(it[0]), int(it[1])

            self.db_handler.create_db_connection(self.device_stats_db_path)
            query_for_devicestats = rf'select min({check_field}), max({check_field}), avg({check_field}) from tbl_wifi_info where timestamp between {startTime} AND {endTime} and ifname="{ifname}"'
            res_device_stats = self.db_handler.execute_query(query_for_devicestats)
            self.db_handler.close_db_connection()

            self.db_handler.create_db_connection(self.upload_stats_db_path)
            query_for_uploadstats = rf"select * from upload_data where timestamp={startTime} order by timestamp desc"
            res_upload_stats = self.db_handler.execute_query(query_for_uploadstats)
            self.db_handler.close_db_connection()

            if not res_device_stats:
                self.logger_obj.error(
                    f"Query Failed to fetch device stats data between timestamp :{startTime} to {endTime}")
                continue

            d_min_field, d_max_field, d_avg_field = res_device_stats[0]
            if d_min_field is None or d_max_field is None or d_avg_field is None:
                continue

            d_min_field = round(d_min_field, 2)
            d_max_field = round(d_max_field, 2)
            d_avg_field = round(d_avg_field, 2)
            self.logger_obj.info(f"Verifying data for time range #{current_row + 1}: stime={startTime}, etime={endTime} for field '{check_field}'")
            self.logger_obj.info(f"From device_stats.db for '{check_field}': min={d_min_field}, max={d_max_field}, avg={d_avg_field}")
            current_row += 1
            for row in res_upload_stats:
                timestamp = row[0]
                uploadJsonData = json.loads(row[2])
                for probe in uploadJsonData["apps"]["probes"]:

                    if probe["app"]["type"] != 'wifi':
                        continue
                    stime = probe["stime"]
                    etime = probe["etime"]
                    if etime - stime > 300 or probe["stats"].get(f"{check_field}") is None:
                        continue
                    stats_data_from_upload = probe["stats"][f"{check_field}"]
                    u_avg_field = stats_data_from_upload["avg"]
                    u_min_field = stats_data_from_upload["min"]
                    u_max_field = stats_data_from_upload["max"]
                    self.logger_obj.info(f"From upload_stats.db for '{check_field}': min={u_min_field}, max={u_max_field}, avg={u_avg_field}")
                    self.logger_obj.info(f"Comparing values for '{check_field}'...")
                    if u_avg_field != d_avg_field:
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_avg_field}::{d_avg_field}] Avg Wifi Info Data field: {check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_avg_field}::{d_avg_field}] Avg Wifi Info Data field: {check_field} not matching at timestamp :{timestamp}")
                    if u_max_field != d_max_field:
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_max_field}::{d_max_field}] Max  Wifi Info Data field: {check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_max_field}::{d_max_field}] Max Wifi Info Data field: {check_field} not matching at timestamp :{timestamp}")
                    if u_min_field != d_min_field:
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_min_field}::{d_min_field}]Max Wifi Info Data field: {check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_min_field}::{d_min_field}]Max  Wifi Info Data field: {check_field} not matching at timestamp :{timestamp}")

        if len(Issues) >= 1:
            pytest.fail(str(Issues))

    @allure.title(
        "Verify correctness of min/max/avg value of  CPU_usage table of upm_device_stats in upload json of upm_upload_stats.db for regular session")
    @pytest.mark.parametrize("check_field", [('pct_total'), ('pct_kernel'), ('pct_user'), ('pct_idle')])
    def test_verify_correctness_cpu_usages_data(self, check_field):
        """
        This function Verify correctness of min/max/avg value of  CPU_usage table of upm_device_stats in upload json of upm_upload_stats.db for regular session
        """
        self.logger_obj.info(
            "\n---------------------------- DeviceStats: Verify correctness of min/max/avg of  the device stats cpu usages  data from device stats and upload stats db ----------------------------\n")

        Issues = []
        current_row =0

        for i, it in enumerate(self.res):
            startTime, endTime = int(it[0]), int(it[1])

            self.db_handler.create_db_connection(self.device_stats_db_path)
            query_for_devicestats = rf'select min({check_field}), max({check_field}), avg({check_field}) from tbl_cpu_usage where timestamp between {startTime} AND {endTime}'
            res_device_stats = self.db_handler.execute_query(query_for_devicestats)
            self.db_handler.close_db_connection()

            self.db_handler.create_db_connection(self.upload_stats_db_path)
            query_for_uploadstats = rf"select * from upload_data where timestamp={startTime} order by timestamp desc"
            res_upload_stats = self.db_handler.execute_query(query_for_uploadstats)
            self.db_handler.close_db_connection()

            if not res_device_stats:
                self.logger_obj.error(
                    f"Query Failed to fetch device stats data between timestamp :{startTime} to {endTime}")
                continue

            d_min_field, d_max_field, d_avg_field = res_device_stats[0]
            if d_min_field is None or d_max_field is None or d_avg_field is None:
                continue

            d_min_field = round(d_min_field, 2)
            d_max_field = round(d_max_field, 2)
            d_avg_field = round(d_avg_field, 2)
            self.logger_obj.info(f"Verifying data for time range #{current_row + 1}: stime={startTime}, etime={endTime} for field '{check_field}'")
            self.logger_obj.info(f"From device_stats.db for '{check_field}': min={d_min_field}, max={d_max_field}, avg={d_avg_field}")
            current_row += 1

            for row in res_upload_stats:
                timestamp = row[0]
                uploadJsonData = json.loads(row[2])
                for probe in uploadJsonData["apps"]["probes"]:

                    if probe["app"]["type"] != 'cpu' or probe.get("tags") is not None:
                        continue
                    stime = probe["stime"]
                    etime = probe["etime"]
                    if etime - stime > 300 or probe["stats"].get(f"{check_field}") is None:
                        continue
                    stats_data_from_upload = probe["stats"][f"{check_field}"]
                    u_avg_field = stats_data_from_upload["avg"]
                    u_min_field = stats_data_from_upload["min"]
                    u_max_field = stats_data_from_upload["max"]
                    self.logger_obj.info(f"From upload_stats.db for '{check_field}': min={u_min_field}, max={u_max_field}, avg={u_avg_field}")
                    self.logger_obj.info(f"Comparing values for '{check_field}'...")
                    if round(abs(u_avg_field - d_avg_field), 2) > 0.01:
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_avg_field}::{d_avg_field}] Avg CPU Usage Data field: {check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_avg_field}::{d_avg_field}] Avg CPU Usage Data field: {check_field} not matching at timestamp :{timestamp}")
                    if round(abs(u_max_field - d_max_field), 2) > 0.01:
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_max_field}::{d_max_field}] Max  CPU Usage Data field: {check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_max_field}::{d_max_field}] Max CPU Usage Data field: {check_field} not matching at timestamp :{timestamp}")
                    if round(abs(u_min_field - d_min_field), 2) > 0.01:
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_min_field}::{d_min_field}]Max CPU Usage Data field: {check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_min_field}::{d_min_field}]Max  CPU Usage Data field: {check_field} not matching at timestamp :{timestamp}")

        if len(Issues) >= 1:
            pytest.fail(str(Issues))

    @allure.title(
        "Verify correctness of min/max/avg 'mem_wss_pct' and 'cpu_total_pct' value of tbl_mon_processes table of upm_device_stats in upload json of upm_upload_stats.db for regular session")
    @pytest.mark.parametrize("d_check_field, u_check_field, app_type",
                             [("mem_wss_pct", "pct_used", "mem"), ("cpu_total_pct", "pct_used", "cpu")])
    def test_verify_correctness_min_max_avg_field_of_tbl_mon_processes(self, d_check_field, u_check_field, app_type):
        """
        This function Verify correctness of min/max/avg 'mem_wss_pct'  and 'cpu_total_pct' value of tbl_mon_processes table of upm_device_stats in upload json of upm_upload_stats.db for regular session
        """
        self.logger_obj.info(
            f"\n---------DeviceStats:Verify correctness of min/max/avg {d_check_field} value of tbl_mon_processes table of upm_device_stats in upload json of upm_upload_stats.db for regular session----------------------------\n")

        Issues = []
        current_row = 0

        for i, it in enumerate(self.res):
            startTime, endTime = int(it[0]), int(it[1])

            self.db_handler.create_db_connection(self.device_stats_db_path)
            query_for_devicestats = rf'select min({d_check_field}), max({d_check_field}), avg({d_check_field}) from tbl_mon_processes where timestamp between {startTime} AND {endTime}'
            res_device_stats = self.db_handler.execute_query(query_for_devicestats)
            self.db_handler.close_db_connection()

            self.db_handler.create_db_connection(self.upload_stats_db_path)
            query_for_uploadstats = rf"select * from upload_data where timestamp={startTime} order by timestamp desc"
            res_upload_stats = self.db_handler.execute_query(query_for_uploadstats)
            self.db_handler.close_db_connection()

            if not res_device_stats:
                self.logger_obj.error(f"Query Failed to fetch fields for stime: {startTime}")
                continue

            d_min_field, d_max_field, d_avg_field = res_device_stats[0]
            if d_min_field is None or d_max_field is None or d_avg_field is None:
                continue

            d_min_field = round(d_min_field, 2)
            d_max_field = round(d_max_field, 2)
            d_avg_field = round(d_avg_field, 2)
            self.logger_obj.info(f"Verifying data for time range #{current_row + 1}: stime={startTime}, etime={endTime} for field '{d_check_field}'")
            self.logger_obj.info(f"From device_stats.db for '{d_check_field}': min={d_min_field}, max={d_max_field}, avg={d_avg_field}")
            current_row += 1

            for row in res_upload_stats:
                timestamp = row[0]
                uploadJsonData = json.loads(row[2])
                for probe in uploadJsonData["apps"]["probes"]:
                    if probe["app"]["type"] != app_type:
                        continue
                    stime = probe["stime"]
                    etime = probe["etime"]
                    if etime - stime > 300 or probe["stats"].get(f"{u_check_field}") is None:
                        continue
                    stats_data_from_upload = probe["stats"][f"{u_check_field}"]
                    u_avg_field = stats_data_from_upload["avg"]
                    u_min_field = stats_data_from_upload["min"]
                    u_max_field = stats_data_from_upload["max"]
                    self.logger_obj.info(f"From upload_stats.db for '{u_check_field}': min={u_min_field}, max={u_max_field}, avg={u_avg_field}")
                    self.logger_obj.info(f"Comparing values for '{d_check_field}' and '{u_check_field}'...")

                    if round(abs(u_avg_field - d_avg_field), 2) > 0.01:
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_avg_field}::{d_avg_field}] Avg {d_check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_avg_field}::{d_avg_field}] Avg {d_check_field} not matching at timestamp :{timestamp}")
                    if round(abs(u_max_field - d_max_field), 2) > 0.01:
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_max_field}::{d_max_field}] Max {d_check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_max_field}::{d_max_field}] Max {d_check_field} not matching at timestamp :{timestamp}")
                    if round(abs(u_min_field - d_min_field), 2) > 0.01:
                        self.logger_obj.error(
                            f"[UPLOAD_DB::DEVICEDB] [{u_min_field}::{d_min_field}] Min {d_check_field} not matching at timestamp :{timestamp}")
                        Issues.append(
                            f"[UPLOAD_DB::DEVICEDB] [{u_min_field}::{d_min_field}] Min {d_check_field} not matching at timestamp :{timestamp}")

        if len(Issues) >= 1:
            pytest.fail(str(Issues))

    @allure.title("Verify ZSAUpm process CPU and memory usage is reported to ADX every 15 minutes")
    def test_verify_cpu_and_memory_usages_uploaded_to_adx_every_15_min(self):
        """
        This function Verify ZSAUpm process CPU and memory usage is reported to ADX every 15 minutes
        """
        self.logger_obj.info(
            "\n---------------------------- DeviceStats: Verify ZSAUpm process CPU and memory usage is reported to ADX every 15 minutes ----------------------------\n")

        Issues = []
        data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='deviceStats')
        if data is None:
            assert False, "Not able to fetch data from device stats log file"

        pattern = re.compile(r'(?ims)lastUpmMetricsUploadTime:([\d]*)')
        res = pattern.findall(data)

        if len(res) <= 1:
            pytest.skip("No sufficient data available to run this testcase")

        for ind in range(len(res) - 1):
            curr = int(res[ind])
            next = int(res[ind + 1])
            # Difference in ms for 15min= 900000
            diff = next - curr
            if diff != 900000:
                msg = f"Upload interval difference is not 15min. Current timestamp: {curr}, next timestamp: {next}"
                self.logger_obj.error(msg)
                Issues.append(msg)
            else:
                self.logger_obj.info(f"Upload interval is correct (15 min). Current timestamp: {curr}, next timestamp: {next}")

        if len(Issues) >= 1:
            pytest.fail(str(Issues))

    @allure.title("Verify that the device stats monitor data upload is successful for each uploader interval")
    def test_verify_device_stats_monitor_data_upload_for_every_uploader_interval(self):
        """
        This functionVerify that the device stats monitor data upload is successful for each uploader interval
        """
        self.logger_obj.info(
            "\n---------------------------- DeviceStats:Verify that the device stats monitor data upload is successful for each uploader interval ----------------------------\n")

        Issues = []
        data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')
        if data is None:
            assert False, "Not able to fetch data from upm log file"

        pattern = re.compile(r'(?is)stime=([\d]*) etime=([\d]*) currentTime=([\d]*).*?Probes are ready')
        res = pattern.findall(data)

        for it in res:
            stime, etime = int(it[0]), int(it[1])
            # 5min 5*60*1000=300000
            if etime - stime != 300000:
                msg = f"Device stats monitor data upload failed at stime={stime}, etime={etime}"
                self.logger_obj.error(msg)
                Issues.append(msg)
            else:
                self.logger_obj.info(f"Device stats monitor data upload interval is correct (5 min) at stime={stime}, etime={etime}")

        if len(Issues) >= 1:
            pytest.fail(str(Issues))

    @allure.title(
        "Verify correctness of min/max/avg 'mem_wss_pct' and 'cpu_total_pct' value of tbl_mon_processes table of upm_device_stats in upload json of upm_upload_stats.db for regular session")
    @pytest.mark.parametrize("check_field,app_type", [("mem_wss_pct", "mem"), ("cpu_total_pct", "cpu")])
    def test_verify_correctness_min_max_avg_field_of_tbl_mon_processes(self, check_field, app_type):
        """
        This function Verify correctness of min/max/avg 'mem_wss_pct'  and 'cpu_total_pct' value of tbl_mon_processes table of upm_device_stats in upload json of upm_upload_stats.db for regular session
        """
        self.logger_obj.info(
            f"\n---------DeviceStats:Verify correctness of min/max/avg {check_field} value of tbl_mon_processes table of upm_device_stats in upload json of upm_upload_stats.db for regular session----------------------------\n")

        Issues = []
        data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='deviceStats')
        if data is None:
            assert False, "Not able to fetch data from device Stats log file"

        pattern = re.compile(r'(?ims)lastUpmMetricsUploadTime:([\d]*)')
        res = pattern.findall(data)

        if len(res) <= 1:
            self.logger_obj.info(f"Insufficient Data for test case")
            pytest.skip(f"Insufficient Data for test case")

        for it in res:
            stime = int(it)
            etime = stime + 15 * 60 * 1000

            self.db_handler.create_db_connection(self.device_stats_db_path)
            query_device_stats = f"select {check_field} from tbl_mon_processes where timestamp between {stime} AND {etime} order by  {check_field}"
            data_for_percentile = self.db_handler.execute_query(query_device_stats)

            data = [it[0] for it in data_for_percentile]
            # [p50,p75,p90,p95,p99]
            percentile = self.calculate_percentiles(data)

            query_device_stats = f"select min({check_field}), max({check_field}), avg({check_field}) from tbl_mon_processes where timestamp between {stime} AND {etime}"
            res_device_stats = self.db_handler.execute_query(query_device_stats)
            self.db_handler.close_db_connection()
            if len(res_device_stats) == 0:
                continue
            # Get First ROw
            res_device_stats = res_device_stats[0]
            # Get Field of first row
            mn = res_device_stats[0]
            mx = res_device_stats[1]
            avg = round(res_device_stats[2], 2)
            # res:[min,max,avg]
            self.db_handler.create_db_connection(self.upload_stats_db_path)
            query_upload_stats = f'select * from upload_data where timestamp={etime}'
            res_upload_stats = self.db_handler.execute_query(query_upload_stats)
            self.db_handler.close_db_connection()

            if len(res_upload_stats) == 0:
                continue
            # Get First row
            res_upload_stats = res_upload_stats[0]
            # Get Timestamp
            timestamp = res_upload_stats[0]
            uploadJsonData = None
            try:
                uploadJsonData = json.loads(res_upload_stats[2])
            except Exception as e:
                self.logger_obj.error(
                    f"upload_json in 'upload_data' table is not in proper json format at timestamp {timestamp}")
                Issues.append(
                    f"upload_json in 'upload_data' table is not in proper json format at timestamp {timestamp}")
                continue
            for probe in uploadJsonData["apps"]["probes"]:
                if probe["app"]["type"] != app_type:
                    continue
                stime = probe["stime"]
                etime = probe["etime"]
                if etime - stime < 300 or probe["stats"].get(f"pct_used") is None:
                    continue
                data = probe["stats"]["pct_used"]

                if abs(data['min'] - mn) != 0.0:
                    self.logger_obj.error(
                        f"'{check_field}' Min {data['min']}::{mn} not matching diff:{data['min'] - mn} at timestamp(upload_data): {timestamp} ")
                    Issues.append(
                        f"'mem_wss_pct' Min {data['min']}::{mn} not matching diff:{data['min'] - mn} at timestamp(upload_data): {timestamp} ")

                if abs(data['max'] - mx) != 0.0:
                    self.logger_obj.error(
                        f" '{check_field}' Max {data['max']}::{mx} not matching diff: {data['max'] - mx} at timestamp(upload_data): {timestamp}")
                    Issues.append(
                        f" '{check_field}' Max {data['max']}::{mx} not matching diff: {data['max'] - mx} at timestamp(upload_data): {timestamp}")

                if abs(data['avg'] - avg) != 0.0:
                    self.logger_obj.error(
                        f" '{check_field}' AVg {data['avg']}::{avg} not matching dif:{data['avg'] - avg} at timestamp(upload_data): {timestamp}")
                    Issues.append(
                        f" '{check_field}' AVg {data['avg']}::{avg} not matching dif:{data['avg'] - avg} at timestamp(upload_data): {timestamp}")

                if abs(data['p50'] - percentile[0]) != 0.0:
                    self.logger_obj.error(
                        f" '{check_field}' p50 {data['p50']}::{percentile[0]} not matching dif:{data['p50'] - percentile[0]} at timestamp(upload_data): {timestamp}")
                    Issues.append(
                        f" '{check_field}' p50 {data['p50']}::{percentile[0]} not matching dif:{data['p50'] - percentile[0]} at timestamp(upload_data): {timestamp}")

                if abs(data['p75'] - percentile[1]) != 0.0:
                    self.logger_obj.error(
                        f" '{check_field}' p75 {data['p75']}::{percentile[1]} not matching dif:{data['p75'] - percentile[1]} at timestamp(upload_data): {timestamp}")
                    Issues.append(
                        f" '{check_field}' p75 {data['p75']}::{percentile[1]} not matching dif:{data['p75'] - percentile[1]} at timestamp(upload_data): {timestamp}")

                if abs(data['p90'] - percentile[2]) != 0.0:
                    self.logger_obj.error(
                        f" '{check_field}' p90 {data['p90']}::{percentile[2]} not matching dif:{data['p90'] - percentile[2]} at timestamp(upload_data): {timestamp}")
                    Issues.append(
                        f" 'mem_wss_pct' p90 {data['p90']}::{percentile[2]} not matching dif:{data['p90'] - percentile[2]} at timestamp(upload_data): {timestamp}")

                if abs(data['p95'] - percentile[3]) != 0.0:
                    self.logger_obj.error(
                        f" '{check_field}' p95 {data['p95']}::{percentile[3]} not matching dif:{data['p95'] - percentile[3]} at timestamp(upload_data): {timestamp}")
                    Issues.append(
                        f" '{check_field}' p95 {data['p95']}::{percentile[3]} not matching dif:{data['p95'] - percentile[3]} at timestamp(upload_data): {timestamp}")

                # For now skipping p99 check, not added in android
                # if abs(data['p99'] - percentile[4]) != 0.0:
                #     self.logger_obj.error(
                #         f" '{check_field}' p99 {data['p99']}::{percentile[4]} not matching dif:{data['p99'] - percentile[4]} at timestamp(upload_data): {timestamp}")
                #     Issues.append(
                #         f" '{check_field}' p99 {data['p99']}::{percentile[4]} not matching dif:{data['p99'] - percentile[4]} at timestamp(upload_data): {timestamp}")

        if len(Issues) >= 1:
            pytest.fail(str(Issues))


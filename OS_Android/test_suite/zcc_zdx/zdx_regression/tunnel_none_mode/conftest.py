import json, os
import time

import pytest

from common_lib.mobileadmin.deviceoverview import DeviceOverview
from common_lib.adminzia.admingroupuser import AdminGroup<PERSON>ser
from common_lib.mobileadmin.appprofile import AppProfile
from common_lib.mobileadmin.forwardingprofile import ForwardingProfile
from common_lib.adminzia.ziahelper import *
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.log_ops import *
from OS_Android.library.ops_system import SysOps
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
from common_lib.mobileadmin.trustednetwork import TrustedNetwork
from common_lib.mobileadmin.serviceentitlement import ServiceEntitlement
from OS_Android.library.database_parse import <PERSON><PERSON><PERSON>and<PERSON>
from OS_Android.library.database_parse import LogHandler
from OS_Android.library.zdx_admin import ZDX_ADMIN
from common_lib.mobileadmin.clientconnectorsupport import ClientConnectorSupport

config_path = os.path.join(os.getcwd(), "config", os.environ.get("CONFIG_FILE"))

with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log", log_level="DEBUG")

device_overview = DeviceOverview(variables["CLOUD"], os.environ.get("CONFIG_FILE"), log_handle=logger_obj)

const_obj = constants.Utils()

sys_ops = SysOps(logger_obj, variables["CLOUD"])

export_logs = FetchAndroidLogs(logger_obj)

create_zia_user = AdminGroupUser(variables["CLOUD"], os.environ.get("CONFIG_FILE"), log_handle=logger_obj)

zia_activate = ZiaHelper(variables["CLOUD"], os.environ.get("CONFIG_FILE"), log_handle=logger_obj)

log_ops = LogOps(log_handle=logger_obj)

pcap_ops = PcapOps(log_handle=logger_obj)

app_profile_obj = AppProfile(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)

forwarding_profile_obj = ForwardingProfile(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)
trusted_network = TrustedNetwork(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)
service_ent = ServiceEntitlement(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)
zdx_admin = ZDX_ADMIN(cloudname=variables["CLOUD_DETAILS"]["ZDX"], configfile=variables,
                      logger=logger_obj)
ma_clientconnectorsupport = ClientConnectorSupport(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)

zia_application = variables["ZDX_ADMIN"]["ZDX_APPLICATION"][0]
zpa_application = variables["ZDX_ADMIN"]["ZDX_APPLICATION"][1]
zia_traceroute_domain = zia_application["tracert_icmp"]["host"]
zia_webprobe_url = zia_application["web_probe"]["url"]
zpa_traceroute_domain = zpa_application["tracert_icmp"]["host"]
zpa_webprobe_url = zpa_application["web_probe"]["url"]
probe_port = 443

db_handler = DbHandler()
log_handler = LogHandler()


@pytest.fixture(scope="package", autouse=True)
def setup_teardown():
    app_profile = app_profile_obj
    forwarding_profile = forwarding_profile_obj

    try:
        zcc = Zcc(log_handle=logger_obj, start_app=True)

        zcc.logger.info("Deleting App profile")
        try:
            result = app_profile.delete_app_profile(operating_system="Android")
            assert result[0], result[1]
        except Exception as e:
            zcc.logger.info(str(e))

        zcc.logger.info("Deleting Forwarding profile")
        try:
            result = forwarding_profile.delete_forwarding_profile()
            assert result[0], result[1]
        except Exception as e:
            zcc.logger.info(str(e))

        result = forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = app_profile.create_app_profile(operating_system="Android")
        assert result[0], result[1]

        result = forwarding_profile.edit_forwarding_profile(tunnel_mode='None')
        assert result[0], result[1]

        result = service_ent.toggle_zdx(action=True)
        assert result[0], result[1]

        result = service_ent.toggle_zpa(action=True)
        assert result[0], result[1]

        zcc.logger.info("Creating webmonitor and traceroute for testing in tunnel_none_mode from zdx_regression")
        zdx_admin.create_application_and_monitors()

        logged_in = zcc.validate_zcc_logged_in()
        if logged_in[0]:
            zcc.logger.info("Clear logs from zcc app")
            result = zcc.clear_logs()
            assert result[0], result[1]

            zcc.logger.info("Logout from zcc")
            result = zcc.logout()
            assert result[0], result[1]

        result = zcc.login(variables["ZIA_USER_ID"], variables["ZIA_USER_PASSWORD"], variables["ZPA_USER_ID"],
                           variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]

        result = zcc.verify_service_status(service_type="ZIA", service_status="disable")
        assert result[0], result[1]

        result = zcc.verify_service_status(service_type="ZPA", service_status="on")
        assert result[0], result[1]

        result = zcc.verify_service_status(service_type="ZDX", service_status="on")
        assert result[0], result[1]

        broker_ip_status = zcc.get_broker_ip()

        zcc.logger.info("Sleep 10 mins: collecting zdx data for tunnel none mode")
        time.sleep(600)

        zcc.logger.info("Fetch logs from android device")
        result = zcc.export_logs()
        assert result[0], result[1]

        zcc.logger.info("Export logs from android device")
        result = export_logs.read_export_log_mail()
        assert result[0], result[1]

    except Exception as e:
        pytest.skip(f"Skipping testcases due to setup failure: {e}")

    yield broker_ip_status

    zcc.logger.info( "Deleting webmonitor and traceroute for testing in tunnel_none_mode from zdx_regression")
    zdx_admin.delete_application_and_monitors()

    zcc.logger.info("Deleting App profile")
    try:
        result = app_profile.delete_app_profile(operating_system="Android")
        assert result[0], result[1]
    except Exception as e:
        zcc.logger.info(str(e))

    zcc.logger.info("Deleting Forwarding profile")
    try:
        result = forwarding_profile.delete_forwarding_profile()
        assert result[0], result[1]
    except Exception as e:
        zcc.logger.info(str(e))

    logged_in = zcc.validate_zcc_logged_in()
    if logged_in[0]:
        zcc.logger.info("Clear logs from zcc app")
        result = zcc.clear_logs()
        assert result[0], result[1]

        zcc.logger.info("Logout from zcc")
        result = zcc.logout()
        assert result[0], result[1]


@pytest.fixture()
def broker_ip_data(setup_teardown):
    broker_ip_status = setup_teardown

    yield broker_ip_status

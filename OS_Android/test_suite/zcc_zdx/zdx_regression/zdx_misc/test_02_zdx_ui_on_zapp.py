import time, os
import json, re
import pytest, allure
from scapy.all import *
import datetime
from OS_Android.test_suite.zcc_zdx.zdx_regression.zdx_misc import conftest
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers
from OS_Android.library.ui_zcc import Zcc


log_db_presence_input = [("upm", 0), ("web", 0), ("traceroute", 0), ("deviceStats", 0),
                         ("deviceProfile", 1), ("uploadStats", 1), ("web", 1),
                         ("traceroute", 1), ("deviceStats", 1)
                         ]


class TestZdxUI:

    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.service_ent = conftest.service_ent
        self.logger_obj = conftest.logger_obj
        self.elements = copy.deepcopy(AndroidElements)
        self.client_connector = conftest.client_connector
        self.zcc = Zcc(log_handle=self.logger_obj, start_app=True)
        self.zdx_username = conftest.variables["ZIA_USER_ID"]

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_type="ZDX", service_status="on")
        assert result[0], result[1]

        time_connected = self.zcc.verify_time_connected(service_type='ZDX')
        assert time_connected[0], time_connected[1]
        self.time_connected_when_zdx_was_on = time_connected[1]

    def verify_turn_onn_off_zdx(self):
        """
             Turn ON / Off 'Digital Experience' in ZApp - Connectivity

             It performs the following steps:
             1. It is validating 'Service Status' should show OFF status when ZDX turn off and ON when its ON
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    return bool

        """

        self.logger_obj.info("Verifying: Turn ON / Off 'Digital Experience' in ZApp - Connectivity")

        self.logger_obj.info("Checking status on ZDX")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="off", sleep=3)

        if result[0]:
            self.logger_obj.info("Turning ON ZDX before running the testcase")
            result = self.zcc.toggle_service(service="ZDX", action=True)
            if not result[0]: return False, result[1]

        self.logger_obj.info("Clearing Notification")
        result = self.zcc.clear_notification()
        if not result[0]: return False, result[1]

        self.logger_obj.info("Turning OFF ZDX")
        result = self.zcc.toggle_service(service="ZDX", action=False)
        if not result[0]: return False, result[1]

        self.logger_obj.info("Verifying off status on ZDX")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="off")
        if not result[0]: return False, result[1]

        self.logger_obj.info("Turning ON ZDX")
        result = self.zcc.toggle_service(service="ZDX", action=True)
        if not result[0]: return False, result[1]

        self.logger_obj.info("Verifying on status on ZDX")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="on")
        if not result[0]: return False, result[1]

        self.logger_obj.info("Verifying Turn off and on notification")
        result = self.zcc.verify_no_restart_service_notification(service="ZDX", expect_failure=True)
        if not result[0]: return False, result[1]

        return True, "Successfully verified"

    @allure.title("ZDX Service Status ON - 'Service Status' should show 'Connecting' when ZCC tries to connect to TPG")
    @pytest.mark.xray("QA-149419")
    @add_markers("regression")
    def test_zdx_shows_connecting_when_tries_to_connect_to_tpg(self):
        """
             This function verifies ZDX Service Status ON - 'Service Status' should show
             'Connecting' when ZCC tries to connect to TPG

             It performs the following steps:
             1. It is validating 'Service Status' should show 'Connecting' when ZCC tries to connect to TPG
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("'Service Status' should show 'Connecting' when ZCC tries to connect to TPG")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="Connecting", sleep=5)

        if result[0]:
            self.logger_obj.info("ZDX showed 'Connecting' status")
        else:
            result = self.zcc.verify_service_status(service_type="ZDX", service_status="on")
            assert result[0], result[1]

    @allure.title("ZDX Service Status ON - 'Service Status' should show 'ON' when TPG is connected")
    @pytest.mark.dependency()
    @pytest.mark.xray("QA-149418")
    @add_markers("regression")
    def test_zdx_shows_on_status_when_zdx_connected_to_tpg(self):
        """
             ZDX Service Status ON - 'Service Status' should show 'ON' when TPG is connected

             It performs the following steps:
             1. It is validating 'Service Status' should show 'ON' when TPG is connected
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """
        self.logger_obj.info("ZDX Service Status ON - 'Service Status' should show 'ON' when TPG is connected")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="on")
        assert result[0], result[1]

    @allure.title("ZDX Service Status ON - 'username' should be correctly displayed")
    @pytest.mark.dependency(depends=['TestZdxUI::test_zdx_shows_on_status_when_zdx_connected_to_tpg'])
    @pytest.mark.xray("QA-149417")
    @add_markers("regression")
    def test_zdx_username_when_zdx_status_is_on(self):
        """
             ZDX Service Status ON - 'username' should be correctly displayed

             It performs the following steps:
             1. It is validating username from ZDX tab when ZDX is on
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("ZDX Service Status ON - 'username' should be correctly displayed")
        result = self.zcc.get_username_from_zapp(service_type="ZDX")
        assert result[0], result[1]

        self.logger_obj.info(f"Username fetched from UI = {result[1]} and given username is = {self.zdx_username} ")
        assert result[1] == self.zdx_username, "Username not matched"

    @allure.title(
        "ZDX Service Status ON - 'Authentication Status' shows 'Authenticated' when user is authenticated to TPG")
    @pytest.mark.dependency(depends=['TestZdxUI::test_zdx_shows_on_status_when_zdx_connected_to_tpg'])
    @pytest.mark.xray("QA-149421")
    @add_markers("regression")
    def test_zdx_authenticated_status(self):
        """
             ZDX Service Status ON - 'Authentication Status' shows 'Authenticated' when user is authenticated to TPG

             It performs the following steps:
             1. It is validating Authentication Status from ZDX tab when ZDX is on
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("ZDX Service Status ON - 'Authentication Status' shows 'Authenticated' "
                             "when user is authenticated to TPG")
        result = self.zcc.get_authenticated_status(service_type="ZDX")
        assert result[0], result[1]

        self.logger_obj.info(f"Auth_status fetched from UI = {result[1]} and validation auth is = 'Authenticated' ")
        assert result[1] == 'Authenticated', "Auth_status not matched"

    @allure.title("ZDX Service Status ON - 'Server' shows the TPG IP that ZCC is connected to.")
    @pytest.mark.dependency(depends=['TestZdxUI::test_zdx_shows_on_status_when_zdx_connected_to_tpg'])
    @pytest.mark.xray("QA-149423")
    @add_markers("regression")
    def test_zdx_server_address(self):
        """
             ZDX Service Status ON - 'Server' shows the TPG IP that ZCC is connected to.

             It performs the following steps:
             1. It is validating server_ip from ZDX tab when ZDX is on
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("ZDX Service Status ON - 'Server' shows the TPG IP that ZCC is connected to.")

        result = self.zcc.get_zdx_server_address()
        assert result[0], result[1]

        server_address_from_ui = result[1]

        self.zcc.logger.info("Sleep 30s for data collection")
        time.sleep(30)

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        time.sleep(2)

        server_address_from_logs = self.log_handler.fetch_zdx_server_address_from_logs()
        assert server_address_from_logs[0], server_address_from_logs[1]

        self.logger_obj.info(f"Fetched server address from UI = {server_address_from_ui} and validation address from"
                             f"logs is = {server_address_from_logs[1]} ")
        assert server_address_from_ui == server_address_from_logs[1], "Server address not matched in log and ZDX UI"

    @allure.title("ZDX Service Status ON - 'ZDX Service Version' should be correctly displayed")
    @pytest.mark.dependency(depends=['TestZdxUI::test_zdx_shows_on_status_when_zdx_connected_to_tpg'])
    @pytest.mark.xray("QA-149427")
    @add_markers("regression")
    def test_zdx_service_version(self):
        """
             ZDX Service Status ON - 'ZDX Service Version' should be correctly displayed

             It performs the following steps:
             1. It is validating ZDX Service Version from ZDX tab when ZDX is on
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("ZDX Service Status ON - 'ZDX Service Version' should be correctly displayed")

        result = self.zcc.get_zdx_service_version()
        assert result[0], result[1]

        zcc_version = self.sys_ops.get_zcc_version()
        zcc_version = zcc_version[2].split('=')[1]
        zcc = zcc_version.split('.')
        zcc = zcc[0] + '.' + zcc[1]
        zcc = float(zcc)

        zdx_version = ""
        if zcc == 3.7 or zcc == 3.8 or zcc == 4.0:
            zdx_version = "3.2"
        else:
            pytest.skip("Add corresponding ZDX version with ZCC in the automation code")

        self.logger_obj.info(f"Fetched ZDX version from UI = {result[1]} and zdx version corresponding"
                             f" to zcc is = {zdx_version} ")
        assert result[1] == zdx_version, "ZDX version is showing incorrect not on ZDX UI"

    @allure.title("ZDX Service Status ON - 'Time Connected' should have correct time, date, year")
    @pytest.mark.dependency(depends=['TestZdxUI::test_zdx_shows_on_status_when_zdx_connected_to_tpg'])
    @pytest.mark.xray(["QA-149424", "QA-149426", "QA-149301"])
    @add_markers("regression")
    def test_zdx_time_connected_format(self):
        """
             ZDX Service Status ON - 'Time Connected' should have correct time, date, year
             It performs the following steps:
             1. It is validating Time Connected from ZDX tab when ZDX is on
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("ZDX Service Status ON - 'Time Connected' should have correct time, date, year")
        log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')
        if not log_data:
            raise Exception(f"No log found in {self.const_obj.LOG_PATH} for UPM")

        result = self.zcc.verify_time_connected(service_type='ZDX')
        assert result[0], result[1]

        time_connected_from_ui = result[1]
        pattern = re.compile(r'Timezone:.*?Offset: ([\d]*), Standard Name', re.DOTALL)
        responses = pattern.findall(log_data)
        if len(responses) == 0:
            assert False, "Not able to find timezone offset in upm logs"
        time_zone_offset = int(responses[0])
        time_connected_from_logs = \
            self.log_handler.find_log_line_timestamp_in_unix(data=log_data, pattern="UTC Offset")[0]
        time_connected_from_logs += time_zone_offset

        unix_timestamp = int(time_connected_from_logs)

        # Convert the Unix timestamp to a datetime object
        date_time = datetime.datetime.fromtimestamp(unix_timestamp)

        # Format the datetime object into the desired string format
        formatted_date_time = datetime.datetime.strftime(date_time, "%I:%M %p %b %d, %Y")

        self.logger_obj.info(
            "QA-149426:: ZDX Service Status ON - 'Time Connected' should have correct time, date, year")
        self.logger_obj.info(f"Fetched Time Connected from UI = {time_connected_from_ui} and timeConnected"
                             f" from logs is = {formatted_date_time} ")
        assert time_connected_from_ui == formatted_date_time, "Time Connected showing incorrect value on ZDX UI"

    @allure.title("Verify if the Digital Experience Turned ON message is shown in the notification tab when user"
                  " login to ZCC for the first time")
    @pytest.mark.dependency(depends=['TestZdxUI::test_zdx_shows_on_status_when_zdx_connected_to_tpg'])
    @pytest.mark.xray("QA-149299")
    @add_markers("regression")
    def test_zdx_turned_on_notification(self):
        """
            Verify if the Digital Experience Turned ON message is shown in the notification
            tab when user login to ZCC for the first time

             It performs the following steps:
             1. It is validating 'Turned ON' message should show in notification tab when ZCC logged in with zdx enabled
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Verify if the Digital Experience Turned ON message is shown in the notification "
                             "tab when user login to ZCC for the first time")
        status = False
        msg = ""

        notification_tab = self.elements.NOTIFICATIONS_TAB_XPATH
        turned_on_notification = self.elements.DIGITAL_EXPERIENCE_TURNED_ON_NOTIFICATION

        try:
            self.zcc.ui.click(path=notification_tab, sleep_time=1)
            self.zcc.ui.check_if_exists(turned_on_notification, AEFC="Digital Experience Turned ON is not present")

            status = True
            msg = "Expected :: 'Digital Experience Turned ON' is present"

        except Exception as e:
            status = False
            msg = f"Failure :: 'Digital Experience Turned ON' is not present \n {e}"

        assert status, msg

    @allure.title("Verify ZCC-ZDX UI is categorized into two sections: 'Connectivity' and 'Troubleshoot'")
    @pytest.mark.dependency(depends=['TestZdxUI::test_zdx_shows_on_status_when_zdx_connected_to_tpg'])
    @pytest.mark.xray("QA-149416")
    @add_markers("regression")
    def test_two_zdx_ui_section_troubleshoot_and_connectivity(self):
        """
             Verify ZCC-ZDX UI is categorized into two sections: 'Connectivity' and 'Troubleshoot'
             It performs the following steps:
             1. It is validating that  ZCC-ZDX UI is categorized into two sections: 'Connectivity' and 'Troubleshoot'"
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Verify ZCC-ZDX UI is categorized into two sections: 'Connectivity' and 'Troubleshoot'")

        status = False
        msg = ""

        zdx_icon = self.elements.DIGITAL_EXPERIENCE_TAB
        troubleshoot = self.elements.TROUBLESHOOT_HEADER
        connectivity = self.elements.CONNECTIVITY_HEADER
        try:
            self.zcc.ui.click(path=zdx_icon, AEFC="Not able to click on zdx service tab", sleep_time=2)

            self.logger_obj.info("Checking Connectivity Header is present in ZDX UI")
            self.zcc.ui.check_if_exists(path=connectivity)
            self.logger_obj.info("Successfully verified")

            self.logger_obj.info("Checking Troubleshoot Header is present in ZDX UI")
            self.zcc.ui.check_if_exists(path=troubleshoot)
            self.logger_obj.info("Successfully verified")

            status = True
            msg = "Expected :: Connectivity and Troubleshoot section found when zdx is ON"

        except Exception as e:
            status = False
            msg = f"Failure :: Connectivity and Troubleshoot section not found when zdx is ON\n {e}"

        assert status, msg

    @allure.title(
        "ZDX Service Status ON - user can toggle 'Service Status' to OFF and status should be correctly displayed")
    @pytest.mark.dependency()
    @pytest.mark.xray("QA-149420")
    @add_markers("regression")
    def test_user_can_turn_off_zdx(self):
        """
             ZDX Service Status ON - user can toggle 'Service Status' to OFF and status should be correctly displayed

             It performs the following steps:
             1. It is validating 'Service Status' should show OFF status when ZDX turn off
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("ZDX Service Status ON - user can toggle 'Service Status'"
                             " to OFF and status should be correctly displayed")

        self.logger_obj.info("Turning OFF ZDX")
        result = self.zcc.toggle_service(service="ZDX", action=False)
        assert result[0], result[1]

        self.logger_obj.info("Verifying off status on ZDX")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="off")
        assert result[0], result[1]

    @allure.title(
        "Verify that all the ZDX related log files are included in the exported zip file after user turns off zdx")
    @pytest.mark.dependency(depends=['TestZdxUI::test_user_can_turn_off_zdx'])
    @pytest.mark.xray("QA-151969")
    @add_markers("regression")
    @pytest.mark.parametrize("plugin,type", log_db_presence_input)
    def test_log_db_presence_in_exported_logs_when_zdx_is_off(self, plugin, type):
        """
             This function verifies the presence of given log/db file presence

             It performs the following steps:
             1. It will check upm logs and db files from exported logs
                Args:
                    self (object): Instance of the test class.
                    plugin (str): Plugin name. Ex - Webprobe, Traceroute etc.
                    type (int) : 0 - Log type, 1 - db type
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        status = True
        msg = ""
        file_type = {0: 'LOG', 1: 'DB'}
        logs_files = {'upm': 'ZSAUpm', 'web': 'ZSAUpm-Webload', 'traceroute': 'ZSAUpm-ZTraceroute',
                      'deviceStats': 'ZSAUpm-DeviceStats'}

        if type == 0:
            self.zcc.logger.info(f"Verifying presence of {logs_files[plugin]} {file_type[type]} file in exported logs")
            zcc_log_files = self.log_handler.get_latest_logfile(self.const_obj.LOG_PATH)
            if len(zcc_log_files[plugin]) == 0:
                status = False
                msg = f"{logs_files[plugin]} {file_type[type]} file is not present in exported logs"

        if type == 1:
            self.zcc.logger.info(f"Verifying presence of {plugin} {file_type[type]} file in exported logs")
            zcc_db_files = self.db_handler.get_latest_dbfile(self.const_obj.LOG_PATH)
            if len(zcc_db_files[plugin]) == 0:
                status = False
                msg = f"{plugin} {file_type[type]} file is not present in exported logs"

        assert status, msg

    @allure.title("ZDX Service Status OFF - 'Service Status' should show 'OFF'")
    @pytest.mark.dependency(depends=['TestZdxUI::test_user_can_turn_off_zdx'])
    @pytest.mark.xray("QA-149436")
    @add_markers("regression")
    def test_zdx_service_status_off(self):
        """
             ZDX Service Status OFF - 'Service Status' should show 'OFF'
             It performs the following steps:
             1. It is validating 'Service Status' should show OFF status when ZDX turn off
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("ZDX Service Status OFF - 'Service Status' should show 'OFF'")

        self.logger_obj.info("Verifying off status on ZDX")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="off")
        assert result[0], result[1]

    @allure.title("ZDX Service Status OFF - The ZDX 'Toubleshoot' Section should be hidden")
    @pytest.mark.dependency(depends=['TestZdxUI::test_user_can_turn_off_zdx'])
    @pytest.mark.xray("QA-149443")
    @add_markers("regression")
    def test_zdx_troubleshoot_section_when_zdx_is_off(self):
        """
             ZDX Service Status OFF - 'Service Status' should show 'OFF'
             It performs the following steps:
             1. It is validating that clear zdx and restart zdx should not present when ZDX turn off
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("ZDX Service Status OFF - The ZDX 'Toubleshoot' Section should be hidden")

        status = False
        msg = ""

        zdx_icon = self.elements.DIGITAL_EXPERIENCE_TAB
        restart_zdx_element = self.elements.DIGITAL_EXPERIENCE_CLEAR_DATA_LABEL
        clear_zdx_data = self.elements.DIGITAL_EXPERIENCE_RESTART
        troubleshoot = self.elements.TROUBLESHOOT_HEADER
        try:
            self.zcc.ui.click(path=zdx_icon, AEFC="Not able to click on zdx service tab", sleep_time=2)
            self.zcc.ui.check_if_exists(path=clear_zdx_data, expect_failure=True)
            self.zcc.ui.check_if_exists(path=restart_zdx_element, expect_failure=True)
            self.zcc.ui.check_if_exists(path=troubleshoot, expect_failure=True)
            status = True
            msg = "Expected :: Troubleshoot section not found when zdx is off"

        except Exception as e:
            status = False
            msg = f"Failure :: Troubleshoot section found when zdx is off\n {e}"

        assert status, msg

    @allure.title("ZDX Service Status OFF - 'username' should be correctly displayed")
    @pytest.mark.dependency(depends=['TestZdxUI::test_user_can_turn_off_zdx'])
    @pytest.mark.xray("QA-149435")
    @add_markers("regression")
    def test_zdx_username_when_zdx_status_is_off(self):
        """
             ZDX Service Status OFF - 'username' should be correctly displayed

             It performs the following steps:
             1. It is validating username from ZDX tab when ZDX is off
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("ZDX Service Status OFF - 'username' should be correctly displayed")
        result = self.zcc.get_username_from_zapp(service_type="ZDX")
        assert result[0], result[1]

        self.logger_obj.info(f"Username fetched from UI = {result[1]} and given username is = {self.zdx_username} ")
        assert result[1] == self.zdx_username, "Username not matched"

    @allure.title("ZDX Service Status OFF - 'Authentication Status' should not show any value when ZDX is off.")
    @pytest.mark.dependency(depends=['TestZdxUI::test_user_can_turn_off_zdx'])
    @pytest.mark.xray("QA-149439")
    @add_markers("regression")
    def test_zdx_auth_status_not_present_when_zdx_is_off(self):
        """
             ZDX Service Status OFF - 'Authentication Status' should not show any value when ZDX is off.

             It performs the following steps:
             1. It is validating 'Authentication Status' should not show any value when ZDX is off.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info(
            "ZDX Service Status OFF - 'Authentication Status' shouldn't show any value when ZDX is off")
        status = False
        msg = ""

        auth_status_element = self.elements.ZDX_AUTH_STATUS_TEXT
        zdx_icon = self.elements.DIGITAL_EXPERIENCE_TAB
        try:
            self.zcc.ui.click(path=zdx_icon, AEFC="Not able to click on zdx service tab", sleep_time=2)
            self.zcc.ui.check_if_exists(path=auth_status_element, expect_failure=True)
            status = True
            msg = "Expected :: Authentication status not found when zdx is off"

        except Exception as e:
            status = False
            msg = f"Failure :: Authentication status found when zdx is off\n {e}"

        assert status, msg

    @allure.title("ZDX Service Status OFF - 'Server' should not show any value when ZDX is off.")
    @pytest.mark.dependency(depends=['TestZdxUI::test_user_can_turn_off_zdx'])
    @pytest.mark.xray("QA-149440")
    @add_markers("regression")
    def test_zdx_server_not_present_when_zdx_is_off(self):
        """
            ZDX Service Status OFF - 'Server' should not show any value when ZDX is off.

             It performs the following steps:
             1. It is validating 'Server' should not show any value when ZDX is off.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("ZDX Service Status OFF - 'Server' should not show any value when ZDX is off.")
        status = False
        msg = ""

        auth_status_element = self.elements.ZDX_SERVER
        zdx_icon = self.elements.DIGITAL_EXPERIENCE_TAB
        try:
            self.zcc.ui.click(path=zdx_icon, AEFC="Not able to click on zdx service tab", sleep_time=2)
            self.zcc.ui.check_if_exists(path=auth_status_element, expect_failure=True)
            status = True
            msg = "Expected :: Server Address not found when zdx is off"

        except Exception as e:
            status = False
            msg = f"Failure :: Server Address found when zdx is OFF \n {e}"

        assert status, msg

    @allure.title("ZDX Service Status OFF - 'Time connected' should not show any value when ZDX is off.")
    @pytest.mark.dependency(depends=['TestZdxUI::test_user_can_turn_off_zdx'])
    @pytest.mark.xray("QA-149441")
    @add_markers("regression")
    def test_time_connected_not_present_when_zdx_is_off(self):
        """
            ZDX Service Status OFF - 'Time Connected' should not show any value when ZDX is off.

             It performs the following steps:
             1. It is validating 'Time Connected' should not show any value when ZDX is off.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("ZDX Service Status OFF - 'Time Connected' should not show any value when ZDX is off.")
        status = False
        msg = ""

        auth_status_element = self.elements.TIME_CONNECTED
        zdx_icon = self.elements.DIGITAL_EXPERIENCE_TAB
        try:
            self.zcc.ui.click(path=zdx_icon, AEFC="Not able to click on zdx service tab", sleep_time=2)
            self.zcc.ui.check_if_exists(path=auth_status_element, expect_failure=True)
            status = True
            msg = "Expected :: Time Connected not found when zdx is off"

        except Exception as e:
            status = False
            msg = f"Failure :: Time Connected found when zdx is OFF \n {e}"

        assert status, msg

    @allure.title("ZDX Service Status OFF - 'ZDX Service Version' should not show any value when ZDX is off.")
    @pytest.mark.dependency(depends=['TestZdxUI::test_user_can_turn_off_zdx'])
    @pytest.mark.xray("QA-149442")
    @add_markers("regression")
    def test_zdx_version_not_present_when_zdx_is_off(self):
        """
            ZDX Service Status OFF - 'ZDX version' should not show any value when ZDX is off.

             It performs the following steps:
             1. It is validating 'ZDX Version' should not show any value when ZDX is off.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("ZDX Service Status OFF - 'ZDX Version' should not show any value when ZDX is off.")
        status = False
        msg = ""

        auth_status_element = self.elements.ZDX_VERSION
        zdx_icon = self.elements.DIGITAL_EXPERIENCE_TAB
        try:
            self.zcc.ui.click(path=zdx_icon, AEFC="Not able to click on zdx service tab", sleep_time=2)
            self.zcc.ui.check_if_exists(path=auth_status_element, expect_failure=True)
            status = True
            msg = "Expected :: 'ZDX Version' not found when zdx is off"

        except Exception as e:
            status = False
            msg = f"Failure :: 'ZDX Version' found when zdx is OFF \n {e}"

        assert status, msg

    @allure.title(
        "Verify if the Digital Experience Turned OFF message is shown in the notification tab when ZDX is turned off")
    @pytest.mark.dependency(depends=['TestZdxUI::test_user_can_turn_off_zdx'])
    @pytest.mark.xray("QA-149300")
    @add_markers("regression")
    def test_zdx_turned_off_notification(self):
        """
            Verify if the Digital Experience Turned OFF message is shown in the notification tab when ZDX is turned off

             It performs the following steps:
             1. It is validating 'Turned OFF' message should show in notification tab when ZDX is turnned off.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info(
            "Verify if the Digital Experience Turned OFF message is shown in the notification tab when ZDX is turned off")
        status = False
        msg = ""

        notification_tab = self.elements.NOTIFICATIONS_TAB_XPATH
        turned_off_notification = self.elements.DIGITAL_EXPERIENCE_TURNED_OFF_NOTIFICATION

        try:
            self.zcc.ui.click(path=notification_tab, sleep_time=1)
            self.zcc.ui.check_if_exists(turned_off_notification, AEFC="Digital Experience Turned OFF is not present")

            status = True
            msg = "Expected :: 'Digital Experience Turned OFF' is present"

        except Exception as e:
            status = False
            msg = f"Failure :: 'Digital Experience Turned OFF' is not present \n {e}"

        assert status, msg

    @allure.title("ZDX Service Status ON - 'Time Connected' should change when user turn on ZDX from OFF")
    @pytest.mark.xray("QA-149425")
    @add_markers("regression")
    def test_zdx_time_connected_format_on_restart_upm(self):
        """
             ZDX Service Status ON - 'Time Connected' should change when user turn on ZDX from OFF
             It performs the following steps:
             1. It is validating Time Connected from ZDX tab when ZDX is turned on from OFF
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("ZDX Service Status ON - 'Time Connected' should change when user turn on ZDX from OFF")
        status = False

        result = self.zcc.verify_service_status(service_type="ZDX", service_status="off",sleep=5)
        if result[0]:
            self.logger_obj.info("Turning ON ZDX")
            result = self.zcc.toggle_service(service="ZDX", action=True)
            assert result[0], result[1]

        result = self.zcc.verify_time_connected(service_type='ZDX')
        assert result[0], result[1]

        time_connected_now_from_ui = result[1]
        time_connected_earlier_from_ui = self.time_connected_when_zdx_was_on

        self.logger_obj.info(f"Time when ZDX was earlier on = {time_connected_earlier_from_ui} and time connected"
                             f"now from UI get= {time_connected_now_from_ui}")
        assert time_connected_now_from_ui != time_connected_earlier_from_ui, ("Time Connected not changed after ZDX "
                                                                              "turn OFF to ON")

    @allure.title(
        "ZDX Clear ZDX Data - Clear ZDX data button and pop up message should be displayed once user click on 'clear ZDX data'")
    @pytest.mark.xray("QA-149428")
    @add_markers("regression")
    def test_clear_zdx_data_button(self):
        """
            QA-149428:: ZDX Clear ZDX Data - User should be able to click on 'Clear ZDX Data'
            QA-149429:: ZDX Clear ZDX Data - A pop-up message should be displayed once user click on 'clear ZDX data'
             It performs the following steps:
             1. It is validating that a pop-up message should be displayed once user click on 'clear ZDX data'
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info(
            "ZDX Clear ZDX Data - A pop up message should be displayed once user click on 'clear ZDX data'")

        result = self.zcc.verify_service_status(service_type="ZDX", service_status="off", sleep=5)
        if result[0]:
            self.logger_obj.info("Turning ON ZDX")
            result = self.zcc.toggle_service(service="ZDX", action=True)
            assert result[0], result[1]

        status, msg = self.zcc.clear_zdx_data()
        assert status, msg

    @allure.title(
        "ZDX Clear ZDX Data - Clear ZDX data button and pop up message should be displayed once user click on 'clear ZDX data'")
    @pytest.mark.xray("QA-149429")
    @add_markers("regression")
    def test_clear_zdx_data_pop_up(self):
        """
            QA-149428:: ZDX Clear ZDX Data - User should be able to click on 'Clear ZDX Data'
            QA-149429:: ZDX Clear ZDX Data - A pop-up message should be displayed once user click on 'clear ZDX data'
             It performs the following steps:
             1. It is validating that a pop-up message should be displayed once user click on 'clear ZDX data'
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info(
            "ZDX Clear ZDX Data - A pop up message should be displayed once user click on 'clear ZDX data'")
        status = False
        msg = " "

        result = self.zcc.verify_service_status(service_type="ZDX", service_status="off",sleep=5)
        if result[0]:
            self.logger_obj.info("Turning ON ZDX")
            result = self.zcc.toggle_service(service="ZDX", action=True)
            assert result[0], result[1]

        zdx_icon = self.elements.DIGITAL_EXPERIENCE_TAB
        clear_zdx_element = self.elements.DIGITAL_EXPERIENCE_CLEAR_DATA_LABEL
        clear_data_accept = self.elements.DIGITAL_EXPERIENCE_CLEAR_DATA_ACCEPT_BUTTON
        try:
            self.zcc.ui.click(path=zdx_icon, AEFC="Not able to click on zdx service tab", sleep_time=2)
            self.zcc.ui.click(path=clear_zdx_element, sleep_time=1, AEFC="Unable to click Clear Data button ")
            self.zcc.ui.click(path=clear_data_accept, sleep_time=1, AEFC="Unable to click ZDX Clear Accept Button")

            status = True
            msg = "Expected :: 'Clear ZDX data' button and pop-up found for Clear All"

        except Exception as e:
            status = False
            msg = f"Failure :: 'Clear ZDX data' button not found or pop-up not found for Clear All \n {e}"

        assert status, msg

    @allure.title("ZDX Service Restart - User should be able to click on 'Restart ZDX Service'")
    @pytest.mark.xray(["QA-149431", "QA-149432", "QA-149433"])
    @add_markers("regression")
    def test_restart_zdx_button_and_pop_up(self):
        """
            QA-149431:: ZDX Service Restart - User should be able to click on 'Restart ZDX Service'
            QA-149432:: ZDX Service Restart - A pop-up message should be displayed once user click on 'Restart ZDX Service'
            QA-149433:: ZDX Service Restart - 'Time Connected' should change when user restart ZDX.
             It performs the following steps:
             1. It is validating that User should be able to click on 'Restart ZDX Service'
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("ZDX Service Restart - User should be able to click on 'Restart ZDX Service'")

        self.logger_obj.info("Verifying on status on ZDX")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="on")
        assert result[0], result[1]

        result = self.zcc.verify_time_connected(service_type='ZDX')
        assert result[0], result[1]

        time_connected_before_restart = result[1]

        status, msg = self.zcc.restart_zdx_service()
        assert status, msg

        self.logger_obj.info("Sleep 60s :: For data collection")
        time.sleep(60)

        result = self.zcc.verify_time_connected(service_type='ZDX')
        assert result[0], result[1]

        time_connected_after_restart = result[1]
        self.logger_obj.info(f"Time connected before restart zdx = {time_connected_before_restart} and time connected"
                             f" after restart ZDX = {time_connected_after_restart}")
        assert time_connected_before_restart != time_connected_after_restart, ("Time Connected not changed after ZDX "
                                                                               "restart, it should change")

    @allure.title("Verify Packet Capture, Packet capture functionality is working as expected and is not disrupting"
                  " ZIA or ZPA applications. Internet connectivity was also working fine.")
    @pytest.mark.xray("QA-149270")
    @add_markers("regression")
    def test_packet_capture(self):
        """
             It performs the following steps:
             1. It is Verify Packet Capture, Packet capture functionality is working as expected and is not disrupting
                ZIA or ZPA applications. Internet connectivity was also working fine.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        status = False
        msg = ""
        self.logger_obj.info("Verify Packet Capture, Packet capture functionality is working as expected and is not"
                             " disrupting ZIA or ZPA applications. Internet connectivity was also working fine.")

        self.logger_obj.info("Enabling packet capture from Mobile Admin")
        result = self.client_connector.toggle_packet_capture(action="enable")
        assert result[0], result[1]

        try:
            self.logger_obj.info("Update Policy")
            result = self.zcc.update_policy()
            assert result[0], result[1]

            self.logger_obj.info("Clear Notifications")
            result = self.zcc.clear_notification()
            assert result[0], result[1]

            self.logger_obj.info("Validate packet capture is knob is present")
            result = self.zcc.packet_capture_present()
            assert result[0], result[1]

            self.logger_obj.info("Start packet capture from zcc app")
            result = self.zcc.packet_capture(action='start', clear_notification=False)
            assert result[0], result[1]

            self.logger_obj.info("Sleep 10s")
            time.sleep(10)

            self.logger_obj.info("Stop packet capture from zcc app and verify packet capture is off")
            result = self.zcc.packet_capture(action='stop',clear_notification=False)
            assert result[0], result[1]

            self.logger_obj.info("Verifying during packet capture, there shouldn't be any ZDX restart")
            result = self.zcc.verify_no_restart_service_notification(service="ZDX")
            assert result[0], result[1]

            self.logger_obj.info("Verifying during packet capture, there shouldn't be any ZDX restart")
            result = self.zcc.verify_no_restart_service_notification(service="ZPA")
            assert result[0], result[1]

            self.logger_obj.info("Verifying during packet capture, there shouldn't be any ZDX restart")
            result = self.zcc.verify_no_restart_service_notification(service="ZIA")
            assert result[0], result[1]

            self.logger_obj.info("Successfully verified for all the services \n")

            status = True
            msg = "Successfully verified for all the services"

        except Exception as e:
            status = False
            msg = f"Failed \n {e}"

        self.logger_obj.info("Disabling packet capture from Mobile Admin")
        self.client_connector.toggle_packet_capture(action="disable")

        assert status, msg

    @allure.title("Z-Tunnel 1.0: Route Based: Turn ON / Off 'Digital Experience' in ZApp - Connectivity")
    @pytest.mark.xray("QA-149170")
    @add_markers("regression")
    def test_turn_onn_off_zdx_tunnel_1(self):
        """
            Z-Tunnel 1.0: Route Based: Turn ON / Off 'Digital Experience' in ZApp - Connectivity

             It performs the following steps:
             1. It is validating 'Service Status' should show OFF status when ZDX turn off and ON when its ON
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Z-Tunnel 1.0: Route Based: Turn ON / Off 'Digital Experience' in ZApp - Connectivity")

        result = self.verify_turn_onn_off_zdx()
        assert result[0],result[1]

    @allure.title("Tunnel Mode : ZIA OFF: Turn ON / Off 'Digital Experience' in ZApp - Connectvity")
    @pytest.mark.xray("QA-149182")
    @add_markers("regression")
    def test_turn_onn_off_zdx_when_zia_is_off(self):
        """
            Tunnel Mode : ZIA OFF: Turn ON / Off 'Digital Experience' in ZApp - Connectvity

             It performs the following steps:
             1. It is validating 'Service Status' should show OFF status when ZDX turn off and ON when its ON
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Tunnel Mode : ZIA OFF: Turn ON / Off 'Digital Experience' in ZApp - Connectvity")

        self.logger_obj.info("Turning OFF ZIA before running the testcase")
        result = self.zcc.toggle_service(service="ZIA", action=False)
        assert result[0], result[1]

        result = self.verify_turn_onn_off_zdx()
        assert result[0],result[1]

        self.logger_obj.info("Turning ON ZIA back after running the testcase")
        result = self.zcc.toggle_service(service="ZIA", action=True)
        assert result[0], result[1]

    @allure.title("Tunnel Mode : NONE: Turn ON / Off 'Digital Experience' in ZApp - Connectvity")
    @pytest.mark.xray("QA-149196")
    @add_markers("regression")
    def test_turn_on_off_zdx_tunnel_none_mode(self):
        """
            Tunnel Mode : NONE: Turn ON / Off 'Digital Experience' in ZApp - Connectvity

             It performs the following steps:
             1. It is validating 'Service Status' should show OFF status when ZDX turn off and ON when its ON
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Tunnel Mode : NONE: Turn ON / Off 'Digital Experience' in ZApp - Connectvity")

        self.logger_obj.info("Checking status on ZIA")
        result = self.zcc.verify_service_status(service_type="ZIA", service_status="off", sleep=5)

        if result[0]:
            self.logger_obj.info("Turning ON ZIA before running the testcase")
            result = self.zcc.toggle_service(service="ZIA", action=True)
            assert result[0], result[1]

        self.logger_obj.info("Changing tunnel Mode to None")
        result = self.forwarding_profile.edit_forwarding_profile(tunnel_mode='None')
        assert result[0], result[1]

        self.logger_obj.info("Update Policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.logger_obj.info("Verifying ZIA should show disabled")
        result = self.zcc.verify_service_status(service_type="ZIA", service_status="disable")
        assert result[0], result[1]

        result = self.verify_turn_onn_off_zdx()
        assert result[0], result[1]

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Deletion of forward profile and app profile
        2. Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

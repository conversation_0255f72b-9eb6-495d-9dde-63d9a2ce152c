import json,os
import time

import pytest
from common_lib.mobileadmin.deviceoverview import DeviceOverview
from common_lib.adminzia.admingroupuser import AdminGroupUser
from common_lib.mobileadmin.appprofile import AppProfile
from common_lib.mobileadmin.forwardingprofile import ForwardingProfile
from common_lib.adminzia.ziahelper import *
from OS_Android.library.ui_zcc import Zcc
from common_lib.common import logger,constants
from common_lib.common.log_ops import *
from OS_Android.library.ops_system import SysOps
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
from common_lib.mobileadmin.trustednetwork import TrustedNetwork
from common_lib.mobileadmin.serviceentitlement import ServiceEntitlement
from OS_Android.library.database_parse import <PERSON>b<PERSON>andler
from common_lib.helper_function import help_return
from OS_Android.library.database_parse import LogHandler
from OS_Android.library.zdx_admin import ZDX_ADMIN
from common_lib.mobileadmin.clientconnectorsupport import ClientConnectorSupport


config_path = os.path.join(os.getcwd(),"config", os.environ.get("CONFIG_FILE"))

with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")

device_overview=DeviceOverview(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)

const_obj=constants.Utils()

sys_ops=SysOps(logger_obj,variables["CLOUD"])

export_logs=FetchAndroidLogs(logger_obj)

create_zia_user=AdminGroupUser(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)

zia_activate=ZiaHelper(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)

log_ops=LogOps(log_handle=logger_obj)

pcap_ops=PcapOps(log_handle=logger_obj)


app_profile_obj = AppProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)

forwarding_profile_obj=ForwardingProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
client_connector = ClientConnectorSupport(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)
service_ent = ServiceEntitlement(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
zdx_admin = ZDX_ADMIN(cloudname=variables["CLOUD_DETAILS"]["ZDX"], configfile=variables,
                                   logger=logger_obj)

zia_application = variables["ZDX_ADMIN"]["ZDX_APPLICATION"][0]
zpa_application = variables["ZDX_ADMIN"]["ZDX_APPLICATION"][1]
zia_traceroute_domain = zia_application["tracert_icmp"]["host"]
zia_webprobe_url = zia_application["web_probe"]["url"]
zpa_traceroute_domain = zpa_application["tracert_icmp"]["host"]
zpa_webprobe_url = zpa_application["web_probe"]["url"]

db_handler = DbHandler()
log_handler = LogHandler()

@allure.title("Base teardown")
def make_teardown():
    """
    1. Delete App and Forwarding profile
    2. Setting privacy Info
    """

    logger_obj.info("Deleting Application and monitors for zdx_regression/zdx_misc")
    zdx_admin.delete_application_and_monitors()

    logger_obj.info("Deleting App profile for zdx_regression/zdx_misc")
    try:
        result = app_profile_obj.delete_app_profile(operating_system="Android")
        assert result[0], result[1]
    except Exception as e:
        logger_obj.info(str(e))

    logger_obj.info("Deleting Forwarding profile for zdx_regression/zdx_misc")
    try:
        result = forwarding_profile_obj.delete_forwarding_profile()
        assert result[0], result[1]
    except Exception as e:
        logger_obj.info(str(e))


@pytest.fixture(scope="package", autouse=True)
def base_setup(request):

    try:
        app_profile = app_profile_obj
        forwarding_profile = forwarding_profile_obj

        logger_obj.info("Deleting App profile")
        try:
            result = app_profile.delete_app_profile(operating_system="Android")
            assert result[0], result[1]
        except Exception as e:
            logger_obj.info(str(e))

        logger_obj.info("Deleting Forwarding profile")
        try:
            result = forwarding_profile.delete_forwarding_profile()
            assert result[0], result[1]
        except Exception as e:
            logger_obj.info(str(e))

        result = forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        result = app_profile.create_app_profile(operating_system="Android")
        assert result[0], result[1]

        logger_obj.info("Creating webmonitor and traceroute for zdx_regression/zdx_misc")
        zdx_admin.create_application_and_monitors(follow_web_monitor=False)

        result = service_ent.toggle_zdx(action=True)
        assert result[0], result[1]

        result = service_ent.toggle_zpa(action=True)
        assert result[0], result[1]

    except AssertionError as e:
        request.addfinalizer(make_teardown)
        pytest.skip(f"setup failed reason:{e}")
    else:
        request.addfinalizer(make_teardown)

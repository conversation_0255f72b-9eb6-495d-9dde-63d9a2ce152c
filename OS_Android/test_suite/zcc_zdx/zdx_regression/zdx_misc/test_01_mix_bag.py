import time, os
import pytest, allure
import json, re
from OS_Android.test_suite.zcc_zdx.zdx_regression.zdx_misc import conftest
from common_lib.Custom_Markers import add_markers
from common_lib.helper_function import help_return
from OS_Android.library.ui_zcc import Zcc
from OS_Android.library.zdx_admin import ZDX_ADMIN

first_export_after_logout = True
first_check_after_clear_zdx_data = True
zia_traceroute = conftest.zia_traceroute_domain

log_db_presence_input = [("upm", 0), ("web", 0), ("traceroute", 0), ("deviceStats", 0),
                         ("deviceProfile", 1), ("uploadStats", 1), ("web", 1),
                         ("traceroute", 1), ("deviceStats", 1)
                         ]
db_inputs = ["upm_device_profile.db",
             "upm_upload_stats.db",
             "upm_webload.db",
             "upm_traceroute.db",
             "upm_device_stats.db"
             ]

clear_zdx_data_input = [("upm_device_profile.db", 5),
                        ("upm_upload_stats.db", 0),
                        ("upm_webload.db", 1),
                        ("upm_traceroute.db", 1),
                        ("upm_device_stats.db", 7)
                        ]

file_name = {'upm': 'ZSAUpm_',
             'web': 'ZSAUpm_ZWebload',
             'traceroute': 'ZSAUpm_ZTraceroute',
             'deviceStats': 'ZSAUpm_DeviceStats'
             }


class TestMixBag:

    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.service_ent = conftest.service_ent
        self.logger_obj = conftest.logger_obj
        self.zdx_admin = conftest.zdx_admin
        self.variables = conftest.variables
        self.zdx_admin2 = ZDX_ADMIN(cloudname=self.variables["CLOUD_DETAILS"]["ZDX"], configfile=self.variables,
                                    logger=self.logger_obj)
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_traceroute.db"
        self.zcc = Zcc(log_handle=self.logger_obj, start_app=True)

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_type="ZPA", service_status="on")
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_type="ZDX", service_status="on")
        assert result[0], result[1]

        self.zcc.logger.info("Sleep for 3 mins to Collect ZDX data")
        time.sleep(180)

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.zcc.logger.info("Creating traceroute db connection")
        self.db_handler.create_db_connection(self.db_path)

        self.zcc.logger.info("Fetching Traceroute db data for testcase")
        query = f"SELECT * from trmain WHERE domain='{zia_traceroute}'"
        self.rows_data = self.db_handler.execute_query(query)

        self.logger_obj.info("Closing db connection after traceroute data Fetch")
        self.db_handler.close_db_connection()

    # Helper Function
    def verify_upm_policy_change_on_app_creation_deletion(self):
        """
        Verifies whether a new policy with a new policy version is received after changes are made in the monitors from the ZDX Admin.

        This function performs the following steps:
        1. Retrieves the current policy JSON from the latest ZSAUpm log file.
        2. Logs the current policy versions.
        3. Creates an application and monitors in the ZDX Admin to trigger a policy update.
        4. Polls every 30 seconds to check for a new policy version.
        5. If a new policy version is found before the 300-second timeout, it returns success.
        6. If the timeout is reached without detecting a new policy version, it returns failure.

        Returns:
            tuple: A tuple containing three elements:
                - (bool): `True` if a new policy with a new version was detected, otherwise `False`.
                - (str): A message describing the outcome, either success or failure.
                - (str): A status message indicating the result, either "SUCCESS" or "FAILURE".
        """
        self.logger_obj.info("\n---------------------------- UPM: Verify Policy Change ----------------------------\n")
        Issues = []

        # Step 1: Retrieve the current policy JSON data
        try:
            status, msg, policyList = self.log_handler.get_regular_policy_json_from_log(path=self.const_obj.LOG_PATH)
            if len(policyList) == 0 or not status:
                error_message = "Policy JSON not found in the ZSAUpm log file"
                self.logger_obj.error(error_message)
                Issues.append(error_message)
                return (False, Issues, "FAILURE")
        except Exception as e:
            error_message = f"Error retrieving policy JSON from log: {str(e)}"
            self.logger_obj.error(error_message)
            Issues.append(error_message)
            return (False, Issues, "FAILURE")

        # Step 2: Extract policy versions from the policy list
        try:
            if not all('policy' in item and 'version' in item['policy'] for item in policyList):
                raise KeyError("Missing 'policy' or 'version' key in one or more policy items.")
            PolicyVersions = [item['policy']['version'] for item in policyList]
            self.logger_obj.info(f"Policy Versions Before Creating App: {PolicyVersions}")
            currentPolicyVersion = PolicyVersions[-1]  # Current policy version before app creation
        except KeyError as e:
            error_message = f"Error extracting policy versions: Missing key in policy JSON - {str(e)}"
            self.logger_obj.error(error_message)
            Issues.append(error_message)
            return (False, Issues, "FAILURE")
        except Exception as e:
            error_message = f"Unexpected error during policy extraction: {str(e)}"
            self.logger_obj.error(error_message)
            Issues.append(error_message)
            return (False, Issues, "FAILURE")

        # Step 3: Log creation of the app to trigger a policy change
        self.logger_obj.info("Verify Policy Change :: Creating an app to trigger a new policy")

        retry_count = 3
        for attempt in range(retry_count):
            try:

                self.zdx_admin2.create_application_and_monitors_support_monitor_dropped(configs=self.mix_bag_json)
                self.logger_obj.info("Verify Policy Change :: App Created")

                # Wait for a short period after app creation
                time.sleep(30)

                self.zdx_admin2.delete_application_and_monitors(configs=self.mix_bag_json)
                self.logger_obj.info("Verify Policy Change :: App Deleted")
                break
            except Exception as e:
                self.logger_obj.error(f"Attempt {attempt + 1} failed during app creation or deletion: {str(e)}")
                if attempt == retry_count - 1:
                    error_message = f"All attempts failed during app creation or deletion: {str(e)}"
                    self.logger_obj.error(error_message)
                    Issues.append(error_message)
                    return (False, Issues, "FAILURE")
                time.sleep(10)  # Retry after a short delay

        # Step 4: Polling for the policy change with a 300-second timeout
        start_time = time.time()
        while time.time() - start_time < 300:
            self.logger_obj.info("Waiting for policy change to reflect in logs...")

            # Poll every 30 seconds
            time.sleep(30)

            # Step 5: Retrieve the updated policy JSON data after app creation and deletion
            try:
                time.sleep(3)

                # Exporting logs

                self.zcc.logger.info("Fetch logs from android device")
                result = self.zcc.export_logs()
                assert result[0], result[1]

                self.zcc.logger.info("Export logs from android device")
                result = self.export_logs.read_export_log_mail()
                assert result[0], result[1]

                status, msg, updatedPolicyList = self.log_handler.get_regular_policy_json_from_log(
                    path=self.const_obj.LOG_PATH)
                if len(updatedPolicyList) == 0 or not status:
                    error_message = "Updated Policy JSON not found in the ZSAUpm log file"
                    self.logger_obj.error(error_message)
                    Issues.append(error_message)
                    return (False, Issues, "FAILURE")
            except Exception as e:
                error_message = f"Error retrieving updated policy JSON from log: {str(e)}"
                self.logger_obj.error(error_message)
                Issues.append(error_message)
                return (False, Issues, "FAILURE")

            # Step 6: Extract the updated policy versions from the updated policy list
            try:
                if not all('policy' in item and 'version' in item['policy'] for item in updatedPolicyList):
                    raise KeyError("Missing 'policy' or 'version' key in updated policy JSON.")
                updatedPolicyVersion = [item['policy']['version'] for item in updatedPolicyList]
                self.logger_obj.info(f"Policy Versions After Creating App: {updatedPolicyVersion}")
                latestPolicyVersion = updatedPolicyVersion[-1]  # Latest policy version after app creation/deletion
            except KeyError as e:
                error_message = f"Error extracting updated policy versions: Missing key in updated policy JSON - {str(e)}"
                self.logger_obj.error(error_message)
                Issues.append(error_message)
                return (False, Issues, "FAILURE")
            except Exception as e:
                error_message = f"Unexpected error during updated policy extraction: {str(e)}"
                self.logger_obj.error(error_message)
                Issues.append(error_message)
                return (False, Issues, "FAILURE")

            # Step 7: Compare the current and latest policy versions
            if currentPolicyVersion != latestPolicyVersion:
                self.logger_obj.info(
                    f"SUCCESS :: New policy found as new policy version = {latestPolicyVersion} detected.")
                return (
                True, f"SUCCESS :: New policy found as new policy version = {latestPolicyVersion} detected.", "SUCCESS")

        # Timeout reached without detecting a new policy
        error_message = "Timeout (5 mins) reached without detecting a new policy. Please try again after waiting longer."
        self.logger_obj.error(error_message)
        Issues.append(error_message)

        if len(Issues) > 0:
            return (False, Issues, "FAILURE")

        return (True, "New policy detected", "SUCCESS")

    @allure.title("Verify follows_web set to False if the pathMonId is non zero - Tunnel None - App Not Bypassed")
    @pytest.mark.xray("QA-283282")
    @add_markers("regression")
    def test_zia_traceroute_follow_web_monitor_false_json_from_db(self):
        """
             This function verifies follow_web_monitor field from traceroute json

             It performs the following steps:
             1. It is validating 'mon', 'follows_web'
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        currentrow = 0
        row_data = self.rows_data
        flag = True
        msg = ""

        self.logger_obj.info(
            "Verify follows_web set to False if the pathMonId is zero - Tunnel None - App Not Bypassed")

        if len(row_data) == 0:
            flag = False
            msg = "No zia traceroute data found in traceroute.db"
            assert flag, msg

        for row in row_data:
            currentrow += 1
            data = ""
            json_data = ""
            data = data + row['json']

            if row['status'] == 'Traceroute Stopped':
                continue

            self.logger_obj.info(f"Validating JSON format for traceroute in row = {currentrow}")
            if "app" and "id" not in data:
                flag = False
                msg = f"Invalid JSON format in traceroute json in row = {currentrow}"
                break

            if "mon" and "follows_web" not in data:
                flag = False
                msg = f"'mon' and 'follows_web' field is not present in traceroute json in row = {currentrow}"
                break

            try:
                json_data = json.loads(data)
            except json.JSONDecodeError as e:
                data = re.sub(r'([{,]\s*?"[^"]+"\s*:)\s*,', r'\1 null,', data)
                json_data = json.loads(data)  # {'A': 'a', 'B': None, 'C': 'c'}

            self.logger_obj.info(f"Validating 'follow_web_monitor' from the json for traceroute = {currentrow}")
            if len(str(json_data['mon']['follows_web'])) == 0 or json_data['mon']['follows_web'] is None:
                flag = False
                msg = f"In row = {currentrow} under 'JSON' column, No 'follows_web' value is not present"
                break
            elif json_data['mon']['follows_web'] != 0:
                val = json_data['mon']['follows_web']
                flag = False
                msg = (f"In row = {currentrow} under 'JSON' column, 'follows_web' value is {val} which is incorrect,"
                       f"It should be equal to 0")

            if flag:
                self.logger_obj.info(f"############   Validated correctness of 'follows_web' data for traceroute {currentrow}  ##############")
            else:
                break

        assert flag, msg

    @allure.title("Verify Logs/DBs files presence of all the upm plugins from the exported logs")
    @pytest.mark.xray(["QA-149314", "QA-149444", "QA-149327", "QA-149279","QA-149303","QA-149318"])
    @add_markers("regression")
    @pytest.mark.parametrize("plugin,type", log_db_presence_input)
    def test_log_db_presence_in_exported_logs(self, plugin, type):
        """
             This function verifies the presence of given log/db file presence

             It performs the following steps:
             1. It will check upm logs and db files from exported logs
                Args:
                    self (object): Instance of the test class.
                    plugin (str): Plugin name. Ex - Webprobe, Traceroute etc.
                    type (int) : 0 - Log type, 1 - db type
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        status = True
        msg = ""
        file_type = {0: 'LOG', 1: 'DB'}
        logs_files = {'upm': 'ZSAUpm_', 'web': 'ZSAUpm_ZWebload', 'traceroute': 'ZSAUpm_ZTraceroute',
                      'deviceStats': 'ZSAUpm_DeviceStats'}

        if type == 0:
            self.zcc.logger.info(f"Verifying presence of {logs_files[plugin]} {file_type[type]} file in exported logs")
            zcc_log_files = self.log_handler.get_latest_logfile(self.const_obj.LOG_PATH)
            if len(zcc_log_files[plugin]) == 0:
                status = False
                msg = f"{logs_files[plugin]} {file_type[type]} file is not present in exported logs"

        if type == 1:
            self.zcc.logger.info(f"Verifying presence of {plugin} {file_type[type]} file in exported logs")
            zcc_db_files = self.db_handler.get_latest_dbfile(self.const_obj.LOG_PATH)
            if len(zcc_db_files[plugin]) == 0:
                status = False
                msg = f"{plugin} {file_type[type]} file is not present in exported logs"

        assert status, msg

    @allure.title("Verify ZDX Service Restart - UPM should stop and restart all plugins when users restart ZDX.")
    @pytest.mark.xray("QA-149434")
    @add_markers("regression")
    def test_all_upm_plugin_restart_on_zdx_restart(self):
        """
             This function Verify ZDX Service Restart

             It performs the following steps:
             1. It will validate UPM should stop and restart all plugins when users restart ZDX
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info("Verify ZDX Service Restart - UPM should stop and restart all plugins")
        stop_log_lines_to_search = ["ZPD: Exiting UPM policy downloader thread",
                                    "ZMS: Scheduler Stop called",
                                    "ZPC: Stop plugins called",
                                    "ZPC: Device Stats Plugin stopped successfully",
                                    "ZPC: Webprobe Plugin stopped successfully",
                                    "ZPC: Traceroute Plugin stopped successfully",
                                    "ZSU: Uploader Stopped",
                                    "ZUS: Setting upm service state to: 0!"
                                   ]
        start_log_lines_to_search = ["ZSU: Uploader started",
                                     "ZTCM: createTPGSession: policy download succeeded with host",
                                     "ZPC: Start Regular Sessions: In",
                                     "ZPC: Traceroute Plugin regular session started successfully",
                                     "ZPC: Webprobe Plugin regular session started successfully",
                                     "ZPC: Device Stats Plugin regular session started successfully"
                                    ]
        status = True
        msg = ""

        self.zcc.logger.info("Restart ZDX Service")
        result = self.zcc.restart_zdx_service()
        assert result[0], result[1]

        self.zcc.logger.info("Sleep for 2mins to zdx data")
        time.sleep(120)

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        latest_upm_file = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['upm'][-1]
        second_latest_upm_file = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['upm'][-2]

        self.zcc.logger.info("Validating all the plugin stop on ZDX Turn OFF during restart ZDX")
        for line in stop_log_lines_to_search:
            result = help_return(self.log_obj.search_log_file, file=second_latest_upm_file,start_line = "ZUS: Setting upm service state to: 8!",
                                 words_to_find_in_line=line, directory=self.const_obj.LOG_PATH)
            if not result[0]:
                status = False
                msg = result[1]
                assert False, msg
            else:
                self.logger_obj.info(f"Plugin Stop Found in logs [ {stop_log_lines_to_search} ]")

        self.zcc.logger.info("Validating all the plugin start on ZDX Turn ON after restart ZDX")
        for line in start_log_lines_to_search:
            result = help_return(self.log_obj.search_log_file, file=latest_upm_file,
                                 start_line="ZUS: Setting upm service state to: 2",
                                 words_to_find_in_line=line, directory=self.const_obj.LOG_PATH)
            if not result[0]:
                status = False
                msg = result[1]
                break
            else:
                self.logger_obj.info(f"Plugin Start Found in logs [ {stop_log_lines_to_search} ]")

        assert status, msg
        self.zcc.logger.info("##########  Success Validated all the plugin stop on Tun OFF and start on ZDX Turn ON during restart ZDX ############")

    @allure.title("Verify that all the ZDX related log files (both compressed and"
                  " uncompressed) are included in the exported zip file")
    @pytest.mark.xray("QA-149357")
    @add_markers("regression")
    def test_export_logs_included_zip_and_unzip_file(self):
        """
             Verify that all the ZDX related log files (both compressed and uncompressed) are
              included in the exported zip file
             It performs the following steps:
             1. It is validating both zip and unzip files are present in export logs
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        status = True

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail(by_default_unzip=False)
        assert result[0], result[1]


        files_to_search = ["upm", "web", "deviceStats", "traceroute"]
        exported_log_files = os.listdir(self.const_obj.LOG_PATH)

        for plugin in files_to_search:
            found_zip = False
            found_unzip = False
            for file in exported_log_files:
                if file_name[plugin] in file:
                    if ".zip" in file:
                        found_zip = True
                    else:
                        found_unzip = True

            if not found_zip:
                assert False, f"Zip file is not present for plugin = {plugin}"
            else:
                self.logger_obj.info(f"Zip file found for plugin = {plugin}")
            if not found_unzip:
                assert False, f"UnZip file is not present for plugin = {plugin}"
            else:
                self.logger_obj.info(f"Unzip file found for plugin = {plugin} \n")

        assert status, "File found"

    @allure.title("UploadStatsDB: Verify device Profile Sent to TPG when user User restart ZDX service")
    @pytest.mark.xray("QA-151971")
    @add_markers("regression")
    def test_device_profile_upload_to_tpg_when_zdx_restart(self):
        """
             This function checks whether the device profile data is being uploaded to tpg when user Restarted zdx
             It performs the following steps:
             1. It is validating device profile data from upload data json
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        flag = True
        msg = "No data found in upload stats json for device profile"

        self.zcc.logger.info("UploadStatsDB: Verify device Profile Sent to TPG when user login to ZCC")
        db_path = self.const_obj.LOG_PATH + f'\\upm_upload_stats.db'

        try:
            query = "SELECT * from upload_data"
            self.db_handler.create_db_connection(db_path)
            rows_data = self.db_handler.execute_query(query)
            self.db_handler.close_db_connection()

            timestamp = rows_data[0][0]
            status = rows_data[0][1]
            upload_json = json.loads(rows_data[0][2])
            device_profile = upload_json['profile']

            self.zcc.logger.info("Validating if upload json data is available or not after restart zdx")
            if upload_json == '' or len(str(upload_json)) <= 20:
                flag = False
                msg = "Upload_json data is not available at 1st row which is incorrect after restart zdx"

            self.zcc.logger.info(
                "Validating if device profile data is available or not in upload_stats json after restart zdx")
            if flag and (device_profile == '' or len(device_profile) == 0):
                flag = False
                msg = "Failed to send device profile data to the tpg, check 1st row data of upload stats json "

        except Exception as e:
            flag = False
            msg = f"Some error occured while looking for device profile data after restart zdx {e}"

        assert flag, msg

    @allure.title("Verify that 'Update Policy' is not interfering with the UPM service")
    @pytest.mark.xray(["QA-149280","QA-149309"])
    @add_markers("regression")
    def test_update_policy_not_interfering_upm_service(self):
        """
             Verify that 'Update Policy' is not interfering with the UPM service
             1. When user click on the 'Update Policy', zapp should only check for and download new policy from
                Mobile Admin, and the UPM service should be staying ON through-out without any interruption.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        flag = True
        msg = ""

        try:
            self.logger_obj.info("Clearing Notifications")
            result = self.zcc.clear_notification()
            assert result[0], result[1]
            self.logger_obj.info("Notifications successfully cleared \n")

            self.logger_obj.info("Update Policy")
            result = self.zcc.update_policy()
            assert result[0], result[1]

            self.logger_obj.info("Verifying during update policy, there shouldn't be any zdx restart")
            result = self.zcc.verify_no_restart_service_notification(service="ZDX")
            assert result[0], result[1]
            self.logger_obj.info("Successfully verified \n")

        except Exception as e:
            flag = False
            msg = f"Some error occured while checking for no ZDX restart = \n {e}"

        assert flag, msg

    @allure.title("Verify ZDX restart do not interrupt ZIA and ZPA functionalities")
    @pytest.mark.xray("QA-149261")
    @add_markers("regression")
    def test_zdx_restart_dont_interrupt_zia_and_zpa(self):
        """
             Verify that 'Restart ZDX' is not interfering with the ZIA and ZPA
             1. When user click on the 'Restart ZDX', zapp should only restart zdx and not zia and zpa.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        flag = True
        msg = ""

        try:
            self.zcc.logger.info("Restart zdx service")
            result = self.zcc.restart_zdx_service()
            assert result[0], result[1]
            time.sleep(10)

            self.zcc.logger.info("check for zdx on status")
            result = self.zcc.verify_service_status(service_type="ZDX",service_status="on")
            assert result[0], result[1]

            result = self.zcc.verify_no_restart_service_notification(service="ZPA")
            assert result[0], result[1]

            result = self.zcc.verify_no_restart_service_notification(service="ZIA")
            assert result[0], result[1]

        except Exception as e:
            flag = False
            msg = f"Some error occured while checking for no restart of service= \n {e}"

        assert flag, msg

    @allure.title("Verify ZDX Turn ON/OFF do not interrupt ZIA and ZPA functionalities")
    @pytest.mark.xray("QA-149262")
    @add_markers("regression")
    def test_zdx_turn_on_off_dont_interrupt_zia_and_zpa(self):
        """
             Verify that 'ZDX Turn ON/OFF' is not interfering with the ZIA and ZPA
             1. When user click on the 'ZDX Turn ON/OFF', zapp should only restart zdx and not zia and zpa.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        flag = True
        msg = ""

        try:
            self.logger_obj.info("Clearing Notifications")
            result = self.zcc.clear_notification()
            assert result[0], result[1]
            self.logger_obj.info("Notifications successfully cleared \n")

            self.zcc.logger.info("Turn OFF ZDX ")
            result = self.zcc.toggle_service(service="ZDX", action=False)
            assert result[0], result[1]

            time.sleep(2)

            self.zcc.logger.info("Turn ON ZDX ")
            result = self.zcc.toggle_service(service="ZDX", action=True)
            assert result[0], result[1]

            self.zcc.logger.info("check for zdx on status")
            result = self.zcc.verify_service_status(service_type="ZDX",service_status="on")
            assert result[0], result[1]

            result = self.zcc.verify_no_restart_service_notification(service="ZPA")
            assert result[0], result[1]

            result = self.zcc.verify_no_restart_service_notification(service="ZIA")
            assert result[0], result[1]

        except Exception as e:
            flag = False
            msg = f"Some error occured while checking for no restart of service= \n {e}"

        assert flag, msg

    @allure.title('Test UPM Policy Change on App Creation/Deletion')
    @pytest.mark.xray("QA-149382")
    @pytest.mark.skip("Needs Rework, will update in some other PR")
    @add_markers("regression")
    def test_upm_policy_change_on_app_creation_deletion(self):
        status, data, msg = self.verify_upm_policy_change_on_app_creation_deletion()

        if not status:
            if data:
                error_message = f"Policy change verification failed: {str(data)}"
                assert False, error_message
            else:
                error_message = "UPM Policy change verification failed without detailed data."
                assert False, error_message



    @allure.title("Clear ZDX data: Verify all the zdx db data got cleared after clicking clear zdx data from zcc ui")
    @pytest.mark.xray("QA-149313")
    @pytest.mark.parametrize("db, data_buffer", clear_zdx_data_input)
    @add_markers("regression")
    def test_clear_zdx_db_data_from_zcc_ui(self, db, data_buffer):
        """
             This function Verify logout functionality in ZDX deletes all the contents in the upm_device_stats.db
             It performs the following steps:
             1. It is verifying logout will clear all zdx db data
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        global first_check_after_clear_zdx_data
        db_path = self.const_obj.LOG_PATH + f'\\{db}'
        status = True
        msg = f"ZDX data not cleared for {db} after clicking clear zdx data from zcc UI"

        if first_check_after_clear_zdx_data:
            self.zcc.logger.info("Clearing ZDX data")
            result = self.zcc.clear_zdx_data()
            assert result[0], result[1]

            self.zcc.logger.info("Fetch logs from android device")
            result = self.zcc.export_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Export logs from android device")
            result = self.export_logs.read_export_log_mail()
            assert result[0], result[1]

        try:
            self.db_handler.create_db_connection(db_path)

            query = "SELECT name FROM sqlite_master WHERE type='table';"
            tables = self.db_handler.execute_query(query)
            total_record_count = 0

            for table in tables:
                table_name = table[0]
                count_query = f"SELECT DISTINCT COUNT(*) FROM {table_name};"
                record_count = self.db_handler.execute_query(count_query)[0][0]
                total_record_count += record_count

            if total_record_count > data_buffer:
                status = False

        except Exception as e:
            status = False
            msg = f"Some error occurred in {db} while clearing ZDX data after clicking clear zdx {e}"

        self.db_handler.close_db_connection()
        first_check_after_clear_zdx_data = False

        self.zcc.logger.info(f"Verifying zdx data cleared for {db} on clicking clear zdx data from zcc ui")
        assert status, msg

    @allure.title("Verify that the 'Clear Logs' button is clearing only the log files but not the db files , "
                  "Verify that 'Clear Logs' functionality is clearing the ZDX Logs as well along with other ZApp logs")
    @pytest.mark.xray(["QA-149317","QA-149306", "QA-149430"])
    @add_markers("regression")
    def test_clear_logs(self):
        """
        This function tests the validation of clear log and verify db logs.
        It performs the following steps:
        1. Checking db file present after log clear
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        dict1 = self.log_handler.get_latest_logfile(self.const_obj.LOG_PATH)
        self.zcc.logger.info("Count Total upm log files - UPM, DeviceStats, Traceroute, Webload")
        for key, value in dict1.items():
            if key in ['upm','deviceStats','traceroute','web']:
                self.logger_obj.info(f"Total {len(value)} file present for given Plugin {key}")


        self.zcc.logger.info("Clear logs for zapp")
        self.zcc.clear_logs()

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        dict2 = self.log_handler.get_latest_logfile(self.const_obj.LOG_PATH)
        self.zcc.logger.info("Count Total upm log files present after Clear Logs- UPM, DeviceStats, Traceroute, Webload")
        for key, value in dict2.items():
            if key in ['upm', 'deviceStats', 'traceroute', 'web']:
                if len(value) !=1:
                    assert False, f"Total file present for {key} is {len(value)}, it should be 1"
                self.logger_obj.info(f"Total {len(value)} file present for given {key} Plugin after Clear Logs, which is correct")

    @allure.title("Verify that 'Restart Service' is not interfering with the UPM service")
    @pytest.mark.xray("QA-149304")
    @add_markers("regression")
    def test_restart_service(self):
        """
        This function tests the validation of Restart Service UPM service
        It performs the following steps:
        1. checking restart service not restarting ZDX
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info("Restart service")
        result = self.zcc.restart_service()
        assert result[0], result[1]
        time.sleep(10)

        self.zcc.logger.info("check for zdx on status")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="on")
        assert result[0], result[1]

        result  = self.zcc.verify_no_restart_service_notification(service="ZDX")
        assert result[0], result[1]

    @allure.title("ZDX Disabled from MA - When user login to ZCC, 'Digital Experience' tab should not be shown on ZCC")
    @pytest.mark.xray("QA-58327")
    @add_markers("regression")
    def test_disable_zdx(self):
        """
        This function tests the validation of ZDX Disabled.
        It performs the following steps:
        1. Checking ZDX tab is not present
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info("Disable zdx service from MA")
        result = self.service_ent.toggle_zdx(action=False)
        assert result[0], result[1]

        self.zcc.logger.info("update policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Check status of service")
        result = self.zcc.service_ent_status(current_status='Disable')
        assert result[0], result[1]

    @allure.title("ZDX Enabled from MA - when user login to ZCC, 'Digital Experience' tab should be shown on ZCC")
    @pytest.mark.xray("QA-58327")
    @add_markers("regression")
    def test_enable_zdx(self):
        """
        This function tests the validation of zdx enablement tgp_upm.
        It performs the following steps:
        1. ZDX tab is present on zdx enable from MA
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        result =  self.service_ent.toggle_zdx(action=True)
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        result = self.zcc.service_ent_status(current_status='Enable')
        assert result[0], result[1]

    @allure.title("Logout: Verify all the zdx db data got cleared after logout")
    @pytest.mark.xray("QA-149446")
    @pytest.mark.parametrize("db", db_inputs)
    @add_markers("regression")
    def test_clear_zdx_db_data_after_logout(self, db):
        """
             This function Verify logout functionality in ZDX deletes all the contents in the upm_device_stats.db
             It performs the following steps:
             1. It is verifying logout will clear all zdx db data
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        global first_export_after_logout
        db_path = self.const_obj.LOG_PATH + f'\\{db}'
        status = False
        msg = f"ZDX data nor cleared for {db} on logout"

        if first_export_after_logout:
            self.zcc.logger.info("Sleep 3min to collect ZDX data")

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        self.zcc.logger.info("Sleep 1s before running next testcase")
        time.sleep(1)
        try:

            if first_export_after_logout:
                self.zcc.logger.info("Fetch logs from android device")
                result = self.zcc.export_logs()
                assert result[0], result[1]

                self.zcc.logger.info("Export logs from android device")
                result = self.export_logs.read_export_log_mail()
                assert result[0], result[1]

            self.db_handler.create_db_connection(db_path)

            query = "SELECT name FROM sqlite_master WHERE type='table';"
            tables = self.db_handler.execute_query(query)
            total_record_count = 0

            for table in tables:
                table_name = table[0]
                count_query = f"SELECT DISTINCT COUNT(*) FROM {table_name};"
                record_count = self.db_handler.execute_query(count_query)[0][0]
                total_record_count += record_count

            if total_record_count == 0:
                status = True

        except Exception as e:
            status = False
            msg = f"Some error occured while clearning ZDX data after logout {e}"

        self.db_handler.close_db_connection()
        first_export_after_logout = False

        self.zcc.logger.info(f"Verifying zdx data cleared for {db} on logout")
        assert status, msg

    @allure.title("Logout: Verify all upm Plugins should stop after logout ZCC")
    @pytest.mark.xray("QA-149445")
    @add_markers("regression")
    def test_all_zdx_plugin_stop_after_logout(self):
        """
             This function verify all upm plugin should stop after ' logout '
             It performs the following steps:
             1. It is verifying logout will clear all zdx db data
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info(f"Verifying all zdx plugin should stop on logout")

        status = True
        msg = ""
        latest_upm_file = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['upm'][-1]
        stop_log_lines_to_search = ["ZPD: Exiting UPM policy downloader thread",
                                    "ZMS: Scheduler Stop called",
                                    "ZPC: Stop plugins called",
                                    "ZPC: Device Stats Plugin stopped successfully",
                                    "ZPC: Webprobe Plugin stopped successfully",
                                    "ZPC: Traceroute Plugin stopped successfully",
                                    "ZSU: Uploader Stopped",
                                    "ZUS: Setting upm service state to: 0!"
                                    ]

        start_line = "ZPC: User logout. ZSAUpm Clear DB command received!"

        for line in stop_log_lines_to_search:
            result = help_return(self.log_obj.search_log_file, file=latest_upm_file,
                                 words_to_find_in_line=line, start_line=start_line, directory=self.const_obj.LOG_PATH)
            if not result[0]:
                status = False
                msg = f"{line} <<- This plugin didn't stop after logout"
                break

        assert status, msg

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Deletion of forward profile and app profile
        2. Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        result = self.zcc.service_ent_status(current_status='Enable')
        assert result[0], result[1]

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

import time
import pytest, allure
import json, re
from OS_Android.test_suite.zcc_zdx.zdx_regression.tunnel2_mode import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.traceroute_resource import traceroute_resource

zia_traceroute = conftest.zia_traceroute_domain
zpa_traceroute = conftest.zpa_traceroute_domain


class TestZiaTracerouteT2:

    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.service_ent = conftest.service_ent
        self.logger_obj = conftest.logger_obj
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_traceroute.db"
        self.traceroute_resource = traceroute_resource()

    @allure.title("Validate traceroute Probes: for Tunnel 2.0 for Regular session from traceroute logs")
    @add_markers("regression")
    def test_traceroute_probes(self):
        """
        Test to validate the presence and correctness of ZIA 1.0, ZIA 2.0, and ZPA traceroute probes
        by parsing the ZSAUpm_Traceroute logs. It ensures all expected fields are present
        and correct based on the probe type (Regular or LT).

        Args:
            service_type (int): Type of service to verify.
                                1 - ZIA 1.0
                                2 - ZIA 2.0
                                3 - ZPA
            is_lt (int): Specifies if the probe is an LT probe.
                        0 - Regular
                        1 - LT

        Raises:
            pytest.fail: If the probe validation fails due to missing or incorrect fields.
        """
        service_type = 2
        is_lt = 0
        status, msg = self.traceroute_resource.verify_traceroute_probes(service_type=service_type, is_lt=is_lt)

        if not status:
            self.logger_obj.error(f"Traceroute probe verification failed:\n\n{msg}")
            assert False, f"Traceroute Probe verification failed:\n\n{msg}"
        else:
            self.logger_obj.info(f"SUCCESS ::Traceroute Probe verification completed.")


    @allure.title("Validate traceroute attributes from upm_traceroute DB, isLT: {islt}")
    @add_markers("regression")
    def test_traceroute_columns(self):
        """
        Test to validate key performance-related columns in the 'trmain' or 'trltmain' table
        of the 'upm_traceroute' database.

        The columns checked include DNS time, availability, time-to-first-byte, total time,
        TCP time, HTTP latency, and page fetch duration. This ensures that all expected fields
        are present and correctly populated.

        Parameters:
            islt (int):
                Flag to determine which table to validate.
                - 0: Validates 'trmain' table.
                - 1: Validates 'trltmain' table.

        Fails the test if any column validation fails.
        """
        islt = 0
        table_name = 'trmain' if islt == 0 else 'trltmain'
        columns = ['appid', 'monid', 'domain', 'hopstodest', 'unresponsivehopcount', 'totalloss', 'totallatency', 'egress', 'protocol', 'mtunnelid', 'resolvedip', 'brokerResponse', 'SMEResponse']
        res, msg = self.traceroute_resource.verify_and_validate_traceroute_data(columns=columns,islt=islt,domain=zia_traceroute)

        if res:
            self.logger_obj.info(f"SUCCESS :: Column validation passed for table '{table_name}' with columns {columns} (islt={islt}).")
        else:
            self.logger_obj.error(f"FAILED :: Column validation failed for table '{table_name}' with columns {columns} (islt={islt}).")
            assert False, "Traceroute probe verification failed:\n\n" + msg

    @allure.title("ZIA TRACEROUTE TUNNEL2 MODE: Verify maxhopcount and hopstodest count from traceroute db")
    @add_markers("regression")
    def test_maxhopcount_hopstodest_unresponsivehopcount_from_traceroute_db(self):
        """
             This function verifies that in Traceroute probes the value of 'hopstodest'
             should not be greater than 'maxhopcount'. It also verifies that in Traceroute
             probes the value of 'unresponsivehopcount' should not be greater than 'hopstodest'

             It performs the following steps:
             1. It is validating maxhopcount, hopstodest and unresponsivehopcount values in traceroute db
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("ZIA TRACEROUTE TUNNEL MODE: Verifying that in Traceroute probes"
                             "the value of'hopstodest'should not be greater than 'maxhopcount'")

        self.db_handler.create_db_connection(self.db_path)
        query = f"SELECT * from trmain WHERE domain='{zia_traceroute}'"
        rows_data = self.db_handler.execute_query(query)
        current_row = 0
        complete_traceroute = False
        flag = True
        msg = ""

        if len(rows_data) == 0:
            flag = False
            msg = "No traceroute data found in traceroute.db"

        self.logger_obj.info("Checking if any zia traceroute data inserted to the db or not")
        assert flag, msg

        for data in rows_data:
            current_row += 1
            domain = data['domain']
            status = data['status']
            hopstodest = data['hopstodest']
            maxhopcount = data['maxhopcount']
            unresponsivehopcount = data['unresponsivehopcount']

            if status == 'Traceroute Stopped':
                continue

            if len(domain) == 0:
                flag = False
                msg = f"No value found at 'domain' column in row = {current_row}"

            if len(str(hopstodest)) == 0 or (len(str(hopstodest)) >= 1 and int(hopstodest) <= 0):
                flag = False
                msg = f"No or Invalid value found at 'hopstodest' in row = {current_row}"

            if len(str(maxhopcount)) == 0 or (len(str(maxhopcount)) >= 1 and int(maxhopcount) <= 0):
                flag = False
                msg = f"No or Invalid value found at maxhopcount in row = {current_row}"

            if len(str(unresponsivehopcount)) == 0 or (
                    len(str(unresponsivehopcount)) >= 1 and int(unresponsivehopcount) < 0):
                flag = False
                msg = f"No or Invalid value found at maxhopcount in row = {current_row}"

            if len(str(hopstodest)) >= 1 and len(str(maxhopcount)) >= 1 and int(hopstodest) > 40:
                flag = False
                msg = f"The value of 'hopstodest' = {hopstodest} is greater than 40 in row = {current_row} which in Invalid"

            if len(str(hopstodest)) >= 1 and len(str(maxhopcount)) >= 1 and (int(hopstodest) > int(maxhopcount)):
                flag = False
                msg = (f"The value of 'hopstodest' = {hopstodest} is greater than"
                       f"maxhopcount = {maxhopcount} in row = {current_row} which in Incorrect")

            if len(str(unresponsivehopcount)) >= 1 and len(str(hopstodest)) >= 1 and int(hopstodest) < int(
                    unresponsivehopcount):
                flag = False
                msg = f"The value of 'unresponsivehopcount' is greater than 'hopstodest' in row = {current_row} which is inalid"

            current_row += 1
            if flag:
                complete_traceroute = True
            else:
                break

        assert complete_traceroute, "No complete traceroute data found in traceroute db"

        self.logger_obj.info("Validating hopstodest should be less than maxhopcount and"
                             "'unresponsivehopcount' should be less than equal to 'hopstodest'")
        assert flag, msg

    @allure.title("ZIA TRACEROUTE TUNNEL2 MODE: Verify totalloss and totallatency from traceroute db")
    @add_markers("regression")
    def test_totalloss_totallatency_values_from_traceroute_db(self):
        """
             This function verifies that in Traceroute probes the value of 'totalloss'
             and 'totallatency' should not be invalid

             It performs the following steps:
             1. It is validating 'totalloss', and 'totallatency' values in traceroute db
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(
            "ZIA TRACEROUTE TUNNEL MODE: Verifying 'totalloss' and 'totallatency' values from traceroute DB")

        query = f"SELECT * from trmain WHERE domain='{zia_traceroute}'"
        rows_data = self.db_handler.execute_query(query)
        currentrow = 1
        flag = True
        complete_traceroute = False
        msg = ""

        if len(rows_data) == 0:
            flag = False
            msg = "No zia traceroute data found in traceroute.db"

        assert flag, msg

        for data in rows_data:
            status = data['status']
            domain = data['domain']
            totalloss = data['totalloss']
            totallatency = data['totallatency']

            if status == 'Traceroute Stopped':
                continue

            if len(str(totalloss)) == 0 or int(totalloss) < -1 or int(totalloss) >= 101:
                flag = False
                msg = f"No or invalid found at 'totalloss' = {totalloss} in row = {currentrow}"

            if len(str(totallatency)) == 0 or int(totallatency) < -1:
                flag = False
                msg = f"No or value found at 'totallatency' = {totallatency} in row = {currentrow}"

            if int(totallatency) >= 201:
                self.logger_obj.warning(f"In row = {currentrow}, the 'totallatency' value = {totallatency} is too high")

            if flag:
                complete_traceroute = True
                self.logger_obj.info(
                    f"------------------------------ Completed for Traceroute: {currentrow} -------------------------------")
            else:
                break

            currentrow += 1

        assert complete_traceroute, "No complete traceroute data found in traceroute db"

        self.logger_obj.info("Validating 'totalloss' and 'totallatency' values from traceroute db")
        assert flag, msg

    @allure.title("ZIA TRACEROUTE TUNNEL2 MODE: Verify traceroute probe json from traceroute db")
    @add_markers("regression")
    def test_zia_traceroute_probe_json_from_db(self):
        """
             This function verifies ZIA TR JSON of DIRECT case (ZIA none mode).

             It performs the following steps:
             1. It is validating 'app', 'id', 'hints'
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        currentrow = 0
        compelete_traceroute = False
        flag = True
        msg = ""

        self.logger_obj.info("ZIA TRACEROUTE NONE MODE: Verify traceroute probe json from traceroute db")

        query = f"SELECT * from trmain WHERE domain='{zia_traceroute}'"
        rows_data = self.db_handler.execute_query(query)

        if len(rows_data) == 0:
            flag = False
            msg = "No zia traceroute data found in traceroute.db"

        assert flag, msg

        for row in rows_data:
            currentrow += 1
            data = ""
            json_data = ""
            data = data + row['json']

            if row['status'] == 'Traceroute Stopped':
                continue

            self.logger_obj.info(f"Validating JSON format for traceroute in row = {currentrow}")
            if "app" and "id" not in data:
                flag = False
                msg = f"Invalid JSON format in traceroute json in row = {currentrow}"
                break

            try:
                json_data = json.loads(data)
            except json.JSONDecodeError as e:
                data = re.sub(r'([{,]\s*?"[^"]+"\s*:)\s*,', r'\1 null,', data)
                json_data = json.loads(data)  # {'A': 'a', 'B': None, 'C': 'c'}

            self.logger_obj.info(f"Validating 'hints' from the json for traceroute = {currentrow}")
            if len(json_data['hints']) == 0 or json_data['hints'] is None:
                flag = False
                msg = f"In row = {currentrow} under 'JSON' column, No legs are present in the 'Hints' section"
                break

            self.logger_obj.info(f"Validating 'client_name', 'clt_ha_name','clt_ip','ecode',"
                                 f"'icode', 'etime' fields in json for traceroute = {currentrow}")

            for i in json_data:
                if "client_name" in i or "clt_ha_name" in i or "ecode" in i or "erefid" in i:
                    if len(str(json_data[i])) == 0 or json_data[i] is None:
                        flag = False
                        msg = f"Invalid or no value in trace json for = {i} in row = {currentrow} of traceroute db"
                        break

                if "clt_ip" in i or "icode" in i or "is_zpa" in i or "etime" in i:
                    if len(str(json_data[i])) == 0 or json_data[i] is None:
                        flag = False
                        msg = f"Invalid or no value in trace json for = {i} in row = {currentrow}of traceroute db"
                        break

            if not flag:
                break

            data = ""
            traceroute_legs = json_data["legs"]
            first_leg_tunnel_type = traceroute_legs[0]['tunnel_type']

            if first_leg_tunnel_type not in [2, 14, 22]:
                flag = False
                msg = (f"ZPA traceroute is not working fine for tunnel1 as tunnel_type ="
                       f"{first_leg_tunnel_type} is showing incorrect value")
                break

            for leg in traceroute_legs:
                source = leg['src']
                destination = leg['dst']
                dst_ip = leg['dst_ip']
                ecode = leg['ecode']
                loss = leg['loss']
                proto = leg['proto']
                src_ip = leg['src_ip']
                tunnel_type = leg['tunnel_type']
                num_hops = leg['num_hops']
                num_unresp_hops = leg['num_unresp_hops']

                self.logger_obj.info("Validating 'src_ip' value in traceroute json")
                if src_ip is None or len(str(src_ip)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'src_ip' value is missing")
                    break

                self.logger_obj.info(f"Validating 'dts_ip' value from json for traceroute = {currentrow}")
                if len(str(dst_ip)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'dst_ip' value is missing")
                    break
                else:
                    match = re.match(r"[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}", dst_ip)
                    if not bool(match):
                        flag = False
                        msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                               f"'dst_ip' value is Invalid")
                        break

                self.logger_obj.info(f"Validating 'ecode' value from json for traceroute = {currentrow}")
                if ecode is None or len(str(ecode)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'ecode' value is missing")
                    break

                self.logger_obj.info(f"Validating 'loss' value from json for traceroute = {currentrow}")
                if loss is None or len(str(loss)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'loss' value is missing")
                    break

                self.logger_obj.info(f"Validating 'protocol' value from json for traceroute = {currentrow}")
                if proto is None or len(str(proto)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'proto' value is missing")
                    break

                self.logger_obj.info(f"Validating 'tunnel_type' value from json for traceroute = {currentrow}")
                if tunnel_type is None or len(str(tunnel_type)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'tunnel_type' value is missing")
                    break

                self.logger_obj.info(f"Validating 'num_hops' value from json for traceroute = {currentrow}")
                if num_hops is None or len(str(num_hops)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'nums_hops' value is missing")
                    break

                self.logger_obj.info(f"Validating 'num_unresp_hops' value from json for traceroute = {currentrow}")
                if num_unresp_hops is None or len(str(num_unresp_hops)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'nums_unresp_hops' value is missing")
                    break

            if flag:
                compelete_traceroute = True
                self.logger_obj.info(f"####################   Completed Verification for JSON from traceroute db for row = {currentrow}  ####################")
            else:
                break

        assert compelete_traceroute, "No Complete traceroute data found in the db"

        self.logger_obj.info("Validating correctness of full json data for all zia traceroute from the db")
        assert flag, msg

    @allure.title("Verify follows_web set to true if the pathMonId is non zero - Tunnel 2.0 - App Not Bypassed")
    @add_markers("regression")
    def test_zia_traceroute_follow_web_monitor_json_from_db(self):
        """
             This function verifies follow_web_monitor field from traceroute json

             It performs the following steps:
             1. It is validating 'mon', 'follows_web'
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        currentrow = 0
        compelete_traceroute = False
        flag = True
        msg = ""

        self.logger_obj.info(
            "Verify follows_web set to true if the pathMonId is non zero - Tunnel 2.0 - App Not Bypassed")

        query = f"SELECT * from trmain WHERE domain='{zia_traceroute}'"
        rows_data = self.db_handler.execute_query(query)

        if len(rows_data) == 0:
            flag = False
            msg = "No zia traceroute data found in traceroute.db"

        assert flag, msg

        for row in rows_data:
            currentrow += 1
            data = ""
            json_data = ""
            data = data + row['json']

            if row['status'] == 'Traceroute Stopped':
                continue

            self.logger_obj.info(f"Validating JSON format for traceroute in row = {currentrow}")
            if "app" and "id" not in data:
                flag = False
                msg = f"Invalid JSON format in traceroute json in row = {currentrow}"
                break

            if "mon" and "follows_web" not in data:
                flag = False
                msg = f"'mon' and 'follows_web' field is not present in traceroute json in row = {currentrow}"
                break

            try:
                json_data = json.loads(data)
            except json.JSONDecodeError as e:
                data = re.sub(r'([{,]\s*?"[^"]+"\s*:)\s*,', r'\1 null,', data)
                json_data = json.loads(data)  # {'A': 'a', 'B': None, 'C': 'c'}

            self.logger_obj.info(f"Validating 'follow_web_monitor' from the json for traceroute = {currentrow}")
            if len(str(json_data['mon']['follows_web'])) == 0 or json_data['mon']['follows_web'] is None:
                flag = False
                msg = f"In row = {currentrow} under 'JSON' column, No 'follows_web' value is not present"
                break
            elif json_data['mon']['follows_web'] != 1:
                val = json_data['mon']['follows_web']
                flag = False
                msg = (f"In row = {currentrow} under 'JSON' column, 'follows_web' value is {val} which is incorrect,"
                       f"It should be equal to 1")

            if flag:
                compelete_traceroute = True
                self.logger_obj.info(f"------------------------------ Completed for Traceroute: {currentrow} -------------------------------")
            else:
                break

        assert compelete_traceroute, "No Complete traceroute data found in the db"

        self.logger_obj.info("Validating correctness of 'follows_web' data for all zia traceroute from the db")
        assert flag, msg

    @allure.title("Android - Add SME IP and SME name in JSON")
    @add_markers("regression")
    def test_sme_ip_and_name_traceroute_probe_json_from_db(self):
        """
             This function verifies sme_ip and sme_name from traceroute json

             It performs the following steps:
             1. It is validating 'src_ip', 'name', from traceroute json
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        currentrow = 0
        compelete_traceroute = False
        flag = True
        msg = ""

        self.logger_obj.info("Android - Add SME IP and SME name in JSON")

        query = f"SELECT * from trmain WHERE domain='{zia_traceroute}'"
        rows_data = self.db_handler.execute_query(query)

        if len(rows_data) == 0:
            flag = False
            msg = "No zia traceroute data found in traceroute.db"

        assert flag, msg

        for row in rows_data:
            currentrow += 1
            data = ""
            json_data = ""
            data = data + row['json']

            if row['status'] == 'Traceroute Stopped':
                continue

            self.logger_obj.info(f"Validating JSON format for traceroute in row = {currentrow}")
            if "app" and "id" not in data:
                flag = False
                msg = f"Invalid JSON format in traceroute json in row = {currentrow}"
                break

            try:
                json_data = json.loads(data)
            except json.JSONDecodeError as e:
                data = re.sub(r'([{,]\s*?"[^"]+"\s*:)\s*,', r'\1 null,', data)
                json_data = json.loads(data)  # {'A': 'a', 'B': None, 'C': 'c'}

            self.logger_obj.info(f"Validating 'hints' from the json for traceroute = {currentrow}")
            if len(json_data['hints']) == 0 or json_data['hints'] is None:
                flag = False
                msg = f"In row = {currentrow} under 'JSON' column, No legs are present in the 'Hints' section"
                break

            self.logger_obj.info(f"Validating 'src_ip', 'name' fields in json for traceroute = {currentrow}")

            data = ""
            traceroute_legs = json_data["legs"]
            first_leg_tunnel_type = traceroute_legs[0]['tunnel_type']

            if first_leg_tunnel_type not in [3, 15, 23]:
                flag = False
                msg = (f"ZIA traceroute is not working fine for tunnel2 as tunnel_type ="
                       f"{first_leg_tunnel_type} is showing incorrect value")
                break

            for leg in traceroute_legs:
                source = leg['src']
                destination = leg['dst']
                src_ip = leg['src_ip']
                if not (source == "zen" and destination == "egress"):
                    continue

                name = leg['name']

                self.logger_obj.info(f"Validating 'src_ip' value from json for traceroute = {currentrow}")
                if len(str(src_ip)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'src_ip' value is missing")
                    break
                else:
                    match = re.match(r"[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}", src_ip)
                    if not bool(match):
                        flag = False
                        msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                               f"'src_ip' value is Invalid")
                        break

                self.logger_obj.info(f"Validating 'name' value from json for traceroute = {currentrow}")
                if name is None or len(str(name)) == 0:
                    flag = False
                    msg = (f"Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'name' value is missing")
                    break

            if flag:
                compelete_traceroute = True
                self.logger_obj.info(
                    f"------------------------------ Completed for Traceroute: {currentrow} -------------------------------")
            else:
                break

        assert compelete_traceroute, "No Complete traceroute data found in the db"

        self.logger_obj.info("Validating correctness of full json data for all zia traceroute from the db")
        assert flag, msg

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Deletion of forward profile and app profile
        2. Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """
        self.logger_obj.info("Closing db connection after verification")
        self.db_handler.close_db_connection()

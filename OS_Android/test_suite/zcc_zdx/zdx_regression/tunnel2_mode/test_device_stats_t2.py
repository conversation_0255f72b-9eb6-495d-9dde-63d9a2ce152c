import time, re
import pytest, allure
from datetime import datetime
from OS_Android.test_suite.zcc_zdx.zdx_regression.tunnel2_mode import conftest
from common_lib.Custom_Markers import add_markers
from common_lib.helper_function import help_return


device_stats_tables = ["tbl_battery_status", 'tbl_cpu_usage', 'tbl_disk_io', 'tbl_memory_usage',
                       'tbl_network_interface', 'tbl_mon_processes', 'tbl_wifi_info']


class TestDeviceStatsT2:

    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.service_ent = conftest.service_ent
        self.logger_obj = conftest.logger_obj
        self.log_handler = conftest.log_handler
        self.device_type = self.sys_ops.android_device_type()
        self.act_net = self.export_logs.appinfo_file_values('Active Network')
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_device_stats.db"

        self.db_handler.create_db_connection(self.db_path)

    def verify_and_validate_devicestats_data(self, table_name, columns, islt):
        """
        Validates specified columns in a database table against predefined rules.

        Parameters:
        - table_name (str): Base name of the table (prefix handled internally).
        - columns (list): List of columns to validate.
        - islt (int): Flag to determine if table is long-term; adds 'lt_session_id' if set.

        Returns:
        - (bool, str): True with "SUCCESS" if all validations pass, else False with issues listed.
        """

        actual_table_name = f"tbl_{table_name}" if islt == 0 else f"tbl_lt_{table_name}"
        columns.append('lt_session_id') if islt == 1 else None

        self.logger_obj.info(f"Verifying data from {actual_table_name}...")
        rows_data = self.db_handler.execute_query(f"SELECT * FROM {actual_table_name}")

        if not rows_data:
            pytest.skip(f"'{actual_table_name}' is empty.")
        column_rules = {
            'pct_total': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'pct_kernel': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'pct_user': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'pct_idle': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'rssi_dbm': {'allow_empty': False, 'data_type': 'float', 'min_value': -100, 'max_value': 100},
            'tx_pkts': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100000000},
            'rx_pkts': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100000000},
            'rxmit_pkts': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100000000},
            'rxmit_pct': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'snr': {'allow_empty': False, 'data_type': 'float', 'min_value': -1, 'max_value': 100},
            'mcs': {'allow_empty': False, 'data_type': 'float', 'min_value': -1, 'max_value': 100},
            'signal_quality': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'rd_bps': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100000000000},
            'wt_bps': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100000000000},
            'free_mb': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 10000000000},
            'avg_diskq_len_scale_1000': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 10000000000},
            'pct_used': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'cpu_total_pct': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'mem_wss_pct': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'isWifi': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 1},
            'is_active': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 1},
            'bandwidth_mbps': {'allow_empty': False, 'data_type': 'float', 'min_value': -1, 'max_value': 10000000},
            'recv_bps': {'allow_empty': False, 'data_type': 'float', 'min_value': -1, 'max_value': 10000000},
            'send_bps': {'allow_empty': False, 'data_type': 'float', 'min_value': -1, 'max_value': 10000000},
            'refid': {'allow_empty': True, 'data_type': 'string'},
            'ifname': {'allow_empty': True, 'data_type': 'string'},
            'timestamp': {'allow_empty': False, 'data_type': 'string'},
            'ssid': {'allow_empty': True, 'data_type': 'string'},
            'total': {'allow_empty': False, 'data_type': 'string'},
            'free': {'allow_empty': False, 'data_type': 'string'},
            'used': {'allow_empty': False, 'data_type': 'string'},
            'swap_used': {'allow_empty': False, 'data_type': 'string'},
            'pid': {'allow_empty': False, 'data_type': 'string'},
            'name': {'allow_empty': False, 'data_type': 'string'},
            'cpu_user': {'allow_empty': False, 'data_type': 'string'},
            'cpu_kernel': {'allow_empty': False, 'data_type': 'string'},
            'cpu_total': {'allow_empty': False, 'data_type': 'string'},
            'mem_wss_bytes': {'allow_empty': False, 'data_type': 'string'},
            'guid': {'allow_empty': False, 'data_type': 'string'},
            'disk_name': {'allow_empty': False, 'data_type': 'string'}
        }

        all_issues = []

        try:
            schema_query = f"PRAGMA table_info({actual_table_name});"
            schema = self.db_handler.execute_query(schema_query)
            db_columns = [col['name'] for col in schema]
        except Exception as e:
            self.logger_obj.error(f"Error fetching table schema for '{actual_table_name}': {e}")
            return False, f"Error fetching table schema for '{actual_table_name}'."

        invalid_columns = [col for col in columns if col not in db_columns]
        if invalid_columns:
            invalid_columns_str = ", ".join(invalid_columns)
            self.logger_obj.warn(f"Columns {invalid_columns_str} do not exist in the table '{actual_table_name}'. Skipping these columns.")
            columns = [col for col in columns if col in db_columns]

        for row_idx, row_data in enumerate(rows_data, start=1):
            row_issues = []
            self.logger_obj.info(f"{'#' * 20} Verifying row {row_idx}, table: '{actual_table_name}' {'#' * 20}\n")

            if row_idx ==1 and table_name in ["network_interface","mon_processes"]:
                self.logger_obj.info(f"Skipping first row check for {table_name} because 1st value is most of the time negative\n")
                continue

            for col in columns:
                value = str(row_data[col])
                self.logger_obj.info(f"Verifying column '{col}': {value}")

                if col not in column_rules:
                    self.logger_obj.info(f"Skipping column '{col}' as it is not defined in column rules.")
                    continue

                rules = column_rules[col]

                # Special check for network_interface table
                if table_name == "network_interface" and col in ['bandwidth_mbps', 'send_bps', 'recv_bps']:
                    try:
                        is_active = float(row_data['is_active'])
                        value_float = float(value)
                        if is_active == 1 and value_float < 0:
                            msg = f"Invalid value '{value}' for '{col}' in row {row_idx}, table: '{actual_table_name}'. Should be non-negative when is_active=1."
                            self.logger_obj.error(msg)
                            row_issues.append(msg)
                    except (ValueError, TypeError) as e:
                        msg = f"Error validating '{col}' in row {row_idx}, table: '{actual_table_name}': {e}"
                        self.logger_obj.error(msg)
                        row_issues.append(msg)
                    continue  # skip normal rule check for these columns

                if not value and not rules.get('allow_empty', True):
                    msg = f"Empty value found at '{col}' in row {row_idx}, table: '{actual_table_name}'. It should not be empty."
                    self.logger_obj.error(msg)
                    row_issues.append(msg)

                elif rules['data_type'] in ['float', 'int']:
                    try:
                        value_float = float(value)
                    except ValueError:
                        msg = f"Invalid value '{value}' for column '{col}' in row {row_idx}, table: '{actual_table_name}'. It should be a number."
                        self.logger_obj.error(msg)
                        row_issues.append(msg)
                        continue

                    if 'min_value' in rules and value_float < rules['min_value']:
                        msg = f"Invalid value '{value}' for column '{col}' in row {row_idx}, table: '{actual_table_name}'. It must be >= {rules['min_value']}."
                        self.logger_obj.error(msg)
                        row_issues.append(msg)
                    if 'max_value' in rules and value_float > rules['max_value']:
                        msg = f"Invalid value '{value}' for column '{col}' in row {row_idx}, table: '{actual_table_name}'. It must be <= {rules['max_value']}."
                        self.logger_obj.error(msg)
                        row_issues.append(msg)

                elif rules['data_type'] == 'string':
                    if not isinstance(value, str):
                        msg = f"Invalid value '{value}' for column '{col}' in row {row_idx}, table: '{actual_table_name}'. It should be a string."
                        self.logger_obj.error(msg)
                        row_issues.append(msg)

            if not row_issues:
                self.logger_obj.info(f"Row {row_idx} successfully verified with no issues, table: '{actual_table_name}'.")

            if row_issues:
                all_issues.extend(row_issues)

        if all_issues:
            return False, "\n".join(all_issues)
        else:
            self.logger_obj.info(f"Successfully verified all rows in table '{actual_table_name}'. No issues found.\n")
            return True, "SUCCESS"

    @allure.title("Get one Timestamp before pattern timestamp where data is present")
    def create_timestamp_dict(self, pattern_data: dict[int, dict])->dict [int,int]:
        """
        Creates a dictionary where the keys are the current timestamps and the values are the
        corresponding previous timestamps.

        Args:
        pattern_data (dict[int, dict]): A dictionary of timestamps where each key is a timestamp and each value is a dictionary of data.

        Returns:
        dict[int, int]: A dictionary where the keys are the previous timestamps and the values are the corresponding current timestamps.

        """
        timestamp_dict = {}
        timestamps = sorted(pattern_data.keys())
        for i in range(len(timestamps)):
            if i > 0:
                timestamp_dict[timestamps[i - 1]] = timestamps[i]
        return timestamp_dict


    @allure.title("DeviceStats: Verify Battery Stats from DeviceStats db")
    @add_markers("regression")
    def test_tbl_battery_status_db(self):
        """
            This function tests the validation of tbl_battery_stats table from device_stats db
            It performs the following steps:
            1. Doing validation on all the columns and also checking correct battery percentage
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("Verifying tbl_battery_status data from device_stats_db")
        query = "SELECT level_pct from tbl_battery_status order by timestamp desc"
        rows_data = self.db_handler.execute_query(query)
        status = True
        msg = ""

        last_value_pct_inserted_in_db = -1
        result = self.sys_ops.get_battery_percentage()
        assert result[0],result[1]

        device_battery = result[2]

        for data in rows_data:
            if len(str(data['level_pct'])) >= 1:
                last_value_pct_inserted_in_db = int(data['level_pct'])
                break

        self.logger_obj.info(f"Current battery pct from the device using adb command = {device_battery}")
        self.logger_obj.info(f"Latest battery pct in upm_device_stats.db = {last_value_pct_inserted_in_db}")

        self.logger_obj.info("Checking if latest battery pct from the logs and the db is same")
        if not (device_battery - 2 <= last_value_pct_inserted_in_db <= device_battery +2):
           status = False
           msg = "Incorrect value of the 'level_pct' in the device stats db file"

        assert status, msg

        query = "Select * from tbl_battery_status"
        rows_data = self.db_handler.execute_query(query)

        self.logger_obj.info("Validating all the columns data of tbl_battery_status from device_stats db")

        for data in rows_data:
            columns = []
            columns.extend(['timestamp', 'refid', 'level_pct'])

            for i in columns:
                if len(str(data[i])) == 0:
                    msg = f"No value found at column = {i} in row timestamp = {data['timestamp']}"
                    assert False, msg
                else:
                    if re.search('[a-zA-Z ]', str(data[i])) is None:
                        if i == 'level_pct':
                            if int(data['level_pct']) >= 101 or int(data['level_pct']) <= -2:
                                msg = f"For timestamp = {data['timestamp']}, The 'level_pct' value is = {data['level_pct']} which is Invalid"
                                assert False, msg
                            else:
                                self.logger_obj.info(
                                    f"Validated timestamp = {data['timestamp']}, the 'level_pct' value is = {data['level_pct']}, which is correct")
                        else:
                            if float(data[i]) <= 0:
                                msg = f"Value at colum = {i} in row timestamp = {data['timestamp']} is {data[i]} which is invalid"
                                assert False, msg

        assert status, msg

    @allure.title("---- DeviceStats: Verifying device_stats data insertion frequency -----")
    @add_markers("regression")
    @pytest.mark.parametrize("table_name", device_stats_tables)
    def test_device_stats_data_insertion_frequency(self, table_name):
        """
             This function Verify data is written to upm_device_stats.db in every 20s for regular session

            It performs the following steps:
            1. Doing validation on all the columns
            Args:
                self (object): Instance of the test class.
                Name of the table which needs to validate
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(f"DeviceStatsDb: Verifying device_stats data insertion frequency for {table_name} table")
        status = True
        msg = ""
        query = f"select distinct timestamp from {table_name} order by timestamp limit 30"
        output = self.db_handler.execute_query(query)

        if len(output) <= 1:
            status = False
            msg = f"Failed to get data for table '{table_name}' "

        assert status, msg

        for ind in range(0, len(output) - 1):
            # here curr_timestamp < prev_timestamp
            # timestamp is in unix ms
            curr_timestamp = output[ind][0]
            prev_timestamp = output[ind + 1][0]

            difference = int(round(abs(curr_timestamp - prev_timestamp) / 1000, 0))

            if not (55 <= difference <= 65):
                status = False
                msg = (f"Failed to collect data for {table_name} in 1min at currentime "
                       f":{datetime.fromtimestamp(int(curr_timestamp / 1000))} and prevtime "
                       f":{datetime.fromtimestamp(int(prev_timestamp / 1000))} diff "
                       f":{curr_timestamp - prev_timestamp} {int(round((curr_timestamp - prev_timestamp) / 1000, 0))} ")
            else:
                self.logger_obj.info(f"Validating time difference between current_row = {curr_timestamp} and "
                                     f"prev_row ={prev_timestamp}, Difference is {difference}s which is correct")

        assert status, msg
        self.logger_obj.info(f"Validation completed for table = {table_name}")

    @allure.title("Validating device stats table [tbl_wifi_info] data from DeviceStats DB")
    @add_markers("regression")
    def test_tbl_wifi_info_devicestats_db(self):
        """
        Test to verify the integrity of WiFi statistics in the 'wifi_info' table of the 'upm_device_stats' database.

        This test is parameterized with `islt=0` (regular mode) and `islt=1` (LT mode), and performs two key validations:

        1. `verify_wifi(islt)`: Validates the live WiFi statistics collected from the system for the specified mode.
        2. `verify_and_validate_devicestats_data(...)`: Checks the correctness and completeness of expected columns
        in the 'wifi_info' table, including: 'timestamp', 'refid', 'ifname', 'ssid', 'signal_quality', 'rssi_dbm',
        'snr', 'tx_pkts', 'rx_pkts', 'rxmit_pkts', 'rxmit_pct', and 'mcs'.

        The test logs detailed information and fails if any verification step encounters mismatches or missing data.
        """

        errors = []
        columns = ['timestamp', 'refid', 'ifname', 'ssid', 'signal_quality', 'rssi_dbm', 'snr', 'tx_pkts', 'rx_pkts',
                   'rxmit_pkts', 'rxmit_pct', 'mcs']

        result = self.verify_and_validate_devicestats_data(table_name='wifi_info',columns=columns,islt=0)

        if not result[0]:
            errors.append(f"verify_data_from_db failed: {result[1]}")
            self.logger_obj.error(f"verify_and_validate_devicestats_data verification failed. Details: {result[1]}")
        else:
            self.logger_obj.info(f"verify_and_validate_devicestats_data verification passed. {result[1]}")

        if errors:
            assert not errors, "\n".join(errors)
        else:
            self.logger_obj.info(
                "Test Passed: Both verify_wifi and verify_and_validate_devicestats_data verifications succeeded with no issues.")

    @allure.title("Verify data from device info should correctly inserted to the tbl_wifi_info table device_stats db")
    @add_markers("regression")
    def test_validate_tbl_wifi_info_from_upm_logs_to_db_data(self):
        """
            This function is validating data of ZDX:setWifiDetails  to the device stats db data

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Verify data from device info should correctly inserted to the"
                             " tbl_wifi_info table device_stats db")
        status = True
        msg = ""

        self.logger_obj.info("Fetching ZCC_Android logs data for validation")
        upm_logs_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                              plugin='upm')
        if upm_logs_data is None:
            msg = "Not able to fetch data from 'ZCC_Android' log file"
            assert False, msg

        sp = "ZDX:setWifiDetails"
        ep = "ZDX:setMemoryDetails"

        self.logger_obj.info("Fetching setWifiDetails from ZCC_Android logs")
        set_wifi_details = self.log_handler.fetch_device_stats_info(data=upm_logs_data, start_pattern=sp,
                                                                    end_pattern=ep)
        assert set_wifi_details[0], set_wifi_details[1]
        length = len(set_wifi_details[2])
        set_wifi_details = set_wifi_details[2]

        self.logger_obj.info(f"--------------setWifiDetails data from logs--------------")
        for key, value in set_wifi_details.items():
            self.logger_obj.info(f"Value with timestamp = {key} : {value}")

        self.logger_obj.info(f"---------------------------------------------------------")

        previous_timestamp = self.create_timestamp_dict(set_wifi_details)

        query = "select * from tbl_wifi_info"
        row_data = self.db_handler.execute_query(query)
        # if self.device_type.lower() == "chromeos":
        #     set_wifi_details = set_wifi_details[1:]
        # length = min(length, len(row_data))
        length = len(row_data)

        for index in range(1, length):
            # for [timestamp,refid,ifname,ssid,signal_quality,rssi_dbm]
            timestamp = row_data[index]['timestamp']
            timestamp = int(timestamp/1000)
            timestamp = previous_timestamp[timestamp]
            db_if_name = row_data[index]['ifname']
            db_signal_quality = row_data[index]['signal_quality']
            db_rssi_dbm = row_data[index]['rssi_dbm']
            signal_quality_from_rssi = 0

            if db_rssi_dbm == 0:
                signal_quality_from_rssi = 0
            elif db_rssi_dbm <= -100:
                signal_quality_from_rssi = 0
            elif db_rssi_dbm >= -50:
                signal_quality_from_rssi = 100
            elif -50 > db_rssi_dbm > -100:
                signal_quality_from_rssi = 2 * (db_rssi_dbm + 100)

            ifname_from_log = set_wifi_details[timestamp]['ifDescription']
            rssi_dbm_from_log = set_wifi_details[timestamp]['rssi']

            self.logger_obj.info(
                f"Validating 'ifname', 'rssi_dbm', 'signal_quality' values from ZCC_Android "
                f"logs and table tbl_wifi_info of device_stats db for timestamp = {timestamp}")

            if db_if_name != ifname_from_log:
                status = False
                msg = (f"[DB:LOG]:[{db_if_name}:{ifname_from_log}] ifname not matching "
                       f"for INTERFACE: {db_if_name} at timestamp: {timestamp}")
                break

            if int(db_rssi_dbm) != int(rssi_dbm_from_log):
                status = False
                msg = (f"[DB:LOG]:[{rssi_dbm_from_log}:{db_rssi_dbm}] RSSI_dbm not matching "
                       f"for INTERFACE: {db_if_name} in timestamp: {timestamp}")
                break

            if int(db_signal_quality) != int(signal_quality_from_rssi):
                status = False
                msg = (f"[DB:DB]:[{db_signal_quality}:{signal_quality_from_rssi}] Signal quality is not matching for "
                       f"INTERFACE: {db_if_name} at timestamp: {timestamp} with formula => SignalQuality = 2 * (RSSI+100)")
                break

        assert status, msg

    @allure.title("Validating device stats table [tbl_cpu_usage] data from DeviceStats DB")
    @add_markers("regression")
    def test_cpu_usage_devicestats_db(self):
        """
        Test to validate the CPU usage statistics stored in the 'cpu_usage' table of the 'upm_device_stats' database.

        This test runs for both regular (`islt=0`)
        It verifies the presence and correctness of the following columns in the 'cpu_usage' table:
        - 'timestamp'
        - 'refid'
        - 'pct_total'
        - 'pct_kernel'
        - 'pct_user'
        - 'pct_idle'

        The test fails and logs an error if any validation discrepancies are found in the database records.
        """
        columns = ['timestamp', 'refid', 'pct_total', 'pct_kernel', 'pct_user', 'pct_idle']
        result = self.verify_and_validate_devicestats_data(table_name='cpu_usage',columns=columns,islt=0 )

        if not result[0]:
            assert False, f"verify_and_validate_devicestats_data failed for CPU usage: {result[1]}"
        else:
            self.logger_obj.info(f"CPU usage verification passed. {result[1]}")

    @allure.title("Validating device stats table [tbl_disk_io] data from DeviceStats DB")
    def test_disk_io_devicestats_db(self):
        """
        Test to validate disk I/O statistics in the 'disk_io' table of the 'upm_device_stats' database.

        - 'timestamp'
        - 'refid'
        - 'disk_name'
        - 'rd_bps' (read bytes per second)
        - 'wt_bps' (write bytes per second)
        - 'free_mb' (available space in MB)
        - 'pct_used' (percentage of disk used)
        - 'avg_diskq_len_scale_1000' (scaled average disk queue length)

        The test logs a failure with details if any validation check does not pass.
        """
        columns = ['timestamp', 'refid', 'disk_name', 'rd_bps', 'wt_bps', 'free_mb', 'pct_used',
                   'avg_diskq_len_scale_1000']
        res = self.verify_and_validate_devicestats_data(table_name='disk_io',columns=columns,islt=0)

        if not res[0]:
            assert False, f"verify_and_validate_devicestats_data failed for disk_io: {res[1]}"
        else:
            self.logger_obj.info(f"disk_io verification passed. {res[1]}")


    @allure.title("Verify data from device info should correctly inserted to the tbl_disk_io table device_stats db")
    @add_markers("regression")
    def test_validate_tbl_disk_io_data_from_upm_logs_to_db_data(self):
        """
            This function is validating data of ZDX:setDiskDetails to the device stats db data

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Verify data from device info should correctly inserted to the"
                             " tbl_wifi_info table device_stats db")
        status = True
        msg = ""

        self.logger_obj.info("Fetching ZCC_Android logs data for validation")
        upm_logs_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                          plugin='upm')
        if upm_logs_data is None:
            msg = "Not able to fetch data from 'ZCC_Android' log file"
            assert False, msg

        sp = "ZDX:setDiskDetails"
        ep = "ZDX:setWifiDetails"

        self.logger_obj.info("Fetching setDiskDetails from ZCC_Android logs")
        set_disk_details = self.log_handler.fetch_device_stats_info(data=upm_logs_data, start_pattern=sp,
                                                                    end_pattern=ep)
        assert set_disk_details[0], set_disk_details[1]
        length = len(set_disk_details[2])
        set_disk_details = set_disk_details[2]

        self.logger_obj.info(f"--------------setDiskDetails data from logs--------------")
        for key, value in set_disk_details.items():
            self.logger_obj.info(f"Value with timestamp = {key} : {value}")

        self.logger_obj.info(f"---------------------------------------------------------")

        query = "select * from tbl_disk_io"
        row_data = self.db_handler.execute_query(query)
        length =  len(row_data)
        # if self.device_type.lower() == "chromeos":
        #     set_disk_details = set_disk_details[1:]

        for index in range(1, length):
            # for [timestamp,refid,disk_name,rd_bps,wt_bps,avd_diskq_len_scale_1000, free_mb, pct_used]
            timestamp = row_data[index]['timestamp']
            timestamp = int(timestamp / 1000)
            disk_name = row_data[index]['disk_name']
            free_mb_from_db = round(float(row_data[index]['free_mb']), 2)
            pct_used_from_db = round(float(row_data[index]['pct_used']), 2)

            disk_name_from_log = set_disk_details[timestamp]['name']
            pct_used_from_log = round(set_disk_details[timestamp]['usedPercent'], 2)
            free_mb_from_log = round(set_disk_details[timestamp]['freeMB'], 2)

            self.logger_obj.info(
                f"Validating 'free_mb_db':'free_mb_log' = {free_mb_from_db} : {free_mb_from_log} \n'pct_used_db':'pct_used_log' = {pct_used_from_db} : {pct_used_from_log}"
                f"\n'disk_name_db':'disk_name_log' = {disk_name} : {disk_name_from_log}\nvalues from Upm "
                f"logs and table tbl_disk_io of device_stats db for timestamp = {timestamp}")

            if float(free_mb_from_db) != float(free_mb_from_log):
                status = False
                msg = (f"[{free_mb_from_db}:{free_mb_from_log}]free_mb from DB and log are not "
                       f"matching for Disk Name:{disk_name} at timestamp:{timestamp}")
                assert False, msg

            if float(pct_used_from_db) != float(pct_used_from_log):
                status = False
                msg = (f"[{pct_used_from_db}:{pct_used_from_log}]pct_used from DB and log are not "
                       f"matching for Disk Name:{disk_name} at timestamp:{timestamp}")
                assert False, msg

            if disk_name != disk_name_from_log:
                status = False
                msg = (f"[{disk_name}:{disk_name_from_log}] diskName from DB and log are not "
                       f"matching  at timestamp:{timestamp}")
                assert False, msg

        assert status, msg

    @allure.title("Validating device stats table [tbl_mem_usage] data from DeviceStats DB")
    def test_memory_usage_devicestats_db(self):
        """
        Test to validate memory usage statistics in the 'memory_usage' table of the 'upm_device_stats' database.

        It checks for the presence and correctness of the following columns:
        - 'timestamp'
        - 'refid'
        - 'total' (total memory)
        - 'free' (free memory)
        - 'used' (used memory)
        - 'pct_used' (percentage of memory used)
        - 'swap_used' (swap memory used)

        The test fails with a detailed log message if any of the validations do not succeed.
        """
        columns = ['timestamp', 'refid', 'total', 'free', 'used', 'pct_used', 'swap_used']
        res = self.verify_and_validate_devicestats_data(table_name='memory_usage',columns = columns,islt=0)

        if not res[0]:
            assert False, f"verify_and_validate_devicestats_data failed for memory_usage: {res[1]}"
        else:
            self.logger_obj.info(f"memory_usage verification passed. {res[1]}")

    @allure.title("Verify data from device info should correctly inserted to the tbl_mem_usage table device_stats db")
    @add_markers("regression")
    def test_validate_tbl_mem_usage_data_from_upm_logs_to_db_data(self):
        """
            This function is validating data of ZDX:setMemoryDetails to the device stats db data

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info(
            "Verify data from device info should correctly inserted to the tbl_mem_usage table device_stats db")

        status = True
        msg = ""

        self.logger_obj.info("Fetching ZCC_Android logs data for validation")
        upm_logs_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                          plugin='upm')
        if upm_logs_data is None:
            msg = "Not able to fetch data from 'ZCC_Android' log file"
            assert False, msg

        sp = "ZDX:setMemoryDetails"
        ep = "ZDX:setProcessDetails"

        self.logger_obj.info("Fetching setMemoryDetails from ZCC_Android logs")
        set_memory_details = self.log_handler.fetch_device_stats_info(data=upm_logs_data, start_pattern=sp,
                                                                      end_pattern=ep)
        assert set_memory_details[0], set_memory_details[1]
        length = len(set_memory_details[2])
        set_memory_details = set_memory_details[2]

        self.logger_obj.info(f"--------------setMemoryDetails data from logs--------------")
        for key, value in set_memory_details.items():
            self.logger_obj.info(f"Value with timestamp = {key} : {value}")
        self.logger_obj.info(f"---------------------------------------------------------")

        query = "select * from tbl_memory_usage"
        row_data = self.db_handler.execute_query(query)
        # if self.device_type.lower() == "chromeos":
        #     set_memory_details = set_memory_details[1:]
        # length = min(length, len(row_data))

        for index in range(1 , length):
            # for [timestamp,refid,total,free,used,pct_used, swap_used]
            timestamp = row_data[index]['timestamp']
            timestamp = int(timestamp / 1000)
            total_from_db = row_data[index]['total']
            free_from_db = row_data[index]['free']
            used_from_db = row_data[index]['used']
            pct_used_from_db = row_data[index]['pct_used']

            total_from_log = set_memory_details[timestamp]['totalMem']
            free_from_log = set_memory_details[timestamp]['availMem']
            used_from_log = set_memory_details[timestamp]['usedMem']
            pct_used_from_log = set_memory_details[timestamp]['memLoad']

            self.logger_obj.info(
                f"Validating 'total' = {total_from_db}, 'free' = {free_from_db}, 'used' = {used_from_db}, 'pct_used' = {pct_used_from_db} values from Upm"
                f"logs and table tbl_memory_usage of device_stats db for timestamp = {timestamp}")

            if int(total_from_db) != int(total_from_log):
                status = False
                msg = (f"[{total_from_db}:{total_from_log}]total from DB and log are not "
                       f"matching for [Memory]: at timestamp:{timestamp}")
                break

            if int(free_from_db) != int(free_from_log):
                status = False
                msg = (f"[{free_from_db}:{free_from_log}]free from DB and log are not "
                       f"matching for [Memory]: at timestamp:{timestamp}")
                break

            if int(used_from_db) != int(used_from_log):
                status = False
                msg = (f"[{used_from_db}:{used_from_log}]used from DB and log are not "
                       f"matching for [Memory]: at timestamp:{timestamp}")
                break

            if int(pct_used_from_db) != int(pct_used_from_log):
                status = False
                msg = (f"[{pct_used_from_db}:{pct_used_from_log}]pct_used from DB and log are not "
                       f"matching for[Memory]: at timestamp:{timestamp}")
                break

        assert status, msg

    @allure.title("Validating device stats table [tbl_mem_usage] data from DeviceStats DB")
    def test_mon_processes_devicestats_db(self):
        """
        Test to validate monitored process statistics in the 'mon_processes' table of the 'upm_device_stats' database.

        Checks for correctness and completeness of the following columns:
        - 'timestamp'
        - 'pid'
        - 'name'
        - 'cpu_user'
        - 'cpu_kernel'
        - 'cpu_total'
        - 'cpu_total_pct'
        - 'mem_wss_bytes'
        - 'mem_wss_pct'

        If the validation fails, the test raises a detailed error via pytest and logs the failure context.
        """

        columns = ['timestamp', 'pid', 'name', 'cpu_user', 'cpu_kernel', 'cpu_total', 'cpu_total_pct',
                   'mem_wss_bytes', 'mem_wss_pct']
        res = self.verify_and_validate_devicestats_data(table_name='mon_processes',columns=columns, islt=0)

        if not res[0]:
            assert False, f"verify_and_validate_devicestats_data failed for mon_processes: {res[1]}"
        else:
            self.logger_obj.info(f"mon_processes verification passed. {res[1]}")

    @allure.title("Validating device stats table [tbl_network_interface] data from DeviceStats DB")
    def test_network_interface_devicestats_db(self):
        """
        Test to verify the integrity of network interface statistics in the 'network_interface' table
        within the 'upm_device_stats' database.

        This test is esuring comprehensive validation across different data collection contexts.

        It validates the presence and correctness of the following columns:
        - 'timestamp'
        - 'refid'
        - 'ifname'
        - 'guid'
        - 'recv_bps'
        - 'send_bps'
        - 'is_active'
        - 'bandwidth_mbps'

        The test will fail and log an error if the validation fails, ensuring visibility into any data quality issues.
        """

        columns = ['timestamp', 'refid', 'ifname', 'guid', 'recv_bps', 'send_bps', 'is_active', 'bandwidth_mbps']
        res = self.verify_and_validate_devicestats_data(table_name='network_interface',columns=columns, islt=0)


        if not res[0]:
            assert False, f"verify_and_validate_devicestats_data failed for network_interface: {res[1]}"
        else:
            self.logger_obj.info(f"Network_interface verification passed. {res[1]}")

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Deletion of forward profile and app profile
        2. Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Closing db connection after verification")
        self.db_handler.close_db_connection()

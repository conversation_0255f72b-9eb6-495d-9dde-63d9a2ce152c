import pytest, allure
from OS_Android.test_suite.zcc_zdx.zdx_regression.tunnel2_mode import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.deviceprofile_resource import deviceprofile_resource

@pytest.mark.sanity
@pytest.mark.deviceprofile
@pytest.mark.t2
class Test_deviceprofileT2:
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.service_ent = conftest.service_ent
        self.logger = conftest.logger_obj
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_traceroute.db"
        self.deviceprofile_resource = deviceprofile_resource()

    @allure.title("Verify table : cpu_info, DB : upm_device_profile")
    def test_cpu_info(self):
        """
        Test case to validate CPU information in the 'cpu_info' table of the 'upm_device_profile' database.

        This test performs two key validations:
        1. `validate_cpu_info()` - Ensures the current system CPU details match the expected format and values.
        2. `verify_and_validate_deviceprofile_data()` - Verifies that all required columns exist and contain valid data
        in the 'cpu_info' table, including: 'timestamp', 'refid', 'model', 'manufacturer',
        'cur_speed_mhz', 'max_speed_mhz', 'num_logical_processors', and 'num_of_cores'.

        The test logs detailed results for each validation and fails if any discrepancies are found.
        """

        errors = []
        res1 = self.deviceprofile_resource.validate_cpu_info()
        if not res1[0]:
            errors.append(f"validate_cpu_info failed: {res1[1]}")
            self.logger.error(f"validate_cpu_info verification failed. Details: {res1[1]}")
        else:
            self.logger.info(f"validate_cpu_info verification passed. {res1[1]}")

        columns = ['timestamp', 'refid','model', 'manufacturer','cur_speed_mhz','max_speed_mhz','num_logical_processors', 'num_of_cores']
        res2 = self.deviceprofile_resource.verify_and_validate_deviceprofile_data(table_name='cpu_info',columns=columns)

        if not res2[0]:
            errors.append(f"verify_and_validate_deviceprofile_data failed: {res2[1]}")
            self.logger.error(f"verify_and_validate_deviceprofile_data verification failed. Details: {res2[1]}")
        else:
            self.logger.info(f"verify_and_validate_deviceprofile_data verification passed. {res2[1]}")

        if errors:
            pytest.fail("\n".join(errors))
        else:
            self.logger.info(
                "Test Passed: Both validate_cpu_info and verify_and_validate_deviceprofile_data verifications succeeded with no issues.")

    @allure.title("Verify table : wifi_info, DB : upm_device_profile")
    def test_wifi_info(self):
        """
        Validates the integrity and accuracy of WiFi-related data stored in the 'wifi_info' table
        of the 'upm_device_profile' database.

        This test performs the following verifications:
        1. Cross-verifies system WiFi configuration (e.g., SSID, interface name, GUID, channel)
        against the latest entry in the database using `validate_wifi_device_profile()`.
        2. Validates the structure and consistency of the 'wifi_info' table by ensuring
        all required columns ('timestamp', 'refid', 'ifname', 'type', 'ssid',
        'bssid', 'channel', 'driver_ver') are populated and accurate using
        `verify_and_validate_deviceprofile_data()`.

        The test fails if any discrepancies or missing data are detected in the WiFi configuration
        or the database schema. Detailed error logs are generated for easier debugging.
        """

        errors = []
        res1 = self.deviceprofile_resource.validate_wifi_device_profile()
        if not res1[0]:
            errors.append(f"validate_wifi_device_profile failed: {res1[1]}")
            self.logger.error(f"validate_wifi_device_profile failed. Details: {res1[1]}")
        else:
            self.logger.info(f"validate_wifi_device_profile passed. {res1[1]}")

        columns = ['timestamp', 'refid','ifname', 'type', 'guid', 'channel', ]
        res2 = self.deviceprofile_resource.verify_and_validate_deviceprofile_data(table_name='wifi_info',columns=columns)

        if not res2[0]:
            errors.append(f"verify_and_validate_deviceprofile_data failed: {res2[1]}")
            self.logger.error(f"verify_and_validate_deviceprofile_data verification failed. Details: {res2[1]}")
        else:
            self.logger.info(f"verify_and_validate_deviceprofile_data verification passed. {res2[1]}")

        if errors:
            pytest.fail("\n".join(errors))
        else:
            self.logger.info(
                "Test Passed: Both validate_wifi_device_profile and verify_and_validate_deviceprofile_data verifications succeeded with no issues.")

    @allure.title("Verify table : disk_info, DB : upm_device_profile")
    def test_disk_info(self):
        """
        Validates the integrity and completeness of the 'disk_info' table
        in the 'upm_device_profile' database.

        This test ensures that all required columns—'timestamp', 'refid', 'model', 'size', and 'type'—
        are present and correctly populated for each record in the 'disk_info' table.
        The validation is performed using the `verify_and_validate_deviceprofile_data()` method.

        The test will fail if any required column data is missing, inconsistent, or improperly formatted.
        On success, a confirmation is logged.
        """

        res = self.deviceprofile_resource.verify_and_validate_deviceprofile_data(
            table_name='disk_info',
            columns=['timestamp', 'refid', 'size', 'type']
        )

        if not res[0]:
            pytest.fail(f"verify_and_validate_deviceprofile_data failed for disk_info: {res[1]}")
        else:
            self.logger.info(f"disk_info verification passed. {res[1]}")

    @allure.title("Verify table : system_info, DB : upm_device_profile")
    def test_system_info(self):
        """
        Verifies the integrity of the 'system_info' table in the 'upm_device_profile' database.

        This test performs two key validation steps:
        1. It compares the system's actual information with the expected system information using the
        `verify_and_compare_system_info()` method.
        2. It checks the completeness and consistency of the 'system_info' table by validating the
        following columns: 'timestamp', 'refid', 'os_name', 'os_build', 'os_version', 'hw_serial_no',
        'sys_mfg', 'sys_model', 'sys_type', 'gpu', 'mem_model', 'hostname', 'netbios_name', 'username',
        'total_installed_mem', 'total_phy_mem', 'avail_phy_mem', 'last_bootup_time', 'num_processors',
        'npcap_version' using the `verify_and_validate_deviceprofile_data()` method.

        The test will fail if any of the validations fail, and details will be logged for debugging. On success,
        a confirmation message is logged indicating that all verifications were successful.
        """
        errors = []

        res1 = self.deviceprofile_resource.verify_and_compare_system_info()
        if not res1[0]:
            errors.append(f"verify_and_compare_system_info failed: {res1[1]}")
            self.logger.error(f"verify_and_compare_system_info failed. Details: {res1[1]}")
        else:
            self.logger.info(f"verify_and_compare_system_info passed. {res1[1]}")

        res2 = self.deviceprofile_resource.verify_and_validate_deviceprofile_data(table_name='system_info',
                                                                                  columns=['timestamp', 'refid',
                                                                                           'os_name', 'os_build',
                                                                                           'os_version', 'hw_serial_no',
                                                                                           'sys_mfg', 'sys_model',
                                                                                           'sys_type', 'gpu', 'hostname',
                                                                                           'netbios_name', 'username',
                                                                                           'total_installed_mem',
                                                                                           'total_phy_mem',
                                                                                           'last_bootup_time',
                                                                                           'num_processors'])

        if not res2[0]:
            errors.append(f"verify_and_validate_deviceprofile_data failed: {res2[1]}")
            self.logger.error(f"verify_and_validate_deviceprofile_data verification failed. Details: {res2[1]}")
        else:
            self.logger.info(f"verify_and_validate_deviceprofile_data verification passed. {res2[1]}")

        if errors:
            pytest.fail("\n".join(errors))
        else:
            self.logger.info(
                "Test Passed: Both verify_wifi_deviceprofile and verify_and_validate_deviceprofile_data verifications succeeded with no issues.")

    @allure.title("Verify table : system_info, DB : upm_device_profile")
    def test_interface_details(self):
        """
        Verifies the integrity of the 'system_info' table in the 'upm_device_profile' database.

        This test performs validation steps:
        1. It compares the system's actual information with the expected system information using the
        `verify_interface_details()` method.

        The test will fail if any of the validations fail, and details will be logged for debugging. On success,
        a confirmation message is logged indicating that all verifications were successful.
        """
        errors = []

        res1 = self.deviceprofile_resource.verify_interface_details()
        if not res1[0]:
            errors.append(f"verify_interface_details failed: {res1[1]}")
            self.logger.error(f"verify_interface_details failed. Details: {res1[1]}")
        else:
            self.logger.info(f"verify_interface_details passed. {res1[1]}")

        if errors:
            pytest.fail("\n".join(errors))
        else:
            self.logger.info(
                "Test Passed:  verify_interface_details succeeded with no issues.")

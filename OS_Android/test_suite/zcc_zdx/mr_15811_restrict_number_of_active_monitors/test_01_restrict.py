import time, os
import pytest, allure
from OS_Android.test_suite.zcc_zdx.mr_15811_restrict_number_of_active_monitors import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.ui_zcc import Zcc


class TestRestrictMonitorPolicy:
    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        if not ("ZDX_ADVANCED_PLUS" in conftest.variables["ZDX_ADMIN"]):
            pytest.skip(f" 'ZDX_ADVANCED_PLUS' Key is not present in the config.json.  ")
        elif not conftest.is_zdx_advanced_plus:
            pytest.skip('Skipping all cases :: zdx_advanced_plus SKU is not present in your zdx subscription')

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.variables = conftest.variables
        self.logger_obj = conftest.logger_obj
        self.zdx_admin = conftest.zdx_admin
        self.zcc = Zcc(log_handle=self.logger_obj, start_app=True)
        self.zdx_admin_monitor_dropped = conftest.zdx_admin_support_monitor
        self.monitor_config_json = conftest.monitor_config_json

        self.zcc.logger.info("Creating application and monitor data on ZDX Admin UI")
        self.zdx_admin.create_application_and_monitors()

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]

        self.zcc.logger.info("Validating if ZDX service status is ON")
        result = self.zcc.verify_service_status(service_status="on", service_type='ZDX')
        assert result[0], result[1]

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

    # Helper Function
    def is_monitor_drop_zero(self, expect_failure=False):
        policy_status = self.log_handler.get_regular_policy_json_from_log(path=self.const_obj.LOG_PATH)
        if not policy_status[0]:
            return policy_status[0], policy_status[1]

        policies = policy_status[2]
        for policy in policies:
            num_dropped_monitor = policy['policy']['num_dropped_monitors']
            if not expect_failure:
                if num_dropped_monitor == 0:
                    self.zcc.logger.info("Dropped monitor policy found in the logs with"
                                         " value equal to 0, which is true")
                    status = True
                    msg = "Dropped monitor policy found in the logs with value equal to 0, which is true"
                    return status, msg
            else:
                if num_dropped_monitor != 0:
                    self.zcc.logger.info("Dropped monitor policy found in the logs with"
                                         " value not equal to 0, which is true")
                    status = True
                    msg = "Dropped monitor policy found in the logs with value not equal to 0, which is true"
                    return status, msg

        return False, "Incorrect value found for 'num_dropped_monitors' "

    @allure.title(
        "Verify that if no monitors are dropped by TPG then 'monitors_dropped' section will not be present in policy")
    @pytest.mark.xray("QA-283108")
    @add_markers("regression")
    def test_no_monitor_dropped_policy_received(self):
        """
             This function Verify that if no monitors are dropped by TPG then 'monitors_dropped' section will not be
              present in policy

             Args:

             It performs the following steps:
             1. It will configure less than 30 monitor on the zdx admin UI and check
             for "num_dropped_monitors": value should be 0 in policy received
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info("Verify that if no monitors are dropped by TPG then 'num_dropped_monitors' "
                             " should be 0 in the policy")

        result = self.is_monitor_drop_zero()
        assert result[0], result[1]

    @allure.title("Verify that if admin creates more than 30 monitors then TPG will include 'monitors_dropped'"
                  " in the policy that will indicate which monitors are dropped by TPG")
    @pytest.mark.xray("QA-283104")
    @add_markers("regression")
    def test_monitor_dropped_policy_received(self):
        """
             Verify that if admin creates more than 30 monitors then TPG will include "monitors_dropped"
             in the policy that will indicate monitors are dropped by TPG

             Args:

             It performs the following steps:
             1. It will configure 30 monitor on the zdx admin UI and check
             for "num_dropped_monitors": value should be not equal to 0 in policy received
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Verify that if admin creates more than 30 monitors then TPG will include"
                             " 'monitors_dropped' in the policy that will indicate which monitors are dropped by TPG")

        self.zcc.logger.info("Creating 30 webprobe and traceroute for Support monitor dropped feature")
        result = self.zdx_admin_monitor_dropped.create_application_and_monitors_support_monitor_dropped(
            configs=self.monitor_config_json)
        assert result[0], result[1]

        self.zcc.logger.info("Sleep:: 10s")
        time.sleep(10)

        self.zcc.logger.info("Update policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info(" Restart ZDX ")
        result = self.zcc.restart_zdx_service()
        assert result[0], result[1]

        time.sleep(5)

        self.zcc.logger.info("Clear Outdated logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Sleep:: 45s to collect zdx data")
        time.sleep(45)

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = self.is_monitor_drop_zero(expect_failure=True)
        assert result[0], result[1]

    @allure.title("Verify that if no monitors are dropped by TPG then 'monitors_dropped'"
                  " section will not be present in policy")
    @pytest.mark.xray("QA-283112")
    @add_markers("regression")
    def test_no_monitor_dropped_policy_received_after_deleting_extra_monitors(self):
        """
             Verify that once the additional x monitors are deleted from the total (30+x)
             monitors, then the 'monitors_dropped' will not be part of the policy anymore

             Args:

             It performs the following steps:
             1. It will configure less than 30 monitor on the zdx admin UI and check
             for "num_dropped_monitors": value should be 0 in policy received
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Verify that if no monitors are dropped by TPG then 'monitors_dropped' "
                             "section will not be present in policy")

        self.zcc.logger.info("Deleting support monitor dropped config webprobes and traceroute")
        self.zdx_admin_monitor_dropped.delete_application_and_monitors_support_monitor_dropped(
            configs=self.monitor_config_json)

        self.zcc.logger.info("Sleep:: 10s")
        time.sleep(10)

        self.zcc.logger.info("Update Policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info(" Restart ZDX ")
        result = self.zcc.restart_zdx_service()
        assert result[0], result[1]

        time.sleep(5)

        self.zcc.logger.info("Clear Outdated logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Sleep:: 60s to collect zdx data")
        time.sleep(60)

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = self.is_monitor_drop_zero()
        assert result[0], result[1]

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Deletion of forward profile and app profile
        2. Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        self.zcc.logger.info("Deleting Application and monitors")
        self.zdx_admin.delete_application_and_monitors()

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

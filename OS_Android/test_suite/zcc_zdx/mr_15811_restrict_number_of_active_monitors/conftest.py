import pytest

from common_lib.mobileadmin.deviceoverview import DeviceOverview
from common_lib.adminzia.admingroupuser import AdminG<PERSON><PERSON><PERSON>
from common_lib.mobileadmin.appprofile import AppProfile
from common_lib.mobileadmin.forwardingprofile import ForwardingProfile
from common_lib.adminzia.ziahelper import *
from common_lib.common.log_ops import *
from OS_Android.library.ops_system import SysOps
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
from common_lib.mobileadmin.trustednetwork import TrustedNetwork
from OS_Android.library.database_parse import DbHandler
from OS_Android.library.database_parse import <PERSON>gHandler
from OS_Android.library.zdx_admin import ZDX_ADMIN
monitor_config_path = os.getcwd() + f"\\OS_Android\\test_suite\\zcc_zdx\\mr_15811_restrict_number_of_active_monitors\\Config\\monitor_dropped.json"


config_path = os.path.join(os.getcwd(),"config", os.environ.get("CONFIG_FILE"))

with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)

with open(monitor_config_path) as file:
    monitor_config_json = json.load(file)

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")
device_overview=DeviceOverview(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)

const_obj=constants.Utils()

sys_ops=SysOps(logger_obj,variables["CLOUD"])

export_logs=FetchAndroidLogs(logger_obj)

create_zia_user=AdminGroupUser(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)

zia_activate=ZiaHelper(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)

log_ops=LogOps(log_handle=logger_obj)

pcap_ops=PcapOps(log_handle=logger_obj)


app_profile_obj = AppProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
forwarding_profile_obj=ForwardingProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
trusted_network = TrustedNetwork(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)

db_handler = DbHandler()
log_handler = LogHandler()
zdx_admin = ZDX_ADMIN(cloudname=variables["CLOUD_DETAILS"]["ZDX"], configfile=variables,logger=logger_obj)
zdx_admin_support_monitor = ZDX_ADMIN(cloudname=variables["CLOUD_DETAILS"]["ZDX"], configfile=variables,logger=logger_obj)

is_zdx_advanced_plus = False
if "ZDX_ADVANCED_PLUS" in variables["ZDX_ADMIN"]:
    is_zdx_advanced_plus = True if variables["ZDX_ADMIN"]["ZDX_ADVANCED_PLUS"] == 1 else False


@allure.title("Base teardown")
def make_teardown():
    """
    1. Delete App and Forwarding profile
    """

    logger_obj.info("Deleting App profile for mr_15811_restrict_number_of_active_monitors")
    try:
        result = app_profile_obj.delete_app_profile(operating_system="Android")
        assert result[0], result[1]
    except Exception as e:
        logger_obj.info(str(e))

    logger_obj.info("Deleting Forwarding profile for mr_15811_restrict_number_of_active_monitors")
    try:
        result = forwarding_profile_obj.delete_forwarding_profile()
        assert result[0], result[1]
    except Exception as e:
        logger_obj.info(str(e))


@pytest.fixture(scope="class", autouse=True)
def base_setup(request):
    app_profile = app_profile_obj
    forwarding_profile = forwarding_profile_obj

    try:
        logger_obj.info("Deleting App profile")
        try:
            result = app_profile.delete_app_profile(operating_system="Android")
            assert result[0], result[1]
        except Exception as e:
            logger_obj.info(str(e))

        logger_obj.info("Deleting Forwarding profile")
        try:
            result = forwarding_profile.delete_forwarding_profile()
            assert result[0], result[1]
        except Exception as e:
            logger_obj.info(str(e))

        logger_obj.info("Creating Forwarding profile")
        result = forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        logger_obj.info("Creating App profile")
        result = app_profile.create_app_profile(operating_system="Android")
        assert result[0], result[1]
    except AssertionError as e:
        request.addfinalizer(make_teardown)
        pytest.skip(f"setup failed reason:{e}")
    else:
        request.addfinalizer(make_teardown)

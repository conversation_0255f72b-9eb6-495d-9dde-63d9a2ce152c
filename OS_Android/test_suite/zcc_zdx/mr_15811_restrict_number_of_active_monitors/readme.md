# mr_15811_restrict_number_of_active_monitors - Requirements
## Prerequisites:
    1) On MA compnay and user must be configured.
    2) On Android device zcc version should be installed
    3) There should not any configuration present on ZIA
    4) ZDX service should be enabled
    5) ZDX subscription should be (ZDX Advanced Plus) [Must]

```commandline

  "ZDX_ADVANCED_PLUS": 0,      # If subscription is available use 1 else 0 (0 will skip cases for this)

```


## Commands to Execute Automation:
**Running All Cases:** python trigger_automation.py --config <config.json> --testcases test_suite/zcc_zdx

**Running One Suite from zcc_zdx folder:** python trigger_automation.py --config config.json --testcases test_suite/zcc_zdx/mr_15811_restrict_number_of_active_monitors

**Running One Test File from mr_15811_restrict_number_of_active_monitors folder:** python trigger_automation.py --config config.json --testcases test_suite/zcc_zdx/mr_15811_restrict_number_of_active_monitors/*.py


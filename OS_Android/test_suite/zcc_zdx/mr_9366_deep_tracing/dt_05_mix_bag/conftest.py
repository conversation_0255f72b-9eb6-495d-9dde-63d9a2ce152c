import json,os

from common_lib.mobileadmin.deviceoverview import DeviceOverview
from common_lib.mobileadmin.appprofile import AppProfile
from common_lib.mobileadmin.forwardingprofile import ForwardingProfile
from common_lib.common.log_ops import *
from OS_Android.library.ops_system import SysOps
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
from OS_Android.library.database_parse import <PERSON>b<PERSON><PERSON><PERSON>
from OS_Android.library.database_parse import <PERSON>g<PERSON>andler
from OS_Android.library.zdx_admin import ZDX_ADMIN

config_path = os.path.join(os.getcwd(),"config", os.environ.get("CONFIG_FILE"))

with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")
device_overview=DeviceOverview(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)
const_obj=constants.Utils()
sys_ops=SysOps(logger_obj,variables["CLOUD"])
export_logs=FetchAndroidLogs(logger_obj)
log_ops=LogOps(log_handle=logger_obj)
pcap_ops=PcapOps(log_handle=logger_obj)


app_profile_obj = AppProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
forwarding_profile_obj=ForwardingProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
zdx_admin = ZDX_ADMIN(cloudname=variables["CLOUD_DETAILS"]["ZDX"], configfile=variables,
                                   logger=logger_obj)


db_handler = DbHandler()
log_handler = LogHandler()

zdx_application = variables["ZDX_ADMIN"]["ZDX_APPLICATION"][0]
zia_traceroute_domain = zdx_application["tracert_icmp"]["host"]
zia_webprobe_url = zdx_application["web_probe"]["url"]
session_dur = zdx_application["DT"][0]
session_duration = session_dur["durationMinutes"]
probe_port = 443

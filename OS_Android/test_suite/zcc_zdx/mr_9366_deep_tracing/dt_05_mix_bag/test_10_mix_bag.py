import time, os
import pytest, allure
import json, re
from datetime import datetime
from OS_Android.test_suite.zcc_zdx.mr_9366_deep_tracing.dt_05_mix_bag import conftest
from common_lib.Custom_Markers import add_markers
from common_lib.helper_function import help_return
from OS_Android.library.ui_zcc import Zcc


upm_to_search = ["ZLTU: LT Uploader stop called",
                 "ZLTU: Stopping LT uploader",
                 "ZLTU: LT Uploader Stopped",
                 "ZLTU: Uploader thread exit"
                 ]

device_stats_to_search = ["ZDSPlugin: LT sessions stop called",
                          "ZDSPlugin: Abort all LT sessions"
                          ]
webload_to_search = ["ZWLT: Webload stop called",
                     "ZWLT: Abort all LT sessions",
                     "ZWLT: Stopping session"
                     ]
traceroute_to_search = ["ZUpmTraceroute:: stopLtSession: In",
                        "ZUpmTraceroute: stopLtSession:Out with Success"
                        ]


class TestDeeptracingMixBag:
    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.variables = conftest.variables
        self.logger_obj = conftest.logger_obj
        self.zdx_admin = conftest.zdx_admin
        self.export_logs = conftest.export_logs
        self.zcc = Zcc(log_handle=self.logger_obj, start_app=True)

        try:
            self.zcc.logger.info("Deleting App profile")
            try:
                result = self.app_profile.delete_app_profile(operating_system="Android")
                assert result[0], result[1]
            except Exception as e:
                self.zcc.logger.info(str(e))

            self.zcc.logger.info("Deleting Forwarding profile")
            try:
                result = self.forwarding_profile.delete_forwarding_profile()
                assert result[0], result[1]
            except Exception as e:
                self.zcc.logger.info(str(e))

            self.zcc.logger.info("Creating Forwarding profile")
            result = self.forwarding_profile.create_forwarding_profile()
            assert result[0], result[1]

            self.zcc.logger.info("Creating App profile")
            result = self.app_profile.create_app_profile(operating_system="Android")
            assert result[0], result[1]

            logged_in = self.zcc.validate_zcc_logged_in()
            if logged_in[0]:
                self.zcc.logger.info("Clear logs from zcc app")
                result = self.zcc.clear_logs()
                assert result[0], result[1]

                self.zcc.logger.info("Logout from zcc")
                result = self.zcc.logout()
                assert result[0], result[1]

            result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                    conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0], result[1]

            self.zcc.logger.info("Validating if ZDX service status is ON")
            result = self.zcc.verify_service_status(service_status="on", service_type='ZDX')
            assert result[0], result[1]

            self.zcc.logger.info("Creating webmonitor and traceroute for DT session")
            self.zdx_admin.create_application_and_monitors()

            self.zcc.logger.info("Creating DT session with Youtube application")
            self.zdx_admin.create_dt_session(application="Automation_ZIA", device_probing=False)

            self.dt_create_time = datetime.now()
            self.dt_create_time_timestamp = datetime.timestamp(self.dt_create_time)
            self.zcc.logger.info(f"Dt session create time - {self.dt_create_time_timestamp}")

            self.zcc.logger.info("Sleep :: 120s to collect dt session data")
            time.sleep(120)

            self.zcc.logger.info("Aborting DT Session from Admin UI")
            dt_session_info = self.zdx_admin.abort_dt_session()
            assert dt_session_info[0], dt_session_info[1]

            self.dt_session_id = dt_session_info[2]

            self.zcc.logger.info("Sleep :: 60s to change status abort_initiated to aborted")
            time.sleep(60)

            self.zcc.logger.info("Fetch logs from android device")
            result = self.zcc.export_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Export logs from android device")
            result = self.export_logs.read_export_log_mail()
            assert result[0], result[1]
        except Exception as e:
            pytest.skip(f"Skipping testcases due to setup failure: {e}")


    # Helper function to validate plugin stopped
    @allure.title("Check LT plugin stopped or not for given plugin")
    def check_lt_plugin_stopped(self, plugin_name: str, except_failure: bool = False):
        """
           This function performs the log validation of LT plugin stopped
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        file_name = ""
        to_search = []
        status = True

        if plugin_name == "web":
            to_search = webload_to_search
        elif plugin_name == "upm":
            to_search = upm_to_search
        elif plugin_name == "traceroute":
            to_search = traceroute_to_search
        elif plugin_name == "deviceStats":
            to_search = device_stats_to_search
        else:
            return False, "Pass correct plugin name to search for plugin stop"

        log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin=plugin_name)
        if len(log_data) ==0:
            self.zcc.logger.info("Not able to fetch data from log file/ log file is missing")
            assert False, "Not able to fetch data from  log file"

        if except_failure:
            self.zcc.logger.info(f"Validating {plugin_name} for failure of plugin stop")
        else:
            self.zcc.logger.info(f"Validating {plugin_name} for plugin stop")

        for pattern in to_search:
            status = False
            for log_line in log_data.split("\n"):
                if pattern in log_line:
                    status = True
                    break

            if except_failure and status:
                return False, f"Expecting Failure {pattern}, but we found in the logs"
            if not except_failure and not status:
                return False, f"Fail :: Expecting log found {pattern}, but log is not present in the log file"

        return True, "SUCCESS:: Search is successful"

    @allure.title("LT session - Verify that zapp does not gets device stats policy for LT when user creates an"
                  " LT session on Admin UI by not selecting device probing ")
    @pytest.mark.xray("QA-266933")
    @add_markers("regression")
    def test_lt_device_stats_not_included_when_devicemonitoring_is_disabled(self):
        """

             LT session - Verify that zapp does not get device stats policy for LT when user creates an LT
              session on Admin UI by not selecting device probing

              Disable deviceMonitoring when creating DT session
              Device stats policy should not be included in LT policy download

             Args:

             It performs the following steps:
             1.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info(f"LT session - Verify that zapp gets didn't receive device stats policy for LT when"
                             " user creates an LT session on Admin UI by disabling deviceMonitoringEnabled ")

        status = False
        msg = f"Device stats policy present in the DT session"

        self.zcc.logger.info("Fetching upm log file data to validate")
        upm_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')
        if upm_log_data is None:
            self.zcc.logger.info("Not able to fetch data from upm log file")
            assert False, "Not able to fetch data from upm log file"

        self.zcc.logger.info("Fetching LT policies from upm log file")
        policies_status = self.log_handler.get_dt_policy_json_from_log(return_json=False)
        assert policies_status[0], policies_status[1]

        policies = policies_status[2]

        self.zcc.logger.info("Validating type: DEVICESTATS should not present in LT policy")
        for policy in policies:
            if "created" in policy and '"type": "DEVICESTATS"' not in policy:
                status = True
                break
        assert status, msg

    @allure.title("LT session - Verify that there is no significant delay between admin creating the LT session on ZDX"
                  " Admin UI and the LT session getting delivered to ZApp via policy -  ")
    @pytest.mark.xray("QA-266935")
    @add_markers("regression")
    def test_lt_should_not_have_significant_delay(self):
        """
             LT session - Verify that there is no significant delay between admin creating the LT session on ZDX Admin
             UI and the LT session getting delivered to ZApp via policy -

             Args:

             It performs the following steps:
             1. It will check LT policy from upm logs files and check time differnce between creating dt session to
             lt policy received
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        status = True
        msg = "DT session is not started immediately after creating it"
        log_to_find = "Process CREATED LT sessions"

        self.zcc.logger.info("Fetching upm log file data to validate timestamp")
        upm_log_file_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')
        if len(upm_log_file_data) == 0:
            status = False
            msg = "Not able to fetch UPM log file data"
        assert status, msg

        self.zcc.logger.info("Fetching timestamp from upm log file data for pattern 'Process CREATED LT sessions'")
        start_time = self.log_handler.find_log_line_timestamp_in_unix(data=upm_log_file_data, pattern=log_to_find)
        assert len(start_time) == 1, ("'Process CREATED LT sessions' is not in the logs, check dt session received"
                                      "in the upm logs or not")

        self.zcc.logger.info("Validating time difference should not be above 20s in dt receive after "
                             "creating dt session on admin UI")
        start_timestamp = start_time[0]
        start_timestamp += 19800
        time_difference = abs(start_timestamp - self.dt_create_time_timestamp)

        self.zcc.logger.info(f"Time difference is = {round(time_difference, 2)}")
        if time_difference > 20:
            status = False
            msg = f"Time difference = {time_difference}, it should be less than 20 seconds"

        assert status, msg

    @allure.title("LT session - Verify that if zapp receives LT session with state as ABORT_INITIATED then zapp will"
                  " report the status back as ABORTED after stoping the ongoing LT session -  ")
    @pytest.mark.xray(["QA-266988", "QA-266990", "QA-267030"])
    @add_markers("regression")
    def test_lt_abort_and_tpg_status_on_abort(self):
        """
             This function Verify that on aborting dt session we are recieving abort initated
             by policy, and we are aborting the dession and sending aborted status to tpg
             payload

             Args:

             It performs the following steps:
             1. It will check LT policy from upm logs files and time difference between policy fetch for LT session
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        status = False
        msg = "Sending ABORTED status to the tpg Failed"
        log_to_find = "ZLT: Payload sent to TPG"
        latest_upm_file = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['upm'][-1]

        self.zcc.logger.info("Validating 'ABORT_IN_PROGRESS (abort initiated)' from upm logs")
        result = help_return(self.log_obj.search_log_file, file=latest_upm_file,
                             words_to_find_in_line=["Process ABORT_IN_PROGRESS (abort initiated) LT sessions"],
                             directory=self.const_obj.LOG_PATH)
        assert result[0], result[1]

        self.zcc.logger.info(f"Validating querySessionsStatus:{self.dt_session_id} completed/aborted")
        aborted_pattern = f"ZLT: querySessionsStatus:{self.dt_session_id} completed/aborted"
        result = help_return(self.log_obj.search_log_file, file=latest_upm_file,
                             words_to_find_in_line=[aborted_pattern],
                             directory=self.const_obj.LOG_PATH)
        assert result[0], result[1]

        self.zcc.logger.info("Fetching upm log file data to validate timestamp")
        upm_log_file_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')
        if upm_log_file_data is None:
            status = False
            msg = "Not able to fetch UPM log file data"
            assert status, msg

        pattern_matched_log_lines = self.log_handler.find_log(data=upm_log_file_data, log_to_check=log_to_find)
        payload_pattern = f'"id":{self.dt_session_id},"state":"ABORTED"'

        self.zcc.logger.info("Checking if ZLT: Payload sent to TPG log line is present or not in upm log")
        if len(pattern_matched_log_lines) == 0:
            assert False, f"Unable to find TPG LT payload in the upm logs = {log_to_find}"

        self.zcc.logger.info("Checking if ABORTED state is present or not in the tpg upload payload")
        for lines in pattern_matched_log_lines:
            if payload_pattern in lines:
                self.zcc.logger.info("Success:: Tpg payload have aborted status")
                status = True
                break

        assert status, msg

    @allure.title("LT session - Verify that on ZDX turn off, all regular / LT sessions will be stopped")
    @pytest.mark.xray(["QA-266995","QA-266994"])
    @add_markers("regression")
    def test_all_lt_plugin_stop_on_turn_off_upm(self):
        """
             This function Verify lt plugin stop on turning upm off

             It performs the following steps:
             1. It will validate UPM should stop and restart all plugins when users restart ZDX
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info("Verify ZDX Turn Off - UPM should stop all the regular and lt plugins")
        status = True
        msg = ""

        self.zcc.logger.info("Restart ZDX Service")
        result = self.zcc.restart_zdx_service()
        assert result[0], result[1]

        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Creating DT session with Youtube application")
        self.zdx_admin.create_dt_session(application="Automation_ZIA")

        self.zcc.logger.info("Sleep 120s :: DT session data collection")
        time.sleep(120)

        self.zcc.logger.info(" Turn off UPM ")
        result = self.zcc.toggle_service(service="ZDX", action=False)
        assert result[0], result[1]

        time.sleep(5)

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        time.sleep(2)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.zcc.logger.info(" Turn ON UPM ")
        result = self.zcc.toggle_service(service="ZDX", action=True)
        assert result[0], result[1]

        result = self.check_lt_plugin_stopped(plugin_name="upm")
        assert result[0],result[1]

        result = self.check_lt_plugin_stopped(plugin_name="web")
        assert result[0], result[1]

        result = self.check_lt_plugin_stopped(plugin_name="traceroute")
        assert result[0], result[1]

        result = self.check_lt_plugin_stopped(plugin_name="deviceStats")
        assert result[0], result[1]

    @allure.title("LT session - Verify that when a regular monitor stops it will"
                  " not interrupt/stop the LT monitors that is currently running -")
    @pytest.mark.xray(["QA-266998","QA-266999","QA-267000"])
    @add_markers("regression")
    def test_no_interruption_in_lt_monitors_on_changing_regular_monitors_policy(self):
        """

             LT session - Verify that when a regular monitor stops it will
             not interrupt/stop the LT monitors that is currently running -

             Args:

             It performs the following steps:
             1.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info(f"LT session - Verify that when a regular monitor stops it will"
                             " not interrupt/stop the LT monitors that is currently running -")

        status = True
        msg = ""

        self.logger_obj.info("Checking status on ZDX")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="off", sleep=3)

        if result[0]:
            self.logger_obj.info("Turning ON ZDX before running the testcase")
            result = self.zcc.toggle_service(service="ZDX", action=True)
            if not result[0]: return False, result[1]

        self.zcc.logger.info("Restart ZDX Service")
        result = self.zcc.restart_zdx_service()
        assert result[0], result[1]

        self.zcc.logger.info("Creating DT session with Youtube application")
        self.zdx_admin.create_dt_session(application="Automation_ZIA")

        self.zcc.logger.info("Sleep 90s :: DT session data collection")
        time.sleep(90)

        self.zcc.logger.info("Deleting newly added webprobe and traceroute ")
        self.zdx_admin.delete_application_and_monitors(extra_web_mtr=True)

        self.zcc.logger.info("Sleep 90s :: DT session data collection")
        time.sleep(90)

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        time.sleep(5)

        self.zcc.logger.info("Validating upm logs to check failure for LT plugin stopped")
        result = self.check_lt_plugin_stopped(plugin_name="upm", except_failure=True)
        assert result[0], result[1]

        self.zcc.logger.info("Validating webload logs to check failure for LT plugin stopped")
        result = self.check_lt_plugin_stopped(plugin_name="web", except_failure=True)
        assert result[0], result[1]

        self.zcc.logger.info("Validating traceroute logs to check failure for LT plugin stopped")
        result = self.check_lt_plugin_stopped(plugin_name="traceroute", except_failure=True)
        assert result[0], result[1]

        self.zcc.logger.info("Validating device_stats logs to check failure for LT plugin stopped")
        result = self.check_lt_plugin_stopped(plugin_name="deviceStats", except_failure=True)
        assert result[0], result[1]

    @allure.title("LT session - Verify that on Logout, all regular / LT sessions will be stopped")
    @pytest.mark.xray("QA-266996")
    @add_markers("regression")
    def test_all_lt_plugin_stop_on_zcc_logout(self):
        """
             This function Verify lt plugin stop on logout

             It performs the following steps:
             1. It will validate UPM should stop and restart all plugins when user logsout
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info("Verify ZCC logout - UPM should stop all the lt plugins")

        self.logger_obj.info("Checking status on ZDX")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="off", sleep=3)

        if result[0]:
            self.logger_obj.info("Turning ON ZDX before running the testcase")
            result = self.zcc.toggle_service(service="ZDX", action=True)
            if not result[0]: return False, result[1]

        self.zcc.logger.info("Creating DT session with Youtube application")
        self.zdx_admin.create_dt_session(application="Automation_ZIA")

        self.zcc.logger.info("Sleep 120s :: DT session data collection")
        time.sleep(120)

        self.zcc.logger.info("Logout ZCC")
        result = self.zcc.logout()
        assert result[0], result[1]

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = self.check_lt_plugin_stopped(plugin_name="upm")
        assert result[0],result[1]

        result = self.check_lt_plugin_stopped(plugin_name="web")
        assert result[0], result[1]

        result = self.check_lt_plugin_stopped(plugin_name="traceroute")
        assert result[0], result[1]

        result = self.check_lt_plugin_stopped(plugin_name="deviceStats")
        assert result[0], result[1]

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Deletion of forward profile and app profile
        2. Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        self.zcc.logger.info("Deleting Application and monitors")
        self.zdx_admin.delete_application_and_monitors()

        self.zcc.logger.info("Deleting App profile")
        try:
            result = self.app_profile.delete_app_profile(operating_system="Android")
            assert result[0], result[1]
        except Exception as e:
            self.zcc.logger.info(str(e))

        self.zcc.logger.info("Deleting Forwarding profile")
        try:
            result = self.forwarding_profile.delete_forwarding_profile()
            assert result[0], result[1]
        except Exception as e:
            self.zcc.logger.info(str(e))

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]
import time, os
import pytest, allure
import json, re, math
from datetime import datetime
from OS_Android.test_suite.zcc_zdx.mr_9366_deep_tracing.dt_01_zia_tunnel import conftest
from common_lib.Custom_Markers import add_markers

device_stats_inputs = ['tbl_lt_battery_status', 'tbl_lt_cpu_usage', 'tbl_lt_disk_io', 'tbl_lt_memory_usage',
                       'tbl_lt_network_interface', 'tbl_lt_wifi_info']

device_stats_pattern_inputs = [
    pytest.param('ZDX:setDiskDetails', marks=pytest.mark.xray(["QA-283311", "QA-283316", "QA-289611"])),
    pytest.param('ZDX:setMemoryDetails', marks=pytest.mark.xray(["QA-283312", "QA-283316", "QA-289611"])),
    pytest.param('ZDX:setNetworkDetails', marks=pytest.mark.xray(["QA-283313", "QA-283316", "QA-289611"])),
    pytest.param('ZDX:setWifiDetails', marks=pytest.mark.xray(["QA-283314", "QA-283316", "QA-289611"]))
]


class TestLtDeviceStatsDb:

    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        3. Login to ZCC
        3. Creating DT session
        4. Export logs, Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.variables = conftest.variables
        self.logger_obj = conftest.logger_obj
        self.zdx_admin = conftest.zdx_admin
        self.session_duration = conftest.session_duration
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_device_stats.db"

        self.logger_obj.info("Creating db connection with device_stats_db")
        self.db_handler.create_db_connection(self.db_path)

    @allure.title("DT session - Verify all the 7 tables are created on upm_device_stats.db for DT session")
    @pytest.mark.xray("QA-266945")
    @add_markers("regression")
    def test_device_stats_lt_table_creation(self):
        """
            This function is validating for table creation of all the tables for device_stats db
            It performs the following steps:
            1. It will check following lt tables are present or not
               'tbl_lt_battery_status', 'tbl_lt_cpu_usage', 'tbl_lt_disk_io', 'tbl_lt_memory_usage',
               'tbl_lt_network_interface', 'tbl_lt_processes', 'tbl_lt_wifi_info'
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        status = True
        msg = ""

        devicestats_table_names = {
            'tbl_lt_battery_status': 1,
            'tbl_lt_cpu_usage': 1,
            'tbl_lt_disk_io': 1,
            'tbl_lt_memory_usage': 1,
            'tbl_lt_network_interface': 1,
            'tbl_lt_wifi_info': 1
        }

        self.logger_obj.info("Validating if all the tables are present or not for dt session")

        query = "SELECT name FROM sqlite_master WHERE type='table'"
        rows_data = self.db_handler.execute_query(query)
        for row in rows_data:
            table_name = row[0]
            if devicestats_table_names.get(table_name) is not None:
                devicestats_table_names[table_name] = 2

        for table in devicestats_table_names:
            if devicestats_table_names[table] == 1:
                status = False
                msg = f"{table} table is not present in upm_device_stats.db"
                break

        assert status, msg

    @allure.title("DT session - Verify device_stats lt db data insertion frequency is 10seconds")
    @pytest.mark.parametrize("table_name", device_stats_inputs)
    @pytest.mark.xray(["QA-266974", "QA-283308", "QA-267031"])
    @add_markers("regression")
    def test_data_collection_for_upm_device_stats_db_for_dt_session(self, table_name):
        """
            This function Verify data is written to upm_device_stats.db in every 10s for DT session
            It performs the following steps:
            1. It will check time difference between two rows should be 10seconds
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        status = True
        msg = ""

        self.logger_obj.info(f"Validating data insertion frequency 10s for table = {table_name}")
        query = f"select distinct timestamp from {table_name} order by timestamp desc limit 29"
        rows_data = self.db_handler.execute_query(query)

        query_lt_session_id = f"select lt_session_id from {table_name}"
        row_data_session_id = self.db_handler.execute_query(query_lt_session_id)

        # For android, we are not supporting data insertion for tbl_lt_process yet, but table is present.
        if len(rows_data) <= 1:
            status = False
            msg = f"Failed to get data for table '{table_name}' "

        assert status, msg

        for ind in range(len(rows_data) - 1):
            # here curr_timestamp > prev_timestamp
            # timestamp is in unix ms
            curr_timestamp = rows_data[ind][0]
            prev_timestamp = rows_data[ind + 1][0]

            difference = int(round((curr_timestamp - prev_timestamp) / 1000, 0))

            self.logger_obj.info(f"Validating time difference between current_row = {curr_timestamp} and "
                                 f"prev_row ={prev_timestamp}")

            if difference != 10:
                status = False
                msg = (f"Failed to collect data for {table_name} in 10s at currentime "
                       f":{datetime.fromtimestamp(int(curr_timestamp / 1000))} and prevtime "
                       f":{datetime.fromtimestamp(int(prev_timestamp / 1000))} diff "
                       f":{curr_timestamp - prev_timestamp} {int(round((curr_timestamp - prev_timestamp) / 1000, 0))} ")
                break
            else:
                self.logger_obj.info(f"Validated time difference between current_row = {curr_timestamp} and "
                                     f"prev_row ={prev_timestamp}, Difference is {difference}s which is correct")

        assert status, msg

        session_id = row_data_session_id[0]
        #Step is pending to Fetch DT session id from logs

        for id_from_db in row_data_session_id:
            if id_from_db != session_id:
                status = False
                msg = (f"lt_session_id is incorrect in table = {table_name}, Id from MA"
                       f" = {session_id} and Id from db = {id_from_db}")

        assert status, msg

    @allure.title("DT session - Verify raw data collection and insertion into tbl_lt_network_interface")
    @pytest.mark.xray(["QA-266971", "QA-283306"])
    @add_markers("regression")
    def test_verify_monitor_data_for_lt_network_interface(self):
        """
            This function is validating data for 'tbl_lt_network_interface'

            It performs the following steps:
            1. It will check if data is present or not and also validates data from the logs
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("DT session: Verify & Comparing data for table network interface for 'DT' session")
        status = True
        msg = ""

        device_stats_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                  plugin='deviceStats')
        if len(device_stats_log_data) == 0:
            status = False
            msg = "Not able to fetch data from devicestats log file"

        assert status, msg

        query = "select * from tbl_lt_network_interface order by timestamp desc limit 29"

        try:
            row_data = self.db_handler.execute_query(query)
            for row in row_data:
                # for DT [timestamp,refid,lt_session_id,ifname,guid,bandwidth_mbps,revc_bps,send_bps,isactive]
                timestamp_from_db = row[0]
                session_id = row[2]
                guid_from_db = row[4]
                bandwidth_from_db = row[5]
                recv_from_db = row[6]
                send_from_db = row[7]
                is_wifi_from_db = row[8]
                is_active_from_db = row[9]
                pattern = re.compile(
                    fr'(?im)LT sessionId:{session_id}.*?timestamp:\s+{timestamp_from_db}.*?GUID:\[{guid_from_db}].*?isConnected:([0-9]).*?Bandwidth:\[(.*?)\s+.*?\].*?Receive:\[(.*?)\s+.*?\].*?Send:\[(.*?)\s+.*?\]',
                    re.DOTALL)
                res = pattern.findall(device_stats_log_data)
                if len(res) > 0:
                    if len(res[0]) != 4:
                        status = False
                        msg = (f"Unable to fetch [Network Interface] field for interface with GUID:{guid_from_db} "
                               f"at timestamp :{timestamp_from_db}")
                    else:
                        self.logger_obj.info(
                            f"Validating 'is_connected', 'bandwidth', 'recv_bps' and 'send_bps' values from "
                            f"device_stats logs and table tbl_lt_network_interface db for timestamp = {timestamp_from_db}")

                        is_connected_from_log, bandwidth_from_log, recv_from_log, send_from_log = res[0]

                        if int(is_connected_from_log) != int(is_active_from_db):
                            status = False
                            msg = (
                                f"[{is_active_from_db}:{is_connected_from_log}]IsActive(isConnected) from DB and log not "
                                f"matching for Interface with GUID:{guid_from_db} at timestamp:{timestamp_from_db}")
                            break

                        if float(bandwidth_from_db) != float(bandwidth_from_log):
                            status = False
                            msg = (f"[{bandwidth_from_db}:{bandwidth_from_log}]Bandwidth from DB and log not "
                                   f"matching for Interface with GUID:{guid_from_db} at timestamp:{timestamp_from_db}")
                            break

                        if float(send_from_db) != float(send_from_log):
                            status = False
                            msg = (f"[{send_from_db}:{send_from_log}]Send Field from DB and log not matching for "
                                   f"Interface with GUID:{guid_from_db} at timestamp:{timestamp_from_db}")
                            break

                        if float(recv_from_db) != float(recv_from_log):
                            status = False
                            msg = (f"[{recv_from_db}:{recv_from_log}]Recv Field from DB and log not matching for "
                                   f"Interface with GUID:{guid_from_db} at timestamp:{timestamp_from_db}")
                            break
                else:
                    status = False
                    msg = "Unable to fetch tbl_lt_network_interface data from logs "
                    break

        except Exception as e:
            status = False
            msg = f"Unexpected Error occurred :{e}"

        assert status, msg

    @allure.title("DT session - Verify raw data collection and insertion into tbl_lt_wifi_info")
    @pytest.mark.xray(["QA-266973", "QA-283307"])
    @add_markers("regression")
    def test_verify_monitor_data_for_lt_wifi_info(self):
        """
            This function is validating data for 'tbl_lt_wifi_info'

            It performs the following steps:
            1. It will check if data is present or not and also validates data from the logs
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("DT session: Verify & Comparing data for table wifi info for 'DT' session")
        status = True
        msg = ""

        device_stats_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                  plugin='deviceStats')
        if len(device_stats_log_data) == 0:
            status = False
            msg = "Not able to fetch data from 'devicestats' log file"

        assert status, msg

        query = 'select * from  tbl_lt_wifi_info order by timestamp desc limit 29'

        try:
            row_data = self.db_handler.execute_query(query)
            for row in row_data:
                # for DT [timestamp,refid,lt_session_id,ifname,ssid,signal_quality,rssi_dbm,snr,tx_pkts,x_pkts,rxmit_pkts,rxmit_pct,mcs]
                db_timestamp = row[0]
                session_id = row[2]
                db_if_name = row[3]
                db_signal_quality = row[5]
                db_rssi_dbm = row[6]

                pattern = re.compile(
                    fr'(?ims)LT sessionId:{session_id}.*?timestamp:\s+{db_timestamp}.*?{re.escape(db_if_name)}.*?SSID:\[(.*?)\].*?Signal Quality:\[(.*?)%\].*?RSSI:\[(.*?)\s+.*?\]')

                stats_from_log = pattern.findall(device_stats_log_data)
                if len(stats_from_log) > 0:
                    if len(stats_from_log[0]) != 3:
                        status = False
                        msg = "Not able to fetch monitor data for [Wifi] from devicestats log file"
                    else:
                        self.logger_obj.info(
                            f"Validating 'signal_quality' and 'rssi_dbm' values from device_stats logs "
                            f"and table tbl_lt_wifi_info db for timestamp = {db_timestamp}")

                        log_ssid, log_signal_quality, log_rssi_dbm = stats_from_log[0]

                        if int(log_signal_quality) != int(db_signal_quality):
                            status = False
                            msg = (f"[DB:LOG]:[{log_signal_quality}:{db_signal_quality}] Signal Quality not matching "
                                   f"for INTERFACE: {db_if_name} in timestamp: {db_timestamp}")
                            break

                        if int(log_rssi_dbm) != int(db_rssi_dbm):
                            status = False
                            msg = (f"[DB:LOG]:[{log_rssi_dbm}:{db_rssi_dbm}] RSSI not matching "
                                   f"for INTERFACE: {db_if_name} in timestamp: {db_timestamp}")
                            break
                else:
                    status = False
                    msg = "Unable to fetch tbl_lt_wifi_info data from logs "
                    break

        except Exception as e:
            status = False
            msg = f"Unexpected Error occurred :{e}"

        assert status, msg

    @allure.title("DT session - Verify raw data collection and insertion into tbl_lt_disk_io of device stats")
    @pytest.mark.xray(["QA-266970", "QA-283304"])
    @pytest.mark.skip("Now we are not printing logs in Device stats after 4.0 ")
    @add_markers("regression")
    def test_verify_monitor_data_for_lt_disk_io(self):
        """
            This function is validating data for 'tbl_lt_disk_io'

            It performs the following steps:
            1. It will check if data is present or not and also validates data from the logs
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("DT session: Verify & Comparing data for table disk_io for 'DT' session")
        status = True
        msg = ""

        device_stats_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                  plugin='deviceStats')
        if len(device_stats_log_data) == 0:
            status = False
            msg = "Not able to fetch data from devicestats log file"

        assert status, msg

        query = "select * from tbl_lt_disk_io order by timestamp desc limit 29"

        try:
            row_data = self.db_handler.execute_query(query)
            for row in row_data:
                # for DT [timestamp,refid,lt_session_id,disk_name,rd_bps,wt_bps,avd_diskq_len_scale_1000, free_mb, pct_used]
                timestamp_from_db = row[0]
                session_id = row[2]
                disk_name = row[3]
                rd_bps_from_db = row[4]
                wt_bps_from_db = row[5]
                free_mb_from_db = round(float(row[7]), 2)
                pct_used_from_db = round(float(row[8]), 2)

                pattern = re.compile(
                    fr'(?im)LT sessionId:{session_id}.*?timestamp:\s+{timestamp_from_db}.*?{re.escape(disk_name)}.*?Free:\[(.*?)\s+.*?\].*?used:\[(\d+\.\d+)%\].*?Disk Read Bytes/sec:\[(\d+\.\d+)\].*?Disk Write Bytes/sec:\[(\d+\.\d+)\]',
                    re.DOTALL)

                res = pattern.findall(device_stats_log_data)
                if len(res) > 0:
                    if len(res[0]) != 4:
                        status = False
                        msg = f"Unable to fetch field for [Disk I/O] at timestamp :{timestamp_from_db}"
                    else:
                        self.logger_obj.info(
                            f"Validating 'free_mb', 'pct_used', 'rd_bps' and 'wt_bps' values from device_stats logs "
                            f"and table tbl_lt_disk_io db for timestamp = {timestamp_from_db}")

                        free_mb_from_log, pct_used, rd_bps_from_log, wt_bps_from_log = res[0]
                        pct_used_from_log = float(pct_used)

                        if float(free_mb_from_db) != float(free_mb_from_log):
                            status = False
                            msg = (f"[{free_mb_from_db}:{free_mb_from_log}]free_mb from DB and log are not "
                                   f"matching for Disk Name:{disk_name} at timestamp:{timestamp_from_db}")
                            break

                        if float(pct_used_from_db) != float(pct_used_from_log):
                            status = False
                            msg = (f"[{pct_used_from_db}:{pct_used_from_log}]pct_used from DB and log are not "
                                   f"matching for Disk Name:{disk_name} at timestamp:{timestamp_from_db}")
                            break

                        if float(wt_bps_from_db) != float(wt_bps_from_log):
                            status = False
                            msg = (f"[{wt_bps_from_db}:{wt_bps_from_log}]wt_bps from DB and log are not "
                                   f"matching for Disk Name:{disk_name} at timestamp:{timestamp_from_db}")
                            break

                        if float(rd_bps_from_db) != float(rd_bps_from_log):
                            status = False
                            msg = (f"[{rd_bps_from_db}:{rd_bps_from_log}]rd_bps from DB and log are not "
                                   f"matching for Disk Name:{disk_name} at timestamp:{timestamp_from_db}")
                            break
                else:
                    status = False
                    msg = "Unable to fetch tbl_lt_disk_io data from logs "
                    break

        except Exception as e:
            status = False
            msg = f"Unexpected Error occurred :{e}"

        assert status, msg

    @allure.title("DT session - Verify raw data collection and insertion into tbl_lt_memory_usage")
    @pytest.mark.xray(["QA-266969", "QA-283305"])
    @pytest.mark.skip("Now we are not printing logs in Device stats after 4.0 ")
    @add_markers("regression")
    def test_verify_monitor_data_for_tbl_lt_memory_usage(self):
        """
            This function is validating data for 'tbl_lt_memory_usage'

            It performs the following steps:
            1. It will check if data is present or not and also validates data from the logs
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("DT session: Verify & Comparing data for table tbl_lt_memory_usage for 'DT' session")
        status = True
        msg = ""

        device_stats_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                  plugin='deviceStats')
        if len(device_stats_log_data) == 0:
            status = False
            msg = "Not able to fetch data from devicestats log file"

        assert status, msg

        query = "select * from tbl_lt_memory_usage order by timestamp desc limit 29"

        try:
            row_data = self.db_handler.execute_query(query)
            for row in row_data:
                # for DT [timestamp,refid,lt_session_id,total,free,used,pct_used, swap_used]
                timestamp_from_db = row[0]
                session_id = row[2]
                total_from_db = row[3]
                free_from_db = row[4]
                used_from_db = row[5]
                pct_used_from_db = row[6]
                swap_used_from_db = row[7]

                pattern = re.compile(
                    fr'(?im)LT sessionId:{session_id}.*?timestamp:\s+{timestamp_from_db}.*?Physical memory: total:\[(.*?)\s+.*?\].*?free:\[(.*?)\s+.*?\].*?used:\[(.*?)\s+.*?\].*?Swap: total:\[(.*?)\s+.*?\].*?used:\[(.*?)\s+.*?\]',
                    re.DOTALL)
                res = pattern.findall(device_stats_log_data)
                if len(res) > 0:
                    if len(res[0]) != 5:
                        status = False
                        msg = f"Unable to fetch field for [Memory] at timestamp :{timestamp_from_db}"
                    else:
                        self.logger_obj.info(
                            f"Validating 'total', 'free', 'used', 'pct_used' and 'swap_used' values from device_stats "
                            f"logs and table tbl_lt_memory_usage db for timestamp = {timestamp_from_db}")

                        total_from_log, free_from_log, used_from_log, swap_total_from_log, swap_used_from_log = res[0]
                        pct_used_from_log = 0
                        if int(total_from_log) != 0:
                            pct_used_from_log = math.ceil((int(used_from_log) / int(total_from_log)) * 100)

                        if int(total_from_db) != int(total_from_log):
                            status = False
                            msg = (f"[{total_from_db}:{total_from_log}]total from DB and log are not "
                                   f"matching for [Memory]: at timestamp:{timestamp_from_db}")
                            break

                        if int(free_from_db) != int(free_from_log):
                            status = False
                            msg = (f"[{free_from_db}:{free_from_log}]free from DB and log are not "
                                   f"matching for [Memory]: at timestamp:{timestamp_from_db}")
                            break

                        if int(used_from_db) != int(used_from_log):
                            status = False
                            msg = (f"[{used_from_db}:{used_from_log}]used from DB and log are not "
                                   f"matching for [Memory]: at timestamp:{timestamp_from_db}")
                            break

                        if int(pct_used_from_db) != int(pct_used_from_log):
                            status = False
                            msg = (f"[{pct_used_from_db}:{pct_used_from_log}]pct_used from DB and log are not "
                                   f"matching for[Memory]: at timestamp:{timestamp_from_db}")
                            break

                        if int(swap_used_from_db) != int(swap_used_from_log):
                            status = False
                            msg = (f"[{swap_used_from_db}:{swap_used_from_log}]swap_used from DB and log are not "
                                   f"matching for [Memory] Swap: at timestamp:{timestamp_from_db}")
                            break
                else:
                    status = False
                    msg = "Unable to fetch tbl_lt_memory_usage data from logs "
                    break

        except Exception as e:
            status = False
            msg = f"Unexpected Error occurred :{e}"

        assert status, msg

    @allure.title("DT session - Verify raw data collection and insertion into tbl_lt_battery_status")
    @pytest.mark.xray(["QA-266968", "QA-283302", "QA-283309"])
    @add_markers("regression")
    def test_verify_monitor_data_for_tbl_lt_battery_status(self):
        """
            This function is validating data for 'tbl_lt_battery_status'

            It performs the following steps:
            1. It will check if data is present or not and also validates data from the logs
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("DT session: Verify & Comparing data for table tbl_lt_battery_status for 'DT' session")
        status = True
        msg = ""

        device_stats_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                  plugin='deviceStats')
        if len(device_stats_log_data) == 0:
            status = False
            msg = "Not able to fetch data from devicestats log file"

        assert status, msg

        query = "SELECT level_pct from tbl_lt_battery_status order by timestamp desc"
        rows_data = self.db_handler.execute_query(query)
        status = True
        msg = ""

        last_value_pct_inserted_in_db = -1
        result = self.sys_ops.get_battery_percentage()
        assert result[0], result[1]

        device_battery = result[2]

        for data in rows_data:
            if len(str(data['level_pct'])) >= 1:
                last_value_pct_inserted_in_db = int(data['level_pct'])
                break

        self.logger_obj.info(f"Current battery pct from the device using adb command = {device_battery}")
        self.logger_obj.info(f"Latest battery pct in upm_device_stats.db = {last_value_pct_inserted_in_db}")

        self.logger_obj.info("Checking if latest battery pct from the device and the db is same")
        if not (device_battery - 5 <= last_value_pct_inserted_in_db <= device_battery + 5):
            status = False
            msg = "Incorrect value of the 'level_pct' in the device stats db file"
        assert status, msg

        # Removed support in 4.0 []
        # self.logger_obj.info("Checking if battery pct from the devicestats logs and the db are same")
        # try:
        #     query = "select * from tbl_lt_battery_status order by timestamp desc limit 28"
        #     row_data = self.db_handler.execute_query(query)
        #     for row in row_data:
        #         # for DT [timestamp,refid,lt_session_id,level_pct]
        #         timestamp_from_db = row[0]
        #         session_id = row[2]
        #         level_pct_from_db = row[3]
        #
        #         if level_pct_from_db <= 0 or level_pct_from_db > 100:
        #             status = False
        #             msg = "Battery is out of range [0,100]"
        #         pattern = re.compile(
        #             fr'(?im)LT sessionId:{session_id}.*?timestamp:\s+{timestamp_from_db}.*?Battery level:\[(\d+\.\d+)%\]',
        #             re.DOTALL)
        #         res = pattern.findall(device_stats_log_data)
        #         if len(res) > 0:
        #             if len(res[0]) == 0:
        #                 status = False
        #                 msg = f"Unable to fetch field for [Battery] at timestamp :{timestamp_from_db}"
        #             else:
        #                 self.logger_obj.info(
        #                     f"Validating 'level_pct' from device_stats logs "
        #                     f"and table tbl_lt_battery_status db for timestamp = {timestamp_from_db}")
        #
        #                 level_pct_from_log = res[0]
        #
        #                 if float(level_pct_from_db) != float(level_pct_from_log):
        #                     status = False
        #                     msg = (f"[{level_pct_from_db}:{level_pct_from_log}]level_pct from DB and log are not "
        #                            f"matching for [Battery]: IdleTime at timestamp:{timestamp_from_db}")
        #                     break
        #         else:
        #             status = False
        #             msg = "Unable to fetch tbl_lt_battery_status data from logs "
        #             break
        #
        # except Exception as e:
        #     status = False
        #     msg = f"Unexpected Error occurred :{e}"

        assert status, msg

    @allure.title("DT session - Verify raw data collection and insertion into tbl_lt_cpu_usage")
    @pytest.mark.xray(["QA-266967", "QA-283303", "QA-283310"])
    @pytest.mark.skip("Now we are not printing logs in Device stats after 4.0 ")
    @add_markers("regression")
    def test_verify_monitor_data_for_tbl_lt_cpu_usage(self):
        """
            This function is validating data for 'tbl_lt_cpu_usage'

            It performs the following steps:
            1. It will check if data is present or not and also validates data from the logs
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("DT session: Verify & Comparing data for table tbl_lt_cpu_usage for 'DT' session")
        status = True
        msg = ""

        device_stats_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                  plugin='deviceStats')
        if len(device_stats_log_data) == 0:
            status = False
            msg = "Not able to fetch data from devicestats log file"

        assert status, msg

        query = "select * from tbl_lt_cpu_usage order by timestamp desc limit 29"

        try:
            row_data = self.db_handler.execute_query(query)
            for row in row_data:
                # for DT [timestamp,refid,lt_session_id,pct_total,pct_kernel,pct_user,pct_idle]
                timestamp_from_db = row[0]
                session_id = row[2]
                pct_total_from_db = row[3]
                pct_idle_from_db = row[6]

                pattern = re.compile(
                    fr'(?im)LT sessionId:{session_id}.*?timestamp:\s+{timestamp_from_db}.*?ProcessorTime:\[(\d+\.\d+)%\].*?IdleTime\[(\d+\.\d+)%\]',
                    re.DOTALL)

                res = pattern.findall(device_stats_log_data)
                if len(res) > 0:
                    if len(res[0]) != 2:
                        status = False
                        msg = f"Unable to fetch field for [CPU] at timestamp :{timestamp_from_db}"
                    else:
                        self.logger_obj.info(
                            f"Validating 'pct_total' and 'pct_idle' values from device_stats logs "
                            f"and table tbl_lt_cpu_usage db for timestamp = {timestamp_from_db}")
                        pct_total, pct_idle = res[0]
                        pct_total_from_log = float(pct_total)
                        pct_idle_from_log = float(pct_idle)

                        if float(pct_idle_from_db) != float(pct_idle_from_log):
                            status = False
                            msg = (f"[{pct_idle_from_db}:{pct_idle_from_log}]pct_idle from DB and log are not "
                                   f"matching for [CPU]: IdleTime at timestamp:{timestamp_from_db}")
                            break

                        if float(pct_total_from_db) != float(pct_total_from_log):
                            status = False
                            msg = (f"[{pct_total_from_db}:{pct_total_from_log}]pct_total from DB and log are not "
                                   f"matching for [CPU]: ProcessorTime at timestamp:{timestamp_from_db}")
                            break
                else:
                    status = False
                    msg = "Unable to fetch tbl_lt_cpu_usage data from logs "
                    break

        except Exception as e:
            status = False
            msg = f"Unexpected Error occurred :{e}"

        assert status, msg

    @allure.title("DT session - Verify new data fetch should be every 1min for device stats db")
    @pytest.mark.parametrize("pattern", device_stats_pattern_inputs)
    @add_markers("regression")
    def test_verify_data_fetch_from_android_logs_should_be_1_min(self, pattern):
        """
            This function is validating frequency of ZDX:setWifiDetails, ZDX:setNetworkDetails, ZDX:setMemoryDetails
            INF ZDX:setDiskDetails is 1min

            Args:
                pattern: Pattern to search for in Android logs to validate the data fetch is 1min.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("DT session: Verify new data fetch should be every 1min for device stats db")
        status = True
        msg = ""

        upm_logs_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                              plugin='upm')
        if len(upm_logs_data) == 0:
            status = False
            msg = "Not able to fetch data from 'ZSA_Upm' log file"
        assert status, msg

        try:
            result = self.log_handler.find_log_line_timestamp_in_unix(data=upm_logs_data, pattern=pattern)
            if len(result) < 3:
                status = False
                msg = f"Fetched only {len(result)} points, it should be more than equal to 5"
                assert status, msg

            for idx in range(1,len(result) - 1):
                prev_timestamp = result[idx]
                curr_timestamp = result[idx + 1]

                self.logger_obj.info(
                    f"Validating time differnce between prev_timestamp = {prev_timestamp} and current_timestamp = {curr_timestamp}")

                # Ideal case is to check 1min, but while running DT session setWifiDetails these value fetch will be
                # different for dt and regular session and there is no way to check which one belong to which session
                if (curr_timestamp - prev_timestamp) > 61:
                    status = False
                    msg = "Data fetch from Android logs is more than 1min which is incorrect"
                    break

        except Exception as e:
            status = False
            msg = f"Unexpected Error occurred :{e}"

        assert status, msg

    @allure.title("DT session - Verify Data fetch should just after every dt session json upload"
                  " which is 1min for all the tables in device_stats db")
    @pytest.mark.xray("QA-283315")
    @add_markers("regression")
    def test_verify_data_fetch_should_just_after_every_dt_upload(self):
        """
            This function is validating value fetch of ZDX:setWifiDetails, ZDX:setNetworkDetails, ZDX:setMemoryDetails
            INF ZDX:setDiskDetails is just after DT session upload

            It is fetching all the timestamp from ZSA_Upm logs where LT data upload is successful
            And validate those timestamp with the ZDXWorker:Start. Command = UPDATE_ZDX_DEVICE_STATS
            from zcc_android logs

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("DT session - Verify Data fetch should just after every dt session json upload"
                             " which is 1min for all the tables in device_stats db")
        status = True
        msg = ""
        pattern = "ZDXWorkManager:updateDeviceStats"

        upm_logs_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                          plugin='upm')
        if len(upm_logs_data)==0:
            msg = "Not able to fetch data from 'ZSA_Upm' log file"
            assert False, msg

        try:
            upload_data_timestamp = self.log_handler.find_log_line_timestamp_in_unix(data=upm_logs_data,pattern="ZLTU: About to upload Json of length")
            fetch_data_fetch_timestamp = self.log_handler.find_log_line_timestamp_in_unix(data=upm_logs_data,pattern=pattern)

            if len(upload_data_timestamp)!=5:
                assert False, f"Upload Data count = {len(upload_data_timestamp)} should be equal to dt session time which is {self.session_duration}"
            if len(fetch_data_fetch_timestamp) < len(upload_data_timestamp):
                assert False, f"Data fetch count = {len(fetch_data_fetch_timestamp)} should be greater than this {len(upload_data_timestamp)}"

            for timestamp in upload_data_timestamp:

                self.logger_obj.info(f"Validating if data fetch is just after DT upload for timestamp = {timestamp}")
                if timestamp not in fetch_data_fetch_timestamp:
                    status = False
                    msg = f"Data fetch is not just after DT json upload for timestamp = {timestamp}"
                    break

        except Exception as e:
            status = False
            msg = f"Unexpected Error occurred :{e}"

        assert status, msg

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Closing db connection
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Closing db connection after verification")
        self.db_handler.close_db_connection()

import time, os
import pytest, allure
import json, re, math
from datetime import datetime
from OS_Android.test_suite.zcc_zdx.mr_9366_deep_tracing.dt_01_zia_tunnel import conftest
from common_lib.Custom_Markers import add_markers

class TestLtTracerouteDataTunnelMode:

    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        3. Login to ZCC
        3. Creating DT session
        4. Export logs, Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.variables = conftest.variables
        self.logger_obj = conftest.logger_obj
        self.zdx_admin = conftest.zdx_admin
        self.db_path  = self.const_obj.LOG_PATH + f"\\upm_traceroute.db"
        self.zdx_application = self.variables["ZDX_ADMIN"]["ZDX_APPLICATION"][0]
        self.zia_traceroute_domain = self.zdx_application["tracert_icmp"]["host"]

        self.logger_obj.info("Creating db connection with traceroute_db")
        self.db_handler.create_db_connection(self.db_path)

    @allure.title(
        "DT Session - ZIA TRACEROUTE TUNNEL MODE - Verify new table created in upm_traceroute.db tunnel None Mode and LT data is recorded onto it")
    @pytest.mark.xray("QA-266943")
    @add_markers("regression")
    def test_lt_table_creation_for_traceroute_db(self):
        """
            This function is validating for table creation for traceroute db
            It performs the following steps:
            1. It will check following lt tables is present or not
               'trltmain', 'trltleg', 'trlthoplat'
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        status = True
        msg = ""

        traceroute_table_name = {
            'trltmain': 1,
            'trltleg': 1,
            'trlthoplat': 1
        }

        self.logger_obj.info("DT Session - ZIA TRACEROUTE TUNNEL MODE - Verify that new tables are created on"
                             " upm_traceroute.db and LT data is recorded onto it ")

        query = "SELECT name FROM sqlite_master WHERE type='table'"
        rows_data = self.db_handler.execute_query(query)
        for row in rows_data:
            table_name = row[0]
            if traceroute_table_name.get(table_name) is not None:
                traceroute_table_name[table_name] = 2

        for table in traceroute_table_name:
            if traceroute_table_name[table] == 1:
                status = False
                msg = f"{table} table is not present in upm_traceroute.db tunnel T1.0 Mode"
                break
            else:
                self.logger_obj.info(f"Verified table = {table} found in traceroute db")

        assert status, msg

    @allure.title("DT session - Verify trltmain db data insertion frequency is 1mins")
    @pytest.mark.xray(["QA-266966", "QA-267031"])
    @add_markers("regression")
    def test_data_collection_for_upm_traceroute_db_for_dt_session(self):
        """
            This function Verify data is written to upm_traceroute.db in every 1min for DT session
            It performs the following steps:
            1. It will check time difference between two rows should be 1mins
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        status = True
        msg = ""

        self.logger_obj.info(f"Validating data insertion frequency 1min for table"
                             f" 'trltmain' for upm_traceroute_db for DT session")
        query = f"select distinct timestamp from trltmain order by timestamp desc"
        rows_data = self.db_handler.execute_query(query)

        query_lt_session_id = f"select sessionid from trltmain"
        row_data_session_id = self.db_handler.execute_query(query_lt_session_id)

        self.logger_obj.info("Validating if data is present or not for lt tables in traceroute db")
        if len(rows_data) <= 1:
            status = False
            msg = (f"Failed to get data for table 'trltmain' of upm_traceroute_db for"
                   f" ZIA traceroute for DT session in tunnel T1.0 Mode ")
        assert status, msg

        for ind in range(len(rows_data) - 1):
            # here curr_timestamp > prev_timestamp
            # timestamp is in unix ms
            curr_timestamp = rows_data[ind][0]
            prev_timestamp = rows_data[ind + 1][0]

            difference = int(round((curr_timestamp - prev_timestamp) / 1000, 0))

            if not (55 <= difference <= 65):
                status = False
                msg = (f"Failed to collect data for trltmain in 1min at currentime for DT - Session in tunnel T1.0 Mode"
                       f":{datetime.fromtimestamp(int(curr_timestamp / 1000))} and prevtime "
                       f":{datetime.fromtimestamp(int(prev_timestamp / 1000))} diff "
                       f":{curr_timestamp - prev_timestamp} {int(round((curr_timestamp - prev_timestamp) / 1000, 0))} ")
            else:
                self.logger_obj.info(
                    f"DT Session - Validated time difference between current_row = {curr_timestamp} and "
                    f"prev_row ={prev_timestamp},Difference is 60s which is correct")

        assert status, msg

        session_id_from_ma = row_data_session_id[0]
        for id_from_db in row_data_session_id:
            if id_from_db != session_id_from_ma:
                status = False
                msg = (f"lt_session_id is incorrect in table = trltmain, Id from MA"
                       f" = {session_id_from_ma} and Id from db = {id_from_db}")

        assert status, msg

    @allure.title(
        "DT Session - ZIA TRACEROUTE TUNNEL T1.0 MODE: Verify maxhopcount and hopstodest count from traceroute db")
    @pytest.mark.xray(["QA-266962", "QA-266963", "QA-266964"])
    @add_markers("regression")
    def test_maxhopcount_hopstodest_unresponsivehopcount_from_traceroute_lt_db(self):
        """
             DT session - This function verifies that in Traceroute probes the value of 'hopstodest'
             should not be greater than 'maxhopcount'. It also verifies that in Traceroute
             probes the value of 'unresponsivehopcount' should not be greater than 'hopstodest'

             It performs the following steps:
             1. It is validating maxhopcount, hopstodest and unresponsivehopcount values in traceroute db
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("DT session - ZIA TRACEROUTE TUNNEL MODE: Verifying that in Traceroute probes"
                             "the value of'hopstodest'should not be greater than 'maxhopcount'")

        query = f"SELECT * from trltmain WHERE domain='{self.zia_traceroute_domain}'"
        rows_data = self.db_handler.execute_query(query)
        current_row = 1
        flag = True
        msg = ""

        if len(rows_data) == 0:
            assert False, "DT Session - No zia traceroute data found in traceroute.db"

        for data in rows_data:
            domain = data['domain']
            status = data['status']
            hopstodest = data['hopstodest']
            maxhopcount = data['maxhopcount']
            unresponsivehopcount = data['unresponsivehopcount']

            if status == 'Traceroute Stopped':
                continue

            self.logger_obj.info(f"DT Session - Validating values should be present and 'hopstodest' should"
                                 f" be less than maxhopcount and unresponsivehopcount' should be less than"
                                 f" equal to 'hopstodest' for probe = {current_row} ")

            if len(domain) == 0:
                flag = False
                msg = f"DT Session - No value found at 'domain' column in row = {current_row}"
                assert flag,msg

            if len(str(hopstodest)) == 0 or (len(str(hopstodest)) >= 1 and int(hopstodest) <= 0):
                flag = False
                msg = f"DT Session - No or Invalid value found at 'hopstodest' in row = {current_row}"
                assert flag, msg

            if len(str(maxhopcount)) == 0 or (len(str(maxhopcount)) >= 1 and int(maxhopcount) <= 0):
                flag = False
                msg = f"DT Session - No or Invalid value found at maxhopcount in row = {current_row}"
                assert flag, msg

            if len(str(unresponsivehopcount)) == 0 or (
                    len(str(unresponsivehopcount)) >= 1 and int(unresponsivehopcount) < 0):
                flag = False
                msg = f"DT Session - No or Invalid value found at maxhopcount in row = {current_row}"
                assert flag, msg

            if len(str(hopstodest)) >= 1 and len(str(maxhopcount)) >= 1 and int(hopstodest) > 40:
                flag = False
                msg = f"DT Session - The value of 'hopstodest' = {hopstodest} is greater than 40 in row = {current_row} which in Invalid"
                assert flag, msg

            if len(str(hopstodest)) >= 1 and len(str(maxhopcount)) >= 1 and (int(hopstodest) > int(maxhopcount)):
                flag = False
                msg = (f"DT Session - The value of 'hopstodest' = {hopstodest} is greater than"
                       f"maxhopcount = {maxhopcount} in row = {current_row} which in Incorrect")
                assert flag, msg

            if len(str(unresponsivehopcount)) >= 1 and len(str(hopstodest)) >= 1 and int(hopstodest) < int(
                    unresponsivehopcount):
                flag = False
                msg = f"DT Session - The value of 'unresponsivehopcount' is greater than 'hopstodest' in row = {current_row} which is inalid"
                assert flag, msg

            current_row += 1

        assert flag, msg

    @allure.title("DT Session - ZIA TRACEROUTE TUNNEL MODE: Verify totalloss and totallatency from traceroute db")
    @pytest.mark.xray(["QA-266962", "QA-266963", "QA-266964"])
    @add_markers("regression")
    def test_totalloss_totallatency_values_from_traceroute_lt_db(self):
        """
             DT Session - This function verifies that in Traceroute probes the value of 'totalloss'
             and 'totallatency' should not be invalid

             It performs the following steps:
             1. It is validating 'totalloss', and 'totallatency' values in traceroute db
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(
            "DT Session - ZIA TRACEROUTE TUNNEL MODE: Verifying 'totalloss' and 'totallatency' values from traceroute DB")

        query = f"SELECT * from trltmain WHERE domain='{self.zia_traceroute_domain}'"
        rows_data = self.db_handler.execute_query(query)
        current_row = 1
        flag = True
        msg = ""

        if len(rows_data) == 0:
            assert False, "DT Session - No zia traceroute data found in traceroute.db"

        for data in rows_data:
            status = data['status']
            domain = data['domain']
            totalloss = data['totalloss']
            totallatency = data['totallatency']

            if status == 'Traceroute Stopped':
                continue

            self.logger_obj.info(f"Validating values should be present for 'totalloss' and 'totallatency'"
                                 f" in the traceroute DB for current row = {current_row} ")

            if len(str(totalloss)) == 0 or int(totalloss) < -1 or int(totalloss) >= 101:
                flag = False
                msg = f"DT Session - No or invalid found at 'totalloss' = {totalloss} in row = {current_row}"
                break

            if len(str(totallatency)) == 0 or int(totallatency) < -1:
                flag = False
                msg = f"DT Session - No or value found at 'totallatency' = {totallatency} in row = {current_row}"
                break

            if int(totallatency) >= 201:
                self.logger_obj.warning(
                    f"DT Session - In row = {current_row}, the 'totallatency' value = {totallatency} is too high")

            current_row += 1

        assert flag, msg

    @allure.title("DT Session - ZIA TRACEROUTE TUNNEL MODE: Verify traceroute probe json from traceroute db")
    @pytest.mark.xray(["QA-266962", "QA-266963", "QA-266964"])
    @add_markers("regression")
    def test_zia_traceroute_probe_json_from_lt_db(self):
        """
             DT Session - This function verifies ZIA TR JSON of tunnel case (ZIA T1.0 mode).

             It performs the following steps:
             1. It is validating 'app', 'id', 'hints'
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        currentrow = 0
        flag = True
        msg = ""

        self.logger_obj.info("DT Session - ZIA TRACEROUTE TUNNEL MODE: Verify traceroute probe json from traceroute db")

        query = f"SELECT * from trltmain WHERE domain='{self.zia_traceroute_domain}'"
        rows_data = self.db_handler.execute_query(query)

        if len(rows_data) == 0:
             assert False, "DT Session - No zia traceroute data found in traceroute.db"

        for row in rows_data:
            currentrow += 1
            domain = row['domain']
            data = ""
            data = data + row['json']

            self.logger_obj.info(f"DT Session - Validating JSON format for traceroute_{currentrow}: {domain}")
            if "app" and "id" not in data:
                flag = False
                msg = f"DT Session - Invalid JSON format in traceroute json in row = {currentrow}"
                assert flag, msg

            try:
                json_data = json.loads(data)
            except json.JSONDecodeError as e:
                data = re.sub(r'([{,]\s*?"[^"]+"\s*:)\s*,', r'\1 null,', data)
                json_data = json.loads(data)  # {'A': 'a', 'B': None, 'C': 'c'}

            self.logger_obj.info(f"DT Session - Validating 'hints' for traceroute_{currentrow}: {domain}")
            if len(json_data['hints']) == 0 or json_data['hints'] is None:
                flag = False
                msg = f"DT Session - In row = {currentrow} under 'JSON' column, No legs are present in the 'Hints' section"
                assert flag, msg

            self.logger_obj.info(f"DT Session - Validating 'client_name', 'clt_ha_name','clt_ip','ecode',"
                                 f"'icode', 'etime' fields in json for traceroute_{currentrow}: {domain}")
            for i in json_data:
                if "client_name" in i or "clt_ha_name" in i or "ecode" in i or "erefid" in i:
                    if len(str(json_data[i])) == 0 or json_data[i] is None:
                        flag = False
                        msg = f"DT Session - Invalid or no value in trace json for = {i} in row = {currentrow} of traceroute db"
                        assert flag, msg

                if "clt_ip" in i or "icode" in i or "is_zpa" in i or "etime" in i:
                    if len(str(json_data[i])) == 0 or json_data[i] is None:
                        flag = False
                        msg = f"DT Session - Invalid or no value in trace json for = {i} in row = {currentrow} of traceroute db"
                        assert flag, msg

            data = ""
            traceroute_legs = json_data["legs"]
            first_leg_tunnel_type = traceroute_legs[0]['tunnel_type']

            if first_leg_tunnel_type not in [2, 14, 22]:
                flag = False
                msg = (f"DT Session - ZIA traceroute is not working fine for tunnel TUNNEL mode,"
                       f" showing incorrect tunnel_type = {first_leg_tunnel_type},"
                       f" it should be in ['2','14','22']")
                assert flag, msg

            for leg in traceroute_legs:
                source = leg['src']
                destination = leg['dst']
                dst_ip = leg['dst_ip']
                ecode = leg['ecode']
                loss = leg['loss']
                proto = leg['proto']
                src_ip = leg['src_ip']
                tunnel_type = leg['tunnel_type']
                num_hops = leg['num_hops']
                num_unresp_hops = leg['num_unresp_hops']

                self.logger_obj.info(f"DT Session - Validating 'src_ip' value in json for traceroute_{currentrow}: {domain}")
                if src_ip is None or len(str(src_ip)) == 0:
                    flag = False
                    msg = (f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'src_ip' value is missing")
                    assert flag, msg

                self.logger_obj.info(f"DT Session - Validating 'dts_ip' value in json for traceroute_{currentrow}: {domain}")
                if len(str(dst_ip)) == 0:
                    flag = False
                    msg = (f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'dst_ip' value is missing")
                    assert flag, msg
                else:
                    match = re.match(r"[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}", dst_ip)
                    if not bool(match):
                        flag = False
                        msg = (
                            f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                            f"'dst_ip' value is Invalid")
                        assert flag, msg

                self.logger_obj.info(f"DT Session - Validating 'ecode' value in json for traceroute_{currentrow}: {domain}")
                if ecode is None or len(str(ecode)) == 0:
                    flag = False
                    msg = (f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'ecode' value is missing")
                    assert flag, msg

                self.logger_obj.info(f"DT Session - Validating 'loss' value in json for traceroute_{currentrow}: {domain}")
                if loss is None or len(str(loss)) == 0:
                    flag = False
                    msg = (f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'loss' value is missing")
                    assert flag, msg

                self.logger_obj.info(f"DT Session - Validating 'protocol' value in json for traceroute_{currentrow}: {domain}")
                if proto is None or len(str(proto)) == 0:
                    flag = False
                    msg = (f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'proto' value is missing")
                    assert flag, msg

                self.logger_obj.info(
                    f"DT Session - Validating 'tunnel_type' value in json for traceroute_{currentrow}: {domain}")
                if tunnel_type is None or len(str(tunnel_type)) == 0:
                    flag = False
                    msg = (f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'tunnel_type' value is missing")
                    assert flag, msg

                self.logger_obj.info(f"DT Session - Validating 'num_hops' value in json for traceroute_{currentrow}: {domain}")
                if num_hops is None or len(str(num_hops)) == 0:
                    flag = False
                    msg = (f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'nums_hops' value is missing")
                    assert flag, msg

                self.logger_obj.info(
                    f"DT Session - Validating 'num_unresp_hops' value in json for traceroute_{currentrow}: {domain}")
                if num_unresp_hops is None or len(str(num_unresp_hops)) == 0:
                    flag = False
                    msg = (f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'nums_unresp_hops' value is missing")
                    assert flag, msg
            self.logger_obj.info(
                f"######################### Validated traceroute data for Row = {currentrow} #########################")

        assert flag, msg

    @allure.title("DT Session - Traceroute: Verify ZIA Traceroute tunnel Mode")
    @pytest.mark.xray(["QA-266962", "QA-266963", "QA-266964"])
    @add_markers("regression")
    def test_zia_lt_traceroute_probes_from_log(self):
        """
             This function test ZIA traceroute for tunnel mode for LT Traceroute probes

             Args:

             It performs the following steps:
             1. It is validating zia traceroute logs
                It will check service discovery response and validate it.
                It will check are we calculating computeDifferential
                It will check DBG TRACEROUTE:: Hops after adjustment Done
                It will check Saving response into the database
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        traceroute_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                plugin='traceroute')

        service_type = 1
        is_lt = 0

        try:
            service_type_map = {1: "ZIA_1_0", 2: "ZIA_2_0", 3: "ZPA"}
            sd_type = service_type_map.get(service_type, "")
            self.logger_obj.info(f"\n--- Traceroute: Verifying {sd_type} {'LT' if is_lt else 'Regular'} Probes ---\n")

            logs_map = {
                'start_tr_probe': "ZUpmTracerouteThread:: run - In",
                'regular_or_lt': "isLT",
                'service_type': "Service type",
                'zpa': "zpa",
                'sd_response': "SD response",
                'zen_request': "completed with status",
                'print_status': 'ZUpmTracerouteThread:: printStatus',
                'saving_data': "saveToDatabase",
                'run_out_probe': "ZUpmTracerouteThread:: run - Out",
            }

            issues = []
            active_threads = {}
            matching_probe_found = 0

            for line in traceroute_log_data.split("\n"):
                line_l = line.lower()

                if logs_map['start_tr_probe'].lower() in line_l:
                    match = re.search(r"\[(.*?)\]", line)
                    thread_id = match.group(1) if match else None
                    if not thread_id:
                        continue
                    if (is_lt == 0 and f"{logs_map['regular_or_lt']}=0".lower() in line_l) or (
                            is_lt == 1 and f"{logs_map['regular_or_lt']}=1".lower() in line_l):
                        if thread_id not in active_threads:
                            active_threads[thread_id] = {'run_in': 1, 'flag': 0, 'sd_response_found': 0,
                                                         'check_sd_url': 0, 'check_sd_ip': 0, 'check_sd_port': 0,
                                                         'check_sd_sme_ip': 0, 'check_sd_sme_port': 0,
                                                         'check_zen_request': 0,
                                                         'zen_response_code': 0, 'zen_request_text': '',
                                                         'check_print_status': 0, 'save_data_in_db': 0,
                                                         'save_data_text': '', 'sd_text': '', 'print_status_text': '',
                                                         'starting_text': line, 'is_zpa': False,
                                                         'matched_sd_type': False
                                                         }

                for thread_id in list(active_threads.keys()):
                    if thread_id not in line:
                        continue

                    flags = active_threads[thread_id]

                    if logs_map['service_type'].lower() in line_l:
                        if logs_map['zpa'].lower() in line_l:
                            flags['is_zpa'] = True
                        else:
                            flags['is_zpa'] = False

                        # Match correct sd_type
                        if sd_type.lower() in line_l:
                            flags['matched_sd_type'] = True
                            matching_probe_found += 1
                        else:
                            del active_threads[thread_id]
                            continue

                    if not flags.get('matched_sd_type'):
                        continue

                    if logs_map['sd_response'].lower() in line_l:
                        flags['flag'] = 1
                        flags['sd_response_found'] = 1
                        flags['sd_text'] = line
                        try:
                            flags['check_sd_url'] = bool(re.search(r"Url:\s*(\S+)(?=\s*(Success|$))", line))
                            flags['check_sd_ip'] = bool(
                                re.search(r"Destination Ips:\s*(\S+)(?=\s*(Destination Ports|$))", line))
                            flags['check_sd_port'] = bool(
                                re.search(r"Destination Ports:\s*(\S+)(?=\s*(SmeIp|$))", line))
                            flags['check_sd_sme_ip'] = bool(re.search(r"SmeIp:\s*(\S+)(?=\s*(SmePort|$))", line))
                            flags['check_sd_sme_port'] = bool(
                                re.search(r"SmePort:\s*(\S+)(?=\s*(SystemProxy|$))", line))
                            flags['check_broker_name'] = bool(
                                re.search(r"BrokerName:\s*(\S+)(?=\s*(BrokerIp|$))", line))
                            flags['check_broker_ip'] = bool(re.search(r"BrokerIp:\s*(\S+)(?=\s*(BindIp|$))", line))
                        except Exception as e:
                            self.logger_obj.error(f"[Thread ID: {thread_id}] Error parsing SD line: {line} - {e}")
                            continue

                    if ("exception" in line_l and "error" in line_l):
                        self.logger_obj.error(f"{line}")
                        issues.append(line)

                    if logs_map['zen_request'].lower() in line_l:
                        flags['check_zen_request'] = 1
                        flags['zen_request_text'] = line
                        match = re.search(r'\bstatus\s*(\d+)', line, re.IGNORECASE)
                        flags['zen_response_code'] = str(match.group(1)) if match else ''

                    if logs_map['print_status'].lower() in line_l:
                        if "success" in line_l:
                            flags['check_print_status'] = 1
                        flags['print_status_text'] = line

                    if logs_map['saving_data'].lower() in line_l:
                        flags['save_data_in_db'] = 1
                        flags['save_data_text'] = line

                    if logs_map['run_out_probe'].lower() in line_l:
                        issue_prefix = f"[Thread ID: {thread_id}]"
                        self.logger_obj.info(f"{flags['starting_text']}")
                        self.logger_obj.info(f"{flags['sd_text']}")
                        self.logger_obj.info(f"{issue_prefix} SD Response Found: {flags['sd_response_found']}")
                        if not flags['is_zpa']:
                            self.logger_obj.info(f"{flags['zen_request_text']}")

                        self.logger_obj.info(f"{flags['print_status_text']}")
                        self.logger_obj.info(f"{line}")

                        if not flags['is_zpa']:
                            flags['check_sd_sme_ip'] = True
                            flags['check_sd_sme_port'] = True
                            flags['check_zen_request'] = True
                        else:
                            flags['check_broker_name'] = True
                            flags['check_broker_ip'] = True

                        checks = [
                            ('check_sd_url', f"Missing SD URL. log - {flags['sd_text']}"),
                            ('check_sd_ip', f"Missing SD Destination Ip. log - {flags['sd_text']}"),
                            ('check_sd_port', f"Missing SD Destination Port. log - {flags['sd_text']}"),
                            ('check_sd_sme_ip', f"Missing SD SME IP. log - {flags['sd_text']}"),
                            ('check_sd_sme_port', f"Missing SD SME Port. log - {flags['sd_text']}"),
                            ('check_zen_request', "Zen Request failure."),
                            ('check_print_status',
                             f"The traceroute probe did not generate a 'Success' entry in the 'ZUpmTracerouteThread::printStatus' log line . Log : {flags['print_status_text']}"),
                            ('save_data_in_db', "Missing 'saveToDatabase - Saving response into the database'")
                        ]

                        if flags['is_zpa']:
                            checks.extend([
                                ('check_broker_name', f"Missing Broker Name. log - {flags['sd_text']}"),
                                ('check_broker_ip', f"Missing Broker IP. log - {flags['sd_text']}"),
                            ])

                        for item in checks:
                            flag_key = item[0]
                            msg = item[1]
                            condition = True
                            if len(item) == 3:
                                condition = item[2]
                            if not flags.get(flag_key, False) and condition:
                                self.logger_obj.info(f"{issue_prefix} {msg} Probe: {flags['starting_text']}")
                                issues.append(f"{issue_prefix} {msg} Probe: {flags['starting_text']}")

                        if not flags['is_zpa']:
                            if flags['zen_response_code'] and str(flags['zen_response_code']).startswith('4') or str(
                                    flags['zen_response_code']).startswith('5') or str(
                                    flags['zen_response_code']).startswith('-'):
                                self.logger_obj.error(
                                    f"{issue_prefix} Invalid ZEN response status: {flags['zen_response_code']}. Log: {flags['zen_request_text']}")
                                issues.append(
                                    f"{issue_prefix} Invalid ZEN response status: {flags['zen_response_code']}. Log: {flags['zen_request_text']}")

                        if not any(k for k in issues if k.startswith(issue_prefix)):
                            self.logger_obj.info(f"{issue_prefix} Probe Successful.")
                        self.logger_obj.info("-" * 120)
                        del active_threads[thread_id]

            if matching_probe_found == 0:
                self.logger_obj.error(
                    f"No {sd_type} {'LT' if is_lt else 'Regular'} probes found in the ZSAUpm_Traceroute logs.\n")
                pytest.skip(
                    f"No {sd_type} {'LT' if is_lt else 'Regular'} probes found in the ZSAUpm_Traceroute logs.\n")

            if issues:
                assert False, f"Validation failed for {sd_type} {'LT' if is_lt else 'Regular'} probes:\n" + "\n\n\n".join(
                    issues)

            msg = f"All {sd_type} {'LT' if is_lt else 'Regular'} probes validated successfully."
            self.logger_obj.info(msg)

        except Exception as e:
            self.logger_obj.error(f"Unexpected error in verify_traceroute_probes: {e}")
            assert False, f"Exception occurred during probe verification: {e}"

    @allure.title("DT session - Tunnel T1.0 Mode :- Verify data upload to the tpg for a TraceRoute")
    @pytest.mark.xray("QA-266965")
    @add_markers("regression")
    def test_lt_traceroute_data_upload_to_tpg(self):
        """
             This function test ZIA traceroute data upload to tpg for tunnel T1.0 mode for DT Session

             Args:

             It performs the following steps:
             1. It is validating upm logs to check if data is uploaded to tpg or not for DT Session
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """
        self.logger_obj.info("DT session - Tunnel T1.0 Mode :- Verify data upload to the tpg for a TraceRoute")
        upm_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')

        if len(upm_log_data)==0:
            self.logger_obj.info("Not able to fetch data from upm log file")
            assert False, "Not able to fetch data from upm log file"

        status = True
        msg = ""
        current_probe = 1

        # Get data from log from uploadJson
        final_json_data = self.log_handler.get_final_upload_json_from_upm_log(upm_log_data)
        if len(final_json_data)==0:
            assert False, "LT upload json data is not present in the ZSAUpm logs"

        for it in final_json_data:
            stime, etime = it[0], it[1]
            lt_session_id = it[2]
            upload_json = it[3]

            self.logger_obj.info(f"Validating traceroute data from ZLTU: Final JSON"
                                 f" upload string from upm file for probe = {current_probe} ")

            self.logger_obj.info(
                f"Validating mtr probes is present in Final json upload string for probe = {current_probe} "
                f"between stime = {stime} to etime = {etime} ")
            if upload_json.get("mtr") is None or upload_json["mtr"].get("probes") is None:
                status = False
                msg = (f"'mtr' invalid data or data is not uploaded to tpg for DT session from "
                       f"stime = {stime} to etime = {etime}")
                assert status, msg

            self.logger_obj.info(
                f"Validating cloud path probes is valid type in Final json upload string for probe = {current_probe} "
                f"between stime = {stime} to etime = {etime} ")
            app = upload_json['mtr']['probes'][0]

            probe_type = app["app"]["type"]
            if len(probe_type) == 0 or probe_type != "mtr":
                status = False
                msg = f"{probe_type} is not a valid type for cloud Path probe for DT session - Tunnel T1.0"
                assert status, msg

            current_probe += 1

        assert status, msg

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Closing db connection
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Closing db connection after verification")
        self.db_handler.close_db_connection()

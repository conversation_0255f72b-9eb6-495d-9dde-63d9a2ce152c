import math
import time, os
import pytest, allure
from datetime import datetime
from OS_Android.test_suite.zcc_zdx.mr_9366_deep_tracing.dt_01_zia_tunnel import conftest
from common_lib.Custom_Markers import add_markers

tbl_lt_disk_io_input = [pytest.param('free_mb', marks=pytest.mark.xray("QA-267008")),
                        pytest.param('pct_used', marks=pytest.mark.xray("QA-267009"))
                        ]

tbl_lt_cpu_usage_input = [pytest.param('pct_total', marks=pytest.mark.xray("QA-267006")),
                          pytest.param('pct_idle', marks=pytest.mark.xray("QA-267007"))
                          ]

tbl_lt_mem_usage_input = [pytest.param('free', marks=pytest.mark.xray("QA-267012")),
                          pytest.param('pct_used', marks=pytest.mark.xray("QA-267013")),
                          pytest.param('swap_used', marks=pytest.mark.xray("QA-267014")),
                          pytest.param('total', marks=pytest.mark.xray("QA-267015")),
                          pytest.param('used', marks=pytest.mark.xray("QA-267016"))
                          ]

tbl_lt_network_interface = [pytest.param('bandwidth_mbps', marks=pytest.mark.xray("QA-267017")),
                            pytest.param('recv_bps', marks=pytest.mark.xray("QA-267018")),
                            pytest.param('send_bps', marks=pytest.mark.xray("QA-267019"))
                            ]

tbl_lt_wifi_info = [pytest.param('rssi_dbm', marks=pytest.mark.xray("QA-267020")),
                    pytest.param('signal_quality', marks=pytest.mark.xray("QA-267021"))
                    ]


class TestLtDeviceStatsMinMaxAvg:

    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        3. Login to ZCC
        3. Creating DT session
        4. Export logs, Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.variables = conftest.variables
        self.logger_obj = conftest.logger_obj
        self.zdx_admin = conftest.zdx_admin
        self.session_duration = conftest.session_duration
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_device_stats.db"

        self.logger_obj.info("Creating db connection with device_stats db")
        self.db_handler.create_db_connection(self.db_path)

        # Fetching UPM log file data
        self.upm_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')
        if self.upm_log_data is None:
            self.logger_obj.info("Not able to fetch data from upm log file")
            assert False, "Not able to fetch data from upm log file"

        # Get final upload json data list from log uom logs for DT session
        self.final_json_data = self.log_handler.get_final_upload_json_from_upm_log(self.upm_log_data)

        #Automation is failing because of this bug, will remove the comment once that bug is resolved
        # For now i don't want because of 1bug i skip 15 regression cases

        # if len(self.final_json_data) != self.session_duration:
        #     assert False, f"Upload json data count = {len(self.final_json_data)} is not equal to LT session duration = {self.session_duration}"

    @allure.title(
        "Verify correctness of min/max/avg value of all the columns tbl_lt_disk_io table of upm_device_stats in upload json for DT session")
    @pytest.mark.parametrize("check_field", tbl_lt_disk_io_input)
    @add_markers("regression")
    def test_lt_verify_min_max_avg_disk_io_data(self, check_field):
        """
        This function Verify correctness of min/max/avg value of all the columns tbl_lt_disk_io table of upm_device_stats in upload json of DT session
        Args:
            check_field is the name of the field in Devicestats DB to check for min,max,avg in UploadLT json from upm logs
        """
        self.logger_obj.info("DT session: Verify correctness of min/max/avg value of all the"
                             " columns tbl_lt_disk_io table of upm_device_stats in upload json")
        status = True
        msg = ""
        upload_count = 0
        d_check_field = check_field
        u_check_field = check_field

        # Get final upload json data list from log uom logs for DT session
        for data in self.final_json_data:
            upload_json = data[3]
            stime = data[0]
            etime = data[1]

            try:
                startTime = stime
                endTime = etime

                query = (f'select min({d_check_field}), max({d_check_field}), avg({d_check_field}) from tbl_lt_disk_io'
                         f' where timestamp between {startTime} AND {endTime} and disk_name="Internal shared storage" ')
                res_device_stats = self.db_handler.execute_query(query)
                if len(res_device_stats) == 0:
                    self.logger_obj.error(
                        f"Query Failed to fetch device stats data between timestamp :{startTime} to {endTime}")
                    continue
                d_min_field, d_max_field, d_avg_field = res_device_stats[0]

                if d_min_field is None or d_max_field is None or d_avg_field is None:
                    self.logger_obj.info(
                        f"No Devicestats Disk IO data  found between stime:{startTime} etime:{endTime} ")
                    continue

                d_min_field = round(d_min_field, 2)
                d_max_field = round(d_max_field, 2)
                d_avg_field = round(d_avg_field, 2)
                upload_json_data = upload_json
                for probe in upload_json_data["apps"]["probes"]:
                    if probe["app"]["type"] != 'disk':
                        continue
                    s_time = probe["stime"]
                    e_time = probe["etime"]
                    if e_time - s_time > 60 or probe["stats"].get(f"{u_check_field}") is None:
                        continue
                    stats_data_from_upload = probe["stats"][f"{u_check_field}"]
                    u_avg_field = stats_data_from_upload["avg"]
                    u_min_field = stats_data_from_upload["min"]
                    u_max_field = stats_data_from_upload["max"]

                    self.logger_obj.info(
                        f"Verifying 'avg' value of field {u_check_field} between startTime = {startTime} to endTime= {endTime}")
                    self.logger_obj.info(f"DB avg: {d_avg_field}, Upload JSON avg: {u_avg_field}")
                    if round(abs(u_avg_field - d_avg_field), 2) > 0.01:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_avg_field}::{d_avg_field}] Diff :{round(abs(u_avg_field - d_avg_field), 3)}"
                            f" Avg disk io field : {u_check_field} not matching bw startTime = {startTime} and endTime = {endTime}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"Verifying 'max' value of field {u_check_field} between startTime = {startTime} to endTime= {endTime}")
                    self.logger_obj.info(f"DB max: {d_max_field}, Upload JSON max: {u_max_field}")
                    if round(abs(u_max_field - d_max_field), 2) > 0.01:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_max_field}::{d_max_field}] Diff :{round(abs(u_max_field - d_max_field), 3)}"
                            f" Max disk io field : {u_check_field} not matching bw startTime = {startTime} and endTime = {endTime}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"Verifying 'min' value of field {u_check_field} between startTime = {startTime} to endTime= {endTime}")
                    self.logger_obj.info(f"DB min: {d_min_field}, Upload JSON min: {u_min_field}")
                    if round(abs(u_min_field - d_min_field), 2) > 0.01:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_min_field}::{d_min_field}] Diff :{round(abs(u_min_field - d_min_field), 3)}"
                            f" Min disk io field : {u_check_field} not matching bw startTime = {startTime} and endTime = {endTime}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"##################  Verified min/max/avg of {u_check_field} between startTime = {startTime} to endTime= {endTime} ##################")

            except Exception as e:
                self.logger_obj.info(f"Unexpected Error occurred :{e}")
                status = False
                msg = f"Unexpected Error occurred :{e}"
                break

        assert status, msg

    @allure.title(
        "Verify correctness of min/max/avg value of tbl_lt_battery_status table of upm_device_stats in upload json for DT session")
    @pytest.mark.xray("QA-267005")
    @add_markers("regression")
    def test_lt_verify_min_max_avg_tbl_lt_battery_status(self):
        """
        This function Verify correctness of min/max/avg value of all the columns tbl_lt_battery_status
        able of upm_device_stats in upload json of DT session

        """

        self.logger_obj.info("DT session: Verify correctness of min/max/avg value of"
                             " tbl_lt_battery_status table of upm_device_stats in upload json")
        status = True
        msg = ""
        d_check_field = 'level_pct'
        u_check_field = 'level_pct'

        for data in self.final_json_data:
            upload_json = data[3]
            stime = data[0]
            etime = data[1]

            try:
                start_time = stime
                end_time = etime

                query = (f'select min({d_check_field}), max({d_check_field}), avg({d_check_field}) from '
                         f'tbl_lt_battery_status where timestamp between {start_time} AND {end_time}')
                res_device_stats = self.db_handler.execute_query(query)
                if len(res_device_stats) == 0:
                    self.logger_obj.error(
                        f"Query Failed to fetch device stats data between timestamp :{start_time} to {end_time}")
                    continue
                d_min_field, d_max_field, d_avg_field = res_device_stats[0]

                if d_min_field is None or d_max_field is None or d_avg_field is None:
                    self.logger_obj.info(
                        f"No Devicestats Battery data found between stime:{start_time} etime:{end_time} ")
                    continue

                d_min_field = round(d_min_field, 2)
                d_max_field = round(d_max_field, 2)
                d_avg_field = round(d_avg_field, 2)
                upload_json_data = upload_json
                for probe in upload_json_data["apps"]["probes"]:
                    if probe["app"]["type"] != 'battery':
                        continue
                    s_time = probe["stime"]
                    e_time = probe["etime"]
                    if e_time - s_time > 60 or probe["stats"].get(f"{u_check_field}") is None:
                        continue
                    stats_data_from_upload = probe["stats"][f"{u_check_field}"]
                    u_avg_field = stats_data_from_upload["avg"]
                    u_min_field = stats_data_from_upload["min"]
                    u_max_field = stats_data_from_upload["max"]

                    self.logger_obj.info(
                        f"Verifying 'avg' value of field {u_check_field} between startTime = {start_time} to endTime= {end_time}")
                    self.logger_obj.info(f"DB avg: {d_avg_field}, Upload JSON avg: {u_avg_field}")
                    if u_avg_field != d_avg_field:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_avg_field}::{d_avg_field}] Diff :{abs(u_avg_field - d_avg_field)}"
                            f" Avg level_pct field : {u_check_field} not matching bw startTime = {start_time} and endTime = {end_time}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"Verifying 'max' value of field {u_check_field} between startTime = {start_time} to endTime= {end_time}")
                    self.logger_obj.info(f"DB max: {d_max_field}, Upload JSON max: {u_max_field}")
                    if u_max_field != d_max_field:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_max_field}::{d_max_field}] Diff :{abs(u_max_field - d_max_field)}"
                            f" Max level_pct field : {u_check_field} not matching bw startTime = {start_time} and endTime = {end_time}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"Verifying 'min' value of field {u_check_field} between startTime = {start_time} to endTime= {end_time}")
                    self.logger_obj.info(f"DB min: {d_min_field}, Upload JSON min: {u_min_field}")
                    if u_min_field != d_min_field:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_min_field}::{d_min_field}] Diff :{abs(u_min_field - d_min_field)}"
                            f" Min level_pct field : {u_check_field} not matching bw startTime = {start_time} and endTime = {end_time}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"##################  Verified min/max/avg of {u_check_field} between startTime = {start_time} to endTime= {end_time} ##################")

            except Exception as e:
                self.logger_obj.info(f"Unexpected Error occurred :{e}")
                status = False
                msg = f"Unexpected Error occurred :{e}"
                break

        assert status, msg

    @allure.title("Verify correctness of min/max/avg value of tbl_lt_cpu_usage"
                  " table of upm_device_stats in upload json for DT session")
    @pytest.mark.parametrize("check_field", tbl_lt_cpu_usage_input)
    @add_markers("regression")
    def test_lt_verify_min_max_avg_tbl_lt_cpu_usage(self, check_field):
        """
        This function Verify correctness of min/max/avg value of all the columns tbl_lt_cpu_usage table of upm_device_stats in upload json of DT session
        Args:
            check_field is the name of the field in Devicestats DB to check for min,max,avg in UploadLT json from upm logs

        """

        self.logger_obj.info("DT session: Verify correctness of min/max/avg value of"
                             " tbl_lt_cpu_usage table of upm_device_stats in upload json")
        status = True
        msg = ""
        d_check_field = check_field
        u_check_field = check_field

        for data in self.final_json_data:
            upload_json = data[3]
            stime = data[0]
            etime = data[1]

            try:
                start_time = stime
                end_time = etime

                query = (f'select min({d_check_field}), max({d_check_field}), avg({d_check_field}) from '
                         f'tbl_lt_cpu_usage where timestamp between {start_time} AND {end_time}')
                res_device_stats = self.db_handler.execute_query(query)
                if len(res_device_stats) == 0:
                    self.logger_obj.error(
                        f"Query Failed to fetch device stats data between timestamp :{start_time} to {end_time}")
                    continue
                d_min_field, d_max_field, d_avg_field = res_device_stats[0]

                if d_min_field is None or d_max_field is None or d_avg_field is None:
                    self.logger_obj.info(
                        f"No Devicestats CPU usage data found between stime:{start_time} etime:{end_time} ")
                    continue

                d_min_field = round(d_min_field, 2)
                d_max_field = round(d_max_field, 2)
                d_avg_field = round(d_avg_field, 2)
                upload_json_data = upload_json
                for probe in upload_json_data["apps"]["probes"]:
                    if probe["app"]["type"] != 'cpu':
                        continue
                    s_time = probe["stime"]
                    e_time = probe["etime"]
                    if e_time - s_time > 60 or probe["stats"].get(f"{u_check_field}") is None:
                        continue
                    stats_data_from_upload = probe["stats"][f"{u_check_field}"]
                    u_avg_field = stats_data_from_upload["avg"]
                    u_min_field = stats_data_from_upload["min"]
                    u_max_field = stats_data_from_upload["max"]

                    self.logger_obj.info(
                        f"Verifying 'avg' value of field {u_check_field} between startTime = {start_time} to endTime= {end_time}")
                    self.logger_obj.info(f"DB avg: {d_avg_field}, Upload JSON avg: {u_avg_field}")
                    if round(u_avg_field - d_avg_field, 2) > 0.01:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_avg_field}::{d_avg_field}] Diff :{abs(u_avg_field - d_avg_field)}"
                            f" Avg field : {u_check_field} not matching bw startTime = {start_time} and endTime = {end_time}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"Verifying 'max' value of field {u_check_field} between startTime = {start_time} to endTime= {end_time}")
                    self.logger_obj.info(f"DB max: {d_max_field}, Upload JSON max: {u_max_field}")
                    if round(u_max_field - d_max_field, 2) > 0.01:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_max_field}::{d_max_field}] Diff :{abs(u_max_field - d_max_field)}"
                            f" Max field : {u_check_field} not matching bw startTime = {start_time} and endTime = {end_time}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"Verifying 'min' value of field {u_check_field} between startTime = {start_time} to endTime= {end_time}")
                    self.logger_obj.info(f"DB min: {d_min_field}, Upload JSON min: {u_min_field}")
                    if round(u_min_field - d_min_field, 2) > 0.01:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_min_field}::{d_min_field}] Diff :{abs(u_min_field - d_min_field)}"
                            f" Min field : {u_check_field} not matching bw startTime = {start_time} and endTime = {end_time}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"##################  Verified min/max/avg of {u_check_field} between startTime = {start_time} to endTime= {end_time} ##################")

            except Exception as e:
                self.logger_obj.info(f"Unexpected Error occurred :{e}")
                status = False
                msg = f"Unexpected Error occurred :{e}"
                break

        assert status, msg

    @allure.title("Verify correctness of min/max/avg value of tbl_lt_memory_usage"
                  " table of upm_device_stats in upload json for DT session")
    @pytest.mark.parametrize("check_field", tbl_lt_mem_usage_input)
    @add_markers("regression")
    def test_lt_verify_min_max_avg_tbl_lt_memory_usage(self, check_field):
        """
        This function Verify correctness of min/max/avg value of all the columns tbl_lt_memory_usage table of upm_device_stats in upload json of DT session
        Args:
            check_field is the name of the field in Devicestats DB to check for min,max,avg in UploadLT json from upm logs

        """

        self.logger_obj.info("DT session: Verify correctness of min/max/avg value of"
                             " tbl_lt_memory_usage table of upm_device_stats in upload json")
        status = True
        msg = ""
        d_check_field = check_field
        u_check_field = check_field

        for data in self.final_json_data:
            upload_json = data[3]
            stime = data[0]
            etime = data[1]

            try:
                start_time = stime
                end_time = etime

                query = (f'select min({d_check_field}), max({d_check_field}), avg({d_check_field}) from '
                         f'tbl_lt_memory_usage where timestamp between {start_time} AND {end_time}')
                res_device_stats = self.db_handler.execute_query(query)
                if len(res_device_stats) == 0:
                    self.logger_obj.error(
                        f"Query Failed to fetch device stats data between timestamp :{start_time} to {end_time}")
                    continue
                d_min_field, d_max_field, d_avg_field = res_device_stats[0]

                if d_min_field is None or d_max_field is None or d_avg_field is None:
                    self.logger_obj.info(
                        f"No Devicestats Memory usage data found between stime:{start_time} etime:{end_time} ")
                    continue

                d_min_field = round(d_min_field, 2)
                d_max_field = round(d_max_field, 2)
                d_avg_field = round(d_avg_field, 2)
                upload_json_data = upload_json
                for probe in upload_json_data["apps"]["probes"]:
                    if probe["app"]["type"] != 'mem':
                        continue
                    s_time = probe["stime"]
                    e_time = probe["etime"]
                    if e_time - s_time > 60 or probe["stats"].get(f"{u_check_field}") is None:
                        continue
                    stats_data_from_upload = probe["stats"][f"{u_check_field}"]
                    u_avg_field = stats_data_from_upload["avg"]
                    u_min_field = stats_data_from_upload["min"]
                    u_max_field = stats_data_from_upload["max"]

                    self.logger_obj.info(
                        f"Verifying 'avg' value of field {u_check_field} between startTime = {start_time} to endTime= {end_time}")
                    self.logger_obj.info(f"DB avg: {d_avg_field}, Upload JSON avg: {u_avg_field}")
                    if u_avg_field != d_avg_field:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_avg_field}::{d_avg_field}] Diff :{abs(u_avg_field - d_avg_field)}"
                            f" Avg field : {u_check_field} not matching bw startTime = {start_time} and endTime = {end_time}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"Verifying 'max' value of field {u_check_field} between startTime = {start_time} to endTime= {end_time}")
                    self.logger_obj.info(f"DB max: {d_max_field}, Upload JSON max: {u_max_field}")
                    if u_max_field != d_max_field:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_max_field}::{d_max_field}] Diff :{abs(u_max_field - d_max_field)}"
                            f" Max field : {u_check_field} not matching bw startTime = {start_time} and endTime = {end_time}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"Verifying 'min' value of field {u_check_field} between startTime = {start_time} to endTime= {end_time}")
                    self.logger_obj.info(f"DB min: {d_min_field}, Upload JSON min: {u_min_field}")
                    if u_min_field != d_min_field:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_min_field}::{d_min_field}] Diff :{abs(u_min_field - d_min_field)}"
                            f" Min field : {u_check_field} not matching bw startTime = {start_time} and endTime = {end_time}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"##################  Verified min/max/avg {u_check_field} between startTime = {start_time} to endTime= {end_time} ##################")

            except Exception as e:
                self.logger_obj.info(f"Unexpected Error occurred :{e}")
                status = False
                msg = f"Unexpected Error occurred :{e}"
                break

        assert status, msg

    @allure.title("Verify correctness of min/max/avg value of tbl_lt_network_interface"
                  " table of upm_device_stats in upload json for DT session")
    @pytest.mark.parametrize("check_field", tbl_lt_network_interface)
    @add_markers("regression")
    def test_lt_verify_min_max_avg_tbl_lt_network_interface(self, check_field):
        """
        This function Verify correctness of min/max/avg value of all the columns tbl_lt_network_interface table of upm_device_stats in upload json of DT session
        Args:
            check_field is the name of the field in Devicestats DB to check for min,max,avg in UploadLT json from upm logs

        """

        self.logger_obj.info("DT session: Verify correctness of min/max/avg value of"
                             " tbl_lt_network_interface table of upm_device_stats in upload json")
        status = True
        msg = ""
        d_check_field = check_field
        u_check_field = check_field

        for data in self.final_json_data:
            upload_json = data[3]
            stime = data[0]
            etime = data[1]

            try:
                start_time = stime
                end_time = etime

                query = (f'select min({d_check_field}), max({d_check_field}), avg({d_check_field}) from '
                         f'tbl_lt_network_interface where timestamp between {start_time} AND {end_time}')
                res_device_stats = self.db_handler.execute_query(query)
                if len(res_device_stats) == 0:
                    self.logger_obj.error(
                        f"Query Failed to fetch device stats data between timestamp :{start_time} to {end_time}")
                    continue
                d_min_field, d_max_field, d_avg_field = res_device_stats[0]

                if d_min_field is None or d_max_field is None or d_avg_field is None:
                    self.logger_obj.info(
                        f"No Devicestats tbl_lt_network_interface data found between stime:{start_time} etime:{end_time} ")
                    continue

                d_min_field = round(d_min_field, 2)
                d_max_field = round(d_max_field, 2)
                d_avg_field = round(d_avg_field, 2)
                upload_json_data = upload_json
                for probe in upload_json_data["apps"]["probes"]:
                    if probe["app"]["type"] != 'ifstat':
                        continue
                    s_time = probe["stime"]
                    e_time = probe["etime"]
                    if e_time - s_time > 60 or probe["stats"].get(f"{u_check_field}") is None:
                        continue
                    stats_data_from_upload = probe["stats"][f"{u_check_field}"]
                    u_avg_field = stats_data_from_upload["avg"]
                    u_min_field = stats_data_from_upload["min"]
                    u_max_field = stats_data_from_upload["max"]

                    self.logger_obj.info(
                        f"Verifying 'avg' value of field {u_check_field} between startTime = {start_time} to endTime= {end_time}")
                    self.logger_obj.info(f"DB avg: {d_avg_field}, Upload JSON avg: {u_avg_field}")
                    if round(u_avg_field - d_avg_field, 2) > 0.01:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_avg_field}::{d_avg_field}] Diff :{abs(u_avg_field - d_avg_field)}"
                            f" Avg field : {u_check_field} not matching bw startTime = {start_time} and endTime = {end_time}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"Verifying 'max' value of field {u_check_field} between startTime = {start_time} to endTime= {end_time}")
                    self.logger_obj.info(f"DB max: {d_max_field}, Upload JSON max: {u_max_field}")
                    if round(u_max_field - d_max_field, 2) > 0.01:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_max_field}::{d_max_field}] Diff :{abs(u_max_field - d_max_field)}"
                            f" Max field : {u_check_field} not matching bw startTime = {start_time} and endTime = {end_time}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"Verifying 'min' value of field {u_check_field} between startTime = {start_time} to endTime= {end_time}")
                    self.logger_obj.info(f"DB min: {d_min_field}, Upload JSON min: {u_min_field}")
                    if round(u_min_field - d_min_field, 2) > 0.01:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_min_field}::{d_min_field}] Diff :{abs(u_min_field - d_min_field)}"
                            f" Min field : {u_check_field} not matching bw startTime = {start_time} and endTime = {end_time}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"##################  Verified min/max/avg of {u_check_field} between startTime = {start_time} to endTime= {end_time} ##################")

            except Exception as e:
                self.logger_obj.info(f"Unexpected Error occurred :{e}")
                status = False
                msg = f"Unexpected Error occurred :{e}"
                break

        assert status, msg

    @allure.title("Verify correctness of min/max/avg value of tbl_lt_wifi_info"
                  " table of upm_device_stats in upload json for DT session")
    @pytest.mark.parametrize("check_field", tbl_lt_wifi_info)
    @add_markers("regression")
    def test_lt_verify_min_max_avg_tbl_lt_wifi_info(self, check_field):
        """
        This function Verify correctness of min/max/avg value of all the columns tbl_lt_wifi_info table of upm_device_stats in upload json of DT session
        Args:
            check_field is the name of the field in Devicestats DB to check for min,max,avg in UploadLT json from upm logs

        """

        self.logger_obj.info("DT session: Verify correctness of min/max/avg value of"
                             " tbl_lt_wifi_info table of upm_device_stats in upload json")
        status = True
        msg = ""
        d_check_field = check_field
        u_check_field = check_field

        for data in self.final_json_data:
            upload_json = data[3]
            stime = data[0]
            etime = data[1]

            try:
                start_time = stime
                end_time = etime

                query = (f'select min({d_check_field}), max({d_check_field}), avg({d_check_field}) from '
                         f'tbl_lt_wifi_info where timestamp between {start_time} AND {end_time}')
                res_device_stats = self.db_handler.execute_query(query)
                if len(res_device_stats) == 0:
                    self.logger_obj.error(
                        f"Query Failed to fetch device stats data between timestamp :{start_time} to {end_time}")
                    continue
                d_min_field, d_max_field, d_avg_field = res_device_stats[0]

                if d_min_field is None or d_max_field is None or d_avg_field is None:
                    self.logger_obj.info(
                        f"No Devicestats tbl_lt_wifi_info data found between stime:{start_time} etime:{end_time} ")
                    continue

                d_min_field = math.ceil(d_min_field)
                d_max_field = math.ceil(d_max_field)
                d_avg_field = math.ceil(d_avg_field)
                upload_json_data = upload_json
                for probe in upload_json_data["apps"]["probes"]:
                    if probe["app"]["type"] != 'wifi':
                        continue
                    s_time = probe["stime"]
                    e_time = probe["etime"]
                    if e_time - s_time > 60 or probe["stats"].get(f"{u_check_field}") is None:
                        continue
                    stats_data_from_upload = probe["stats"][f"{u_check_field}"]
                    u_avg_field = stats_data_from_upload["avg"]
                    u_min_field = stats_data_from_upload["min"]
                    u_max_field = stats_data_from_upload["max"]

                    self.logger_obj.info(
                        f"Verifying 'avg' value of field {u_check_field} between startTime = {start_time} to endTime= {end_time}")
                    self.logger_obj.info(f"DB avg: {d_avg_field}, Upload JSON avg: {u_avg_field}")
                    if round(abs(u_avg_field - d_avg_field), 2) > 0.01:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_avg_field}::{d_avg_field}] Diff :{abs(u_avg_field - d_avg_field)}"
                            f" Avg disk io field : {u_check_field} not matching bw startTime = {start_time} and endTime = {end_time}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"Verifying 'max' value of field {u_check_field} between startTime = {start_time} to endTime= {end_time}")
                    self.logger_obj.info(f"DB max: {d_max_field}, Upload JSON max: {u_max_field}")
                    if round(abs(u_max_field - d_max_field), 2) > 0.01:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_max_field}::{d_max_field}] Diff :{abs(u_max_field - d_max_field)}"
                            f" Max disk io field : {u_check_field} not matching bw startTime = {start_time} and endTime = {end_time}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"Verifying 'min' value of field {u_check_field} between startTime = {start_time} to endTime= {end_time}")
                    self.logger_obj.info(f"DB min: {d_min_field}, Upload JSON min: {u_min_field}")
                    if round(abs(u_min_field - d_min_field), 2) > 0.01:
                        msg = (
                            f"[UPLOAD_LT_JSON::DEVICE_DB] [{u_min_field}::{d_min_field}] Diff :{abs(u_min_field - d_min_field)}"
                            f" Min disk io field : {u_check_field} not matching bw startTime = {start_time} and endTime = {end_time}")
                        self.logger_obj.error(msg)
                        assert False, msg

                    self.logger_obj.info(
                        f"##################  Verified min/max/avg of {u_check_field} between startTime = {start_time} to endTime= {end_time}##################")

            except Exception as e:
                self.logger_obj.info(f"Unexpected Error occurred :{e}")
                status = False
                msg = f"Unexpected Error occurred :{e}"
                break

        assert status, msg

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Closing db connection
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Closing db connection after verification")
        self.db_handler.close_db_connection()

import time, os
import pytest, allure
import json, re
import datetime
from OS_Android.test_suite.zcc_zdx.mr_9366_deep_tracing.dt_01_zia_tunnel import conftest
from common_lib.Custom_Markers import add_markers
from common_lib.helper_function import help_return

policy_search_pattern = [
    pytest.param('"type": "WEB"', "Webload", marks=pytest.mark.xray("QA-266930")),
    pytest.param('"type": "TRACERT"', "Traceroute", marks=pytest.mark.xray("QA-266931")),
    pytest.param('"type": "DEVICESTATS"', "DeviceStats", marks=pytest.mark.xray("QA-266932"))
]


class TestDeeptracingPolicy:
    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.variables = conftest.variables
        self.logger_obj = conftest.logger_obj
        self.zdx_admin = conftest.zdx_admin
        self.session_duration = conftest.session_duration

    @allure.title("Verify that the LT monitors start immediately after the LT session arrived in the policy and"
                  " there should not be a significant delay")
    @pytest.mark.xray("QA-266936")
    @add_markers("regression")
    def test_lt_starts_immediately_after_lt_policy_received(self):
        """
             This function Verify that zapp starts lt session right after policy recieved
             There should not be any significant delay

             Args:

             It performs the following steps:
             1. It will check the response code when user creates a new DT session while device
                is already being monitored by another Deep Tracing session
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Verifying that lt_starts_immediately_after_lt_policy_received from upm logs")

        status = True
        msg = ""

        self.logger_obj.info("Fetching upm log file data to validate timestamp")
        upm_log_file_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')
        if upm_log_file_data is None:
            status = False
            msg = "Not able to fetch UPM log file data"
        assert status, msg

        self.logger_obj.info("Searching for 'INF Process CREATED LT sessions' line from upm log file")
        pattern_to_search = "INF Process CREATED LT sessions"
        result = self.log_handler.find_log_line_timestamp_in_unix(upm_log_file_data, pattern=pattern_to_search)
        if len(result) == 0:
            msg = f"Unable to find created logs line = {pattern_to_search} for DT session start"
            status = False
        assert status, msg
        time_dt_created = result[0]

        # First payload sent will be immediately after start of LT session (3-4 seconds buffer )
        self.logger_obj.info("Searching for 'LT payload sent to TPG successfully' line from upm log file")
        pattern_to_search = "LT payload sent to TPG successfully"
        result = self.log_handler.find_log_line_timestamp_in_unix(upm_log_file_data, pattern=pattern_to_search)
        if len(result) == 0:
            msg = f"Unable to find start logs line = {pattern_to_search} for DT session start"
            status = False
        assert status, msg
        time_dt_started = result[0]
        difference = time_dt_started - time_dt_created

        self.logger_obj.info("Checking if ZLT: Payload sent to TPG is without any significant delay")
        if difference >= 5:
            status = False
            msg = (f"DT session is not started within time, Create and Start time have delay of"
                   f" {difference}s which is not expected. ")

        assert status, msg

    @allure.title("LT session - Verify that the LT policy will be pooled at an interval of 15 seconds")
    @pytest.mark.xray("QA-266940")
    @add_markers("regression")
    def test_lt_policy_pooled_at_15s_from_upm_log(self):
        """
             This function Verify that the LT policy will be pooled at an interval of 15 seconds from upm logs

             Args:

             It performs the following steps:
             1. It will check LT policy from upm logs files and time difference between policy fetch for LT session
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        status = True
        msg = ""
        self.logger_obj.info("Fetching upm log file data to validate timestamp")
        upm_log_file_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')
        if upm_log_file_data is None:
            status = False
            msg = "Not able to fetch UPM log file data"
        assert status, msg

        self.logger_obj.info("Verifying that the LT policy will be pooled at an interval of 15 seconds from upm logs")
        pattern_to_search = "ZPD: Received LT session"
        result = self.log_handler.find_log_line_timestamp_in_unix(upm_log_file_data, pattern=pattern_to_search)

        status = True
        msg = ""

        # Check if any timestamps were found
        if len(result) == 0:
            status = False
            msg = "No LT policy interval fetched from logs"

        # Skipping first data as first LT policy can arrive at any time interval, after that we will have 15s gaps
        # Check if the time difference between timestamps is 15 seconds
        for index in range(2, len(result)):
            prev_timestamp = result[index - 1]
            curr_timestamp = result[index]

            time_difference = abs(curr_timestamp - prev_timestamp)
            if time_difference > 17:
                status = False
                msg = (f"Time difference between two policy downloads is {time_difference}"
                       f" which is incorrect, it should be 15 seconds")
                break

        assert status, msg

    @allure.title("LT session - Verify that zapp runs only one LT session at a time on the user device")
    @pytest.mark.xray("QA-266942")
    @add_markers("regression")
    def test_lt_run_one_session_at_a_time_for_same_user(self):
        """
             This function Verify that zapp runs only one LT session at a time on the user device

             Args:

             It performs the following steps:
             1. It will check the response code when user creates a new DT session while device
                is already being monitored by another Deep Tracing session
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("Validating Status code and Failure reason when user created another DT session"
                             " while device is already being monitored by another Deep Tracing session")

        pytest.skip("Needs rework, will update in next PR")

        # dt_session_failure_code = self.zdx_admin2.dtsessionfailreason['fail_status_code']
        # dt_session_failure_reason = self.zdx_admin2.dtsessionfailreason['fail_reason']
        #
        # # Check if the DT session failed with a 400 status code, Failure expected
        # if dt_session_failure_code == 400:
        #     self.logger_obj.info(f"DT session failed - {dt_session_failure_code} - {dt_session_failure_reason}\n")
        #
        # return dt_session_failure_code == 400, dt_session_failure_reason

    @allure.title("LT session - Verify that for LT session upload a persistent connection and"
                  " for regular session uploads a new connection to TPG is used")
    @pytest.mark.xray("QA-266939")
    @add_markers("regression")
    def test_lt_session_use_persistent_connection_for_data_upload_to_tpg(self):
        """
             This function verifies that for LT session upload a persistent connection
             and for regular session uploads a new connection to TPG is used

             Args:

             It performs the following steps:
             1. LT session - Verify that for LT session upload a persistent connection and
                for regular session uploads a new connection to TPG is used
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("Validating that for LT session upload a persistent connection")
        result = self.log_handler.check_lt_session_uses_persistent_tpg_connection_on_each_upload(self.session_duration)
        assert result[0], result[1]

        self.logger_obj.info("Validating that for regular session uploads a new connection to TPG is used")
        result = self.log_handler.check_regular_session_creating_new_tpg_session_on_each_upload()
        assert result[0], result[1]

    @allure.title("Verify that when dt session ends then COMPLETED status will be returned to TPG")
    @pytest.mark.xray("QA-266985")
    @add_markers("regression")
    def test_lt_completed_status_send_to_tpg(self):
        """
             This function verifies that for LT session sends completed status to the tpg
             Once dt session ends gracefully (no abort)

             Args:

             It performs the following steps:
             1. LT session - Verify ZLT: Payload sent to TPG = {"LTs":[{"id":128211725311751,"state":"STARTED","zapp_initiated_ts":1725311772}]}
                when dt sessions ends gracefully, id and time is dynamic
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("Validating lt completed status sent to the tpg")

        status = False
        msg = "Sending Completed status to the tpg Failed"
        log_to_find = "ZTCM: getLTPayloadToTpgInJsonStr: Payload will be sent to TPG"

        self.logger_obj.info("Fetching upm log file data to validate timestamp")
        upm_log_file_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')
        if upm_log_file_data is None:
            status = False
            msg = "Not able to fetch UPM log file data"
            assert status, msg

        pattern_matched_log_lines = self.log_handler.find_log(data=upm_log_file_data, log_to_check=log_to_find)

        # Checking if ZLT: Payload sent to TPG log line is present or not in upm log
        if len(pattern_matched_log_lines) == 0:
            assert False, f"Unable to find log line in the upm logs = {log_to_find}"

        # Checking if completed state is present or not in the tpg upload payload
        for lines in pattern_matched_log_lines:
            if '"state":"COMPLETED"' in lines:
                status = True
                break

        assert status, msg

    @allure.title("Verify that when an LT session arrives in the policy zapp will not restart any of the plugins and "
                  "the regular sessions continues without any interruption")
    @pytest.mark.xray("QA-266938")
    @add_markers("regression")
    def test_lt_policy_dont_restart_regular_plugins(self):
        """
             This function verifies that we don't restart regular plugin when lT arrives in the
             policy

             Args:

             It performs the following steps:
             1.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("Verify that when an LT session arrives in the policy zapp will not restart any"
                             " of the plugins and  the regular sessions continues without any interruption")
        status = True
        msg = ""
        stop_pattern = ["ZPC: Stop Regular Sessions: In",
                       "Device Stats Plugin stopped successfully",
                       "Webprobe Plugin stopped successfully",
                       "Traceroute Plugin stopped successfully",
                      "Stop Regular Sessions: Out"
                      ]

        latest_upm_file = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['upm'][-1]


        self.logger_obj.info("Validating zapp will not restart any of the plugins and"
                             " the regular sessions continues without any interruption")
        for pattern in stop_pattern:
            result = help_return(self.log_obj.search_log_file, file=latest_upm_file,directory=self.const_obj.LOG_PATH,
                                 words_to_find_in_line=pattern,start_line= "Process CREATED LT sessions",failure_expected= True)
            if not result[0]:
                status = False
                msg = result[1]
                break

        assert status, msg

    @allure.title("LT session - Verify that the LT stats upload contains the data from"
                  " one interval for all the plugins")
    @pytest.mark.xray(["QA-267001", "QA-266976", "QA-266977", "QA-266978", "QA-267002"])
    @add_markers("regression")
    def test_lt_data_upload_for_all_plugin(self):
        """
             This function verifies that for LT session sends data for
             all the 3 plugin (webload, traceroute, device_stats) in each payload
             which is every 1min

             Args:

             It performs the following steps:
             1. It will fetch payload json from upm log file and reads the content of the upload json
                Verifies if it contains data from each of 3 plugin mentioned above
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        status = True
        msg = ""
        session_duration = self.session_duration

        self.logger_obj.info("Validating data upload to the tpg for each of the plugin from LT upload payload json")

        # Fetching UPM log file data
        upm_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')
        if upm_log_data is None:
            self.logger_obj.info("Not able to fetch data from upm log file")
            assert False, "Not able to fetch data from upm log file"

        # Get final upload json data list from log uom logs for DT session
        final_json_data_status = self.log_handler.get_final_upload_json_from_upm_log(upm_log_data)
        if len(final_json_data_status) == 0:
            assert False, "Upload json data is not present for LT session in upm log file"

        self.logger_obj.info(f"Validating {session_duration} mins DT session"
                             f" will have {session_duration} tpg upload")

        msg = "Upm logs is not having all ZLTU: Final JSON upload string"
        assert session_duration == len(final_json_data_status), msg
        self.logger_obj.info(f"Validated {session_duration} mins DT session"
                             f" having {session_duration} tpg upload")

        for data in final_json_data_status:
            stime = data[0]
            etime = data[1]
            lt_session_id = data[2]
            upload_json = data[3]

            self.logger_obj.info(f"Validating traceroute data type from ZLTU: Final JSON"
                                 f" upload string from upm file for probe stime = {stime} etime = {etime} ")
            result = self.log_handler.validate_type_data_presence_lt_upload_json(stime, etime, upload_json, 'mtr')
            if not result[0]:
                status = False
                msg = result[1]
                break
            else:
                self.logger_obj.info(f"{result[1]}")

            self.logger_obj.info(f"Validating webload data type from ZLTU: Final JSON"
                                 f" upload string from upm file for probe stime = {stime} etime = {etime} ")
            result = self.log_handler.validate_type_data_presence_lt_upload_json(stime, etime, upload_json, 'web')
            if not result[0]:
                status = False
                msg = result[1]
                break
            else:
                self.logger_obj.info(f"{result[1]}")

            self.logger_obj.info(f"Validating device_stats data type from ZLTU: Final JSON"
                                 f" upload string from upm file for probe stime = {stime} etime = {etime} ")
            result = self.log_handler.validate_type_data_presence_lt_upload_json(stime, etime, upload_json,
                                                                                 'device_stats')
            if not result[0]:
                status = False
                msg = result[1]
                break
            else:
                self.logger_obj.info(f"{result[1]}")

        assert status, msg

    @allure.title("LT session - Verify that after getting LT settings from the policy, Device Stats"
                  " Plugin sets the LT state to 'CREATED' -  ")
    @pytest.mark.xray("QA-266980")
    @add_markers("regression")
    def test_lt_device_stats_state_to_created(self):
        """
             This function verifies that after receiving lt session
             CREATED will be returned by a plugin when the plugin has not started the monitor of the LT

             Args:

             It performs the following steps:
             1.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("LT session - Verify that after getting LT settings from the policy,"
                             " Device Stats Plugin sets the LT state to 'CREATED' - ")

        status = True
        msg = "Device Stats Plugin didn't sets the LT state to 'CREATED' "

        # Fetching UPM log file data
        upm_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')
        if upm_log_data is None:
            self.logger_obj.info("Not able to fetch data from upm log file")
            assert False, "Not able to fetch data from upm log file"

        pattern1 = re.compile(r'(?ims)ZLT: querySessionsStatus mtrStatsLTMap id.*?state = CREATED')
        pattern2 = re.compile(r'(?ims)ZLT: querySessionsStatus deviceStatusLTMap id.*?state = CREATED')

        matches1 = pattern1.findall(upm_log_data)
        matches2 = pattern2.findall(upm_log_data)

        if len(matches1) == 0 or len(matches2) == 0:
            status = False
            msg = "ZLT: querySessionsStatus mtrStatsLTMap and deviceStatusLTMap are not CREATED"

        assert status, msg

    @allure.title("LT session - Verify that after getting LT settings from the policy,"
                  " Device Stats Plugin sets the LT state to 'STARTED' ")
    @pytest.mark.xray(["QA-266981", "QA-267029"])
    @add_markers("regression")
    def test_lt_device_stats_state_to_started(self):
        """
             This function verifies that after receiving lt session
             Zapp sends payload to TPG showing state as STARTED.

             Args:

             It performs the following steps:
             1.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("LT session - Verify that after getting LT settings from the policy,"
                             " Device Stats Plugin sets the LT state to 'STARTED' ")

        status = False
        msg = (' Zapp didnt sends payload to TPG showing state as STARTED. '
               'Search for this line "getLTPayloadToTpgInJsonStr: Payload will be sent to TPG" ')

        # Fetching UPM log file data
        upm_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')
        if upm_log_data is None:
            self.logger_obj.info("Not able to fetch data from upm log file")
            assert False, "Not able to fetch data from upm log file"

        pattern = re.compile(r'(?ims)getLTPayloadToTpgInJsonStr: Payload will be sent to TPG.*?LT payload sent to TPG successfully')
        matches = pattern.findall(upm_log_data)

        if len(matches) == 0:
            msg = (' Zapp didnt sends payload to TPG showing state as STARTED.'
                   ' Search for this line "getLTPayloadToTpgInJsonStr: Payload will be sent to TPG" ')
            assert False, msg

        for data in matches:
            if '"state":"STARTED"' in data:
                status = True
                break

        assert status, msg

    @allure.title("LT session - Verify that when the start time has reached LT traceroutemonitor+web+device_stats"
                  " plugin will start the monitor and will return the status as INPROGRESS to TPG -   ")
    @pytest.mark.xray(["QA-266982", "QA-266984"])
    @add_markers("regression")
    def test_lt_inprogess_status_to_tpg(self):
        """
             This function verifies that after receiving lt session
             Verify that when the start time has reached LT traceroutemonitor+web+device_stats plugin
             will start the monitor and will return the status as IN_PROGRESS to TPG -

             Verify from logs that UPM Plugin controller is able to maintain the IN_PROGRESS
             status once the plugins start running the LT monitors -

             Args:

             It performs the following steps:
             1.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("Verify that when the start time has reached LT traceroutemonitor+web+device_stats"
                             " plugin will start the monitor and will return the status as INPROGRESS to TPG -   ")

        status = True
        msg = "   "

        # Fetching UPM log file data
        upm_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')
        if upm_log_data is None:
            self.logger_obj.info("Not able to fetch data from upm log file")
            assert False, "Not able to fetch data from upm log file"

        pattern = re.compile(r'(?ims)ZLT: ltStateIdMap.*?IN_PROGRESS')
        matches = pattern.findall(upm_log_data)

        if len(matches) == 0:
            status = False
            msg = "Zapp plugin didn't start the monitor and not returning  the status as IN_PROGRESS'"
        elif len(matches) <= 15:
            status = False
            msg = "UPM Plugin controller is not able to maintain the IN_PROGRESS status once the plugins starts"

        assert status, msg

    @allure.title("LT session - Verify that when the LT Collector starts, it sets LT session to 'IN_PROGRESS' state")
    @pytest.mark.xray("QA-266983")
    @add_markers("regression")
    def test_lt_collector_sets_in_progress_state(self):
        """
             This function verifies that after receiving lt session
             Verify that when the LT Collector starts, it sets LT session to “IN_PROGRESS” state-

             Args:

             It performs the following steps:
             1.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("LT session - Verify that when the LT Collector starts, it sets"
                             " LT session to “IN_PROGRESS” state  ")

        status = True
        msg = "   "

        # Fetching UPM log file data
        upm_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')
        if upm_log_data is None:
            self.logger_obj.info("Not able to fetch data from upm log file")
            assert False, "Not able to fetch data from upm log file"

        pattern = re.compile(r'(?ims)ZLT:1 ltStateIdMap.*?IN_PROGRESS')
        matches = pattern.findall(upm_log_data)

        if len(matches) == 0:
            status = False
            msg = "LT Collector starts, is not setting LT session to 'IN_PROGRESS' state"

        assert status, msg

    @allure.title("LT session - Verify that zapp gets all the plugins policy for LT when"
                  " user creates an LT session on Admin UI")
    @pytest.mark.parametrize("pattern, plugin_type", policy_search_pattern)
    @add_markers("regression")
    def test_lt_policy_download_for_all_the_plugins(self, pattern, plugin_type):
        """

             LT session - Verify that zapp gets plugins policy for LT when
             user creates an LT session on Admin UI

             Args:

             It performs the following steps:
             1.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(f"LT session - Verify that zapp gets {plugin_type} policy for LT when"
                             " user creates an LT session on Admin UI ")

        status = False
        msg = f"No {plugin_type} policy received it DT session"

        # Fetching UPM log file data
        upm_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')
        if upm_log_data is None:
            self.logger_obj.info("Not able to fetch data from upm log file")
            assert False, "Not able to fetch data from upm log file"

        self.logger_obj.info("Fetching LT policies from upm log file")
        policies_status = self.log_handler.get_dt_policy_json_from_log(return_json=False)
        assert policies_status[0], policies_status[1]

        policies = policies_status[2]
        for policy in policies:
            if "created" in policy and pattern in policy:
                status = True
                break
        assert status, msg

    @allure.title("LT session - Verify that a single persistent connection shared and used for policy"
                  " downloading of regular and LT session ")
    @pytest.mark.xray("QA-266934")
    @add_markers("regression")
    def test_lt_and_regular_policy_download_use_presistent_connection(self):
        """

             LT session - Verify that a single persistent connection shared and
              used for policy downloading of regular and LT session

             Args:

             It performs the following steps:
             1.
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(f"LT session - Verify that a single persistent connection shared and used for policy"
                             f" downloading of regular and LT session ")

        status = True
        msg = " "
        thread_id_list = []

        # Fetching UPM log file data
        self.logger_obj.info("Fetching upm log file data for validation ")
        upm_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')
        if upm_log_data is None:
            self.logger_obj.info("Not able to fetch data from upm log file")
            assert False, "Not able to fetch data from upm log file"

        self.logger_obj.info("Fetching thread ID for 'ZPD: downloadUpmPolicy succeeded with 200 OK' ")
        for item in upm_log_data.split("\n"):
            if "ZPD: downloadUpmPolicy succeeded with 200 OK" in item:
                res = re.findall(r"\[([^\[\]]*)\]", item)
                temp = str(res[0])
                thread_id_list.append(temp)

        if len(thread_id_list) == 0:
            assert False, " Failure:: 'downloadUpmPolicy succeeded with 200 OK' not found in the logs "

        first_thread_id = thread_id_list[0]

        self.logger_obj.info("Validating dt and regular policy shared persistent connection having same thread id")
        for thread_id in thread_id_list:
            if thread_id != first_thread_id:
                status = False
                msg = "Single persistent connection is not shared between regular and lt policy download"
                break

        assert status, msg

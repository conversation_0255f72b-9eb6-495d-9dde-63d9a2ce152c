import json,os
import time
import pytest
import os, shutil
import sys
import platform

from common_lib.mobileadmin.deviceoverview import DeviceOverview
from common_lib.adminzia.admingroupuser import AdminGroupUser
from common_lib.mobileadmin.appprofile import AppProfile
from common_lib.mobileadmin.forwardingprofile import ForwardingProfile
from common_lib.adminzia.ziahelper import *
from OS_Android.library.ui_zcc import Zcc
from common_lib.common import logger,constants
from common_lib.common.log_ops import *
from OS_Android.library.ops_system import SysOps
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
from common_lib.mobileadmin.trustednetwork import TrustedNetwork
from common_lib.mobileadmin.serviceentitlement import ServiceEntitlement
from OS_Android.library.database_parse import DbHandler
from OS_Android.library.database_parse import LogHandler
from OS_Android.library.zdx_admin import ZDX_ADMIN

config_path = os.path.join(os.getcwd(),"config", os.environ.get("CONFIG_FILE"))

with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")

device_overview=DeviceOverview(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)

const_obj=constants.Utils()

sys_ops=SysOps(logger_obj,variables["CLOUD"])

export_logs=FetchAndroidLogs(logger_obj)

create_zia_user=AdminGroupUser(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)

zia_activate=ZiaHelper(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)

log_ops=LogOps(log_handle=logger_obj)

pcap_ops=PcapOps(log_handle=logger_obj)


app_profile_obj = AppProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
forwarding_profile_obj=ForwardingProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
trusted_network = TrustedNetwork(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
zdx_admin = ZDX_ADMIN(cloudname=variables["CLOUD_DETAILS"]["ZDX"], configfile=variables,
                                   logger=logger_obj)


db_handler = DbHandler()
log_handler = LogHandler()

zdx_application = variables["ZDX_ADMIN"]["ZDX_APPLICATION"][0]
zia_traceroute_domain = zdx_application["tracert_icmp"]["host"]
zia_webprobe_url = zdx_application["web_probe"]["url"]
session_dur = zdx_application["DT"][0]
session_duration = session_dur["durationMinutes"]

probe_port = 443

@allure.title("Base teardown")
def make_teardown():
    """
    1. Delete App and Forwarding profile
    2. Setting privacy Info
    """

    logger_obj.info("Deleting Application and monitors for mr_9366_deep_tracing/dt_01_zia_tunnel")
    zdx_admin.delete_application_and_monitors()

    logger_obj.info("Deleting App profile for mr_9366_deep_tracing/dt_01_zia_tunnel")
    try:
        result = app_profile_obj.delete_app_profile(operating_system="Android")
        assert result[0], result[1]
    except Exception as e:
        logger_obj.info(str(e))

    logger_obj.info("Deleting Forwarding profile for mr_9366_deep_tracing/dt_01_zia_tunnel")
    try:
        result = forwarding_profile_obj.delete_forwarding_profile()
        assert result[0], result[1]
    except Exception as e:
        logger_obj.info(str(e))


@pytest.fixture(scope="package", autouse=True)
def base_setup(request):
    app_profile = app_profile_obj
    forwarding_profile = forwarding_profile_obj

    try:
        zcc = Zcc(start_app=True, log_handle=logger_obj)

        zcc.logger.info("Deleting App profile")
        try:
            result = app_profile.delete_app_profile(operating_system="Android")
            assert result[0], result[1]
        except Exception as e:
            zcc.logger.info(str(e))

        zcc.logger.info("Deleting Forwarding profile")
        try:
            result = forwarding_profile.delete_forwarding_profile()
            assert result[0], result[1]
        except Exception as e:
            zcc.logger.info(str(e))

        zcc.logger.info("Creating Forwarding profile")
        result = forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        zcc.logger.info("Creating App profile")
        result = app_profile.create_app_profile(operating_system="Android")
        assert result[0], result[1]

        zcc.logger.info("Creating webmonitor and traceroute for DT session for mr_9366_deep_tracing/dt_01_zia_tunnel")
        zdx_admin.create_application_and_monitors()

        logged_in = zcc.validate_zcc_logged_in()
        if logged_in[0]:
            zcc.logger.info("Clear logs from zcc app")
            result = zcc.clear_logs()
            assert result[0], result[1]

            zcc.logger.info("Logout from zcc")
            result = zcc.logout()
            assert result[0], result[1]

        result = zcc.login(variables["ZIA_USER_ID"], variables["ZIA_USER_PASSWORD"], variables["ZPA_USER_ID"],
                           variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]

        zcc.logger.info("Validating if ZDX service status is ON")
        result = zcc.verify_service_status(service_status="on", service_type='ZDX')
        assert result[0], result[1]

        # By default, it will run for 'durationMinutes' configured in the config file.
        # For custom duration pass 'durationMinutes' = 5/15/30/60
        zcc.logger.info("Creating DT session with Youtube application")
        zdx_admin.create_dt_session(application="Automation_ZIA")

        time.sleep(5)

        zcc.logger.info(" Creating another DT session with Youtube application while there is a"
                        " ongoing session for testcase purpose, It is not an error")
        zdx_admin.create_dt_session(application="Automation_ZIA")
        zdx_admin.init_wait_DT()

        zcc.logger.info("Fetch logs from android device")
        result = zcc.export_logs()
        assert result[0], result[1]

        zcc.logger.info("Export logs from android device")
        result = export_logs.read_export_log_mail()
        assert result[0], result[1]

        logged_in = zcc.validate_zcc_logged_in()
        if logged_in[0]:
            zcc.logger.info("Clear logs from zcc app")
            zcc.clear_logs()

            zcc.logger.info("Logout from zcc")
            zcc.logout()

    except AssertionError as e:
        request.addfinalizer(make_teardown)
        pytest.skip(f"setup failed reason:{e}")
    else:
        request.addfinalizer(make_teardown)

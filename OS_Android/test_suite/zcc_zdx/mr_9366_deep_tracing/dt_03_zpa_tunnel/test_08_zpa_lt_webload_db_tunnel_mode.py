import time, os
import pytest, allure
import json, re, math
from OS_Android.test_suite.zcc_zdx.mr_9366_deep_tracing.dt_03_zpa_tunnel import conftest
from common_lib.Custom_Markers import add_markers


class TestLtZpaWeblodTunnelMode:

    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.variables = conftest.variables
        self.logger_obj = conftest.logger_obj
        self.zpa_web_url = conftest.zpa_web_url
        self.session_duration = conftest.session_duration
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_webload.db"

        # Get final upload json data list from log uom logs for DT session
        self.final_json_data = self.log_handler.get_final_upload_json_from_upm_log(self.upm_log_data)
        if len(self.final_json_data) == self.session_duration:
            assert False, f"Upload json data count = {len(self.final_json_data)} should be equal to LT session duration = {self.session_duration}"

        self.logger_obj.info("Creating db connection with upm_webload_db")
        self.db_handler.create_db_connection(self.db_path)

    @allure.title("LT session - Verify that the LT stats upload contains the data from"
                  " one interval for ZPA webprobe")
    @pytest.mark.xray(["QA-266979"])
    @add_markers("regression")
    def test_lt_data_upload_contains_zpa_webload_data(self):
        """
             This function verifies that for LT session sends data for
             ZPA webprobe (webload, traceroute, device_stats) in each payload
             which is every 1min

             Args:

             It performs the following steps:
             1. It will fetch payload json from upm log file and reads the content of the upload json
                Verifies if it contains data from each of 3 plugin mentioned above
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Validating data upload to the tpg for each of the plugin from LT upload payload json")

        status = True
        msg = "Upm logs is not having all ZLTU: Final JSON upload string"

        for data in self.final_json_data:
            stime = data[0]
            etime = data[1]
            lt_session_id = data[2]
            upload_json = data[3]

            self.logger_obj.info(f"Validating webload data type from ZLTU: Final JSON"
                                 f" upload string from upm file for probe stime = {stime} etime = {etime} ")
            result = self.log_handler.validate_type_data_presence_lt_upload_json(stime, etime, upload_json, 'web')
            if not result[0]:
                status = False
                msg = result[1]
                break
            else:
                self.logger_obj.info(f"{result[1]}")

        assert status, msg

    @allure.title("DT session Z-Tunnel 1.0: Verify ZApp is writing the web monitor data to db")
    @pytest.mark.xray("QA-266956")
    @add_markers("regression")
    def test_verify_lt_zpa_webprobe_data_from_db(self):
        """
            This function tests the validation the webload db for ZPA webprobe when ZIA is in Tunnel1 mode.
            It performs the following steps:
            1. Doing validation on mtunnel_id - It should be empty for ZPA DT probe
            2  Doing validation on sme_id - It should be = 0
            3  Doing validation on Availability - It should be = 100
            4  Doing validation on PageFetchTime_ns - It should be not empty or less than 1
            5  Doing validation if status code is in ['200','301','302']
            6  Doing validation on HTTPLatency_ns - It should be not empty or less than 1
            7  Doing validation on DNSTime_ns - It should be not empty or less than 1
            8  Doing validation on Success and ecode - It will fail when probe have ecode

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        status = True
        probecount = 0
        msg = ""
        query = f"SELECT * from WebDataLT WHERE URL='{self.zpa_web_url}'"
        rows = self.db_handler.execute_query(query)

        if len(rows) == 0:
            status = False
            msg = "No ZPA Webprobe data found for DT Session"

        for item in rows:
            probecount += 1
            data = item['mtunnelid']
            smeid = item['SMEID']
            statuscode = item['StatusCode']
            availability = item['Availability']
            page_fetch = item['PageFetchTime_ns']
            status_code = item['StatusCode']
            http_latency = item['HTTPLatency_ns']
            dns_time = item['DNSTime_ns']
            ecode = item['ecode']

            self.logger_obj.info(f"DT Session - Verifying mtunnel_id of ZPA webprobe = {probecount}")
            if int(smeid) == 0 and len(statuscode) and int(statuscode) == 200:
                assert len(data) == 0, "Ideally for DT ZPA probes, MtunnelId should be empty"

            self.logger_obj.info(f"Verifying sme id of ZPA webprobe = {probecount}")
            assert len(str(smeid)) != 0, (f"No value found at 'SMEID' column in row = {probecount} ,"
                                          f"which is invalid. It should be zero")

            if len(str(smeid)) >= 1 and int(smeid) != int(0):
                assert False, "For a ZPA probe, the SMEID should be 0"

            self.logger_obj.info(f"Verifying Availability for Zpa Webprobe = {probecount}")
            assert len(str(availability)) != 0, f"No value found at 'Availability' column in row = {probecount}"
            assert int(availability) == 100, f"Availability value should be 100 in row = {probecount}"

            self.logger_obj.info(f"Validating pageFetchTime_ns from Webload DB for Zpa Webprobe = {probecount}")
            assert len(str(page_fetch)) != 0, f"No value found at 'PageFetchTime_ns' in row = {probecount}"

            self.logger_obj.info(f"Checking values of pageFetchTime_ns for Zpa Webprobe = {probecount}")
            if len(str(page_fetch)) >= 1 and int(page_fetch) <= 0:
                status = False
            assert status, f" 'PageFetchTime_ns' value is {page_fetch} which is invalid in row = {probecount}"

            self.logger_obj.info(f"Verifying Status Code for ZPA webprobe = {probecount}")
            assert len(str(status_code)) != 0, f"No value found at 'StatusCode' in row {probecount}"

            self.logger_obj.info(f"Checking if status code is in ['200','301','302'] for ZPA webprobe = {probecount}")
            if status_code not in ['200', '301', '302']:
                status = False
            assert status, "Status code is not in ['200', '301', '302]"

            self.logger_obj.info(f"Checking if HTTPLatency_ns value are valid or not for ZPA webprobe = {probecount}")
            assert len(str(http_latency)) != 0, f"No value found at 'HTTPLatency_ns' in row = {probecount}"
            if len(str(http_latency)) >= 1 and int(http_latency) <= 0:
                status = False
            assert status, f" 'HTTPLatency_ns' value is {http_latency} which is invalid in row = {probecount}"

            self.logger_obj.info(f"Validating values of DNSTime_ns for ZPA webprobe = {probecount}")
            assert len(str(dns_time)) != 0, f"No value found at 'DNSTime_ns' in row = {probecount}"

            self.logger_obj.info(f"Checking if DNSTime_ns value is valid or not for ZPA webprobe = {probecount}")
            if len(str(dns_time)) >= 1 and int(dns_time) <= 0:
                status = False
            assert status, f" 'DNSTime_ns' value is {dns_time} which is invalid in row = {probecount}"

            self.logger_obj.info(f"Validating Web Probe Success for ZPA webprobe = {probecount}")
            if int(statuscode) != 200 and int(ecode) != 0:
                status = False
            assert status, f" ZPA Web probe({item['URL']}) failed with ECODE = {item['ecode']} in row = {probecount}"

        assert status, msg

    @allure.title("DT session - Z-Tunnel 1.0: Verify ZApp collecting data for a WebMonitor - Private App (ZPA)")
    @pytest.mark.xray("QA-266955")
    @add_markers("regression")
    def test_verify_lt_zpa_webprobe_data_from_log(self):
        """
            This function tests the validation the webload log for ZPA webprobe when ZIA is in Tunnel1 mode.
            It performs the following steps:
            1. Doing validation of logs for ZPA Web Probe DT session
            2  Doing validation if service discovery found of zpa webprobe or not
            3  Validating 'sd_url','sd_ip', 'sd_port', 'broker_name' and 'broker_ip'
            4  Validating Response Status: 200 for successful web probes
            5  Validating Saving data for host: to the db

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        zpa_url = self.zpa_web_url
        status = True
        msg = ""
        current_probe = 0
        webload_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH , plugin='web')

        thread_list = set([])
        check = 0

        for item in webload_data.split("\n"):
            if "Starting monitor" in item and "SessionId: -1" not in item:
                check = 1

            if "Service Discovery:" in item:
                if "Service type: ZPA" in item:
                    if int(check) == 1:
                        res = re.findall(r"\[([^\[\]]*)\]", item)
                        temp = str(res[0])
                        thread_list.add(temp)
                        check = 0
                    else:
                        check = 0

        if len(thread_list) == 0:
            assert False, "ZPA webload probes are not available in the webload log file"

        for i in thread_list:
            current_thread_id = str(i)
            flag = 0
            sd_response_found = 0
            run_in = 0
            check_sd_url = 0
            check_sd_ip = 0
            check_sd_port = 0
            check_broker_name = 0
            check_broker_ip = 0
            response_status = 0
            check_response_status = 0
            save_data_for_host = 0
            run_in_text = ""
            sd_text = ""
            starting_text = ""
            sd_url = ""
            sd_ip = ""
            sd_port =""
            sd_broker_ip = ""
            sd_broker_name = ""

            for item in webload_data.split("\n"):
                if "Starting monitor" in item and current_thread_id in item and "SessionId: -1" not in item:
                    current_probe += 1
                    self.logger_obj.info(f"DT Session - Validating logs for ZPA Web Probe = {current_probe}")
                    run_in = 1
                    run_in_text = item

                if len(run_in_text) >= 1:
                    starting_text = run_in_text

                if current_thread_id in item and "Service Discovery:" in item and "Service type: ZPA" in item and flag == 0:
                    current_thread_id = i
                    flag = 1

                    if run_in == 0:
                        flag = 0
                        run_in_text = ""

                if str(current_thread_id) in item and flag == 1 and run_in == 1:
                    if len(run_in_text) >= 1:
                        self.logger_obj.info(run_in_text)
                        run_in_text = ""

                    if "Service Discovery:" in item:
                        if "ZPA" in item:
                            sd_response_found = 1
                        else:
                            flag = 0
                            run_in = 0
                            continue

                        sd_text = item
                        sd_url = re.search("Url: (.*?)\\Success:", item).group(1)
                        sd_url = sd_url.strip()
                        if len(sd_url) > 1:
                            check_sd_url = 1

                        sd_ip = re.search("Destination Ips: (.*?)\\Destination Ports:", item).group(1)
                        sd_ip = sd_ip.strip()
                        if len(sd_ip) > 1:
                            check_sd_ip = 1

                        sd_port = re.search("Destination Ports: (.*?)\\SmeIp:", item).group(1)
                        sd_port = sd_port.strip()
                        if len(sd_port) > 1:
                            check_sd_port = 1

                        sd_broker_name = re.search("BrokerName: (.*?)\\ BrokerIp: ", item).group(1)
                        sd_broker_name = sd_broker_name.strip()
                        if len(sd_broker_name) > 1:
                            check_broker_name = 1

                        sd_broker_ip = re.search("BrokerIp: (.*?)\\ BindIp: ", item).group(1)
                        sd_broker_ip = sd_broker_ip.strip()
                        if len(sd_broker_ip) > 1:
                            check_broker_ip = 1

                    if "Response Status:" in item:
                        check_response_status = 1
                        temp = item.split("Response Status: ", 1)
                        response_status = str(temp[1])
                        response_status = response_status.strip()
                        self.logger_obj.info(f"Response Status Code:{response_status}")

                    if "Saving data for host:" in item:
                        save_data_for_host = 1

                    if "Monitor done:" in item and run_in == 1:
                        self.logger_obj.info(f"SD Response Found: {sd_response_found}")
                        self.logger_obj.info(f"SD URL: {sd_url}")
                        self.logger_obj.info(f"SD IP: {sd_ip}")
                        self.logger_obj.info(f"SD Port: {sd_port}")
                        self.logger_obj.info(f"Broker Name: {sd_broker_name}")
                        self.logger_obj.info(f"Broker Ip: {sd_broker_ip}")
                        self.logger_obj.info(f"Save data :{save_data_for_host}")

                        if check_response_status == 0:
                            status = False
                            msg = f"'Response Status' logging not found in the probe. Probe = {starting_text}"
                            assert status, msg

                        if response_status and str(response_status[0]) in ['4']:
                            status = False
                            msg = f"Invalid Response Status = {response_status}. Probe = {starting_text}"
                            assert status, msg

                        if check_sd_url == 0:
                            status = False
                            msg = f"Service Discovery's URL attribute value is missing. Log - '{sd_text}'"
                            assert status, msg

                        if check_sd_ip == 0:
                            status = False
                            msg = f"Service Discovery's IP attribute value is missing. Log - '{sd_text}'"
                            assert status, msg

                        if check_sd_port == 0:
                            status = False
                            msg = f"Service Discovery's PORT attribute value is missing. Log - '{sd_text}'"
                            assert status, msg

                        if check_broker_name == 0:
                            status = False
                            msg = f"Service Discovery's BrokerName attribute value is missing. Log - '{sd_text}'"
                            assert status, msg

                        if check_broker_ip == 0:
                            status = False
                            msg = f"Service Discovery's BrokerIp attribute value is missing. Log - '{sd_text}'"
                            assert status, msg

                        if save_data_for_host == 0:
                            status = False
                            msg = f" 'Saving data for host:' not found for current web probe "
                            assert status, msg

                        self.logger_obj.info(
                            f"DT Session - ZPA Web Probe = {current_probe} in tunnel mode"
                            f" is Successful with Thread ID = " + str(current_thread_id))
                        self.logger_obj.info(
                            "#######################################################################################################")

                        flag = 0
                        sd_response_found = 0
                        check_response_status = 0
                        response_status = 0
                        check_sd_url = 0
                        check_sd_ip = 0
                        check_sd_port = 0
                        check_broker_name = 0
                        check_broker_ip = 0
                        run_in_text = ""
                        sd_text = ""
                        starting_text = ""
                        sd_url = ""
                        sd_ip = ""
                        sd_port = ""
                        sd_broker_ip = ""
                        sd_broker_name = ""

                if flag == 1 and str(current_thread_id) in item and run_in == 1:
                    self.logger_obj.info(item)

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Deletion of forward profile and app profile
        2. Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Closing db connection after verification")
        self.db_handler.close_db_connection()

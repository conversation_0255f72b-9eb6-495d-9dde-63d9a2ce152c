import time, os
import pytest, allure
import json, re, math
from datetime import datetime
from OS_Android.test_suite.zcc_zdx.mr_9366_deep_tracing.dt_02_zia_none import conftest
from common_lib.Custom_Markers import add_markers


class TestLtTracerouteNoneMode:

    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        3. Login to ZCC
        3. Creating DT session
        4. Export logs, Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.variables = conftest.variables
        self.logger_obj = conftest.logger_obj
        self.zia_traceroute_domain = conftest.zia_traceroute_domain
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_traceroute.db"

        self.logger_obj.info("Creating db connection with traceroute_db")
        self.db_handler.create_db_connection(self.db_path)

    @allure.title("DT Session - ZIA TRACEROUTE NONE MODE - Verify new table created in"
                  " upm_traceroute.db tunnel None Mode and LT data is recorded onto it")
    @pytest.mark.xray("QA-266943")
    @add_markers("regression")
    def test_lt_table_creation_for_traceroute_db(self):
        """
            This function is validating for table creation for traceroute db
            It performs the following steps:
            1. It will check following lt tables is present or not
               'trltmain', 'trltleg', 'trlthoplat'
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        status = True
        msg = ""

        traceroute_table_name = {
            'trltmain': 1,
            'trltleg': 1,
            'trlthoplat': 1
        }

        self.logger_obj.info("DT Session - ZIA TRACEROUTE NONE MODE - Verify that new tables are created on"
                             " upm_traceroute.db and LT data is recorded onto it ")

        query = "SELECT name FROM sqlite_master WHERE type='table'"
        rows_data = self.db_handler.execute_query(query)
        for row in rows_data:
            table_name = row[0]
            if traceroute_table_name.get(table_name) is not None:
                traceroute_table_name[table_name] = 2

        for table in traceroute_table_name:
            if traceroute_table_name[table] == 1:
                status = False
                msg = f"{table} table is not present in upm_traceroute.db tunnel None Mode"
                break
            else:
                self.logger_obj.info(f"Verified table = {table} found in traceroute db")

        assert status, msg

    @allure.title("DT session - Verify trltmain db data insertion frequency is 1mins")
    @pytest.mark.xray("QA-266966")
    @add_markers("regression")
    def test_data_collection_for_upm_traceroute_db_for_dt_session(self):
        """
            This function Verify data is written to upm_traceroute.db in every 10s for DT session
            It performs the following steps:
            1. It will check time difference between two rows should be 1mins
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        status = True
        msg = ""

        self.logger_obj.info(f"Validating data insertion frequency 1min for table 'trltmain'"
                             f" for upm_traceroute_db for DT session")
        query = f"select distinct timestamp from trltmain order by timestamp desc"
        rows_data = self.db_handler.execute_query(query)

        self.logger_obj.info("Validating if data is present or not for lt tables in traceroute db")
        if len(rows_data) <= 1:
            status = False
            msg = (f"Failed to get data for table 'trltmain' of upm_traceroute_db"
                   f" for ZIA traceroute for DT session in tunnel None Mode ")
        assert status, msg

        for ind in range(len(rows_data) - 1):
            # here curr_timestamp > prev_timestamp
            # timestamp is in unix ms
            curr_timestamp = rows_data[ind][0]
            prev_timestamp = rows_data[ind + 1][0]

            difference = int(round((curr_timestamp - prev_timestamp) / 1000, 0))
            self.logger_obj.info(
                f"Verifying difference between Curr_timestamp = {curr_timestamp} , prev_timestamp ={prev_timestamp}")

            if not (55 <= difference <= 65):
                status = False
                msg = (
                    f"Failed to collect data for trltmain in 1min at currentime for DT - Session in tunnel None Mode"
                    f":{datetime.fromtimestamp(int(curr_timestamp / 1000))} and prevtime "
                    f":{datetime.fromtimestamp(int(prev_timestamp / 1000))} diff "
                    f":{curr_timestamp - prev_timestamp} {int(round((curr_timestamp - prev_timestamp) / 1000, 0))} ")
                break
            else:
                self.logger_obj.info(f"DT Session - Validated time difference between current_row = {curr_timestamp} and "
                    f"prev_row ={prev_timestamp},Difference is 60s which is correct")

        assert status, msg

    @allure.title("DT Session - ZIA TRACEROUTE NONE MODE: Verify maxhopcount and hopstodest count from traceroute db")
    @pytest.mark.xray(["QA-266959", "QA-266960"])
    @add_markers("regression")
    def test_maxhopcount_hopstodest_unresponsivehopcount_from_traceroute_lt_db(self):
        """
             DT session - This function verifies that in Traceroute probes the value of 'hopstodest'
             should not be greater than 'maxhopcount'. It also verifies that in Traceroute
             probes the value of 'unresponsivehopcount' should not be greater than 'hopstodest'

             It performs the following steps:
             1. It is validating maxhopcount, hopstodest and unresponsivehopcount values in traceroute db
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("DT session - ZIA TRACEROUTE NONE MODE: Verifying that in Traceroute probes"
                             "the value of'hopstodest'should not be greater than 'maxhopcount'")

        query = f"SELECT * from trltmain WHERE domain='{self.zia_traceroute_domain}'"
        rows_data = self.db_handler.execute_query(query)
        current_row = 1
        flag = True
        msg = ""

        if len(rows_data) == 0:
            assert False, "DT Session - No zia traceroute data found in traceroute.db"

        for data in rows_data:
            domain = data['domain']
            status = data['status']
            hopstodest = data['hopstodest']
            maxhopcount = data['maxhopcount']
            unresponsivehopcount = data['unresponsivehopcount']

            if status == 'Traceroute Stopped':
                continue

            self.logger_obj.info(f"DT Session - Validating values should be present and 'hopstodest' should"
                                 f" be less than maxhopcount and unresponsivehopcount' should be less than"
                                 f" equal to 'hopstodest' for probe = {current_row} ")
            if len(domain) == 0:
                flag = False
                msg = f"DT Session - No value found at 'domain' column in row = {current_row}"
                assert flag, msg

            if len(str(hopstodest)) == 0 or (len(str(hopstodest)) >= 1 and int(hopstodest) <= 0):
                flag = False
                msg = f"DT Session - No or Invalid value found at 'hopstodest' in row = {current_row}"
                assert flag, msg

            if len(str(maxhopcount)) == 0 or (len(str(maxhopcount)) >= 1 and int(maxhopcount) <= 0):
                flag = False
                msg = f"DT Session - No or Invalid value found at maxhopcount in row = {current_row}"
                assert flag, msg

            if len(str(unresponsivehopcount)) == 0 or (
                    len(str(unresponsivehopcount)) >= 1 and int(unresponsivehopcount) < 0):
                flag = False
                msg = f"DT Session - No or Invalid value found at maxhopcount in row = {current_row}"
                assert flag, msg

            if len(str(hopstodest)) >= 1 and len(str(maxhopcount)) >= 1 and int(hopstodest) > 40:
                flag = False
                msg = f"DT Session - The value of 'hopstodest' = {hopstodest} is greater than 40 in row = {current_row} which in Invalid"
                assert flag, msg

            if len(str(hopstodest)) >= 1 and len(str(maxhopcount)) >= 1 and (int(hopstodest) > int(maxhopcount)):
                flag = False
                msg = (f"DT Session - The value of 'hopstodest' = {hopstodest} is greater than"
                       f"maxhopcount = {maxhopcount} in row = {current_row} which in Incorrect")
                assert flag, msg

            if len(str(unresponsivehopcount)) >= 1 and len(str(hopstodest)) >= 1 and int(hopstodest) < int(
                    unresponsivehopcount):
                flag = False
                msg = f"DT Session - The value of 'unresponsivehopcount' is greater than 'hopstodest' in row = {current_row} which is inalid"
                assert flag, msg

            current_row += 1

        assert flag, msg

    @allure.title("DT Session - ZIA TRACEROUTE NONE MODE: Verify totalloss and totallatency from traceroute db")
    @pytest.mark.xray(["QA-266959", "QA-266960"])
    @add_markers("regression")
    def test_totalloss_totallatency_values_from_traceroute_lt_db(self):
        """
             DT Session - This function verifies that in Traceroute probes the value of 'totalloss'
             and 'totallatency' should not be invalid

             It performs the following steps:
             1. It is validating 'totalloss', and 'totallatency' values in traceroute db
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(
            "DT Session - ZIA TRACEROUTE NONE MODE: Verifying 'totalloss' and 'totallatency' values from traceroute DB")

        query = f"SELECT * from trltmain WHERE domain='{self.zia_traceroute_domain}'"
        rows_data = self.db_handler.execute_query(query)
        current_row = 1
        flag = True
        complete_traceroute = False
        msg = ""

        if len(rows_data) == 0:
            assert False, "DT Session - No zia traceroute data found in traceroute.db"

        for data in rows_data:
            status = data['status']
            domain = data['domain']
            totalloss = data['totalloss']
            totallatency = data['totallatency']

            if status == 'Traceroute Stopped':
                continue

            self.logger_obj.info(f"Validating values should be present for 'totalloss' and 'totallatency'"
                                 f" values from traceroute DB = {current_row} ")

            if len(str(totalloss)) == 0 or int(totalloss) < -1 or int(totalloss) >= 101:
                flag = False
                msg = f"DT Session - No or invalid found at 'totalloss' = {totalloss} in row = {current_row}"
                break

            if len(str(totallatency)) == 0 or int(totallatency) < -1:
                flag = False
                msg = f"DT Session - No or value found at 'totallatency' = {totallatency} in row = {current_row}"
                break

            if int(totallatency) >= 201:
                self.logger_obj.warning(
                    f"DT Session - In row = {current_row}, the 'totallatency' value = {totallatency} is too high")

            current_row += 1

        assert flag, msg

    @allure.title("DT Session - ZIA TRACEROUTE NONE MODE: Verify traceroute probe json from traceroute db")
    @pytest.mark.xray(["QA-266959", "QA-266960"])
    @add_markers("regression")
    def test_zia_traceroute_probe_json_from_lt_db(self):
        """
             DT Session - This function verifies ZIA TR JSON of DIRECT case (ZIA none mode).

             It performs the following steps:
             1. It is validating 'app', 'id', 'hints'
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        currentrow = 0
        compelete_traceroute = False
        flag = True
        msg = ""

        self.logger_obj.info("DT Session - ZIA TRACEROUTE NONE MODE: Verify traceroute probe json from traceroute db")

        query = f"SELECT * from trltmain WHERE domain='{self.zia_traceroute_domain}'"
        rows_data = self.db_handler.execute_query(query)

        if len(rows_data) == 0:
            assert False, "DT Session - No zia traceroute data found in traceroute.db"

        for row in rows_data:
            currentrow += 1
            domain = row['domain']
            data = ""
            data = data + row['json']

            self.logger_obj.info(f"DT Session - Validating JSON format for traceroute_{currentrow}: {domain}")
            if "app" and "id" not in data:
                flag = False
                msg = f"DT Session - Invalid JSON format in traceroute json in row = {currentrow}"
                assert flag,msg

            try:
                json_data = json.loads(data)
            except json.JSONDecodeError as e:
                data = re.sub(r'([{,]\s*?"[^"]+"\s*:)\s*,', r'\1 null,', data)
                json_data = json.loads(data)  # {'A': 'a', 'B': None, 'C': 'c'}

            self.logger_obj.info(f"DT Session - Validating 'hints' for traceroute_{currentrow}: {domain}")
            if len(json_data['hints']) == 0 or json_data['hints'] is None:
                flag = False
                msg = f"DT Session - In row = {currentrow} under 'JSON' column, No legs are present in the 'Hints' section"
                assert flag,msg

            self.logger_obj.info(f"DT Session - Validating 'client_name', 'clt_ha_name','clt_ip','ecode',"
                                 f"'icode', 'etime' fields in json for traceroute_{currentrow}: {domain}")
            for i in json_data:
                if "client_name" in i or "clt_ha_name" in i or "ecode" in i or "erefid" in i:
                    if len(str(json_data[i])) == 0 or json_data[i] is None:
                        flag = False
                        msg = f"DT Session - Invalid or no value in trace json for = {i} in row = {currentrow} of traceroute db"
                        assert flag, msg

                if "clt_ip" in i or "icode" in i or "is_zpa" in i or "etime" in i:
                    if len(str(json_data[i])) == 0 or json_data[i] is None:
                        flag = False
                        msg = f"DT Session - Invalid or no value in trace json for = {i} in row = {currentrow} of traceroute db"
                        assert flag,msg

            data = ""
            traceroute_legs = json_data["legs"]
            first_leg_tunnel_type = traceroute_legs[0]['tunnel_type']

            if first_leg_tunnel_type not in [1, 13, 21]:
                flag = False
                msg = (
                    f"DT Session - ZIA traceroute is not working fine for tunnel NONE mode, showing incorrect tunnel_type ="
                    f"{first_leg_tunnel_type}, it should be in ['1','13','21']")
                assert flag,msg

            for leg in traceroute_legs:
                source = leg['src']
                destination = leg['dst']
                dst_ip = leg['dst_ip']
                ecode = leg['ecode']
                loss = leg['loss']
                proto = leg['proto']
                src_ip = leg['src_ip']
                tunnel_type = leg['tunnel_type']
                num_hops = leg['num_hops']
                num_unresp_hops = leg['num_unresp_hops']

                self.logger_obj.info(f"DT Session - Validating 'src_ip' value in json for traceroute_{currentrow}: {domain}")
                if src_ip is None or len(str(src_ip)) == 0:
                    flag = False
                    msg = (f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'src_ip' value is missing")
                    assert flag,msg

                self.logger_obj.info(f"DT Session - Validating 'dts_ip' value in json for traceroute_{currentrow}: {domain}")
                if len(str(dst_ip)) == 0:
                    flag = False
                    msg = (f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'dst_ip' value is missing")
                    assert flag, msg
                else:
                    match = re.match(r"[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}", dst_ip)
                    if not bool(match):
                        flag = False
                        msg = (
                            f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                            f"'dst_ip' value is Invalid")
                        assert flag, msg

                self.logger_obj.info(f"DT Session - Validating 'ecode' value in json for traceroute_{currentrow}: {domain}")
                if ecode is None or len(str(ecode)) == 0:
                    flag = False
                    msg = (f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'ecode' value is missing")
                    assert flag, msg

                self.logger_obj.info(f"DT Session - Validating 'loss' value in json for traceroute_{currentrow}: {domain}")
                if loss is None or len(str(loss)) == 0:
                    flag = False
                    msg = (f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'loss' value is missing")
                    assert flag, msg

                self.logger_obj.info(f"DT Session - Validating 'protocol' value in json for traceroute_{currentrow}: {domain}")
                if proto is None or len(str(proto)) == 0:
                    flag = False
                    msg = (f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'proto' value is missing")
                    assert flag, msg

                self.logger_obj.info(
                    f"DT Session - Validating 'tunnel_type' value in json for traceroute_{currentrow}: {domain}")
                if tunnel_type is None or len(str(tunnel_type)) == 0:
                    flag = False
                    msg = (f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'tunnel_type' value is missing")
                    assert flag, msg

                self.logger_obj.info(f"DT Session - Validating 'num_hops' value in json for traceroute_{currentrow}: {domain}")
                if num_hops is None or len(str(num_hops)) == 0:
                    flag = False
                    msg = (f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'nums_hops' value is missing")
                    assert flag, msg

                self.logger_obj.info(
                    f"DT Session - Validating 'num_unresp_hops' value in json for traceroute_{currentrow}: {domain}")
                if num_unresp_hops is None or len(str(num_unresp_hops)) == 0:
                    flag = False
                    msg = (f"DT Session - Row = {currentrow} in 'JSON' column. Under {source} to {destination} leg ,"
                           f"'nums_unresp_hops' value is missing")
                    assert flag, msg
            self.logger_obj.info(
                f"######################### Validated traceroute data for Row = {currentrow} #########################")

        assert flag, msg

    @allure.title("DT Session - Traceroute: Verify ZIA Traceroute tunnel None Mode")
    @pytest.mark.xray(["QA-266959", "QA-266960"])
    @add_markers("regression")
    def test_zia_lt_traceroute_probes_from_log(self):
        """
             This function test ZIA traceroute for tunnel node for LT Traceroute probes

             Args:

             It performs the following steps:
             1. It is validating zia traceroute logs
                It will check service discovery response and validate it.
                It will check are we calculating computeDifferential
                It will check DBG TRACEROUTE:: Hops after adjustment Done
                It will check Saving response into the database
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """

        traceroute_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                plugin='traceroute')

        status = True
        msg = ""
        sd_type = "DIRECT"
        sd_tunnel = "None"
        thread_list = set([])
        current_probe = 0

        for item in traceroute_log_data.split("\n"):
            if "ZUpmTracerouteThread:: run - In" in item and "isLT=1" in item:
                res = re.findall(r"\[([^\[\]]*)\]", item)
                temp = str(res[0])
                thread_list.add(temp)
                if len(thread_list) == 4:
                    self.logger_obj.info(thread_list)
                    break

        if len(thread_list) == 0:
            assert False, f"DT Session - Traceroute ZIA probes are not available in the traceroute log file tunnel Mode = {sd_tunnel}"

        res = ""
        for i in thread_list:
            current_thread_id = str(i)
            flag = 0
            sd_response_found = 0
            run_out = 0
            run_in = 0
            insert_in_database = 0
            check_sd_url = 0
            check_sd_ip = 0
            check_sd_port = 0

            for item in traceroute_log_data.split("\n"):
                if current_thread_id in item and "SD response" in item and flag == 0 and "ZPA" not in item and "Service type: {}".format(
                        sd_type) in item and "ip.zscaler.com" not in item:
                    current_probe += 1
                    self.logger_obj.info(f"DT Session - Validating logs for Probe = {current_probe}")
                    current_thread_id = i
                    flag = 1
                    run_in = 1

                if str(current_thread_id) in item and flag == 1:
                    if "ZPA" in item:
                        self.logger_obj.info(f"Skipping check for ZPA traceroute")
                        break

                    if "SD response" in item and "ip.zscaler.com" not in item:
                        if "{}".format(sd_type) in item:
                            sd_response_found = 1
                        else:
                            flag = 0
                            continue

                        sd_url = re.search("result=Url: (.*?) Success:", item).group(1)
                        sd_url = sd_url.strip()
                        if len(sd_url) > 1:
                            check_sd_url = 1
                        self.logger_obj.info(f"DT Session - Service discovery Url found in the logs : {sd_url}")

                        sd_ip = re.search("Destination Ips: (.*?) Destination Ports:", item).group(1)
                        sd_ip = sd_ip.strip()
                        if len(sd_ip) > 1:
                            check_sd_ip = 1
                        self.logger_obj.info(f"DT Session - Service discovery IP found in the logs : {sd_ip}")

                        sd_port = re.search("Destination Ports: (.*?) SmeIp:", item).group(1)
                        sd_port = sd_port.strip()
                        if len(sd_port) > 1:
                            check_sd_port = 1
                        self.logger_obj.info(f"DT Session - Service discovery Port found in the logs : {sd_port}")


                    if flag == 1 and str(current_thread_id) in item and sd_response_found == 1:
                        self.logger_obj.info(item)
                        res += item


                    if "ZUpmTracerouteThread:: saveToDatabase - Saving response into the database":
                        insert_in_database = 1

                    if "ZUpmTracerouteThread:: run - Out" in item and run_in == 1 and flag == 1:
                        run_out = 1
                        self.logger_obj.info(
                            f"Validating if data inserted to the db or not for probe = {current_probe}")
                        if insert_in_database == 0:
                            status = False
                            msg = (f"DT Session - ZUpmTracerouteDb:insertInDatabase not found for current"
                                   f" probe with thread ID = ") + str(current_thread_id)
                        assert status, msg

                        self.logger_obj.info(
                            f"Checking Service discovery (SD) found or not for probe = {current_probe}")
                        if sd_response_found == 0:
                            status = False
                            msg = ("DT Session - Service discovery (SD) not found for current"
                                   " probe with thread ID = ") + str(current_thread_id)
                        assert status, msg

                        self.logger_obj.info(f"Checking 'sd_ip'and 'sd_url' for probe = {current_probe}")
                        if check_sd_ip == 0 or check_sd_url == 0:
                            status = False
                            msg = ("DT Session - Some attribute (sd_ip or sd_url or sd_port is missing or invalid"
                                   " in the service discovery with thread ID = ") + str(current_thread_id)
                        assert status, msg


                        self.logger_obj.info(
                            f"Checking ZUpmTracerouteThread:: run - In Thread found or not for probe = {current_probe}")
                        if run_in == 0:
                            status = False
                            msg = ("DT Session - DBG ZUpmTracerouteThread:: run In Thread not found in the"
                                   " probe with thread ID = ") + str(current_thread_id)
                        assert status, msg

                        self.logger_obj.info(
                            f"Checking ZUpmTracerouteThread:: run Out Thread found or not for probe = {current_probe}")
                        if run_out == 0:
                            status = False
                            msg = ("DT Session - DBG ZUpmTracerouteThread:: run Out Thread not found in the"
                                   " probe with thread ID = ") + str(current_thread_id)
                        assert status, msg

                        if insert_in_database == 1:
                            self.logger_obj.info(
                                f"DT Session - ZIA traceroute Probe = {current_probe} in tunnel none mode"
                                f" is Successful with Thread ID = " + str(current_thread_id))
                            self.logger_obj.info(
                                "#######################################################################################################")

                        # Reassigning 0 values to check for next traceroute log data
                        flag = 0
                        sd_response_found = 0
                        run_out = 0
                        run_in = 0
                        insert_in_database = 0
                        check_sd_url = 0
                        check_sd_ip = 0
                        check_sd_port = 0

        self.logger_obj.info("DT Session - Verified complete traceroute log for ZIA traceroute in tunnel none Mode")
        assert status, msg

    @allure.title("DT session - Tunnel Mode : ZIA OFF - Verify data upload to the tpg for a TraceRoute")
    @pytest.mark.xray("QA-266961")
    @add_markers("regression")
    def test_lt_traceroute_data_upload_to_tpg(self):
        """
             This function test ZIA traceroute data upload to tpg for tunnel none mode for DT Session

             Args:

             It performs the following steps:
             1. It is validating upm logs to check if data is uploaded to tpg or not for DT Session
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.

        """
        self.logger_obj.info("DT session - Tunnel Mode : ZIA OFF - Verify data upload to the tpg for a TraceRoute")
        upm_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')

        if len(upm_log_data)==0:
            self.logger_obj.info("Not able to fetch data from upm log file")
            pytest.skip("Not able to fetch data from upm log file")

        status = True
        msg = ""
        current_probe = 1

        # Get data from log from uploadJson
        final_json_data = self.log_handler.get_final_upload_json_from_upm_log(upm_log_data)
        if len(final_json_data)==0:
            assert False, "LT upload json data is not present in the ZSAUpm logs"

        for it in final_json_data:
            stime, etime = it[0], it[1]
            lt_session_id = it[2]
            upload_json = it[3]

            self.logger_obj.info(f"Validating traceroute data from ZLTU: Final JSON"
                                 f" upload string from upm file for probe = {current_probe} ")

            self.logger_obj.info(f"Validating mtr probes is present in Final json upload string for"
                                 f" probe = {current_probe} between stime = {stime} to etime = {etime} ")
            if upload_json.get("mtr") is None or upload_json["mtr"].get("probes") is None:
                status = False
                msg = (f"'mtr' invalid data or data is not uploaded to tpg for DT session from "
                       f"stime = {stime} to etime = {etime}")
                assert status, msg

            self.logger_obj.info(
                f"Validating cloud path probes is valid type in Final json upload string for probe = {current_probe} "
                f"between stime = {stime} to etime = {etime} ")
            app = upload_json['mtr']['probes'][0]

            probe_type = app["app"]["type"]
            if len(probe_type) == 0 or probe_type != "mtr":
                status = False
                msg = f"{probe_type} is not a valid type for cloud Path probe for DT session - Tunnel None Mode"
                assert status, msg

            current_probe += 1

        assert status, msg

    @allure.title(
        "LT session - Verify that the LT policy will be polled at an interval of 15 seconds in Tunnel None mode")
    @pytest.mark.xray("QA-266937")
    @add_markers("regression")
    def test_lt_policy_polled_at_15s_from_upm_log(self):
        """
             This function Verify that the LT policy will be polled at an interval of 15 seconds from upm logs

             Args:

             It performs the following steps:
             1. It will check LT policy from upm logs files and time difference between policy fetch for LT session
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """

        pattern = "ZPD: Received LT session"
        status = True
        msg = " "
        self.logger_obj.info("Fetching upm log file data")
        upm_log_file_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='upm')

        self.logger_obj.info("Verifying that the LT policy will be polled at an interval of 15 seconds from upm logs")
        policy_status = self.log_handler.find_log_line_timestamp_in_unix(data=upm_log_file_data, pattern=pattern)

        if len(policy_status) == 0:
            assert False, "No DT policy found in the logs"

        for idx in range(2, len(policy_status)):
            prev_time = policy_status[idx - 1]
            curr_time = policy_status[idx]

            difference = curr_time - prev_time
            self.logger_obj.info(f"Validation time difference between two policy download {curr_time} and {prev_time}")
            if not (13 <= difference <= 17):
                status = False
                msg = (f"Time difference bw two policy download {curr_time} and {prev_time}"
                       f" is {difference} seconds, which is not correct")
                break
            else:
                self.logger_obj.info(
                    f"Validated time difference between two policy download {curr_time} and {prev_time}, which is correct")

        assert status, msg

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Closing db connection
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Closing db connection after verification")
        self.db_handler.close_db_connection()

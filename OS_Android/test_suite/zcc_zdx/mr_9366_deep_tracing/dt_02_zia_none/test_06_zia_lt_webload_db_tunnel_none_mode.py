import time, os
import pytest, allure
import json, re
import datetime
from OS_Android.test_suite.zcc_zdx.mr_9366_deep_tracing.dt_02_zia_none import conftest
from common_lib.Custom_Markers import add_markers

class TestWebloadLtNone:
    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.variables = conftest.variables
        self.logger_obj = conftest.logger_obj
        self.zdx_admin = conftest.zdx_admin
        self.zia_webprobe_url = conftest.zia_webprobe_url
        self.probe_port = conftest.probe_port
        self.session_duration = conftest.session_duration
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_webload.db"

        self.logger_obj.info("Creating db connection with webload_db")
        self.db_handler.create_db_connection(self.db_path)

    @allure.title("DT session - ZIA WEBLOAD: Verify 5mins DT session should have 5 webload data rows")
    @pytest.mark.xray("QA-266947")
    @add_markers("regression")
    def test_verify_count_for_zia_lt_webprobe_from_db(self):
        """
            This function tests that x min DT session should contain x row count, ZIA webprobe runs every 1min in DT session
            It performs the following steps:
            1. Doing validation on count of rows
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("DT session - Verify 5mins DT session should have 5 webload data rows")

        query = f"SELECT * from WebDataLT where URL='{self.zia_webprobe_url}'"
        rows = self.db_handler.execute_query(query)
        status = True
        msg = " "

        if len(rows) == 0:
            status = False
            msg = "No data inserted to the WebdataLt for DT Session"
        elif len(rows) != self.session_duration:
            status = False
            msg = f"Db is having {len(rows)} data rows in the db which is incorrect, it should have {self.session_duration} data rows"

        assert status, msg

    @allure.title("DT session - ZIA WEBLOAD: Verify Webload data insertion frequency which is 1min")
    @pytest.mark.xray("QA-266957")
    @add_markers("regression")
    def test_data_collection_for_upm_webload_db_for_dt_session(self):
        """
            This function Verify data is written to upm_webload.db in every 1min for DT session
            It performs the following steps:
            1. It will check time difference between two rows should be 1mins
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        status = True
        msg = ""

        self.logger_obj.info(f"Validating data insertion frequency 1min for table"
                             f" 'WebDataLT' for upm_webload_db for DT session")

        query = f"select Timestamp from WebDataLT order by Timestamp desc"
        rows_data = self.db_handler.execute_query(query)
        status = True
        msg = " "

        self.logger_obj.info("Validating if data is present or not for lt tables in traceroute db")
        if len(rows_data) != self.session_duration:
            msg = (f"Failed to get full data for table 'WebDataLT' of upm_webload_db for"
                   f" ZIA webload for DT session in tunnel T1.0 Mode ")
            assert False, msg

        for ind in range(len(rows_data) - 1):
            # here curr_timestamp > prev_timestamp
            # timestamp is in unix ms
            curr_timestamp = rows_data[ind][0]
            prev_timestamp = rows_data[ind + 1][0]

            difference = int(round((curr_timestamp - prev_timestamp) / 1000, 0))

            if not (55 <= difference <= 65):
                status = False
                msg = (
                    f"Failed to collect data for WebDataLT in 1min at currentime for DT - Session in tunnel T1.0 Mode"
                    f":{datetime.datetime.fromtimestamp(int(curr_timestamp / 1000))} and prevtime "
                    f":{datetime.datetime.fromtimestamp(int(prev_timestamp / 1000))} diff "
                    f":{curr_timestamp - prev_timestamp} {int(round((curr_timestamp - prev_timestamp) / 1000, 0))} ")
            else:
                self.logger_obj.info(
                    f"DT Session - Validating time difference between current_row = {curr_timestamp} and "
                    f"prev_row ={prev_timestamp}, Difference is 60s which is correct")

        assert status, msg

    @allure.title("DT session - ZIA WEBLOAD: Verify Mtunnel ID for zia webprobe in Tunnel None mode")
    @pytest.mark.xray("QA-266947")
    @add_markers("regression")
    def test_verify_mtunnel_id_for_zia_lt_webprobe_from_db(self):
        """
            This function tests the validation of mtunnel in webload db for ZIA webprobe when ZIA is in None mode.
            It performs the following steps:
            1. Doing validation on mtunnel for ZPA webload for zdx
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("DT session - Verifying Mtunnel ID From ZIA Webload when in T1.0 mode")

        query = "SELECT MTunnelId from WebDataLT"
        rows = self.db_handler.execute_query(query)
        status = True
        currentrow = 0
        msg = " "

        if len(rows) == 0:
            status = False
            msg = "No data inserted to the WebdataLt for DT Session"

        for item in rows:
            data = item['MTunnelId']
            currentrow += 1

            self.logger_obj.info(f"Verifying mtunnel id which should be empty for ZIA webprobe in row = {currentrow}")
            if len(data) != 0:
                status = False
                msg = (f"Value found at MTunnelId in row {currentrow}."
                       f"For ZIA probes, MtunnelId should be empty")
                break

        assert status, msg

    @allure.title("DT session - ZIA WEBLOAD: Verify SMEID from Webload Db in Tunnel None mode")
    @pytest.mark.xray("QA-266947")
    @add_markers("regression")
    def test_verify_smeid_for_zia_lt_webprobe_from_db(self):

        """
            This function tests the validation of smeid in webload db for ZIA webprobe when ZIA is in None mode.
            It performs the following steps:
            1. Doing validation on smeid, it should not be empty and value should be correct
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("DT session - ZIA WEBLOAD: Verify SMEID from Webload Db in Tunnel None mode")

        query = "SELECT * from WebDataLT"
        rows = self.db_handler.execute_query(query)
        status = True
        currentrow = 0
        msg = " "

        # for None mode sme_id should be 0
        smeid_from_logs = 0

        if len(rows) == 0:
            status = False
            msg = "No data inserted to the WebdataLt for DT Session"

        for item in rows:
            data = item['SMEID']

            currentrow += 1

            self.logger_obj.info(f"Verifying sme id = {data} of ZIA webprobe in row = {currentrow}")
            assert len(str(data)) != 0, (f"No value found at 'SMEID' column in row = {currentrow} ,"
                                         f"which is invalid. It should not be empty")

            if len(str(data)) >= 1 and int(str(data)) != smeid_from_logs:
                assert False, f"The value of SMEID is not 0, for ZIA webprobe in None Mode it should be 0 in row = {currentrow}"

        assert status, msg

    @allure.title("DT session - ZIA WEBLOAD: Verify ProxyHost and ProxyPort from Webload Db in Tunnel None mode")
    @pytest.mark.xray("QA-266947")
    @add_markers("regression")
    def test_verify_proxyhost_proxyport_for_zia_lt_webprobe_from_db(self):

        """
            This function tests the proxyhost and proxyport values in webload db for ZIA webprobe
            when ZIA is in None mode.

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(
            "DT session - ZIA WEBLOAD: Verify ProxyHost and ProxyPort from Webload Db in Tunnel None mode")

        query = "SELECT ProxyHost,ProxyPort from WebDataLT"
        rows = self.db_handler.execute_query(query)
        status = True
        currentrow = 0
        msg = " "

        if len(rows) == 0:
            status = False
            msg = "No data inserted to the WebdataLt for DT Session"

        for data in rows:
            currentrow += 1
            proxy_host = data[0]
            proxy_port = data[1]

            self.logger_obj.info(f"Verifying ProxyHost of ZIA webprobe in row = {currentrow}")
            if len(proxy_host) != 0:
                msg = f" ProxyHost from db = {proxy_host} should be empty in row = {currentrow}"
                assert False,msg

            self.logger_obj.info(f"Verifying ProxyPort of ZIA webprobe in row = {currentrow}")
            if proxy_port != 0:
                msg = f"ProxyPort from db = {data[1]} should be equal to zero in none mode in row = {currentrow}"
                assert False, msg

        assert status, msg

    @allure.title(
        "DT session - ZIA WEBLOAD: Verify Pagefetchtime_ns and httplatency from Webload Db in Tunnel None mode")
    @pytest.mark.xray("QA-266947")
    @add_markers("regression")
    def test_verify_Pagefetchtime_ns_httplatency_for_zia_lt_webprobe_from_db(self):

        """
            This function tests the Pagefetchtime_ns and httplatency values in webload db for ZIA webprobe
            when ZIA is in tunnel1 mode.

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("DT session - ZIA WEBLOAD: Verify Pagefetchtime_ns and"
                             " httplatency from Webload Db in Tunnel None mode")

        query = "SELECT PageFetchTime_ns,HTTPLatency_ns from WebDataLT"
        rows = self.db_handler.execute_query(query)
        status = True
        currentrow = 0
        msg = " "

        if len(rows) == 0:
            status = False
            msg = "No data inserted to the WebdataLt for DT Session"

        for data in rows:
            currentrow += 1

            self.logger_obj.info(f"Validating pageFetchTime_ns = {str(data[0])} from Webload DB for ZIA Webprobe in row = {currentrow}")
            assert len(str(data[0])) != 0, f"No value found at 'PageFetchTime_ns' in row = {currentrow}"

            if len(str(data[0])) >= 1 and int(data[0]) <= 0:
                assert False, f"In row = {currentrow} , 'PageFetchTime_ns' value is {data[0]} which is invalid"

            self.logger_obj.info(f"Verifying HTTP Latency = {str(data[1])} for ZIA Webprobe in row = {currentrow}")
            assert len(str(data[1])) != 0, f"No value found at 'HTTPLatency_ns' in row = {currentrow}"

            if len(str(data[1])) >= 1 and int(data[1]) <= 0:
                assert False, f"In row = {currentrow} , 'HTTPLatency_ns' value is {data[1]} which is invalid"

        assert status, msg

    @allure.title("DT session - ZIA WEBLOAD: Verify Status Code and availability"
                  " from Webload DB for ZIA webprobe in tunnel none mode")
    @pytest.mark.xray("QA-266947")
    @add_markers("regression")
    def test_verify_status_code_and_availability_for_zia_lt_webprobe_from_db(self):

        """
            This function tests the validation of status code and availability for zia_webprobe
            in webload db when ZIA is in tunnel1 mode.

            It performs the following steps:
            1. Doing validation on status code for ZIA webload for zdx and checking availability
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        query = "SELECT StatusCode,Availability from WebDataLT"
        rows = self.db_handler.execute_query(query)
        status = True
        currentrow = 0
        msg = " "

        if len(rows) == 0:
            status = False
            msg = "No data inserted to the WebdataLt for DT Session"

        for data in rows:
            currentrow += 1

            self.logger_obj.info(f"Verifying Status Code is in ['200','301','302'] for ZIA webprobe in row = {currentrow}")
            assert len(str(data[0])) != 0, f"No value found at 'StatusCode' in row {currentrow}"

            if data[0] not in ['200', '301', '302']:
                assert False, "Status code is not in ['200', '301', '302]"

            self.logger_obj.info(f"Verifying Availability = {data[1]} for ZIA Webprobe in row = {currentrow}")
            assert len(str(data[1])) != 0, f"No value found at 'Availability' in row = {currentrow}"
            assert int(data[1]) == 100, f"Availability value should be 100 in row = {currentrow}"

        assert status, msg

    @allure.title("DT session - ZIA WEBLOAD: Verify DNS Time and web probe_success"
                  " from Webload DB for ZIA webprobe in tunnel none mode")
    @pytest.mark.xray("QA-266947")
    @add_markers("regression")
    def test_verify_dnstime_ns_and_probe_success_for_zia_lt_webprobe_from_db(self):

        """
            This function tests the validation of dnstime_ns and probe_success for zia_webprobe
             in webload db when ZIA is in tunnel none mode.
            It performs the following steps:
            1. Doing validation on dnstime_ns for ZIA webload for zdx and check web probe success
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        query = "SELECT * from WebDataLT"
        rows = self.db_handler.execute_query(query)
        status = True
        currentrow = 0
        msg = " "

        if len(rows) == 0:
            status = False
            msg = "No data inserted to the WebdataLt for DT Session"

        for item in rows:
            currentrow += 1
            data = item['DNSTime_ns']
            statuscode = item['StatusCode']
            ecode = item['ECode']
            availability = item['Availability']

            self.logger_obj.info(
                f"Validating values of DNSTime_ns = {str(data)} for ZIA webprobe in row = {currentrow}")
            assert len(str(data)) != 0, f"No value found at 'DNSTime_ns' in row = {currentrow}"

            if len(str(data)) >= 1 and int(data) <= 0:
                assert False, f"In row = {currentrow} , 'DNSTime_ns' value is {data} which is invalid"

            self.logger_obj.info(
                f"Validating Web Probe Success by checking statuscode = {statuscode},availability = {availability} and ecode = {ecode} for ZIA webprobe = {currentrow}")
            if int(availability) == 0 and int(statuscode) != 200 and int(ecode) != 0:
                assert False, f"In Row = {currentrow}, ZIA Web probe({item['URL']}) failed with ECODE = {item['ecode']}"

        assert status,msg

    @allure.title("DT session - Z-Tunnel None: Verify ZApp collecting data for a WebMonitor ")
    @pytest.mark.xray("QA-266946")
    @add_markers("regression")
    def test_verify_lt_zia_webprobe_data_from_log(self):
        """
            This function tests the validation the webload log for ZIA webprobe when ZIA is in tunnel None mode.
            It performs the following steps:
            1. Doing validation of logs for ZIA Web Probe DT session
            2  Doing validation if service discovery found of zpa webprobe or not
            3  Validating 'sd_url','sd_ip', 'sd_port', 'broker_name' and 'broker_ip'
            4  Validating Response Status: 200 for successful web probes
            5  Validating Saving data for host: to the db

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        zia_url = self.zia_webprobe_url
        status = True
        msg = ""
        current_probe = 0
        webload_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin='web')

        thread_list = set([])

        check = 0
        for item in webload_data.split("\n"):
            if "Starting monitor" in item and " SessionId: -1" not in item:
                check = 1

            if "ZWB::Service Discovery: Url" in item:
                if "Service type: DIRECT" in item:
                    if int(check) == 1:
                        res = re.findall(r"\[([^\[\]]*)\]", item)
                        temp = str(res[0])
                        thread_list.add(temp)
                        check = 0
                    else:
                        check = 0

        if len(thread_list) == 0:
            assert False, "ZIA webload probes are not available in the webload log file"

        for i in thread_list:
            current_thread_id = str(i)
            flag = 0
            sd_response_found = 0
            run_in = 0
            check_sd_url = 0
            check_sd_ip = 0
            check_sd_port = 0
            response_status = 0
            check_response_status = 0
            save_data_for_host = 0
            run_in_text = ""
            sd_text = ""
            starting_text = ""
            sd_url = ""
            sd_ip = ""
            sd_port = ""

            self.logger_obj.info(f"######################### Validating webprobe data for thread id = [{current_thread_id}] #########################")

            for item in webload_data.split("\n"):
                if "Starting monitor" in item and current_thread_id in item and "SessionId: -1" not in item:
                    current_probe += 1
                    self.logger_obj.info(f"DT Session - Validating logs for ZIA Web Probe = {current_probe}")
                    run_in = 1
                    run_in_text = item

                if len(run_in_text) >= 1:
                    starting_text = run_in_text

                if current_thread_id in item and "ZWB::Service Discovery: Url" in item and "Service type: DIRECT" in item and flag == 0:
                    current_thread_id = i
                    flag = 1

                    if run_in == 0:
                        flag = 0
                        run_in_text = ""

                if str(current_thread_id) in item and flag == 1 and run_in == 1:
                    if len(run_in_text) >= 1:
                        self.logger_obj.info(run_in_text)
                        run_in_text = ""

                    if "ZWB::Service Discovery: Url" in item:
                        if "DIRECT" in item:
                            sd_response_found = 1
                        else:
                            flag = 0
                            run_in = 0
                            continue

                        sd_text = item
                        sd_url = re.search("Url: (.*?)\\Success:", item).group(1)
                        sd_url = sd_url.strip()
                        if len(sd_url) > 1:
                            check_sd_url = 1

                        sd_ip = re.search("Destination Ips: (.*?)\\Destination Ports:", item).group(1)
                        sd_ip = sd_ip.strip()
                        if len(sd_ip) > 1:
                            check_sd_ip = 1

                        sd_port = re.search("Destination Ports: (.*?)\\SmeIp:", item).group(1)
                        sd_port = sd_port.strip()
                        if len(sd_port) > 1:
                            check_sd_port = 1

                    if "Response Status:" in item:
                        check_response_status = 1
                        temp = item.split("Response Status: ", 1)
                        response_status = str(temp[1])
                        response_status = response_status.strip()
                        self.logger_obj.info(f"Response Status Code:{response_status}")

                    if "Saving data for host:" in item:
                        save_data_for_host = 1

                    if "Monitor done:" in item and run_in == 1:
                        self.logger_obj.info(f"SD Response Found: {sd_response_found}")
                        self.logger_obj.info(f"SD URL: {sd_url}")
                        self.logger_obj.info(f"SD IP: {sd_ip}")
                        self.logger_obj.info(f"SD Port: {sd_port}")

                        if check_response_status == 0:
                            status = False
                            msg = f"'Response Status' logging not found in the probe. Probe = {starting_text}"
                        assert status, msg

                        if response_status and str(response_status[0]) in ['4']:
                            status = False
                            msg = f"Invalid Response Status = {response_status}. Probe = {starting_text}"
                        assert status, msg

                        if check_sd_url == 0:
                            status = False
                            msg = f"Service Discovery's URL attribute value is missing. Log - '{sd_text}'"
                        assert status, msg

                        if check_sd_ip == 0:
                            status = False
                            msg = f"Service Discovery's IP attribute value is missing. Log - '{sd_text}'"
                        assert status, msg

                        if check_sd_port == 0:
                            status = False
                            msg = f"Service Discovery's PORT attribute value is missing. Log - '{sd_text}'"
                        assert status, msg

                        if save_data_for_host == 0:
                            status = False
                            msg = f" 'Saving data for host:' not found for current web probe "
                        assert status, msg

                        self.logger_obj.info(
                            f"DT Session - ZIA Web Probe = {current_probe} in tunnel mode"
                            f" is Successful with Thread ID = " + str(current_thread_id))
                        self.logger_obj.info(
                            "#######################################################################################################")

                        flag = 0
                        sd_response_found = 0
                        run_in = 0
                        check_sd_url = 0
                        check_sd_ip = 0
                        check_sd_port = 0
                        response_status = 0
                        check_response_status = 0
                        save_data_for_host = 0
                        run_in_text = ""
                        sd_text = ""
                        starting_text = ""
                        sd_url = ""
                        sd_ip = ""
                        sd_port = ""

                if flag == 1 and str(current_thread_id) in item and run_in == 1:
                    self.logger_obj.info(item)

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Deletion of forward profile and app profile
        2. Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Closing db connection after verification")
        self.db_handler.close_db_connection()

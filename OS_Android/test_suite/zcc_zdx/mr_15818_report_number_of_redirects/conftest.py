import json,os
import time
import pytest
import os, shutil
import sys
import platform

from common_lib.mobileadmin.deviceoverview import DeviceOverview
from common_lib.adminzia.admingroupuser import AdminGroupUser
from common_lib.mobileadmin.appprofile import AppProfile
from common_lib.mobileadmin.forwardingprofile import ForwardingProfile
from common_lib.adminzia.ziahelper import *
from OS_Android.library.ui_zcc import Zcc
from common_lib.common import logger,constants
from common_lib.common.log_ops import *
from OS_Android.library.ops_system import SysOps
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
from common_lib.mobileadmin.trustednetwork import TrustedNetwork
from OS_Android.library.database_parse import <PERSON>b<PERSON>andler
from OS_Android.library.database_parse import LogHandler
from OS_Android.library.zdx_admin import ZDX_ADMIN

config_path = os.path.join(os.getcwd(),"config", os.environ.get("CONFIG_FILE"))
redirect_config_path = os.getcwd() + (f"\\OS_Android\\test_suite\\zcc_zdx\\mr_15818_report_number_of_redirects\\"
                                          f"Config\\number_of_redirects.json")

with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)

with open(redirect_config_path) as file:
    redirect_config_json = json.load(file)

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")
device_overview=DeviceOverview(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)
const_obj=constants.Utils()
sys_ops=SysOps(logger_obj,variables["CLOUD"])
export_logs=FetchAndroidLogs(logger_obj)
create_zia_user=AdminGroupUser(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)
zia_activate=ZiaHelper(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)
log_ops=LogOps(log_handle=logger_obj)
pcap_ops=PcapOps(log_handle=logger_obj)


app_profile_obj = AppProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
forwarding_profile_obj=ForwardingProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
trusted_network = TrustedNetwork(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
zdx_admin = ZDX_ADMIN(cloudname=variables["CLOUD_DETAILS"]["ZDX"], configfile=variables,
                                   logger=logger_obj)

db_handler = DbHandler()
log_handler = LogHandler()

@pytest.fixture(scope="class",autouse=True)
def setup_teardown():
    app_profile = app_profile_obj
    forwarding_profile = forwarding_profile_obj

    zcc = Zcc(start_app=True, log_handle=logger_obj)

    zcc.logger.info("Deleting App profile")
    try:
        result = app_profile.delete_app_profile(operating_system="Android")
        assert result[0], result[1]
    except Exception as e:
        zcc.logger.info(str(e))

    zcc.logger.info("Deleting Forwarding profile")
    try:
        result = forwarding_profile.delete_forwarding_profile()
        assert result[0], result[1]
    except Exception as e:
        zcc.logger.info(str(e))

    zcc.logger.info("Creating Forwarding profile")
    result = forwarding_profile.create_forwarding_profile()
    assert result[0], result[1]

    zcc.logger.info("Creating App profile")
    result = app_profile.create_app_profile(operating_system="Android")
    assert result[0], result[1]

    zcc.logger.info("Creating webmonitor and traceroute for Report number of redirects for Webload Main page ")
    zdx_admin.create_application_and_monitors_support_monitor_dropped(configs=redirect_config_json)

    logged_in = zcc.validate_zcc_logged_in()
    if logged_in[0]:
        logger_obj.info("Clear logs from zcc app")
        result = zcc.clear_logs()
        assert result[0], result[1]

        logger_obj.info("Logout from zcc")
        result = zcc.logout()
        assert result[0], result[1]

    result = zcc.login(variables["ZIA_USER_ID"], variables["ZIA_USER_PASSWORD"],
                      variables["ZPA_USER_ID"], variables["ZPA_USER_PASSWORD"])
    assert result[0], result[1]

    zcc.logger.info("Validating if ZDX service status is ON")
    result = zcc.verify_service_status(service_status="on", service_type='ZDX')
    assert result[0], result[1]

    logger_obj.info("Sleep 7 mins :: Collecting ZDX db data data")
    time.sleep(420)

    logger_obj.info("Fetch logs from android device")
    result = zcc.export_logs()
    assert result[0], result[1]

    logger_obj.info("Export logs from android device")
    result = export_logs.read_export_log_mail()
    assert result[0], result[1]

    logger_obj.info("Deleting webprobe from ZDX ADMIN UI")
    zdx_admin.delete_application_and_monitors_support_monitor_dropped(configs=redirect_config_json)


    yield

    zcc.logger.info("Deleting App profile")
    try:
        result = app_profile.delete_app_profile(operating_system="Android")
        assert result[0], result[1]
    except Exception as e:
        zcc.logger.info(str(e))

    zcc.logger.info("Deleting Forwarding profile")
    try:
        result = forwarding_profile.delete_forwarding_profile()
        assert result[0], result[1]
    except Exception as e:
        zcc.logger.info(str(e))

    logged_in = zcc.validate_zcc_logged_in()
    if logged_in[0]:
        zcc.logger.info("Clear logs from zcc app")
        result = zcc.clear_logs()
        assert result[0], result[1]

        zcc.logger.info("Logout from zcc")
        result = zcc.logout()
        assert result[0], result[1]

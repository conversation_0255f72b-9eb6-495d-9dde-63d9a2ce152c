import pytest
import os, sys
import allure, json
import requests
from common_lib.Custom_Markers import add_markers, zcc_mark_version, zdx_mark_version  # Required!
from OS_Android.test_suite.zcc_zdx.mr_15818_report_number_of_redirects import conftest

inputs = [
    pytest.param('https://account.box.com/login', marks=pytest.mark.xray("QA-283294")),
    pytest.param('https://amazon.com', marks=pytest.mark.xray("QA-283295")),
    pytest.param("https://www.drive.google.com", marks=pytest.mark.xray("QA-283296")),
    pytest.param("http://www.gnu.org", marks=pytest.mark.xray("QA-283297")),
    pytest.param("http://www.amazon.com", marks=pytest.mark.xray("QA-283298")),
    pytest.param("http://amazon.com", marks=pytest.mark.xray("QA-283299"))
]


def get_redirect_count(url) -> int:
    """
    Function to get redirect count of a webpage
    """
    response = requests.get(url, allow_redirects=True)
    redirect_count = len(response.history)
    return redirect_count


class TestNumberOfRedirects:

    @allure.title("Setup Method")
    def setup_class(self):
        """
                This function performs the setup process for the test class.
                It includes the following steps:
                1. Creation of forward profile and app profile
                2. Logout zcc and clear logs
                3. Login to ZCC
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.variables = conftest.variables
        self.logger_obj = conftest.logger_obj
        self.zdx_admin = conftest.zdx_admin
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_webload.db"

        self.logger_obj.info("Creating webload db connection")
        self.db_handler.create_db_connection(self.db_path)

    @allure.title("Verify Report number of redirects for Webload Main page")
    @pytest.mark.parametrize("url", inputs)
    @add_markers("regression")
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_webprobe_redirect(self, url):
        """
             This function Verify Report number of redirects for Webload Main page

            It performs the following steps:
            1. Doing validation on webload redirect count column
            Args:
                self (object): Instance of the test class.
                Name of the table which needs to validate
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(f"Verifying RedirectCount from WebData for URL = {url}")
        status = True
        msg = ""
        query = f"SELECT RedirectCount from WebData WHERE URL='{url}'"
        output = self.db_handler.execute_query(query)

        if len(output) == 0:
            msg = f"No Data found for URL = {url}"
            pytest.skip(msg)

        redirect_count = get_redirect_count(url)
        self.logger_obj.info(f"Fetched redirect count from requests.get is = {redirect_count}")
        assert output[0][0] == redirect_count
        self.logger_obj.info(f"Found correct redirect count in the db for url = {url}")

    def teardown_class(self):

        self.logger_obj.info("Closing db connection after verification")
        self.db_handler.close_db_connection()


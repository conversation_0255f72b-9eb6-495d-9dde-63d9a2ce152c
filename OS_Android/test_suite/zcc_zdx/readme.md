# ZDX_DB_Test - Requirements
## Prerequisites:
    1) On MA compnay and user must be configured.
    2) On Android device zcc version should be installed
    3) There should not any configuration present on ZIA
    4) ZDX service should be enabled


## Commands to Execute Automation:
**Running All Cases:** python trigger_automation.py --config <config.json> --testcases test_suite/zcc_zdx

**Running One Suite from zcc_zdx folder:** python trigger_automation.py --config config.json --testcases test_suite/zcc_zdx/suite_name

**Running One Test File from zcc_zdx folder:** python trigger_automation.py --config config.json --testcases test_suite/zcc_zdx/suite_name/*.py


# ZCC_ZDX

This test folder is designed to test the functionality of multiple feature of ZDX. Before running the test suite, please ensure that you have set up the necessary configurations in the `config.json` file.
This file should be a copy of the `ZDX_Automation_Config_Template.json` file, with any necessary modifications made.

## Setting up the Configuration File

1. Make a copy of the `ZDX_Automation_Config_Template.json` file and rename it to `config.json` and paste it to `zapp_automation\config` folder.
2. Open the `config.json` file and update the required configurations based on your specific setup.
3. Save the changes and close the file.
4. For successful run of (mr_9366_deep_tracing), make sure to configure all required fields

`ZDX_Automation_Config_Template.json`

```commandline
{
  "ZIA_USER_ID" : "<EMAIL>",
  "ZIA_USERNAME" : "abc",
  "ZIA_USER_PASSWORD" : "ADD_YOUR_PASSWORD_HERE",
  "ZIA_ADMIN_ID" : "<EMAIL>",
  "ZIA_ADMIN_PASSWORD" : "ADD_YOUR_PASSWORD_HERE",
  "ZPA_ADMIN_ID" : "<EMAIL>",
  "ZPA_ADMIN_PASSWORD" : "ADD_YOUR_PASSWORD_HERE",
  "ZPA_USER_ID" : "<EMAIL>",
  "ZPA_USER_PASSWORD" : "ADD_YOUR_PASSWORD_HERE",
  "applicationName":"https://zapplogs.corp.zscaler.com",
  "DEVICE_PIN_OR_PASSWORD": "1234",              # Device pin or password should be configured in config.json
  "CLOUD":"zscalerbeta",
  "ZDX_DB_PATH": "D:\\devraj_automation\\",      # for example D:\\devraj_automation\\ path of Zapp automation directory
  "ZPA_URL": "http://device.com",                # ZPA Application URL, must be a ZPA app not ZIA, should be configured before running automation
  "ZIA_WEBPROBE":"https://zscaler.zoom.us",      # ZIA Webprobe URL, must be a ZIA app not ZPA, should be configured before running automation
  "ZIA_TRACEROUTE": "zscaler.zoom.us",           # ZIA Traceroute domain, must be a ZIA app not ZPA, should be configured before running automation
  "ZPA_TRACEROUTE": "device.com",                # ZPA Traceroute domain, must be a ZPA app not ZPA, should be configured before running automation
  "LOG_LOCATION": "D:\\devraj_automation\\",     # for example D:\\devraj_automation\\ path of Zapp automation directory
  "CLOUD_DETAILS": {
        "ZIA": "zscalerbeta",
        "ZDX": "zdxbeta",
        "ZPA": ""
    },

    "ZDX_ADMIN":{
        "ZDX_ADMIN_ID" : "<EMAIL>",   # ZDX Admin creds to configure DT session
        "ZDX_ADMIN_PASSWORD" : "ADD_YOUR_PASSWORD_HERE",
        "ZDX_ADVANCED_PLUS" : 0,                           # If you ZDX SKU is advanced plus, change this value to 1
        "ZDX_APPLICATION":[
            {
                "application": {
                    "name": "Automation_ZIA"
                },
                "web_probe": {

                    "name":"web_probe",
                    "url":"https://www.youtube.com"
                },
                "tracert_icmp": {
                    "name":"trace_icmp",
                    "host":"youtube.com",
                    "maxPacketCount":10
                },
                "DT":[
                    {
                        "DT_PROTOCOL": "ICMP",
                        "BANDWIDTH_TEST": false,
                        "name": "Automation_DT_Session",
                        "devicename": "Pixel 7a",
                        "durationMinutes": 5
                    }
                ]
            },
          {
                "application": {
                    "name": "Automation_ZPA"
                },
                "web_probe": {

                    "name":"zpa_webprobe",
                    "url":"http://device.com"
                },
                "tracert_icmp": {
                    "name":"zpa_traceroute",
                    "host":"device.com",
                    "maxPacketCount":10
                },
                "DT":[
                    {
                        "DT_PROTOCOL": "ICMP",
                        "BANDWIDTH_TEST": false,
                        "name": "Automation_DT_Session_Zpa",
                        "devicename": "Pixel 7a",
                        "durationMinutes": 5
                    }
                ]
            },
          {
                "application": {
                    "name": "Extra_for_update_policy"
                },
                "web_probe": {

                    "name":"web_probe",
                    "url":"https://www.google.com"
                },
                "tracert_icmp": {
                    "name":"trace_icmp",
                    "host":"google.com",
                    "maxPacketCount":10
                }
            }
        ]

    }
}

```


- `devicename`: For Successful DT run (mr_9366_deep_tracing), device name should be configured correctly for example my device name is "Pixel 7a"
Make sure to replace the placeholder values with the appropriate credentials and configurations for your specific setup before running the test suite.
- `ZDX_ADVANCED_PLUS`: For successfully running (mr_15811_restrict_number_of_active_monitors) should be configured correctly
- `DEVICE_PIN_OR_PASSWORD`: For successfully running (mr_16086_zdx_optimization) should be configured correctly
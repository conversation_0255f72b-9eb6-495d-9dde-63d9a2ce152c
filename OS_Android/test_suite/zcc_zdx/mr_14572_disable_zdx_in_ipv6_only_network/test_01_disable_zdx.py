import time, os
import pytest, allure
import json, re
from OS_Android.test_suite.zcc_zdx.mr_14572_disable_zdx_in_ipv6_only_network import conftest
from common_lib.Custom_Markers import add_markers,zcc_mark_version,zdx_mark_version
from common_lib.helper_function import help_return
from OS_Android.library.ui_zcc import Zcc
from common_lib.common.common_ui import *
from OS_Android.library.android_element import AndroidElements


upm_to_search = ["ZLTU: LT Uploader stop called",
                 "ZLTU: Stopping LT uploader",
                 "ZLTU: LT Uploader Stopped"
                 ]

device_stats_to_search = ["ZDSPlugin: LT sessions stop called",
                          "ZDSPlugin: Abort all LT sessions"
                          ]
webload_to_search = ["ZWLT: Webload stop called",
                     "ZWLT: Abort all LT sessions",
                     "ZWLT: Stopping session"
                     ]
traceroute_to_search = ["ZUpmTraceroute:: stopLtSession: In",
                        "ZUpmTraceroute: stopLtSession:Out with Success"
                        ]


class TestDisableZdxInIpv6:
    @allure.title("Setup Method")
    def setup_class(self):

        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. It will check current active network on device
        2. Disable Wifi (dual stack) and Enables Mobile data (Ipv6 only)
        3. Login to ZCC, fetch and export logs
        4. If Ipv6 only network is not present it will skip all the cases
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.variables = conftest.variables
        self.logger_obj = conftest.logger_obj
        self.export_logs = conftest.export_logs
        self.service_ent = conftest.service_ent
        self.zdx_admin = conftest.zdx_admin
        self.device_overview = conftest.device_overview
        self.app_profile_obj = conftest.app_profile_obj
        self.forwarding_profile_obj = conftest.forwarding_profile_obj
        self.is_ipv6_only = conftest.is_ipv6_only
        self.password = conftest.password
        self.zcc = Zcc(log_handle=self.logger_obj, start_app=True)
        self.current_network = "IPV6_Only"    # for logging purpose only

        self.tray_file = self.log_handler.get_log_file_name(plugin_name="tray")
        assert self.tray_file[0], self.tray_file[0]
        self.tray_file = self.tray_file[1]

        self.tunnel_file = self.log_handler.get_log_file_name(plugin_name="tunnel")
        assert self.tunnel_file[0], self.tunnel_file[0]
        self.tunnel_file = self.tunnel_file[1]

        self.logger_obj.info("Deleting App profile")
        try:
            result = self.app_profile_obj.delete_app_profile(operating_system="Android")
            assert result[0], result[1]
        except Exception as e:
            self.logger_obj.info(str(e))

        self.logger_obj.info("Deleting Forwarding profile")
        try:
            result = self.forwarding_profile_obj.delete_forwarding_profile()
            assert result[0], result[1]
        except Exception as e:
            self.logger_obj.info(str(e))

        self.logger_obj.info("Creating Forwarding profile")
        result = self.forwarding_profile_obj.create_forwarding_profile()
        assert result[0], result[1]

        self.logger_obj.info("Creating App profile")
        result = self.app_profile_obj.create_app_profile(operating_system="Android")
        assert result[0], result[1]

        if not("IS_IPV6_ONLY_NETWORK_PRESENT_ON_MOBILE_DATA" in conftest.variables):
            pytest.skip(f" 'IS_IPV6_ONLY_NETWORK_PRESENT_ON_MOBILE_DATA' Key is not present in the config.json ")
        elif not self.is_ipv6_only:
            pytest.skip('Skipping all cases :: IPV6 only network is not present')

        self.logger_obj.info("Creating application and monitor")
        self.zdx_admin.create_application_and_monitors()

        network_status_wifi = self.sys_ops.check_connection(connection="WIFI")
        if network_status_wifi[0]:
            self.sys_ops.toggle_wifi(disable=True)

        network_status_mobile_data = self.sys_ops.check_connection(connection="MOBILE_DATA")
        if not network_status_mobile_data[0]:
            self.sys_ops.toggle_mobile_data(enable=True)

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.logger_obj.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.logger_obj.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"], conftest.variables["ZPA_USER_ID"],
                           conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]

        self.logger_obj.info("Sleep 60s :: Collecting logs data")
        time.sleep(60)

        self.logger_obj.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.logger_obj.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.logger_obj.info("Sleep 5s :: Checking for ipv6 only network from logs")
        time.sleep(5)

        is_ipv6_from_app_info = self.export_logs.appinfo_file_values('IPv6 Address')
        self.logger_obj.info(f"Is current network ipv6 only : {is_ipv6_from_app_info}")

        if 'true' not in is_ipv6_from_app_info:
            pytest.skip(f"Skipping all the cases :: 'Current network is not supporting ipv6 only network' ")

    # Helper function to validate plugin stopped
    @allure.title("Check LT plugin stopped or not for given plugin")
    def check_lt_plugin_stopped(self, plugin_name: str, except_failure: bool = False):
        """
        This function performs the log validation of LT plugin stopped
        Args:
              self (object): Instance of the test class.
        Returns:
              None
        Raises:
              AssertionError: If any of the steps fail.
        """

        file_name = ""
        to_search = []
        status = True

        if plugin_name == "web":
            to_search = webload_to_search
        elif plugin_name == "upm":
            to_search = upm_to_search
        elif plugin_name == "traceroute":
            to_search = traceroute_to_search
        elif plugin_name == "deviceStats":
            to_search = device_stats_to_search
        else:
            return False, "Pass correct plugin name to search for plugin stop"

        log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH, plugin=plugin_name)
        if log_data is None:
            self.logger_obj.info("Not able to fetch data from log file/ log file is missing")
            assert False, "Not able to fetch data from  log file"

        if except_failure:
            self.logger_obj.info(f"Validating {plugin_name} for failure of plugin stop")
        else:
            self.logger_obj.info(f"Validating {plugin_name} for plugin stop")

        for pattern in to_search:
            status = False
            for log_line in log_data.split("\n"):
                if pattern in log_line:
                    status = True
                    break

            if except_failure and status:
                return False, f"Expecting Failure {pattern}, but we found in the logs"
            if not except_failure and not status:
                return False, f"Fail :: Expecting log found {pattern}, but log is not present in the log file"

        return True, "SUCCESS:: Search is successful"

    @allure.title("Verify in Ipv6 only network ZDX should go to UNSUPPORTED_NW")
    @pytest.mark.xray(["QA-288600","QA-288601","QA-288603"])
    @add_markers("regression")
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_unsupported_network_error_in_ipv6_only_network(self):
        """
        Verify in Ipv6 only network ZDX shouldn't go to SERVER_ERROR
        Args:
        It performs the following steps:
        1. It will switch network to ipv6 only network which is mobile data in this case and verify the behaviour
           QA-288600:: ZDX shouldn't show server error
           QA-288601:: ZDX should go to unsupported_network error in ipv6 only
           QA-288603:: Verify In IPv6 network error should be always Unsupported network.
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.logger_obj.info(f"Current network is -> {self.current_network} as per setup")

        self.logger_obj.info(f"Verify in Ipv6 only network ZDX shouldn't go to SERVER_ERROR")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="unsupported_network")
        assert result[0], result[1]


    @allure.title("Verify learn more section should show 'Current network is not supported by ZDX yet' in IPv6 only network")
    @pytest.mark.xray("QA-288602")
    @add_markers("regression")
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_learn_more_section_in_ipv6_only_network(self):
        """
        Verify learn more section should show "Current network is not supported by ZDX yet" in IPv6 only network
        Args:
        It performs the following steps:
        1. It will switch network to ipv6 only network which is mobile data in this case and verify the behaviour
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(f"Current network is -> {self.current_network} as per setup")
        self.logger_obj.info(f"Verify learn more section should show 'Current network is not supported by ZDX yet'"
                             f" in IPv6 only network")
        result = self.zcc.verify_zdx_unsupported_nw_learn_more_error()
        assert result[0],result[1]

    @allure.title("Verify in case current active network’s LinkProperties do not have any IPv4 DNS address or"
                  " address is empty it is considered as IPv6 network.(Deciding ipv6) ")
    @pytest.mark.xray("QA-288605")
    @add_markers("regression")
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_link_properties_ipv6_only(self):
        """
        Verify in case current active network’s LinkProperties do not have any IPv4 DNS address or address is
         empty it is considered as IPv6 network.(Deciding ipv6)
        Args:
        It performs the following steps:
        1. It will check App info log and verify if LinkProperties do not have any IPv4 DNS address
        it will be considered as ipv6 only
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(f"Current network is -> {self.current_network} as per setup")
        self.logger_obj.info(f"Verify in case current active network’s LinkProperties do not have any IPv4 DNS"
                             f"address or address is empty it is considered as IPv6 network.(Deciding ipv6) ")

        act_net = self.export_logs.appinfo_file_values('Active Network')
        self.logger_obj.info(f"Current Active Network is {act_net}")

        self.logger_obj.info("Fetching ip addr of active network")
        ipv4 = self.sys_ops.active_ipv4(act_net)
        ipv6 = self.sys_ops.active_ipv6(act_net)

        if len(ipv4)!= 0:
            assert False, "Current network is ipv6 only, it should have ipv4 dns"
        if len(ipv6) == 0:
            assert False, "Current network not having ipv6 dns, it should have"

        is_ipv6_from_app_info = self.export_logs.appinfo_file_values('IPv6 Address')
        self.logger_obj.info(f"Is current network ipv6 only from Appinfo log file : {is_ipv6_from_app_info}")

        if 'true' not in is_ipv6_from_app_info:
            assert False, "IPv6 Address value should be true in Appinfo log files"

    @allure.title("IPv6 only network: Verify when in IPv6 only network, keep Alive is "
                  "not trying to constantly trying to start ZDX")
    @pytest.mark.xray("QA-288617")
    @add_markers("regression")
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_keep_alive_non_trying_constantly_to_start_zdx_in_ipv6only_network(self):
        """
        IPv6 only network: Verify when in IPv6 only network, keep Alive is not trying to constantly trying to start ZDX
        Args:
        It performs the following steps:
        1. It will check ZCC_Android logs and verifies keep alive is not starting zdx again and again in ipv6 only network
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.logger_obj.info(f"Current network is -> {self.current_network} as per setup")

        start_line = "NetworkUpdateListener:updateZDXSupportedNW : try to stop ZDX in ipv6 network"
        log_expected = "ZDXWorkManager:stopping ZDX as isLoggedOut: false isIPv6Only:true isDeviceIdleMode:false"
        log_unexpected = "ZDXWorkManager:startZDX  called"

        self.logger_obj.info(f"Validating {log_expected} in ipv6 only network")
        result = help_return(self.log_obj.search_log_file, file=self.tray_file, words_to_find_in_line=log_expected,
                             directory=self.const_obj.LOG_PATH, start_line=start_line)
        assert result[0], result[1]

        self.logger_obj.info(f"Validating failure:: {log_unexpected} in ipv6 only network")
        result = help_return(self.log_obj.search_log_file, file=self.tray_file, words_to_find_in_line=log_unexpected,
                             directory=self.const_obj.LOG_PATH, start_line_number=start_line, failure_expected = True)
        assert result[0], result[1]

    @allure.title("Disable ZDX from MA, login to ZCC in IPv6 only network, enable ZDX back from MA,"
                  " verify UPM will not be started")
    @pytest.mark.xray("QA-288613")
    @add_markers("regression")
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_zdx_not_start_in_ipv6_only_when_enable_disable_from_ma(self):
        """
        Disable ZDX from MA, login to ZCC in IPv6 only network, enable ZDX back from MA,
        verify UPM will not be started

        Args:
        It performs the following steps:
        1. It will log out zcc if logged in
        2. Disable ZDX from MA
        3. Login ZCC in ipv6 only network, and will check ZDX tab should not be there
        4. Enable ZDX and check ZDX should show unsupported network
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(f"Current network is -> {self.current_network} as per setup")
        self.logger_obj.info("Disable ZDX from MA, login to ZCC in IPv6 only network, enable ZDX back from MA,"
                             " verify UPM will not be started")

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.logger_obj.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.logger_obj.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        self.logger_obj.info("Disabling Wi-Fi / Running dual stack network")
        result = self.sys_ops.toggle_wifi(disable=True)
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Disable ZDX service from MA")
        result = self.service_ent.toggle_zdx(action=False)
        assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                           conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(5)

        self.logger_obj.info("Check status of service, ZDX tab should not be present")
        result = self.zcc.service_ent_status(service="ZDX", current_status='Disable')
        assert result[0], result[1]

        self.logger_obj.info("Enable ZDX from MA")
        result = self.service_ent.toggle_zdx(action=True)
        assert result[0], result[1]

        self.logger_obj.info("Update Policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(5)

        self.logger_obj.info("Check service status, ZDX tab should be present")
        result = self.zcc.service_ent_status(current_status='Enable')
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Verifying ZDX should show Unsupported Network ")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="unsupported_network")
        assert result[0], result[1]


    @allure.title("Verify the ZDX health in MA would show as active when ZDX comes back up in dual stack or ipv4 only network")
    @pytest.mark.xray(["QA-288615","QA-288616"])
    @add_markers("regression")
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_zdx_health_from_ma_on_switch_between_ipv6_only_and_dual_stack(self):
        """
        QA-288616:: Verify the ZDX health in MA would show as inactive when ZCC is in ipv6 only network
        QA-288615:: Verify the ZDX health in MA would show as active when ZDX comes back up in dual stack or ipv4 only network
        Args:
        It performs the following steps:
        1. It will check ZDX health status from MA
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("Enabling Mobile Data / Running Ipv6 only network")
        result = self.sys_ops.toggle_mobile_data(enable=True)
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Disabling Wi-Fi / Running dual stack network")
        result = self.sys_ops.toggle_wifi(disable=True)
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Verifying ZDX should show Unsupported Network ")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="unsupported_network")
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Updating Policy")
        result = self.zcc.update_policy()
        assert result[0],result[1]
        time.sleep(3)


        self.logger_obj.info("QA-288616:: Verify the ZDX health in MA would show as inactive when ZCC is in ipv6 only network")
        result = self.device_overview.search_enrolled_devices(action="verify_zdx_health_inactive",fetch_this_device_details = True)
        assert result[0],result[1]

        self.logger_obj.info("Enable Wi-Fi / Running dual stack network")
        result = self.sys_ops.toggle_wifi(enable=True)
        assert result[0], result[1]
        time.sleep(2)

        self.logger_obj.info("Validating if ZDX service status is ON")
        result = self.zcc.verify_service_status(service_status="on", service_type='ZDX')
        assert result[0], result[1]
        time.sleep(2)

        self.logger_obj.info("Updating Policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(2)

        self.logger_obj.info("QA-288615 :: Verify the ZDX health in MA would show as active when ZCC switch from ipv6 only to dual stack network")
        result = self.device_overview.search_enrolled_devices(action="verify_zdx_health_active",
                                                              fetch_this_device_details=True)
        assert result[0], result[1]


    @allure.title("Verify In case ZDX is already turned off by the user. It will switch to unsupported network error on"
                  " changing network from ipv4 to ipv6 (ZDX OFF state)")
    @pytest.mark.xray(["QA-288606","QA-288612"])
    @add_markers("regression")
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_zdx_turn_off_on_network_switch(self):
        """
        QA-288606:: Verify In case ZDX is already turned off by the user. It will switch to unsupported network
        error on changing network from ipv4 to ipv6 (ZDX OFF state)

        QA-288612:: Verify ZDX off scenario in case of network change from ipv6 to ipv4

        Args:
        It performs the following steps:
        1. It will switch network to dual stack and turn off ZDX
        2. Switch network to ipv6 only
        3. ZDX will switch to unsupported network error
        4. Again after switching to dual stack, ZDX should turned ON
        it will be considered as ipv6 only
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("QA-288606:: Verify In case ZDX is already turned off by the user. It will switch"
                             " to unsupported network error on changing network from ipv4 to ipv6 (ZDX OFF state)")

        self.logger_obj.info("Enabling Wi-Fi / Running dual stack network")
        result = self.sys_ops.toggle_wifi(enable=True)
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Validating if ZDX service status is ON")
        result = self.zcc.verify_service_status(service_status="on", service_type='ZDX')
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Turning OFF ZDX")
        result = self.zcc.toggle_service(service="ZDX", action=False)
        assert result[0],result[1]
        time.sleep(3)

        self.logger_obj.info("Disabling Wi-Fi / Running dual stack network")
        result = self.sys_ops.toggle_wifi(disable=True)
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Verifying ZDX should show Unsupported Network ")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="unsupported_network")
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Enabling Wi-Fi / Running dual stack network")
        result = self.sys_ops.toggle_wifi(enable=True)
        assert result[0], result[1]
        time.sleep(5)

        result = self.zcc.refresh_zcc_ui(current_service="zdx")
        assert result[0], result[1]
        time.sleep(2)

        self.logger_obj.info("QA-288612:: Verify ZDX off scenario in case of network change from ipv6 to ipv4")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="off")
        assert result[0], result[1]

    @allure.title("Verify network switch scenario from IPv4(zdx on) to Ipv6(Unsupported network error)")
    @pytest.mark.xray(["QA-288608","QA-288609","QA-288610"])
    @add_markers("regression")
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_network_switch_ipv4_to_ipv6_to_ipv4(self):
        """
        QA-288608:: Verify network switch scenario from IPv4(zdx on) to Ipv6(Unsupported network error)
        QA-288609:: Verify network switch scenario from IPv4(zdx on) to Ipv6(Unsupported network error) to Ipv4 (zdx on)
        QA-288610:: Verify network switch scenario from IPv6(Unsupported network error) to Ipv4(zdx on)
        Args:
        It performs the following steps:
        1. It will switch network from dual stack to ipv6 only
        2. ZDX error state should change to Unsupported network.
        3. Verify all the zdx plugins stopped
        4. Verify android log file "DBG NetworkUpdateListener:updateZDXSupportedNW : try to stop ZDX in ipv6 network"
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("Verify network switch scenario from IPv4(zdx on) to Ipv6(Unsupported network error)")

        plugin_stop = ["Stop plugins called",
                       "ZPC: Stopped Traceroute Monitor",
                       "ZPC: Stopped WebLoad Monitor",
                       "ZPC: Stopped DeviceStats Monitor",
                       "ZSU: Uploader Stopped"]
        plugin_start = ["ZPC: Traceroute plugin started",
                       "ZPC: Webload plugin started",
                       "ZPC: Device stats plugin started"
                        ]


        self.logger_obj.info("Enabling Wi-Fi / Running dual stack network")
        result = self.sys_ops.toggle_wifi(enable=True)
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Enabling Mobile Data / Running Ipv6 only network")
        result = self.sys_ops.toggle_mobile_data(enable=True)
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Turning ON ZDX")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="off")
        if result[0]:
            result = self.zcc.toggle_service(service="ZDX", action=True)
            assert result[0], result[1]


        self.logger_obj.info("Validating if ZDX service status is ON")
        result = self.zcc.verify_service_status(service_status="on", service_type='ZDX')
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Restart Service")
        result = self.zcc.restart_service()
        assert result[0], result[1]
        time.sleep(2)

        self.logger_obj.info("Turning OFF ZDX")
        result = self.zcc.toggle_service(service="ZDX", action=False)
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Turning ON ZDX")
        result = self.zcc.toggle_service(service="ZDX", action=True)
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]
        time.sleep(5)

        self.logger_obj.info("Disabling Wi-Fi / Running dual stack network")
        result = self.sys_ops.toggle_wifi(disable=True)
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("QA-288608:: Verify network switch scenario from IPv4(zdx on) to Ipv6(Unsupported network error)")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="unsupported_network")
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.logger_obj.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]
        time.sleep(2)

        self.logger_obj.info("Validating all the plugins stop changing network from dual stack to ipv6 only")
        start_line = "ZPD: UPM policy downloader stop called"
        for pattern in plugin_stop:
            result = help_return(self.log_obj.search_log_file, file="ZSAUpm_",words_to_find_in_line=pattern,
                                 directory=self.const_obj.LOG_PATH, start_line = start_line)
            assert result[0], result[1]

        self.logger_obj.info("Validating 'NetworkUpdateListener:updateZDXSupportedNW:try to stop ZDX in ipv6 network'")
        pattern = "NetworkUpdateListener:updateZDXSupportedNW : try to stop ZDX in ipv6 network"
        start_line = "isIpv6Only : trueisIpv4Only : false"
        result = help_return(self.log_obj.search_log_file, file=self.tray_file,directory=self.const_obj.LOG_PATH,
                             words_to_find_in_line=pattern, start_line = start_line)
        assert result[0], result[1]

        self.logger_obj.info("Validating 'ZDXWorkManager:stopping ZDX as isLoggedOut: false isIPv6Only:true isDeviceIdleMode:false' ")
        pattern = "ZDXWorkManager:stopping ZDX as isLoggedOut: false isIPv6Only:true isDeviceIdleMode:false"
        start_line = 'updateZDXSupportedNW : try to stop ZDX in ipv6 network'
        result = help_return(self.log_obj.search_log_file, file=self.tray_file, directory=self.const_obj.LOG_PATH,
                             words_to_find_in_line=pattern, start_line = start_line)
        assert result[0], result[1]

        self.logger_obj.info("QA-288608:: Validated Successfully ")

        # ----------------   Switching back to dual stack and check zdx on state
        self.logger_obj.info("QA-288610:: Verify network switch scenario from IPv6(Unsupported network error) to Ipv4(zdx on)")

        self.logger_obj.info("Enabling Wi-Fi / Running dual stack network")
        result = self.sys_ops.toggle_wifi(enable=True)
        assert result[0], result[1]
        time.sleep(5)

        result = self.zcc.refresh_zcc_ui(current_service="zdx")
        assert result[0], result[1]
        time.sleep(2)

        self.logger_obj.info("Validating if ZDX service status is ON")
        result = self.zcc.verify_service_status(service_status="on", service_type='ZDX')
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.logger_obj.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]
        time.sleep(2)

        self.logger_obj.info("Validating 'NetworkUpdateListener:updateZDXSupportedNW : try to start ZDX in non ipv6 network'")
        pattern = "NetworkUpdateListener:updateZDXSupportedNW : try to start ZDX in non ipv6 network"
        start_line = 'updateZDXSupportedNW : try to stop ZDX in ipv6 network'
        result = help_return(self.log_obj.search_log_file, file=self.tray_file, directory=self.const_obj.LOG_PATH,
                             words_to_find_in_line=pattern, start_line=start_line)
        assert result[0], result[1]

        self.logger_obj.info("Validating all the plugins start after changing network from ipv6 only to dual stack ")
        start_line = "ZPD: Policy Downloader received updated Policy, new version"
        for pattern in plugin_start:
            result = help_return(self.log_obj.search_log_file, file="ZSAUpm_", words_to_find_in_line=pattern,
                                 directory=self.const_obj.LOG_PATH, start_line=start_line)
            assert result[0], result[1]

        self.logger_obj.info("QA-288610:: Validated Successfully ")

        self.logger_obj.info("QA-288609:: Validated Successfully  [network switch scenario from IPv4(zdx on) to"
                             " Ipv6(Unsupported network error) to Ipv4 (zdx on)]")


    @allure.title("Verify when in dual stack network, start a DT , while the LT session is running, switch the network"
                  " to IPv6 only, check ZDX will stop the lt plugins")
    @pytest.mark.xray("QA-288614")
    @add_markers("regression")
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_lt_plugin_stop_on_switch_from_ipv4_to_ipv6only(self):
        """
        Verify when in dual stack network, start a DT , while the LT session is running,
        switch the network to IPv6 only, check ZDX will stop the lt plugins

        Args:
        It performs the following steps:
        1. It will start DT session in dual stack network
        2. Switch to ipv6 only network
        3. Verify lt plugin should stop

        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.logger_obj.info("Verify when in dual stack network, start a DT , while the LT session is running, switch"
                             " the network to IPv6 only, check ZDX will stop the lt plugins")

        logged_in = self.zcc.validate_zcc_logged_in()
        if not logged_in[0]:
            result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                               conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0], result[1]

        self.logger_obj.info("Enabling Wi-Fi / Running dual stack network")
        result = self.sys_ops.toggle_wifi(enable=True)
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Validating if ZDX service status is ON")
        result = self.zcc.verify_service_status(service_status="on", service_type='ZDX')
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Creating DT session with ZIA application")
        self.zdx_admin.create_dt_session(application="Automation_ZIA")

        self.logger_obj.info("Sleep 60s :: Collecting DT session data")
        time.sleep(60)

        self.logger_obj.info("Disabling Wi-Fi / Running dual stack network")
        result = self.sys_ops.toggle_wifi(disable=True)
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Verifying ZDX should show Unsupported Network ")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="unsupported_network")
        assert result[0], result[1]
        time.sleep(1)

        self.logger_obj.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.logger_obj.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]
        time.sleep(1)

        result = self.check_lt_plugin_stopped(plugin_name="upm")
        assert result[0], result[1]

        result = self.check_lt_plugin_stopped(plugin_name="web")
        assert result[0], result[1]

        result = self.check_lt_plugin_stopped(plugin_name="traceroute")
        assert result[0], result[1]

        result = self.check_lt_plugin_stopped(plugin_name="deviceStats")
        assert result[0], result[1]


    @allure.title("Verify ZDX don't start when device comes up from ideal mode")
    @pytest.mark.xray("QA-292874")
    @add_markers("regression")
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_zdx_unsupported_network_when_device_comes_out_of_idle_mode(self):
        """
        Verify ZDX don't start when device comes up from ideal mode

        Args:
        It performs the following steps:
        1. Verify ZDX don't start when device comes up from ideal mode
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        device_type = self.sys_ops.android_device_type()
        if device_type.lower() == "chromeos":
            pytest.skip(f" Not supported for Chromeos ")

        self.logger_obj.info("Enabling Wi-Fi / Running dual stack network")
        result = self.sys_ops.toggle_wifi(disable=True)
        assert result[0], result[1]
        time.sleep(5)

        self.logger_obj.info("Verifying ZDX should show Unsupported Network ")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="unsupported_network")
        assert result[0], result[1]

        self.logger_obj.info("Sleep 10s :: Before putting device to idle state")
        time.sleep(10)

        self.logger_obj.info("Turning device screen OFF")
        result = self.sys_ops.toggle_screen_on_off(screen='OFF')
        assert result[0], result[1]

        self.logger_obj.info("Sleep 5s :: Before putting device to idle state")
        time.sleep(5)

        self.logger_obj.info("Running ADB command for putting device to idle state")
        cmd = "adb shell dumpsys deviceidle force-idle"
        result = self.sys_ops.run_adb_command(cmd)
        assert result[0], result[1]

        self.logger_obj.info("Sleep 10s :: Collecting data")
        time.sleep(10)

        self.logger_obj.info("Running ADB command for removing device-idle state")
        cmd = "adb shell dumpsys deviceidle unforce"
        result = self.sys_ops.run_adb_command(cmd)
        assert result[0], result[1]

        self.logger_obj.info("Sleep 5s :: Before unlocking the device")
        time.sleep(5)

        self.logger_obj.info("Device Unlocking/Screen ON")
        result = self.sys_ops.toggle_screen_on_off(screen='ON', pin_or_password=self.password)
        assert result[0], result[1]

        self.zcc.bring_zcc_to_focus()

        self.logger_obj.info("Sleep 10s :: ZCC to become stable")
        time.sleep(5)

        self.logger_obj.info("Verifying ZDX should show Unsupported Network ")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="unsupported_network")
        assert result[0], result[1]


    @allure.title("ZDX tray status should show correct status when ZDX is turned OFF in dual stack network,"
                  " user exit and relaunches ZCC in ipv6 only network")
    @pytest.mark.xray("QA-288618")
    @add_markers("regression")
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_zcc_kill_network_switch_zdx_behaviour(self):
        """
        ZDX tray status should show correct status when ZDX is turned OFF in dual stack network,
        user exit and relaunches ZCC in ipv6 only network
        Args:
        It performs the following steps:
        1. It will check ZCC_Android logs and verifies keep alive is not starting zdx again and again in ipv6 only network
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Sleep :: 5s")
        time.sleep(5)
        self.logger_obj.info("Enabling Wi-Fi / Running dual stack network")
        result = self.sys_ops.toggle_wifi(enable=True)
        assert result[0], result[1]
        time.sleep(10)

        self.logger_obj.info("Validating if ZDX service status is ON")
        result = self.zcc.verify_service_status(service_status="on", service_type='ZDX')
        assert result[0], result[1]
        time.sleep(1)

        self.logger_obj.info("Turning OFF ZDX")
        result = self.zcc.toggle_service(service="ZDX", action=False)
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Killing the ZApp")
        result = self.zcc.kill_zcc()
        assert result[0], result[1]
        time.sleep(1)

        self.logger_obj.info("Disabling Wi-Fi / Running dual stack network")
        result = self.sys_ops.toggle_wifi(disable=True)
        assert result[0], result[1]
        time.sleep(3)

        self.logger_obj.info("Restarting the ZApp and verifying ZIA status")
        result = self.zcc.ui.start_app(app=AndroidElements.APP, sleep_time=10)
        time.sleep(15)

        self.logger_obj.info("Verifying ZDX should show Unsupported Network ")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="unsupported_network")
        assert result[0], result[1]


    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Deletion of forward profile and app profile
        2. Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """

        self.logger_obj.info("Deleting Application and monitors")
        self.zdx_admin.delete_application_and_monitors()

        self.sys_ops.toggle_wifi(enable=True)

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.logger_obj.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.logger_obj.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        self.logger_obj.info("Deleting Forwarding profile")
        result = self.app_profile_obj.delete_app_profile(operating_system="Android")
        assert result[0], result[1]

        self.logger_obj.info("Deleting App profile")
        result = self.forwarding_profile_obj.delete_forwarding_profile()
        assert result[0], result[1]
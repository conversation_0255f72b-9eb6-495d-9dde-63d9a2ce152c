# DISABLE_ZDX_IN_IPV6_ONLY - Requirements
## Prerequisites:
    1) On MA compnay and user must be configured.
    2) On Android device zcc version should be installed
    3) There should not any configuration present on ZIA
    4) ZDX service should be enabled

```commandline
  "DEVICE_PIN_OR_PASSWORD": "1234",                      # Device pin or password should be configured in config.json
                                                           If password is not configured in device use None
  "IS_IPV6_ONLY_NETWORK_PRESENT_ON_MOBILE_DATA": 1       # If mobile data is IPv6 only, use 1 else 0 
                                                           0 will Skip all the cases because ipv6only setup is not present

```


## Commands to Execute Automation:
**Running All Cases:** python trigger_automation.py --config <config.json> --testcases test_suite/zcc_zdx/mr_14572_disable_zdx_in_ipv6_only_network

**Running One Test File from mr_16086_ZDX_optimization folder:** python trigger_automation.py --config config.json --testcases test_suite/zcc_zdx/mr_14572_disable_zdx_in_ipv6_only_network/*.py

import pytest, time
import os, sys
import allure, json
import requests
from common_lib.Custom_Markers import add_markers, zcc_mark_version, zdx_mark_version  # Required!
from OS_Android.test_suite.zcc_zdx.mr_15816_webprobes_via_ZEN_3rdPartyProxy_DIRECT import conftest
from OS_Android.library.ui_zcc import Zcc


class TestWebprobePathViaZenProxyDirect:

    @allure.title("setup")
    def setup_class(self):
        """
        1. Login into ZCC
        2. Check ZIA and ZDX on Status
        3. Collecting ZDX data
        4. Fetch and Export logs
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.variables = conftest.variables
        self.logger_obj = conftest.logger_obj
        self.zdx_admin = conftest.zdx_admin
        self.zia_pac = conftest.zia_pac
        self.custom_config_json = conftest.custom_config_json
        self.zcc = Zcc(start_app=True, log_handle=self.logger_obj)
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_webload.db"

        # Proxy ip, squid proxy, running on devraj's VM (<EMAIL>)
        self.external_proxy_ip = conftest.external_proxy_ip
        self.external_proxy_port = conftest.external_proxy_port
        self.test_url = conftest.test_url

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            result = self.zcc.clear_logs()
            assert result[0]
            result = self.zcc.logout()
            assert result[0]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]

        self.zcc.logger.info("Validating if ZIA service status is ON")
        result = self.zcc.verify_service_status(service_type="ZIA", service_status="on")
        assert result[0], result[1]

        self.zcc.logger.info("Validating if ZDX service status is ON")
        result = self.zcc.verify_service_status(service_type="ZDX", service_status="on")
        assert result[0], result[1]

        self.logger_obj.info("Sleep 5 mins :: Collecting ZDX db data data")
        time.sleep(310)

        self.logger_obj.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.logger_obj.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.logger_obj.info("Creating webload db connection")
        self.db_handler.create_db_connection(self.db_path)

    @allure.title("Verify proxy info is included in web load json - Tunnel 1.0 - App Not Bypassed")
    @add_markers("regression")
    @pytest.mark.xray("QA-283290")
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_webprobe_via_zen(self):
        """
            Verify proxy info is included in web load json - Tunnel 1.0 - App Not Bypassed

            It performs the following steps:
            1. Doing validation on webload db for configured URL
               It will check whether given url going via ZEN, and it will also check proxy Host data for same
            2. Access the application specified in the pac file
            3. Search for the logs to verify that domains specified in the Pac file are bypassing

            Args:
                self (object): Instance of the test class.
                Name of the table which needs to validate
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(f"Verifying proxy info is included in web load json - Tunnel 1.0 - App Not Bypassed")

        status = False
        msg = ("Log Validation failed --> Url: https://www.amazon.com Success: 1 Service type: ZIA_1_0"
               " Destination Ip: x.x.x.x Destination Port: 443 SmeIp: x.x.x.x SmePort: 443")
        url = 'https://www.amazon.com'

        self.logger_obj.info(f"Fetching Proxy IP from APP info file")
        proxy_details = self.export_logs.appinfo_file_values("ZApp Proxy")
        proxy_ip = proxy_details.split(':')

        query = f"select ProxyHost from WebData WHERE URL='{url}'"
        output = self.db_handler.execute_query(query)

        self.logger_obj.info(f"Validating proxy ip should match in the db when webprobe go via ZEN")
        assert output[0]['ProxyHost'] == proxy_ip[0]

        webprobe_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                              plugin='web')

        self.logger_obj.info(f"Checking webprobe service discovery for url={url} to validate 'Service type' and 'SmeIp'")
        sme_info = f"SmeIp: {proxy_ip[0]}"
        for item in webprobe_log_data.split("\n"):
            if url in item and "Service type: ZIA_1_0" in item and sme_info in item and "ZPA" not in item:
                status = True
                break

        assert status, msg

    @allure.title("Verify proxy info should not be included in web load json - Tunnel 1.0 - App Bypassed")
    @add_markers("regression")
    @pytest.mark.xray("QA-283291")
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_webprobe_via_direct(self):
        """
            Verify proxy info should not be included in web load json - Tunnel 1.0 - App Bypassed

            It performs the following steps:
            1. Doing validation on webload db for configured URL
               It will check whether given url going via DIRECT, and it will also check empty proxy Host data
            2. Access the application specified in the pac file
            3. Search for the logs to verify that domains specified in the Pac file are bypassing

            Args:
                self (object): Instance of the test class.
                Name of the table which needs to validate
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.logger_obj.info(f"Verifying proxy info is included in web load json - Tunnel 1.0 - App Not Bypassed")

        status = False
        msg = "Log Validation failed --> Url: https://zscaler.zoom.us Success: 1 Service type: DIRECT Destination"
        url = 'https://zscaler.zoom.us'

        query = f"select ProxyHost from WebData WHERE URL='{url}'"
        output = self.db_handler.execute_query(query)

        self.logger_obj.info(f"Validating proxy ip should be empty")
        assert output[0]['ProxyHost'] == ""

        webprobe_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                              plugin='web')

        self.logger_obj.info(f"Checking webprobe service discovery for url={url} to validate 'Service type' and 'SmeIp'")
        for item in webprobe_log_data.split("\n"):
            if url in item and "Service type: DIRECT" in item and "SmeIp:  SmePort" in item:
                status = True
                break

        assert status, msg

    @allure.title("Verify external proxy info should be included in web load json - Tunnel 1.0 - App not Bypassed")
    @add_markers("regression")
    @pytest.mark.xray("QA-283292")
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_webprobe_via_3rd_part_external_proxy(self, proxy_status):
        """
           Verify external proxy info should be included in web load json - Tunnel 1.0 - App not Bypassed

            It performs the following steps:
            1. Doing validation on webload db for configured URL
               It will check whether given url going via Eternal configured Squid Proxy, and it will also check
               proxy Host data for same
            2. Access the application specified in the pac file
            3. Search for the logs to verify that domains specified in the Pac file are bypassing

            Args:
                self (object): Instance of the test class.
                Name of the table which needs to validate
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info(
            f"Verify external proxy info should be included in web load json - Tunnel 1.0 - App not Bypassed")

        self.logger_obj.info("Checking if External Proxy Setup is running or not")
        if not proxy_status:
            pytest.skip("Skipping test as External Proxy is not running, check external proxy setup")

        # set up proxy configuration for the request
        proxies = {
            "http": f"http://{self.external_proxy_ip}:{self.external_proxy_port}",
            "https": f"http://{self.external_proxy_ip}:{self.external_proxy_port}"
        }

        # Make a HTTP request through the proxy

        self.logger_obj.info("Checking if Request through External Proxy Setup is Success or not")
        try:
            response = requests.get(self.test_url, proxies=proxies, timeout=10)
            assert response.status_code == 200, "Failed to get a valid response from the proxy"
        except requests.RequestException as e:
            pytest.fail(f"Check external proxy setup. Request through External Proxy Failed: {e}")

        status = False
        url = 'https://www.flipkart.com'
        msg = (
            "Log Validation failed --> Url: https://www.flipkart.com Success: 1 Service type: ZIA_1_0 Destination Ip:"
            " ************ Destination Port: 443 SmeIp: x.x.x.x")  # x.x.x.x is external proxy ip

        query = f"select ProxyHost from WebData WHERE URL='{url}'"
        output = self.db_handler.execute_query(query)

        self.logger_obj.info(f"Validating proxy ip should match in the db when webprobe go via 3rd Party Proxy")
        assert output[0]['ProxyHost'] == self.external_proxy_ip

        webprobe_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                              plugin='web')

        self.logger_obj.info(f"Checking service discovery for url={url} to validate 'Service type' and 'SmeIp'")
        sme_info = f"SmeIp: {self.external_proxy_ip}"
        for item in webprobe_log_data.split("\n"):
            if url in item and "Service type: ZIA_1_0" in item and sme_info in item and "ZPA" not in item:
                status = True
                break

        assert status, msg

    @allure.title("teardown")
    def teardown_class(self):
        """
        1. Closing Db connection
        2. Logout from zcc
        """
        self.logger_obj.info("Closing db connection after fetching output data")
        self.db_handler.close_db_connection()

        self.logger_obj.info("Logout from ZCC")
        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

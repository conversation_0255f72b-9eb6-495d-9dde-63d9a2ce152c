import pytest, time
import os, sys
import allure, json
import requests
from common_lib.Custom_Markers import add_markers, zcc_mark_version, zdx_mark_version  # Required!
from OS_Android.test_suite.zcc_zdx.mr_9364_zdx_privacy_config_from_MA import conftest
from OS_Android.library.ui_zcc import Zcc

toggle_disabled_inputs = [
    pytest.param('hostname', 'COLLECT MACHINE HOSTNAME', marks=pytest.mark.xray("QA-265203")),
    pytest.param('username', 'COLLECT DEVICE INFO', marks=pytest.mark.xray("QA-265205")),
    pytest.param('mac_addr', 'COLLECT MACHINE HOSTNAME', marks=pytest.mark.xray("QA-265207")),
    pytest.param('ssid', 'COLLECT MACHINE HOSTNAME', marks=pytest.mark.xray("QA-265213")),
    pytest.param('bssid', 'COLLECT MACHINE HOSTNAME', marks=pytest.mark.xray("QA-265213"))
]

toggle_enabled_inputs = [
    pytest.param('hostname', 'COLLECT MACHINE HOSTNAME', marks=pytest.mark.xray("QA-265204")),
    pytest.param('username', 'COLLECT DEVICE INFO', marks=pytest.mark.xray("QA-265206")),
    pytest.param('mac_addr', 'COLLECT MACHINE HOSTNAME', marks=pytest.mark.xray("QA-265208")),
    pytest.param('ssid', 'COLLECT MACHINE HOSTNAME ', marks=pytest.mark.xray("QA-265214")),
    pytest.param('bssid', 'COLLECT MACHINE HOSTNAME', marks=pytest.mark.xray("QA-265214"))
]


class TestZdxPrivacyConfigFromMA:

    logger_obj = conftest.logger_obj
    app_profile_obj = conftest.app_profile_obj
    forwarding_profile_obj = conftest.forwarding_profile_obj
    const_obj = conftest.const_obj
    export_logs = conftest.export_logs
    db_handler = conftest.db_handler
    log_handler = conftest.log_handler
    variables = conftest.variables
    zdx_admin = conftest.zdx_admin
    clientconnectorsupport = conftest.ma_clientconnectorsupport
    db_path = const_obj.LOG_PATH + f"\\upm_upload_stats.db"
    zcc = Zcc(start_app=True, log_handle=logger_obj)

    @allure.title("teardown")
    def make_teardown(self):
        """
        1. Delete App and Forwarding profile
        2. Setting privacy Info
        3  Logout
        """

        self.logger_obj.info("Deleting App profile for mr_9364_zdx_privacy_config_from_MA")
        try:
            result = self.app_profile_obj.delete_app_profile(operating_system="Android")
            assert result[0], result[1]
        except Exception as e:
            self.logger_obj.info(str(e))

        self.logger_obj.info("Deleting Forwarding profile for mr_9364_zdx_privacy_config_from_MA")
        try:
            result = self.forwarding_profile_obj.delete_forwarding_profile()
            assert result[0], result[1]
        except Exception as e:
            self.logger_obj.info(str(e))

        # Enable 'collect device owner' and 'collect machine hostname' information
        try:
            self.clientconnectorsupport.set_privacy_info(collect_device_owner_information=True,
                                                       collect_machine_hostname_information=True)
        except Exception as e:
            self.logger_obj.info(str(e))

        self.logger_obj.info("Logout from ZCC")
        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

    @allure.title("setup")
    @pytest.fixture(autouse=True, scope="class")
    def create_setup(self, request):
        """
        Setup fixture to create necessary profiles, login to ZCC, and export logs.
    
        This fixture performs the following steps:
        1. Deletes existing app and forwarding profiles.
        2. Creates new app and forwarding profiles.
        3. Disables collection of device owner and machine hostname information.
        4. Logs out of ZCC if already logged in and clears logs.
        5. Logs in to ZCC using provided credentials.
        6. Verifies ZIA and ZDX service statuses.
        7. Waits for ZDX data collection.
        8. Exports logs from the Android device.
    
        Args:
            request: Pytest request object.
    
        Raises:
            AssertionError: If any of the setup steps fail.
        """
        try:
            # Delete existing profiles
            self.logger_obj.info("Deleting App profile")
            try:
                result = self.app_profile_obj.delete_app_profile(operating_system="Android")
                assert result[0], result[1]
            except Exception as e:
                self.logger_obj.info(str(e))
    
            self.logger_obj.info("Deleting Forwarding profile")
            try:
                result = self.forwarding_profile_obj.delete_forwarding_profile()
                assert result[0], result[1]
            except Exception as e:
                self.logger_obj.info(str(e))
    
            # Create new profiles
            self.logger_obj.info("Creating Forwarding profile")
            result = self.forwarding_profile_obj.create_forwarding_profile()
            assert result[0], result[1]
    
            self.logger_obj.info("Creating App profile")
            result = self.app_profile_obj.create_app_profile(operating_system="Android")
            assert result[0], result[1]
    
            # Disable collection of device owner and machine hostname information
            try:
                self.clientconnectorsupport.set_privacy_info(collect_device_owner_information=False,
                                                           collect_machine_hostname_information=False)
            except Exception as e:
                self.logger_obj.info(str(e))
    
            # Logout and clear logs if already logged in
            logged_in = self.zcc.validate_zcc_logged_in()
            if logged_in[0]:
                result = self.zcc.clear_logs()
                assert result[0]
                result = self.zcc.logout()
                assert result[0]
    
            # Login to ZCC
            result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                    conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0], result[1]
    
            # Verify service status
            self.zcc.logger.info("Validating if ZIA service status is ON")
            result = self.zcc.verify_service_status(service_type="ZIA", service_status="on")
            assert result[0], result[1]
    
            self.zcc.logger.info("Validating if ZDX service status is ON")
            result = self.zcc.verify_service_status(service_type="ZDX", service_status="on")
            assert result[0], result[1]
    
            # Wait for ZDX data collection
            self.zcc.logger.info("Sleep 15s :: ZDX Data Collection")
            time.sleep(15)
    
            # Export logs
            self.logger_obj.info("Fetch logs from android device")
            result = self.zcc.export_logs()
            assert result[0], result[1]
    
            self.logger_obj.info("Export logs from android device")
            result = self.export_logs.read_export_log_mail()
            assert result[0], result[1]
    
        except AssertionError as e:
            request.addfinalizer(self.make_teardown)
            pytest.skip(f"setup failed reason:{e}")
        else:
            request.addfinalizer(self.make_teardown)
    


    @add_markers("regression")
    @pytest.mark.parametrize("value,toggle", toggle_disabled_inputs)
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_device_profile_info_when_toggle_is_disabled_from_ma(self, value, toggle):
        """
            QA-265203: Verify hostname field will not be visible on upload stats device profile when"
            COLLECT MACHINE HOSTNAME is disabled from MA

            QA-265205: Verify username field will not be included on upload stats device profile when
            COLLECT DEVICE INFO is disabled from MA

            QA-265207: Verify mac_addr field will not be included on upload stats device profile when
            Collect Machine Hostname is disabled from MA

            It performs the following steps:
            1. It will check device profile data in upload stats
            2. It will validate 'hostname' field should not be visible --> QA-265203
            3. It will validate 'username' field should not be visible --> QA-265205
            4. It will validate 'mac_addr' field should not be visible --> QA-265207

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        allure.dynamic.title(f"Verify {value} field will not be visible on upload stats device profile when"
                             f" {toggle} is disabled from MA")

        self.logger_obj.info(f"Verify {value} field will not be visible on upload stats device profile when"
                             f" {toggle} is disabled from MA")

        self.logger_obj.info("Creating upload stats db connection")
        self.db_handler.create_db_connection(self.db_path)

        query = "SELECT * from upload_data"
        db_data = self.db_handler.execute_query(query)
        flag = True

        self.logger_obj.info("Closing db connection after fetching output data")
        self.db_handler.close_db_connection()

        try:

            rows_data = db_data
            upload_json = json.loads(rows_data[0][2])
            device_profile = upload_json['profile']

            self.logger_obj.info("Validating if upload json data is available or not")
            if upload_json == '' or len(str(upload_json)) <= 20:
                flag = False
                msg = "Upload_json data is not available at timestamp at 1st row which is incorrect"

            self.logger_obj.info("Validating if device profile data is available or not in upload_stats json")
            if flag and (device_profile == '' or len(device_profile) == 0):
                flag = False
                msg = "Failed to send device profile data to the tpg, check 1st row data of upload stats json "

            self.logger_obj.info(f"Validating {value} field should not be visible on upload stats device profile")
            if flag and value not in ["mac_addr", "bssid", "ssid"] and device_profile.get(value) is not None:
                flag = False
                msg = f"ERROR:: {value} field is present in device profile when {toggle} is disabled from MA"

            if flag and (value == "bssid" or value == "ssid"):
                if device_profile.get('wifi') is not None:
                    for wifi in device_profile['wifi']:
                        if wifi.get(value) is None or len(wifi[value]) != 0:
                            flag = False
                            msg = f"ERROR:: {value} field is not present or value is not empty in device profile when {toggle} is disabled from MA"
                else:
                    flag = False
                    msg = f"ERROR:: {value} 'wifi' field is not present in device profile when {toggle} is disbaled from MA"

            if flag and value == "mac_addr":
                if device_profile.get('net_interfaces') is not None:
                    for net_interfaces in device_profile['net_interfaces']:
                        if net_interfaces.get('mac_addr') is not None:
                            flag = False
                            msg = f"ERROR:: {value} field is present in device profile when {toggle} is disabled from MA"
                else:
                    flag = False
                    msg = f"ERROR:: {value} 'net_interfaces' field is present in device profile when {toggle} is disabled from MA"

        except Exception as e:
            flag = False
            msg = f"Some error occured while looking for device profile data after login {e}"

        assert flag, msg

    @add_markers("regression")
    @allure.title("Verify COLLECT MACHINE HOSTNAME and COLLECT DEVICE INFO toggle f(OFF to ON) data update on keep Alive after changes")
    @pytest.mark.xray(["QA-265209", "QA-265211"])
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_device_profile_data_update_on_keepalive_when_ma_toggle_from_off_to_on(self):
        """
            Verify COLLECT MACHINE HOSTNAME and COLLECT DEVICE INFO toggle f(OFF to ON) data update on keep Alive after changes

            It performs the following steps:
            1. It will enable toggle from MA
            2. Restart ZDX
            3. It will check device profile data in upload stats

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info(
            f"Verify COLLECT MACHINE HOSTNAME toggle f(OFF to ON) data update on keep Alive after changes")

        # Enable 'collect device owner' and 'collect machine hostname' information
        try:
            self.clientconnectorsupport.set_privacy_info(collect_device_owner_information=True, collect_machine_hostname_information=True)
        except Exception as e:
            self.logger_obj.info(str(e))

        self.logger_obj.info("Update Policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(5)

        self.logger_obj.info("Clear ZDX data")
        result = self.zcc.clear_zdx_data()
        assert result[0], result[1]

        self.zcc.logger.info("Restart ZDX ")
        result = self.zcc.restart_zdx_service()
        assert result[0], result[1]

        self.zcc.logger.info("Sleep 15s :: ZDX Data Collection")
        time.sleep(15)

        self.logger_obj.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.logger_obj.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.logger_obj.info("Creating upload stats db connection")
        self.db_handler.create_db_connection(self.db_path)

        query = "SELECT * from upload_data"
        db_data = self.db_handler.execute_query(query)
        flag = True

        self.logger_obj.info("Closing db connection after fetching output data")
        self.db_handler.close_db_connection()

        try:
            rows_data = db_data
            upload_json = json.loads(rows_data[0][2])
            device_profile = upload_json['profile']

            self.logger_obj.info("Validating if upload json data is available or not")
            if upload_json == '' or len(str(upload_json)) <= 20:
                flag = False
                msg = "Upload_json data is not available at timestamp at 1st row which is incorrect"

            self.logger_obj.info("Validating if device profile data is available or not in upload_stats json")
            if flag and (device_profile == '' or len(device_profile) == 0):
                flag = False
                msg = "Failed to send device profile data to the tpg, check 1st row data of upload stats json "

            self.logger_obj.info(f"Validating 'hostname' field should be visible on upload stats device profile")
            if flag and (device_profile.get('hostname') is None or len(device_profile['hostname']) == 0):
                flag = False
                msg = (f"ERROR:: 'hostname' field is not present in device profile when "
                       f"'COLLECT MACHINE HOSTNAME ' is enabled from MA")

            self.logger_obj.info(f"Validating 'username' field should be visible on upload stats device profile")
            if flag and (device_profile.get('username') is None or len(device_profile['username']) == 0):
                flag = False
                msg = (f"ERROR:: 'username' field is not present in device profile when "
                       f"'COLLECT DEVICE INFO ' is enabled from MA")

            self.logger_obj.info(f"Validating 'mac_addr' field should be visible on upload stats device profile")
            if device_profile.get('net_interfaces') is not None:
                for net_interfaces in device_profile['net_interfaces']:
                    if net_interfaces.get('mac_addr') is None or len(net_interfaces['mac_addr']) != 0:
                        flag = False
                        msg = (f"ERROR:: 'mac_addr' field is not present or value is not empty in "
                               f"device profile when 'COLLECT MACHINE HOSTNAME ' is enabled from MA")
            else:
                flag = False
                msg = f"ERROR:: 'net_interfaces' field is not present in device profile when toggle is enabled from MA"

        except Exception as e:
            flag = False
            msg = f"Some error occured while looking for device profile data after login {e}"

        assert flag, msg

    @add_markers("regression")
    @pytest.mark.parametrize("value,toggle", toggle_enabled_inputs)
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_device_profile_info_when_toggle_is_enabled_from_ma(self, value, toggle):
        """
            QA-265204: Verify hostname will be visible on upload stats device profile when
            COLLECT MACHINE HOSTNAME is enabled from MA

            QA-265206: Verify username will be visible and included on upload stats device
            profile when COLLECT DEVICE INFO is enabled from MA

            QA-265208: Verify mac_addr  will be visible and included on upload stats device
            profile whenCollect Machine Hostname is enabled from MA

            It performs the following steps:
            1. It will check device profile data in upload stats
            2. It will validate 'hostname' field should be visible --> QA-265204
            3. It will validate 'username' field should be visible --> QA-265206
            4. It will validate 'mac_addr' field should be visible --> QA-265208

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        allure.dynamic.title(f"Verify {value} field should be visible on upload stats device profile when"
                             f" {toggle} is enabled from MA")

        self.logger_obj.info(f"Verify {value} field should be visible on upload stats device profile when"
                             f" {toggle} is enabled from MA")

        self.logger_obj.info("Creating upload stats db connection")
        self.db_handler.create_db_connection(self.db_path)

        query = "SELECT * from upload_data"
        db_data = self.db_handler.execute_query(query)
        flag = True

        self.logger_obj.info("Closing db connection after fetching output data")
        self.db_handler.close_db_connection()

        try:
            rows_data = db_data
            upload_json = json.loads(rows_data[0][2])
            device_profile = upload_json['profile']

            self.logger_obj.info("Validating if upload json data is available or not")
            if upload_json == '' or len(str(upload_json)) <= 20:
                flag = False
                msg = "Upload_json data is not available at timestamp at 1st row which is incorrect"

            self.logger_obj.info("Validating if device profile data is available or not in upload_stats json")
            if flag and (device_profile == '' or len(device_profile) == 0):
                flag = False
                msg = "Failed to send device profile data to the tpg, check 1st row data of upload stats json "

            self.logger_obj.info(f"Validating {value} field should be visible on upload stats device profile")
            if flag and value not in ["mac_addr", "bssid", "ssid"] and (
                    device_profile.get(value) is None or len(device_profile[value]) == 0):
                flag = False
                msg = f"ERROR:: {value} field is not present in device profile when {toggle} is enabled from MA"

            if flag and (value == "bssid" or value == "ssid"):
                if device_profile.get('wifi') is not None:
                    for wifi in device_profile['wifi']:
                        if wifi.get(value) is None or len(wifi[value]) != 0:
                            flag = False
                            msg = f"ERROR:: {value} field is not present or value is not empty in device profile when {toggle} is enabled from MA"
                else:
                    flag = False
                    msg = f"ERROR:: {value} 'wifi' field is not present in device profile when {toggle} is enabled from MA"

            if flag and value == "mac_addr":
                if device_profile.get('net_interfaces') is not None:
                    for net_interfaces in device_profile['net_interfaces']:
                        if net_interfaces.get('mac_addr') is None or len(net_interfaces['mac_addr']) != 0:
                            flag = False
                            msg = f"ERROR:: {value} field is not present or value is not empty in device profile when {toggle} is enabled from MA"
                else:
                    flag = False
                    msg = f"ERROR:: {value} 'net_interfaces' field is not present in device profile when {toggle} is enabled from MA"

        except Exception as e:
            flag = False
            msg = f"Some error occured while looking for device profile data after login {e}"

        assert flag, msg

    @add_markers("regression")
    @allure.title(
        "Verify COLLECT MACHINE HOSTNAME and COLLECT DEVICE INFO toggle f(ON to OFF) data update on keep Alive after changes")
    @pytest.mark.xray(["QA-265210", "QA-265212"])
    @zcc_mark_version(3.7)
    @zdx_mark_version(3.2)
    def test_device_profile_data_update_on_keepalive_when_ma_toggle_from_on_to_off(self):
        """
            Verify COLLECT MACHINE HOSTNAME and COLLECT DEVICE INFO toggle f(ON to OFF) data update on keep Alive after changes

            It performs the following steps:
            1. It will disable toggle from MA
            2. Restart ZDX
            3. It will check device profile data in upload stats

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info(
            f"Verify COLLECT MACHINE HOSTNAME and COLLECT DEVICE INFO toggle f(ON to OFF) data update on keep Alive after changes")

        # Disable 'collect device owner' and 'collect machine hostname' information
        try:
            self.clientconnectorsupport.set_privacy_info(collect_device_owner_information=False,
                                                         collect_machine_hostname_information=False)
        except Exception as e:
            self.logger_obj.info(str(e))

        self.logger_obj.info("Update Policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(5)

        self.logger_obj.info("Clear ZDX data")
        result = self.zcc.clear_zdx_data()
        assert result[0], result[1]

        self.zcc.logger.info("Restart ZDX ")
        result = self.zcc.restart_zdx_service()
        assert result[0], result[1]

        self.zcc.logger.info("Sleep 15s :: ZDX Data Collection")
        time.sleep(15)

        self.logger_obj.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.logger_obj.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.logger_obj.info("Creating upload stats db connection")
        self.db_handler.create_db_connection(self.db_path)

        query = "SELECT * from upload_data"
        db_data = self.db_handler.execute_query(query)
        flag = True

        self.logger_obj.info("Closing db connection after fetching output data")
        self.db_handler.close_db_connection()

        try:
            rows_data = db_data
            upload_json = json.loads(rows_data[0][2])
            device_profile = upload_json['profile']

            self.logger_obj.info("Validating if upload json data is available or not")
            if upload_json == '' or len(str(upload_json)) <= 20:
                flag = False
                msg = "Upload_json data is not available at timestamp at 1st row which is incorrect"

            self.logger_obj.info("Validating if device profile data is available or not in upload_stats json")
            if flag and (device_profile == '' or len(device_profile) == 0):
                flag = False
                msg = "Failed to send device profile data to the tpg, check 1st row data of upload stats json "

            self.logger_obj.info(f"Validating 'hostname' field should not be visible on upload stats device profile")
            if flag and device_profile.get('hostname') is not None:
                flag = False
                msg = (f"ERROR:: 'hostname' field is present in device profile when "
                       f"'COLLECT MACHINE HOSTNAME ' is disabled from MA")

            self.logger_obj.info(f"Validating 'username' field should not be visible on upload stats device profile")
            if flag and device_profile.get('username') is not None:
                flag = False
                msg = (f"ERROR:: 'username' field is present in device profile when "
                       f"'COLLECT DEVICE INFO ' is disabled from MA")

            self.logger_obj.info(f"Validating 'mac_addr' field should not be visible on upload stats device profile")
            if device_profile.get('net_interfaces') is not None:
                for net_interfaces in device_profile['net_interfaces']:
                    if net_interfaces.get('mac_addr') is not None:
                        flag = False
                        msg = (f"ERROR:: 'mac_addr' field is present in device profile "
                               f"when 'COLLECT MACHINE HOSTNAME ' is disabled from MA")
            else:
                flag = False
                msg = f"ERROR:: 'net_interfaces' field is not present in device profile when toggle is disabled from MA"

        except Exception as e:
            flag = False
            msg = f"Some error occured while looking for device profile data after login {e}"

        assert flag, msg

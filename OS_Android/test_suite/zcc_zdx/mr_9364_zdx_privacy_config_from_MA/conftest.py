import json,os
import time
import pytest
import allure
import os, shutil
import sys
import platform

from common_lib.mobileadmin.deviceoverview import DeviceOverview
from common_lib.mobileadmin.appprofile import AppProfile
from common_lib.mobileadmin.forwardingprofile import ForwardingProfile
from common_lib.common.log_ops import *
from OS_Android.library.ops_system import SysOps
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
from common_lib.mobileadmin.trustednetwork import TrustedNetwork
from OS_Android.library.database_parse import <PERSON><PERSON><PERSON><PERSON><PERSON>
from OS_Android.library.database_parse import LogHandler
from OS_Android.library.zdx_admin import ZDX_ADMIN
from common_lib.mobileadmin.clientconnectorsupport import ClientConnectorSupport

config_path = os.path.join(os.getcwd(), "config", os.environ.get("CONFIG_FILE"))

with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log", log_level="DEBUG")
const_obj = constants.Utils()
export_logs = FetchAndroidLogs(logger_obj)
log_ops = LogOps(log_handle=logger_obj)
app_profile_obj = AppProfile(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)
forwarding_profile_obj = ForwardingProfile(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)
zdx_admin = ZDX_ADMIN(cloudname=variables["CLOUD_DETAILS"]["ZDX"], configfile=variables,
                      logger=logger_obj)
ma_clientconnectorsupport = ClientConnectorSupport(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)
db_handler = DbHandler()
log_handler = LogHandler()

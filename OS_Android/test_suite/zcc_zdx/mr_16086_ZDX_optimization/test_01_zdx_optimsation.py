import time, os
import pytest, allure
import json, re
import math
from datetime import datetime
from OS_Android.test_suite.zcc_zdx.mr_16086_ZDX_optimization import conftest
from common_lib.Custom_Markers import add_markers
from common_lib.helper_function import help_return

device_stats_inputs = [
    pytest.param('tbl_battery_status', marks=pytest.mark.xray("QA-289614")),
    pytest.param('tbl_cpu_usage', marks=pytest.mark.xray("QA-289615")),
    pytest.param('tbl_disk_io', marks=pytest.mark.xray("QA-289616")),
    pytest.param('tbl_memory_usage', marks=pytest.mark.xray("QA-289617")),
    pytest.param('tbl_network_interface', marks=pytest.mark.xray("QA-289618")),
    pytest.param('tbl_wifi_info', marks=pytest.mark.xray("QA-289619")),
    pytest.param('tbl_mon_processes', marks=pytest.mark.xray(["QA-289620", "QA-305566"]))

]

device_stats_pattern_inputs = [
    pytest.param('ZDX:setDiskDetails', marks=pytest.mark.xray(["QA-289610"])),
    pytest.param('ZDX:setMemoryDetails', marks=pytest.mark.xray(["QA-289610"])),
    pytest.param('ZDX:setNetworkDetails', marks=pytest.mark.xray(["QA-289610"])),
    pytest.param('ZDX:setWifiDetails', marks=pytest.mark.xray(["QA-289610"]))
]


class TestZdxOptimization:
    @allure.title("Setup Method")
    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.variables = conftest.variables
        self.logger_obj = conftest.logger_obj
        self.password = conftest.password
        self.db_path = self.const_obj.LOG_PATH + f"\\upm_device_stats.db"
        self.device_type = self.sys_ops.android_device_type()

        self.logger_obj.info(" Creating db Connection")
        self.db_handler.create_db_connection(self.db_path)

    @allure.title("Verify we should not trigger ZDX off event when device screen is on")
    @pytest.mark.xray("QA-289603")
    @add_markers("regression")
    def test_no_trigger_of_zdx_off_event(self):
        """
             Verify we should not trigger ZDX off event when device screen is on

             Args:

             It performs the following steps:
             1. It will check the android logs that we are not triggering ZDX off event when device screen is ON
                Args:
                    self (object): Instance of the test class.
                Returns:
                    None
                Raises:
                    AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Verify we should not trigger ZDX off event when device screen is on")

        status = True
        msg = ""
        pattern = "Device idle mode changed stopping ZDX"

        self.logger_obj.info("Fetching Android log file data ZDX stop event pattern")
        android_log_file_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                  plugin='tray')

        if android_log_file_data is None:
            status = False
            msg = "Not able to fetch UPM log file data"
            assert status, msg

        for lines in android_log_file_data.split("\n"):
            if pattern in lines:
                status = False
                msg = f"Failure :: Pattern = {pattern} found in the logs, but we were expecting failure"

        assert status, msg

    @allure.title("Verify data insertion in the table of device stats, should be 1min")
    @pytest.mark.parametrize("table", device_stats_inputs)
    @add_markers("regression")
    def test_data_collection_for_upm_device_stats_db(self, table):
        """
            This function Verify data is written to upm_device_stats.db in every 1min for regular session
            It performs the following steps:
            1. It will check time difference between two rows should be 1min
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        status = True
        msg = ""

        self.logger_obj.info(f"Validating data insertion frequency 1min for table = {table}")
        query = f"select distinct timestamp from {table} order by timestamp limit 10"
        rows_data = self.db_handler.execute_query(query)

        if len(rows_data) == 0:
            status = False
            msg = f"Failed to get data for table '{table}' "
        assert status, msg

        for ind in range(2, len(rows_data) - 1):
            # here curr_timestamp < prev_timestamp
            # timestamp is in unix ms
            curr_timestamp = rows_data[ind][0]
            prev_timestamp = rows_data[ind + 1][0]

            difference = int(round(abs(curr_timestamp - prev_timestamp) / 1000, 0))

            if not (55 <= difference <= 65):
                status = False
                msg = (f"Failed to collect data for {table} in 1min at currentime "
                       f":{datetime.fromtimestamp(int(curr_timestamp / 1000))} and prevtime "
                       f":{datetime.fromtimestamp(int(prev_timestamp / 1000))} diff "
                       f":{curr_timestamp - prev_timestamp} {int(round((curr_timestamp - prev_timestamp) / 1000, 0))} ")
            else:
                self.logger_obj.info(f"Validating time difference between current_row = {curr_timestamp} and "
                                     f"prev_row ={prev_timestamp}, Difference is {difference}s which is correct")

        assert status, msg
        self.logger_obj.info(f"Validation completed for table = {table}")

    @allure.title("Verify raw data collection and insertion into tbl_network_interface")
    @pytest.mark.xray("QA-289618")
    @add_markers("regression")
    def test_verify_monitor_data_for_network_interface(self):
        """
            This function is validating data for 'tbl_network_interface'

            It performs the following steps:
            1. It will check if data is present or not and also validates data from the logs
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Verify raw data collection and insertion into tbl_network_interface")
        status = True
        msg = ""

        device_stats_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                  plugin='deviceStats')
        if device_stats_log_data is None:
            status = False
            msg = "Not able to fetch data from devicestats log file"

        assert status, msg

        query = "select * from tbl_network_interface order by timestamp desc limit 10"

        try:
            row_data = self.db_handler.execute_query(query)
            for row in row_data:
                # for DT [timestamp,refid,ifname,guid,bandwidth_mbps,revc_bps,send_bps,isactive]
                timestamp_from_db = row[0]
                guid_from_db = row[3]
                bandwidth_from_db = row[4]
                recv_from_db = row[5]
                send_from_db = row[6]
                is_active_from_db = row[7]
                pattern = re.compile(
                    fr'(?im)LT sessionId:-1.*?timestamp:\s+{timestamp_from_db}.*?GUID:\[{guid_from_db}].*?isConnected:([0-9]).*?Bandwidth:\[(.*?)\s+.*?\].*?Receive:\[(.*?)\s+.*?\].*?Send:\[(.*?)\s+.*?\]',
                    re.DOTALL)
                res = pattern.findall(device_stats_log_data)
                if len(res) > 0:
                    if len(res[0]) != 4:
                        status = False
                        msg = (f"Unable to fetch [Network Interface] field for interface with GUID:{guid_from_db} "
                               f"at timestamp :{timestamp_from_db}")
                    else:
                        self.logger_obj.info(
                            f"Validating 'is_connected', 'bandwidth', 'recv_bps' and 'send_bps' values from "
                            f"device_stats logs and table tbl_network_interface db for timestamp = {timestamp_from_db}")

                        is_connected_from_log, bandwidth_from_log, recv_from_log, send_from_log = res[0]

                        if int(is_connected_from_log) != int(is_active_from_db):
                            status = False
                            msg = (
                                f"[{is_active_from_db}:{is_connected_from_log}]IsActive(isConnected) from DB and log not "
                                f"matching for Interface with GUID:{guid_from_db} at timestamp:{timestamp_from_db}")

                        if float(bandwidth_from_db) != float(bandwidth_from_log):
                            status = False
                            msg = (f"[{bandwidth_from_db}:{bandwidth_from_log}]Bandwidth from DB and log not "
                                   f"matching for Interface with GUID:{guid_from_db} at timestamp:{timestamp_from_db}")

                        if float(send_from_db) != float(send_from_log):
                            status = False
                            msg = (f"[{send_from_db}:{send_from_log}]Send Field from DB and log not matching for "
                                   f"Interface with GUID:{guid_from_db} at timestamp:{timestamp_from_db}")

                        if float(recv_from_db) != float(recv_from_log):
                            status = False
                            msg = (f"[{recv_from_db}:{recv_from_log}]Recv Field from DB and log not matching for "
                                   f"Interface with GUID:{guid_from_db} at timestamp:{timestamp_from_db}")

        except Exception as e:
            status = False
            msg = f"Unexpected Error occurred :{e}"

        assert status, msg

    @allure.title("Verify raw data collection and insertion into tbl_wifi_info")
    @pytest.mark.xray("QA-289619")
    @add_markers("regression")
    def test_verify_monitor_data_for_wifi_info(self):
        """
            This function is validating data for 'tbl_wifi_info'

            It performs the following steps:
            1. It will check if data is present or not and also validates data from the logs
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Verify & Comparing data for table wifi info for regular session")
        status = True
        msg = ""

        device_stats_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                  plugin='deviceStats')
        if device_stats_log_data is None:
            status = False
            msg = "Not able to fetch data from 'devicestats' log file"

        assert status, msg

        query = 'select * from  tbl_wifi_info order by timestamp desc limit 10'

        try:
            row_data = self.db_handler.execute_query(query)
            for row in row_data:
                # for DT [timestamp,refid,ifname,ssid,signal_quality,rssi_dbm]
                db_timestamp = row[0]
                db_if_name = row[2]
                db_signal_quality = row[4]
                db_rssi_dbm = row[5]

                pattern = re.compile(
                    fr'(?ims)LT sessionId:-1.*?timestamp:\s+{db_timestamp}.*?{re.escape(db_if_name)}.*?SSID:\[(.*?)\].*?Signal Quality:\[(.*?)%\].*?RSSI:\[(.*?)\s+.*?\]')

                stats_from_log = pattern.findall(device_stats_log_data)
                if len(stats_from_log) > 0:
                    if len(stats_from_log[0]) != 3:
                        status = False
                        msg = "Not able to fetch monitor data for [Wifi] from devicestats log file"
                    else:
                        self.logger_obj.info(
                            f"Validating 'signal_quality' and 'rssi_dbm' values from device_stats logs "
                            f"and table tbl_wifi_info db for timestamp = {db_timestamp}")

                        log_ssid, log_signal_quality, log_rssi_dbm = stats_from_log[0]

                        if int(log_signal_quality) != int(db_signal_quality):
                            status = False
                            msg = (f"[DB:LOG]:[{log_signal_quality}:{db_signal_quality}] Signal Quality not matching "
                                   f"for INTERFACE: {db_if_name} in timestamp: {db_timestamp}")

                        if int(log_rssi_dbm) != int(db_rssi_dbm):
                            status = False
                            msg = (f"[DB:LOG]:[{log_rssi_dbm}:{db_rssi_dbm}] RSSI not matching "
                                   f"for INTERFACE: {db_if_name} in timestamp: {db_timestamp}")

        except Exception as e:
            status = False
            msg = f"Unexpected Error occurred :{e}"

        assert status, msg

    @allure.title("Verify raw data collection and insertion into tbl_disk_io of device stats")
    @pytest.mark.xray("QA-289616")
    @add_markers("regression")
    def test_verify_monitor_data_for_disk_io(self):
        """
            This function is validating data for 'tbl_disk_io'

            It performs the following steps:
            1. It will check if data is present or not and also validates data from the logs
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Verify & Comparing data for table disk_io for regular session")
        status = True
        msg = ""

        device_stats_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                  plugin='deviceStats')
        if device_stats_log_data is None:
            status = False
            msg = "Not able to fetch data from devicestats log file"

        assert status, msg

        query = "select * from tbl_disk_io order by timestamp desc limit 10"

        try:
            row_data = self.db_handler.execute_query(query)
            for row in row_data:
                # for DT [timestamp,refid,disk_name,rd_bps,wt_bps,avd_diskq_len_scale_1000, free_mb, pct_used]
                timestamp_from_db = row[0]
                disk_name = row[2]
                rd_bps_from_db = row[3]
                wt_bps_from_db = row[4]
                free_mb_from_db = round(float(row[6]), 2)
                pct_used_from_db = round(float(row[7]), 2)

                pattern = re.compile(
                    fr'(?im)LT sessionId:-1.*?timestamp:\s+{timestamp_from_db}.*?{re.escape(disk_name)}.*?Free:\[(.*?)\s+.*?\].*?used:\[(\d+\.\d+)%\].*?Disk Read Bytes/sec:\[(\d+\.\d+)\].*?Disk Write Bytes/sec:\[(\d+\.\d+)\]',
                    re.DOTALL)

                res = pattern.findall(device_stats_log_data)
                if len(res) > 0:
                    if len(res[0]) != 4:
                        status = False
                        msg = f"Unable to fetch field for [Disk I/O] at timestamp :{timestamp_from_db}"
                    else:
                        self.logger_obj.info(
                            f"Validating 'free_mb', 'pct_used', 'rd_bps' and 'wt_bps' values from device_stats logs "
                            f"and table tbl_disk_io db for timestamp = {timestamp_from_db}")

                        free_mb_from_log, pct_used, rd_bps_from_log, wt_bps_from_log = res[0]
                        pct_used_from_log = float(pct_used)

                        if float(free_mb_from_db) != float(free_mb_from_log):
                            status = False
                            msg = (f"[{free_mb_from_db}:{free_mb_from_log}]free_mb from DB and log are not "
                                   f"matching for Disk Name:{disk_name} at timestamp:{timestamp_from_db}")

                        if float(pct_used_from_db) != float(pct_used_from_log):
                            status = False
                            msg = (f"[{pct_used_from_db}:{pct_used_from_log}]pct_used from DB and log are not "
                                   f"matching for Disk Name:{disk_name} at timestamp:{timestamp_from_db}")

                        if float(wt_bps_from_db) != float(wt_bps_from_log):
                            status = False
                            msg = (f"[{wt_bps_from_db}:{wt_bps_from_log}]wt_bps from DB and log are not "
                                   f"matching for Disk Name:{disk_name} at timestamp:{timestamp_from_db}")

                        if float(rd_bps_from_db) != float(rd_bps_from_log):
                            status = False
                            msg = (f"[{rd_bps_from_db}:{rd_bps_from_log}]rd_bps from DB and log are not "
                                   f"matching for Disk Name:{disk_name} at timestamp:{timestamp_from_db}")

        except Exception as e:
            status = False
            msg = f"Unexpected Error occurred :{e}"

        assert status, msg

    @allure.title("Verify raw data collection and insertion into tbl_memory_usage")
    @pytest.mark.xray("QA-289617")
    @add_markers("regression")
    def test_verify_monitor_data_for_tbl_memory_usage(self):
        """
            This function is validating data for 'tbl_memory_usage'

            It performs the following steps:
            1. It will check if data is present or not and also validates data from the logs
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Verify & Comparing data for table tbl_memory_usage for regular session")
        status = True
        msg = ""

        device_stats_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                  plugin='deviceStats')
        if device_stats_log_data is None:
            status = False
            msg = "Not able to fetch data from devicestats log file"

        assert status, msg

        query = "select * from tbl_memory_usage order by timestamp desc limit 10"

        try:
            row_data = self.db_handler.execute_query(query)
            for row in row_data:
                # for DT [timestamp,refid,total,free,used,pct_used, swap_used]
                timestamp_from_db = row[0]
                total_from_db = row[2]
                free_from_db = row[3]
                used_from_db = row[4]
                pct_used_from_db = row[5]
                swap_used_from_db = row[6]

                pattern = re.compile(
                    fr'(?im)LT sessionId:-1.*?timestamp:\s+{timestamp_from_db}.*?Physical memory: total:\[(.*?)\s+.*?\].*?free:\[(.*?)\s+.*?\].*?used:\[(.*?)\s+.*?\].*?Swap: total:\[(.*?)\s+.*?\].*?used:\[(.*?)\s+.*?\]',
                    re.DOTALL)
                res = pattern.findall(device_stats_log_data)
                if len(res) > 0:
                    if len(res[0]) != 5:
                        status = False
                        msg = f"Unable to fetch field for [Memory] at timestamp :{timestamp_from_db}"
                    else:
                        self.logger_obj.info(
                            f"Validating 'total', 'free', 'used', 'pct_used' and 'swap_used' values from device_stats "
                            f"logs and table tbl_memory_usage db for timestamp = {timestamp_from_db}")

                        total_from_log, free_from_log, used_from_log, swap_total_from_log, swap_used_from_log = res[0]
                        pct_used_from_log = 0
                        if int(total_from_log) != 0:
                            pct_used_from_log = math.ceil((int(used_from_log) / int(total_from_log)) * 100)

                        if int(total_from_db) != int(total_from_log):
                            status = False
                            msg = (f"[{total_from_db}:{total_from_log}]total from DB and log are not "
                                   f"matching for [Memory]: at timestamp:{timestamp_from_db}")

                        if int(free_from_db) != int(free_from_log):
                            status = False
                            msg = (f"[{free_from_db}:{free_from_log}]free from DB and log are not "
                                   f"matching for [Memory]: at timestamp:{timestamp_from_db}")

                        if int(used_from_db) != int(used_from_log):
                            status = False
                            msg = (f"[{used_from_db}:{used_from_log}]used from DB and log are not "
                                   f"matching for [Memory]: at timestamp:{timestamp_from_db}")

                        if int(pct_used_from_db) != int(pct_used_from_log):
                            status = False
                            msg = (f"[{pct_used_from_db}:{pct_used_from_log}]pct_used from DB and log are not "
                                   f"matching for[Memory]: at timestamp:{timestamp_from_db}")

                        if int(swap_used_from_db) != int(swap_used_from_log):
                            status = False
                            msg = (f"[{swap_used_from_db}:{swap_used_from_log}]swap_used from DB and log are not "
                                   f"matching for [Memory] Swap: at timestamp:{timestamp_from_db}")

        except Exception as e:
            status = False
            msg = f"Unexpected Error occurred :{e}"

        assert status, msg

    @allure.title("Verify raw data collection and insertion into tbl_battery_status")
    @pytest.mark.xray("QA-289614")
    @add_markers("regression")
    def test_verify_monitor_data_for_tbl_battery_status(self):
        """
            This function is validating data for 'tbl_battery_status'

            It performs the following steps:
            1. It will check if data is present or not and also validates data from the logs
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Verify & Comparing data for table tbl_battery_status for regular session")
        status = True
        msg = ""

        device_stats_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                  plugin='deviceStats')
        if device_stats_log_data is None:
            status = False
            msg = "Not able to fetch data from devicestats log file"

        assert status, msg

        query = "SELECT level_pct from tbl_battery_status order by timestamp desc"
        rows_data = self.db_handler.execute_query(query)
        status = True
        msg = ""

        last_value_pct_inserted_in_db = -1
        result = self.sys_ops.get_battery_percentage()
        assert result[0], result[1]

        device_battery = result[2]

        for data in rows_data:
            if len(str(data['level_pct'])) >= 1:
                last_value_pct_inserted_in_db = int(data['level_pct'])
                break

        self.logger_obj.info(f"Current battery pct from the device using adb command = {device_battery}")
        self.logger_obj.info(f"Latest battery pct in upm_device_stats.db = {last_value_pct_inserted_in_db}")

        self.logger_obj.info("Checking if latest battery pct from the device and the db is same")
        if not (device_battery - 5 <= last_value_pct_inserted_in_db <= device_battery + 5):
            status = False
            msg = "Incorrect value of the 'level_pct' in the device stats db file"
        assert status, msg

        self.logger_obj.info("Checking if battery pct from the devicestats logs and the db are same")
        try:
            query = "select * from tbl_battery_status order by timestamp desc limit 10"
            row_data = self.db_handler.execute_query(query)
            for row in row_data:
                # for DT [timestamp,refid,level_pct]
                timestamp_from_db = row[0]
                level_pct_from_db = row[2]

                if level_pct_from_db <= 0 or level_pct_from_db > 100:
                    status = False
                    msg = "Battery is out of range [0,100]"
                pattern = re.compile(
                    fr'(?im)LT sessionId:-1.*?timestamp:\s+{timestamp_from_db}.*?Battery level:\[(\d+\.\d+)%\]',
                    re.DOTALL)
                res = pattern.findall(device_stats_log_data)
                if len(res) > 0:
                    if len(res[0]) == 0:
                        status = False
                        msg = f"Unable to fetch field for [Battery] at timestamp :{timestamp_from_db}"
                    else:
                        self.logger_obj.info(
                            f"Validating 'level_pct' from device_stats logs "
                            f"and table tbl_battery_status db for timestamp = {timestamp_from_db}")

                        level_pct_from_log = res[0]

                        if float(level_pct_from_db) != float(level_pct_from_log):
                            status = False
                            msg = (f"[{level_pct_from_db}:{level_pct_from_log}]level_pct from DB and log are not "
                                   f"matching for [Battery]: IdleTime at timestamp:{timestamp_from_db}")

        except Exception as e:
            status = False
            msg = f"Unexpected Error occurred :{e}"

        assert status, msg

    @allure.title("Verify raw data collection and insertion into tbl_cpu_usage")
    @pytest.mark.xray("QA-289615")
    @add_markers("regression")
    def test_verify_monitor_data_for_tbl_cpu_usage(self):
        """
            This function is validating data for 'tbl_cpu_usage'

            It performs the following steps:
            1. It will check if data is present or not and also validates data from the logs
            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Verify & Comparing data for table tbl_cpu_usage for regular session")
        status = True
        msg = ""

        device_stats_log_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                                  plugin='deviceStats')
        if device_stats_log_data is None:
            status = False
            msg = "Not able to fetch data from devicestats log file"

        assert status, msg

        query = "select * from tbl_cpu_usage order by timestamp desc limit 10"

        try:
            row_data = self.db_handler.execute_query(query)
            for row in row_data:
                # for DT [timestamp,refid,pct_total,pct_kernel,pct_user,pct_idle]
                timestamp_from_db = row[0]
                pct_total_from_db = row[2]
                pct_idle_from_db = row[5]

                pattern = re.compile(
                    fr'(?im)LT sessionId:-1.*?timestamp:\s+{timestamp_from_db}.*?ProcessorTime:\[(\d+\.\d+)%\].*?IdleTime\[(\d+\.\d+)%\]',
                    re.DOTALL)

                res = pattern.findall(device_stats_log_data)
                if len(res) > 0:
                    if len(res[0]) != 2:
                        status = False
                        msg = f"Unable to fetch field for [CPU] at timestamp :{timestamp_from_db}"
                    else:
                        self.logger_obj.info(
                            f"Validating 'pct_total' and 'pct_idle' values from device_stats logs "
                            f"and table tbl_cpu_usage db for timestamp = {timestamp_from_db}")
                        pct_total, pct_idle = res[0]
                        pct_total_from_log = float(pct_total)
                        pct_idle_from_log = float(pct_idle)

                        if float(pct_idle_from_db) != float(pct_idle_from_log):
                            status = False
                            msg = (f"[{pct_idle_from_db}:{pct_idle_from_log}]pct_idle from DB and log are not "
                                   f"matching for [CPU]: IdleTime at timestamp:{timestamp_from_db}")

                        if float(pct_total_from_db) != float(pct_total_from_log):
                            status = False
                            msg = (f"[{pct_total_from_db}:{pct_total_from_log}]pct_total from DB and log are not "
                                   f"matching for [CPU]: ProcessorTime at timestamp:{timestamp_from_db}")

        except Exception as e:
            status = False
            msg = f"Unexpected Error occurred :{e}"

        assert status, msg

    @allure.title("Verify new data fetch should be every 1min for device stats db")
    @pytest.mark.parametrize("pattern", device_stats_pattern_inputs)
    @add_markers("regression")
    def test_verify_data_fetch_from_android_logs_should_be_1_min(self, pattern):
        """
            This function is validating frequency of ZDX:setWifiDetails, ZDX:setNetworkDetails, ZDX:setMemoryDetails
            INF ZDX:setDiskDetails is 1min

            Args:
                pattern: Pattern to search for in Android logs to validate the data fetch is 1min.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Verify new data fetch should be every 1min for device stats db")
        status = True
        msg = ""

        android_logs_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                              plugin='tunnel')
        if android_logs_data is None:
            status = False
            msg = "Not able to fetch data from 'ZCC_Android' log file"
        assert status, msg

        try:
            result = self.log_handler.find_log_line_timestamp_in_unix(data=android_logs_data, pattern=pattern)

            for idx in range(len(result) - 1):
                prev_timestamp = result[idx]
                curr_timestamp = result[idx + 1]

                self.logger_obj.info(
                    f"Validating time differnce between prev_timestamp = {prev_timestamp} and current_timestamp = {curr_timestamp}")

                # Ideal case is to check 1min, but while running DT session setWifiDetails these value fetch will be
                # different for dt and regular session and there is no way to check which one belong to which session
                if (curr_timestamp - prev_timestamp) > 63:
                    status = False
                    msg = "Data fetch from Android logs is more than 1min which is incorrect"
                    break

        except Exception as e:
            status = False
            msg = f"Unexpected Error occurred :{e}"

        assert status, msg

    @allure.title("Verify data from device info should correctly inserted to the tbl_disk_io table device_stats db")
    @pytest.mark.xray(["QA-289612", "QA-289613"])
    @add_markers("regression")
    def test_validate_tbl_disk_io_data_from_android_logs_to_db_data(self):
        """
            This function is validating data of ZDX:setDiskDetails to the device stats db data

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info(
            "Verify data from device info should correctly inserted to the tbl_disk_io table device_stats db")
        status = True
        msg = ""

        self.logger_obj.info("Fetching ZCC_Android logs data for validation")
        android_logs_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                              plugin='tunnel')
        if android_logs_data is None:
            status = False
            msg = "Not able to fetch data from 'ZCC_Android' log file"
        assert status, msg

        sp = "ZDX:setDiskDetails"
        ep = "ZDX:setWifiDetails"

        self.logger_obj.info("Fetching setDiskDetails from ZCC_Android logs")
        set_disk_details = self.log_handler.fetch_device_stats_info(data=android_logs_data, start_pattern=sp,
                                                                    end_pattern=ep)
        assert set_disk_details[0], set_disk_details[1]
        length = len(set_disk_details[2])
        set_disk_details = set_disk_details[2]

        self.logger_obj.info(f"--------------setDiskDetails data from logs--------------")
        for index in range(len(set_disk_details)):
            disk_name = set_disk_details[index]['name']
            pct_used = round(set_disk_details[index]['usedPercent'], 2)
            free_mb = round(set_disk_details[index]['freeMB'], 2)
            self.logger_obj.info(f"disk_name = {disk_name} , pct_used = {pct_used} , free_mb = {free_mb} ")

        self.logger_obj.info(f"---------------------------------------------------------")

        query = "select * from tbl_disk_io"
        row_data = self.db_handler.execute_query(query)
        length = min(length, len(row_data))
        if self.device_type.lower() == "chromeos":
            set_disk_details = set_disk_details[1:]

        for index in range(length):
            # for [timestamp,refid,disk_name,rd_bps,wt_bps,avd_diskq_len_scale_1000, free_mb, pct_used]
            timestamp = row_data[index][0]
            disk_name = row_data[index][2]
            free_mb_from_db = round(float(row_data[index][6]), 2)
            pct_used_from_db = round(float(row_data[index][7]), 2)

            disk_name_from_log = set_disk_details[index]['name']
            pct_used_from_log = round(set_disk_details[index]['usedPercent'], 2)
            free_mb_from_log = round(set_disk_details[index]['freeMB'], 2)

            self.logger_obj.info(
                f"Validating 'free_mb', 'pct_used', 'disk_name' values from ZCC_Android "
                f"logs and table tbl_disk_io of device_stats db for timestamp = {timestamp}")

            if float(free_mb_from_db) != float(free_mb_from_log):
                status = False
                msg = (f"[{free_mb_from_db}:{free_mb_from_log}]free_mb from DB and log are not "
                       f"matching for Disk Name:{disk_name} at timestamp:{timestamp}")
                assert False, msg

            if float(pct_used_from_db) != float(pct_used_from_log):
                status = False
                msg = (f"[{pct_used_from_db}:{pct_used_from_log}]pct_used from DB and log are not "
                       f"matching for Disk Name:{disk_name} at timestamp:{timestamp}")
                assert False, msg

            if disk_name != disk_name_from_log:
                status = False
                msg = (f"[{disk_name}:{disk_name_from_log}] diskName from DB and log are not "
                       f"matching  at timestamp:{timestamp}")
                assert False, msg

        assert status, msg

    @allure.title("Verify data from device info should correctly inserted to the tbl_mem_usage table device_stats db")
    @pytest.mark.xray(["QA-289612", "QA-289613"])
    @add_markers("regression")
    def test_validate_tbl_mem_usage_data_from_android_logs_to_db_data(self):
        """
            This function is validating data of ZDX:setMemoryDetails to the device stats db data

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info(
            "Verify data from device info should correctly inserted to the tbl_mem_usage table device_stats db")
        status = True
        msg = ""

        self.logger_obj.info("Fetching ZCC_Android logs data for validation")
        android_logs_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                              plugin='tunnel')
        if android_logs_data is None:
            status = False
            msg = "Not able to fetch data from 'ZCC_Android' log file"
        assert status, msg

        sp = "ZDX:setMemoryDetails"
        ep = "ZDX:setProcessDetails"

        self.logger_obj.info("Fetching setMemoryDetails from ZCC_Android logs")
        set_memory_details = self.log_handler.fetch_device_stats_info(data=android_logs_data, start_pattern=sp,
                                                                      end_pattern=ep)
        assert set_memory_details[0], set_memory_details[1]
        length = len(set_memory_details[2])
        set_memory_details = set_memory_details[2]

        self.logger_obj.info(f"--------------setMemoryDetails data from logs--------------")
        for index in range(len(set_memory_details)):
            total = set_memory_details[index]['totalMem']
            free = set_memory_details[index]['availMem']
            used = set_memory_details[index]['usedMem']
            pct_used = set_memory_details[index]['memLoad']
            self.logger_obj.info(f"total = {total} , free = {free} , used = {used} , pct_used = {pct_used}")
        self.logger_obj.info(f"---------------------------------------------------------")

        query = "select * from tbl_memory_usage"
        row_data = self.db_handler.execute_query(query)
        if self.device_type.lower() == "chromeos":
            set_memory_details = set_memory_details[1:]
        length = min(length, len(row_data))

        for index in range(length):
            # for [timestamp,refid,total,free,used,pct_used, swap_used]
            timestamp = row_data[index][0]
            total_from_db = row_data[index][2]
            free_from_db = row_data[index][3]
            used_from_db = row_data[index][4]
            pct_used_from_db = row_data[index][5]

            total_from_log = set_memory_details[index]['totalMem']
            free_from_log = set_memory_details[index]['availMem']
            used_from_log = set_memory_details[index]['usedMem']
            pct_used_from_log = set_memory_details[index]['memLoad']

            self.logger_obj.info(
                f"Validating 'total', 'free', 'used', 'pct_used' values from ZCC_Android "
                f"logs and table tbl_memory_usage of device_stats db for timestamp = {timestamp}")

            if int(total_from_db) != int(total_from_log):
                status = False
                msg = (f"[{total_from_db}:{total_from_log}]total from DB and log are not "
                       f"matching for [Memory]: at timestamp:{timestamp}")
                break

            if int(free_from_db) != int(free_from_log):
                status = False
                msg = (f"[{free_from_db}:{free_from_log}]free from DB and log are not "
                       f"matching for [Memory]: at timestamp:{timestamp}")
                break

            if int(used_from_db) != int(used_from_log):
                status = False
                msg = (f"[{used_from_db}:{used_from_log}]used from DB and log are not "
                       f"matching for [Memory]: at timestamp:{timestamp}")
                break

            if int(pct_used_from_db) != int(pct_used_from_log):
                status = False
                msg = (f"[{pct_used_from_db}:{pct_used_from_log}]pct_used from DB and log are not "
                       f"matching for[Memory]: at timestamp:{timestamp}")
                break

        assert status, msg

    @allure.title(
        "Verify data from device info should correctly inserted to the tbl_network_interface table device_stats db")
    @pytest.mark.xray(["QA-289612", "QA-289613", "QA-149240"])
    @add_markers("regression")
    def test_validate_tbl_network_interface_from_android_logs_to_db_data(self):
        """
            This function is validating data of ZDX:setNetworkDetails  to the device stats db data

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Verify data from device info should correctly inserted to the"
                             " tbl_network_interface table device_stats db")
        status = True
        msg = ""

        self.logger_obj.info("Fetching ZCC_Android logs data for validation")
        android_logs_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                              plugin='tunnel')
        if android_logs_data is None:
            status = False
            msg = "Not able to fetch data from 'ZCC_Android' log file"
        assert status, msg

        sp = "ZDX:setWifiDetails"
        ep = "ZDX:setMemoryDetails"

        self.logger_obj.info("Fetching setNetworkDetails from ZCC_Android logs")
        set_network_details = self.log_handler.fetch_device_stats_info(data=android_logs_data, start_pattern=sp,
                                                                       end_pattern=ep)
        assert set_network_details[0], set_network_details[1]
        length = len(set_network_details[2])
        set_network_details = set_network_details[2]

        self.logger_obj.info(f"--------------setProcessDetails data from logs--------------")
        for index in range(len(set_network_details)):
            ifname = set_network_details[index]['ifDescription']
            bandwidth = round(float(set_network_details[index]['bandwidthMbps']), 2)
            recv_bps = round(float(set_network_details[index]['downSpeed']), 2)
            send_bps = round(float(set_network_details[index]['upSpeed']), 2)
            is_active = round(float(set_network_details[index]['isActive']), 2)
            self.logger_obj.info(
                f"ifname = {ifname} , bandwidth = {bandwidth} , recv_bps = {recv_bps} , send_bps = {send_bps} , is_active = {is_active}")
        self.logger_obj.info(f"---------------------------------------------------------")

        query = "select * from tbl_network_interface"
        row_data = self.db_handler.execute_query(query)
        if self.device_type.lower() == "chromeos":
            set_network_details = set_network_details[1:]
        length = min(length, len(row_data))

        for index in range(length):
            # for [timestamp,refid,ifname,guid,bandwidth_mbps,revc_bps,send_bps,isactive]
            timestamp = row_data[index][0]
            ifname_from_db = row_data[index][2]
            guid_from_db = row_data[index][3]
            bandwidth_from_db = round(float(row_data[index][4]), 2)
            recv_bps_from_db = round(float(row_data[index][5]), 2)
            send_bps_from_db = round(float(row_data[index][6]), 2)
            is_active_from_db = round(float(row_data[index][7]), 2)

            ifname_from_log = set_network_details[index]['ifDescription']
            bandwidth_from_log = round(float(set_network_details[index]['bandwidthMbps']), 2)
            recv_bps_from_log = round(float(set_network_details[index]['downSpeed']), 2)
            send_bps_from_log = round(float(set_network_details[index]['upSpeed']), 2)
            is_active_from_logs = round(float(set_network_details[index]['isActive']), 2)

            self.logger_obj.info(
                f"Validating 'ifname', 'bandwidth', 'recv_bps', 'send_bps', 'is_active' values from ZCC_Android "
                f"logs and table tbl_network_interface of device_stats db for timestamp = {timestamp}")

            if ifname_from_db != ifname_from_log:
                status = False
                msg = (
                    f"[{ifname_from_db}:{ifname_from_log}]ifname from DB and log not "
                    f"matching for Interface with GUID:{guid_from_db} at timestamp:{timestamp}")
                break

            if int(is_active_from_db) != int(is_active_from_logs):
                status = False
                msg = (
                    f"[{is_active_from_db}:{is_active_from_logs}]IsActive(isConnected) from DB and log not "
                    f"matching for Interface with GUID:{guid_from_db} at timestamp:{timestamp}")

            if float(bandwidth_from_db) != float(bandwidth_from_log):
                status = False
                msg = (f"[{bandwidth_from_db}:{bandwidth_from_log}]Bandwidth from DB and log not "
                       f"matching for Interface with GUID:{guid_from_db} at timestamp:{timestamp}")

            if float(send_bps_from_db) != float(send_bps_from_log):
                status = False
                msg = (f"[{send_bps_from_db}:{send_bps_from_log}] send_bps Field from DB and log not matching for "
                       f"Interface with GUID:{guid_from_db} at timestamp:{timestamp}")

            if float(recv_bps_from_db) != float(recv_bps_from_log):
                status = False
                msg = (f"[{recv_bps_from_db}:{recv_bps_from_log}] Recv_bps Field from DB and log not matching for "
                       f"Interface with GUID:{guid_from_db} at timestamp:{timestamp}")

        assert status, msg

    @allure.title("Verify data from device info should correctly inserted to the tbl_wifi_info table device_stats db")
    @pytest.mark.xray(["QA-289612", "QA-289613"])
    @add_markers("regression")
    def test_validate_tbl_wifi_info_from_android_logs_to_db_data(self):
        """
            This function is validating data of ZDX:setWifiDetails  to the device stats db data

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Verify data from device info should correctly inserted to the"
                             " tbl_wifi_info table device_stats db")
        status = True
        msg = ""

        self.logger_obj.info("Fetching ZCC_Android logs data for validation")
        android_logs_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                              plugin='tunnel')
        if android_logs_data is None:
            status = False
            msg = "Not able to fetch data from 'ZCC_Android' log file"
        assert status, msg

        sp = "ZDX:setWifiDetails"
        ep = "ZDX:setMemoryDetails"

        self.logger_obj.info("Fetching setWifiDetails from ZCC_Android logs")
        set_wifi_details = self.log_handler.fetch_device_stats_info(data=android_logs_data, start_pattern=sp,
                                                                    end_pattern=ep)
        assert set_wifi_details[0], set_wifi_details[1]
        length = len(set_wifi_details[2])
        set_wifi_details = set_wifi_details[2]

        self.logger_obj.info(f"--------------setWifiDetails data from logs--------------")
        for index in range(len(set_wifi_details)):
            ifname = set_wifi_details[index]['ifDescription']
            rssi = set_wifi_details[index]['rssi']
            self.logger_obj.info(f"ifname = {ifname} , rssi_dbm = {rssi}")
        self.logger_obj.info(f"---------------------------------------------------------")

        query = "select * from tbl_wifi_info"
        row_data = self.db_handler.execute_query(query)
        if self.device_type.lower() == "chromeos":
            set_wifi_details = set_wifi_details[1:]
        length = min(length, len(row_data))

        for index in range(length):
            # for [timestamp,refid,ifname,ssid,signal_quality,rssi_dbm]
            timestamp = row_data[index][0]
            db_if_name = row_data[index][2]
            db_signal_quality = row_data[index][4]
            db_rssi_dbm = row_data[index][5]
            signal_quality_from_rssi = 0

            if db_rssi_dbm == 0:
                signal_quality_from_rssi = 0
            elif db_rssi_dbm <= -100:
                signal_quality_from_rssi = 0
            elif db_rssi_dbm >= -50:
                signal_quality_from_rssi = 100
            elif -50 > db_rssi_dbm > -100:
                signal_quality_from_rssi = 2 * (db_rssi_dbm + 100)

            ifname_from_log = set_wifi_details[index]['ifDescription']
            rssi_dbm_from_log = set_wifi_details[index]['rssi']

            self.logger_obj.info(
                f"Validating 'ifname', 'rssi_dbm', 'signal_quality' values from ZCC_Android "
                f"logs and table tbl_wifi_info of device_stats db for timestamp = {timestamp}")

            if db_if_name != ifname_from_log:
                status = False
                msg = (f"[DB:LOG]:[{db_if_name}:{ifname_from_log}] ifname not matching "
                       f"for INTERFACE: {db_if_name} at timestamp: {timestamp}")
                break

            if int(db_rssi_dbm) != int(rssi_dbm_from_log):
                status = False
                msg = (f"[DB:LOG]:[{rssi_dbm_from_log}:{db_rssi_dbm}] RSSI_dbm not matching "
                       f"for INTERFACE: {db_if_name} in timestamp: {timestamp}")
                break

            if int(db_signal_quality) != int(signal_quality_from_rssi):
                status = False
                msg = (f"[DB:DB]:[{db_signal_quality}:{signal_quality_from_rssi}] Signal quality is not matching for "
                       f"INTERFACE: {db_if_name} at timestamp: {timestamp} with formula => SignalQuality = 2 * (RSSI+100)")
                break

        assert status, msg

    @allure.title("Verify raw data should be correctly inserted to tbl_mon_processes of device stats ZCC_Android logs")
    @pytest.mark.xray("QA-289620")
    @add_markers("regression")
    def test_validate_tbl_mon_processes_from_android_logs_to_db_data(self):
        """
            This function is validating data of ZDX:setProcessDetails  to the device stats db data

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.logger_obj.info("Verify raw data should be correctly inserted to tbl_mon_processes"
                             " of device stats from ZCC_Android logs")
        status = True
        msg = ""

        self.logger_obj.info("Fetching ZCC_Android logs data for validation")
        android_logs_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                              plugin='tunnel')
        if android_logs_data is None:
            status = False
            msg = "Not able to fetch data from 'ZCC_Android' log file"
        assert status, msg

        sp = "ZDX:setProcessDetails"
        ep = "ZDXWorker:End"

        self.logger_obj.info("Fetching setProcessDetails from ZCC_Android logs")
        set_processes_details = self.log_handler.fetch_device_stats_info(data=android_logs_data, start_pattern=sp,
                                                                         end_pattern=ep)
        assert set_processes_details[0], set_processes_details[1]
        length = len(set_processes_details[2])
        set_processes_details = set_processes_details[2]

        self.logger_obj.info(f"--------------setProcessDetails data from logs--------------")
        for index in range(len(set_processes_details)):
            pid = set_processes_details[index]['pid']
            name = set_processes_details[index]['name']
            mem_bytes = set_processes_details[index]['memSize']
            mem_pct = round(float(set_processes_details[index]['memUsagePct']), 2)
            self.logger_obj.info(f"pid = {pid} , name = {name} , mem_wss_bytes = {mem_bytes} , mem_wss_pct = {mem_pct}")
        self.logger_obj.info(f"---------------------------------------------------------")

        query = "select * from tbl_mon_processes"
        row_data = self.db_handler.execute_query(query)
        if self.device_type.lower() == "chromeos":
            set_processes_details = set_processes_details[1:]
        length = min(length, len(row_data))

        for index in range(length):
            # for [timestamp,refid,pid,name,cpu_user,cpu_kernel,cpu_total,cpu_total_pct,mem_wss_bytes,mem_wss_pct]
            timestamp = row_data[index][0]
            pid_from_db = row_data[index][2]
            name_from_db = row_data[index][3]
            mem_wss_bytes_from_db = row_data[index][8]
            mem_wss_pct_from_db = round(float(row_data[index][9]), 2)

            pid_from_log = set_processes_details[index]['pid']
            name_from_log = set_processes_details[index]['name']
            mem_wss_bytes_from_log = set_processes_details[index]['memSize']
            mem_wss_pct_from_log = round(float(set_processes_details[index]['memUsagePct']), 2)

            self.logger_obj.info(
                f"Validating 'pid', 'name', 'mem_wss_bytes', 'mem_wss_pct' values from ZCC_Android "
                f"logs and table tbl_mon_processes of device_stats db for timestamp = {timestamp}")

            if pid_from_log != pid_from_db:
                status = False
                msg = (f"[DB:LOG]:[{pid_from_db}:{pid_from_log}] pid not matching "
                       f"at timestamp: {timestamp}")
                break

            if name_from_log != name_from_db:
                status = False
                msg = (f"[DB:LOG]:[{name_from_db}:{name_from_log}] name of service is not matching "
                       f"at timestamp: {timestamp}")
                break

            if int(mem_wss_bytes_from_db) != int(mem_wss_bytes_from_log):
                status = False
                msg = (f"[DB:LOG]:[{mem_wss_bytes_from_db}:{mem_wss_bytes_from_log}] mem_wss_bytes is not matching "
                       f"at timestamp: {timestamp}")
                break

            if mem_wss_pct_from_db != mem_wss_pct_from_log:
                status = False
                msg = (f"[DB:DB]:[{mem_wss_pct_from_db}:{mem_wss_pct_from_log}] mem_wss_pct is not matching for "
                       f"at timestamp: {timestamp}")
                break

        assert status, msg

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Deletion of forward profile and app profile
        2. Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """
        self.logger_obj.info("Closing already opened db connection")
        self.db_handler.close_db_connection()

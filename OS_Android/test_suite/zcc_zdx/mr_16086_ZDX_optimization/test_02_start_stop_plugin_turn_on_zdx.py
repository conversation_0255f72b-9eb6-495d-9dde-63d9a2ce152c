import time, os
import pytest, allure
from OS_Android.test_suite.zcc_zdx.mr_16086_ZDX_optimization import conftest
from common_lib.Custom_Markers import add_markers
from common_lib.helper_function import help_return
from OS_Android.library.ui_zcc import Zcc


class TestZdxOptimizationStartStopPlugins:
    @allure.title("Setup Method")
    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.variables = conftest.variables
        self.logger_obj = conftest.logger_obj

        self.tray_file = self.log_handler.get_log_file_name(plugin_name="tray")
        assert self.tray_file[0], self.tray_file[0]
        self.tray_file = self.tray_file[1]

        self.tunnel_file = self.log_handler.get_log_file_name(plugin_name="tunnel")
        assert self.tunnel_file[0], self.tunnel_file[0]
        self.tunnel_file = self.tunnel_file[1]

        device_type = self.sys_ops.android_device_type()
        if device_type.lower() == "chromeos":
            pytest.skip(f" Not supported for Chromeos ")

        self.zcc = Zcc(log_handle=self.logger_obj, start_app=True)
        self.password = conftest.password

        time.sleep(2)

        self.zcc.logger.info("Clearing ZCC Logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Validating if ZDX service status is ON")
        result = self.zcc.verify_service_status(service_status="on", service_type='ZDX')
        assert result[0], result[1]

        self.zcc.logger.info("Sleep 30s :: Before putting device to idle state")
        time.sleep(30)

        self.zcc.logger.info("Turning device screen OFF")
        result = self.sys_ops.toggle_screen_on_off(screen='OFF')
        assert result[0], result[1]

        self.zcc.logger.info("Sleep 5s :: Before putting device to idle state")
        time.sleep(5)

        self.zcc.logger.info("Running ADB command for putting device to idle state")
        cmd = "adb shell dumpsys deviceidle force-idle"
        result = self.sys_ops.run_adb_command(cmd)
        assert result[0], result[1]

        self.zcc.logger.info("Sleep 2 mins :: Collecting data after device idle for validation")
        time.sleep(120)

        self.zcc.logger.info("Running ADB command for removing device-idle state")
        cmd = "adb shell dumpsys deviceidle unforce"
        result = self.sys_ops.run_adb_command(cmd)
        assert result[0], result[1]

        self.zcc.logger.info("Sleep 5s :: Before unlocking the device")
        time.sleep(5)

        self.zcc.logger.info("Device Unlocking/Screen ON")
        result = self.sys_ops.toggle_screen_on_off(screen='ON', pin_or_password=self.password)
        assert result[0], result[1]

        self.zcc.bring_zcc_to_focus()

        self.zcc.logger.info("Sleep 10s :: For zdx to connect and stable")
        time.sleep(10)

        self.zcc.logger.info("Validating if ZDX service status is ON")
        result = self.zcc.verify_service_status(service_status="on", service_type='ZDX')
        assert result[0], result[1]

        self.zcc.logger.info("Sleep 30s for data collection")
        time.sleep(30)

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

    @allure.title("Verify we should stop all the zdx plugin (Turn off UPM) when device goes into deep idle mode")
    @pytest.mark.xray(["QA-289621", "QA-289623"])
    @add_markers("regression")
    def test_device_idle_mode_change_start_and_stop_zdx(self):
        """
            Verify we should stop all the zdx plugin (Turn off UPM) when device goes into deep idle mode

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """

        self.zcc.logger.info("Verify we should stop all the zdx plugin (Turn off UPM)"
                             " when device goes into deep idle mode")
        status = True
        msg = ""

        turn_off_pattern = ["android.os.action.DEVICE_IDLE_MODE_CHANGED",
                            "Device idle mode changed stopping ZDX",
                            "ZDXWorkManager:stopZDX called in background: true"]

        turn_on_pattern = ["android.os.action.DEVICE_IDLE_MODE_CHANGED",
                           "Device idle mode changed starting ZDX",
                           "Digital Experience Turned On"]

        self.zcc.logger.info("Fetching ZCC_Android logs data for validation")
        android_logs_data = self.log_handler.get_latest_file_data_with_plugin(path=self.const_obj.LOG_PATH,
                                                                              plugin='tray')
        if android_logs_data is None:
            status = False
            msg = "Not able to fetch data from 'ZCC_Android' log file"
        assert status, msg

        self.zcc.logger.info("Validation ZDX turn off on device idle from logs")
        for pattern in turn_off_pattern:
            result = help_return(self.log_obj.search_log_file, file=self.tray_file, words_to_find_in_line=pattern,
                                 directory=self.const_obj.LOG_PATH, break_after_first_hit=True)
            assert result[0], result[1]

        self.zcc.logger.info("Validation ZDX turn ON on device wakeup from logs")
        for pattern in turn_on_pattern:
            result = help_return(self.log_obj.search_log_file, file=self.tray_file, words_to_find_in_line=pattern,
                                 directory=self.const_obj.LOG_PATH, break_after_first_hit=True,
                                 start_line = 'Device idle mode changed stopping ZDX')
            assert result[0], result[1]

    @allure.title("Verify we should stop all the zdx plugin (Turn off UPM) when device goes into deep idle mode")
    @pytest.mark.xray("QA-289606")
    @add_markers("regression")
    def test_stop_all_the_zdx_plugin_on_device_idle(self):
        """
            Verify we should stop all the zdx plugin (Turn off UPM) when device goes into deep idle mode

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Verify we should stop all the zdx plugin (Turn off UPM)"
                             "when device goes into deep idle mode")
        status = True
        msg = ""

        start_line_for_stop = "UPM policy downloader stop called"
        plugin_stop_pattern = ["ZPC: Stopped Traceroute Monitor",
                               "ZPC: Stopped WebLoad Monitor",
                               "ZPC: Stopped DeviceStats Monitor"]

        self.zcc.logger.info("Fetching  ZSA_Upm logs data for validation")
        all_upm_files_data = self.log_handler.get_latest_logfile(src=self.const_obj.LOG_PATH)['upm']

        if len(all_upm_files_data[0]) < 2:
            assert False, (
                f"Less than 2 UPM log file is present at {self.const_obj.LOG_PATH}, it should be more than 1,"
                f"because we did turn off and ON UPM, turn ON should create new log file")

        self.zcc.logger.info("Validation plugin stop when device goes to device-idle mode")
        second_latest_file_data = all_upm_files_data[len(all_upm_files_data) - 2]
        for pattern in plugin_stop_pattern:
            result = help_return(self.log_obj.search_log_file, file=second_latest_file_data,
                                 words_to_find_in_line=pattern,
                                 start_line=start_line_for_stop, break_after_first_hit=True)
            assert result[0], result[1]

    @allure.title("Verify we should start all the zdx plugin automatically (Turn on UPM) when device screen tuned ON")
    @pytest.mark.xray("QA-289607")
    @add_markers("regression")
    def test_start_all_the_zdx_plugin_on_device_screen_on_after_idle(self):
        """
            Verify we should start all the zdx plugin automatically (Turn on UPM) when device screen tuned ON

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info(
            "Verify we should start all the zdx plugin automatically (Turn on UPM) when device screen tuned ON")
        status = True
        msg = ""

        plugin_start_pattern = ["Start plugins called",
                                "ZPC: Traceroute plugin started",
                                "ZPC: Webload plugin started",
                                "ZPC: Device stats plugin started"]

        self.zcc.logger.info("Validation plugin start when device comes out/wakeup from device-idle mode")
        for pattern in plugin_start_pattern:
            result = help_return(self.log_obj.search_log_file, file="ZSAUpm_", words_to_find_in_line=pattern,
                                 directory=self.const_obj.LOG_PATH, break_after_first_hit=True)
            assert result[0], result[1]

    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Deletion of forward profile and app profile
        2. Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """
        self.zcc.logger.info("Tear Down")
        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout ZCC")
            result = self.zcc.logout()
            assert result[0], result[1]

# ZDX_Optimization - Requirements
## Prerequisites:
    1) On MA compnay and user must be configured.
    2) On Android device zcc version should be installed
    3) There should not any configuration present on ZIA
    4) ZDX service should be enabled

```commandline
  "DEVICE_PIN_OR_PASSWORD": "1234",          # Device pin or password should be configured in config.json

```


## Commands to Execute Automation:
**Running All Cases:** python trigger_automation.py --config <config.json> --testcases test_suite/zcc_zdx/mr_16086_ZDX_optimization

**Running One Test File from mr_16086_ZDX_optimization folder:** python trigger_automation.py --config config.json --testcases test_suite/zcc_zdx/mr_16086_ZDX_optimization/*.py

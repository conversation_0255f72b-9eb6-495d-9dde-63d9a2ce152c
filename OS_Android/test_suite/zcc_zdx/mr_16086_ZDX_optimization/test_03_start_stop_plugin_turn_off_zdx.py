import time, os
import pytest, allure
from OS_Android.test_suite.zcc_zdx.mr_16086_ZDX_optimization import conftest
from common_lib.Custom_Markers import add_markers
from common_lib.helper_function import help_return
from OS_Android.library.ui_zcc import Zcc


class TestZdxOptimizationNoStartStopPlugins:
    @allure.title("Setup Method")
    def setup_class(self):
        """
        This function performs the setup process for the test class.
        It includes the following steps:
        1. Creation of forward profile and app profile
        2. Logout zcc and clear logs
        3. Login to ZCC
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.
        """

        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.db_handler = conftest.db_handler
        self.log_handler = conftest.log_handler
        self.variables = conftest.variables
        self.logger_obj = conftest.logger_obj

        self.tray_file = self.log_handler.get_log_file_name(plugin_name="tray")
        assert self.tray_file[0], self.tray_file[0]
        self.tray_file = self.tray_file[1]

        self.tunnel_file = self.log_handler.get_log_file_name(plugin_name="tunnel")
        assert self.tunnel_file[0], self.tunnel_file[0]
        self.tunnel_file = self.tunnel_file[1]

        device_type = self.sys_ops.android_device_type()
        if device_type.lower() == "chromeos":
            pytest.skip(f" Not supported for Chromeos ")

        self.zcc = Zcc(log_handle=self.logger_obj, start_app=True)
        self.password = conftest.password

        time.sleep(2)

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout ZCC")
            result = self.zcc.logout()
            assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]

        self.zcc.logger.info("Validating if ZDX service status is ON")
        result = self.zcc.verify_service_status(service_status="on", service_type='ZDX')
        assert result[0], result[1]

        self.zcc.logger.info("Turning ZDX OFF")
        result = self.zcc.toggle_service(service="ZDX", action=False)
        assert result[0], result[1]

        self.zcc.logger.info("Sleep 20s :: Before turning OFF ZDX")
        time.sleep(20)

        self.zcc.logger.info("Turning device screen OFF")
        result = self.sys_ops.toggle_screen_on_off(screen='OFF')
        assert result[0], result[1]

        self.zcc.logger.info("Sleep 5s :: Before putting device to idle state")
        time.sleep(5)

        self.zcc.logger.info("Running ADB command for putting device to idle state")
        cmd = "adb shell dumpsys deviceidle force-idle"
        result = self.sys_ops.run_adb_command(cmd)
        assert result[0], result[1]

        self.zcc.logger.info("Sleep 1 min :: Collecting data after device idle for validation")
        time.sleep(60)

        self.zcc.logger.info("Running ADB command for removing device-idle state")
        cmd = "adb shell dumpsys deviceidle unforce"
        result = self.sys_ops.run_adb_command(cmd)
        assert result[0], result[1]

        self.zcc.logger.info("Sleep 5s :: Before unlocking the device")
        time.sleep(5)

        self.zcc.logger.info("Device Unlocking/Screen ON")
        result = self.sys_ops.toggle_screen_on_off(screen='ON', pin_or_password=self.password)
        assert result[0], result[1]

        self.zcc.logger.info("Sleep 5s")
        time.sleep(5)

        self.zcc.bring_zcc_to_focus()

        self.zcc.logger.info("Sleep 30s :: Before exporting the logs")
        time.sleep(30)

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

    @allure.title("Verify we should not Turn ON UPM when device comes out of deep idle mode if upm was already"
                  " turned off before going to idle")
    @pytest.mark.xray("QA-289608")
    @add_markers("regression")
    def test_no_start_zdx_on_wake_up_if_it_was_already_turned_off(self):
        """
            Verify we should not Turn ON UPM when device comes out of deep idle mode if upm was already
            turned off before going to idle

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Verify we should not Turn ON UPM when device comes out of deep"
                             " idle mode if upm was already turned off before going to idle")

        self.zcc.logger.info("Validating if ZDX service status is OFF, is should be OFF")
        result = self.zcc.verify_service_status(service_status="off", service_type='ZDX')
        assert result[0], result[1]

    @allure.title("Verify 'Device idle mode changed starting ZDX' logs will not be there when device comes out deep"
                  " idle mode (UPM was already turned off manually before putting it to idle)")
    @pytest.mark.xray("QA-289624")
    @add_markers("regression")
    def test_no_device_idle_mode_change_start_zdx(self):
        """
            Verify 'Device idle mode changed starting ZDX' logs will not be there when device comes out deep
            idle mode (UPM was already turned off manually before putting it to idle)

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Verify 'Device idle mode changed starting ZDX' logs will not be there when device comes"
                             " out deep idle mode (UPM was already turned off manually before putting it to idle)")

        pattern = "v7.a:Device idle mode changed starting ZDX"
        result = help_return(self.log_obj.search_log_file, file=self.tray_file, words_to_find_in_line=pattern,
                             directory=self.const_obj.LOG_PATH, failure_expected=True)
        assert result[0], result[1]

    @allure.title("Verify 'Device idle mode changed stopping ZDX' logs will not be ther when device"
                  " goes to deep idle mode (UPM was already turned off manually before putting it to idle)")
    @pytest.mark.xray("QA-289622")
    @add_markers("regression")
    def test_no_device_idle_mode_change_stop_zdx(self):
        """
            Verify "Device idle mode changed stopping ZDX" logs will not be ther when device goes to deep idle mode
             (UPM was already turned off manually before putting it to idle)

            Args:
                self (object): Instance of the test class.
            Returns:
                None
            Raises:
                AssertionError: If any of the steps fail.
        """
        self.zcc.logger.info("Verify 'Device idle mode changed stopping ZDX' logs will not be ther when device goes to"
                             " deep idle mode (UPM was already turned off manually before putting it to idle)")

        pattern= "v7.a:Device idle mode changed stopping ZDX"

        self.zcc.logger.info("Validating 'Device idle mode changed stopping ZDX' should not be present in the logs")
        result = help_return(self.log_obj.search_log_file, file=self.tray_file, words_to_find_in_line=pattern,
                             directory=self.const_obj.LOG_PATH, failure_expected = True)
        assert result[0], result[1]


    @allure.title("teardown method")
    def teardown_class(self):
        """
        This function performs the teardown process for the test class.
        It includes the following steps:
        1. Deletion of forward profile and app profile
        2. Logout zcc and clear logs
        Args:
            self (object): Instance of the test class.
        Returns:
            None
        Raises:
            AssertionError: If any of the steps fail.

        """
        self.zcc.logger.info("Tear Down")
        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

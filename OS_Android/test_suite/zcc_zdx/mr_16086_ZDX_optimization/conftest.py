import pytest
import json

from common_lib.mobileadmin.deviceoverview import Device<PERSON>ver<PERSON>
from common_lib.mobileadmin.appprofile import AppProfile
from common_lib.mobileadmin.forwardingprofile import ForwardingProfile
from common_lib.common.log_ops import *
from OS_Android.library.ops_system import SysOps
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
from OS_Android.library.database_parse import <PERSON>b<PERSON><PERSON><PERSON>
from OS_Android.library.database_parse import <PERSON>g<PERSON>andler
from OS_Android.library.ui_zcc import Zcc
from OS_Android.library.zdx_admin import ZDX_ADMIN

config_path = os.path.join(os.getcwd(), "config", os.environ.get("CONFIG_FILE"))

with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log", log_level="DEBUG")

device_overview = DeviceOverview(variables["CLOUD"], os.environ.get("CONFIG_FILE"), log_handle=logger_obj)

const_obj = constants.Utils()

sys_ops = SysOps(logger_obj, variables["CLOUD"])

export_logs = FetchAndroidLogs(logger_obj)

log_ops = LogOps(log_handle=logger_obj)

pcap_ops = PcapOps(log_handle=logger_obj)

app_profile_obj = AppProfile(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)
forwarding_profile_obj = ForwardingProfile(cloud=variables["CLOUD"], config=config_path, log_handle=logger_obj)
zdx_admin = ZDX_ADMIN(cloudname=variables["CLOUD_DETAILS"]["ZDX"], configfile=variables,
                                   logger=logger_obj)
db_handler = DbHandler()
log_handler = LogHandler()

zdx_application = variables["ZDX_ADMIN"]["ZDX_APPLICATION"][0]
zia_traceroute_domain = zdx_application["tracert_icmp"]["host"]
zia_webprobe_url = zdx_application["web_probe"]["url"]
password = variables["DEVICE_PIN_OR_PASSWORD"]
probe_port = 443


def make_teardown():
    """
    1. Delete App and Forwarding profile
    """

    logger_obj.info("Deleting App profile for mr_16086_ZDX_optimization")
    try:
        result = app_profile_obj.delete_app_profile(operating_system="Android")
        assert result[0], result[1]
    except Exception as e:
        logger_obj.info(str(e))

    logger_obj.info("Deleting Forwarding profile for mr_16086_ZDX_optimization")
    try:
        result = forwarding_profile_obj.delete_forwarding_profile()
        assert result[0], result[1]
    except Exception as e:
        logger_obj.info(str(e))

    logger_obj.info("Deleting webmonitor and traceroute for testing in mr_16086_ZDX_optimization from zdx_regression")
    zdx_admin.delete_application_and_monitors()


@pytest.fixture(scope="package", autouse=True)
def base_setup(request):
    app_profile = app_profile_obj
    forwarding_profile = forwarding_profile_obj

    try:
        zcc = Zcc(log_handle=logger_obj, start_app=True)

        logger_obj.info("Deleting App profile")
        try:
            result = app_profile.delete_app_profile(operating_system="Android")
            assert result[0], result[1]
        except Exception as e:
            logger_obj.info(str(e))

        logger_obj.info("Deleting Forwarding profile")
        try:
            result = forwarding_profile.delete_forwarding_profile()
            assert result[0], result[1]
        except Exception as e:
            logger_obj.info(str(e))

        logger_obj.info("Creating Forwarding profile")
        result = forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]

        logger_obj.info("Creating App profile")
        result = app_profile.create_app_profile(operating_system="Android")
        assert result[0], result[1]

        logger_obj.info("Creating webmonitor and traceroute for testing in mr_16086_ZDX_optimization from zdx_regression")
        zdx_admin.create_application_and_monitors()

        logged_in = zcc.validate_zcc_logged_in()
        if logged_in[0]:
            logger_obj.info("Clear logs from zcc app")
            result = zcc.clear_logs()
            assert result[0], result[1]

            logger_obj.info("Logout from zcc")
            result = zcc.logout()
            assert result[0], result[1]

        result = zcc.login(variables["ZIA_USER_ID"], variables["ZIA_USER_PASSWORD"],
                           variables["ZPA_USER_ID"], variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]

        logger_obj.info("Validating if ZDX service status is ON")
        result = zcc.verify_service_status(service_status="on", service_type='ZDX')
        assert result[0], result[1]

        logger_obj.info("Sleep :: 7 mins to collect zdx data")
        time.sleep(420)

        logger_obj.info("Fetch logs from android device")
        result = zcc.export_logs()
        assert result[0], result[1]

        logger_obj.info("Export logs from android device")
        result = export_logs.read_export_log_mail()
        assert result[0], result[1]

    except AssertionError as e:
        request.addfinalizer(make_teardown)
        pytest.skip(f"setup failed reason:{e}")
    else:
        request.addfinalizer(make_teardown)
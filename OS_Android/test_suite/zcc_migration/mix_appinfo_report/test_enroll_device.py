import pytest,allure
from OS_Android.test_suite.zcc_migration.mix_appinfo_report import conftest
from common_lib.Custom_Markers import add_markers
from OS_Android.library.ui_zcc import Zcc
import time


class TestEnrollDevice:

    @allure.title("Setup Method")
    def setup_class(self):

        self.logger_obj = conftest.logger_obj
        self.zcc = Zcc(start_app=True,log_handle=self.logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.device_overview = conftest.device_overview
        self.trusted_network = conftest.trusted_network
        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

    @allure.title("Validate device retire functionality from mobile admin portal")
    @pytest.mark.xray("QA-58314")
    @add_markers("regression", "P0")
    def test_retire(self, setup_teardown):
        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(20)

        self.zcc.logger.info("Verify status for ZIA is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = self.device_overview.fetch_enrolled_devices_details(get_registration_status=True)
        assert result[0], result[1] is True

        result = self.device_overview.force_remove_device_ma()
        assert result[0], result[1]

        result = self.device_overview.fetch_enrolled_devices_details(get_registration_status=True)
        assert result[0] is False, result[1]



    @allure.title("Validate device status on mobile portal after login/logout to app.")
    @pytest.mark.xray(["QA-58316", "QA-156035"])
    @add_markers("regression", "P0")
    def test_device_status(self, setup_teardown):

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(20)

        self.zcc.logger.info("Verify status for ZIA is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = self.zcc.export_logs()
        assert result[0], result[1]

        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.zcc.logger.info("Look for match in appinfo file")
        self.log_obj.search_log_file(file="AppInfo",words_to_find_in_line=["Proxy State: TUNNEL_FORWARDING"], directory=self.const_obj.LOG_PATH)

        result = self.device_overview.fetch_enrolled_devices_details(get_registration_status=True)
        assert result[0], result[1]

        result = self.zcc.clear_logs()
        assert result[0], result[1]
        result = self.zcc.logout()
        assert result[0], result[1]

        result = self.device_overview.fetch_enrolled_devices_details(get_registration_status=True)
        assert result[0], result[1]

    @allure.title("verify enrolled device status on MA when Auth become successful")
    @pytest.mark.xray("QA-139388")
    @add_markers("regression", "P0")
    def test_enrolled_device(self, setup_teardown):
        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.zcc.logger.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.zcc.logger.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(20)

        self.zcc.logger.info("Verify status for ZIA is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = self.zcc.export_logs()
        assert result[0], result[1]

        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.zcc.logger.info("Look for match in appinfo file")
        self.log_obj.search_log_file(file="AppInfo",words_to_find_in_line=["Proxy State: TUNNEL_FORWARDING"], directory=self.const_obj.LOG_PATH)

        result = self.device_overview.fetch_enrolled_devices_details(get_registration_status=True)
        assert result[0], result[1]

        result = self.zcc.clear_logs()
        assert result[0], result[1]

        result = self.zcc.logout()
        assert result[0], result[1]

        result = self.device_overview.fetch_enrolled_devices_details(get_registration_status=True)
        assert result[0], result[1]

    def teardown_class(self):
        '''
        Login if not logged in
        '''


        try:
            result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                    conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
            assert result[0], result[1]

            self.zcc.logger.info("Verify status for ZIA is on")
            result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
            assert result[0], result[1]

        except Exception as e:
            self.zcc.logger.info(str(e))


import time
import pytest,allure
from OS_Android.test_suite.zcc_migration.mix_appinfo_report import conftest
from common_lib.Custom_Markers import add_markers
from common_lib.helper_function import help_return
from OS_Android.library.ui_zcc import Zcc

output = '*********'
status = 'false'


test_input = [
               pytest.param('UserName ', conftest.variables["ZIA_USER_ID"], marks=pytest.mark.xray("QA-58368")),
               pytest.param('CloudName ', conftest.variables["CLOUD"], marks=pytest.mark.xray("QA-58370")),
               pytest.param('IPv6 Address ', status , marks=pytest.mark.xray("QA-58373")),
               pytest.param('Active Network ', 'rmnet1', marks=pytest.mark.xray("QA-58376")),
               pytest.param('PacUrl ', 'https://pac.zscalerbeta.net/y8QGSvW7GdLW/special_character', marks=pytest.mark.xray("QA-58377")),
               pytest.param('Proxy State', 'TUNNEL_FORWARDING', marks=pytest.mark.xray("QA-58378")),
               pytest.param('ZApp Proxy ', '', marks=pytest.mark.xray("QA-58379")),
               #pytest.param('System DNS servers ', ', DNS : [2401:4900:50:9::200, 2404:a800:0:14::1:101a, *************, *************], Domains : []', marks=pytest.mark.xray("QA-58380")),
               pytest.param('VPN Service Active ', 'true', marks=pytest.mark.xray("QA-58382")),
               pytest.param('Zscaler Client Connector version ',  output, marks=pytest.mark.xray("QA-58383"))

            ]


class TestAppinfo:

    @allure.title("Setup Method")
    def setup_class(self):
        self.logger_obj = conftest.logger_obj
        self.zcc = Zcc(start_app=True,log_handle=self.logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.device_overview = conftest.device_overview
        self.trusted_network = conftest.trusted_network
        act_net = self.export_logs.appinfo_file_values('Active Network')
        self.logger_obj.info(f"Current Active Network is {act_net}")

        self.logger_obj.info("Fetching ip addr of active network")
        self.ipv4 = self.sys_ops.active_ipv4(act_net)
        self.ipv6 = self.sys_ops.active_ipv6(act_net)

    @allure.title("Validate username in appinfo file")
    @pytest.mark.parametrize("appinfo_key, appinfo_value", test_input)
    @add_markers("regression")
    def test_appinfo_parameter(self, appinfo_key, appinfo_value, setup_teardown):
        self.zcc.logger.info("Verifying username from appinfo file")
        if appinfo_key == 'ZApp Proxy ':

            proxy = self.zcc.fetch_proxy_server()
            appinfo_value = proxy[3][0]

        if appinfo_key == 'IPv6 Address ' and len(self.ipv4) == 0:
            appinfo_value = 'true'

        if appinfo_key == 'Zscaler Client Connector version ':
            output = self.sys_ops.get_zcc_version()
            appinfo_value = output[2].split('=')[1]
        result = help_return(self.log_obj.search_log_file, file="AppInfo", words_to_find_in_line=[f'{appinfo_key}: {appinfo_value}'],
                                                  directory=self.const_obj.LOG_PATH)

        assert result[0], result[1]



















import pytest,allure
from OS_Android.test_suite.zcc_migration.mix_appinfo_report import conftest
from common_lib.Custom_Markers import add_markers
from common_lib.helper_function import help_return
from OS_Android.library.ui_zcc import Zcc
import time


class TestMixBag:
    @allure.title("Setup Method")
    def setup_class(self):

        self.logger_obj = conftest.logger_obj
        self.zcc = Zcc(start_app=True,log_handle=self.logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.trusted_network = conftest.trusted_network
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs


        result = self.zcc.toggle_service(service='ZIA', action=False)
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_type='ZIA', service_status='off')
        assert result[0], result[1]

        self.zcc.logger.info("Restart services from ZCC")
        result = self.zcc.restart_service(object_check="Private Access is Connected")
        assert result[0], result[1]

        self.zcc.logger.info("turn on the zia service")
        result = self.zcc.toggle_service(service='ZIA', action=True)
        assert result[0], result[1]

        self.zcc.logger.info("verify zia service is On")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]


    @allure.title("Validate time connected on zapp ui")
    @pytest.mark.xray("QA-58336")
    @add_markers("regression", "P1")
    def test_time_connected(self,setup_teardown):

        try:
            self.zcc.logger.info("turn on the zia service")
            result = self.zcc.toggle_service(service='ZIA', action=True)
            assert result[0], result[1]

        except Exception as e:
            self.zcc.logger.info("zia service is On")


        result = self.zcc.verify_time_connected()
        assert result[0], result[1]

    @allure.title("Validate re-login to zapp.")
    @pytest.mark.xray("QA-58326")
    @add_markers("regression", "P0")
    def test_re_login(self,setup_teardown):
        result = self.zcc.validate_zcc_logged_in()
        self.zcc.logger.info("Verify logging to ZCC is successful")
        assert result[0], result[1]

        self.zcc.logger.info("Logout from ZCC")
        result = self.zcc.logout()
        assert result[0], result[1]

        result = self.zcc.validate_zcc_logged_in()
        self.zcc.logger.info("Verify logging to ZCC is successful")
        assert not result[0], result[1]

        time.sleep(2)
        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(10)

        self.zcc.logger.info("Verify status for ZIA is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        result = self.zcc.validate_zcc_logged_in()
        self.zcc.logger.info("Verify logging to ZCC is successful")
        assert result[0], result[1]

    @allure.title("Validate ZCC stores logs properly")
    @pytest.mark.xray("QA-156050")
    @add_markers("regression", "P0")
    def test_zcc_store_logs(self, setup_teardown):

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.zcc.logger.info("Read appinfo logs to validate logs in zcc stored properly or not")
        self.zcc.logger.info("Verifying username from appinfo file")

        result = help_return(self.log_obj.search_log_file, file="AppInfo", words_to_find_in_line=[f'UserName : {conftest.variables["ZIA_USER_ID"]}'],
                             directory=self.const_obj.LOG_PATH)

        assert result[0], result[1]

    @allure.title("Validate app policy name from zapp ui")
    @pytest.mark.xray("QA-58308")
    @add_markers("regression", "P0")
    def test_app_policy_name(self, setup_teardown):

        self.zcc.logger.info("Verifying policy name on zcc")
        result = self.zcc.verify_device_policy(self.app_profile.device_policy_name)
        assert result[0], result[1]

    @allure.title("Validate clear logs on zapp")
    @pytest.mark.xray("QA-58312")
    @add_markers("regression", "P0")
    def test_clear_logs(self, setup_teardown):
        self.zcc.logger.info("Clear zcc logs")
        result = self.zcc.clear_logs()
        self.zcc.logger.info("verify logs are clear by checking return type")
        assert result[0], result[1]

    @allure.title("Validate restart of service")
    @pytest.mark.xray("QA-58328")
    @add_markers("regression", "P0")
    def test_restart_service(self,setup_teardown):

        self.zcc.logger.info("verify zia service is on")
        result = self.zcc.verify_service_status(service_type='ZIA', service_status='on')
        assert result[0], result[1]
        self.zcc.logger.info("Restart services from ZCC")
        result = self.zcc.restart_service()
        assert result[0], result[1]
        self.zcc.logger.info("Validating restart is successful")
        assert result[0], result[1]

        self.zcc.logger.info("verify zia service is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

    @allure.title("verify no tunnel crashes should occurs while exiting or restarting zcc")
    @pytest.mark.xray("QA-139392")
    @add_markers("regression", "P2")
    def test_no_crash(self,setup_teardown):

        self.zcc.logger.info("verify zia service is on")
        result = self.zcc.verify_service_status(service_type='ZIA', service_status='on')
        assert result[0], result[1]

        self.zcc.logger.info("Restart services from ZCC")
        result = self.zcc.restart_service()

        self.zcc.logger.info("Validating restart is successful")
        assert result[0], result[1]

        self.zcc.logger.info("verify zia service is on")
        result = self.zcc.verify_service_status(service_type='ZIA', service_status='on')
        assert result[0], result[1]

        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        result = help_return(self.log_obj.search_log_file, file="Zscaler-Android-Logcat", words_to_find_in_line=['fatal'],
                                                  directory=self.const_obj.LOG_PATH, failure_expected = True)

        assert result[0], result[1]

        result = help_return(self.log_obj.search_log_file, file="Zscaler-Android-Logcat", words_to_find_in_line=['crash'],
                                                  directory=self.const_obj.LOG_PATH, failure_expected = True)

    @allure.title("Check update policy functionality")
    @pytest.mark.xray("QA-58298")
    @add_markers("regression", "P0")
    def test_update_policy(self,setup_teardown):

        result = self.forwarding_profile.edit_forwarding_profile(tunnel_mode='none')
        assert result[0], result[1]
        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Verify status for ZIA is off")

        result = self.zcc.verify_service_status(service_type='ZIA', service_status='disable')
        assert result[0], result[1]

        result = self.forwarding_profile.edit_forwarding_profile(tunnel_mode='tunnel_1')
        assert result[0], result[1]

        status = self.zcc.update_policy()

        self.zcc.logger.info("Verify status for ZIA is on")
        result = self.zcc.verify_service_status(service_type='ZIA', service_status='on')
        assert result[0], result[1]

    @allure.title("Zapp should be working fine on restarting service when zia")
    @pytest.mark.xray("QA-62253")
    @add_markers("regression", "P1")
    def test_services_off_restart(self):
        self.zcc.logger.info("turn off the zia service")

        result = self.zcc.toggle_service(service='ZIA', action=False)
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_type='ZIA', service_status='off')
        assert result[0], result[1]

        self.zcc.logger.info("Restart services from ZCC")
        result = self.zcc.restart_service(object_check="Private Access Connected.")
        assert result[0], result[1]

        self.zcc.logger.info("turn on the zia service")
        result = self.zcc.toggle_service(service='ZIA', action=True)
        assert result[0], result[1]

        self.zcc.logger.info("verify zia service is On")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]



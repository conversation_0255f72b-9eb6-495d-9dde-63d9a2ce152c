import allure
import pytest
from OS_Android.test_suite.zcc_migration.pac_automation import conftest
from common_lib.helper_function import *
from common_lib.Custom_Markers import add_markers
from OS_Android.library.log_file_helper_function import *
from OS_Android.library.ui_zcc import Zcc


'''

This Python script tests PAC (Proxy Auto-Configuration) files in a Zscaler environment using pytest and Allure. It 
sets up the testing environment, creates PAC files, edits app profiles, and visits target URLs to check proxy usage. 
The script fetches and exports logs, verifies log entries, and deletes PAC files. The script also includes teardown 
methods to delete app profiles and forwarding profiles, and log out of the Zscaler app.

Generated by <PERSON><PERSON><PERSON><PERSON>, assisted by ZCoder on 19-June-2024 <Please retain this marker>

'''

test_input_parametrize = [
                           pytest.param("special_character",  marks=pytest.mark.xray("QA-105250, QA-105251")),
                           pytest.param("ipbypass",   marks=pytest.mark.xray("QA-58319")),
                           pytest.param("dnsdomainis", marks=pytest.mark.xray("QA-58317")),
                           pytest.param("without_dnsResolve",  marks=pytest.mark.xray("QA-105255")),
                           pytest.param("localHostOrDomain",  marks=pytest.mark.xray("QA-105263"))
                        ]

# These configurations are intentionally done in corresponding to the TC requirements
pac_file_rules = {
    "special_character": {"test_input": ('special_character', 'https://www.llanfairpwllgwyngyllgogerychwyrndrobwllllantysiliogogogoch.co.uk', 'DIRECT'),
                          "pac_name": "Automation_Created_Pac_special_char",
                          "private_ip_bypass": True,
                          "dns_resolution": True,
                          "plain_hostname_rule_check": True,
                          "ftp_bypass": True,
                          "zpa_synthetic_range_bypass": True,
                          "trust_url_bypass": True,
                          "custom_default_return_statement_flag": True,
                          "custom_default_return_statement": "PROXY *************:80; PROXY **************:80; DIRECT",
                          "app_profile_pac": True,
                          "local_proxy_pac": False,
                          "variable_list": [],
                          "variable_based_rules_list": [],
                          "return_pac_url": True,
                          "obfuscate_url": True,
                          "proxy_rules_list": [
                              [{"domain": "theverge.com", "action": "DIRECT", "pacFunction": "dnsDomainIs"},
                               {"domain": "www.theverge.com", "action": "DIRECT", "pacFunction": "dnsDomainIs"}],
                              [{"domain": "*faß*", "action": "DIRECT", "pacFunction": "shExpMatch"}],
                              [{"domain": "*.de", "action": "DIRECT", "pacFunction": "shExpMatch"}],
                              [{"domain": "*dell*", "action": "DIRECT", "pacFunction": "shExpMatch"}],
                              [{"domain": "www.bayern-fass.de", "action": "DIRECT", "pacFunction": "dnsDomainIs"}],
                              [{"domain": "www.llanfairpwllgwyngyllgogerychwyrndrobwllllantysiliogogogoch.co.uk",
                                "action": "DIRECT", "pacFunction": "dnsDomainIs"}],
                              [{"domain": "*buecher*", "action": "DIRECT", "pacFunction": "shExpMatch"}],
                              [{"domain": "nxkjsjdsh.com", "action": "DIRECT", "pacFunction": "dnsDomainIs"},
                               {"domain": "*&^%$#@!nbcjhdshhd1223234", "action": "DIRECT",
                                "pacFunction": "dnsDomainIs"},
                               {"domain": "√.com", "action": "DIRECT", "pacFunction": "dnsDomainIs"}],
                              [{"domain": "gateway.zscalerbeta.net",
                                "action": "PROXY zs-165-225-72-38.gateway.zscalerbeta.net:80",
                                "pacFunction": "dnsDomainIs"}],
                              [{"domain": "www.logitech.com", "action": "PROXY **************:80",
                                "pacFunction": "dnsDomainIs"}]]},

    "ipbypass": {"test_input": ('ipbypass', 'https://www.cgc.edu.in', 'DIRECT'),
                 "pac_name": "Automation_Created_Pac_ipbypass",
                 "private_ip_bypass": True,
                 "dns_resolution": True,
                 "plain_hostname_rule_check": False,
                 "ftp_bypass": False,
                 "zpa_synthetic_range_bypass": False,
                 "trust_url_bypass": False,
                 "custom_default_return_statement_flag": True,
                 "custom_default_return_statement": "PROXY *************:443; PROXY ***************:443; DIRECT",
                 "app_profile_pac": True,
                 "local_proxy_pac": False,
                 "variable_list": [],
                 "variable_based_rules_list": [],
                 "return_pac_url": True,
                 "obfuscate_url": True,
                 "proxy_rules_list": [[{"ipAddress": "*************", "action": "DIRECT", "pacFunction": "isInNet",
                                        "netmask": "*************"}]]},

    "dnsdomainis": {"test_input": ('dnsdomainis', 'https://www.apple.com', 'DIRECT'),
                    "pac_name": "Automation_Created_Pac_dnsdomainis",
                    "private_ip_bypass": True,
                    "dns_resolution": True,
                    "plain_hostname_rule_check": False,
                    "ftp_bypass": False,
                    "zpa_synthetic_range_bypass": False,
                    "trust_url_bypass": False,
                    "custom_default_return_statement_flag": True,
                    "custom_default_return_statement": "PROXY ***************:443",
                    "app_profile_pac": True,
                    "local_proxy_pac": False,
                    "variable_list": [],
                    "variable_based_rules_list": [],
                    "return_pac_url": True,
                    "obfuscate_url": True,
                    "proxy_rules_list": [
                        [{"domain": "www.apple.com", "action": "DIRECT", "pacFunction": "dnsDomainIs"}]]},

    "without_dnsResolve": {"test_input": ('dnsdomainis', 'https://www.apple.com', 'DIRECT'),
                    "pac_name": "Automation_Created_Pac_dnsdomainis",
                    "private_ip_bypass": True,
                    "dns_resolution": False,
                    "plain_hostname_rule_check": False,
                    "ftp_bypass": False,
                    "zpa_synthetic_range_bypass": False,
                    "trust_url_bypass": False,
                    "custom_default_return_statement_flag": True,
                    "custom_default_return_statement": "PROXY ***************:443",
                    "app_profile_pac": True,
                    "local_proxy_pac": False,
                    "variable_list": [],
                    "variable_based_rules_list": [],
                    "return_pac_url": True,
                    "obfuscate_url": True,
                    "proxy_rules_list": [
                        [{"domain": "www.apple.com", "action": "DIRECT", "pacFunction": "dnsDomainIs"}]]},

    "localHostOrDomain": {"test_input": ('localHostOrDomain', 'https://www.logitech.com', 'DIRECT'),
                          "pac_name": "Automation_Created_Pac_localHostOrDomain",
                          "private_ip_bypass": True,
                          "dns_resolution": True,
                          "plain_hostname_rule_check": False,
                          "ftp_bypass": False,
                          "zpa_synthetic_range_bypass": False,
                          "trust_url_bypass": False,
                          "custom_default_return_statement_flag": True,
                          "custom_default_return_statement": "PROXY ***************:443",
                          "app_profile_pac": True,
                          "local_proxy_pac": False,
                          "variable_list": [],
                          "variable_based_rules_list": [],
                          "return_pac_url": True,
                          "obfuscate_url": True,
                          "proxy_rules_list": [[{"domain": "www.logitech.com", "action": "DIRECT", "pacFunction": "localHostOrDomainIs"}]]}
}

class TestPAC:
    @allure.title("Setup Method")
    def setup_class(self):
        '''
        Steps:
        1. Creating instances of required functionalities
        2. Creating Forwarding Profile and App Profile
        3. Logging out to start with a fresh environment
        4. Verifying ZIA connectivity
        '''
        self.logger_obj = conftest.logger_obj
        self.zcc = Zcc(start_app=True,log_handle=self.logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.zia_pac = conftest.zia_pac


        self.zcc.clear_logs()

    @allure.title("Verify PAC long domain name")
    @add_markers("regression")
    @pytest.mark.parametrize("test_data", test_input_parametrize)
    def test_pacfunc(self, test_data, setup_teardown):
        self.zcc.logger.info("Deleting same name PAC file if exists on ZIA")
        try:
            delete_pac = self.zia_pac.delete_pac_file(name=pac_file_rules[test_data]['pac_name'])
            self.zcc.logger.info(pac_file_rules[test_data]['pac_name'])
            assert delete_pac[0], delete_pac[1]
        except Exception as e:
            self.zcc.logger.info(str(e))
            pass

        self.zcc.logger.info("Step 1: Creating PAC file using stored configurations")
        pac_creation = self.zia_pac.create_custom_pac_file(pac_name=pac_file_rules[test_data]['pac_name'],
                                                               private_ip_bypass=pac_file_rules[test_data][
                                                                   'private_ip_bypass'],
                                                               dns_resolution=pac_file_rules[test_data]['dns_resolution'],
                                                               plain_hostname_rule_check=pac_file_rules[test_data][
                                                                   'plain_hostname_rule_check'],
                                                               ftp_bypass=pac_file_rules[test_data]['ftp_bypass'],
                                                               zpa_synthetic_range_bypass=pac_file_rules[test_data][
                                                                   'zpa_synthetic_range_bypass'],
                                                               trust_url_bypass=pac_file_rules[test_data][
                                                                   'trust_url_bypass'],
                                                               custom_default_return_statement_flag=
                                                               pac_file_rules[test_data][
                                                                   'custom_default_return_statement_flag'],
                                                               custom_default_return_statement=pac_file_rules[test_data][
                                                                   'custom_default_return_statement'],
                                                               app_profile_pac=pac_file_rules[test_data][
                                                                   'app_profile_pac'],
                                                               local_proxy_pac=pac_file_rules[test_data][
                                                                   'local_proxy_pac'],
                                                               proxy_rules_list=pac_file_rules[test_data][
                                                                   'proxy_rules_list'],
                                                               variable_list=pac_file_rules[test_data]['variable_list'],
                                                               variable_based_rules_list=pac_file_rules[test_data][
                                                                   'variable_based_rules_list'],
                                                               return_pac_url=pac_file_rules[test_data]['return_pac_url'],
                                                               obfuscate_url=pac_file_rules[test_data]['obfuscate_url'])
        time.sleep(5)
        assert pac_creation[0], pac_creation[1]

        self.zcc.logger.info("Step 2: Editing App profile with and syncing ZApp with new policy")
        pac_url = pac_creation[2]
        result = self.app_profile.edit_app_profile(pac=pac_url, operating_system='Android')
        time.sleep(5)
        assert result[0]
        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(3)

        self.zcc.logger.info("Step 3: Visiting the URL to generate logs")
        function_check = pac_file_rules[test_data]["test_input"][1]
        cmd = f"adb shell am start -a android.intent.action.VIEW -d {function_check}"
        self.sys_ops.adb_commands(cmd)
        time.sleep(7)

        self.zcc.logger.info("Step 4: Fetching and Exporting logs from android device")
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Step 5: Searching over logs")
        proxy_action = pac_file_rules[test_data]["test_input"][2]
        files_to_search = log_file_helper()
        log_found_check = False
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file, words_to_find_in_line=[f'uri={function_check} Proxy={proxy_action}'], directory=conftest.const_obj.LOG_PATH)

            if result[0]:
                log_found_check = True
                break

        assert log_found_check

        self.zcc.logger.info("Step 6: Deleting PAC file")
        delete_pac = self.zia_pac.delete_pac_file(name=pac_file_rules[test_data]['pac_name'])
        self.zcc.logger.info(pac_file_rules[test_data]['pac_name'])
        assert delete_pac[0], delete_pac[1]



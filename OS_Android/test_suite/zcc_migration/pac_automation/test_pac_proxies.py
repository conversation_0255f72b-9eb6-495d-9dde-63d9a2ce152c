import allure
import pytest

from OS_Android.test_suite.zcc_migration.pac_automation import conftest
from scapy.all import *
from common_lib.helper_function import *
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers
from OS_Android.library.log_file_helper_function import *
from OS_Android.library.ui_zcc import Zcc


'''

This Python script tests PAC (Proxy Auto-Configuration) files in a Zscaler environment using pytest and Allure. It 
sets up the testing environment, creates PAC files, edits app profiles, and visits target URLs to check proxy usage. 
The script fetches and exports logs, verifies log entries, and deletes PAC files. The script also includes teardown 
methods to delete app profiles and forwarding profiles, and log out of the Zscaler app.

Generated by <PERSON><PERSON><PERSON><PERSON>, assisted by <PERSON><PERSON>oder on 19-June-2024 <Please retain this marker>

'''

test_input = {
    "function_unreachablegateway": "function FindProxyForURL(url, host) {\n\tif(dnsResolve(host) == \"***************\" || dnsResolve(host) == \"*************\" || dnsResolve(host) == \"*************\") return \"PROXY 260.0.1.1\";\n\treturn \"PROXY ${GATEWAY_HOST}:8800; PROXY ${SECONDARY_GATEWAY_HOST}:8800; DIRECT\";}",

    "function_primaryunreachable": "function FindProxyForURL(url, host) {\n\treturn \"PROXY **************:80; PROXY ***************:80; DIRECT\";}",
}

class TestPacProxies:
    def setup_class(self):
        '''
        Steps:
        1. Creating instances of required functionalities
        2. Creating Forwarding Profile and App Profile
        3. Logging out to start with a fresh environment
        4. Verifying ZIA connectivity
        '''
        self.logger_obj = conftest.logger_obj
        self.zcc = Zcc(start_app=True,log_handle=self.logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.zia_pac = conftest.zia_pac
        self.elements = copy.deepcopy(AndroidElements)

    @allure.title("Validate pac file with unreachable gateway ips then default sme should be followed")
    @pytest.mark.xray("QA-58325")
    @add_markers("regression", "P1")
    def test_unreachable_gateway(self,setup_teardown ):
        testing_function = "function_unreachablegateway"

        self.zcc.logger.info("Step 1: Deleting same name PAC if exists on ZIA")
        try:
            delete_pac = self.zia_pac.delete_pac_file(name="testing_{}".format(testing_function))
            self.zcc.logger.info(name="testing_{}".format(testing_function))
            assert delete_pac[0], delete_pac[1]
        except Exception as e:
            self.zcc.logger.info(str(e))
            pass

        self.zcc.logger.info("Step 2: Creating PAC file")
        result = conftest.zia_pac.create_pac_file(name="testing_{}".format(testing_function),
                                                  pac_content=test_input[testing_function])
        assert result[0], result[1]
        self.zcc.logger.info(result[2])
        pac_url = result[2]['pacUrl']

        self.zcc.logger.info("Step 3: Editing App Profile to add new PAC url")
        result = self.app_profile.edit_app_profile(pac=pac_url, operating_system='Android')
        time.sleep(5)
        assert result[0]

        self.zcc.logger.info("Step 4: Updating policy and storing SME IP")
        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(3)
        self.zcc.ui.click(path=self.elements.WEBSECURITY, AEFC="Internet Security Not Clicked")
        time.sleep(1)
        SME_IP = self.zcc.ui.click(path=self.elements.SME_IP, do_click=False, return_text=True,
                                   AEFC="SME IP not visible")
        time.sleep(1)

        self.zcc.logger.info("Step 5: Visiting the function to check")
        function_check = "https://ip.zscaler.com"
        cmd = f"adb shell am start -a android.intent.action.VIEW -d {function_check}"
        self.sys_ops.adb_commands(cmd)
        time.sleep(7)

        self.zcc.logger.info("Step 6: Fetching and Exporting logs")
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Step 7: Checking logs for unreachable ip")
        to_search = "Registering failed attempt for: 260.0.1.1, Exception: No address found: 260.0.1.1"
        files_to_search = log_file_helper()
        log_found_check = False
        # for file in files_to_search['tunnel_logs']:
        result = help_return(self.log_obj.search_log_file, file="ZCC_Android",
                             words_to_find_in_line=[to_search],
                             directory=conftest.const_obj.LOG_PATH)

        if result[0]:
            log_found_check = True

        assert log_found_check

        self.zcc.logger.info("Step 8: Checking logs for SME ip")
        to_search = "Using primary pac proxy as SME ip: " + SME_IP
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[to_search],
                                 directory=conftest.const_obj.LOG_PATH)

            if result[0]:
                log_found_check = True
                break

        assert log_found_check

        self.zcc.logger.info("Step 9: Deleting the PAC file")
        delete_pac = self.zia_pac.delete_pac_file(name="testing_{}".format(testing_function))
        assert delete_pac[0], delete_pac[1]

    @allure.title("Validate pac file with unreachable gateway ip for primary then secondary ip should be followed")
    @pytest.mark.xray("QA-58324")
    @add_markers("regression", "P0")
    def test_primary_ip(self, setup_teardown):
        testing_function = "function_primaryunreachable"

        self.zcc.logger.info("Step 1: Deleting same name PAC if exists on ZIA")
        try:
            delete_pac = self.zia_pac.delete_pac_file(name="testing_{}".format(testing_function))
            self.zcc.logger.info(name="testing_{}".format(testing_function))
            assert delete_pac[0], delete_pac[1]
        except Exception as e:
            self.zcc.logger.info(str(e))
            pass

        self.zcc.logger.info("Step 2: Creating PAC file")
        result = conftest.zia_pac.create_pac_file(name="testing_{}".format(testing_function),
                                                  pac_content=test_input[testing_function])
        assert result[0], result[1]
        self.zcc.logger.info(result[2])
        pac_url = result[2]['pacUrl']

        self.zcc.logger.info("Step 3: Editing App Profile to add new PAC url")
        result = self.app_profile.edit_app_profile(pac=pac_url, operating_system='Android')
        time.sleep(5)
        assert result[0]

        self.zcc.logger.info("Step 4: Updating policy to sync up with latest app profile changes")
        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(3)

        self.zcc.logger.info("Step 5: Visiting the function to check")
        function_check = ["https://ip.zscaler.com"]
        self.zcc.logger.info("Hitting the target")
        cmd = f"adb shell am start -a android.intent.action.VIEW -d {function_check}"
        self.sys_ops.adb_commands(cmd)
        time.sleep(7)

        self.zcc.logger.info("Step 6: Fetching and Exporting logs")
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Step 7: Verifying that secondary proxy is getting used")
        to_search = "Using secondary pac proxy as SME ip: ***************: 443"
        files_to_search = log_file_helper()
        log_found_check = False
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[to_search],
                                 directory=conftest.const_obj.LOG_PATH)

            if result[0]:
                log_found_check = True
                break

        assert log_found_check

        self.zcc.logger.info("Step 8: Deleting PAC file")
        delete_pac = self.zia_pac.delete_pac_file(name="testing_{}".format(testing_function))
        assert delete_pac[0], delete_pac[1]



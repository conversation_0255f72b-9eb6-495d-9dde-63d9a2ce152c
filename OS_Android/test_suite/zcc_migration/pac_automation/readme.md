# pac_automation TCs - Requirements
## Prerequisites:
    1) On MA, company and user must be properly configured.
    2) On Android device, proper zcc version should be installed along with test apk.
    3) Environment must be clean for PAC files on ZIA.


## Commands to Execute Automation:
**Running All Cases:** python trigger_automation.py --config <config.json> --testcases test_suite/pac_automation

**Running One Test File from debugging_logs folder:** python trigger_automation.py --config config.json --testcases test_suite/pac_automation/*.py

import allure
import pytest
import conftest
from scapy.all import *
from common_lib.helper_function import *
from common_lib.Custom_Markers import add_markers
from OS_Android.library.log_file_helper_function import *
from OS_Android.library.ui_zcc import Zcc
from OS_Android.test_suite.zcc_migration.pac_automation import conftest


'''

This script tests PAC file functionality in a Zscaler environment. It creates custom PAC files, edits app profiles, 
updates policies, and visits URLs to check proxy actions. The methods test large bypass lists, various PAC functions, 
and dnsResolve. The script asserts correct proxy actions in log files.

Generated by <PERSON><PERSON><PERSON><PERSON>, assisted by ZCoder on 19-June-2024 <Please retain this marker>

'''

test_input_bypass_list = [
                           pytest.param("large_bypass_list_domains",  marks=pytest.mark.xray("QA-105253")),
                           pytest.param("large_bypass_list_ips",   marks=pytest.mark.xray("QA-105254"))
                        ]

test_input_functions = [
                           pytest.param("function_shexpmatch",  marks=pytest.mark.xray("QA-105257")),
                           pytest.param("function_dnsdomainis",   marks=pytest.mark.xray("QA-105260")),
                           pytest.param("function_isinnet", marks=pytest.mark.xray("QA-105258"))
                        ]

# These configurations are intentionally done in corresponding to the TC requirements
pac_file_rules = {
    "large_bypass_list_domains": {"test_input": ("large_bypass_list_domains", "https://trust.zscaler.com", 'DIRECT'),
                                  "pac_name": "Automation_Created_PAC_large_bypass_list_domains",
                                  "private_ip_bypass": False,
                                  "dns_resolution": False,
                                  "plain_hostname_rule_check": False,
                                  "ftp_bypass": False,
                                  "zpa_synthetic_range_bypass": False,
                                  "trust_url_bypass": True,
                                  "custom_default_return_statement_flag": True,
                                  "custom_default_return_statement": "PROXY *************:80; PROXY **************:80; DIRECT",
                                  "app_profile_pac": True,
                                  "local_proxy_pac": False,
                                  "variable_list": [],
                                  "variable_based_rules_list": [],
                                  "return_pac_url": True,
                                  "obfuscate_url": True,
                                  "proxy_rules_list": [[{'domain': "trust.zscaler.com", "action": "DIRECT", "pacFunction": "localHostOrDomainIs"}]]
                                  },

    "large_bypass_list_ips": {"test_input": ("large_bypass_list_ips", "https://**************", 'DIRECT'),
                                  "pac_name": "Automation_Created_PAC_large_bypass_list_ips",
                                  "private_ip_bypass": False,
                                  "dns_resolution": False,
                                  "plain_hostname_rule_check": False,
                                  "ftp_bypass": False,
                                  "zpa_synthetic_range_bypass": False,
                                  "trust_url_bypass": False,
                                  "custom_default_return_statement_flag": True,
                                  "custom_default_return_statement": "PROXY *************:80; PROXY **************:80; DIRECT",
                                  "app_profile_pac": True,
                                  "local_proxy_pac": False,
                                  "variable_list": [],
                                  "variable_based_rules_list": [],
                                  "return_pac_url": True,
                                  "obfuscate_url": True,
                                  "proxy_rules_list": [[{"domain": "************", "action": "DIRECT", "pacFunction": "shExpMatch"},
                                                        {"domain": "**************", "action": "DIRECT", "pacFunction": "shExpMatch"},
                                                        {"domain": "74.51.221.132", "action": "DIRECT", "pacFunction": "shExpMatch"},
                                                        {"domain": "**************", "action": "DIRECT", "pacFunction": "shExpMatch"},
                                                        {"domain": "167.187.103.215", "action": "DIRECT", "pacFunction": "shExpMatch"},
                                                        {"domain": "167.187.103.217", "action": "DIRECT", "pacFunction": "shExpMatch"},
                                                        {"domain": "************", "action": "DIRECT", "pacFunction": "shExpMatch"},
                                                        {"domain": "************", "action": "DIRECT", "pacFunction": "shExpMatch"}]]
                                  },

    "function_shexpmatch": {"test_input": ("function_shexpmatch", "https://www.mbusa.com", 'DIRECT'),
                            "pac_name": "Automation_Created_Pac_shExpMatch",
                 "private_ip_bypass": False,
                 "dns_resolution": False,
                 "plain_hostname_rule_check": False,
                 "ftp_bypass": False,
                 "zpa_synthetic_range_bypass": False,
                 "trust_url_bypass": False,
                 "custom_default_return_statement_flag": True,
                 "custom_default_return_statement": "PROXY *************:443; PROXY ***************:443; DIRECT",
                 "app_profile_pac": True,
                 "local_proxy_pac": False,
                 "variable_list": [],
                 "variable_based_rules_list": [],
                 "return_pac_url": True,
                 "obfuscate_url": True,
                 "proxy_rules_list": [[{"domain": "*mbusa*", "action": "DIRECT", "pacFunction": "shExpMatch"}]]},

    "function_dnsdomainis": {"test_input": ("function_dnsdomainis", "https://www.mbusa.com", 'DIRECT'),
                             "pac_name": "Automation_Created_Pac_dnsDomainIs",
                 "private_ip_bypass": False,
                 "dns_resolution": False,
                 "plain_hostname_rule_check": False,
                 "ftp_bypass": False,
                 "zpa_synthetic_range_bypass": False,
                 "trust_url_bypass": False,
                 "custom_default_return_statement_flag": True,
                 "custom_default_return_statement": "PROXY *************:443; PROXY ***************:443; DIRECT",
                 "app_profile_pac": True,
                 "local_proxy_pac": False,
                 "variable_list": [],
                 "variable_based_rules_list": [],
                 "return_pac_url": True,
                 "obfuscate_url": True,
                 "proxy_rules_list": [[{"domain": "mbusa.com", "action": "DIRECT", "pacFunction": "dnsDomainIs"}]]},

    "function_isinnet": {"test_input": ("function_isinnet", "https://www.mbusa.com", 'DIRECT'),
                         "pac_name": "Automation_Created_Pac_isInNet",
                 "private_ip_bypass": False,
                 "dns_resolution": False,
                 "plain_hostname_rule_check": False,
                 "ftp_bypass": False,
                 "zpa_synthetic_range_bypass": False,
                 "trust_url_bypass": False,
                 "custom_default_return_statement_flag": True,
                 "custom_default_return_statement": "PROXY *************:443; PROXY ***************:443; DIRECT",
                 "app_profile_pac": True,
                 "local_proxy_pac": False,
                 "variable_list": [],
                 "variable_based_rules_list": [],
                 "return_pac_url": True,
                 "obfuscate_url": True,
                 "proxy_rules_list": [[{"ipAddress": "*************", "action": "DIRECT", "pacFunction": "isInNet",
                                        "netmask": "*************"}]]},

    "function_dnsresolve": "function FindProxyForURL(url, host) {\n\tif(dnsResolve(host) == \"***************\" || dnsResolve(host) == \"*************\" || dnsResolve(host) == \"*************\" || dnsResolve(host) == \"*************\") return \"DIRECT\";\n\treturn \"PROXY ${GATEWAY_HOST}:8800; PROXY ${SECONDARY_GATEWAY_HOST}:8800; DIRECT\";}",
}

class TestPacSemiAuto:
    def setup_class(self):
        '''
        Steps:
        1. Creating instances of required functionalities
        2. Creating Forwarding Profile and App Profile
        3. Logging out to start with a fresh environment
        4. Verifying ZIA connectivity
        '''
        self.logger_obj = conftest.logger_obj
        self.zcc = Zcc(start_app=True,log_handle=self.logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.device_overview = conftest.device_overview
        self.zia_pac = conftest.zia_pac
        self.zcc.clear_logs()

    @allure.title("Verify large bypass list in the PAC file w.r.t. ips")
    @add_markers("regression", "P1")
    @pytest.mark.parametrize("test_data", test_input_bypass_list)
    def test_large_bypass_list(self, test_data,setup_teardown):
        self.zcc.logger.info("Step 1: Deleting same name PAC if exists on ZIA")
        try:
            delete_pac = self.zia_pac.delete_pac_file(name=pac_file_rules[test_data]['pac_name'])
            self.zcc.logger.info(pac_file_rules[test_data]['pac_name'])
            assert delete_pac[0], delete_pac[1]
        except Exception as e:
            self.zcc.logger.info(str(e))
            pass

        self.zcc.logger.info("Step 2: Creating PAC file")
        function_check = pac_file_rules[test_data]["test_input"][1]
        result = conftest.zia_pac.create_custom_pac_file(pac_name=pac_file_rules[test_data]['pac_name'],
                                                         private_ip_bypass=pac_file_rules[test_data][
                                                             'private_ip_bypass'],
                                                         dns_resolution=pac_file_rules[test_data]['dns_resolution'],
                                                         plain_hostname_rule_check=pac_file_rules[test_data][
                                                             'plain_hostname_rule_check'],
                                                         ftp_bypass=pac_file_rules[test_data]['ftp_bypass'],
                                                         zpa_synthetic_range_bypass=pac_file_rules[test_data][
                                                             'zpa_synthetic_range_bypass'],
                                                         trust_url_bypass=pac_file_rules[test_data][
                                                             'trust_url_bypass'],
                                                         custom_default_return_statement_flag=
                                                         pac_file_rules[test_data][
                                                             'custom_default_return_statement_flag'],
                                                         custom_default_return_statement=pac_file_rules[test_data][
                                                             'custom_default_return_statement'],
                                                         app_profile_pac=pac_file_rules[test_data][
                                                             'app_profile_pac'],
                                                         local_proxy_pac=pac_file_rules[test_data][
                                                             'local_proxy_pac'],
                                                         proxy_rules_list=pac_file_rules[test_data][
                                                             'proxy_rules_list'],
                                                         variable_list=pac_file_rules[test_data]['variable_list'],
                                                         variable_based_rules_list=pac_file_rules[test_data][
                                                             'variable_based_rules_list'],
                                                         return_pac_url=pac_file_rules[test_data]['return_pac_url'],
                                                         obfuscate_url=pac_file_rules[test_data]['obfuscate_url'])
        assert result[0], result[1]
        self.zcc.logger.info(result[2])
        pac_url = result[2]

        self.zcc.logger.info("Step 3: Editing App Profile to add new PAC url")
        result = self.app_profile.edit_app_profile(pac=pac_url, operating_system='Android')
        time.sleep(5)
        assert result[0]

        self.zcc.logger.info("Step 4: Updating policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(3)

        self.zcc.logger.info("Step 5: Visiting the function to check")
        cmd = f"adb shell am start -a android.intent.action.VIEW -d {function_check}"
        self.sys_ops.adb_commands(cmd)
        time.sleep(5)

        self.zcc.logger.info("Step 6: Fetching and Exporting logs")
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Step 7: Searching log file for proper Proxy Action")
        proxy_action = pac_file_rules[test_data]["test_input"][2]
        files_to_search = log_file_helper()
        log_found_check = False
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[f'uri={function_check} Proxy={proxy_action}'],
                                 directory=conftest.const_obj.LOG_PATH)

            if result[0]:
                log_found_check = True
                break

        assert log_found_check

        self.zcc.logger.info("Step 8: Deleting PAC file")
        delete_pac = self.zia_pac.delete_pac_file(name=pac_file_rules[test_data]['pac_name'])
        assert delete_pac[0], delete_pac[1]

    @allure.title("Verify pac function {} with hardcoded domain, special character, ip, variable, random text")
    @add_markers("regression", "P1")
    @pytest.mark.parametrize("test_data", test_input_functions)
    def test_pac_function(self, test_data,setup_teardown):
        self.zcc.logger.info("Step 1: Deleting same name PAC if exists on ZIA")
        try:
            delete_pac = self.zia_pac.delete_pac_file(name=pac_file_rules[test_data]['pac_name'])
            self.zcc.logger.info(pac_file_rules[test_data]['pac_name'])
            assert delete_pac[0], delete_pac[1]
        except Exception as e:
            self.zcc.logger.info(str(e))
            pass

        self.zcc.logger.info("Step 2: Creating PAC file")
        function_check = pac_file_rules[test_data]["test_input"][1]
        result = conftest.zia_pac.create_custom_pac_file(pac_name=pac_file_rules[test_data]['pac_name'],
                                                         private_ip_bypass=pac_file_rules[test_data][
                                                             'private_ip_bypass'],
                                                         dns_resolution=pac_file_rules[test_data][
                                                             'dns_resolution'],
                                                         plain_hostname_rule_check=
                                                         pac_file_rules[test_data][
                                                             'plain_hostname_rule_check'],
                                                         ftp_bypass=pac_file_rules[test_data]['ftp_bypass'],
                                                         zpa_synthetic_range_bypass=
                                                         pac_file_rules[test_data][
                                                             'zpa_synthetic_range_bypass'],
                                                         trust_url_bypass=pac_file_rules[test_data][
                                                             'trust_url_bypass'],
                                                         custom_default_return_statement_flag=
                                                         pac_file_rules[test_data][
                                                             'custom_default_return_statement_flag'],
                                                         custom_default_return_statement=
                                                         pac_file_rules[test_data][
                                                             'custom_default_return_statement'],
                                                         app_profile_pac=pac_file_rules[test_data][
                                                             'app_profile_pac'],
                                                         local_proxy_pac=pac_file_rules[test_data][
                                                             'local_proxy_pac'],
                                                         proxy_rules_list=pac_file_rules[test_data][
                                                             'proxy_rules_list'],
                                                         variable_list=pac_file_rules[test_data][
                                                             'variable_list'],
                                                         variable_based_rules_list=
                                                         pac_file_rules[test_data][
                                                             'variable_based_rules_list'],
                                                         return_pac_url=pac_file_rules[test_data][
                                                             'return_pac_url'],
                                                         obfuscate_url=pac_file_rules[test_data][
                                                             'obfuscate_url'])
        assert result[0], result[1]
        self.zcc.logger.info(result[2])
        pac_url = result[2]

        self.zcc.logger.info("Step 3: Editing App Profile to add new PAC url")
        result = self.app_profile.edit_app_profile(pac=pac_url, operating_system='Android')
        time.sleep(5)
        assert result[0]

        self.zcc.logger.info("Step 4: Updating policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(3)

        self.zcc.logger.info("Step 5: Visiting the testing url")
        cmd = f"adb shell am start -a android.intent.action.VIEW -d {function_check}"
        self.sys_ops.adb_commands(cmd)
        time.sleep(7)

        self.zcc.logger.info("Step 6: Fetching and Exporting logs")
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Step 7: Searching log file for proper Proxy Action")
        proxy_action = pac_file_rules[test_data]["test_input"][2]
        files_to_search = log_file_helper()
        log_found_check = False
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[f'uri={function_check} Proxy={proxy_action}'],
                                 directory=conftest.const_obj.LOG_PATH)

            if result[0]:
                log_found_check = True
                break
        assert log_found_check

        self.zcc.logger.info("Step 8: Deleting PAC File")
        delete_pac = self.zia_pac.delete_pac_file(name=pac_file_rules[test_data]['pac_name'])
        assert delete_pac[0], delete_pac[1]

    @allure.title("Verify pac function dnsResolve with hardcoded domain, special character, ip, variable, random text")
    @pytest.mark.xray("QA-105261")
    @add_markers("regression", "P1")
    # Writing separate TC for dnsResolve in order to use it as a dependency due to limitations in direct use
    def test_dnsResolve(self, setup_teardown):
        function_check = ["https://example.com", "function_dnsresolve", "DIRECT"]
        testing_function = function_check[1]

        self.zcc.logger.info("Step 1: Deleting same name PAC if exists on ZIA")
        try:
            delete_pac = self.zia_pac.delete_pac_file(name="testing_{}".format(testing_function))
            self.zcc.logger.info("testing_{}".format(testing_function))
            assert delete_pac[0], delete_pac[1]
        except Exception as e:
            self.zcc.logger.info(str(e))
            pass

        self.zcc.logger.info("Step 2: Creating PAC file")
        result = conftest.zia_pac.create_pac_file(name="testing_{}".format(testing_function),
                                                  pac_content=pac_file_rules[testing_function])
        assert result[0], result[1]
        self.zcc.logger.info(result[2])
        pac_url = result[2]['pacUrl']

        self.zcc.logger.info("Step 3: Editing App Profile to add new PAC url")
        result = self.app_profile.edit_app_profile(pac=pac_url, operating_system='Android')
        time.sleep(5)
        assert result[0]

        self.zcc.logger.info("Step 4: Updating policy")
        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(3)

        self.zcc.logger.info("Step 5: Visiting the testing url")
        cmd = f"adb shell am start -a android.intent.action.VIEW -d {function_check[0]}"
        self.sys_ops.adb_commands(cmd)
        time.sleep(7)

        self.zcc.logger.info("Step 6: Fetching and Exporting logs")
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Step 7: Searching log file for proper Proxy Action")
        proxy_action = function_check[2]
        files_to_search = log_file_helper()
        log_found_check = False
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[f'uri={function_check[0]} Proxy={proxy_action}'],
                                 directory=conftest.const_obj.LOG_PATH)

            if result[0]:
                log_found_check = True
                break
        assert log_found_check

        self.zcc.logger.info("Step 8: Deleting PAC File")
        delete_pac = self.zia_pac.delete_pac_file(name="testing_{}".format(testing_function))
        assert delete_pac[0], delete_pac[1]


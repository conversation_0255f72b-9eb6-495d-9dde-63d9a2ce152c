import time
import pytest,allure
from OS_Android.test_suite.zcc_migration.mix_appinfo_report import conftest
from common_lib.Custom_Markers import add_markers

from OS_Android.library.ui_zcc import Zcc

class TestCloudNameSuite:

    @allure.title("Setup Method")
    def setup_class(self):
        self.logger_obj = conftest.logger_obj
        self.zcc = Zcc(start_app=True,log_handle=self.logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.device_overview = conftest.device_overview
        self.trusted_network = conftest.trusted_network

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            result = self.zcc.clear_logs()
            assert result[0], result[1]
            result = self.zcc.logout()
            assert result[0], result[1]

    @allure.title("Maximum cloud name limit is 20 letters")
    @pytest.mark.xray("QA-81204")
    @add_markers("regression", "P1")
    def test_verify_max_cloud_name_char(self):
        self.zcc.logger.info("Giving adhoc cloud name more than 20 charcter")
        result = self.zcc.adhoc_cloud_name_max_char('zscalerbetaaaaaaaaaaaaaaaaaaaaaaaaa')
        assert result[0], result[1]

    @allure.title("Adhoc field should be cleared after logout")
    @pytest.mark.xray(["QA-81206","QA-156048"])
    @add_markers("regression","P0")
    def test_verify_logout_clear_adhoc_cloud_name(self):

        self.zcc.logger.info("Giving adhoc cloud name as f{conftest.variables['ADHOC_CLOUD_NAME']} which validate")
        self.zcc.enter_adhoc_cloud_name(conftest.variables["ADHOC_CLOUD_NAME"])

        self.zcc.logger.info("Verify adhoc cloud name is correctly configured")
        result = self.zcc.verify_adhoc_cloud_name(conftest.variables["ADHOC_CLOUD_NAME"])
        assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(20)

        self.zcc.logger.info("Verify status for ZIA is on")
        status = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert status

        self.zcc.logger.info("Logout from zcc")
        self.zcc.logout()

        time.sleep(6)
        self.zcc.logger.info("Verify adhoc cloud name is cleared after logout")
        result = self.zcc.verify_adhoc_cloud_name()
        assert result[0], result[1]

    @allure.title("Empty cloud name should be allowed to saved")
    @pytest.mark.xray("QA-81208")
    @add_markers("regression", "P1")
    def test_verify_cloud_name_empty(self):
        self.zcc.logger.info("Giving adhoc cloud name containing special char")
        result = self.zcc.empty_adhoc_cloud_name()
        assert result[0], result[1]

    @allure.title("Check login with valid cloud name")
    @pytest.mark.xray("QA-81210")
    @add_markers("regression", "P0")
    def test_valid_cloud_name(self):

        self.zcc.logger.info("Giving adhoc cloud name as f{conftest.variables['ADHOC_CLOUD_NAME']} which validate")
        self.zcc.enter_adhoc_cloud_name(conftest.variables["ADHOC_CLOUD_NAME"])

        self.zcc.logger.info("Verify adhoc cloud name is correctly configured")
        result = self.zcc.verify_adhoc_cloud_name(conftest.variables["ADHOC_CLOUD_NAME"])
        assert result[0], result[1]

        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(20)

        self.zcc.logger.info("Verify status for ZIA is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')

        assert result[0], result[1]

        result = self.zcc.export_logs()
        assert result[0], result[1]
        result = self.export_logs.read_export_log_mail()
        assert result[0], result[1]

        self.zcc.logger.info("Verifying cloud name from appinfo file")
        self.log_obj.search_log_file(file="AppInfo", words_to_find_in_line=[f"CloudName : {conftest.variables['ADHOC_CLOUD_NAME']}"], directory=self.const_obj.LOG_PATH)

        result = self.zcc.logout()
        assert result[0], result[1]

        result = self.zcc.validate_zcc_logged_in()
        assert result[0] is not True, result[1]

    @allure.title("Verify Special characters are not allowed in cloudname")
    @pytest.mark.xray("QA-81214")
    @add_markers("regression","P0")
    def test_verify_cloud_name_special_char(self):
        self.zcc.logger.info("Giving adhoc cloud name containing special char")
        result = self.zcc.adhoc_cloud_name_special_char('zscalerbeta*')
        assert result[0], result[1]

    @allure.title("On clearing the cloudname, it should be reset Enter cloud name (placeholder value)")
    @pytest.mark.xray("QA-81216")
    @add_markers("regression", "P0")
    def test_reset_cloud_name(self):
        self.zcc.logger.info("Giving adhoc cloud name as f{conftest.variables['ADHOC_CLOUD_NAME']} which validate")
        self.zcc.enter_adhoc_cloud_name(conftest.variables["ADHOC_CLOUD_NAME"])

        self.zcc.logger.info("Verify adhoc cloud name is correctly configured")
        result = self.zcc.verify_adhoc_cloud_name(conftest.variables["ADHOC_CLOUD_NAME"])
        assert result[0], result[1]

        self.zcc.logger.info("Giving adhoc cloud name containing special char")
        result = self.zcc.verify_adhoc_cloud_name(conftest.variables["ADHOC_CLOUD_NAME"])
        assert result[0], result[1]

        self.zcc.logger.info("Clear adhoc cloud name and check place holder")
        self.zcc.clear_adhoc_cloud_name(conftest.variables["ADHOC_CLOUD_NAME"])

        result = self.zcc.verify_adhoc_cloud_name('')
        assert result[0], result[1]

    @allure.title('Verify adhoc cloud should accept "mod.z" as prefix')
    @pytest.mark.xray("QA-156058")
    @add_markers("regression", "P0")
    def test_verify_cloud_zname(self):
        self.zcc.logger.info("Giving adhoc cloud name as f{conftest.variables['ADHOC_CLOUD_NAME']} which validate")
        self.zcc.enter_adhoc_cloud_name('mod.z')
        result = self.zcc.verify_adhoc_cloud_name('mod.z')
        assert result[0], result[1]

        self.zcc.clear_adhoc_cloud_name('mod.z')

        result = self.zcc.verify_adhoc_cloud_name('')
        assert result[0], result[1]

    @allure.title("teardown method")
    def teardown_class(self):

        logged_in = self.zcc.validate_zcc_logged_in()
        if logged_in[0]:
            self.logger_obj.info("Clear logs from zcc app")
            result = self.zcc.clear_logs()
            assert result[0], result[1]

            self.logger_obj.info("Logout from zcc")
            result = self.zcc.logout()
            assert result[0], result[1]




















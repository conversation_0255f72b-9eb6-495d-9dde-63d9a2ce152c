import json,os
import pytest
from common_lib.mobileadmin.deviceoverview import DeviceOverview
from common_lib.adminzia.admingroupuser import AdminGroupUser
from common_lib.mobileadmin.appprofile import AppProfile
from common_lib.mobileadmin.forwardingprofile import ForwardingProfile
from common_lib.adminzia.ziahelper import *
from OS_Android.library.ui_zcc import Zcc
from common_lib.common import logger,constants
from common_lib.common.log_ops import *
from OS_Android.library.ops_system import SysOps
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
from common_lib.mobileadmin.trustednetwork import TrustedNetwork
from common_lib.mobileadmin.serviceentitlement import ServiceEntitlement

from common_lib.mobileadmin.clientconnectorsupport import ClientConnectorSupport
from common_lib.adminzpa.zpapolicy import ZpaPolicy


config_path = os.path.join(os.getcwd(),"config", os.environ.get("CONFIG_FILE"))

with open(os.path.join(os.getcwd(), config_path)) as json_file:
    variables = json.load(json_file)

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")


device_overview=DeviceOverview(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)

const_obj=constants.Utils()

sys_ops=SysOps(logger_obj,variables["CLOUD"])

export_logs=FetchAndroidLogs(logger_obj)

create_zia_user=AdminGroupUser(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)

zia_activate=ZiaHelper(variables["CLOUD"],os.environ.get("CONFIG_FILE"),log_handle=logger_obj)

log_ops=LogOps(log_handle=logger_obj)

pcap_ops=PcapOps(log_handle=logger_obj)


app_profile_obj = AppProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
forwarding_profile_obj=ForwardingProfile(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
trusted_network = TrustedNetwork(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
service_ent = ServiceEntitlement(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
client_conn_support = ClientConnectorSupport(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
zpa = ZpaPolicy(cloud=variables["CLOUD"],config=config_path,log_handle=logger_obj)
if variables["ZPA_USER_ID"] and variables["ZPA_ADMIN_PASSWORD"]:
    pass


@pytest.fixture(scope="package",autouse=True )
def setup_teardown():
    app_profile = app_profile_obj
    forwarding_profile = forwarding_profile_obj
    zcc = Zcc(start_app=True,log_handle=logger_obj)

    try:
        result = app_profile.delete_app_profile(operating_system="Android")
        assert result[0], result[1]
        result = forwarding_profile.delete_forwarding_profile()
        assert result[0], result[1]
    except Exception as e:
        zcc.logger.info(str(e))
        pass

    result = forwarding_profile.create_forwarding_profile()
    assert result[0], result[1]

    result = app_profile.create_app_profile(operating_system="Android")
    assert result[0], result[1]

    # result = sys_ops.toggle_mobile_data(enable=True)
    # assert result[0], result[1]
    #
    # result = sys_ops.toggle_wifi(disable=True)
    # assert result[0], result[1]

    result = client_conn_support.toggle_pre_populate_client_connector_username(using_javascript=False, using_login_hint=False)
    assert result[0], result[1]

    logged_in = zcc.validate_zcc_logged_in()
    if logged_in[0]:
        zcc.logger.info("Clear logs from zcc app")
        result = zcc.clear_logs()
        assert result[0], result[1]

        zcc.logger.info("Logout from zcc")
        result = zcc.logout()
        assert result[0], result[1]

    result = zcc.login(variables["ZIA_USER_ID"], variables["ZIA_USER_PASSWORD"],variables["ZPA_USER_ID"],variables["ZPA_USER_PASSWORD"])
    assert result[0], result[1]

    time.sleep(5)

    result = zcc.verify_service_status(service_status='on', service_type='ZIA')
    assert result[0], result[1]

    result = zcc.verify_service_status(service_status='on', service_type='ZPA')
    assert result[0], result[1]

    yield

    zcc.logger.info("Deleting App profile and forwarding profile")
    try:
        result = app_profile.delete_app_profile(operating_system="Android")
        assert result[0], result[1]
        result = forwarding_profile.delete_forwarding_profile()
        assert result[0], result[1]
    except Exception as e:
        zcc.logger.info(str(e))

    logged_in = zcc.validate_zcc_logged_in()
    if logged_in[0]:
        zcc.logger.info("Clear logs from zcc app")
        result = zcc.clear_logs()
        assert result[0], result[1]

        zcc.logger.info("Logout from zcc")
        result = zcc.logout()
        assert result[0], result[1]




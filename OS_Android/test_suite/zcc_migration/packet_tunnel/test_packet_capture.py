import time
import pytest,allure
from OS_Android.test_suite.zcc_migration.mix_appinfo_report import conftest
from common_lib.Custom_Markers import add_markers
from common_lib.common.log_ops import *
from OS_Android.library.ui_zcc import Zcc

logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log",log_level="DEBUG")


class TestPacketCapture:
    @allure.title("Setup Method")
    def setup_class(self):
        self.zcc = Zcc(start_app=True,log_handle=logger_obj)
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.log_obj = conftest.log_ops
        self.pcap_obj = conftest.pcap_ops
        self.sys_ops = conftest.sys_ops
        self.const_obj = conftest.const_obj
        self.export_logs = conftest.export_logs
        self.device_overview = conftest.device_overview
        self.client_conn_support = conftest.client_conn_support
        self.service_ent = conftest.service_ent
        self.trusted_nertwork = conftest.trusted_network

        result = self.zcc.verify_service_status(service_status='on', service_type='ZDX')
        assert result[0], result[1]

        self.zcc.logger.info("Start packet capture from zcc app")
        result = self.client_conn_support.toggle_packet_capture(action="enable")
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]


    @allure.title("Validate packet capture on network switching with other modes to tunnel")
    @pytest.mark.xray("QA-105147", "QA-126898")
    @add_markers("regression", "P1")
    def test_packet_capture_trusted_nw(self):
        self.zcc.logger.info("Create trusted network with dns server creteria")
        result = self.trusted_nertwork.create_trusted_network(condition='Any', dns_servers_list=['192.168.1.1'])
        assert result[0], result[1]

        self.zcc.logger.info("edit forward profile with dns server base  predefined trusted network ")
        result = self.forwarding_profile.edit_forwarding_profile(tunnel_mode="none",Trusted_Network="predefined",)
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Verify status for ZIA is Disable")
        result = self.zcc.verify_service_status(service_status='disable', service_type='ZIA')
        assert result[0], result[1]

        self.zcc.logger.info("Start packet capture from zcc app")
        self.zcc.packet_capture(enable=True)

        self.zcc.logger.info("Stop packet capture from zcc app and verify packet capture is off")
        self.zcc.packet_capture(disable=True)

        self.zcc.logger.info("Validate packet capture is knob is present")
        self.zcc.packet_capture_present()

        self.zcc.logger.info("Delete trusted network for MA")
        result = self.trusted_nertwork.delete_trusted_network()
        assert result[0], result[1]

    @allure.title("Verify packet capture on turning off web security")
    @pytest.mark.xray("QA-105135")
    @add_markers("regression", "P0")
    def test_off_web_security_pck_cap(self):

        result = self.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Verify status for ZIA is on")
        status = self.zcc.verify_service_status(service_type='ZIA', service_status='on')
        assert status

        result = self.zcc.toggle_service(service='ZIA', action=False)
        assert result[0], result[1]

        self.zcc.logger.info("Verify status for ZIA is off")
        status = self.zcc.verify_service_status(service_status='off', service_type='ZIA')
        assert status

        self.zcc.logger.info("Start packet capture from zcc app and verify packet capture is on")
        result = self.zcc.packet_capture(action='start')
        assert result[0], result[1]

        self.zcc.logger.info("Stop packet capture from zcc app and verify packet capture is off")
        result = self.zcc.packet_capture(action='stop')
        assert result[0], result[1]

        result = self.zcc.toggle_service(service='ZIA', action=True)
        assert result[0], result[1]

        self.zcc.logger.info("Verify status for ZIA is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

    @allure.title("Verify packet capture after turning on web security with tunnel mode")
    @pytest.mark.xray("QA-105136")
    @add_markers("regression", "P0")
    def test_off_on_pck_cap(self):

        result = self.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Verify status for ZIA is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]


        self.zcc.toggle_service(service='ZIA', action=False)
        self.zcc.logger.info("Verify status for ZIA is off")
        result = self.zcc.verify_service_status(service_status='off', service_type='ZIA')
        assert result[0], result[1]

        result = self.zcc.toggle_service(service='ZIA', action=True)
        assert result[0], result[1]

        self.zcc.logger.info("Verify status for ZIA is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        self.zcc.logger.info("Start packet capture from zcc app and verify packet capture is on")
        result = self.zcc.packet_capture(action='start')
        assert result[0], result[1]

        self.zcc.logger.info("Stop packet capture from zcc app and verify packet capture is off")
        result = self.zcc.packet_capture(action='stop')
        assert result[0], result[1]

        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]


    @allure.title("Validate packet capture knob for tunnel mode")
    @pytest.mark.xray("QA-105145")
    @add_markers("regression", "P1")
    def test_packet_capture_knob_tunnel(self):
        result = self.forwarding_profile.edit_forwarding_profile(tunnel_mode="tunnel_1")
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Verify status for ZIA is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        self.zcc.logger.info("Validate packet capture is knob is present")
        result = self.zcc.packet_capture_present()
        assert result[0], result[1]

        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

    @allure.title("Verify that Packets can be captured properly using ZAPP's built-in feature")
    @pytest.mark.xray("QA-105152")
    @add_markers("regression", "P1")
    def test_packet_capture_built_in(self):

        result = self.forwarding_profile.edit_forwarding_profile(tunnel_mode='tunnel_1')
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Verify status for ZIA is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        self.zcc.logger.info("Start packet capture from zcc app and verify packet capture is on")
        result = self.zcc.packet_capture(action='start')
        assert result[0], result[1]

        self.zcc.logger.info("Stop packet capture from zcc app and verify packet capture is off")
        result = self.zcc.packet_capture(action='stop')
        assert result[0], result[1]

        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

    @allure.title("Validate packet capture with zapp in the none mode")
    @pytest.mark.xray("QA-105148")
    @add_markers("regression", "P1")
    def test_packet_capture_none_mode(self):

        result = self.forwarding_profile.edit_forwarding_profile(tunnel_mode='none')
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Start packet capture from zcc app and verify packet capture is on")
        result = self.zcc.packet_capture(action='start')
        assert result[0], result[1]

        self.zcc.logger.info("Stop packet capture from zcc app and verify packet capture is off")
        result = self.zcc.packet_capture(action='stop')
        assert result[0], result[1]

        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

    @allure.title("Validate status change on tray on stopping packet capture")
    @pytest.mark.xray("QA-105144")
    @add_markers("regression", "P0")
    def test_packet_capture_default_status(self):
        result = self.forwarding_profile.edit_forwarding_profile(tunnel_mode='tunnel_1')
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Verify status for ZIA is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        self.zcc.logger.info("Validate packet capture is off by default")
        result = self.zcc.packet_knob_off()
        assert result[0], result[1]

        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

    @allure.title("Validate automatic stop of packet capture is 5 min")
    @pytest.mark.xray("QA-105139")
    @add_markers("regression", "P1")
    def test_packet_capture_default_stop(self):

        self.zcc.logger.info("Verify status for ZIA is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        self.zcc.logger.info("Start packet capture from zcc app")
        self.zcc.packet_capture(action='start')
        time.sleep(305)

        self.zcc.no_packet_capture_notification()
        assert result[0], result[1]

        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

    @allure.title("Validate status change on starting packet capture.")
    @add_markers("regression", "P0")
    @pytest.mark.xray("QA-147250")
    def test_status_change_on_capture(self):
        self.zcc.logger.info("Validate packet capture is off by default")
        result = self.zcc.packet_knob_off()
        assert result[0], result[1]

        self.zcc.logger.info("Start packet capture from zcc app")
        result = self.zcc.packet_capture(action='start')
        assert result[0], result[1]

        self.zcc.logger.info("Validate packet capture is off by default")
        result = self.zcc.packet_knob_on()
        assert result[0], result[1]


    @allure.title("Validate the policy update to disable packet capture if the user is already running a packet capture")
    @pytest.mark.xray("QA-105150")
    @add_markers("regression", "P1")
    def test_packet_capture_policy_update(self):
        result = self.forwarding_profile.edit_forwarding_profile(tunnel_mode='tunnel_1')
        assert result[0], result[1]

        result = self.zcc.update_policy()

        self.zcc.logger.info("Verify status for ZIA is on")
        result = self.zcc.verify_service_status(service_status='on', service_type='ZIA')
        assert result[0], result[1]

        self.zcc.logger.info("Start packet capture from zcc app")
        result = self.zcc.packet_capture(action='start')
        assert result[0], result[1]

        self.client_conn_support.toggle_packet_capture(action='disable')

        self.zcc.logger.info("Update policy on ZCC")
        self.zcc.update_policy()

        self.zcc.logger.info("verify packet capture knob is not present")
        result = self.zcc.packet_capture_not_present()
        assert result[0], result[1]

        result = self.client_conn_support.toggle_packet_capture(action='enable')
        assert result[0], result[1]

        self.zcc.logger.info("Update policy on ZCC")
        self.zcc.update_policy()

        self.zcc.logger.info("Clear logs from zcc app")
        result = self.zcc.clear_logs()
        assert result[0], result[1]







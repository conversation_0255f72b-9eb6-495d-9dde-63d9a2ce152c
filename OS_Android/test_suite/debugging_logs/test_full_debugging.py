import allure, time, copy
import pytest

import conftest
from OS_Android.library.log_file_helper_function import *
from common_lib.helper_function import *
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers
from selenium.webdriver.support import expected_conditions as EC
from common_lib.common.common_ui import *
from common_lib.common.log_ops import *
from OS_Android.library.ui_zcc import Zcc
logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log", log_level="DEBUG")
class TestFullDebugging:
    @allure.title("Setup method")
    def setup_class(self):
        '''
        Steps:
        1. Creating instances of required functionalities
        2. Logging out to start with a fresh environment
        3. Creating Forwarding Profile and App Profile
        4. Verifying ZIA connectivity
        5. Fetching deviceId from MA for use in TCs
        '''

        self.zcc = Zcc(start_app=True, log_handle=logger_obj)
        self.log_obj = conftest.log_ops
        self.sys_ops = conftest.sys_ops
        self.export_logs = conftest.export_logs
        self.elements = copy.deepcopy(AndroidElements)

        self.device_id = conftest.device_overview.fetch_enrolled_devices_details(get_device_id=True,
                                                                                 get_registration_status=True)


    @pytest.mark.xray("QA-256307")
    @allure.title("Validate Remote Fetch logs have the logs for specific time interval")
    @add_markers("regression")
    def test_remote_fetch_logs(self):

        self.zcc.logger.info("Step 1: Clearing logs in order to avoid collision with older logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Step 2: Navigating to More and selecting log mode to Debug")
        self.zcc.ui.click(path=self.elements.LOG_MODE_LIST, AEFC="Dropdown not clicked")
        time.sleep(1)
        self.zcc.ui.click(path=self.elements.LOG_MODE_DEBUG_XPATH, AEFC="Debug mode not clicked")
        time.sleep(1)

        self.zcc.logger.info("Step 3: Initiating Remote Fetch logs from MA")
        result = conftest.device_overview.remote_fetch_logs(enrolled_device_id=self.device_id[2])
        assert result[0], result[1]
        time.sleep(5)
        self.zcc.logger.info("Step 4: Updating policy twice to skip keep alive and extraction time")
        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(5)
        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(5)
        self.zcc.logger.info("Step 5: Verifying that logs have been fetched successfully")
        log_status = conftest.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert log_status[0]
        self.zcc.logger.info(log_status[2]['logFetchInfo'])

        if log_status[2]['logFetchInfo']['logFetchStatus'] != "Completed":
            assert False
        else:
            assert True

        self.zcc.logger.info("Step 6: Fetching and exporting logs from android device")
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Step 7: Searching the logs in the corresponding log files")
        files_to_search = log_file_helper()
        self.zcc.logger.info(files_to_search)

        to_search = "VER LogFetchManager:VerboseLog was enabled for exporting ZCC Logs"

        log_found_check = False
        verbose_lines = []

        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[to_search],
                                 directory=conftest.const_obj.LOG_PATH)

        assert result[0], result[1]

        to_search = "VER LogFetchManager:Disabling verbose logs"
        log_found_check = False
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[to_search],
                                 directory=conftest.const_obj.LOG_PATH)
        assert result[0], result[1]

    @pytest.mark.xray("QA-256306")
    @allure.title("Validate Report an issue have the logs for specific time interval")
    @add_markers("regression")
    def test_report_an_issue_logs(self):
        self.zcc.logger.info("Step 1: Clearing logs in order to avoid collision with older logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Step 2: Navigating to More and selecting log mode to Debug")
        self.zcc.ui.click(path=self.elements.LOG_MODE_LIST, AEFC="Dropdown not clicked")
        time.sleep(1)
        self.zcc.ui.click(path=self.elements.LOG_MODE_DEBUG_XPATH, AEFC="Debug mode not clicked")
        time.sleep(1)

        self.zcc.logger.info("Step 3: Reporting the issue")
        result = self.zcc.report_issue(cc_email="<EMAIL>")
        assert result

        self.zcc.logger.info("Step 4: Fetching and exporting logs from android device")
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Step 5: Searching the logs in the corresponding log files")
        files_to_search = log_file_helper()
        self.zcc.logger.info(files_to_search)

        to_search = "VER ReportIssueActivity:VerboseLog was enabled for exporting ZCC Logs"
        log_found_check = False
        verbose_lines = []
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[to_search],
                                 directory=conftest.const_obj.LOG_PATH)
        assert result[0], result[1]

        to_search = "VER ReportIssueActivity:Disabling verbose logs"
        log_found_check = False
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[to_search],
                                 directory=conftest.const_obj.LOG_PATH)
        assert result[0], result[1]

    @pytest.mark.xray("QA-256308")
    @allure.title("Validate that if full debugging is enabled during export logs it should be disabled as soon as it finishes up")
    @add_markers("regression")
    def test_export_logs_debugging_logs(self):
        self.zcc.logger.info("Step 1: Clearing logs in order to avoid collision with older logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Step 2: Exporting logs via Export logs button in ZApp")
        self.zcc.ui.click(path=self.elements.NOTIFICATION, AEFC="More tab option not clicked")
        time.sleep(1)
        self.zcc.ui.click(path=self.elements.EXPORT_LOGS, AEFC="Export logs button not clicked")
        time.sleep(2)
        self.zcc.app_back()

        self.zcc.logger.info("Step 3: Fetching and exporting logs from android device")
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Step 4: Searching the logs in the corresponding log files")
        files_to_search = log_file_helper()
        self.zcc.logger.info(files_to_search)

        to_search = "BaseActivity:VerboseLog was enabled for exporting ZCC Logs"
        log_found_check = False
        verbose_lines = []
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[to_search],
                                 directory=conftest.const_obj.LOG_PATH)
        assert result[0], result[1]

        to_search = "BaseActivity:Disabling verbose logs"
        log_found_check = False
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[to_search],
                                 directory=conftest.const_obj.LOG_PATH)
        assert result[0], result[1]

    @pytest.mark.xray("QA-256309")
    @allure.title("Validate that if full debugging is enabled during Report an Issue it should be disabled as soon as it finishes up")
    @add_markers("regression")
    def test_report_issue_debugging_logs(self):
        self.zcc.logger.info("Step 1: Clearing logs in order to avoid collision with older logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Step 2: Reporting the issue")
        result = self.zcc.report_issue(cc_email="<EMAIL>")
        assert result

        self.zcc.logger.info("Step 3: Fetching and exporting logs from android device")
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Step 4: Searching the logs in the corresponding log files")
        files_to_search = log_file_helper()
        self.zcc.logger.info(files_to_search)

        to_search = "BaseActivity:VerboseLog was enabled for exporting ZCC Logs"
        log_found_check = False
        verbose_lines = []
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[to_search],
                                 directory=conftest.const_obj.LOG_PATH)
        assert result[0], result[1]

        to_search = "BaseActivity:Disabling verbose logs"
        log_found_check = False
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[to_search],
                                 directory=conftest.const_obj.LOG_PATH)
        assert result[0], result[1]

    @pytest.mark.xray("QA-256310")
    @allure.title("Validate that if full debugging is enabled during Remote fetch logs it should be disabled as soon as it finishes up")
    @add_markers("regression")
    def test_remote_fetch_debugging_logs(self):
        self.zcc.logger.info("Step 1: Clearing logs in order to avoid collision with older logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Step 2: Initiating Remote Fetch logs process from MA")
        result = conftest.device_overview.remote_fetch_logs(enrolled_device_id=self.device_id[2])
        assert result[0], result[1]

        log_status = conftest.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert log_status[0]
        self.zcc.logger.info(log_status[2]['logFetchInfo'])

        self.zcc.logger.info("Step 3: Exporting logs via export logs button in ZApp")
        self.zcc.ui.click(path=self.elements.NOTIFICATION, AEFC="More tab option not clicked")
        time.sleep(1)
        self.zcc.ui.click(path=self.elements.EXPORT_LOGS, AEFC="Export logs button not clicked")
        time.sleep(2)
        self.zcc.app_back()

        self.zcc.logger.info("Step 4: Fetching and exporting logs from android device")
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Step 5: Searching the logs in the corresponding log files")
        files_to_search = log_file_helper()
        self.zcc.logger.info(files_to_search)

        to_search = "BaseActivity:VerboseLog was enabled for exporting ZCC Logs"
        log_found_check = False
        verbose_lines = []
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[to_search],
                                 directory=conftest.const_obj.LOG_PATH)
        assert result[0], result[1]


        to_search = "BaseActivity:Disabling verbose logs"
        log_found_check = False
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[to_search],
                                 directory=conftest.const_obj.LOG_PATH)
        assert result[0], result[1]

    @pytest.mark.xray("QA-256311")
    @allure.title("Validate that there should not be any error in MA while remote fetch logs (error while fetching logs)")
    @add_markers("regression")
    def test_error_on_remote_fetch_logs(self):
        self.zcc.logger.info("Step 1: Clearing logs in order to avoid collision with older logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Step 2: Remote Fetch logs from MA")
        result = conftest.device_overview.remote_fetch_logs(enrolled_device_id=self.device_id[2])
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(5)

        self.zcc.logger.info("Step 3: Verifying log status of Remote Fetch log process")
        log_status = conftest.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert log_status[0]
        self.zcc.logger.info(log_status[2]['logFetchInfo'])

        if log_status[2]['logFetchInfo'] == "Error while fetching logs":
            assert False
        else:
            assert True

    @pytest.mark.xray("QA-256312")
    @allure.title("Validate the export logs and Remote Fetch logs in parallel")
    @add_markers("regression")
    def test_export_logs_and_remote_fetch_parallel(self):
        self.zcc.logger.info("Step 1: Clearing logs in order to avoid collision with older logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Step 2: Exporting logs via Export Logs button in ZApp")
        self.zcc.ui.click(path=self.elements.NOTIFICATION, AEFC="More tab option not clicked")
        time.sleep(1)
        self.zcc.ui.click(path=self.elements.EXPORT_LOGS, AEFC="Export logs button not clicked")
        time.sleep(2)
        self.zcc.app_back()

        self.zcc.logger.info("Step 3: Resuming the ZApp after export logs event")
        self.sys_ops.adb_commands("adb shell input keyevent KEYCODE_HOME")
        time.sleep(2)
        self.sys_ops.adb_commands("adb shell am start zscaler.com.zscaler/com.zscaler.activities.MainActivity")
        time.sleep(2)

        self.zcc.logger.info("Step 4: Remote Fetch Logs from MA")
        result = conftest.device_overview.remote_fetch_logs(enrolled_device_id=self.device_id[2])
        assert result[0], result[1]

        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(5)
        result = self.zcc.update_policy()
        assert result[0], result[1]

        self.zcc.logger.info("Step 5: Verifying the log status of Remote Fetch log process")
        log_status = conftest.device_overview.fetch_enrolled_devices_details(fetch_this_device_details=True)
        assert log_status[0]
        self.zcc.logger.info(log_status[2]['logFetchInfo'])

        if log_status[2]['logFetchInfo']['logFetchStatus'] != "Completed":
            assert False
        else:
            assert True

        self.zcc.logger.info("Step 6: Fetching and exporting logs from android device")
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Step 7: Searching the logs in the corresponding log files")
        files_to_search = log_file_helper()
        self.zcc.logger.info(files_to_search)

        to_search = "BaseActivity:VerboseLog was enabled for exporting ZCC Logs"
        log_found_check = False
        verbose_lines = []
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[to_search],
                                 directory=conftest.const_obj.LOG_PATH)
        assert result[0], result[1]

        to_search = "BaseActivity:Disabling verbose logs"
        log_found_check = False
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[to_search],
                                 directory=conftest.const_obj.LOG_PATH)
        assert result[0], result[1]

    @pytest.mark.xray("QA-256300")
    @allure.title("Validate that in ZCC_Android _Logcat file should have the proper full debugging logs")
    @add_markers("regression")
    def test_logs_in_logcat(self):
        self.zcc.logger.info("Step 1: Clearing logs in order to avoid collision with older logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Step 2: Exporting logs via Export Logs button from ZApp")
        self.zcc.ui.click(path=self.elements.NOTIFICATION, AEFC="More tab option not clicked")
        time.sleep(1)
        self.zcc.ui.click(path=self.elements.EXPORT_LOGS, AEFC="Export logs button not clicked")
        time.sleep(2)
        self.zcc.app_back()

        self.zcc.logger.info("Step 3: Fetching and exporting logs from android device")
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Step 4: Searching the logs in the corresponding log files")
        to_search = "Setting VerboseLogEnabledForExportingZCCLogsCount to : 1"
        log_found_check = False
        file = "Zscaler-Android-Logcat"
        result = help_return(self.log_obj.search_log_file, file=file,
                             words_to_find_in_line=[to_search],
                             directory=conftest.const_obj.LOG_PATH)
        assert result[0], result[1]

    @pytest.mark.xray("QA-256303")
    @allure.title("Validate that ZApp should not crash On having the full debugging logs enabled for report an issue")
    @add_markers("regression")
    def test_crash_on_full_debugging(self):
        self.zcc.logger.info("Step 1: Clearing logs in order to avoid collision with older logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Step 2: Killing the ZApp")
        result = self.zcc.kill_zcc()
        assert result[0], result[1]
        time.sleep(1)

        self.zcc.logger.info("Step 3: Restarting the ZApp and verifying ZIA status")
        result = self.zcc.ui.start_app(app=AndroidElements.APP, sleep_time=10)
        time.sleep(15)
        result = self.zcc.verify_service_status(service_type='ZIA')
        assert result[0], result[1]

        self.zcc.logger.info("Step 4: Reporting the issue process")
        result = self.zcc.report_issue(cc_email='<EMAIL>')
        assert result

        self.zcc.logger.info("Step 5: Fetching and exporting logs from android device")
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Step 6: Searching the logs in the corresponding log files")
        to_search = "fatal"
        file = "Zscaler-Android-Logcat"

        log_found_check = False
        result = help_return(self.log_obj.search_log_file, file=file,
                             words_to_find_in_line=[to_search],
                             directory=conftest.const_obj.LOG_PATH)

        assert result[0], result[1]


    @pytest.mark.xray("QA-256304")
    @allure.title("Validate that the full logs for specific interval should be there (while we have full debugging logs for export logs, there shouln't be the full debugging logs for the Report an issue)")
    @add_markers("regression")
    def test_login_logs(self):
        self.zcc.logger.info("Step 1: Clearing logs in order to avoid collision with older logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Step 2: Navigating to More and selecting log mode to Verbose")
        self.zcc.ui.click(path=self.elements.LOG_MODE_LIST, AEFC="Dropdown not clicked")
        time.sleep(1)
        self.zcc.ui.click(path=self.elements.LOG_MODE_VERBOSE_XPATH, AEFC="Verbose mode not clicked")
        time.sleep(1)

        self.zcc.logger.info("Step 3: Logging out from ZApp")
        result = self.zcc.logout()
        assert result[0], result[1]

        self.zcc.logger.info("Step 4: Logging in ZApp and verifying ZIA status")
        result = self.zcc.login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"],
                                conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        time.sleep(10)
        assert result[0], result[1]

        result = self.zcc.verify_service_status(service_type='ZIA')
        assert result[0], result[1]

        self.zcc.logger.info("Step 5: Verifying the verbose log mode")
        self.zcc.ui.click(path=self.elements.NOTIFICATION, AEFC="More tab option not clicked")
        time.sleep(1)

        text = self.zcc.ui.click(path=self.elements.LOG_MODE_LIST, do_click=False, return_text=True, AEFC="Verbose mode not visible")
        time.sleep(1)

        if text == "Verbose":
            assert True
        else:
            assert False

    @pytest.mark.xray("QA-256299")
    @allure.title("Validate that Logs for ZIA should be saved in ZCC_Android_Logs File")
    @add_markers("regression")
    def test_zia_logs(self):
        self.zcc.logger.info("Step 1: Clearing logs in order to avoid collision with older logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Step 2: Verifying ZIA status and navigating to MORE tab")
        result = self.zcc.verify_service_status()
        self.zcc.ui.click(path=self.elements.NOTIFICATION, AEFC="More tab option not clicked")
        time.sleep(1)

        self.zcc.logger.info("Step 3: Clearing all notifications from NOTIFICATION tab")
        self.zcc.ui.click("//*[@class='android.widget.TextView' and @text='Notifications']", sleep_time=1)
        try:
            self.zcc.ui.click(self.elements.NOTIFICATION_REMOVE, sleep_time=1, AEFC="Unable to click")
            self.zcc.ui.click("//*[@class='android.widget.TextView' and @resource-id='zscaler.com.zscaler:id/ok']",
                          sleep_time=1)
        except:
            self.zcc.ui.click(self.elements.REPORT_ISSUE_CLOSE)

        self.zcc.logger.info("Step 4: Restarting the service and verifying it by notification tab")
        try:
            self.zcc.ui.click(self.elements.NOTIFICATION, sleep_time=0, AEFC="Unable to click More button on ZCC")
            self.zcc.ui.click(self.elements.RESTART, sleep_time=3, AEFC="Unable to click Restart Service button on ZCC")

            self.zcc.ui.click(self.elements.YES_RESTART, sleep_time=0, AEFC="Unable to confirm ZCC Restart")

            self.zcc.ui.click(path="//*[@class='android.widget.TextView' and @text='Notifications']", sleep_time=1)
            status_out = self.zcc.ui.check_if_exists(
                "//*[@class='android.widget.TextView' and @text='Internet Security Stopped']",
                AEFC="Service not stopped after restart of service")
            time.sleep(10)
            self.zcc.ui.click(path=self.elements.WEBSECURITY, AEFC="Internet Security Not Clicked")
            time.sleep(5)
            result = self.zcc.verify_service_status()
            time.sleep(1)
            self.zcc.ui.click(path="//*[@class='android.widget.TextView' and @text='Notifications']", sleep_time=1)
            status_out = self.zcc.ui.check_if_exists(
                "//*[@class='android.widget.TextView' and @text='Internet Security Started']",
                AEFC="Service not started after restart of svc")
            self.zcc.ui.click(self.elements.WEBSECURITY, sleep_time=0, AEFC="Unable to click on Internet Security")
            WebDriverWait(self.zcc.ui.driver, 10).until(
                EC.visibility_of_element_located((By.XPATH, self.elements.ZIA_ON_STATUS)))
            if status_out:
                status = True
                msg = "restart service is successful"
        except Exception as e:
            self.zcc.logger.error("Restart Service Failed ; {}\n".format(e))
            msg = "restart service is not successful"
        self.zcc.logger.info("Success :: Restarted Service!")

        result = self.zcc.update_policy()
        assert result[0], result[1]
        time.sleep(1)

        self.zcc.logger.info("Step 5: Collecting Broker IP from the INTERNET SECURITY tab")
        self.zcc.ui.click(path=self.elements.WEBSECURITY, AEFC="Internet Security button not clicked")
        time.sleep(1)
        SME_IP = self.zcc.ui.click(path=self.elements.BROKER_IP, do_click=False, return_text=True, AEFC="Broker IP not visible")
        time.sleep(1)

        self.zcc.logger.info("Step 6: Exporting logs via Export Logs button in ZApp")
        self.zcc.ui.click(path=self.elements.NOTIFICATION, AEFC="More tab option not clicked")
        time.sleep(1)
        self.zcc.ui.click(path=self.elements.EXPORT_LOGS, AEFC="Export logs button not clicked")
        time.sleep(2)
        self.zcc.app_back()

        self.zcc.logger.info("Step 7: Fetching and exporting logs from android device")
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Step 8: Searching the logs in the corresponding log files")
        files_to_search = log_file_helper()
        self.zcc.logger.info(files_to_search)

        to_search = "SME IP="+SME_IP
        log_found_check = False
        verbose_lines = []
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[to_search],
                                 directory=conftest.const_obj.LOG_PATH)
        assert result[0], result[1]

    @pytest.mark.xray("QA-256302")
    @allure.title("Validate that Proper Logs for Authentication should be there in ZCC_Android_logs File")
    @add_markers("regression")
    def test_authentication_logs(self):
        self.zcc.logger.info("Step 1: Clearing logs in order to avoid collision with older logs")
        result = self.zcc.clear_logs()
        assert result[0], result[1]

        self.zcc.logger.info("Step 2: Logging out from ZApp")
        result = self.zcc.logout()
        assert result[0], result[1]

        self.zcc.logger.info("Step 3: Logging into ZApp")
        result = self.zcc.login(zia_user=conftest.variables["ZIA_USER_ID"], zia_password=conftest.variables["ZIA_USER_PASSWORD"],
                                zpa_user=conftest.variables["ZPA_USER_ID"], zpa_password=conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(15)

        self.zcc.logger.info("Step 4: Exporting logs via Export Logs button in ZApp")
        self.zcc.ui.click(path=self.elements.NOTIFICATION, AEFC="More tab option not clicked")
        time.sleep(1)
        self.zcc.ui.click(path=self.elements.EXPORT_LOGS, AEFC="Export logs button not clicked")
        time.sleep(2)
        self.zcc.app_back()

        self.zcc.logger.info("Step 5: Fetching and exporting logs from android device")
        self.zcc.logger.info("Fetch logs from android device")
        result = self.zcc.export_logs()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Export logs from android device")
        result = self.export_logs.read_export_log_mail()
        assert result[0]
        time.sleep(5)

        self.zcc.logger.info("Step 6: Searching the logs in the corresponding log files")
        files_to_search = log_file_helper()
        self.zcc.logger.info(files_to_search)

        to_search = "Auth::Library initializeAuthenticationLibrary complete"
        log_found_check = False
        verbose_lines = []
        for file in files_to_search['tunnel_logs']:
            result = help_return(self.log_obj.search_log_file, file=file,
                                 words_to_find_in_line=[to_search],
                                  directory=conftest.const_obj.LOG_PATH)
        assert result[0], result[1]



# debugging_logs TCs - Requirements
## Prerequisites:
    1) On MA company and user must be configured.
    2) On Android device zcc version should be installed
    3) There should not any configuration present on ZIA
    4) Must have configured a ZPA application


## Commands to Execute Automation:
**Running All Cases:** python trigger_automation.py --config <config.json> --testcases test_suite/debugging_logs

**Running One Test File from debugging_logs folder:** python trigger_automation.py --config config.json --testcases test_suite/debugging_logs/*.py

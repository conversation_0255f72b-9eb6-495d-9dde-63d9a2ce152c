import allure, time, copy
import pytest
import conftest
from OS_Android.library.log_file_helper_function import *
from common_lib.helper_function import *
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from common_lib.common.common_ui import *

class TestDeviceTokenError:
    @allure.title("Setup method")
    def setup_class(self):
        '''
        Setup Class steps:
        1. Initializing required instances
        2. Logging into ZApp using OKTA
        '''

        self.zcc = conftest.zcc
        self.log_obj = conftest.log_ops
        self.sys_ops = conftest.sys_ops
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.elements = copy.deepcopy(AndroidElements)

        self.zcc.logger.info("Doing OKTA Login")
        signin_button_xpath = '//android.widget.Button[@text="Sign in"]'
        result = self.zcc.initial_zcc_login_window(conftest.variables["ZIA_USER_ID"])
        assert result[0], result[1]
        try:
            self.zcc.explicit_wait(signin_button_xpath, sleep=30)
            self.sys_ops.adb_commands("adb shell input keyevent 61")
            self.sys_ops.adb_commands("adb shell input keycombination 113 29")
            self.sys_ops.adb_commands("adb shell input keyevent 67")
            time.sleep(1)
            cmd = "adb shell input text {}".format(conftest.variables["ZPA_USER_ID"])
            self.sys_ops.adb_commands(cmd)
            self.sys_ops.adb_commands("adb shell input keyevent 61")
            time.sleep(3)
            cmd = "adb shell input text {}".format(conftest.variables["ZPA_USER_PASSWORD"])
            self.sys_ops.adb_commands(cmd)
            self.zcc.ui.click('//android.widget.TextView[@text="Sign In"]', AEFC="Sign In text not clicked")
            self.zcc.ui.click(signin_button_xpath, AEFC="Sign In button not clicked on OKTA Screen")
            time.sleep(10)
            self.zcc.explicit_wait(xpath='//android.widget.Button[@content-desc="Logout"]')
        except:
            self.zcc.explicit_wait(xpath='//android.widget.Button[@content-desc="Logout"]')

    @allure.title("Validate UDID Mismatch message on login")
    @pytest.mark.xray("QA-270045")
    @add_markers("regression")
    @pytest.mark.skip("Due to MO-34386")
    def test_udid_error(self):
        '''
        This function tests the scenario where a UDID error occurs during the enrollment process.

        It performs the following steps:
        1. Uninstalls the ZApp.
        2. Installs the ZApp from the specified APK file.
        3. Starts the ZApp.
        4. Grants necessary permissions.
        5. Logs in with a different user account.
        6. Checks if the desired UDID error popup exists.
        7. If the error popup exists, it asserts the test case.
        8. Exits the error popup.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 23-July-2024 <Please retain this marker>
        '''

        alert = '//android.widget.TextView[@resource-id="zscaler.com.zscaler:id/alertTitle"]'
        ok_button = '//android.widget.Button[@resource-id="android:id/button1"]'
        allow_permission = '//android.widget.Button[@resource-id="com.android.permissioncontroller:id/permission_allow_button"]'
        bg_run_and_vpn_permission = '//android.widget.Button[@resource-id="android:id/button1"]'
        error_string = "//*[@class='android.widget.TextView' and @text='Failed to Enroll Device: UDID criteria failed, value not unique and is in use (3100)']"

        apk_name = "Zscaler-Android-3.7.0.57-installer.apk"
        other_user_userid = "<EMAIL>"
        other_user_pass = "admin@123"

        self.zcc.logger.info("Uninstalling ZApp")
        self.sys_ops.adb_commands("adb uninstall zscaler.com.zscaler")
        time.sleep(15)

        self.zcc.logger.info("Installing ZApp by picking ZApp apk from resource directory")
        self.sys_ops.adb_commands("adb install ./OS_Android/resource/{}".format(apk_name))
        time.sleep(25)

        self.zcc.logger.info("Starting ZApp")
        self.sys_ops.adb_commands("adb shell am start -n zscaler.com.zscaler/com.zscaler.activities.MainActivity")

        self.zcc.logger.info("Granting Permissions")
        self.zcc.explicit_wait(xpath=alert)
        self.zcc.ui.click(path=ok_button, AEFC="OK button in alert popup not clicked")
        time.sleep(1)
        self.zcc.ui.click(path=allow_permission, AEFC="Notification permission allowed not clicked")
        time.sleep(1)
        self.zcc.ui.click(path=allow_permission, AEFC="Phone calls permission allowed not clicked")
        time.sleep(1)
        self.zcc.ui.click(path=bg_run_and_vpn_permission, AEFC="Background permission allowed not clicked")

        self.zcc.logger.info("Logging into ZCC")
        result = self.zcc.login(zia_user=other_user_userid, zia_password=other_user_pass)
        assert result[0], result[1]

        self.zcc.logger.info("Checking for desired results i.e. UDID Error Popup")
        self.zcc.explicit_wait(xpath='//android.widget.Button[@resource-id="zscaler.com.zscaler:id/ok_button"]')
        error_exists = self.zcc.ui.check_if_exists(path=error_string, AEFC="Error string mismatched", boolean=True)
        assert error_exists

        self.zcc.logger.info("Exiting Error Popup")
        self.zcc.ui.click(path='//android.widget.Button[@resource-id="zscaler.com.zscaler:id/ok_button"]', AEFC="OK button not clicked")

    @allure.title("Validate Internal Error in ZApp")
    @pytest.mark.xray("QA-270036")
    @add_markers("regression")
    def test_internal_error(self):
        """
        This function tests the internal error scenario in the ZApp. It performs the following steps:

        1. Restarts the ZApp and performs a custom Okta login.
        2. Clicks on the background run and VPN permission button multiple times.
        3. Clicks on the More tab, Restart Service, Yes Restart, and Internet Security tab.
        4. Checks if the service is in an error state and if the error message is displayed.
        5. Clicks on the Learn More button and checks if the error string matches.
        6. Clicks on the OK button.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 26-July-2024 <Please retain this marker>
        """

        bg_run_and_vpn_permission = '//android.widget.Button[@resource-id="android:id/button2"]'
        internal_error_locater = "//*[@class='android.widget.TextView' and @text='Internal Error']"
        error_string = "//*[@class='android.widget.TextView' and @text='Internal socket problem has been detected.']"
        learn_more_locater = '//android.widget.TextView[@text="Learn More"]'

        self.zcc.logger.info("Restarting ZApp")
        result = self.zcc.kill_zcc()
        assert result[0], result[1]
        self.sys_ops.adb_commands("adb shell am start -n zscaler.com.zscaler/com.zscaler.activities.MainActivity")

        self.zcc.logger.info("Logging into ZApp")
        result = self.zcc.custom_okta_login(conftest.variables["ZIA_USER_ID"], conftest.variables["ZIA_USER_PASSWORD"], conftest.variables["ZPA_USER_ID"], conftest.variables["ZPA_USER_PASSWORD"])
        assert result[0], result[1]
        time.sleep(15)

        self.zcc.logger.info("Waiting for permission prompts")
        WebDriverWait(self.zcc.ui.driver, 120).until(EC.visibility_of_element_located((By.XPATH, bg_run_and_vpn_permission)))

        self.zcc.logger.info("Denying permissions for VPN setup")
        self.zcc.ui.click(path=bg_run_and_vpn_permission, AEFC="Permission denied failed")
        time.sleep(2)
        self.zcc.ui.click(path=bg_run_and_vpn_permission, AEFC="Permission denied failed")
        time.sleep(2)
        self.zcc.ui.click(path=bg_run_and_vpn_permission, AEFC="Permission denied failed")
        time.sleep(2)
        self.zcc.ui.click(path=bg_run_and_vpn_permission, AEFC="Permission denied failed")
        time.sleep(2)

        self.zcc.logger.info("Restarting service")
        self.zcc.ui.click(self.elements.MORE_TAB_XPATH, AEFC="More tab not clicked")
        self.zcc.ui.click(self.elements.RESTART, AEFC="Restart Service not clicked")
        self.zcc.ui.click(self.elements.YES_RESTART, AEFC="Yes Restart not clicked")
        time.sleep(2)
        self.zcc.ui.click(self.elements.WEBSECURITY, AEFC="Internet Security tab not clicked")

        self.zcc.logger.info("Checking for Internal Error")
        WebDriverWait(self.zcc.ui.driver, 120).until(EC.visibility_of_element_located((By.XPATH, internal_error_locater)))
        error_state = self.zcc.ui.check_if_exists(path=internal_error_locater, AEFC="Service is not in Error state", boolean=True)
        assert error_state

        self.zcc.ui.click(path=learn_more_locater, AEFC="Learn More not clicked")
        error_string_matched = self.zcc.ui.check_if_exists(path=error_string, AEFC="Error string mismatched", boolean=True)
        assert error_string_matched

        self.zcc.ui.click(path='//android.widget.Button[@resource-id="zscaler.com.zscaler:id/ok_button"]', AEFC="OK button not clicked")

    @allure.title("Teardown Class")
    def teardown_class(self):
        result = self.zcc.logout(password="l")
        assert result[0], result[1]

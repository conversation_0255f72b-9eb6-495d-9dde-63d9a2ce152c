import allure, time, copy
import pytest
import conftest
from OS_Android.library.log_file_helper_function import *
from common_lib.helper_function import *
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from common_lib.common.common_ui import *

class TestZpaReauth:
    def setup_class(self):
        '''
        Setup Class steps:
        1. Initializing required instances
        2. Creating Timeout policy on ZPA
        3. Logging into ZApp using OKTA
        '''

        self.zcc = conftest.zcc
        self.log_obj = conftest.log_ops
        self.sys_ops = conftest.sys_ops
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.elements = copy.deepcopy(AndroidElements)
        self.zpa_policy = conftest.zpa_policy

        try:
            result = self.app_profile.delete_app_profile(operating_system="Android")
            assert result[0], result[1]
            result = self.forwarding_profile.delete_forwarding_profile()
            assert result[0], result[1]
        except Exception as e:
            self.zcc.logger.info(str(e))
            pass

        self.zcc.logger.info("Creating Timeout Policy on ZApp")
        result = self.zpa_policy.create_timeout_policy(idle_timeout=[600, 600])
        assert result[0], result[1]

        self.zcc.logger.info("Creating Forwarding Profile and App Profile")
        result = self.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]
        result = self.app_profile.create_app_profile(operating_system="Android")
        assert result[0], result[1]

        self.zcc.logger.info("Doing OKTA Login")
        signin_button_xpath = '//android.widget.Button[@text="Sign in"]'
        result = self.zcc.initial_zcc_login_window(conftest.variables["ZIA_USER_ID"])
        assert result[0], result[1]
        try:
            self.zcc.explicit_wait(signin_button_xpath, sleep=30)
            self.sys_ops.adb_commands("adb shell input keyevent 61")
            self.sys_ops.adb_commands("adb shell input keycombination 113 29")
            self.sys_ops.adb_commands("adb shell input keyevent 67")
            time.sleep(1)
            cmd = "adb shell input text {}".format(conftest.variables["ZPA_USER_ID"])
            self.sys_ops.adb_commands(cmd)
            self.sys_ops.adb_commands("adb shell input keyevent 61")
            time.sleep(3)
            cmd = "adb shell input text {}".format(conftest.variables["ZPA_USER_PASSWORD"])
            self.sys_ops.adb_commands(cmd)
            self.zcc.ui.click('//android.widget.TextView[@text="Sign In"]', AEFC="Sign In text not clicked")
            self.zcc.ui.click(signin_button_xpath, AEFC="Sign In button not clicked on OKTA Screen")
            time.sleep(10)
            self.zcc.explicit_wait(xpath='//android.widget.Button[@content-desc="Logout"]')
        except:
            self.zcc.explicit_wait(xpath='//android.widget.Button[@content-desc="Logout"]')

        self.zcc.ui.click('//android.widget.TextView[@resource-id="zscaler.com.zscaler:id/optionName" and @text="Internet Security"]', AEFC="Internet Security tab not clicked")

    @allure.title("Validate ReAuth Error in ZPA in ZApp")
    @pytest.mark.xray("A-270041")
    @add_markers("regression")
    @pytest.mark.skip("Due to excess time taking and optional")
    def test_zpa_reauth(self):
        '''
            This function tests the scenario where the ZPA (Zscaler Private Access) tab requires re-authentication.

                It performs the following steps:
                1. Waits for 610 seconds to ensure the session expires.
                2. Opens the ZPA application URL using the adb command.
                3. Starts the Zscaler MainActivity.
                4. Checks if the re-authentication prompt exists.
                5. If the re-authentication prompt exists, it asserts the test case.

                Generated by Priyanshu Choudhary, assisted by ZCoder on 23-July-2024 <Please retain this marker>
        '''

        self.zcc.logger.info("Waiting for Reauth Timeout")
        time.sleep(610)

        self.zcc.logger.info("Accessing the ZPA App")
        self.sys_ops.adb_commands(f"adb shell am start -a android.intent.action.VIEW -d https://{conftest.variables['ZPA_APPLICATION_NAME']}")

        self.zcc.logger.info("Bringing ZApp to focus")
        cmd = "adb shell am start -n zscaler.com.zscaler/com.zscaler.activities.MainActivity"
        self.sys_ops.adb_commands(cmd)

        self.zcc.logger.info("Validating the existence of Reauth Prompt")
        reauth_prompt_exists = self.zcc.ui.check_if_exists(path="//*[@class='android.widget.TextView' and @text='Authentication Required']", boolean=True)
        if reauth_prompt_exists:
            assert True
        else:
            assert False

    def teardown_class(self):
        '''
        Teardown class steps:
        1. Deleting Timeout policy on ZPA
        2. Logging out of ZApp
        '''

        self.zcc.logger.info("Teardown class executing")
        result = self.zpa_policy.delete_policy(policy_type="TimeoutPolicy")
        assert result[0], result[1]
        result = self.zcc.logout(password='l')
        assert result[0], result[1]

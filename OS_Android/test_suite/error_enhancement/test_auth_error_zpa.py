import allure, time, copy
import pytest
import conftest
from OS_Android.library.log_file_helper_function import *
from common_lib.helper_function import *
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from common_lib.common.common_ui import *

class TestAuthErrorZpa:
    @allure.title("Setup method")
    def setup_class(self):
        '''
        Setup class steps:
        1. Initializing required instances
        2. Logging into ZApp using OKTA
        '''

        self.zcc = conftest.zcc
        self.log_obj = conftest.log_ops
        self.sys_ops = conftest.sys_ops
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.elements = copy.deepcopy(AndroidElements)
        self.service_entitlement = conftest.service_ent

        try:
            result = self.service_entitlement.toggle_zpa(action=True)
            assert result[0], result[1]
        except Exception as e:
            self.zcc.logger.info(str(e))

        self.zcc.logger.info("Doing OKTA Login")
        signin_button_xpath = '//android.widget.Button[@text="Sign in"]'
        result = self.zcc.initial_zcc_login_window(conftest.variables["ZIA_USER_ID"])
        assert result[0], result[1]
        try:
            self.zcc.explicit_wait(signin_button_xpath, sleep=30)
            self.sys_ops.adb_commands("adb shell input keyevent 61")
            self.sys_ops.adb_commands("adb shell input keycombination 113 29")
            self.sys_ops.adb_commands("adb shell input keyevent 67")
            time.sleep(1)
            cmd = "adb shell input text {}".format(conftest.variables["ZPA_USER_ID"])
            self.sys_ops.adb_commands(cmd)
            self.sys_ops.adb_commands("adb shell input keyevent 61")
            time.sleep(3)
            cmd = "adb shell input text {}".format(conftest.variables["ZPA_USER_PASSWORD"])
            self.sys_ops.adb_commands(cmd)
            self.zcc.ui.click('//android.widget.TextView[@text="Sign In"]', AEFC="Sign In text not clicked")
            self.zcc.ui.click(signin_button_xpath, AEFC="Sign In button not clicked on OKTA Screen")
            time.sleep(10)
            self.zcc.explicit_wait(xpath='//android.widget.Button[@content-desc="Logout"]')
        except:
            self.zcc.explicit_wait(xpath='//android.widget.Button[@content-desc="Logout"]')

    @allure.title("Validate Authentication Error in ZApp")
    @pytest.mark.xray("QA-270035")
    @add_markers("regression")
    def test_auth_error(self):
        '''
        This function tests the authentication error scenario in the ZPA (Zscaler Private Access) tab.

        It performs the following steps:
        1. Verifies the ZPA service status.
        2. Toggles the ZPA service off and waits for 5 seconds.
        3. Turns off ZPA from the service entitlement and waits for 5 seconds.
        4. Updates the policy and captures the toast message.
        5. Turns on ZPA from the service entitlement and waits for 5 seconds.
        6. Updates the policy and captures the toast message.
        7. Captures the authentication error in the ZPA tab.
        8. Clicks the learn more button and validates the error string.
        9. Clicks the OK button.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 23-July-2024 <Please retain this marker>
        '''
        auth_error_locater = "//*[@class='android.widget.TextView' and @text='Authentication Error']"
        learn_more_locater = '//android.widget.TextView[@text="Learn More"]'
        error_string = "//*[@class='android.widget.TextView' and @text='A tunnel authentication error has occurred.']"

        self.zcc.logger.info("Verifying ZPA service status")
        result = self.zcc.verify_zpa_service_status()
        assert result[0], result[1]

        self.zcc.logger.info("Toggling ZPA service to off")
        result = self.zcc.toggle_service(service="ZPA", action="off", password="D")
        assert result[0], result[1]
        time.sleep(5)

        self.zcc.logger.info("Turning OFF ZPA from service entitlement")
        result = self.service_entitlement.toggle_zpa(action=False)
        assert result[0], result[1]
        time.sleep(5)

        try:
            self.zcc.logger.info("Updating Policy and capturing toast message")
            self.zcc.ui.click(self.elements.NOTIFICATION, AEFC="Unable to click More button on ZCC")
            self.zcc.ui.click(self.elements.UPDATE, sleep_time=0, AEFC="Unable to click Update Policy button")
            WebDriverWait(self.zcc.ui.driver, 120).until(EC.presence_of_element_located((By.XPATH, "//*[@text='Zscaler Private Access disabled']")))

            self.zcc.logger.info("Truning ON ZPA from service entitlement")
            result = self.service_entitlement.toggle_zpa(action=True)
            assert result[0], result[1]
            time.sleep(5)

            self.zcc.logger.info("Updating policy and capturing toast message")
            self.zcc.ui.click(self.elements.NOTIFICATION, AEFC="Unable to click More button on ZCC")
            self.zcc.ui.click(self.elements.UPDATE, sleep_time=0, AEFC="Unable to click Update Policy button")
            WebDriverWait(self.zcc.ui.driver, 120).until(EC.presence_of_element_located((By.XPATH, "//*[@text='Zscaler Private Access enabled']")))

            self.zcc.logger.info("Capturing Auth error in ZPA tab")
            self.zcc.ui.click(path=self.elements.PRIVATE_SECURITY, AEFC="Private Access tab not clicked")
            WebDriverWait(self.zcc.ui.driver, 120).until(EC.visibility_of_element_located((By.XPATH, auth_error_locater)))
            error_state = self.zcc.ui.check_if_exists(path=auth_error_locater, AEFC="Service is not in Error state", boolean=True)
            assert error_state

            self.zcc.logger.info("Clicking learn more button and validating the string")
            self.zcc.ui.click(path=learn_more_locater, AEFC="Learn More not clicked")
            error_string_matched = self.zcc.ui.check_if_exists(path=error_string, AEFC="Error string mismatched", boolean=True)
            assert error_string_matched

            self.zcc.ui.click(path='//android.widget.Button[@resource-id="zscaler.com.zscaler:id/ok_button"]', AEFC="OK button not clicked")

        except Exception as e:
            self.zcc.logger.info(str(e))
            raise Exception

    @allure.title("Teardown Class")
    def teardown_class(self):
        '''
        Teardown class steps:
        1. Logging out of ZApp
        '''
        self.zcc.logger.info("Tearing down class")
        result = self.zcc.logout(password="l")
        assert result[0], result[1]

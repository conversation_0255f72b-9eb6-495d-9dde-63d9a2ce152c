import allure, time, copy
import pytest
import conftest
from OS_Android.library.log_file_helper_function import *
from common_lib.helper_function import *
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers
from selenium.webdriver.support.ui import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from common_lib.common.common_ui import *

class TestNetworkTypeColumn:
    @allure.title("Setup method")
    def setup_class(self):
        '''
        Setup class steps:
        1. Initializing required instances
        2. Clearing already existing automation FP/AP
        3. Creating FP/AP
        4. Doing OKTA Login
        '''

        self.zcc = conftest.zcc
        self.log_obj = conftest.log_ops
        self.sys_ops = conftest.sys_ops
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.elements = copy.deepcopy(AndroidElements)

        try:
            result = self.app_profile.delete_app_profile(operating_system="Android")
            assert result[0], result[1]
            result = self.forwarding_profile.delete_forwarding_profile()
            assert result[0], result[1]
        except Exception as e:
            self.zcc.logger.info(str(e))
            pass

        self.zcc.logger.info("Creating Forwarding Profile and App Profile")
        result = self.forwarding_profile.create_forwarding_profile()
        assert result[0], result[1]
        result = self.app_profile.create_app_profile(operating_system="Android")
        assert result[0], result[1]

        self.zcc.logger.info("Doing OKTA Login")
        signin_button_xpath = '//android.widget.Button[@text="Sign in"]'
        result = self.zcc.initial_zcc_login_window(conftest.variables["ZIA_USER_ID"])
        assert result[0], result[1]
        try:
            self.zcc.explicit_wait(signin_button_xpath, sleep=30)
            self.sys_ops.adb_commands("adb shell input keyevent 61")
            self.sys_ops.adb_commands("adb shell input keycombination 113 29")
            self.sys_ops.adb_commands("adb shell input keyevent 67")
            time.sleep(1)
            cmd = "adb shell input text {}".format(conftest.variables["ZPA_USER_ID"])
            self.sys_ops.adb_commands(cmd)
            self.sys_ops.adb_commands("adb shell input keyevent 61")
            time.sleep(3)
            cmd = "adb shell input text {}".format(conftest.variables["ZPA_USER_PASSWORD"])
            self.sys_ops.adb_commands(cmd)
            self.zcc.ui.click('//android.widget.TextView[@text="Sign In"]', AEFC="Sign In text not clicked")
            self.zcc.ui.click(signin_button_xpath, AEFC="Sign In button not clicked on OKTA Screen")
            time.sleep(10)
            self.zcc.explicit_wait(xpath='//android.widget.Button[@content-desc="Logout"]')
        except:
            self.zcc.explicit_wait(xpath='//android.widget.Button[@content-desc="Logout"]')

        self.zcc.ui.click('//android.widget.TextView[@resource-id="zscaler.com.zscaler:id/optionName" and @text="Internet Security"]', AEFC="Internet Security tab not clicked")

    @allure.title("Validate Network Type column in ZIA")
    @add_markers("regression")
    @pytest.mark.xray("QA-270048,QA-270049")
    def test_nw_column_zia(self):
        '''
        This function tests the Network Type column and service status in the ZIA (Zscaler Internet Access) tab.

        It performs the following steps:
        1. Checks the existence of the Network Type column and verifies if the service status is in the connecting state.
        2. Verifies the ZIA service status.
        3. Checks the existence of the Network Type column and verifies if the service status is in the ON state.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 23-July-2024 <Please retain this marker>
        '''

        nw_type_locater = "//*[@class='android.widget.TextView' and @text='Network Type']"
        service_on_locater = "//*[@class='android.widget.TextView' and @text='ON']"
        service_connecting_locater = "//*[@class='android.widget.TextView' and @text='Connecting...']"

        self.zcc.logger.info("Checking the existence of Network type column and service status in connecting state")
        nw_column_exists = self.zcc.ui.check_if_exists(path=nw_type_locater, AEFC="Network Type column not found", boolean=True)
        connecting_state = self.zcc.ui.check_if_exists(path=service_connecting_locater, AEFC="Service is not in connecting state", boolean=True)
        if nw_column_exists and connecting_state:
            assert True
        else:
            assert False

        self.zcc.logger.info("Verifying ZIA service status")
        result = self.zcc.verify_service_status()
        assert result[0], result[1]

        self.zcc.logger.info("Checking the existence of Network type column and service status in ON state")
        nw_column_exists = self.zcc.ui.check_if_exists(path=nw_type_locater, AEFC="Network Type column not found", boolean=True)
        on_state = self.zcc.ui.check_if_exists(path=service_on_locater, AEFC="Service is not in ON state", boolean=True)
        if nw_column_exists and on_state:
            assert True
        else:
            assert False

    @allure.title("Validate Report an Issue Failed error")
    @pytest.mark.xray("QA-270043")
    @add_markers("regression")
    def test_report_issue_error(self):
        '''
        This function tests the scenario where an error occurs while reporting an issue in the ZCC (Zscaler Client Connector) app.

        It performs the following steps:
        1. Disables the WiFi.
        2. Starts the reporting issue process.
        3. Types in the username and comments fields.
        4. Reports the issue and waits for an error to occur.
        5. Validates if the error string exists.
        6. If the error string exists, it asserts the test case.
        7. Exits the error popup.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 23-July-2024 <Please retain this marker>
        '''

        error_string = "//*[@class='android.widget.TextView' and @text='An error occurred processing request']"

        self.zcc.logger.info("Disabling the WiFi")
        result = self.sys_ops.toggle_wifi(disable=True)
        assert result[0], result[1]

        self.zcc.logger.info("Reporting and Issue process commenced")
        self.zcc.ui.click(self.elements.NOTIFICATION, AEFC="More tab not clicked")
        self.zcc.ui.click(self.elements.REPORT_ISSUE, AEFC="Report an issue not not clicked")
        self.zcc.ui.type(path='//android.widget.EditText[@resource-id="zscaler.com.zscaler:id/userName"]', text="test", sleep_time=2)
        self.zcc.ui.type(path='//android.widget.EditText[@resource-id="zscaler.com.zscaler:id/comments"]', text="test", sleep_time=2)

        self.zcc.logger.info("Reporting issue and awaiting for error")
        self.sys_ops.adb_commands("adb shell input keyevent KEYCODE_BACK")
        time.sleep(1)
        self.zcc.ui.click('//android.widget.Button[@resource-id="zscaler.com.zscaler:id/send"]', AEFC='Not able to click on send')
        output = self.zcc.explicit_wait(xpath='//android.widget.TextView[@resource-id="zscaler.com.zscaler:id/errorText"]')

        if output[1] == 'element found':
            assert True
        else:
            assert False

        self.zcc.logger.info("Validating error string")
        self.zcc.explicit_wait('//android.widget.TextView[@resource-id="zscaler.com.zscaler:id/errorText"]')
        error_exists = self.zcc.ui.check_if_exists(path=error_string, AEFC="Error string mismatched", boolean=True)
        assert error_exists

        self.zcc.ui.click('//android.widget.Button[@resource-id="zscaler.com.zscaler:id/ok_button"]', AEFC="OK button not clicked")

    @allure.title("Validate ZDX Server Error in ZApp")
    @pytest.mark.xray("QA-270040")
    @add_markers("regression")
    def test_server_error(self):
        '''
        This function tests the scenario where a server error occurs in the ZDX (Digital Experience) tab.

        It performs the following steps:
        1. Navigates to the ZDX tab and checks for the error.
        2. Validates if the error string exists.
        3. If the error string exists, it asserts the test case.
        4. Exits the error popup.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 23-July-2024 <Please retain this marker>
        '''

        server_error_locater = "//*[@class='android.widget.TextView' and @text='Server Error']"
        error_string = "//*[@class='android.widget.TextView' and @text='Zscaler Client Connector is unable to connect to the ZDX cloud.']"
        learn_more_locater = '//android.widget.TextView[@text="Learn More"]'

        self.zcc.logger.info("Navigating to ZDX tab and checking for error")
        self.zcc.ui.click(path='//android.widget.TextView[@resource-id="zscaler.com.zscaler:id/optionName" and @text="Digital Experience"]', AEFC="ZDX tab button not clicked")
        time.sleep(10)
        WebDriverWait(self.zcc.ui.driver, 120).until(EC.visibility_of_element_located((By.XPATH, server_error_locater)))
        error_state = self.zcc.ui.check_if_exists(path=server_error_locater, AEFC="Service is not in Error state", boolean=True)
        assert error_state

        self.zcc.logger.info("Validating error string")
        self.zcc.ui.click(path=learn_more_locater, AEFC="Learn More not clicked")
        error_exists = self.zcc.ui.check_if_exists(path=error_string, AEFC="Error string mismatched", boolean=True)
        assert error_exists

        self.zcc.ui.click(path='//android.widget.Button[@resource-id="zscaler.com.zscaler:id/ok_button"]', AEFC="OK button not clicked")

    @allure.title("Validate Network Error in Zapp")
    @pytest.mark.xray("QA-270034,QA-270050")
    @add_markers("regression")
    def test_network_error(self):
        '''
        This function tests the scenario where a network error occurs in the Internet Security tab.

        It performs the following steps:
        1. Navigates to the Internet Security tab.
        2. Checks the existence of the Network Type column and verifies if the service status is in the error state.
        3. Validates if the error state exists.
        4. If the error state exists, it asserts the test case.
        5. Validates if the error string exists.
        6. If the error string exists, it asserts the test case.
        7. Exits the error popup.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 23-July-2024 <Please retain this marker>
        '''

        service_error_locater = "//*[@class='android.widget.TextView' and @text='Network Error']"
        nw_type_locater = "//*[@class='android.widget.TextView' and @text='Network Type']"

        self.zcc.logger.info("Navigating to Internet Security Tab")
        self.zcc.ui.click(
            path='//android.widget.TextView[@resource-id="zscaler.com.zscaler:id/optionName" and @text="Internet Security"]')
        time.sleep(5)

        self.zcc.logger.info("Checking the existence of Network type column and service status in error state")
        nw_column_exists = self.zcc.ui.check_if_exists(path=nw_type_locater, AEFC="Network Type column not found",
                                                       boolean=True)
        WebDriverWait(self.zcc.ui.driver, 180).until(
            EC.visibility_of_element_located((By.XPATH, service_error_locater)))
        error_state = self.zcc.ui.check_if_exists(path=service_error_locater, AEFC="Service is not in Error state",
                                                  boolean=True)
        if nw_column_exists and error_state:
            assert True
        else:
            assert False

        network_error_locater = "//*[@class='android.widget.TextView' and @text='Network Error']"
        learn_more_locater = '//android.widget.TextView[@text="Learn More"]'
        error_string = "//*[@class='android.widget.TextView' and @text='No network interface can be detected.']"

        self.zcc.logger.info("Validating error state")
        WebDriverWait(self.zcc.ui.driver, 120).until(
            EC.visibility_of_element_located((By.XPATH, network_error_locater)))
        error_state = self.zcc.ui.check_if_exists(path=network_error_locater, AEFC="Service is not in Error state",
                                                  boolean=True)
        assert error_state

        self.zcc.logger.info("Validating error string")
        self.zcc.ui.click(path=learn_more_locater, AEFC="Learn More not clicked")
        error_exists = self.zcc.ui.check_if_exists(path=error_string, AEFC="Error string mismatched", boolean=True)
        assert error_exists

        self.zcc.ui.click(path='//android.widget.Button[@resource-id="zscaler.com.zscaler:id/ok_button"]',
                          AEFC="OK button not clicked")

    @allure.title("Validate Network Status column when ZPA is in error state")
    @pytest.mark.xray("QA-270053")
    @add_markers("regression")
    def test_nw_column_zpa_error_state(self):
        '''
        This function tests the scenario where a network error occurs in the ZPA (Zscaler Private Access) tab.

        It performs the following steps:
        1. Navigates to the ZPA tab and waits for the error state.
        2. Checks the existence of the Network Type column and verifies if the service status is in the error state.
        3. If the error state exists, it asserts the test case.
        4. Turns the WiFi on.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 23-July-2024 <Please retain this marker>
        '''

        nw_type_locater = "//*[@class='android.widget.TextView' and @text='Network Type']"
        service_error_locater = "//*[@class='android.widget.TextView' and @text='Network Error']"

        self.zcc.logger.info("Navigating to ZPA tab and awaiting for error state")
        self.zcc.ui.click(self.elements.PRIVATE_SECURITY, AEFC="ZPA tab not clicked")
        WebDriverWait(self.zcc.ui.driver, 120).until(EC.visibility_of_element_located((By.XPATH, service_error_locater)))
        nw_column_exists = self.zcc.ui.check_if_exists(path=nw_type_locater, AEFC="Network Type column not found", boolean=True)
        error_state = self.zcc.ui.check_if_exists(path=service_error_locater, AEFC="Service is not in Error state", boolean=True)
        if nw_column_exists and error_state:
            assert True
        else:
            assert False

        self.zcc.logger.info("Turning WiFi to ON")
        result = self.sys_ops.toggle_wifi(enable=True)
        assert result[0], result[1]

    @allure.title("Validate Network Status column when ZPA is in connecting state")
    @pytest.mark.xray("QA-270052")
    @add_markers("regression")
    def test_nw_column_zpa_connecting_state(self):
        '''
        This function tests the scenario where a network error occurs in the ZPA (Zscaler Private Access) tab.

        It performs the following steps:
        1. Navigates to the ZPA tab and waits for the error state.
        2. Checks the existence of the Network Type column and verifies if the service status is in the error state.
        3. If the error state exists, it asserts the test case.
        4. Turns the WiFi on.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 23-July-2024 <Please retain this marker>
        '''

        nw_type_locater = "//*[@class='android.widget.TextView' and @text='Network Type']"
        service_connecting_locater = "//*[@class='android.widget.TextView' and @text='Connecting...']"

        self.zcc.logger.info("Restarting ZApp")
        result = self.zcc.kill_zcc()
        assert result[0], result[1]
        res = self.zcc.ui.start_app(app=AndroidElements.APP, sleep_time=15)

        self.zcc.logger.info("Checking the existence of Network type column and service status in connecting state")
        WebDriverWait(self.zcc.ui.driver, 120).until(EC.visibility_of_element_located((By.XPATH, service_connecting_locater)))
        nw_column_exists = self.zcc.ui.check_if_exists(path=nw_type_locater, AEFC="Network Type column not found", boolean=True)
        connecting_state = self.zcc.ui.check_if_exists(path=service_connecting_locater, AEFC="Service is not in connecting state", boolean=True)
        if nw_column_exists and connecting_state:
            assert True
        else:
            assert False

    @allure.title("Validate Network Status column when ZPA is in ON state")
    @pytest.mark.xray("QA-270051")
    @add_markers("regression")
    def test_nw_column_zpa_on_state(self):
        '''
        This function tests the scenario where the ZPA (Zscaler Private Access) tab is in the ON state.

        It performs the following steps:
        1. Verifies the ZPA service status.
        2. Checks the existence of the Network Type column and verifies if the service status is in the ON state.
        3. If the ON state exists, it asserts the test case.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 23-July-2024 <Please retain this marker>
        '''

        nw_type_locater = "//*[@class='android.widget.TextView' and @text='Network Type']"
        service_on_locater = "//*[@class='android.widget.TextView' and @text='ON']"

        self.zcc.logger.info("Verifying ZPA service status")
        result = self.zcc.verify_zpa_service_status()
        assert result[0], result[1]

        self.zcc.logger.info("Checking the existence of Network type column and service status in ON state")
        nw_column_exists = self.zcc.ui.check_if_exists(path=nw_type_locater, AEFC="Network Type column not found",
                                                       boolean=True)
        on_state = self.zcc.ui.check_if_exists(path=service_on_locater, AEFC="Service is not in ON state", boolean=True)
        if nw_column_exists and on_state:
            assert True
        else:
            assert False

    @allure.title("Teardown Class")
    def teardown_class(self):
        '''
        Teardown class steps:
        1. Toggling the wifi to ON
        2. Deleting AP/FP
        3. Logging out of ZApp
        '''

        self.zcc.logger.info("Tearing down class")
        result = self.sys_ops.toggle_wifi(enable=True)
        assert result[0], result[1]
        result = self.app_profile.delete_app_profile(operating_system="Android")
        assert result[0], result[1]
        result = self.forwarding_profile.delete_forwarding_profile()
        assert result[0], result[1]
        result = self.zcc.logout(password='l')
        assert result[0], result[1]

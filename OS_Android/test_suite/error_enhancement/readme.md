# Error Enhancement TCs - Requirements
## Prerequisites:
    1) On MA company and user must be configured properly.
    2) On Android device proper zcc version should be installed.
    3) Browser Based auth must be enabled on MA.
    4) User must be ZIA+ZPA user.
    5) Remove any saved passwords for okta login page in order to avoid unnecessary popups during login.
    6) Mobile data should be off, online wifi should be enabled before launching the suite.
    7) Modify the apk_name variable in test_udid_error.py file.
    8 ) Provide with one more user creds in test_udid_error.py file.

## Commands to Execute Automation:
**Running All Cases:** python trigger_automation.py --config <config.json> --testcases test_suite/error_enhancement

**Running One Test File from debugging_logs folder:** python trigger_automation.py --config config.json --testcases test_suite/error_enhancement/*.py

**Testcases of [MR-9420](https://jira.corp.zscaler.com/browse/MR-9420)

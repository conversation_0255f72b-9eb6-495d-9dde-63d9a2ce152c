import allure, time, copy
import pytest
import conftest
from OS_Android.library.log_file_helper_function import *
from common_lib.helper_function import *
from OS_Android.library.android_element import AndroidElements
from common_lib.Custom_Markers import add_markers
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from common_lib.common.common_ui import *

class TestDeviceTokenError:
    def setup_class(self):
        '''
        Setup Class steps:
        1. Initializing required instances
        2. Setting up Client Connector as IDP
        3. Enabling SAML Authentication
        '''

        self.zcc = conftest.zcc
        self.log_obj = conftest.log_ops
        self.sys_ops = conftest.sys_ops
        self.app_profile = conftest.app_profile_obj
        self.forwarding_profile = conftest.forwarding_profile_obj
        self.elements = copy.deepcopy(AndroidElements)
        self.saml_obj = conftest.saml_obj

        try:
            result = self.saml_obj.delete_zscaler_client_connector_idp()
            assert result[0], result[1]
        except Exception as e:
            self.zcc.logger.info(str(e))

        self.zcc.logger.info("Setting up Client Connector as IDP")
        result = self.saml_obj.create_zscaler_client_connector_idp()
        assert result[0], result[1]
        time.sleep(1)

        self.zcc.logger.info("Enabling the SAML mode from ZIA")
        result = self.saml_obj.saml_action_smui("enable")
        assert result[0], result[1]

    @allure.title("Validate 'Invalid Device Token' error in ZApp")
    @pytest.mark.xray("QA-270046")
    @add_markers("regression")
    @pytest.mark.skip("Due to unavailability of locator of Invalid Device token error")
    def test_invalid_device_token(self):
        '''
        This function tests the scenario where an invalid device token is encountered during the login process.

        It performs the following steps:
        1. Logs in with ZCC as the identity provider setup.
        2. Checks if the error exists.
        3. If the error exists, it asserts the test case.

        Generated by Priyanshu Choudhary, assisted by ZCoder on 23-July-2024 <Please retain this marker>
        '''

        error_string = "//*[@class='android.widget.TextView' and @text='Invalid Device Token']"

        self.zcc.logger.info("Logging in with zcc as idp setup")
        result = self.zcc.initial_zcc_login_window(conftest.variables["ZIA_USER_ID"])
        assert result[0], result[1]

        self.zcc.logger.info("Checking if the error exists")
        self.zcc.explicit_wait('//android.widget.Button[@resource-id="zscaler.com.zscaler:id/login_button"]')
        error_exist = self.zcc.ui.check_if_exists(path=error_string, boolean=True)
        assert error_exist

    def teardown_class(self):
        '''
        Teardown Class steps:
        1. Disabling the SAML mode from ZIA which was enabled in the setup class
        2. Deleting the ZCC as IDP config
        '''
        result = self.saml_obj.saml_action_smui("disable")
        assert result[0], result[1]
        result = self.saml_obj.delete_zscaler_client_connector_idp()
        assert result[0], result[1]

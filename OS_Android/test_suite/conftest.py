import json,os,sys
import socket,re
import pytest
import allure
import subprocess
from datetime import datetime
from common_lib import helper_function
from datetime import datetime
from OS_Android.library.ops_system import SysOps
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
from OS_Android.library.ui_zcc import Zcc
fetch_logs = FetchAndroidLogs()
sys_ops=SysOps()
setup_failure_captured = {}

install_zcc = os.environ["ZCC_INSTALL"]

# ------------------------------------------------- PYTEST HTML CONFIGURATION STARTS -------------------------------------------------------
total_test_cases_executed = []  # list containing jira id fetched from xray marker


def pytest_html_results_table_header(cells):
    cells.insert(2, "<th>Total Test Cases Executed</th>")
    cells.insert(3, "<th>Test Case ID</th>")
    cells.insert(4, "<th>BUG ID</th>")


def pytest_html_results_table_row(report, cells):
    """
        Generated by Abhimanyu Sharma, assisted by ZCoder on 14-May-2024
        <Please retain this marker>

        This function is used to modify the pytest html results table row.

        Args:
            report (pytest.report): The pytest report object.
            cells (list): The list of cells in the table row.
    """
    tc_reason = None
    bug_reason = None

    # This codeblock to handle cases where "Setup" or "Conftest" fails for a test case or test suite.
    try:
        report._tc_id is None
    except:
        line = f"report._tc_id not assigned"
        report._tc_id = None
        try:
            line2 = f"{report.head_line} setup or conftest failed."
        except:
            line2 = "setup or conftest failed"
        print(f"{line} {line2}")
        tc_reason = line2

    try:
        report._bug_id is None
    except:
        line = f"report._bug_id not assigned"
        report._bug_id = None
        try:
            line2 = f"{report.head_line} setup or conftest failed."
        except:
            line2 = "setup or conftest failed"
        print(f"{line} {line2}")
        bug_reason = line2

    if report._tc_id == None:
        cells.insert(2, '<td>0</td>')
        if tc_reason is None:
            cells.insert(3, '<td></td>')
        else:
            cells.insert(3, f'<td>{tc_reason}</td>')
    else:
        if type(report._tc_id) is str:
            report._tc_id = report._tc_id.split(",")
        cells.insert(2, f'<td>{len(report._tc_id)}</td>')

        all_testcases = ""
        for testcase in report._tc_id:
            line = f'<a href="https://jira.corp.zscaler.com/browse/{testcase}">{testcase}</a>'
            if testcase == report._tc_id[-1]:
                all_testcases = f"{all_testcases} {line}"
            else:
                all_testcases = f"{all_testcases} {line}, "
        cells.insert(3, f'<td>{all_testcases}</td>')

    if report._bug_id == None:
        if bug_reason is None:
            cells.insert(4, '<td></td>')
        else:
            cells.insert(4, f'<td>{bug_reason}</td>')
    else:
        if type(report._bug_id) is str:
            report._bug_id = report._bug_id.split(",")

        all_bugs = ""
        for bug in report._bug_id:
            line = f'<a href="https://jira.corp.zscaler.com/browse/{bug}">{bug}</a>'
            if bug == report._bug_id[-1]:
                all_bugs = f"{all_bugs} {line}"
            else:
                all_bugs = f"{all_bugs} {line}, "

        # cells.insert(4, f'<td><a href="https://jira.corp.zscaler.com/browse/{report._bug_id}">{report._bug_id}</a></td>')
        cells.insert(4, f'<td>{all_bugs}</td>')


def pytest_html_results_summary(prefix, summary, postfix):
    global total_test_cases_executed

    prefix.append('<table style="border-collapse: collapse; width: 20%;">')

    prefix.append('''
    <tr>
        <th style="border: 1px solid black; padding: 8px; text-align: left; font-weight: bold; background-color: #f2f2f2;">Configurations</th>
        <th style="border: 1px solid black; padding: 8px; text-align: left; font-weight: bold; background-color: #f2f2f2;">Value</th>
    </tr>
    ''')

    # Add rows for each piece of information
    prefix.append(f'''
    <tr>
        <td style="border: 1px solid black; padding: 8px;">Operating System Type</td>
        <td style="border: 1px solid black; padding: 8px; color: black;">{os.environ.get("Andr_Platform")}</td>
    </tr>
    <tr>
        <td style="border: 1px solid black; padding: 8px;">Target Machine OS Version</td>
        <td style="border: 1px solid black; padding: 8px; color: black;">{os.environ.get("Target_OS_Version")}</td>
    </tr>
    <tr>
        <td style="border: 1px solid black; padding: 8px;">Target Machine SDK Version</td>
        <td style="border: 1px solid black; padding: 8px; color: black;">{os.environ.get("Target_SDK_Version")}</td>
    </tr>
    <tr>
        <td style="border: 1px solid black; padding: 8px;">Host PC</td>
        <td style="border: 1px solid black; padding: 8px; color: black;">{os.environ.get("machine_used")}</td>
    </tr>
    <tr>
        <td style="border: 1px solid black; padding: 8px;">Host OS Version</td>
        <td style="border: 1px solid black; padding: 8px; color: black;">{os.environ.get("os_version")}</td>
    </tr>
    <tr>
        <td style="border: 1px solid black; padding: 8px;">ZCC Version</td>
        <td style="border: 1px solid black; padding: 8px; color: black;">{os.environ.get("zcc_version")}</td>
    </tr>
    <tr>
        <td style="border: 1px solid black; padding: 8px;">Total Jira Testcases Executed</td>
        <td style="border: 1px solid black; padding: 8px; color: black;">{len(set(total_test_cases_executed))}</td>
    </tr>
    ''')

    # Close the table
    prefix.append('</table>')


def pytest_html_report_title(report):
    report.title = "Automation Report"

# ------------------------------------------------- PYTEST HTML CONFIGURATION ENDS -------------------------------------------------------

# omited rest for clarity

def capture_logs_and_screenshot(log_dir=None, fetch = True):
    zcc = Zcc()
    if fetch:
        sys_ops.zapp_screenshot('automation_1.png')
        fetch_logs.delete_zcc_logs()
        zcc.export_logs()

    file_name = os.path.join(log_dir , "tc.png")
    sys_ops.copy_screen_shot('automation_1.png', file_name)
    with open(file_name, 'rb') as file:
        allure.attach(file.read(), name='Screenshot', attachment_type=allure.attachment_type.PNG)

    cmd = "adb shell ls -t /sdcard/Download/"
    out = subprocess.run(cmd, capture_output=True, text=True)

    file_to_extract = ""
    for file in out.stdout.split('\n'):
        if 'ZCC_' in file:
            file_to_extract = file
            break

    file_name_log_zip = os.path.join(log_dir , "tc.zip")
    cmd = f"adb pull /sdcard/Download/{file_to_extract} {file_name_log_zip}"
    output = sys_ops.adb_commands(cmd)

@pytest.hookimpl(hookwrapper=True)
def pytest_runtest_makereport(item, call):
    """
        This function is used to generate a report for each test case run by pytest.
        It is a hookwrapper function, which means it can yield to other plugins
        and it can modify the outcome of the test run.

        Generated by Abhimanyu Sharma, assisted by ZCoder on 14-May-2024 <Please retain this marker>
    """

    global total_test_cases_executed
    global total_MO_Ids_executed
    global setup_failure_captured

    outcome = yield
    report = outcome.get_result()
    report.description = str(item.function.__doc__)

    try:
        # store as _trid the value from marker
        report._tc_id = item.get_closest_marker("xray").args[0]

        if type(report._tc_id) is str:
            report._tc_id = report._tc_id.split(",")
        total_test_cases_executed.extend(report._tc_id)
    except:
        report._tc_id = None

    try:
        # store as _trid the value from marker
        report._bug_id = item.get_closest_marker("bug").args[0]
        if type(report._bug_id) is str:
            report._bug_id = report._bug_id.split(",")
        total_MO_Ids_executed.extend(report._bug_id)
    except:
        report._bug_id = None

    try:
        os_version = str(11 if sys.getwindowsversion().build >= 22000 else 10)
        report._machine_details = str(helper_function.get_interface_ip()) + " Win" + os_version
    except Exception as e:
        print(f"Issues in {e}")
        report._machine_details = None


    if os.environ.get("exportlogsandscreenshot") == "true":
        if report.when == 'call' or report.when == "setup":
            if report.skipped or report.failed:
                timestamp_str = datetime.now().strftime("%d_%m_%Y-%I_%M_%S_%p")

                # file_name_1 = os.path.join(os.environ.get("reports_folder"), "screenshots",
                #                            os.environ.get('PYTEST_CURRENT_TEST').split(' ')[0].split('::')[
                #                                -1] + "_" + str(timestamp_str) + ".png")
                # file_name_2 = os.path.join(os.environ.get("reports_folder"), "exportlogs",
                #                            os.environ.get('PYTEST_CURRENT_TEST').split(' ')[0].split('::')[
                #                                -1] + "_" + str(timestamp_str) + ".zip")

                cwd = os.getcwd()
                temp = os.environ.get('PYTEST_CURRENT_TEST').split('::')[-1].rstrip(' (call)')
                temp = temp.replace(" ","")
                # Replace special characters and multiple consecutive slashes with underscores
                temp = re.sub(r'[^a-zA-Z0-9_\-\.]+', '_', temp)
                log_dir = os.path.join(cwd, os.environ.get("reports_folder"), "debug_logs",temp)
                log_dir = log_dir.replace(" ","")

                if not os.path.exists(log_dir):
                    os.makedirs(log_dir)

                module_name = report.nodeid.split("::")[0]
                print(f"Module name for skipped or Failed case :: {module_name}")
                if report.when == "setup" and module_name not in setup_failure_captured:
                    setup_failure_captured[module_name] = False
                if report.when == "setup" and not setup_failure_captured[module_name]:
                    setup_failure_captured[module_name] = True
                    # Capture logs and screenshot here
                    capture_logs_and_screenshot(log_dir,fetch=True)
                elif report.when == "setup" and setup_failure_captured[module_name]:
                    # Capture logs and screenshot here
                    capture_logs_and_screenshot(log_dir,fetch=False)
                elif report.when == "call":
                    # Capture logs and screenshot here
                    capture_logs_and_screenshot(log_dir)

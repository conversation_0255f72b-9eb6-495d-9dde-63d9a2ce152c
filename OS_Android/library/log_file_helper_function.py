import os


def log_file_helper():
    '''
    This function is responsible for generating a dictionary constituting log files into categories.
    The keys of the dictionary are categories and the values are the list of log files.
    Currently, it is only responsible for constituting tunnel log files. Furthermore, it will be modified according to requirements for other log files too.
    Return a dictionary
    Generated by <PERSON><PERSON><PERSON><PERSON>
    '''

    path = os.getcwd() + "\\OS_Android\\Temp_Logs"
    files_list = os.listdir(path)
    log_files = {"tunnel_logs": [],
                 "tray_logs": []}

    for file in files_list:
        if file.startswith("ZCC_Tunnel") and file.endswith(".log"):
            log_files['tunnel_logs'].append(file)
        elif file.startswith("ZCC_Tray") and file.endswith(".log"):
            log_files['tray_logs'].append(file)

        if file.startswith("ZCC_Android") and file.endswith(".log"):
            log_files['tunnel_logs'].append(file)
        if file.startswith("ZCC_Android") and file.endswith(".log"):
            log_files['tray_logs'].append(file)

    print(log_files)
    return log_files

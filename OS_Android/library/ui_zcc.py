############### IMPORTS BEGINNING #################
import copy
import os
import datetime
import re
import subprocess
import time
import allure
from functools import wraps

from scapy.layers.eap import EAPOL
from selenium.webdriver.support import expected_conditions as EC
from common_lib.common.common_ui import *
from common_lib.common.logger import Logger
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
from OS_Android.library.android_element import AndroidElements
from OS_Android.library.ops_system import SysOps
from selenium.webdriver.support.ui import WebDriverWait
from common_lib.helper_function import help_return
from common_lib.common.log_ops import *
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
from OS_Android.library.ops_system import SysOps
import pyshark
from selenium.webdriver.common.by import By
sys_ops = SysOps()
operating_system = sys_ops.android_device_type()

if operating_system == "Android":
    zcc_package_name = AndroidElements.ANDROID_PACKAGE

else:
    zcc_package_name = AndroidElements.CHROMEOS_PACKAGE

cmd = f'adb shell dumpsys package {zcc_package_name} | findStr versionName'
output = sys_ops.adb_commands(cmd)
version = output[0], output[1], (output[2].strip()).decode()
version = version[2].split("=")
zcc = version[1].split(".")



if int(zcc[0])>=4 and int(zcc[-1])>=46:
    zcc_activity = AndroidElements.NEW_ZCC_ACTIVITY

else:
    zcc_activity = AndroidElements.OLD_ZCC_ACTIVITY


class Zcc:
    """
    This class will perform Ui operations on Zcc
    """

    zcc_ui = Gui()
    os_type = sys_ops.os_detection()[1]
    chromeos = True if os_type.lower() == "chromeos" else False
    os.environ["Andr_Platform"] = "ChromeOS" if chromeos else "Android"
    # self.ui = Gui(handle=handle, log_handle=self.logger)
    operating_system = zcc_ui.operating_system
    zcc_ui.start_app(app=AndroidElements.APP, sleep_time=30)

    def __init__(self,
                 handle: str = "easy",
                 start_app: bool = False,
                 log_handle: bool = None):
        self.logger = (
            log_handle if log_handle else Logger.initialize_logger(log_file_name="ZCC.log", log_level="INFO"))
        self.sys_ops = SysOps(self.logger)
        self.ui = self.zcc_ui
        self.elements = copy.deepcopy(AndroidElements)
        # os_type = self.sys_ops.os_detection()[1]
        # self.chromeos = True if os_type.lower() == "chromeos" else False
        # os.environ["Andr_Platform"] = "ChromeOS" if self.chromeos else "Android"
        # #self.ui = Gui(handle=handle, log_handle=self.logger)
        # self.operating_system = self.ui.operating_system
        # self.logger.info("Initialized ZCC GUI")
        self.sleep_factor = 1
        self.start_app = start_app
        self.fetch_export_logs = FetchAndroidLogs()
        self.zcc_build = ''

        self.initialize_elements()
        self.start_time_for_log_search = ""

        target_os_version = \
            subprocess.Popen('adb shell getprop ro.build.version.release', stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE,
                             stdin=subprocess.PIPE).communicate()[0].decode('utf-8').split()
        os.environ["Target_OS_Version"] = target_os_version[0]
        target_sdk_version = \
            subprocess.Popen('adb shell getprop ro.build.version.sdk', stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                             stdin=subprocess.PIPE).communicate()[0].decode('utf-8').split()
        os.environ["Target_SDK_Version"] = target_sdk_version[0]

    # ------------------------------------------------------------------------------------------------------------------

    def initialize_elements(self):  # initialize elements for win/mac
        """
        This function will copy elements

        Returns:
            True,"Elements copied",None
        """

        if self.start_app:
            self.elements = copy.deepcopy(AndroidElements)
            self.ui.app = self.elements.APP
            return True, "Elements copied"

    # ------------------------------------------------------------------------------------------------------------------

    def explicit_wait(self, xpath, sleep=120):
        """
        This function waits for a specific element to be visible on the page.
        It takes two arguments: xpath - the path to the element, and sleep - the
        time to wait for the element to become visible (default is 120 seconds).
        It returns a tuple with a boolean status and a string message.


        """

        status = False
        try:

            WebDriverWait(self.ui.driver, sleep).until(EC.visibility_of_element_located((By.XPATH, xpath)))
            self.logger.info("SUCCESS:: Element exists::{}".format(xpath))
            status = True

        except:
            self.logger.info("Not able to find element by xpath")

        return status, "element found" if status else "element not found"

    # ------------------------------------------------------------------------------------------------------------------

    def validate_zpa_bytes(self):
        """
        This function validates the ZPA bytes by clicking on the Private Security element, then retrieving the total bytes sent and received.
        It returns a tuple with sent_bytes and received_bytes

        """

        cmd = f"adb shell am start -n {zcc_package_name}/{zcc_activity}"
        subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=subprocess.PIPE)
        time.sleep(5)
        try:
            self.ui.click(self.elements.PRIVATE_SECURITY, sleep_time=1)

            sent_bytes = self.ui.click(self.elements.TOTAL_BYTES_SENT, sleep_time=1, do_click=False,
                                       return_text=True, )

            received_bytes = self.ui.click(self.elements.TOTAL_BYTES_RECEIVED, sleep_time=1,
                                           do_click=False, return_text=True, )
            return True, "Bytes fetched succes", (sent_bytes, received_bytes)
        except:
            return False, "Failed to fetch bytes"

    # ------------------------------------------------------------------------------------------------------------------

    def calculate_function_execution_time(func):
        '''
        Decorator used for calculating time taken for execution of a function
        Flow: ------------------------------
            1. Func is passed to calculate_function_execution_time which calls wrapper func
            2. Wrapper func then first gets the current time(start time) and then make call to the given func as arg in calculate_function_execution_time
            3. After func is executed, we then get the current time(end time) and then calculate endtime-starttime and get the time difference
        '''

        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            # print("START TIME : {}".format(start_time))
            res = func(*args, **kwargs)
            end_time = time.time()
            # print("END TIME : {}".format(end_time))
            args[0].file_transfer_time = int(end_time - start_time)
            print("\n")
            args[0].logger.critical(f"{'*' * 50}")
            args[0].logger.critical(f"TIME TAKEN FOR {func.__name__} : {args[0].file_transfer_time} Seconds")
            args[0].logger.critical(f"{'*' * 50}")
            return res

        return wrapper

    # ------------------------------------------------------------------------------------------------------------------

    def validate_zcc_logged_in(self):
        """
        This function validates if a user is logged in to the ZCC system.

        It first waits explicitly for the ZPA_ON_STATUS element to be present. Then, it checks if the LOGOUT_BUTTON element exists.
        If the LOGOUT_BUTTON exists, it means the user is logged in and the function returns True along with the message "Login success".
        If the LOGOUT_BUTTON does not exist, it means the user is not logged in and the function returns False along with the message "Login failed".

        """

        status = False
        zcc_logged_in_validated = False
        zcc_logged_in_validated = self.ui.check_if_exists(self.elements.LOGOUT_BUTTON, boolean=True)

        if zcc_logged_in_validated:
            status = True
            self.logger.info(f"Logout button exists, zcc logged in")
        else:
            self.logger.error("Logout button does not exists, please check logs and zcc state")

        return status, "Login success" if status else "Login failed"

    def bring_zcc_to_focus(self):
        """
        Brings ZCC to focus.

        Returns:
            tuple: A tuple containing a boolean status and a string message.

        """

        status = False
        try:
            result = self.sys_ops.adb_commands(
                f"adb shell am start {zcc_package_name}/{zcc_activity}")
            if result[0]:
                self.logger.info("Success :: ZCC brought to Focus!")
                status = True
        except Exception as e:
            self.logger.info("ZCC not came in focus ")

        return status, "ZCC in focus " if status else "ZCC is not in focus", None

    # ------------------------------------------------------------------------------------------------------------------

    def do_okta_login(self,
                      user_id: str,  # okta username
                      user_password: str,  # okta password
                      partial_login: bool,  # defines whether to terminate login or do complete login
                      ):
        """
        This function performs Okta login.

        Args:
            user_id (str): Okta username.
            user_password (str): Okta password.
            partial_login (bool): Defines whether to terminate login or do complete login.

        Returns:
            tuple: A tuple containing a boolean status and a string message.
            #
        """
        status = False
        if not partial_login:
            self.explicit_wait(self.elements.OKTA_USERNAME)

            self.ui.click(self.elements.OKTA_USERNAME, retry_frequency=5, max_threshold=10, sleep_time=1,
                          AEFC="Cannot click okta username")
            self.ui.type(self.elements.OKTA_USERNAME, text=user_id, sleep_time=1, AEFC="Something wrong with username")
            self.ui.type(self.elements.OKTA_PASSWORD, text=user_password, sleep_time=1,
                         AEFC="Something wrong with passwd")
            okta_sign_in = self.ui.check_if_exists(self.elements.OKTA_SIGNIN)

            if okta_sign_in:
                self.ui.click(self.elements.OKTA_SIGNIN)

            else:
                self.sys_ops.adb_commands("adb shell input keyevent KEYCODE_BACK")
                self.ui.click(self.elements.OKTA_SIGNIN)

            try:
                self.explicit_wait(self.elements.VPN_OK_BUTTON, sleep=10)
                self.ui.click(self.elements.VPN_OK_BUTTON)
                self.explicit_wait(self.elements.ZPA_ON_STATUS)
                status = True

            except:
                self.explicit_wait(self.elements.ZPA_ON_STATUS)
                status = True
        else:
            self.bring_zcc_to_focus()
            self.logger.info("Aborting Okta Login , hitting back button on ZCC")
            result = self.sys_ops.adb_commands("adb shell input keyevent KEYCODE_BACK")
            if result[0]:
                status = True

        return status, "login to zpa" if status else "Login failed to zpa", None

    # ------------------------------------------------------------------------------------------------------------------

    def initial_zcc_login_window(self,  # helper function for initial user field at zcc login page
                                 username: str):
        """
        This function is to give a username to the ZCC app and hit the login button. This will navigate to the password page.


        """
        status = False
        try:
            self.ui.type(self.elements.USERNAME, text=username, sleep_time=1, AEFC="Something wrong with username")
            self.ui.click(self.elements.LOGIN_BUTTON, sleep_time=0, AEFC="Unable to Click Login Button")
            status = True


        except Exception as e:
            self.logger.error(f"Error :: Login Failed at USER_ID Page :: {e}")

        return status, "Able to give username in login page" if status else "Not able to give username in login page", None

    # ------------------------------------------------------------------------------------------------------------------

    def handle_aup(self, cancel_aup: bool = False):
        """
        This function handles the Acceptable Usage Policy (AUP) page. It clicks on either the decline or accept button based on the input parameter.

        Args:
            cancel_aup (bool, optional): If True, it clicks on the decline button, otherwise it clicks on the accept button. Defaults to False.

        Returns:
            tuple: A tuple containing a boolean status and a string message. The status is True if the operation was successful, otherwise False.
                   The message indicates whether the AUP button was accepted or not.


        """

        try:
            self.ui.click(
                self.elements.EULA_DECLINE_BUTTON if cancel_aup else self.elements.EULA_ACCEPT_BUTTON,
                sleep_time=1,
                AEFC="Cannot click AUP Decline" if cancel_aup else "Cannot click AUP Accept"
            )
            status = True
        except Exception as e:
            self.logger.error(f"Error :: Login Failed at AUP Page :: {e}")
            status = False

        return status, "AUP button accepted" if status else "AUP is not accepted", None

    # ------------------------------------------------------------------------------------------------------------------

    def zia_saml_login(self,
                       user_name: str,
                       password: str,
                       cancel_zia_saml_login: bool
                       ):
        """
        This function performs a ZIA SAML login using Okta
        :param self: The object itself
        :type self: object
        :param user_name: The username for the login
        :type user_name: str
        :param password: The password for the login
        :type password: str
        :type time_for_log_search: unknown
        :param cancel_zia_saml_login: Whether to cancel the ZIA SAML login or not
        :type cancel_zia_saml_login: bool

        :returns: A tuple containing a boolean status and a string message


        """
        try:
            status = False
            output_return = self.do_okta_login(user_id=user_name, user_password=password,
                                               partial_login=cancel_zia_saml_login)

            if output_return[0]:
                status = True

        except Exception as e:
            self.logger.error(f"Login failed at ZIA SAML OKTA Page :: {e}")

        return status, "Zia saml login is done" if status else "Zia saml login is not done", None

    # ------------------------------------------------------------------------------------------------------------------

    def zia_form_based_login(self,  # helper function for zia login
                             password: str,
                             cancel_zia_login: bool):
        """
        This function is used to handle form-based login for ZIA.

        Args:
            password (str): The password to be used for login.
            cancel_zia_login (bool): A flag to indicate if the login should be cancelled.

        Returns:
            tuple:  A tuple containing a boolean status, message string,None.


        """
        status = False
        msg = "Login not performed"
        try:
            if cancel_zia_login:
                self.logger.info("Aborting Login at ZIA, hitting back button on ZCC")
                result = self.sys_ops.adb_commands("adb shell input keyevent KEYCODE_BACK")
                if result[0]:
                    status = True
                    msg = "Login cancel"

            else:
                self.logger.info("Continue login to zia")
                self.explicit_wait(self.elements.PASSWORD)
                # if self.chromeos:
                #     time.sleep(1)
                #     self.sys_ops.adb_commands("adb shell input keyevent KEYCODE_BACK")

                self.ui.type(self.elements.PASSWORD, text=password, sleep_time=1, AEFC="Password button not found")
                self.ui.click(self.elements.LOGIN_BUTTON, sleep_time=0, AEFC="Unable to click Login Button")
                status = True
                msg = "Login is done to zia with form based auth"

        except Exception as e:
            self.logger.error(f"Error :: Login Failed at Form PASSWORD Page :: {e}")
        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def one_id_login(self, username, password, aup= False):
        """
        Handle the ZIA One ID login process.

        Args:
            username (str): The username to use for the login attempt.
            password (str): The password to use for the login attempt.

        Returns:
            None
        """

        status = False
        msg = ''
        # Launch the ZCC app
        cmd = f'adb shell am start -n {zcc_package_name}/{zcc_activity}'
        output = subprocess.run(cmd, capture_output=True, text=True)
        try:
            # Enter the username and click the login button
            self.ui.type(self.elements.USERNAME, text=username, sleep_time=1, AEFC="Something wrong with username")
            self.ui.click(self.elements.LOGIN_BUTTON, sleep_time=1, AEFC="Unable to Click Login Button on Zapp")
            time.sleep(1)

            # Enter the One ID password
            self.explicit_wait(self.elements.ONEID_PASSWORD)
            self.ui.type(self.elements.ONEID_PASSWORD, text=password, sleep_time=1,
                         AEFC="Something wrong with password")

            # Go back and click the sign-in button
            self.app_back()
            self.explicit_wait(self.elements.ONEID_SIGNIN)
            self.ui.click(self.elements.ONEID_SIGNIN, sleep_time=1, AEFC="Unable to Click Login Button on Zapp")
            status = True
            msg = "able to login in zapp using oneID credentials"

            if aup is True:
                self.explicit_wait(self.elements.AUP_ACCEPT)
                self.ui.click(self.elements.AUP_ACCEPT, sleep_time=1,
                              AEFC="Unable to Click Login Button on Zapp")
                time.sleep(3)
            if aup.lower() == "decline":
                pass



        except Exception as e:
            # Log any exceptions that occur during the process
            self.logger.error(f"Error :: Login Failed at USER_ID Page :: {e}")


        return status, msg


    # ------------------------------------------------------------------------------------------------------------------

    def zpa_login(self,
                  username: str,
                  password: str,
                  cancel_zpa_login: bool
                  ) -> tuple:
        """
        This function performs a ZPA login using the provided username and password.

        Args:
            user_name (str): The username for the ZPA login.
            password (str): The password for the ZPA login.
            cancel_zpa_login (bool): A flag to cancel the ZPA login.

        Returns:
            tuple:  A tuple containing a boolean status, message string,None.


        """
        status = False
        msg = 'Login failed at okta'

        try:
            output_return = self.do_okta_login(user_id=username, user_password=password,
                                               partial_login=cancel_zpa_login)

            if output_return[0]:
                status = True
                self.logger.info("Success :: OKTA Login Successful!")
                msg = 'Zpa login success'
            if cancel_zpa_login:
                if output_return[0]:
                    self.logger.info("Login aborted at ZPA SAML")
                    status = True
                    msg = "ZPa login cancel"
        except Exception as e:
            self.logger.error(f"Login failed at ZPA OKTA Page :: {e}")

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def run_macro(self,
                  step_title: str,
                  macro_name: str,
                  ) -> tuple[bool, str, None]:
        """
        This function execute macro in UI vision using ui.vision.html file.

        Args:
            step_title (str): step title for allure.
            macro_name (str): macro name for ui vision.

        Returns:
            tuple: A tuple containing a boolean status, message string,None.
        """

        # Initializing variables
        with allure.step(step_title):

            home_dir = os.path.expanduser("~")
            log_dir = os.path.join(home_dir, "Downloads")
            browser = r"C:/Program Files/Google/Chrome/Application/chrome.exe"
            html_path = os.path.join(log_dir, "ui.vision.html")
            macro = macro_name
            timeout_seconds = 360

            # Create log file
            log_name = 'log_' + datetime.datetime.now().strftime('%m-%d-%Y_%H_%M_%S') + '.txt'
            log_path = os.path.join(log_dir, log_name)

            # Initialize the macro path as per UI Vision syntax
            macro_path = r'file://' + html_path + r'?direct=1&macro=' + macro + r'&savelog=' + log_name + r'&loadmacrotree=1&storage=browser'

            self.logger.info("Log file used is {}".format(log_name))

            # Call UI vision to run the macro
            time.sleep(2)
            proc = subprocess.Popen([browser, macro_path])

            # Continuously check the log file for existence
            status_runtime = 1

            while not os.path.exists(log_path) and status_runtime < timeout_seconds:
                time.sleep(1)
                status_runtime += 1

            # Read the log file whether status is successfull
            if status_runtime < timeout_seconds:
                with open(log_path) as f:
                    status_text = f.readline()
                    full_log = f.read()

                if status_text.find('Status=OK') != -1:
                    self.logger.info("Macro {} completed successfully".format(macro))
                    time.sleep(2)
                    return (True, full_log,
                            None)  # Return True, full log and empty dictionary as no data is being returned in this function.
                else:

                    self.logger.error("Macro {} failed with error {}".format(macro, status_text))
                    return (False, "Macro {} failed with error {}".format(macro, status_text),
                            {})  # Return False, error message and empty dictionary as no data is being returned in this function.
            else:
                self.logger.error("Macro did not complete within given time: {}".format(timeout_seconds))
                time.sleep(2)

                return (False, "Macro did not complete within given time",
                        {})  # Return False, error message and empty dictionary as no data is being returned in this function.

    # ------------------------------------------------------------------------------------------------------------------------------------------------------

    @allure.step("zcc login")
    @calculate_function_execution_time
    def login(self,  # PARENT FUNCTION FOR ZCC LOGIN
              zia_user: str = None,  # zia user id
              zia_password: str = None,  # zia user password
              zpa_user: str = None,  # zpa user id
              zpa_password: str = None,  # zpa user password
              aup: bool = False,  # Defines AUP enabled or not
              zia_saml_login: bool = False,  # Defines ZIA SAML enabled or not
              zia_one_id_login: bool = False,  # Defines ZIA SAML enabled or not
              cancel_login_at_zia: bool = False,  # Defines whether to cancel login at ZIA login page
              cancel_login_at_zpa: bool = False,  # Defines whether to cancel login at ZPA login page
              cancel_aup: bool = False,  # Defines whether to cancel or decline AUP
              ):
        """
        This function is used to handle the login process for ZCC. It supports both ZIA and ZPA logins.
        It also supports AUP handling and cancelling logins at both ZIA and ZPA login pages.
        """
        #
        zia = False
        zpa = False
        status = False

        if zia_user == zia_password == zpa_user == zpa_password == None:
            # no zia user is given, check for zpa user
            self.logger.error("Neither ZIA or ZPA user given to be logged in, cannot login")

        if zia_user and zia_password:
            zia = True

        if zpa_user and zpa_password:
            zpa = True

        if zia and zpa:
            self.logger.info("Given user details are ZIA+ZPA")

        if not zia:
            self.logger.info("Given user is ZPA only user")

        if zia or zpa:
            output_return = self.initial_zcc_login_window(username=zia_user if zia else zpa_user)
            if not output_return[0]:
                return output_return

        if aup:
            output_return = self.handle_aup(cancel_aup=cancel_aup)
            if not output_return[0]:
                return output_return
            status = True
            msg = "aup login is success"
            self.logger.info(msg)

        if zia:
            if zia_saml_login:
                output_return = self.zia_saml_login(user_name=zia_user, password=zia_password,
                                                    cancel_zia_saml_login=cancel_login_at_zia)

                if not output_return[0]:
                    return output_return
                status = True
                msg = "zia saml login success"
                self.logger.info(msg)



            elif zia_one_id_login:
                self.handle_zia_one_id_login()
                status = True
                msg = "zia one id login is success"
                self.logger.info(msg)

            else:
                output_return = self.zia_form_based_login(password=zia_password, cancel_zia_login=cancel_login_at_zia)
                if not output_return[0]:
                    return output_return
                status = True
                msg = "zia form based login success"
                self.logger.info(msg)

        if zpa:
            output_return = self.zpa_login(username=zpa_user, password=zpa_password,
                                           cancel_zpa_login=cancel_login_at_zpa)
            if not output_return[0]:
                return output_return
            status = True
            msg = "zpa login is success"
            self.logger.info(msg)

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    @calculate_function_execution_time
    @allure.step("Logout ZCC")
    def logout(self,
               password: str = None,  # Password/OTP to be used for logout
               sleep_time: int = 30,  # number of seconds to sleep when executed
               failure_expected: bool = False,  # Defines whether logout should fail - used with incorrect password
               cancel_logout: bool = False  # Defines whether to cancel logout operation
               ):
        """
        This function is used to logout from the application
        :param password: Password/OTP to be used for logout. Default is None.
        :param sleep_time: Number of seconds to sleep when executed. Default is 30.
        :param failure_expected: Defines whether logout should fail - used with incorrect password. Default is False.
        :param cancel_logout: Defines whether to cancel logout operation. Default is False.
        :return:  A tuple containing a boolean status, message string,None.
        """
        status = False
        msg = ''
        if sleep_time == 0: sleep_time = 30
        try:
            self.ui.click(self.elements.LOGOUT_BUTTON, sleep_time=1 * self.sleep_factor,
                          AEFC="Unable to click Logout Button")

            if cancel_logout:
                self.logger.info("Aborting Logout")
                self.ui.click(self.elements.LOGOUT_PASSWORD_CANCEL_BUTTON, sleep_time=0,
                              AEFC="Unable to confirm Logout disable")
                status = True
                msg = "Cancel of logout is done"

            if password:
                self.ui.type(self.elements.LOGOUT_PASSWORD, text=password, sleep_time=1 * self.sleep_factor,
                             AEFC="Unable to enter password")
                self.ui.click(self.elements.LOGOUT_PASSWORD_LOGOUT_BUTTON, sleep_time=0 * self.sleep_factor,
                              AEFC="Unable to confirm Logout")

                if failure_expected:
                    self.ui.click(self.elements.LOGOUT_PASSWORD_FAILED_OK_BUTTON, sleep_time=0,
                                  AEFC="Unable to confirm Logout disable")
                    status = True
                    msg = "Failure on giving logout password"
                else:
                    self.explicit_wait(self.elements.USERNAME)
                    status = True
                    msg = "Logout with password is working fine"
            else:
                self.ui.click(self.elements.LOGOUT_PROMPT, sleep_time=1, AEFC="unable to logout prompt")
                self.explicit_wait(self.elements.USERNAME)
                status = True
                msg = "Logout is successful"
        except Exception as e:
            self.logger.error("Logout Failed at ZSTray Page ; {}\n".format(e))
        return status, msg

    @calculate_function_execution_time
    @allure.step("Enable/disable Service ZIA/ZPA/ZDX")
    def toggle_service(self,
                       service: str,  # Which service to Toggle (ZIA/ZPA/ZDX)
                       action: bool = True,  # Turn on if True, otherwise Turn off
                       password: str = None,  # Password/OTP for disabling service
                       disable_reason: str = None,
                       # Disable reason to enter while disabling any zscaler service (zia/zpa/zdx)
                       sleep_time=10,  # Number of seconds to sleep after execution
                       cancel_toggle: bool = False,  # Determines whether to cancel toggle operation or not
                       failure_expected: bool = False  # Expect that password will not be accepted. deal accordingly
                       ):

        """
        Toggle a service (ZIA, ZPA) on or off.

        :param service: str, Which service to Toggle (ZIA/ZPA)
        :param action: str, Turn on if 'on', otherwise Turn off
        :param password: str, Password/OTP for disabling service
        :param cancel_toggle: bool, Determines whether to cancel toggle operation or not
        :return: status: bool, True if operation is successful, False otherwise
                msg: str, A message describing the outcome of the operation
                None
        """

        status = False
        msg = ""

        if service == "ZIA":

            DISABLE_REASON_BOX_WITH_PASSWORD = ''
            PASSWORD_DISABLE_OK_BUTTON = ''
            TAB = self.elements.WEBSECURITY
            TOGGLE_ON = self.elements.INTERNET_SECURITY_TURN_ON_TAB
            TOGGLE_OFF = self.elements.INTERNET_SECURITY_TURN_OFF_TAB

            PASSWORD_BOX = self.elements.INTERNET_SECURITY_PASSWORD_BOX

            PASSWORD_ACCEPT_BUTTON = self.elements.INTERNET_SECURITY_PASSWORD_ACCEPT_BUTTON
            CANCEL_BUTTON = self.elements.INTERNET_SECURITY_PASSWORD_CANCEL_BUTTON
            DISABLE = self.elements.DISABLE

            try:
                # self.bring_zcc_to_focus()

                self.ui.click(TAB, sleep_time=1 * self.sleep_factor, AEFC=f"Unable to open {service} tab")

                if action:
                    self.ui.click(TOGGLE_ON, sleep_time=1 * self.sleep_factor, AEFC=f"Unable to turn on {service}")
                    self.explicit_wait(self.elements.ZIA_ON_STATUS)
                else:
                    self.ui.click(TOGGLE_OFF, sleep_time=1 * self.sleep_factor, AEFC=f"Unable to turn off {service}")

                    if password:
                        self.ui.type(PASSWORD_BOX, text=password, sleep_time=1 * self.sleep_factor,
                                     AEFC="Unable to enter password")
                        if cancel_toggle:
                            self.logger.info("Aborting Toggle")
                            self.ui.click(CANCEL_BUTTON, sleep_time=1 * self.sleep_factor,
                                          AEFC=f"Unable to cancel {service} disable")
                        else:
                            self.ui.click(DISABLE, sleep_time=1 * self.sleep_factor,
                                          AEFC=f"Unable to confirm {service} disable")

                    else:
                        if cancel_toggle:
                            self.ui.click(CANCEL_BUTTON, AEFC="Cancel button not clicked")
                        else:
                            self.ui.click(self.elements.INTERNET_SECURITY_PASSWORD_ACCEPT_BUTTON)

                error_exists = self.ui.check_if_exists(self.elements.INTERNET_SECURITY_DISABLE_INCORRECT_PASSWORD,
                                                       AEFC="Error string does not exists", boolean=True)
                if failure_expected:
                    if error_exists:
                        status = True
                        msg = "Error encountered as expected"
                        self.ui.click(CANCEL_BUTTON, AEFC="Cancel button not clicked after error")
                    else:
                        status = False
                        msg = "Error was expected but does not exists"
                else:
                    if error_exists:
                        status = False
                        msg = "Error encountered when not expected"
                        self.ui.click(CANCEL_BUTTON, AEFC="Cancel button not clicked after error")
                    else:
                        status = True
                        msg = "Toggle operation worked fine"

                return status, msg
            except Exception as e:
                self.logger.error(f"Unable to Toggle Service: {service} {action} :: {e}")
                raise Exception(f"Unable to Toggle Service: {service} {action} :: {e}")

        if service == "ZDP":
            TAB = self.elements.DATA_PREVENTION_TAB
            TOGGLE_ON = self.elements.DATA_PREVENTION_POWER_BUTTON_ON
            TOGGLE_OFF = self.elements.DATA_PREVENTION_POWER_BUTTON_OFF
            PASSOWRD_BOX = self.elements.DATA_PREVENTION_PASSWORD_BOX
            DISABLE_REASON_BOX_WITH_PASSWORD = self.elements.PASSWORD_DISABLE_REASON_BOX
            DISABLE_REASON_BOX = self.elements.DISABLE_REASON_BOX
            PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
            PASSWORD_ACCEPT_BUTTON = self.elements.DATA_PREVENTION_PASSWORD_ACCEPT_BUTTON
            CONFIRM_ACCEPT_BUTTON = self.elements.CONFIRM_ACCEPT_BUTTON
            FAILED_CONFIRM_BUTTON = self.elements.DATA_PREVENTION_PASSWORD_FAILED_OK_BUTTON
            CANCEL_BUTTON = self.elements.DATA_PREVENTION_PASSWORD_CANCEL_BUTTON

        elif service == "ZPA":
            TAB = self.elements.PRIVATE_SECURITY
            TOGGLE_ON = self.elements.PRIVATE_ACCESS_TURN_ON
            TOGGLE_OFF = self.elements.PRIVATE_ACCESS_TURN_OFF
            DISABLE = self.elements.DISABLE
            PASSWORD_BOX = self.elements.PRIVATE_ACCESS_PASSWORD_BOX
            CANCEL_BUTTON = self.elements.PRIVATE_ACCESS_PASSWORD_CANCEL_BUTTON

            status = False
            msg = ""

            try:
                self.ui.click(path=TAB, AEFC="Not able to on zcc home page")
                if action:
                    self.ui.click(TOGGLE_ON, sleep_time=1 * self.sleep_factor, AEFC=f"Unable to turn on {service}")

                else:
                    self.ui.click(TOGGLE_OFF, sleep_time=1 * self.sleep_factor, AEFC=f"Unable to turn off {service}")

                    if password:
                        self.ui.type(PASSWORD_BOX, text=password, sleep_time=1 * self.sleep_factor,
                                     AEFC="Unable to enter password")
                        if cancel_toggle:
                            self.logger.info("Aborting Toggle")
                            self.ui.click(CANCEL_BUTTON, sleep_time=1 * self.sleep_factor,
                                          AEFC=f"Unable to cancel {service} disable")
                        else:
                            self.ui.click(DISABLE, sleep_time=1 * self.sleep_factor,
                                          AEFC=f"Unable to confirm {service} disable")
                    else:
                        if cancel_toggle:
                            self.ui.click(CANCEL_BUTTON, AEFC="Cancel button not clicked")
                        else:
                            self.ui.click(self.elements.INTERNET_SECURITY_PASSWORD_ACCEPT_BUTTON)

                error_exists = self.ui.check_if_exists(self.elements.PRIVATE_ACCESS_DISABLE_INCORRECT_PASSWORD,
                                                       AEFC="Error string does not exists", boolean=True)
                if failure_expected:
                    if error_exists:
                        status = True
                        msg = "Error encountered as expected"
                        self.ui.click(CANCEL_BUTTON, AEFC="Cancel button not clicked after error")
                    else:
                        status = False
                        msg = "Error was expected but does not exists"
                else:
                    if error_exists:
                        status = False
                        msg = "Error encountered when not expected"
                        self.ui.click(CANCEL_BUTTON, AEFC="Cancel button not clicked after error")
                    else:
                        status = True
                        msg = "Toggle operation worked fine"

                return status, msg
            except Exception as e:
                self.logger.error(f"Unable to Toggle Service: {service} {action} :: {e}")
                raise Exception(f"Unable to Toggle Service: {service} {action} :: {e}")

        elif service == "ZDX":
            TAB = self.elements.DIGITAL_EXPERIENCE_TAB
            TOGGLE_ON = self.elements.DIGITAL_POWER_BUTTON_ON
            TOGGLE_OFF = self.elements.DIGITAL_POWER_BUTTON_OFF
            PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
            PASSOWRD_BOX = self.elements.DIGITAL_EXPERIENCE_PASSWORD_BOX
            CANCEL_BUTTON = self.elements.DIGITAL_EXPERIENCE_PASSWORD_CANCEL_BUTTON

            status = False

            self.ui.click(TAB, sleep_time=1 * self.sleep_factor, AEFC=f"Unable to open {service} tab")

            time.sleep(15)
            try:
                if action:

                    self.ui.click(TOGGLE_ON, sleep_time=1 * self.sleep_factor, AEFC=f"Unable to turn on {service}")
                    self.explicit_wait(self.elements.ZIA_ON_STATUS)
                    status = True
                    msg = "ZDX service toggle worked fine"
                else:

                    self.ui.click(TOGGLE_OFF, sleep_time=1 * self.sleep_factor, AEFC=f"Unable to turn off {service}")

                    # and disable_reason
                    if password:
                        # self.ui.click(DISABLE_REASON_BOX_WITH_PASSWORD,sleep_time=1 * self.sleep_factor, AEFC="Unable to enter disable reason")
                        self.ui.type(PASSOWRD_BOX, text=password, sleep_time=1 * self.sleep_factor,
                                     AEFC="Unable to enter password")
                        # password ok button
                        if cancel_toggle:
                            self.logger.info("Aborting Toggle")
                            self.ui.click(CANCEL_BUTTON, sleep_time=1 * self.sleep_factor,
                                          AEFC=f"Unable to cancel {service} disable")
                        else:
                            self.ui.click(PASSWORD_DISABLE_OK_BUTTON, sleep_time=1 * self.sleep_factor,
                                          AEFC=f"Unable to confirm {service} disable")
                    else:
                        if cancel_toggle:
                            self.ui.click(self.elements.INTERNET_SECURITY_PASSWORD_CANCEL_BUTTON)
                        else:
                            try:
                                self.ui.click(self.elements.INTERNET_SECURITY_PASSWORD_ACCEPT_BUTTON)
                            except:
                                self.ui.click(self.elements.INTERNET_SECURITY_PASSWORD_CHROMEOS_ACCEPT_BUTTON)

                                self.explicit_wait(self.elements.ZIA_OFF_STATUS)

                error_exists = self.ui.check_if_exists(self.elements.PRIVATE_ACCESS_DISABLE_INCORRECT_PASSWORD,
                                                       AEFC="Error string does not exists", boolean=True)
                if failure_expected:
                    if error_exists:
                        status = True
                        msg = "Error encountered as expected"
                        self.ui.click(CANCEL_BUTTON, AEFC="Cancel button not clicked after error")
                    else:
                        status = False
                        msg = "Error was expected but does not exists"
                else:
                    if error_exists:
                        status = False
                        msg = "Error encountered when not expected"
                        self.ui.click(CANCEL_BUTTON, AEFC="Cancel button not clicked after error")
                    else:
                        status = True
                        msg = "Toggle operation worked fine"

                return status, msg
            except Exception as e:
                self.logger.error(f"Unable to Toggle Service: {service} {action} :: {e}")
                raise Exception(f"Unable to Toggle Service: {service} {action} :: {e}")

    @calculate_function_execution_time
    @allure.step("Update Policy")
    def update_policy(self):
        """
        This function updates the policy.

        Args:
            validate_through_logs (bool): If True, validates through logs.

        Returns:
            tuple:  A tuple containing a boolean status, message string,None.

        """

        status = False
        msg = "Update policy is failed"
        try:

            self.ui.click(self.elements.NOTIFICATION, sleep_time=1 * self.sleep_factor,
                          AEFC="Unable to click More button on ZCC")

            # -------- Swiper/Scroller Start -----------
            size = self.ui.driver.get_window_size()
            for _ in range(10):
                start_y = size['height'] * 0.3
                end_y = size['height'] * 0.2
                self.ui.driver.swipe(size['width'] // 2, start_y, size['width'] // 2, end_y, 1000)
                element_visibility = self.ui.check_if_exists(path=self.elements.UPDATE, boolean=True)

                if element_visibility:
                    break
            # -------- Swiper/Scroller End -----------

            self.ui.click(self.elements.UPDATE, sleep_time=0, AEFC="Unable to click Update Policy button")
            time.sleep(10)

            for i in range(30):
                time.sleep(3)
                try:
                    output = False
                    self.ui.click(path=self.elements.NOTIFICATIONS_TAB_XPATH, sleep_time=1, AEFC='Unable to click on'
                                                                                                 'notification tab')
                    output = self.ui.check_if_exists(path = self.elements.UPDATE_POLICY_NOTIFICATION, sleep_time=1,
                                            AEFC='Not able to find update policy notification message')

                    if output:
                        status = True
                        msg = "Update policy is successful"
                        break
                except Exception as e:
                    self.logger.info(e)


        except Exception as e:
            self.logger.info(e)

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    @allure.step("Restart Service")
    def restart_service(self, object_check = 'Internet Security is Connected.'):
        """
        Restart the service.

        This function restarts the service by clicking on specific UI elements. It also checks if the service is restarted
        successfully by looking for specific text on the UI.

        Args:
            sleep_time (int): Number of seconds to sleep after execution. Default is 60.

        Returns:
            tuple:  A tuple containing a boolean status, message string,None indicating whether the restart service was
            successful or not.

        """
        #
        status = False
        msg = ""
        self.ui.click("//*[@class='android.widget.TextView' and @text='Notifications']", sleep_time=1)

        try:
            self.ui.click(self.elements.NOTIFICATION_DELETE_LOCATOR, sleep_time=1, AEFC="Unable to click")
            self.ui.click("//*[@class='android.widget.TextView' and @text='YES, CLEAR ALL']", sleep_time=1)

        except:
            self.ui.click(self.elements.REPORT_ISSUE_CLOSE)

        try:
            self.ui.click(self.elements.NOTIFICATION, sleep_time=0, AEFC="Unable to click More button on ZCC")
            self.ui.click(self.elements.RESTART, sleep_time=3, AEFC="Unable to click Restart Service button on ZCC")

            self.ui.click(self.elements.YES_RESTART, sleep_time=0, AEFC="Unable to confirm ZCC Restart")

            time.sleep(30)

            self.ui.click("//*[@class='android.widget.TextView' and @text='Notifications']", sleep_time=1)

            status_out = self.ui.check_if_exists(
                f"//*[@class='android.widget.TextView' and @text='{object_check}']",
                AEFC="Service not started after restart of service")

            # self.ui.click(self.elements.WEBSECURITY, sleep_time=0, AEFC="Unable to click on Internet Security")
            # WebDriverWait(self.ui.driver, 10).until(
            #     EC.visibility_of_element_located((By.XPATH, self.elements.ZIA_ON_STATUS)))
            if status_out:
                status = True
                msg = "restart service is successful"
        except Exception as e:
            self.logger.error("Restart Service Failed ; {}\n".format(e))
            msg = "restart service is not successful"

        self.logger.info("Success :: Restarted Service!")

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    @calculate_function_execution_time
    def export_logs(self,
                    after_login: bool = True,
                    sleep_time: int = 120):
        """
        Export logs function for the application.

        :param self: Instance of the class.
        :param after_login: Boolean to check if login is required.
        :param sleep_time: Time to wait after performing an operation.
        :return: A tuple containing a boolean status, a message and none
        """
        status = False
        status, msg = self.fetch_logs()
        msg = " able to fetch logs"

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    @calculate_function_execution_time
    def uninstall_zcc(self,

                      after_login: bool = False,
                      failure_expected: bool = False,
                      password: str = None,
                      cancel_uninstall: bool = False):

        """
        This function is used to uninstall ZCC.

        Args:
            after_login (bool, optional): Whether to perform the action after login or not. Defaults to False.

        Returns:
            tuple: A tuple containing the status of the operation and a message,None

        """
        status = False
        msg = ""
        PASSWORD_BOX = self.elements.UNINSTALL_PASSWORD_BOX
        CANCEL_BUTTON = self.elements.UNINSTALL_CANCEL
        PASSWORD_BOX_UNINSTALL_BUTTON = self.elements.UNINSTALL_PASSWORD_BOX_OK_BUTTON

        if not after_login:
            try:
                self.ui.click(self.elements.BURGER_MENU, sleep_time=1, AEFC="Unable to click on burger menu")
                self.ui.click(self.elements.UNINSTALL_WITHOUT_LOGIN, sleep_time=1,
                              AEFC="Unable to click on burger menu")

                self.ui.click(self.elements.UNINSTALL_BUTTON, sleep_time=1, AEFC="Unable to click on burger menu")
                self.ui.click("//*[@class='android.widget.Button' and @text='OK']", sleep_time=1,
                              AEFC="Unable to click on burger menu")
                time.sleep(10)
                status = True
                msg = 'Uninstall is successful without login'
            except:
                msg = "Uninstall not successful"
        else:
            try:
                self.ui.click(self.elements.NOTIFICATION)
                self.ui.click(self.elements.UNINSTALL)

                if password:
                    self.ui.type(PASSWORD_BOX, text=password, sleep_time=1 * self.sleep_factor,
                                 AEFC="Unable to enter password")
                    if cancel_uninstall:
                        self.logger.info("Aborting Uninstall")
                        self.ui.click(CANCEL_BUTTON, sleep_time=1 * self.sleep_factor,
                                      AEFC=f"Unable to cancel uninstall")
                    else:
                        self.ui.click(PASSWORD_BOX_UNINSTALL_BUTTON, sleep_time=1 * self.sleep_factor,
                                      AEFC=f"Unable to click on Uninstall")
                else:
                    if cancel_uninstall:
                        self.logger.info("Aborting Uninstall")
                        self.ui.click(CANCEL_BUTTON, sleep_time=1 * self.sleep_factor,
                                      AEFC=f"Unable to cancel uninstall")
                    else:
                        self.ui.click(PASSWORD_BOX_UNINSTALL_BUTTON, sleep_time=1 * self.sleep_factor,
                                      AEFC=f"Unable to click on Uninstall")

                error_exists = self.ui.check_if_exists(self.elements.UNINSTALL_INCORRECT_PASSWORD,
                                                       AEFC="Error string not found", boolean=True)
                if failure_expected:
                    if error_exists:
                        status = True
                        msg = "Uninstall success after login"
                        self.ui.click(CANCEL_BUTTON, AEFC="Cancel button not clicked after error")
                    else:
                        status = False
                        msg = "Error was expected but does not exists"
                else:
                    if error_exists:
                        status = False
                        msg = "Error encountered when not expected"
                        self.ui.click(CANCEL_BUTTON, AEFC="Cancel button not clicked")
                    else:
                        status = True
                        msg = "Uninstall operation successful from ZApp"
            except Exception as e:
                self.logger.info("Exception: " + str(e))

                msg = "Uninstall not success"
        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def fetch_proxy_server(self):
        """
        This function fetches the proxy server details.
        Returns:
            tuple: A tuple containing the status of the operation, message, none

        """

        # Update the policy
        self.update_policy()

        # Click on the web security element with a sleep time of 1 second
        self.ui.click(self.elements.WEBSECURITY, sleep_time=1, AEFC="Unable to click")

        # Initialize variables
        status = False
        msg = ''
        proxy_server_ip = ''

        try:
            # Click on the proxy server element and return the text value
            proxy_server = self.ui.click(self.elements.PROXY_SERVER, do_click=False, return_text=True)
            proxy_server = proxy_server.split(':')
            self.logger.info(proxy_server[0])

            # Set status to True and update the message and proxy server IP
            status = True
            msg = 'Able to fetch sme ip'
            proxy_server_ip = proxy_server[0]

        except:
            msg = 'not able to fetch sme ip detail'

        # Return the status, message, and proxy server IP
        return status, msg, proxy_server_ip, proxy_server

    def validate_traffic_going_through_tunnel(self,
                                              is_zia_enable: bool = False,
                                              is_zia_disable: bool = False):

        """
        Validate if traffic is going through tunnel based on Zscaler status.

        This function checks if traffic is going through Zscaler or not based on its enablement or disablement.
        It uses ADB commands to open a URL and then checks for the presence of specific elements on the page.
        If the element indicating traffic is not going through Zscaler is found, the traffic is considered as not going
        through the cloud. If the element indicating traffic is going through Zscaler is found, the traffic is considered
        as going through the cloud.

        Args:
            is_zia_enable (bool): If True, it indicates that Zscaler should be enabled.
            is_zia_disable (bool): If True, it indicates that Zscaler should be disabled.

        Returns:
            tuple: A tuple containing a boolean status and a string message. The status indicates if the traffic is going
            through the tunnel or not, and the message provides more information on the result.


        """
        status = False
        msg = ''
        if is_zia_disable:
            cmd = "adb shell am start -a android.intent.action.VIEW -d http://ip.zscaler.com"
            subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=subprocess.PIPE)

            try:
                WebDriverWait(self.ui.driver, 120).until(
                    EC.visibility_of_element_located((By.XPATH, self.elements.NOT_GOING_VIA_ZSCALER)))

            except:
                status = False
            trace_output = self.ui.check_if_exists(self.elements.NOT_GOING_VIA_ZSCALER)

            if trace_output:
                status = True
                msg = "Traffic is not going through cloud"
                self.logger.info("Traffic is not going through cloud")

            else:
                trace_output = self.ui.check_if_exists(self.elements.TRAFFIC_VIA_ZSCALER)
                if trace_output:
                    status = False
                    msg = "Traffic is going through cloud when not expected"

        if is_zia_enable:

            cmd = "adb shell am start -a android.intent.action.VIEW -d http://ip.zscaler.com"
            subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=subprocess.PIPE)

            trace_output = self.ui.check_if_exists(self.elements.NOT_GOING_VIA_ZSCALER)
            if trace_output is False:
                status = True
                msg = "Traffic is going through Tunnel"
        self.bring_zcc_to_focus()
        return status, msg

    def validate_zia_tunnel(self,
                            zia_status: str = 'on'):

        """
        Validate the ZIA tunnel status.

        This function checks the ZIA tunnel status and returns a boolean value
        indicating whether the status matches the expected value, as well as a
        message describing the current status.

        Args:
            zia_status (str): The expected status of the ZIA tunnel, either 'on' or 'off'.

        Returns:
            tuple: A tuple containing a boolean value indicating whether the status
            matches the expected value, and a string message describing the current
            status and none

        """
        status = False
        if zia_status == 'on':
            self.ui.driver.find_element(By.XPATH, self.elements.WEBSECURITY).click()
            time.sleep(3)
            out_status = self.ui.check_if_exists(self.elements.ZIA_ON_STATUS)
            if out_status:
                self.logger.info("ZIA Status is ON")
                status = True
                msg = "ZIA service is on"
        else:
            self.ui.driver.find_element(By.XPATH, self.elements.WEBSECURITY).click()
            out_status = self.ui.check_if_exists(self.elements.ZIA_OFF_STATUS)
            if out_status:
                self.logger.info("ZIA state is OFF")
                status = True
                msg = "Zia service is off"
        return status, msg

    def validate_zpa_tunnel(self,
                            zpa_status: str = 'on'):

        """
        Validate the ZPA tunnel status.

        This function checks the ZPA tunnel status based on the provided parameter. If the zpa_status is 'on', it checks
        for the ZIA_ON_STATUS element on the page. If the zpa_status is 'off', it checks for the ZIA_OFF_STATUS element.
        If the corresponding element is found, it sets the status to True and returns a message indicating the ZPA service
        status. Otherwise, it sets the status to False.

        :param self: Instance of the class
        :param zpa_status: Expected ZPA tunnel status, either 'on' or 'off' (default is 'on')
        :return: A tuple containing a boolean status and a message indicating the ZPA service status,none

        #
        """
        status = False
        if zpa_status == 'on':
            self.ui.driver.find_element(By.XPATH, self.elements.PRIVATE_SECURITY).click()
            out_status = self.ui.check_if_exists(self.elements.ZIA_ON_STATUS)
            if out_status:
                status = True
                msg = "ZPA service is on"

        else:
            self.ui.driver.find_element(By.XPATH, self.elements.PRIVATE_SECURITY).click()
            out_status = self.ui.check_if_exists(self.elements.ZIA_OFF_STATUS)
            if out_status:
                status = True
                msg = "ZPA service is off"

        return status, msg

    def packet_capture(self,
                       action: str = 'start',
                       clear_notification: bool = True):

        """
        This function is used to start or stop packet capture based on the input action.

        Args:
            action (str, optional): The action to perform, either 'start' or 'stop'. Defaults to 'start'.

        Returns:
            tuple:  A tuple containing a boolean status, message string,None.
        """

        status = False
        msg = f"Packet capture {action} failed"
        if action == 'start':
            if clear_notification:
                self.ui.click("//*[@class='android.widget.TextView' and @text='Notifications']", sleep_time=1)
                try:
                    self.ui.click(self.elements.REPORT_ISSUE_CLOSE)
                except:
                    self.ui.click(self.elements.NOTIFICATION_REMOVE, sleep_time=1, AEFC="Unable to click")
                    self.ui.click(f"//*[@class='android.widget.TextView' and @resource-id='{zcc_package_name}:id/ok']",
                                  sleep_time=1)

            self.ui.click(self.elements.NOTIFICATION)
            self.ui.click(
                f"//*[@class='android.widget.Switch' and @resource-id='{zcc_package_name}:id/packetCaptureSwitch']",
                sleep_time=10, AEFC="Unable to click packet capture")

            self.ui.click("//*[@class='android.widget.TextView' and @text='Notifications']", sleep_time=1)
            output = self.ui.check_if_exists("//*[@class='android.widget.TextView' and @text='Started Packet Capture']",
                                             AEFC="Started Packet Capture not found", boolean=True)
            if output:
                status = True
                msg = "Packet capture started"

        else:
            if clear_notification:
                self.ui.click("//*[@class='android.widget.TextView' and @text='Notifications']", sleep_time=1)
                try:
                    self.ui.click(self.elements.REPORT_ISSUE_CLOSE)
                except:
                    self.ui.click(self.elements.NOTIFICATION_REMOVE, sleep_time=1, AEFC="Unable to click")
                    self.ui.click(f"//*[@class='android.widget.TextView' and @resource-id='{zcc_package_name}:id/ok']",
                                  sleep_time=1)

            self.ui.click(self.elements.NOTIFICATION)
            self.ui.click(
                f"//*[@class='android.widget.Switch' and @resource-id='{zcc_package_name}:id/packetCaptureSwitch']",
                sleep_time=10, AEFC="Unable to click packet capture")

            self.ui.click("//*[@class='android.widget.TextView' and @text='Notifications']", sleep_time=1)
            output = self.ui.check_if_exists("//*[@class='android.widget.TextView' and @text='Stopped Packet Capture']",
                                             AEFC="Stopped Packet Capture not found", boolean=True)
            if output:
                status = True
                msg = "Packet capture stopped"

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def zpa_re_authentication(self,
                              user_id: str = None,
                              user_password: str = None
                              ):
        """
        This function is used to perform re-authentication in ZPA
        :param user_id: User ID for authentication. Default is None.
        :type user_id: str
        :param user_password: User password for authentication. Default is None.
        :type user_password: str

        :return:  A tuple containing a boolean status, message string,None.

        """

        status = False
        msg = ""
        try:

            self.ui.click(self.elements.PRIVATE_SECURITY, AEFC=" Not able to click on ZPA in ZCC")
            self.ui.click(self.elements.AUTHENTICATE)
            WebDriverWait(self.ui.driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, self.elements.OKTA_USERNAME)))
            self.do_okta_login(user_id=user_id, user_password=user_password, partial_login=False)
            WebDriverWait(self.ui.driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, self.elements.AUTHENTICATED)))
            status = True
            msg = "able to re-login on session expiry"
        except Exception as e:
            self.logger.info("Not able to re-authentication")
            msg = "not able to re-login on session expiry"
        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def no_packet_capture_notification(self):
        """
        This function is used to validate the notification of packet capture stopped present under
        the notification tab after disabling it or default time out.

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False
        msg = ''
        self.logger.info("Moving to more option of ZCC")
        self.ui.click(self.elements.NOTIFICATION)
        time.sleep(5)
        self.logger.info("Checking for packet capture stopped notification is present")

        status = self.ui.check_if_exists("//*[@class='android.widget.TextView' and @text='Stopped Packet Capture']",
                                         AEFC="Stopped Packet Capture not found")

        if status:
            msg = 'Stopped Packet Capture'

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def packet_capture_present(self):
        """
        This function is used to validate the on login option for packet capture is available to
        an Android user.

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        self.logger.info("Moving to more option of ZCC")
        self.ui.click(self.elements.NOTIFICATION)
        time.sleep(2)
        self.logger.info("Checking for packet capture option is present or not")
        status = self.ui.check_if_exists(self.elements.PACKET_CAPTURE_PRESENT,
                                         AEFC="Packet capture option is not present ")

        if status:
            msg = 'Packet capture option is present'

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def packet_capture_not_present(self):
        """
        This function is used to validate the on login option for packet capture is not available to
        an Android user if capture packet is disabled from MA.

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False
        msg = ''
        self.logger.info("Moving to more option of ZCC")
        self.ui.click(self.elements.NOTIFICATION)
        time.sleep(2)
        self.logger.info("Checking for packet capture is not present")
        status = self.ui.check_if_exists(self.elements.PACKET_CAPTURE_PRESENT, expect_failure=True,
                                         AEFC="Packet capture option is present ")
        if status:
            msg = 'Packet capture option is not present'

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def packet_knob_off(self):
        """
        This function is used to verify that packet capture is off by default after logging into the ZCC app.

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False
        msg = ''

        self.logger.info("Moving to more option of ZCC")
        self.ui.click(self.elements.NOTIFICATION)

        time.sleep(2)
        self.logger.info("Checking for packet capture off")
        status = self.ui.check_if_exists(self.elements.PACKET_CAPTURE_OFF, AEFC="Packet capture is not off")
        if status:
            msg = 'Packet capture is off'

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def packet_knob_on(self):
        """
        This function is used to verify that packet capture is on.

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        self.logger.info("Moving to more option of ZCC")
        self.ui.click(self.elements.NOTIFICATION)

        time.sleep(2)
        self.logger.info("Checking for packet capture off")
        status = self.ui.check_if_exists(self.elements.PACKET_CAPTURE_ON, AEFC="Packet capture is not on")
        if status:
            msg = 'Packet capture is on'

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def report_issue(self,
                     name: str = 'Automation dummy issue',
                     cc_email: str = '<EMAIL>',
                     issue_priority: str = 'Low',
                     issue_type: str = 'Other',
                     issue_comment: str = "This is dummy issue please ignore") -> bool:
        """
        This method is to report an issue in ZCC.
        It returns a boolean status indicating the success of the operation.
        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """
        status = False
        NAME_REPORT_ISSUE = "//*[@class='android.widget.EditText' and @text='Name [required]']"
        REPORT_ISSUE = "//*[@class='android.widget.TextView' and @text='Report an Issue']"
        SUCCESS_REPORT_ISSUE = "//*[@class='android.widget.TextView' and @text='Report issue successful']"
        REPORT_ISSUE_CLOSE = "//*[@class='android.widget.TextView' and @text='CLOSE']"
        REPORT_ISSUE_NAME = f'//android.widget.EditText[@resource-id="{zcc_package_name}:id/userName"]'
        REPORT_ISSUE_CC_EMAIL = f'//android.widget.EditText[@resource-id="{zcc_package_name}:id/emailCC"]'
        REPORT_ISSUE_COMMENTS = f'//android.widget.EditText[@resource-id="{zcc_package_name}:id/comments"]'

        REPORT_ISSUE_TYPE = '//android.widget.TextView[@resource-id="android:id/text1" and @text="{}"]'.format(
            issue_type)
        REPORT_ISSUE_ACTION = f'//android.widget.Button[@resource-id="{zcc_package_name}:id/send"]'
        REPORT_ISSUE_PRIORITY_DROP = "//*[@class='android.widget.TextView' and @text='Urgent']"
        REPORT_ISSUE_PRIORITY = '//android.widget.TextView[@resource-id="android:id/text1" and @text="{}"]'.format(
            issue_priority)
        REPORT_ISSUE_TYPE_DROP = "//*[@class='android.widget.TextView' and @text='Authentication']"
        self.clear_notification()

        try:
            self.logger.info("Moving to more option of ZCC")
            self.ui.click(self.elements.NOTIFICATION)
            time.sleep(2)

            self.logger.info("report an issue")
            self.ui.click(self.elements.REPORT_ISSUE, AEFC="Unable to click on report an issue option")
            self.ui.type(path=REPORT_ISSUE_NAME, text=name, sleep_time=2)
            self.ui.type(path=REPORT_ISSUE_CC_EMAIL, text=cc_email, sleep_time=2)
            # self.ui.type(path=REPORT_ISSUE_TYPE, text=issue_type, sleep_time=2)
            self.ui.type(path=REPORT_ISSUE_COMMENTS, sleep_time=2, text=issue_comment)

            self.ui.click(path=REPORT_ISSUE_PRIORITY_DROP, AEFC='Not able to click on priority')

            self.ui.click(path=REPORT_ISSUE_PRIORITY, AEFC="Not able to select priority of issue")

            self.ui.click(path=REPORT_ISSUE_TYPE_DROP, AEFC='Not able to click on priority')
            self.ui.click(path=REPORT_ISSUE_TYPE, AEFC="Not able to select priority of issue")

            self.ui.click(REPORT_ISSUE_ACTION, AEFC='Not able to click on send')

            output = self.explicit_wait(
                xpath=f'//android.widget.TextView[@resource-id="{zcc_package_name}:id/errorText"]', sleep=300)
            output = self.ui.click(path=self.elements.REPORT_ISSUE_OK, AEFC='Not able to click on send',
                                   return_text=True)
            status = True
            msg = "Report an issue is done"

        except Exception as e:
            self.logger.info("Report an issue failed")
            msg = "Report an issue failed"
            status = False

        # self.ui.click(self.elements.NOTIFICATION)
        # self.ui.click("//*[@class='android.widget.Switch' and @resource-id='{zcc_package_name}:id/packetCaptureSwitch']",sleep_time=10, AEFC="Unable to click packet capture")

        self.ui.click("//*[@class='android.widget.TextView' and @text='Notifications']", sleep_time=1)
        output = self.ui.check_if_exists("//*[@class='android.widget.TextView' and @text='Report issue successful']",
                                         AEFC="Reported the issue not success", boolean=True)

        if output:
            status = True
            msg = "Reported the issue"

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def clear_logs(self):
        """
        This method is to clear ZCC logs.
        It returns a tuple with a boolean status and a message string.
        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """
        status = False
        msg = ""
        self.ui.click(self.elements.NOTIFICATION, AEFC="Unable to click")
        time.sleep(2)
        self.ui.click(self.elements.CLEAR_LOGS, AEFC="Unable to click")
        time.sleep(2)
        self.ui.click(self.elements.CLEAR_ALL, AEFC="Unable to click on clear all")

        # status = WebDriverWait(self.ui.driver, 10).until(EC.presence_of_element_located((By.XPATH, self.elements.CLEAR_LOGS_TOAST)))
        status = True
        if status:
            msg = "Logs cleared for ZCC"

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def fetch_logs(self):
        """
        This function fetches logs from the Zscaler test app.

        :return: A tuple containing a boolean status and a message indicating the result of the log fetching attempt.
        :rtype: tuple[bool, str]

        Generated by Dhruv Bansal, assisted by ZCoder on 15-July-2024 <Please retain this marker>
        """

        status = False
        cmd = 'adb shell am start -n com.zscaler.andrtestapp/com.zscaler.andrtestapp.activity.MainActivity'
        output = subprocess.run(cmd, capture_output=True, text=True)
        if 'Starting: Intent { cmp=com.zscaler.andrtestapp/.activity.MainActivity }' in output.stdout:
            self.ui.logger.info("Test app in foreground ")

        time.sleep(2)

        # # -------- Swiper/Scroller Start -----------
        # size = self.ui.driver.get_window_size()
        # for _ in range(10):
        #     start_y = size['height'] * 0.3
        #     end_y = size['height'] * 0.2
        #     self.ui.driver.swipe(size['width'] // 2, start_y, size['width'] // 2, end_y, 1000)
        #     element_visibility = self.ui.check_if_exists(path=self.elements.GET_ZCC_LOGS_TEST_APP, boolean=True)
        #
        #     if element_visibility:
        #         break
        # -------- Swiper/Scroller End -----------

        self.ui.click(self.elements.GET_ZCC_LOGS_TEST_APP, AEFC="Unable to click on get logs radio button")

        self.ui.click(self.elements.PERFORM_ACTION_TEST_APP, AEFC="Unable to click on Perform action ")
        time.sleep(15)
        status = True
        msg = "Log fetch successful"

        # cmd = 'adb shell am force-stop com.zscaler.andrtestapp'
        # output = subprocess.run(cmd, capture_output=True, text=True)

        self.ui.back_page()

        # self.ui.terminate_app('com.zscaler.andrtestapp/com.zscaler.andrtestapp.activity.MainActivity')

        cmd = f'adb shell am start -n {zcc_package_name}/{zcc_activity}'  # if not self.chromeos else 'adb shell am start -n zscaler.com.zschromeosapp/{zcc_activity}'
        output = subprocess.run(cmd, capture_output=True, text=True)
        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def verify_adhoc_cloud_name(self, cloud_name: str = None):
        """
        This function is used to verify the adhoc cloud name in the application.

        :param cloud_name: The cloud name to verify. Default is None.
        :type cloud_name: str

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False

        self.ui.click(self.elements.OPEN_NAVIGATION_DRAWER, AEFC="Not able to open navigation drawer")

        if cloud_name:
            cloud_xpath = f'//android.widget.CheckedTextView[@resource-id="{zcc_package_name}:id/design_menu_item_text" and @text="Cloud name : {cloud_name}"]'
        else:
            cloud_xpath = f'//android.widget.CheckedTextView[@resource-id="{zcc_package_name}:id/design_menu_item_text" and @text="Cloud Name"]'

        try:
            status = self.ui.check_if_exists(cloud_xpath)
            msg = "Adhoc cloud name verified correctly"

        except Exception as e:
            msg = f"error occurred {e}"

        cmd = 'adb shell input keyevent KEYCODE_BACK'
        out = subprocess.run(cmd, capture_output=True, text=True)
        out = subprocess.run(cmd, capture_output=True, text=True)

        cmd = f'adb shell am start -n {zcc_package_name}/{zcc_activity}'
        out = subprocess.run(cmd, capture_output=True, text=True)
        out = subprocess.run(cmd, capture_output=True, text=True)

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def enter_adhoc_cloud_name(self, cloud_name: str):
        """
        This function is used to enter an adhoc cloud name for login.

        :param cloud_name: The cloud name to enter.
        :type cloud_name: str

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False
        msg = ''

        ADHOC_NAME_TOAST = f'//android.widget.Toast[@text="Cloud name saved as {cloud_name}"]'
        self.ui.click(self.elements.OPEN_NAVIGATION_DRAWER, AEFC="Not able to open navigation drawer")
        adhoc_cloud_name = f'//android.widget.CheckedTextView[@resource-id="{zcc_package_name}:id/design_menu_item_text" and @text="Cloud Name"]'

        self.ui.click(adhoc_cloud_name, AEFC="Not able to click on adhoc cloud name")
        self.ui.type(self.elements.GIVE_ADHOC_NAME, text=cloud_name, AEFC="Not able to enter adhoc cloud name")
        time.sleep(2)
        self.ui.click(self.elements.SAVE_ADHOC_NAME, AEFC='Not able to click on save adhoc cloud name')

        status = WebDriverWait(self.ui.driver, 5).until(EC.presence_of_element_located((By.XPATH, ADHOC_NAME_TOAST)))

        if status:
            msg = "adhoc name entered successfully"

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def clear_adhoc_cloud_name(self, cloud_name: str):
        """
        This function is used to clear an adhoc cloud name.

        :param cloud_name: The cloud name to clear.
        :type cloud_name: str

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False
        msg = ''

        self.ui.click(self.elements.OPEN_NAVIGATION_DRAWER, AEFC="Not able to open navigation drawer")
        clear_adhoc_cloud_name = f'//android.widget.CheckedTextView[@resource-id="{zcc_package_name}:id/design_menu_item_text" and @text="Cloud name : {cloud_name}"]'

        self.ui.click(clear_adhoc_cloud_name, AEFC="Not able to click on adhoc cloud name")
        self.ui.click(self.elements.CLEAR_ADHOC_CLOUD_NAME, AEFC="Not able to clear adhoc cloud name")
        status = WebDriverWait(self.ui.driver, 5).until(
            EC.presence_of_element_located((By.XPATH, self.elements.ADHOC_CLOUD_NAME_RESET_TOAST)))

        if status:
            msg = "Adhoc cloud name cleared successfully"

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def adhoc_cloud_name_max_char(self, cloud_name: str):
        """
        This function is used to test the maximum character limit for an adhoc cloud name.

        :param cloud_name: The cloud name to test.
        :type cloud_name: str

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False
        msg = ""

        self.ui.click(self.elements.OPEN_NAVIGATION_DRAWER, AEFC="Not able to open navigation drawer")
        self.ui.click(self.elements.ADHOC_CLOUD_NAME, AEFC="Not able to click on adhoc cloud name")
        self.ui.type(self.elements.GIVE_ADHOC_NAME, text=cloud_name, AEFC="Not able to enter adhoc cloud name")
        time.sleep(2)
        self.ui.click(self.elements.SAVE_ADHOC_NAME, AEFC='Not able to click on save adhoc cloud name')

        text = self.ui.click(path=f'//android.widget.TextView[@resource-id="{zcc_package_name}:id/errorText"]',
                             do_click=False, return_text=True)

        self.ui.click(self.elements.CLEAR_ADHOC_CLOUD_NAME, AEFC="Not able to clear adhoc cloud name")
        status = WebDriverWait(self.ui.driver, 5).until(
            EC.presence_of_element_located((By.XPATH, self.elements.ADHOC_CLOUD_NAME_RESET_TOAST)))

        if text == 'Invalid cloud name. Less than 20 characters allowed.' and status:
            status = True
            msg = text
        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def adhoc_cloud_name_special_char(self, cloud_name: str):
        """
        This function is used to test the special character validation for an adhoc cloud name.

        :param cloud_name: The cloud name to test.
        :type cloud_name: str

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False
        try:
            self.ui.click(self.elements.OPEN_NAVIGATION_DRAWER, AEFC="Not able to open navigation drawer")
            self.ui.click(self.elements.ADHOC_CLOUD_NAME, AEFC="Not able to click on adhoc cloud name")
            self.ui.type(self.elements.GIVE_ADHOC_NAME, text=cloud_name, AEFC="Not able to enter adhoc cloud name")
            time.sleep(2)
            self.ui.click(self.elements.SAVE_ADHOC_NAME, AEFC='Not able to click on save adhoc cloud name')

            text = self.ui.click(path=f'//android.widget.TextView[@resource-id="{zcc_package_name}:id/errorText"]',
                                 do_click=False, return_text=True)
            self.ui.click(self.elements.CLEAR_ADHOC_CLOUD_NAME, AEFC="Not able to clear adhoc cloud name")
            status = WebDriverWait(self.ui.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, self.elements.ADHOC_CLOUD_NAME_RESET_TOAST)))

            if text == 'cloud name validation failed' and status:
                status = True
                msg = text

        except Exception as e:
            status = False
            msg = 'cloud name validation for special char failed'

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def empty_adhoc_cloud_name(self):
        """
        This function is used to test the empty cloud name validation for an adhoc cloud name.

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False
        try:
            self.ui.click(self.elements.OPEN_NAVIGATION_DRAWER, AEFC="Not able to open navigation drawer")
            self.ui.click(self.elements.ADHOC_CLOUD_NAME, AEFC="Not able to click on adhoc cloud name")
            time.sleep(2)
            self.ui.click(self.elements.SAVE_ADHOC_NAME, AEFC='Not able to click on save adhoc cloud name')

            text = self.ui.click(path=f'//android.widget.TextView[@resource-id="{zcc_package_name}:id/errorText"]',
                                 do_click=False, return_text=True)

            self.ui.click(self.elements.CLEAR_ADHOC_CLOUD_NAME, AEFC="Not able to clear adhoc cloud name")
            status = WebDriverWait(self.ui.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, self.elements.ADHOC_CLOUD_NAME_RESET_TOAST)))

            if 'Cloud name is empty' in text and status:
                status = True
                msg = text

        except Exception as e:
            msg = "Not able to verify giving empty cloud name"
            status = False

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def verify_device_policy(self,
                             policy_name: str):

        """
        This function is used to verify the presence of a device policy.

        :param policy_name: The policy name to verify.
        :type policy_name: str

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """
        status = False
        msg = ""
        self.ui.click(self.elements.NOTIFICATION, AEFC="Unable to click")

        status = self.ui.check_if_exists(
            path=f'//android.widget.TextView[@resource-id="{zcc_package_name}:id/value" and @text="{policy_name}"]')

        if status:
            msg = f"Device policy {policy_name} is present"
        return status, msg

    def verify_trusted_network_status(self,
                                      nw_type: str):

        """
        This function is used to verify the trusted network status.

        :param nw_type: The network type to verify.
        :type nw_type: str

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False

        self.ui.click(path=self.elements.INTERNET_SECURITY_TAB, AEFC="Not able to on zcc home page")

        if nw_type == 'on_trust':
            time.sleep(5)
            text = self.ui.click(path=self.elements.NW_SERVICE_STATUS, do_click=False, return_text=True)
            if text == "Trusted Network":
                status = True
                msg = "Trusted Network"
        else:
            time.sleep(5)
            text = self.ui.click(path=self.elements.NW_SERVICE_STATUS, do_click=False, return_text=True)
            if text == "Off Trusted Network":
                status = True
                msg = "Off Trusted Network"
        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def verify_time_connected(self, service_type: str = 'ZIA'):
        """
        This function is used to verify the time connected.

        :return: A boolean status.
        :rtype: bool

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False

        if service_type == 'ZIA':
            zia_icon = self.elements.INTERNET_SECURITY_TAB
            self.ui.click(path=zia_icon, AEFC="Not able to click on zia service tab", sleep_time=2)
        elif service_type == 'ZDX':
            zdx_icon = self.elements.DIGITAL_EXPERIENCE_TAB
            self.ui.click(path=zdx_icon, AEFC="Not able to click on zdx service tab", sleep_time=2)
        elif service_type == 'ZPA':
            zpa_icon = self.elements.PRIVATE_SECURITY_TAB
            self.ui.click(path=zpa_icon, AEFC="Not able to click on zpa service tab", sleep_time=2)
        else:
            return False, "Please provide service name ZPA/ZDX/ZIA"

        output = self.ui.click(path=self.elements.TIME_CONNECTED, do_click=False, return_text=True)
        pattern = '\d{2}\:\d{2}\s\w{2}\s\w{3}\s\d+\,\s\d{4}'
        match_obj = re.match(pattern, output)

        if match_obj:
            status = True
            msg = match_obj.string

        return status, msg

    def verify_service_status(self,
                              service_status: str = "on",
                              service_type: str = 'ZIA',
                              sleep: int = 60):
        """
        This function is used to verify the service status.

        :param service_status: The service status to verify. Default is "on".
        :type service_status: str
        :param service_type: The service type to verify. Default is "ZIA".
        :type service_type: str

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False
        msg = ""

        off_status, on_status, zdx_unsupported, zdx_connecting, zdx_disconnecting, disable_status = (None,) * 6
        if service_type == 'ZIA':
            self.explicit_wait(self.elements.INTERNET_SECURITY_TAB)
            zia_icon = self.elements.INTERNET_SECURITY_TAB
            self.ui.click(path=zia_icon, AEFC="Not able to click on zia service tab")

            off_status = self.elements.ZIA_OFF_STATUS
            on_status = self.elements.ZIA_ON_STATUS
            disable_status = self.elements.DISABLED_SERVICE_STATUS

        elif service_type == 'ZDX':
            self.explicit_wait(self.elements.DIGITAL_EXPERIENCE_TAB)
            zdx_icon = self.elements.DIGITAL_EXPERIENCE_TAB
            self.ui.click(path=zdx_icon, AEFC="Not able to click on zdx service tab")
            off_status = self.elements.ZDX_OFF_STATUS
            on_status = self.elements.ZDX_ON_STATUS
            zdx_unsupported = self.elements.ZDX_UNSUPPORTED_NETWORK
            zdx_connecting = self.elements.ZDX_CONNECTING_STATUS
            zdx_disconnecting = self.elements.ZDX_DISCONNECTING_STATUS
            disable_status = self.elements.DISABLED_SERVICE_STATUS

        elif service_type == 'ZPA':
            self.explicit_wait(self.elements.PRIVATE_SECURITY_TAB)
            zpa_icon = self.elements.PRIVATE_SECURITY_TAB
            self.ui.click(path=zpa_icon, AEFC="Not able to click on zpa service tab")
            off_status = self.elements.SERVICE_STATUS_OFF
            on_status = self.elements.SERVICE_STATUS_ON

        if service_status.lower() == "on":
            status, msg = self.explicit_wait(on_status, sleep=sleep)
        elif service_status.lower() == "disable":
            status, msg = self.explicit_wait(disable_status, sleep=sleep)
        elif service_status.lower() == "unsupported_network":
            status, msg = self.explicit_wait(zdx_unsupported, sleep=sleep)
        elif service_status.lower() == "connecting":
            status, msg = self.explicit_wait(zdx_connecting, sleep=sleep)
        elif service_status.lower() == "disconnecting":
            status, msg = self.explicit_wait(zdx_disconnecting, sleep=sleep)
        elif service_status.lower() == "off":
            status, msg = self.explicit_wait(off_status, sleep=sleep)
        else:
            status = False
            msg = "Please provide correct service status"

        return status, msg

    def verify_zpa_service_status(self,
                                  service_status: str = "on"):
        """
        This function is used to verify the ZPA service status.

        :param service_status: The service status to verify. Default is "on".
        :type service_status: str

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False

        self.explicit_wait(xpath=self.elements.PRIVATE_SECURITY, sleep=60)
        self.ui.click(path=self.elements.PRIVATE_SECURITY, AEFC="Not able to on zcc home page")
        if service_status == "on":

            self.explicit_wait(self.elements.ZPA_ON_STATUS)
            status = True
            msg = "zpa service is on"
        else:
            self.explicit_wait(self.elements.ZPA_OFF_STATUS)
            status = True
            msg = "zpa service is off"
        return status, msg

    def install_zapp(self,
                     image_path: str = ''):
        """
        This function is used to install a ZAPP.

        :param image_path: The image path of the ZAPP. Default is "".
        :type image_path: str

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False
        cmd = f'adb install {image_path}'
        output = subprocess.run(cmd, capture_output=True, text=True)
        time.sleep(2)
        if 'Success' in output.stdout:
            self.logger.info("Install is successful")
            status = True
            msg = "installation successful"

        cmd = f'adb shell am start -n {zcc_package_name}/{zcc_activity}'
        output = subprocess.run(cmd, capture_output=True, text=True)
        time.sleep(1)
        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def license_agreement(self):
        """
        This function is used to accept the license agreement.

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False
        try:
            self.ui.click(path='//android.widget.Button[@resource-id="android:id/button1"]', AEFC='')
            time.sleep(1)
            self.ui.click(
                path='//android.widget.Button[@resource-id="com.android.permissioncontroller:id/permission_allow_button"]',
                AEFC='')
            time.sleep(1)
            self.ui.click(
                path='//android.widget.Button[@resource-id="com.android.permissioncontroller:id/permission_allow_button"]',
                AEFC='')
            time.sleep(1)
            self.ui.click(path='//android.widget.Button[@resource-id="android:id/button1"]', AEFC='')
            status = True
            msg = "License agreement sucessfully done"

        except Exception as e:
            status = False
            msg = "License agreement failed"

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def verify_license_credit_options(self):
        """
        This function is used to verify the license credit options.

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False
        try:
            self.ui.click(self.elements.BURGER_MENU, sleep_time=1, AEFC="Unable to click on burger menu")
            time.sleep(1)
            self.ui.click(self.elements.LICENSE_CREDITS, sleep_time=1, AEFC="Unable to click on Licenses & Credits")

            self.ui.click(self.elements.LICENSE_AGREEMENT, AEFC="We do not have license agreement field")
            time.sleep(1)
            self.ui.click(self.elements.THIRD_PARTY_SOFTWARE, AEFC="We do not have third party software")
            time.sleep(1)
            status = True
            msg = 'license credit options verified'

        except Exception as e:
            status = False
            msg = 'license credit options is not verified'
        self.ui.click(self.elements.THIRD_PARTY_SOFTWARE, do_click=True, AEFC="We do not have third party software")
        self.sys_ops.adb_commands("adb shell input keyevent KEYCODE_BACK")
        return status, msg

    def verify_third_party_software(self,
                                    software_path: str = '',
                                    software_string: str = ''):
        """
        This function is used to verify the third party software.

        :param software_path: The software path to verify. Default is "".
        :type software_path: str
        :param software_string: The software string to verify. Default is "".
        :type software_string: str

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False
        self.ui.click(self.elements.BURGER_MENU, sleep_time=1, AEFC="Unable to click on burger menu")
        time.sleep(1)
        self.ui.click(self.elements.LICENSE_CREDITS, sleep_time=1, AEFC="Unable to click on Licenses & Credits")

        self.ui.click(self.elements.THIRD_PARTY_SOFTWARE, do_click=True, AEFC="We do not have third party software")

        self.ui.click(software_path, AEFC="Unable to click on third party sub license")
        try:
            self.ui.check_if_exists(software_string)
            status = True
            msg = 'Verified third party software successfuly'

        except Exception as e:
            status = False
            msg = "'Verified third party software is not successful'"


        self.ui.click(self.elements.THIRD_PARTY_SOFTWARE, do_click=True, AEFC="We do not have third party software")
        self.sys_ops.adb_commands("adb shell input keyevent KEYCODE_BACK")
        return status, msg

    def zdx_disable_incorrect_password(self,
                                       password: str):
        """
        This function is used to disable ZDX with an incorrect password.

        :param password: The password to use.
        :type password: str

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        status = False
        TAB = self.elements.DIGITAL_EXPERIENCE_TAB
        TOGGLE_OFF = self.elements.DIGITAL_POWER_BUTTON_OFF
        PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
        PASSOWRD_BOX = self.elements.DIGITAL_EXPERIENCE_PASSWORD_BOX
        cancel = self.elements.ZPA_DISABLE_CANCEL
        self.ui.click(TAB, sleep_time=1 * self.sleep_factor, AEFC=f"Unable to open zdx tab")
        time.sleep(5)

        self.ui.click(TOGGLE_OFF, sleep_time=1 * self.sleep_factor, AEFC=f"Unable to turn off zdx")
        self.ui.type(PASSOWRD_BOX, text=password, sleep_time=1 * self.sleep_factor, AEFC="Unable to enter password")
        self.ui.click(PASSWORD_DISABLE_OK_BUTTON, sleep_time=1 * self.sleep_factor,
                      AEFC=f"Unable to confirm zdx disable")
        output = self.ui.check_if_exists(self.elements.ZDX_DISABLE_INCORRECT_PASSWORD)

        if output:
            status = True
            msg = "This password is incorrect"

        self.ui.click(cancel, sleep_time=1 * self.sleep_factor, AEFC=f"Unable to confirm zdx disable")

        return status, msg

    def zpa_disable_incorrect_password(self,
                                       password: str):
        """
        This function is used to disable zpa with an incorrect password.

        :param password: The password to use.
        :type password: str

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>


         TAB = self.elements.PRIVATE_SECURITY
            TOGGLE_ON = self.elements.PRIVATE_ACCESS_TURN_ON
            TOGGLE_OFF = self.elements.PRIVATE_ACCESS_TURN_OFF
            DISABLE = self.elements.DISABLE
            PASSOWRD_BOX = self.elements.INTERNET_SECURITY_PASSWORD_BOX
            zpa_icon = "//*[@class='android.widget.ImageView' and @content-desc='Private Access']"
        """

        status = False
        TAB = self.elements.PRIVATE_SECURITY
        TOGGLE_OFF = self.elements.PRIVATE_ACCESS_TURN_OFF
        PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
        PASSOWRD_BOX = self.elements.INTERNET_SECURITY_PASSWORD_BOX
        cancel = self.elements.ZPA_DISABLE_CANCEL
        self.ui.click(TAB, sleep_time=1 * self.sleep_factor, AEFC=f"Unable to open ZPA tab")
        time.sleep(5)

        self.ui.click(TOGGLE_OFF, sleep_time=1 * self.sleep_factor, AEFC=f"Unable to turn off zdx")
        self.ui.type(PASSOWRD_BOX, text=password, sleep_time=1 * self.sleep_factor, AEFC="Unable to enter password")
        self.ui.click(PASSWORD_DISABLE_OK_BUTTON, sleep_time=1 * self.sleep_factor,
                      AEFC=f"Unable to confirm ZPA disable")
        output = self.ui.check_if_exists(self.elements.ZDX_DISABLE_INCORRECT_PASSWORD)

        if output:
            status = True
            msg = "This password is incorrect"

        self.ui.click(cancel, sleep_time=1 * self.sleep_factor, AEFC=f"Unable to confirm ZPA disable")

        return status, msg

    def get_zcc_version(self):
        """
        This function is used to fetch the ZCC version installed on an Android device.

        :return: The ZCC version.
        :rtype: str

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """

        package_name = f'zscaler.com.zschromeosapp' if self.chromeos else '{zcc_package_name}'
        cmd = f'adb shell dumpsys package {package_name} | findStr versionName'
        output = self.sys_ops.adb_commands(cmd)

        return output[0], output[1], (output[2].strip()).decode()

    # ------------------------------------------------------------------------------------------------------------------

    def vpn_popup_handle(self):
        """
        This method is to handle VPN popup.
        It returns a tuple with a boolean status and a message string.
        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """
        msg = ''
        status = True

        try:
            self.explicit_wait(xpath=self.elements.VPN_OK_BUTTON, sleep=20)
            self.ui.click(path=self.elements.VPN_OK_BUTTON, AEFC="Not able to accept vpn request")
            msg = "Able to accept VPN request"

        except Exception as e:
            status = False
            msg = "Not able to accept vpn request"

        return status, msg

    def disable_reason(self,
                       service: str = 'ZIA',
                       reason: str = 'Reason not given by end user'):
        """
        This method disables a Zscaler service and provides a reason for disabling it.
        It returns a tuple with a boolean status and a message string.
        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """
        status = False
        if service == 'ZIA':
            self.ui.click(self.elements.INTERNET_SECURITY_TAB)
            service_path = self.elements.INTERNET_SECURITY_TURN_OFF_TAB

        elif service == 'ZPA':
            self.ui.click(self.elements.PRIVATE_SECURITY)
            service_path = self.elements.PRIVATE_ACCESS_TURN_OFF

        else:
            self.ui.click(self.elements.DIGITAL_EXPERIENCE_TAB)
            self.explicit_wait(self.elements.DIGITAL_POWER_BUTTON_OFF)
            service_path = self.elements.DIGITAL_POWER_BUTTON_OFF

        try:
            self.ui.click(service_path, AEFC=f"Not able to click on turn off {service}")
            self.ui.type(self.elements.DISABLE_REASON, text=reason)
            self.ui.click(self.elements.DISABLE, AEFC="Not able to click on disable after giving reason to disable")
            status = True
            msg = 'able to give reason for disable successfully'

        except Exception as e:

            status = False
            msg = "Not able to give reason for disable successfully"

        return status, msg

    def zpa_reauth_notification_status(self,
                                       check: str = 'true'):

        """

        This method is check status of reauth notication option in zcc
        return: status and msg

        """
        status = False
        msg = ""
        self.ui.click(self.elements.MORE_TAB, sleep_time=1 * self.sleep_factor, AEFC=f"Unable to open more tab")
        ZPA_REAUTH_NOTIFICATION = f"//*[@resource-id='{zcc_package_name}:id/reauthNotificationSwitch' and @checked='{check}']"

        status = self.ui.check_if_exists(ZPA_REAUTH_NOTIFICATION, AEFC="Status of zpa reauth notification not matched")

        if status:
            msg = "Fetched the status of zpa re-auth notification"

        return status, msg

    def kill_zcc(self):
        """
        This method is used to kill the ZApp completely
        Returns a tuple with a boolean status and a message string
        Generated by Priyanshu Choudhary
        """
        status = False
        msg = ''

        subprocess.run(f"adb shell am force-stop {zcc_package_name}")
        self.ui.sleep(1)

        cmd = "adb shell ps -p 3148"
        out = \
            subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=subprocess.PIPE).communicate()[
                0].decode('utf-8')

        if self.elements.APP in out:
            status = False
            msg = "ZApp killing failed"
        else:
            status = True
            msg = "ZApp killed successfully"

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def license_third_party_check(self):

        """
        This method is verify third party software
        """

        status = False
        msg = ""
        try:
            self.elements = AndroidElements()
            self.ui.click(self.elements.BURGER_MENU, sleep_time=1, AEFC="Unable to click on burger menu")
            time.sleep(1)
            self.ui.click(self.elements.LICENSE_CREDITS, sleep_time=1, AEFC="Unable to click on Licenses & Credits")
            self.ui.click(self.elements.THIRD_PARTY_SOFTWARE, do_click=True, AEFC="We do not have third party software")
            result = self.ui.click(path="//*[@resource-id= 'android:id/text1' and @text='POCO']",
                                   AEFC="Unable to click on third party sub license", )
            self.ui.click(self.elements.THIRD_PARTY_SOFTWARE, do_click=True, AEFC="We do not have third party software")
            result = self.ui.click(path="//*[@resource-id= 'android:id/text1' and @text='PCAP']",
                                   AEFC="Unable to click on third party sub license", )
            self.ui.click(self.elements.THIRD_PARTY_SOFTWARE, do_click=True, AEFC="We do not have third party software")
            #self.sys_ops.adb_commands("adb shell input keyevent KEYCODE_BACK")
            status = True
            msg = "Back otpion is working fine with license "

        except Exception as e:
            status = False
            msg = "Not Back otpion is working fine with license "

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def service_ent_status(self,
                           service: str = 'ZDX',
                           current_status: str = 'Enable'):
        """

        This method is verify status of svc enabled on MA under service
        entitlement

        """
        status = False
        msg = ""

        if service == 'ZIA':
            serivce_tab = self.elements.INTERNET_SECURITY_TAB

        elif service == 'ZDX':
            serivce_tab = self.elements.DIGITAL_EXPERIENCE_TAB

        else:
            serivce_tab = self.elements.PRIVATE_SECURITY_TAB

        if current_status is not 'Enable':
            try:
                self.ui.check_if_exists(path=serivce_tab, expect_failure=True)
                status = True
                msg = "svc entilment status for give service is as expected "

            except Exception as e:
                status = False
                msg = "svc status on zapp is not as expected as service is not present"

        else:
            try:
                self.ui.check_if_exists(path=serivce_tab, expect_failure=False)
                status = True
                msg = "svc entilment status for give service is as expected "

            except Exception as e:
                status = False
                msg = "svc status on zapp is not as expected as service is present"

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def custom_okta_login(self,
                          zia_user_id: str = None,
                          zia_user_pass: str = None,
                          zpa_user_id: str = None,
                          zpa_user_pass: str = None,
                          formbased: bool = False) -> tuple[bool, str]:
        """
        This function performs a custom login for Okta using the provided Zscaler credentials and ZPA credentials.

        :param zia_user_id: The Zscaler user ID.
        :type zia_user_id: str
        :param zia_user_pass: The Zscaler user password.
        :type zia_user_pass: str
        :param zpa_user_id: The ZPA user ID.
        :type zpa_user_id: str
        :param zpa_user_pass: The ZPA user password.
        :type zpa_user_pass: str
        :param formbased: login method includes formbased or not
        :type formbased: bool
        :return: A tuple containing a boolean status and a message indicating the result of the login attempt.
        :rtype: tuple[bool, str]

        Generated by Priyanshu Choudhary, assisted by ZCoder on 15-July-2024 <Please retain this marker>
        """

        status = False
        msg = ""

        signin_button_xpath = self.elements.SIGNIN_BUTTON_XPATH
        result = self.initial_zcc_login_window(zia_user_id)
        assert result[0], result[1]

        if formbased:
            self.explicit_wait(self.elements.PASSWORD)
            self.ui.type(path=self.elements.PASSWORD, text=zia_user_pass, AEFC="Password doesn't typed")
            self.ui.click(self.elements.LOGIN_BUTTON, AEFC="Login button not clicked")

        try:
            self.explicit_wait(signin_button_xpath, sleep=30)
            time.sleep(5)
            self.sys_ops.adb_commands("adb shell input keyevent 61")
            self.sys_ops.adb_commands("adb shell input keycombination 113 29")
            self.sys_ops.adb_commands("adb shell input keyevent 67")
            time.sleep(1)
            cmd = "adb shell input text {}".format(zpa_user_id)
            self.sys_ops.adb_commands(cmd)
            self.sys_ops.adb_commands("adb shell input keyevent 61")
            time.sleep(3)
            cmd = "adb shell input text {}".format(zpa_user_pass)
            self.sys_ops.adb_commands(cmd)
            self.ui.click('//android.widget.TextView[@text="Sign In"]', AEFC="Sign In text not clicked")
            self.ui.click(signin_button_xpath, AEFC="Sign In button not clicked on OKTA Screen")
            time.sleep(10)
            status = True
            msg = "Logging in successfully"
        except:
            status = False
            msg = "Something went wrong"

        return status, msg

    def validate_zpa_reauth_autofill_username(self,
                                              zia_user_id: str = None,  # ZIA User ID
                                              zia_user_pass: str = None,  # ZIA Userpass
                                              zpa_user_id: str = None,  # ZPA User ID
                                              zpa_user_pass: str = None,  # ZPA Userpass
                                              reauth_knob: str = "on"  # "on" if knob is "on" from MA else "off"
                                              ) -> tuple[bool, str]:
        """
                This function performs a validation for autofilling of username during ZPA reauth.

                :param zia_user_id: The Zscaler user ID.
                :type zia_user_id: str
                :param zia_user_pass: The Zscaler user password.
                :type zia_user_pass: str
                :param zpa_user_id: The ZPA user ID.
                :type zpa_user_id: str
                :param zpa_user_pass: The ZPA user password.
                :type zpa_user_pass: str
                :return: A tuple containing a boolean status and a message indicating the result of the login attempt.
                :rtype: tuple[bool, str]
        """

        status = False
        msg = ""

        signin_button_xpath = '//android.widget.Button[@text="Sign in"]'

        try:
            self.ui.click(self.elements.PRIVATE_SECURITY, AEFC="Private Security tab not clicked")
            time.sleep(5)

            reauth_button_exists = self.ui.check_if_exists(self.elements.AUTHENTICATE,
                                                           AEFC="Authenticate button not found", boolean=True)
            if not reauth_button_exists:
                self.explicit_wait(self.elements.AUTHENTICATE, sleep=60)

            self.ui.click(self.elements.AUTHENTICATE, AEFC="Authenticate button not clicked")
            self.explicit_wait(signin_button_xpath, sleep=60)
            time.sleep(10)

            if reauth_knob.lower() == "on":
                username_xpath = f"//*[@class='android.widget.EditText' and @text='{zia_user_id}']"
                autofill_username_exists = self.ui.check_if_exists(username_xpath,
                                                                   AEFC="Autofilled username doesn't exists",
                                                                   boolean=True)

                if autofill_username_exists:
                    status = True
                    msg = "Autofill username operation successful"
                else:
                    status = False
                    msg = "Autofill username not found"

            else:
                username_xpath = "//*[@class='android.widget.EditText' and @resource-id='input28' and @text='']"
                username_blank = self.ui.check_if_exists(username_xpath, AEFC="Username field is not blank",
                                                         boolean=True)

                if username_blank:
                    status = True
                    msg = "Username is found to be blank"
                else:
                    status = False
                    msg = "Username is not blank, something is autofilled"

            self.sys_ops.adb_commands("adb shell input keyevent KEYCODE_BACK")

        except Exception as e:
            status = False
            msg = "Something went wrong | Exception: " + str(e)

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def validate_idp_autofill_username(self,
                                       zia_user_id: str = None,  # ZIA User ID
                                       zia_user_pass: str = None,  # ZIA Userpass
                                       zpa_user_id: str = None,  # ZPA User ID
                                       zpa_user_pass: str = None,  # ZPA Userpass
                                       reauth_knob: str = "on",  # "on" if knob is "on" from MA else "off"
                                       formbased: bool = False
                                       ) -> tuple[bool, str]:
        """
            This function performs a validation for autofilling of username during IDP login.

            :param zia_user_id: The Zscaler user ID.
            :type zia_user_id: str
            :param zia_user_pass: The Zscaler user password.
            :type zia_user_pass: str
            :param zpa_user_id: The ZPA user ID.
            :type zpa_user_id: str
            :param zpa_user_pass: The ZPA user password.
            :type zpa_user_pass: str
            :return: A tuple containing a boolean status and a message indicating the result of the login attempt.
            :rtype: tuple[bool, str]
        """
        status = False
        msg = ""
        signin_button_xpath = '//android.widget.Button[@text="Sign in"]'

        try:
            result = self.initial_zcc_login_window(zia_user_id)
            assert result[0], result[1]

            if formbased:
                self.explicit_wait(self.elements.PASSWORD)
                self.ui.type(path=self.elements.PASSWORD, text=zia_user_pass, AEFC="Password doesn't typed")
                self.ui.click(self.elements.LOGIN_BUTTON, AEFC="Login button not clicked")

            self.explicit_wait(signin_button_xpath, sleep=30)
            time.sleep(5)

            if reauth_knob.lower() == "on":
                username_xpath = f"//*[@class='android.widget.EditText' and @text='{zia_user_id}']"
                autofill_username_exists = self.ui.check_if_exists(username_xpath,
                                                                   AEFC="Autofilled username doesn't exists",
                                                                   boolean=True)

                if autofill_username_exists:
                    status = True
                    msg = "Autofill username operation successful"
                else:
                    status = False
                    msg = "Autofill username not found"

            else:
                username_xpath = "//*[@class='android.widget.EditText' and @resource-id='input28' and @text='']"
                username_blank = self.ui.check_if_exists(username_xpath, AEFC="Username field is not blank",
                                                         boolean=True)

                if username_blank:
                    status = True
                    msg = "Username is found to be blank"
                else:
                    status = False
                    msg = "Username is not blank, something is autofilled"

            self.sys_ops.adb_commands("adb shell input keyevent KEYCODE_BACK")

        except Exception as e:
            status = False
            msg = "Something went wrong | Exception: " + str(e)

        return status, msg

    def verify_mode(self, set=True):

        """
        Validating the mode of mobile device

        """
        status = False
        msg = ""
        cmd = "adb shell am start -a android.settings.DISPLAY_SETTINGS"
        self.sys_ops.adb_commands(cmd)

        if set is True:
            mode_validation = self.elements.MODE_VALIDATION_TRUE


        else:
            mode_validation = self.elements.MODE_VALIDATION_FALSE

        try:
            result = self.ui.check_if_exists(path=mode_validation, expect_failure=False, boolean=True)
            if result:
                status = True
                msg = "Mode is set as per req"

        except Exception as e:
            status = False
            msg = "Mode is not set as per req"

        cmd = f'adb shell am start -n {zcc_package_name}/{zcc_activity}'
        output = subprocess.run(cmd, capture_output=True, text=True)

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def verify_report_exists(self):
        """

        This method is to check report an issue option present or not on ZCC app

        """
        status = False
        msg = "report an issue exits"

        path = self.elements.REPORT_ISSUE

        self.logger.info("Moving to more option of ZCC")
        self.ui.click(self.elements.NOTIFICATION)
        time.sleep(2)

        status = self.ui.check_if_exists(path=path, expect_failure=True, boolean=True)
        if status:
            status = True
            msg = " report an issue is not present under notification section"

        return status, msg

    def clear_zdx_data(self):
        """
        Clear ZDX data.

        This function clears the zdx data by clicking on specific UI elements.

        Args:
            sleep_time (int): Number of seconds to sleep after execution. Default is 30.

        Returns:
            tuple:  A tuple containing a boolean status, message string,None indicating whether the clear zdx was
            successful or not.
        """
        try:
            self.bring_zcc_to_focus()
            self.ui.click(self.elements.DIGITAL_EXPERIENCE_TAB, sleep_time=1, AEFC="Unable to click ZDX Tab")
            self.ui.click(self.elements.DIGITAL_EXPERIENCE_CLEAR_DATA_LABEL, sleep_time=1,
                          AEFC="Unable to click Clear Data button ")
            self.ui.click(self.elements.DIGITAL_EXPERIENCE_CLEAR_DATA_ACCEPT_BUTTON, sleep_time=1,
                          AEFC="Unable to click ZDX Clear Accept Button")
            status = True
            msg = 'able to clear ZDX data successfully'

        except Exception as e:
            self.logger.error(f"CLEAR ZDX DATA FAILED {e}".format(e))
            msg = 'Not able to CLEAR ZDX DATA successfully'
            raise Exception(f"Error :: CLEAR ZDX DATA FAILED :: {e}")

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def restart_zdx_service(self):
        """
        Restart ZDX service.

        This function restarts the zdx service by clicking on specific UI elements. It also checks if the service is restarted
        successfully by looking for specific text on the UI.

        Args:
            sleep_time (int): Number of seconds to sleep after execution. Default is 30.

        Returns:
            tuple:  A tuple containing a boolean status, message string,None indicating whether the restart service was
            successful or not.

        """

        status = False
        msg = ""
        on_status = self.elements.ZDX_ON_STATUS

        self.ui.click(self.elements.NOTIFICATIONS_TAB_XPATH, sleep_time=1)
        try:
            self.ui.click(self.elements.NOTIFICATION_DELETE_LOCATOR, sleep_time=1, AEFC="Unable to click")
            self.ui.click(self.elements.NOTIFICATION_YES_CLEAR_ALL, sleep_time=1, AEFC="Unable to Clear Notification")

        except:
            self.ui.click(self.elements.REPORT_ISSUE_CLOSE)

        try:
            self.bring_zcc_to_focus()
            self.ui.click(self.elements.DIGITAL_EXPERIENCE_TAB, sleep_time=1, AEFC="Unable to click ZDX Tab")
            self.ui.click(self.elements.DIGITAL_EXPERIENCE_RESTART, sleep_time=1,
                          AEFC="Unable to click Restart ZDX button ")
            self.ui.click(self.elements.DIGITAL_EXPERIENCE_RESTART_YES, sleep_time=1,
                          AEFC="Unable to click Restart ZDX Accept Button")

            start_time = int(time.time())
            status, msg = self.explicit_wait(on_status, sleep=60)
            end_time = int(time.time())
            time_taken = end_time - start_time
            self.logger.info(f"Time taken to connect ZDX after restart zdx is = {time_taken}s")

            if time_taken > 40:
                self.logger.warning(f"Time taken by zdx to connect after clicking on restart ZDX is {time_taken}")
            elif time_taken > 20:
                self.logger.critical(f"Time taken by zdx to connect after clicking on restart ZDX is {time_taken}")

            self.ui.click(self.elements.NOTIFICATIONS_TAB_XPATH, sleep_time=1)
            status_out = self.ui.check_if_exists(self.elements.DIGITAL_EXPERIENCE_TURNED_OFF_NOTIFICATION,
                                                 AEFC="ZDX Service not stopped after restart ZDX service")

            status_out = self.ui.check_if_exists(self.elements.DIGITAL_EXPERIENCE_TURNED_ON_NOTIFICATION,
                                                 AEFC="Service not started after restart of svc")

            self.ui.click(self.elements.DIGITAL_EXPERIENCE_TAB, sleep_time=0, AEFC="Unable to click on ZDX Tab")
            WebDriverWait(self.ui.driver, 10).until(
                EC.visibility_of_element_located((By.XPATH, self.elements.ZDX_ON_STATUS)))
            if status_out:
                status = True
                msg = "ZDX restart service is successful"

        except Exception as e:
            self.logger.error(f"Restart ZDX  FAILED {e}".format(e))
            msg = 'Restart ZDX Failed'
            raise Exception(f"Error :: Restart ZDX FAILED :: {e}")

        self.logger.info("Success :: Restarted Service!")

        return status, msg

    # ------------------------------------------------------------------------------------------------------------------

    def get_broker_ip(self):
        """
        get_broker_ip.

        This function get the ZPA broker IP by clicking on specific UI elements.

        Args:
            sleep_time (int): Number of seconds to sleep after execution. Default is 60.

        Returns:
            tuple:  A tuple containing a boolean status, message string contains broker IP

        """

        status = False
        msg = "Failed to get broker IP"
        self.explicit_wait(xpath=self.elements.PRIVATE_SECURITY, sleep=60)
        self.ui.click(path=self.elements.PRIVATE_SECURITY, AEFC="Unable to open Private Access tab")
        self.explicit_wait(xpath=self.elements.ZPA_ON_STATUS, sleep=60)

        output = self.ui.click(path=self.elements.BROKER_IP, do_click=False, return_text=True)
        pattern = r"[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}"
        match_obj = re.match(pattern, output)

        if match_obj:
            status = True
            msg = match_obj.string

        return status, msg

    def refresh_zcc_ui(self, current_service: str = "zdx") -> tuple[bool, str]:
        """
        This function refresh ZCC_UI, requires in network switch change

        """
        switch_tab = ""
        if current_service.lower() == "zdx":
            switch_tab = self.elements.INTERNET_SECURITY_TAB
        elif current_service.lower() == "zia":
            switch_tab = self.elements.DIGITAL_EXPERIENCE_TAB
        elif current_service.lower() == "zpa":
            switch_tab = self.elements.INTERNET_SECURITY_TAB

        try:
            self.ui.click(switch_tab, sleep_time=2)
            return True, "Success"
        except:
            return False, "Failed to switch to"

    def verify_zdx_unsupported_nw_learn_more_error(self) -> tuple[bool, str]:
        """

        This function check the learn more error text when zdx is in ipv6 only network

        Args:
            sleep_time (int): Number of seconds to sleep after execution. Default is 30.

        Returns:
            tuple:  A tuple containing a boolean status, message string,None indicating whether the verification
            is successful or not
        """

        try:
            self.bring_zcc_to_focus()
            self.ui.click(self.elements.DIGITAL_EXPERIENCE_TAB, sleep_time=1, AEFC="Unable to click ZDX Tab")
            self.ui.click(self.elements.LEARN_MORE_LOCATOR, sleep_time=1, AEFC="Learn More not clicked")

            error_string_matched = self.ui.check_if_exists(path=self.elements.ZDX_UNSUPPORTED_NETWORK_ERROR_STRING,
                                                           AEFC="Error string mismatched", boolean=True)
            if not error_string_matched:
                return False, "Error string mismatched"

            self.ui.click(self.elements.LEARN_MORE_OK, sleep_time=1, AEFC="OK button not clicked")
            status = True
            msg = 'Learn More text verified'

        except Exception as e:
            self.logger.error(f"ZDX UNSUPPORTED NETWORK LEARN MORE VERIFICATION FAILED {e}")
            msg = 'ZDX UNSUPPORTED NETWORK LEARN MORE VERIFICATION FAILED'
            raise Exception(f"ZDX UNSUPPORTED NETWORK LEARN MORE VERIFICATION FAILED :: {e}")

        return status, msg

    def verify_no_restart_service_notification(self, service: str = "ZDX", expect_failure: bool = False):
        """
        This method to verify no zdx restart notifications are present
        return:
        True if no notifications
        False if notifications


        """
        status = False
        msg = ""
        notification_tab = "//*[@class='android.widget.TextView' and @text='Notifications']"
        zdx_stop = "//*[@class='android.widget.TextView' and @text='Digital Experience Turned On.']"
        zdx_start = "//*[@class='android.widget.TextView' and @text='Digital Experience Turned Off.']"

        zia_stop = "//*[@class='android.widget.TextView' and @text='Internet Security Stopped']"
        zia_start = "//*[@class='android.widget.TextView' and @text='Internet Security Started']"

        zpa_stop = "//*[@class='android.widget.TextView' and @text='Private Access Stopped']"
        zpa_start = "//*[@class='android.widget.TextView' and @text='Private Access Started']"
        try:
            self.ui.click(path=notification_tab, AEFC="Unable to open Notification tab")
        except Exception as e:
            self.logger.info("Not able to click on notification tab")

        self.logger.info(f"Checking for restart {service} notifications")

        if service.lower() == "zdx":
            status_out1 = self.ui.check_if_exists(zdx_stop, AEFC="Service not stopped after restart of service")
            status_out2 = self.ui.check_if_exists(zdx_start, AEFC="Service not started after restart of service")
        elif service.lower() == "zpa":
            status_out1 = self.ui.check_if_exists(zpa_stop, AEFC="Service not stopped after restart of service")
            status_out2 = self.ui.check_if_exists(zpa_start, AEFC="Service not started after restart of service")
        elif service.lower() == "zia":
            status_out1 = self.ui.check_if_exists(zia_stop, AEFC="Service not stopped after restart of service")
            status_out2 = self.ui.check_if_exists(zia_start, AEFC="Service not started after restart of service")
        else:
            return False, "Please provide correct Service name for notification check"

        if status_out2 is False and status_out1 is False:
            status = True
            msg = f"no notification related to {service} restart is present "

        if expect_failure and not status:
            return True, msg

        return status, msg

    def clear_notification(self):
        """
        this function will clear notification from NOTIFICATION tab.

        Args:
            sleep_time (int): Number of seconds to sleep after execution. Default is 30.

        Returns:
            tuple:  A tuple containing a boolean status, message string,None indicating whether the restart service was
            successful or not.

        """

        status = False
        msg = ""

        self.ui.click(self.elements.NOTIFICATIONS_TAB_XPATH, sleep_time=1)
        try:
            self.ui.click(self.elements.NOTIFICATION_DELETE_LOCATOR, sleep_time=1, AEFC="Unable to click")
            self.ui.click(self.elements.NOTIFICATION_YES_CLEAR_ALL, sleep_time=1, AEFC="Unable to Clear Notification")
            status = True
        except:
            self.ui.click(self.elements.REPORT_ISSUE_CLOSE)
            msg = "Unable to Clear Notifications"

        return status, msg

    def get_username_from_zapp(self, service_type: str = 'ZDX'):
        """
        Get the current username logged into the Zscaler.

        This method brings the Zscaler client (ZCC) to focus and then clicks on the respective tab to open it. It then
        retrieves the username from the tab and returns it. If any step fails, the method returns an error
        message.

        :return: A tuple containing a boolean status (True if successful, False otherwise) and the username (if successful) or
                an error message (if unsuccessful).
        :rtype: (bool, str)

        Example:

        (True, 'example_username')
        """

        sleep = time.sleep(10)
        status = False

        if service_type == 'ZIA':
            zia_icon = self.elements.INTERNET_SECURITY_TAB
            self.ui.click(path=zia_icon, AEFC="Not able to click on zia service tab", sleep_time=2)
        elif service_type == 'ZDX':
            zdx_icon = self.elements.DIGITAL_EXPERIENCE_TAB
            self.ui.click(path=zdx_icon, AEFC="Not able to click on zdx service tab", sleep_time=2)
        elif service_type == 'ZPA':
            zpa_icon = self.elements.PRIVATE_SECURITY_TAB
            self.ui.click(path=zpa_icon, AEFC="Not able to click on zpa service tab", sleep_time=2)
        else:
            return False, "Please provide service name ZPA/ZDX/ZIA"

        try:
            username = self.ui.click(self.elements.ZDX_USERNAME, do_click=False, return_text=True)
            return True, username
        except Exception as e:
            return False, f"Failure : {e}"

    def get_authenticated_status(self, service_type: str = 'ZDX'):
        """
        Get the current auth status from service

        This method brings the Zscaler client (ZCC) to focus and then clicks on the respective tab to open it. It then
        retrieves the auth status from the tab and returns it. If any step fails, the method returns an error
        message.

        :return: A tuple containing a boolean status (True if successful, False otherwise) and the username (if successful) or
                an error message (if unsuccessful).
        :rtype: (bool, str)

        Example:

        (True, 'example_username')
        """

        sleep = time.sleep(10)
        status = False

        auth_status_element = self.elements.ZDX_AUTH_STATUS_TEXT

        if service_type == 'ZDX':
            zdx_icon = self.elements.DIGITAL_EXPERIENCE_TAB
            self.ui.click(path=zdx_icon, AEFC="Not able to click on zdx service tab", sleep_time=2)
        elif service_type == 'ZPA':
            zpa_icon = self.elements.PRIVATE_SECURITY_TAB
            self.ui.click(path=zpa_icon, AEFC="Not able to click on zpa service tab", sleep_time=2)
        else:
            return False, "Please provide service name ZPA/ZDX"

        try:
            auth_status = self.ui.click(auth_status_element, do_click=False, return_text=True)
            return True, auth_status
        except Exception as e:
            return False, f"Failure : {e}"

    def get_zdx_server_address(self):
        """
        Get the zdx server address

        This method brings the Zscaler client (ZCC) to focus and then clicks on the respective tab to open it. It then
        retrieves the server ip from the tab and returns it. If any step fails, the method returns an error
        message.

        :return: A tuple containing a boolean status (True if successful, False otherwise) and the username (if successful) or
                an error message (if unsuccessful).
        :rtype: (bool, str)

        Example:

        (True, 'example_username')
        """

        sleep = time.sleep(10)
        status = False
        msg = "Failed to get ZDX Server IP"

        server_ip_element = self.elements.ZDX_SERVER
        zdx_icon = self.elements.DIGITAL_EXPERIENCE_TAB

        try:
            self.ui.click(path=zdx_icon, AEFC="Not able to click on zdx service tab", sleep_time=2)
            server_address = self.ui.click(path=server_ip_element, do_click=False, return_text=True)
            return True, server_address
        except Exception as e:
            return False, f"Failure : {e}"

    def get_zdx_service_version(self):
        """
        Get the zdx version

        This method brings the Zscaler client (ZCC) to focus and then clicks on the respective tab to open it. It then
        retrieves the zdx version from the tab and returns it. If any step fails, the method returns an error
        message.

        :return: A tuple containing a boolean status (True if successful, False otherwise) and the username (if successful) or
                an error message (if unsuccessful).
        :rtype: (bool, str)

        Example:

        (True, 'version')
        """

        sleep = time.sleep(5)
        status = False
        msg = "Failed to get ZDX Version"

        zdx_version_element = self.elements.ZDX_VERSION
        zdx_icon = self.elements.DIGITAL_EXPERIENCE_TAB

        try:
            self.ui.click(path=zdx_icon, AEFC="Not able to click on zdx service tab", sleep_time=2)
            version = self.ui.click(path=zdx_version_element, do_click=False, return_text=True)
            return True, version
        except Exception as e:
            return False, f"Failure : {e}"

    def app_back(self):
        self.ui.back_page()

    def verify_ip_intercepted(self, list_ips):
        """
        This method is to verify traffic is intercepted by zapp or not using
        zapp packet capture for Tunnel1
        list_ips: list of ip address required to verify in pcap file
        returns: True if traffic intercepct and false if not intercepted by zapp


        """

        base_path =  os.getcwd()

        path =  base_path + r"\OS_Android\Temp_logs"

        list_of_files = os.listdir(path)
        for i in list_of_files:
            if ".pcap" in i:
                pcap_file = path+"\\"+i

        packets = pyshark.FileCapture(pcap_file)
        status = False
        msg = "Traffic not intercepeted by ZCC app"
        for check in packets:
            try:
                if str(check.ip.dst) in list_ips:
                    print(check.ip.dst)
                    status = True
                    "Traffic intercapeted by ZCC app"

            except Exception as e:
                self.logger.info("Not ipv4 packet")

        return status, msg

    def check_serive_tab(self, service='ZIA'):
        """
        This method is to validate service is present in zcc app or not

        """
        status = False
        msg = f"{service} service not present in zcc app"

        if service == 'ZIA':
            serivce_tab = self.elements.INTERNET_SECURITY_TAB

        elif service == 'ZPA':
            serivce_tab = self.elements.PRIVATE_SECURITY_TAB

        else:
            serivce_tab = self.elements.DIGITAL_EXPERIENCE_TAB


        output = self.ui.click(path=serivce_tab, expect_click_failure= True )
        if output:
            status = False
            msg = f"{service} service not present in zcc app"

        else:
            status = True
            msg = f"{service} service present in zcc app"



        return status, msg


    def no_service_service_enttilement(self, username , password ):

        """

        This method is to validate is there any service is enable for user or not
        return: True if no srvc or False if service is enable

        """
        status = False
        msg = "Service is enable for user"

        output_return = self.initial_zcc_login_window(username=username)
        if not output_return[0]:
            return output_return
        output_return = self.zia_form_based_login(password=password, cancel_zia_login= False)
        if not output_return[0]:
            return output_return

        time.sleep(5)
        path = self.elements.NO_SERVICE_ASSIGN
        output = self.ui.check_if_exists(path=path)
        if output:
            msg = "Service is enable for user"
            status = True
        path = self.elements.NO_SERVICE_ERROR_OK
        try:
            self.ui.click(path=path)

        except Exception as e:
            self.logger.info(e)

        for i in range(4):
            self.app_back()

        cmd = f'adb shell am start -n {zcc_package_name}/com.zscaler.activities.MainActivity'
        output = subprocess.run(cmd, capture_output=True, text=True)

        return status, msg

    def okta_login_failed(self, username, password):
        """
        Simulate an Okta login failure by entering incorrect credentials.

        Args:
            username (str): The username to use for the login attempt.
            password (str): The password to use for the login attempt.

        Returns:
            tuple: A tuple containing a boolean indicating whether the login failure was successful and a message describing the outcome.
        """

        status = False
        msg = ""
        cmd = f'adb shell am start -n {zcc_package_name}/{zcc_activity}'  # if not self.chromeos else 'adb shell am start -n zscaler.com.zschromeosapp/{zcc_activity}'
        output = subprocess.run(cmd, capture_output=True, text=True)

        try:

            self.ui.type(self.elements.USERNAME, text=username, sleep_time=1, AEFC="Something wrong with username")
            time.sleep(1)
            self.ui.click(self.elements.LOGIN_BUTTON, sleep_time=1,AEFC="Cannot click on login at Zapp")
            try:
                self.explicit_wait(self.elements.PASSWORD)
                self.ui.click(self.elements.PASSWORD, retry_frequency=5, max_threshold=10, sleep_time=1,
                              AEFC="Cannot click okta username")
                self.ui.type(self.elements.PASSWORD, text=password, sleep_time=1, AEFC="Something wrong with username")
                self.explicit_wait(self.elements.LOGIN_BUTTON)
                self.ui.click(self.elements.LOGIN_BUTTON, sleep_time=1, AEFC="Cannot click on login at Zapp")

            except Exception as e:
                self.logger.info(e)


            self.explicit_wait(self.elements.OKTA_USERNAME)
            self.ui.click(self.elements.OKTA_USERNAME, retry_frequency=5, max_threshold=10, sleep_time=1,AEFC="Cannot click okta username")
            self.ui.type(self.elements.OKTA_USERNAME, text=username, sleep_time=1, AEFC="Something wrong with username")
            self.ui.click(self.elements.OKTA_PASSWORD, retry_frequency=5, max_threshold=10, sleep_time=1,AEFC="Cannot click okta username")
            self.ui.type(self.elements.OKTA_PASSWORD, text='wrong', sleep_time=1,
                         AEFC="Something wrong with passwd")
            self.ui.back_page()
            okta_sign_in = self.ui.check_if_exists(self.elements.OKTA_SIGNIN)
            self.ui.click(self.elements.OKTA_SIGNIN)
            self.explicit_wait(self.elements.OKTA_UNABLE_SIGNIN)
            error_message = self.ui.check_if_exists(self.elements.OKTA_UNABLE_SIGNIN, boolean=True)
            if error_message:
                msg = " Failed to login in okta"
                status = True
        except Exception as e:
            self.ui.logger.info(e)

        return status, msg


    @allure.step("Validate notification in ZApp")
    def validate_notification(self, notification_text: str):
        """
        Validates if a specific notification is present in the ZCC notification tab.

        :param notification_text: The text of the notification to validate.
        :type notification_text: str
        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)
        """

        status = False
        msg = f"Notification '{notification_text}' not found."
        self.logger.info(f"Validating presence of notification: '{notification_text}'")

        try:
            # Navigate to the Notifications tab
            notification_tab_xpath = self.elements.NOTIFICATIONS_TAB_XPATH
            self.ui.click(notification_tab_xpath, sleep_time=1, AEFC="Unable to click on Notifications tab")
            self.logger.info("Successfully navigated to the Notifications tab.")

            # Construct the XPath for the specific notification text
            notification_xpath = f'//android.widget.TextView[@resource-id="zscaler.com.zscaler:id/rowMessage" and @text="{notification_text}"]'
            # Check if the notification exists
            if self.ui.check_if_exists(path=notification_xpath, boolean=True):
                status = True
                msg = f"Successfully found notification: '{notification_text}'"
                self.logger.info(msg)
            else:
                self.logger.warning(msg)

        except Exception as e:
            self.logger.error(f"An error occurred while validating notification: {e}")
            msg = f"An error occurred while validating notification: {e}"

        return status, msg
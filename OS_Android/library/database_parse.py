pytest_plugins = ("helpers_namespace", "pytest_ordering")
import pytest
import json
import logging
import os
import datetime
import platform
import allure
import sqlite3
import re
import pathlib
import zipfile
import time
from sqlite3 import Error
from pathlib import Path, PureWindowsPath, PurePosixPath
from common_lib.common.logger import Logger
from OS_Android.library.ops_system import SysOps
sys_ops = SysOps()
output = sys_ops.get_zcc_version()
version = output[2].split("=")
zcc = version[1].split(".")
zcc_version = int(zcc[0])

log_path = os.getcwd() + f"\\OS_Android\\Temp_logs"


class DbHandler:
    def __init__(self):
        self.connection = None
        self.queryresponse = None

    def get_full_db_name(src: str, module: str) -> list:
        """

        This method is get full name of Db
        Return: List of DB

        """

        res = []
        files = os.listdir(src)
        for item in files:
            if module in item:
                res.append(item)
        return res

    def create_db_connection(self, path: str) -> None:
        """Initializes DB connection as self.connection

        Args:
            path (str): absolute path to DB file

        Returns:
            self.connection (sqlite3 row Object): Sqlite Connection object that represents the database
        """
        try:
            self.connection = sqlite3.connect(path)
            self.connection.row_factory = sqlite3.Row
            print("Connection to {} successful".format(path))
        except Exception as e:
            print("*ERROR*The error {} occurred".format(e))
            raise Exception('Failed to connect to database file: {}'.format(path))

    def close_db_connection(self) -> None:
        """
        This method is to close DB connection
        Return: None

        """
        if self.connection is not None:
            self.connection.close()
            self.connection = None

    def execute_query(self, query: str, throw_excep: bool = False) -> list:
        ''' Executes SQL query.

        Args:
            query (str): SQL query string

        Returns
            result (list): a list of dictionary like object, each object is a row that can be accessed by column name
        '''
        cursor = self.connection.cursor()
        result = None
        try:
            cursor.execute(query)
            result = cursor.fetchall()
            if throw_excep: return [True, result]
            return result
        except Exception as e:
            if throw_excep: return [False, f"{e}"]
            pytest.skip(str(e))

    def execute_insert_query(self,
                             query: str,
                             throw_exce: bool = False) -> None:
        ''' Executes SQL query.

        Args:
            query (str): SQL insert query string

        Returns
            result None
        '''
        cursor = self.connection.cursor()
        result = None
        try:
            cursor.execute(query)
            self.connection.commit()
            if throw_excep: return [True, result]
            return result
        except Exception as e:
            if throw_excep: return [False, f"{e}"]
            pytest.skip(str(e))

    def get_latest_dbfile(self,
                          src: str) -> dict:
        """ Get the latest ZCC DBs

        Returns:
            d: A dictionary containing different type of log as key, and value is the list of logs in ascending order
        """

        d = {'deviceEvents': [], 'uploadStats': [], 'web': [], 'traceroute': [], 'deviceStats': [],
             'deviceInventory': [], 'deviceProfile': [], 'ssit': [], 'trayNotifications': [], 'ucaas': [],
             'bandwidth': []}

        for file in sorted(pathlib.Path(src).iterdir()):
            if re.search(r"upm_device_events.db", file.name):
                d['deviceEvents'].append(file.name)
            elif re.search(r"upm_device_profile.db", file.name):
                d['deviceProfile'].append(file.name)
            elif re.search(r"upm_device_stats.db", file.name):
                d['deviceStats'].append(file.name)
            elif re.search(r"upm_ssit.db", file.name):
                d['ssit'].append(file.name)
            elif re.search(r"upm_device_inventory.db", file.name):
                d['deviceInventory'].append(file.name)
            elif re.search(r"tray_notifications.db", file.name):
                d['trayNotifications'].append(file.name)
            elif re.search(r"upm_traceroute.db", file.name):
                d['traceroute'].append(file.name)
            elif re.search(r"upm_webload.db", file.name):
                d['web'].append(file.name)
            elif re.search(r"upm_ucaas.db", file.name):
                d['ucaas'].append(file.name)
            elif re.search(r"upm_bandwidth_test.db", file.name):
                d['bandwidth'].append(file.name)
            elif re.search(r"upm_upload_stats.db", file.name):
                d['uploadStats'].append(file.name)
        return d


class LogHandler:
    def __init__(self, log_handle: bool = None):
        self.OS = platform.system()
        self.logger = (
            log_handle if log_handle else Logger.initialize_logger(log_file_name="ZCC.log", log_level="INFO"))

    def get_logfile_data(self,
                         src: str) -> str:
        """
        This function returns the text from the file location.
        Args:
            src (str): log file path.
        Returns:
            data (str): data of the log file in string format.
        """
        with open(src, encoding="utf-8") as file:
            data = file.read()
            return data

    def get_latest_logfile(self,
                           src: str) -> dict:
        """ Get the latest ZCC logs

        This function saves logs of the same type in ascending order in dictionary
        If the logs are archived, function will extract the log and remove the archived file from the folder.

        Returns:
            d: A dictionary containing different type of log as key, and value is the list of logs in ascending order
        """

        d = {'upm': [], 'web': [], 'traceroute': [], 'deviceStats': [], 'deviceInventory': [], 'service': [],
             'tray': [],
             'trayhelper': [], 'traymanager': [], 'tunnel': [], 'deviceEvents': [], 'systemEvents': [], 'update': [],
             'capture_adapter': [], 'capture_lwf': [], 'android': []}


        for file in sorted(pathlib.Path(src).iterdir()):

            if re.search(r"ZSAUpm_[0-9-_.]+\.log", file.name) or (
                    self.OS == "Darwin" and re.search(r"com.zscaler.UPMServiceController_[0-9-_.]+\.log", file.name)):
                d['upm'].append(file.name.replace(".zip", ""))
            elif re.search(r"ZSAUpm_ZWebload_.+\.log", file.name):
                d['web'].append(file.name.replace(".zip", ""))
            elif re.search(r"ZSAUpm_ZTraceroute_.+\.log", file.name):
                d['traceroute'].append(file.name.replace(".zip", ""))
            elif re.search(r"ZSAUpm_DeviceStats_.+\.log", file.name):
                d['deviceStats'].append(file.name.replace(".zip", ""))
            elif re.search(r"ZSAUpm_ZDeviceInventory_.+\.log", file.name):
                d['deviceInventory'].append(file.name.replace(".zip", ""))
            elif re.search(r"ZSAService_.+\.log", file.name):
                d['service'].append(file.name.replace(".zip", ""))
            elif re.search(r"ZCC_Tray_.+\.log", file.name):
                d['tray'].append(file.name.replace(".zip", ""))
            elif re.search(r"ZSATrayHelper_.+\.log", file.name):
                d['trayhelper'].append(file.name.replace(".zip", ""))
            elif re.search(r"ZSATrayManager_.+\.log", file.name):
                d['traymanager'].append(file.name.replace(".zip", ""))
            elif re.search(r"ZCC_Tunnel_.+\.log", file.name):
                d['tunnel'].append(file.name.replace(".zip", ""))
            elif re.search(r"ZSAUpm_ZSystemEvents_.+\.log", file.name):
                d['systemEvents'].append(file.name.replace(".zip", ""))
            elif re.search(r"ZSAUpm_ZDeviceEvents_.+\.log", file.name):
                d['deviceEvents'].append(file.name.replace(".zip", ""))
            elif re.search(r"ZSAUpdate_.+\.log", file.name):
                d['update'].append(file.name.replace(".zip", ""))
            elif re.search(r"CaptureAdapters_.+\.pcapng", file.name):
                d['capture_adapter'].append(file.name)
            elif re.search(r"CaptureLWF_.+\.pcapng", file.name):
                d['capture_lwf'].append(file.name)
            if zcc_version < 4 and re.search(r"ZCC_Android_.+\.log", file.name):
                d['tray'].append(file.name.replace(".zip", ""))
            if zcc_version < 4 and re.search(r"ZCC_Android_.+\.log", file.name):
                d['tunnel'].append(file.name.replace(".zip", ""))

        return d

    def get_latest_file_data_with_plugin(self,
                                         path: str,
                                         plugin: str,
                                         latest: int = 1):
        self.zs_log_files = self.get_latest_logfile(path)
        if len(self.zs_log_files[plugin]) >= latest:
            self.latest_file = self.zs_log_files[plugin][len(self.zs_log_files[plugin]) - latest]
            self.latest_file_data = self.get_logfile_data(os.path.join(path, self.latest_file))
            return self.latest_file_data
        else:
            return ""

    def get_realtime_log_data(self, loggers,
                              variables: str,
                              plugin: str):
        """ fetch real time log data
            only meant to fetch real time data from the latest file
        """
        logpath = os.path.join(variables.LOGPATH, self.get_latest_logfile(variables.LOGPATH)[plugin][-1])
        with open(logpath, mode='r', encoding='utf-8') as file:
            file.seek(0, os.SEEK_END)
            while True:
                line = file.readline()
                if not line: continue
                yield line

    def find_log(self,
                 data: str,
                 log_to_check: str) -> list:
        '''
        This function takes a logline and searches for that logline in the passed argument 'log_to_check' data, returning the matching loglines in a list.
        '''
        res = []
        for item in data.split('\n'):
            if log_to_check in item:
                res.append(item)
        return res

    def fetch_device_stats_info(self, data: str, start_pattern: str, end_pattern: str) -> tuple[bool, str, dict[int,dict]]:
        """
        This function collects the device_stats info from Android log file

        Returns:
            policyJson (str) : All the policy JSON available in the latest Android file.
        """
        status = (False, "", None)
        msg = ""

        pattern = re.compile(
            fr'(?ims){start_pattern}.*?{end_pattern}')

        matches = pattern.findall(data)
        if len(matches) == 0:
            status = False
            msg = "No Device stats data found for given plugin in ZCC_Android logs"
            status = (False, msg, None)
            return status
        policies = {}
        is_first = True
        for policy in matches:
            # if is_first:
            #     is_first = False
            #     continue
            # Extract the timestamp from the match
            timestamp_pattern = re.compile(r'(?ims)\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+')
            timestamp_match = timestamp_pattern.search(policy)
            if timestamp_match:
                timestamp = timestamp_match.group()
                dt = datetime.datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S.%f")
                epoch_timestamp = (int(dt.timestamp()) + 19800)
            else:
                timestamp = None
                epoch_timestamp = None

            left_ind = policy.find("{")
            right_ind = policy.rfind("}")
            policy: str = policy[left_ind:right_ind + 1]
            try:
                policy_data = json.loads(policy)
                policies[epoch_timestamp] = policy_data
            except Exception as e:
                raise Exception(f"Error :: Policy fetched Failed from the log :: {e}")

        return True, "Policy fetched from the logs", policies

    def find_log_line_timestamp_in_unix(self, data: str, pattern: str,first_hit_exit:bool = False):

        """
        This function will return te timestamp of the pattern
        If it occurs multiple times, it will return list of timestamp in UNIX format
        It performs the following steps:
            1.
            Args:
                self (object): Instance of the test class.
                data - upm log file data
                pattern - Pattern to search for
            Returns:
                List of timestamp
            Raises:
                AssertionError: If any of the steps fail.

        """

        result = []

        # Parsing log lines to extract timestamps for pattern
        for log_line in data.split("\n"):
            if pattern in log_line:
                timestamp_pattern = r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{6}\(\+\d{4}\)"
                timestamp_match = re.search(timestamp_pattern, log_line)

                if timestamp_match:
                    timestamp_str = timestamp_match.group()
                    timestamp_str = timestamp_str[:-7]  # Remove timezone offset
                    timestamp = datetime.datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S.%f")
                    unix_timestamp = int(timestamp.timestamp())
                    print(f"Unix Timestamp found for {pattern} : {unix_timestamp}")
                    result.append(unix_timestamp)
                    if first_hit_exit:
                        return result
                    first_hit_exit=False

        return result

    def get_regular_policy_json_from_log(self, path: str):
        """
        This function collects the policy JSON from ZSAUpm log file.

        Returns:
            policyJson (str) : All the policy JSON available in the latest ZSAUpm File.
        """
        status = (False, "", None)
        msg = ""
        upm_log_data = self.get_latest_file_data_with_plugin(path, 'upm')
        if not upm_log_data:
            return (False, "No UPM data found in the log folder", None)

        pattern = re.compile(r'(?ims)ZPD: downloadUpmPolicy succeeded with 200 OK.*?ZPD: Policy Downloader received updated Policy')

        matches = pattern.findall(upm_log_data)
        if len(matches) == 0:
            status = False
            msg = "No Policy JSON is available in the ZSAUpm Log file"
            status = (False, msg, None)
            return status
        policies = []
        for policy in matches:
            left_ind = policy.find("{")
            right_ind = policy.rfind("}")
            policy: str = policy[left_ind:right_ind + 1]
            error_pattern = re.compile(r'(?ims)[\d]{4}-[\d]{2}-[\d]{2}')
            new_policy = ""
            for line in policy.splitlines():
                if not error_pattern.match(line):
                    new_policy += line
            try:
                policy_json = json.loads(new_policy)
                policies.append(policy_json)
            except Exception as e:
                raise Exception(f"Error :: Policy fetched Failed from the log :: {e}")

        return (True, "Policy fetched from the logs", policies)

    def get_dt_policy_json_from_log(self, return_json = True):
        """
        This function collects the policy JSON from ZSAUpm log file.

        Returns:
            policyJson (str) : All the policy JSON available in the latest ZSAUpm File.
        """
        status = (False, "", None)
        msg = ""
        upm_log_data = self.get_latest_file_data_with_plugin(log_path, 'upm')
        if not upm_log_data:
            return (False, "No UPM data found in the log folder", None)

        upm_log_data = upm_log_data[::-1]

        #reverse search
        pattern = re.compile(
            r'(?ims)noisses TL devieceR :DPZ.*?KO 002 htiw dedeeccus yciloPmpUdaolnwod :DPZ')
        matches = pattern.findall(upm_log_data)
        if len(matches) == 0:
            status = False
            msg = "No DT Policy JSON is available in the ZSAUpm Log file"
            status = (False, msg, None)
            return status
        policies = []
        for policy in matches:
            policy_reverse = policy[::-1]
            left_ind = policy_reverse.find("{")
            right_ind = policy_reverse.rfind("}")
            policy: str = policy_reverse[left_ind:right_ind + 1]
            error_pattern = re.compile(r'(?ims)[\d]{4}-[\d]{2}-[\d]{2}')
            new_policy = ""

            for line in policy.splitlines():
                if not error_pattern.match(line):
                    new_policy += line

            if return_json:
                try:
                    policy_json = json.loads(new_policy)
                    policies.append(policy_json)
                except Exception as e:
                    raise Exception(f"Error :: DT Policy fetched Failed from the log :: {e}")
            else:
                policies.append(new_policy)

        if len(policies) == 0:
            return False,"Error :: DT Policy fetched Failed from the log ::", policies

        policies = policies[::-1]
        return (True, "DT Policy fetched from the logs", policies)

    def get_final_upload_json_from_upm_log(self, data: str) -> list[str, str, int, dict]:
        """
            This function collects upload JSON for DT session from ZSAUpm log file.

            Returns:
                policyJson (str) : All the upload JSON available in the latest ZSAUpm File with stime, etime, sessionid.
        """
        result = []
        pattern=re.compile(r'INF ZST Fetching LT stats from ([\d]*) to ([\d]*).*?ltIdStateMapToUploader id = ([\d]*).*?ZLTU: Final JSON upload string(.*?)(?=ZLTU: About to upload Json of length:)',re.DOTALL)
        responses = pattern.findall(data)

        for res in responses:
            stime: str = res[0]
            etime: str = res[1]
            lt_session_id = int(res[2])
            upload_json:str = res[3]
            error_pattern = re.compile(r'(?ims)[\d]{4}-[\d]{2}-[\d]{2}')
            new_json = ""

            for line in upload_json.splitlines():
                if not error_pattern.match(line):
                    new_json += line

            uploadJsonString: str = new_json
            lind = uploadJsonString.find("{")
            rind = uploadJsonString.rfind("}")
            uploadJsonString = uploadJsonString[lind:rind + 1]

            pattern = re.compile(r'\n.*DBG')
            uploadJsonString = re.sub(pattern, "", uploadJsonString)

            pattern = re.compile(r'\s+')
            uploadJsonString = re.sub(pattern, "", uploadJsonString)

            try:
                uploadJson = json.loads(uploadJsonString)
                result.append([stime, etime, lt_session_id, uploadJson])
            except Exception as e:
                raise Exception(f"Error :: Policy fetched Failed from the log :: {e}")

        return result

    def check_regular_session_creating_new_tpg_session_on_each_upload(self):
        """
        This function check whether regular session creating new tpg session on each upload

        Returns:
            Status (Boolean) : Return true if condition passed else False.
        """
        status = True
        msg = ""

        upm_log_data = self.get_latest_file_data_with_plugin(log_path, 'upm')
        if not upm_log_data:
            return False, f"No UPM log found in {log_path}"

        pattern = re.compile(r'(?ims)ZTCM: uploadData: send data.*? session is null. calling createTPGSession(.*?)ZSUCore: data upload successfully')
        matches = pattern.findall(upm_log_data)

        # Uploader Failed to create tpg session to upload data
        if len(matches) == 0:
            msg = "Uploader failed to create tpg session to upload data to the TPG"
            status = False
        elif len(matches) == 1:
            msg = ("If your data collection is more than 5m or more and uploader is creating"
                   " tpg session less than twice is Wrong, check logs and debug")
            status = False
        else:
            msg = "Uploader for regular session uploads a new connection to TPG"

        return status, msg

    def check_lt_session_uses_persistent_tpg_connection_on_each_upload(self, session_duration:int):
        """
        This function check whether DT session uses same tpg connection for each upload

        Returns:
            Status (Boolean) : Return true if condition passed else False.
        """
        status = True
        msg = ""

        upm_log_data = self.get_latest_file_data_with_plugin(log_path, 'upm')
        if not upm_log_data:
            return False, f"No UPM log found in {log_path}"

        pattern = re.compile(r'ZTCM: uploadDTData: send data via existing session')
        matches = pattern.findall(upm_log_data)

        # ZLTU: TPG metrics is not using persistent connection
        if len(matches) == session_duration - 1:
            msg = "LT session uses persistent connection"
            status = True
        else:
            msg = "TPG metrics is not using persistent connection => ZTCM: uploadDTData: send data via existing session"
            status = False


        return status, msg

    def count_pattern_occurence_in_the_logs(self, plugin:str, pattern_to_search:str, start_line:str = None):
        """
        This function count the occurence of particular pattern in log file

        Returns:
            Status (Boolean) : Return true if condition passed else False.
        """
        count = 0
        msg = ""

        log_data = self.get_latest_file_data_with_plugin(path=log_path,plugin= plugin)
        if not log_data:
            return False, f"No UPM log found in {log_path}"

        new_log_data = ""
        start_pattern_found = False
        if start_line:
            for line in log_data.splitlines():
                if start_line not in line:
                    continue
                else:
                    start_pattern_found = True

                if start_pattern_found:
                    new_log_data += line
            log_data = new_log_data

        pattern = re.compile(pattern_to_search)
        matches = pattern.findall(log_data)

        if len(matches) == 0:
            msg = "No match found for given pattern"
        else:
            msg = f"Pattern Found in the {plugin} log at log path ={log_path}"
            count = len(matches)

        return count, msg

    def validate_type_data_presence_lt_upload_json(self, stime, etime, upload_json, type):
        """
        This function check whether webload data is present or not in lt upload json to the tpg from upm logs

        Returns:
            Status (Boolean) : Return true if condition passed else False with fail message.
        """
        status = True
        msg = ""

        # Validating data type for mtr (traceroute)
        if type == 'mtr':
            # Validating mtr probes is present or not in Final json upload string for probe = {current_probe} "
            # "between stime = {stime} to etime = {etime}
            if upload_json.get("mtr") is None or upload_json["mtr"].get("probes") is None:
                status = False
                msg = (f"'mtr' invalid data or data is not uploaded to tpg from "
                       f"stime = {stime} to etime = {etime}")

                return status, msg

            # Validating cloud path probes is valid type or not in Final json upload string for probe = {current_probe} "
            # "between stime = {stime} to etime = {etime}
            app = upload_json['mtr']['probes'][0]
            probe_type = app["app"]["type"]
            if len(probe_type) == 0 or probe_type != "mtr":
                status = False
                msg = (f" {probe_type} is not a valid type for cloud Path probe from "
                       f"stime = {stime} to etime = {etime}")
                return status, msg

            return True, f"Traceroute data is uploaded to the tpg in upload json for stime = {stime} to etime = {etime}"

        # Validating data type for web (webprobe)
        if type == 'web':
            # Validating web probes is present or not in Final json upload string for probe = {current_probe} "
            # "between stime = {stime} to etime = {etime}
            if upload_json.get("apps") is None or upload_json["apps"].get("probes") is None:
                status = False
                msg = (f"'apps' invalid data or data is not uploaded to tpg for DT session from "
                       f"stime = {stime} to etime = {etime}")
                return status, msg

            # Validating web probes is valid type or not in Final json upload string for probe = {current_probe} "
            # "between stime = {stime} to etime = {etime}
            for app in upload_json['apps']['probes']:
                probe_type = app["app"]["type"]

                if probe_type in ["cpu", "mem", "disk", "ifstat", "wifi", "battery", "mtr"]:
                    continue

                if probe_type == "web":
                    status = True
                else:
                    status = False
                    msg = (f"{probe_type} is not a valid web probe type from"
                           f" stime = {stime} to etime = {etime}")
                    return status, msg

            return True, f"Webprobe data is uploaded to the tpg in upload json for stime = {stime} to etime = {etime}"

        # Validating data type for device stats("cpu", "mem", "disk", "ifstat", "wifi", "battery")
        if type == 'device_stats':

            # Validating app probes is present or not in Final json upload string for probe = {current_probe} "
            # "between stime = {stime} to etime = {etime}
            if upload_json.get("apps") is None or upload_json["apps"].get("probes") is None:
                status = False
                msg = (f"'apps' invalid data or data is not uploaded to tpg for from "
                       f"stime = {stime} to etime = {etime}")
                return status, msg

            # Validating device_stats probes is valid type or not in Final json upload string for probe = {current_probe} "
            # "between stime = {stime} to etime = {etime}
            for app in upload_json['apps']['probes']:
                probe_type = app["app"]["type"]

                if probe_type == "mtr":
                    continue

                if probe_type not in ["cpu", "mem", "disk", "ifstat", "wifi", "battery"]:
                    status = False
                    msg = (f"{probe_type} device stats data is not uploaded to tpg from "
                           f"stime = {stime} to etime = {etime}")

            return True, (f"DeviceStats data for all types ['cpu', 'mem', 'disk','ifstat, 'wifi','battery'] "
                          f"is uploaded to the tpg in upload json for stime = {stime} to etime = {etime}")

        # If incorrect data type passed as argument
        return False, ("Provide Valid type to check like 'web' for webprobe || 'mtr' for traceroute ||"
                       " 'device_stats' for 'cpu', 'mem'...  all device stats data type")

    def fetch_zdx_server_address_from_logs(self):
        """
        Fetches server address from tpgHostname[smres.zdxbeta.net] logs.

        Args:
            logs (str): The log string to search in.

        Returns:
            list: A list of matches found in the logs.
        """
        status = True
        msg= ""
        log_data = self.get_latest_file_data_with_plugin(path=log_path, plugin='upm')
        if not log_data:
            raise Exception(f"No log found in {log_path} for UPM")

        pattern = re.compile(r'createTPGSession: policy download succeeded with host = (.*?). Returned code:200')
        matches = re.findall(pattern, log_data)
        if len(matches)==0:
            status = False
            msg = "No tpg hostname found in the logs"
        else:
            msg = matches[0]

        return status, msg

    def get_plugins_details(self):
        """
        This function retrieves the details of plugins (whether they are enabled or disabled) based on the latest policy
        JSON fetched from the log. It processes the policy data and updates the plugin status flags accordingly.

        Returns:
            tuple: A tuple containing:
                - bool: Success flag (`True` if successful, `False` otherwise)
                - dict or None: The plugin status dictionary if successful, `None` if an error occurred.
                - str: A message indicating the result (either success message or error details).

        Example:
            Returns (True, plugins, "PASS") on success
            Returns (False, None, "Error message") on failure
        """

        def safe_int(value, default=0):
            """Safely converts a value to an integer, returning the default if conversion fails."""
            try:
                return int(value)
            except (TypeError, ValueError):
                return default

        latest_policy_json = ''
        try:
            status, msg, policies = self.get_regular_policy_json_from_log(path=log_path)
            if not policies:
                error_message = "Unable to find Policy JSON from upm log"
                return (False, None, error_message)

            latest_policy_json = policies[-1]
        except Exception as e:
            error_message = f"Failed to fetch policies from log: {e}"
            return (False, None, error_message)

        plugins = {
            'traceroute': [0, 0, 0],  # [stop_flag, start_flag, enabled_flag]
            'webload': [0, 0, 0],
            # 'device_inventory': [0, 0, 0],
            'device_stats': [0, 0, 0],
            # 'system_events': [0, 0, 0],
            # 'device_events': [0, 0, 0],
            # 'ssit': [0, 0, 0]
        }

        try:
            # Ensure the 'policy' key exists in the JSON
            if 'policy' not in latest_policy_json:
                error_message = "No 'policy' section found in the latest policy JSON"
                return (False, None, error_message)

            policy = latest_policy_json['policy']

            # ssit_enabled = safe_int(policy.get('ssit_policy', {}).get('enabled', 0))
            # device_events_enabled = safe_int(policy.get('device_events_policy', {}).get('enabled', 0))
            # di_enabled = safe_int(policy.get('device_inventory_policy', {}).get('enabled',
            #                                                                     policy.get('device_inventory_policy',
            #                                                                                {}).get('flags', 0)))
            # system_events_enabled = 1 if policy.get('device_events_policy', {}).get('system_events_policy') else 0
            tr_enabled, web_enabled, device_stats_enabled = 0, 0, 0

            monitors = policy.get('monitors', [])
            if not isinstance(monitors, list):
                monitors = []

            for mon in monitors:
                monitor_type = mon.get('type', '').lower()

                if "tracert" in monitor_type:
                    tr_enabled = 1
                if "web" in monitor_type:
                    web_enabled = 1
                if 'devicestats' in monitor_type:
                    device_stats_enabled = 1
        except Exception as e:
            error_message = f"Error processing policy JSON: {e}"
            return (False, None, error_message)

        # plugins['ssit'][2] = ssit_enabled
        # plugins['device_events'][2] = device_events_enabled
        # plugins['device_inventory'][2] = di_enabled
        plugins['webload'][2] = web_enabled
        plugins['traceroute'][2] = tr_enabled
        plugins['device_stats'][2] = device_stats_enabled
        # plugins['system_events'][2] = system_events_enabled

        return (True, plugins, "PASS")

    def verify_plugins_restart_when_policy_received(self):
        """
        Verifies whether the plugins are restarted correctly when a policy is received or updated.

        This function analyzes the logs related to policy updates and checks if the plugins (traceroute, webprobe,
        device stats are properly stopped and restarted as per the
        updated policy.

        Steps:
            1. Extract the latest logs related to policy updates.
            2. Fetch the latest policy and plugin details.
            3. Check for any policy update indications in the logs.
            4. Ensure that plugins are stopped when expected and started again after the update.

        Returns:
            tuple:
                - (bool) Test status (True if all checks pass, False if any plugin fails).
                - (list) List of error messages if any failures occur.
                - (str) "PASS" if the test passed, "FAIL" if it failed.

        """

        try:
            src = self.get_latest_file_data_with_plugin(path=log_path, plugin='upm')
            if not src:
                return False, [], "FAIL, Unable to extract the latest ZSAUpm file data"

            flag = 0
            stop_plugins_log = ""
            start_plugins_log = ""
            Issues = []

            status, msg, policies = self.get_regular_policy_json_from_log(path=log_path)
            if not status or not policies:
                return False, [], f"FAIL:: Failed to fetch policies from logs. Message: {msg}"

            latest_policy_json = policies[-1] if policies else ""

            # Fetch plugin details
            status, plugins, msg = self.get_plugins_details()
            self.logger.info(f"{plugins}")
            if not status or plugins is None:
                error_msg = f"Failed to extract plugin details. Message: {msg}"
                return False, [], error_msg

            for item in src.splitlines():
                # Check for updated policy
                if "policy downloader received updated policy" in item.lower():
                    flag = 1
                    self.logger.info(item)

                # No changes in monitors, skip restarting plugins
                if "skipping update policy" in item.lower():
                    flag = 0

                # Stop plugin logs and update state
                if "policy update, stopping all monitors" in item.lower():
                    stop_plugins_log = item

                plugin_stop_keywords = {
                    "traceroute": 'traceroute',
                    "webload": 'webload',
                    "devicestats": 'device_stats'
                    # "device inventory": 'device_inventory',
                    # "device events": 'device_events',
                    # "system events": 'system_events',
                    # "ssit": 'ssit'
                }

                for keyword, plugin_name in plugin_stop_keywords.items():
                    if keyword in item.lower() and (
                            "stopped" in item.lower() or "skipping" in item.lower()) and flag == 1:
                        plugins[plugin_name][0] = 1
                        self.logger.info(item)

                if "start plugins called" in item.lower():
                    self.logger.info(f"Stop Plugin values - {plugins}")
                    self.logger.info(item)
                    for plugin in plugins:
                        if plugins[plugin][0] == 0 and plugins[plugin][2] == 1:
                            err_msg = f"Policy received but {plugin} plugin did not stop/skip. Log to trace -> {stop_plugins_log}"
                            self.logger.error(err_msg)
                            Issues.append(err_msg)

                # Start plugin logs and update state
                if "start plugins called" in item.lower():
                    start_plugins_log = item

                counter = 0
                for keyword, plugin_name in plugin_stop_keywords.items():
                    if "device stats plugin started" in item.lower() and flag == 1:
                        if counter==0:self.logger.info(item)
                        plugins['device_stats'][1] = 1
                        counter += 1
                    if plugin_name in item.lower() and "started" in item.lower() and flag == 1:
                        self.logger.info(item)
                        plugins[plugin_name][1] = 1

                if "device stats plugin started" in item.lower() and flag == 1:
                    self.logger.info(f"Start Plugin values - {plugins}")
                    for plugin in plugins:
                        if plugins[plugin][1] == 0 and plugins[plugin][2] == 1:
                            err_msg = f"Policy received but {plugin} plugin did not start. Log to trace -> {start_plugins_log}"
                            self.logger.error(err_msg)
                            Issues.append(err_msg)

                    # Teardown
                    for plugin in plugins:
                        plugins[plugin][0] = 0
                        plugins[plugin][1] = 0

                    start_plugins_log = ""
                    stop_plugins_log = ""
                    flag = 0

            if Issues:
                return (False, Issues, "FAIL")
            else:
                self.logger.info(f"SUCCESS: All Plugins were successfully restarted upon policy download/update")
                return (True, [], "PASS")

        except Exception as e:
            error_msg = f"Unexpected error occurred: {str(e)}"
            self.logger.error(error_msg)
            return (False, [error_msg], "FAIL")

    def get_log_file_name(self, plugin_name: str) -> tuple[bool, str]:
        """
        Returns the log file name prefix based on the plugin name and ZCC version.

        Args:
        plugin_name (str): The name of the plugin.

        Returns:
        tuple[bool, str]: A tuple containing a boolean indicating whether the plugin name is valid and the corresponding log file name prefix.

        Raises:
        None
        """


        # Define the plugin name mappings for different ZCC versions
        plugin_mappings = {
            "4": {
                "tray": "ZCC_Android",
                "tunnel": "ZCC_Android",
                "appinfo": "AppInfo"
            },
            "default": {
                "tray": "ZCC_Tray",
                "tunnel": "ZCC_Tunnel"
            }
        }

        # Determine the plugin mappings to use based on the ZCC version
        if zcc_version and zcc_version < 4:
            mappings = plugin_mappings.get("4", {})
        else:
            mappings = plugin_mappings.get("default", {})

        # Check if the plugin name is valid
        if plugin_name in mappings:
            return True, mappings[plugin_name]
        else:
            return False, "Provide correct plugin name"

from OS_Android.library.ops_system import SysOps
sys_ops = SysOps()
operating_system  = sys_ops.android_device_type()
if operating_system == "Android":
    zcc_package_name = 'zscaler.com.zscaler'

else:
    zcc_package_name = 'zscaler.com.zschromeosapp'

class AndroidElements:
    """
    This class will contains xpath of elements

    """
    APP = "zscaler.com.zscaler"
    USERNAME = "//*[@class='android.widget.EditText' and @text='Username']"
    PASSWORD = "//*[@class='android.widget.EditText' and @text='Password']"
    LOGIN_BUTTON = "//*[@class='android.widget.Button' and @text='Login']"
    ACCEPT_BUTTON = "acceptButton"
    INTERNET_UNREACHABLE_ERROR = "//*[@text='Internet unreachable error']"
    SIM_CARD_MANAGER = "//*[@text='SIM card manager']"
    SIM_2 = "//*[@text='SIM 2']"
    UPDATE_POLICY_TOAST = "//*[@text='Policy update success']"
    UPDATE_POLICY_NOTIFICATION = "//*[@class='android.widget.TextView' and @text='Policy update success']"
    MOBILE_DATA = "//*[@text='Mobile data']"
    NETWORK_ERROR = "//*[@text='Network Error']"
    AUTHENTICATE = "//*[@text='AUTHENTICATE']"
    AUTHENTICATED = "//*[@text='Authenticated']"
    CONNECTED = "//*[@text='Connected']"
    CLEAR_ALL = "//*[@text='CLEAR ALL']"
    PROGRESS_TEXT_LOGGING_IN = "progress_text"
    PROGRESS_ICON = "progress_icon"
    ALERT_TITLE_ALLOW_CONNECTION = "alertTitle"
    ALERT_BUTTON_CANCEL = "button2"
    ALERT_BUTTON_OK = "button1"
    STATUS_TEXT = "statusText"
    LOGOUT_PROMPT = "//*[@class='android.widget.TextView' and @text='LOGOUT']"
    STATUS_DETAIL = "statusDetail"
    STATUS_CONTROL_BUTTON = "statusControl"
    STATUS_CONTROL_BUTTON_TEXT = "statusControlText"
    USERNAME_TEXT = "usernameString"
    LOGOUT_BUTTON = "//*[@content-desc='Logout']"
    ALERT_WARNING_LOGOUT = "warningText"
    YOUTUBE_SHORTS="//*[@text='Shorts']"
   #LOGOUT_PASSWORD = "passwordPromptEditText"
    LOGOUT_PASSWORD="//*[@class='android.widget.EditText' and @text='Logout Password']"
    #LOGOUT_PASSWORD_CANCEL_BUTTON = "cancel"
    #LOGOUT_PASSWORD_LOGOUT_BUTTON = "doAction"
    LOGOUT_PASSWORD_LOGOUT_BUTTON ="//*[@class='android.widget.TextView' and @text='LOGOUT']"
    LOGOUT_PASSWORD_FAILED_OK_BUTTON = "//*[@class='android.widget.TextView' and @text='CANCEL']"
    WEBSECURITY_TAB_XPATH = "//android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.widget.RelativeLayout[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.LinearLayout[2]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.widget.RelativeLayout[1]/android.support.v7.widget.RecyclerView[1]/android.widget.RelativeLayout[1]"
    # NOTIFICATIONS_TAB_XPATH = "//android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.widget.RelativeLayout[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.LinearLayout[2]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.widget.RelativeLayout[1]/android.support.v7.widget.RecyclerView[1]/android.widget.RelativeLayout[2]"
    NOTIFICATIONS_TAB_XPATH = "//*[@class='android.widget.TextView' and @text='Notifications']"
    MORE_TAB_XPATH = "//android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.widget.RelativeLayout[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.LinearLayout[2]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.widget.RelativeLayout[1]/android.support.v7.widget.RecyclerView[1]/android.widget.RelativeLayout[3]"
    USERNAME1 = "//hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/androidx.drawerlayout.widget.DrawerLayout/android.view.ViewGroup/android.widget.RelativeLayout/android.widget.ScrollView/android.widget.LinearLayout/android.widget.RelativeLayout/android.widget.RelativeLayout/android.widget.EditText"
    LOG_MODE_LIST = '//android.widget.TextView[@resource-id="android:id/text1"]'
    # LOG_MODE_ERROR_XPATH = "android.widget.ListView[1]/android.widget.TextView[1]"
    # LOG_MODE_WARN_XPATH = "android.widget.ListView[1]/android.widget.TextView[2]"
    # LOG_MODE_INFO_XPATH = "android.widget.ListView[1]/android.widget.TextView[3]"
    # LOG_MODE_DEBUG_XPATH = "android.widget.ListView[1]/android.widget.TextView[4]"
    # LOG_MODE_VERBOSE_XPATH = "//*[@class='android.widget.Button' and @text='Verbose']"
    LOG_MODE_ERROR_XPATH = '//android.widget.CheckedTextView[@resource-id="android:id/text1" and @text="Error"]'
    LOG_MODE_WARN_XPATH = '//android.widget.CheckedTextView[@resource-id="android:id/text1" and @text="Warn"]'
    LOG_MODE_INFO_XPATH = '//android.widget.CheckedTextView[@resource-id="android:id/text1" and @text="Info"]'
    LOG_MODE_DEBUG_XPATH = '//android.widget.CheckedTextView[@resource-id="android:id/text1" and @text="Debug"]'
    LOG_MODE_VERBOSE_XPATH = '//android.widget.CheckedTextView[@resource-id="android:id/text1" and @text="Verbose"]'
    APP_VERSION = "appVersionString"
    DISABLE = "//*[@class='android.widget.TextView' and @text='DISABLE']"
    DISABLE_PASSWORD = "//*[@class='android.widget.EditText' and @text='Disable Password']"
    ZIA_ON_STATUS = "//*[@class='android.widget.TextView' and @text='ON']"
    ZIA_OFF_STATUS="//*[@class='android.widget.TextView' and @text='OFF']"
    ZPA_ON_STATUS = "//*[@class='android.widget.TextView' and @text='ON']"
    ZPA_OFF_STATUS = "//*[@class='android.widget.TextView' and @text='Disabled']"
    # USERNAME="//hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/androidx.drawerlayout.widget.DrawerLayout/android.view.ViewGroup/android.widget.RelativeLayout/android.widget.ScrollView/android.widget.LinearLayout/android.widget.RelativeLayout/android.widget.RelativeLayout/android.widget.EditText"
    BROWSER = "//android.widget.TextView'[@content-desc='HTTP Shortcuts']"
    DOWNLOAD_BUTTON = "//*[@class='android.widget.Button' and @id='button_primary']"
    XFF_HEADER = "/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.view.ViewGroup/android.webkit.WebView/android.webkit.WebView/android.view.View/android.widget.TextView[3]"
    NOT_GOING_VIA_ZSCALER = '''//*[@class='android.view.View' and @text="The request received from you didn't come from a Zscaler IP therefore you are not going through the Zscaler proxy service."]'''
    CANCEL_BUTTON = "//*[@class='android.widget.Button' and @id='button_secondary']"
    NAME_REPORT_ISSUE = "//*[@class='android.widget.EditText' and @text='Name [required]']"
    REPORT_ISSUE = "//*[@class='android.widget.TextView' and @text='Report an Issue']"
    SUCCESS_REPORT_ISSUE = "//*[@class='android.widget.TextView' and @text='Report issue successful']"
    REPORT_ISSUE_CLOSE = "//*[@class='android.widget.TextView' and @text='CLOSE']"
    SEND = "//*[@class='android.widget.Button' and @text='SEND']"
    GMAIL_EXPORT_LOGS = "//*[@class='android.widget.TextView' and @text='Gmail']"
    ANDROID_GMAIL_TO = "//*[@class='android.widget.EditText' and @text='']"
    GMAIL_TO = "//*[@class='android.widget.EditText' and @text='']"
    ANDROID_GMAIL_SEND = "//*[@class='android.widget.Button' and @content-desc='Send']"
    GMAIL_SEND = "//*[@class='android.widget.TextView' and @content-desc='Send']"
    UPDATE = "//*[@class='android.widget.TextView' and @text='Update Policy']"
    RESTART = "//*[@class='android.widget.TextView' and @text='Restart Service']"
    CLEAR_LOGS = "//*[@class='android.widget.TextView' and @text ='Clear Logs']"
    EXPORT_LOGS = "//*[@class='android.widget.TextView' and @text='Export Logs']"
    WITHOUT_LOGIN_EXPORT_LOGS = "//*[@class='android.widget.CheckedTextView' and @text='Export Logs']"
    CANCEL = "//*[@class='android.widget.Button' and @id='cancel']"
    NOTIFICATION = "//*[@class='android.widget.TextView' and @text='More']"
    NOTIFICATION_YES_CLEAR_ALL = "//*[@class='android.widget.TextView' and @text='YES, CLEAR ALL']"
    YES_RESTART = "//*[@class='android.widget.TextView' and @text='YES, RESTART']"
    WEBSECURITY = "//*[@class='android.widget.TextView' and @text='Internet Security']"
    OKTA_USERNAME = '//android.widget.EditText[@resource-id="input28"]'
    OKTA_PASSWORD = '//android.widget.EditText[@resource-id="input36"]'
    OKTA_SIGNIN = '//android.widget.Button[@text="Sign in"]'
    PRIVATE_SECURITY="//*[@class='android.widget.TextView' and @text='Private Access']"
    PRIVATE_ACCESS_TURN_OFF="//*[@class='android.widget.TextView' and @text='TURN OFF']"
    PRIVATE_ACCESS_TURN_ON="//*[@class='android.widget.TextView' and @text='TURN ON']"
    PRIVATE_ACCESS_PASSWORD_BOX = "//*[@class='android.widget.EditText' and @text='Disable Password']"
    PRIVATE_ACCESS_PASSWORD_CANCEL_BUTTON = "//*[@class='android.widget.TextView' and @text='CANCEL']"
    PRIVATE_ACCESS_PASSWORD_OK_BUTTON = "//*[@class='android.widget.TextView' and @text='DISABLE']"
    PRIVATE_ACCESS_DISABLE_INCORRECT_PASSWORD = "//*[@class= 'android.widget.TextView' and @text='This password is incorrect']"
    DISABLED_SERVICE_STATUS = "//*[@class='android.widget.TextView' and @text='Disabled']"
    # OFF_TRUSTED_NW_SERVICE_STATUS = "//*[@class='android.widget.TextView' and @text='Off Trusted Network']"
    POLICY_NAME = "//*[@class='android.widget.TextView' and @text='automation_win_10']"
    BURGER_MENU = "//android.widget.ImageButton[@content-desc='Open navigation drawer']"
    CLOUD_NAME = "/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/androidx.drawerlayout.widget.DrawerLayout/android.widget.FrameLayout/androidx.recyclerview.widget.RecyclerView/androidx.appcompat.widget.LinearLayoutCompat[5]/android.widget.CheckedTextView"
    CLOUD_NAME_FIELD ="//*[@class='android.widget.EditText' and @text='Cloud Name']"
    CLOUD_NAME_SAVE ="//*[@class='android.widget.TextView' and @text='SAVE']"
    EMPTY_CLOUD_NAME ="//*[@class='android.widget.TextView' and @text='Cloud name is empty']"
    CLOUD_NAME_CLEAR ="//*[@class='android.widget.TextView' and @text='CLEAR']"
    ACTIVATE_ADMIN ="//*[@class='android.widget.Button' and @text='Activate']"
    CANCEL_ADMIN="//*[@class='android.widget.Button' and @text='Cancel']"
    KLMS_CHECKBOX="//*[@class='android.widget.CheckBox' and @resource-id='com.samsung.klmsagent:id/checkBox1']"
    KLMS_CONFIRM="//*[@class='android.widget.TextView' and @text='CONFIRM']"
    CA_CERTIFICATE="//*[@class='android.widget.TextView' and @text='CA certificate']"
    ZSCALER_VPN="//*[@class='android.widget.TextView' and @text='Zscaler']"
    KLMS_CANCEL = "//*[@class='android.widget.TextView' and @text='CANCEL']"
    KLMS_AGREE = "//*[@class='android.widget.TextView' and @text='NEXT']"
    UNINSTALL = "//*[@class='android.widget.TextView' and @text='Uninstall']"
    UNINSTALL_WITHOUT_LOGIN = "//*[@class='android.widget.CheckedTextView' and @text='Uninstall']"
    UNINSTALL_BUTTON = "//*[@class='android.widget.TextView' and @text='UNINSTALL']"
    UNINSTALL_PASSWORD_BOX = "//*[@class='android.widget.EditText' and @text='Enter Password']"
    UNINSTALL_INCORRECT_PASSWORD = "//*[@class= 'android.widget.TextView' and @text='This password is incorrect']"
    UNINSTALL_CANCEL = "//*[@class='android.widget.TextView' and @text='CANCEL']"
    UNINSTALL_PASSWORD_BOX_OK_BUTTON = "//*[@class='android.widget.TextView' and @text='UNINSTALL']"
    VPN_OK_BUTTON = "//*[@class='android.widget.Button' and @text='OK']"
    CHROMEOS_VPN_OK_BUTTON = '/hierarchy/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.ScrollView/android.widget.LinearLayout/android.widget.Button[2]'
    CHROMEOS_PROXY_SERVER = '/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.view.ViewGroup/android.widget.RelativeLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.LinearLayout[1]/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.ScrollView/android.widget.LinearLayout/android.widget.LinearLayout/android.widget.LinearLayout[2]/android.widget.TextView[2]'
    CHROMEOS_TIME_CONNECTED = '/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.view.ViewGroup/android.widget.RelativeLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.LinearLayout[1]/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.ScrollView/android.widget.LinearLayout/android.widget.LinearLayout/android.widget.LinearLayout[2]/android.widget.TextView[6]'
    CHROMEOS_TOTAL_BYTES_RECEIVED = "//*[@class='android.widget.TextView' and @resource-id='zscaler.com.zschromeosapp:id/bytesReceived']"
    CHROMEOS_TOTAL_BYTES_SENT = "//*[@class='android.widget.TextView' and @resource-id='zscaler.com.zschromeosapp:id/bytesSent']"
    AD_HOC_MAX_CHARS_TEXT = "//*[@class='android.widget.TextView' and @text='Invalid cloud name. Less than 20 characters allowed.']"
    TRAFFIC_VIA_ZSCALER = "//*[@class='android.widget.TextView' and @package='com.android.chrome']"
    CHROMEOS_SETTINGS_CREDENTIALS = "//*[@class='android.widget.TextView' and @text='Credentials']"
    CHROMEOS_INSTALL_CERT_FROM_SD_CARD = "//*[@class='android.widget.TextView' and @text='Install from SD card']"
    WIFI_SWITCH = "//*[@class='android.widget.Switch' and @content-desc='Wi-Fi' and @resource-id='android:id/switch_widget']"
    CAUTION_RULE = "//*[@class='android.widget.Button' and @text='Continue']"
    BLOCK_RULE = "//*[@class='android.view.View' and @text='Website blocked']"
    AUP_TEXT = "//*[@class='android.widget.TextView' and @text='Automated AUP']"
    AUP_ACCEPT = "//*[@class='android.widget.Button' and @text='ACCEPT']"
    SSL_CERT_TEXT = "//*[@class='android.widget.TextView' and @text='Verified By: Zscaler Inc.,ST=California']"
    LOCK_ICON = "//*[@class='android.widget.ImageView' and @resource-id='org.mozilla.firefox:id/mozac_browser_toolbar_security_indicator']"
    SECURE_CONNECTION = "//*[@class='android.widget.TextView' and @text='Connection is secure']"
    MOD_RESTART_ZCC = "//*[@class='android.widget.TextView' and @text='Zscaler Client Connector']"
    RESTART_ZCC_OK = "//*[@class='android.widget.TextView' and @text='OK']"
    MODZ_POP_UP = "//*[@class='android.widget.Toast' and @text='Cloud name saved as mod.z']"
    ADHOC_VALIDATION_FAILED = "//*[@class='android.widget.TextView' and @text='cloud name validation failed']"
    SETTINGS_SEARCH_BAR = "//*[@class='android.view.ViewGroup' and @resource-id='com.android.settings:id/search_action_bar']"
    ANDROID_SEARCH_BAR = "//*[@class='android.widget.Button' and @content-desc='Search']"
    ALLOW = "//*[@class='android.widget.Button' and @text='Allow']"
    BAD_SERVER_CERT_XPATH = "//*[@class='android.view.View' and @text='Website blocked']"
    AGREE = "//*[@class='android.widget.Button' and @text='Agree']"
    INTERNET_SECURITY_TAB = "//*[@class='android.widget.ImageView' and @content-desc='Internet Security']"
    INTERNET_SECURITY_DISABLE_INCORRECT_PASSWORD = "//*[@class= 'android.widget.TextView' and @text='This password is incorrect']"
    INTERNET_SECURITY_PASSWORD_CHROMEOS_ACCEPT_BUTTON = "//*[@class='android.widget.TextView' and @resource-id='zscaler.com.zschromeosapp:id/ok']"
    INTERNET_SECURITY_TURN_OFF_TAB = "//*[@class='android.widget.TextView' and @text='TURN OFF']"
    INTERNET_SECURITY_TURN_ON_TAB = "//*[@class='android.widget.TextView' and @text='TURN ON']"
    GET_ZCC_LOGS_TEST_APP = "//*[@class='android.widget.RadioButton' and @text='Get Logs of ZCC']"
    PERFORM_ACTION_TEST_APP = "//*[@class='android.widget.Button' and @text='PERFORM ACTION']"
    OPEN_NAVIGATION_DRAWER = '//android.widget.ImageButton[@content-desc="Open navigation drawer"]'
    ADHOC_CLOUD_NAME_RESET_TOAST = '//android.widget.Toast[@text="Cloud name has been reset"]'
    DIGITAL_EXPERIENCE_TAB = "//*[@class='android.widget.ImageView' and @content-desc='Digital Experience']"
    MORE_TAB = "//*[@class='android.widget.ImageView' and @content-desc='More']"
    PRIVATE_SECURITY_TAB = "//*[@class='android.widget.ImageView' and @content-desc='Private Access']"
    DIGITAL_EXPERIENCE_RESTART_YES = "//*[@class='android.widget.TextView' and @text='YES, RESTART']"
    ZDX_ON_STATUS = "//*[@class='android.widget.TextView' and @text='ON']"
    ZDX_OFF_STATUS = "//*[@class='android.widget.TextView' and @text='OFF']"
    ZDX_UNSUPPORTED_NETWORK = "//*[@class='android.widget.TextView' and @text='Unsupported Network']"
    LEARN_MORE_LOCATOR = "//android.widget.TextView[@text='Learn More']"
    ZDX_CONNECTING_STATUS = "//*[@class='android.widget.TextView' and @text='Connecting...']"
    ZDX_DISCONNECTING_STATUS = "//*[@class='android.widget.TextView' and @text='Disconnecting...']"
    DIGITAL_EXPERIENCE_TURNED_OFF_NOTIFICATION = "//*[@class='android.widget.TextView' and @text='Digital Experience Turned Off.']"
    DIGITAL_EXPERIENCE_TURNED_ON_NOTIFICATION = "//*[@class='android.widget.TextView' and @text='Digital Experience Turned On.']"
    DIGITAL_EXPERIENCE_PASSWORD_CANCEL_BUTTON = "//*[@class='android.widget.TextView' and @text='CANCEL']"
    VPN_REQUEST_OK = "//*[@resource-id= 'android:id/button1' and @text='OK']"
    LOG_MODE_VERBOSE_XPATH = '//android.widget.CheckedTextView[@resource-id="android:id/text1" and @text="Verbose"]'
    DISABLE_REASON = "//*[@class='android.widget.EditText' and @text='Disable Reason (Max 200 characters)']"
    DARK_MODE = "//*[@class='android.widget.Switch' and @content-desc='Dark theme']"
    MODE_VALIDATION_TRUE = "//*[@class='android.widget.Switch' and @checked='true']"
    MODE_VALIDATION_FALSE = "//*[@class='android.widget.Switch' and @checked='false']"
    SIGNIN_BUTTON_XPATH = '//android.widget.Button[@text="Sign in"]'
    CLIENT_IP = '{zcc_package_name}:id/clientIp'
    NOTIFICATION_REMOVE=f"//*[@class='android.widget.ImageView' and @resource-id='{zcc_package_name}:id/notificationRemove']"
    LOGOUT_PASSWORD_CANCEL_BUTTON =f"//*[@resource-id= '{zcc_package_name}:id/ok' and @text='LOGOUT']"
    NOTIFICATION_DELETE_LOCATOR = f"//*[@class='android.widget.ImageView' and @resource-id='{zcc_package_name}:id/notificationRemove']"
    PROXY_SERVER = f"//*[@class='android.widget.TextView' and @resource-id='{zcc_package_name}:id/server']"
    TIME_CONNECTED = f"//*[@class='android.widget.TextView' and @resource-id='{zcc_package_name}:id/timeConnected']"
    TOTAL_BYTES_SENT = f"//*[@class='android.widget.TextView' and @resource-id='{zcc_package_name}:id/bytesSent']"
    TOTAL_BYTES_RECEIVED = f"//*[@class='android.widget.TextView' and @resource-id='{zcc_package_name}:id/bytesReceived']"
    NW_SERVICE_STATUS = f'//android.widget.TextView[@resource-id="{zcc_package_name}:id/networkstatusDetail"]'
    BROKER_IP = f'//android.widget.TextView[@resource-id="{zcc_package_name}:id/brokerIp"]'
    SME_IP=f'//android.widget.TextView[@resource-id="{zcc_package_name}:id/server"]'
    INVALID_CLOUD_NAME =f"//*[@class='android.widget.TextView' and @resource-id='{zcc_package_name}:id/errorText']"
    INSTALL_CERTIFICATES=f"//*[@class='android.widget.TextView' and @text='Install Certificates' and @resource-id='{zcc_package_name}:id/text']"
    ROOT_CERT=f"//*[@class='android.widget.TextView' and @text='Zscaler root cert' and @resource-id='{zcc_package_name}:id/zscaler_root_cert']"
    DEVICE_STORAGE=f"//*[@class='android.widget.TextView' and @text='Install from device storage' and @resource-id='{zcc_package_name}:id/install_from_device_storage']"
    INTERNET_SECURITY_PASSWORD_BOX = f"//*[@class='android.widget.EditText' and @resource-id='{zcc_package_name}:id/passwordPromptEditText']"
    INTERNET_SECURITY_PASSWORD_CANCEL_BUTTON = f"//*[@class='android.widget.TextView' and @resource-id='{zcc_package_name}:id/cancel']"
    INTERNET_SECURITY_PASSWORD_ACCEPT_BUTTON = f"//*[@class='android.widget.TextView' and @resource-id='{zcc_package_name}:id/ok']"
    PACKET_CAPTURE_OFF = f"//*[@resource-id='{zcc_package_name}:id/packetCaptureSwitch' and @checked='false']"
    PACKET_CAPTURE_ON = f"//*[@resource-id='{zcc_package_name}:id/packetCaptureSwitch' and @checked='true']"
    PACKET_CAPTURE_PRESENT = f"//*[@resource-id='{zcc_package_name}:id/packetCaptureSwitch' and @class='android.widget.Switch']"
    CLEAR_LOGS_TOAST = '(//android.widget.ImageView[@resource-id="{zcc_package_name}:id/optionIcon"])[3]'
    ADHOC_CLOUD_NAME = f"//*[@resource-id= '{zcc_package_name}:id/design_menu_item_text' and @text='Cloud Name']"
    GIVE_ADHOC_NAME = f'//android.widget.EditText[@resource-id="{zcc_package_name}:id/passwordPromptEditText"]'
    SAVE_ADHOC_NAME = f"//*[@resource-id= '{zcc_package_name}:id/ok' and @text='SAVE']"
    ZCC_HOME = f'//android.widget.LinearLayout[@resource-id="{zcc_package_name}:id/action_bar_root"]'
    CLEAR_ADHOC_CLOUD_NAME = f"//*[@resource-id= '{zcc_package_name}:id/cancel' and @text='CLEAR']"
    ISSUE_NAME=f'//android.widget.EditText[@resource-id="{zcc_package_name}:id/userName"]'
    ISSUE_CC_EMAIL=f'//android.widget.EditText[@resource-id="{zcc_package_name}:id/emailCC"]'
    ISSUE_COMMENTS=f'//android.widget.EditText[@resource-id="{zcc_package_name}:id/comments"]'
    LICENSE_CREDITS=f"//*[@resource-id= '{zcc_package_name}:id/design_menu_item_text' and @text='Licenses & Credits']"
    THIRD_PARTY_SOFTWARE =f"//*[@resource-id= '{zcc_package_name}:id/thirdPartyLicensesListButton' and @text='Third Party Software']"
    LICENSE_AGREEMENT =f"//*[@resource-id= '{zcc_package_name}:id/zscalerLicenseAgreementButton' and @text='License Agreement']"
    DIGITAL_EXPERIENCE_CLEAR_DATA_LABEL = f'//android.widget.TextView[@resource-id="{zcc_package_name}:id/text" and @text="Clear ZDX Data"]'
    DIGITAL_EXPERIENCE_CLEAR_DATA_ACCEPT_BUTTON = f'//android.widget.TextView[@resource-id="{zcc_package_name}:id/ok"]'
    DIGITAL_EXPERIENCE_RESTART = f'//android.widget.TextView[@resource-id="{zcc_package_name}:id/text" and @text="Restart ZDX Service"]'
    ZPA_SERVICE_STATUS = f'// android.widget.TextView[ @ resource - id = "{zcc_package_name}:id/statusText"]'
    LEARN_MORE_OK = f"//android.widget.Button[@resource-id='{zcc_package_name}:id/ok_button']"
    ZDX_UNSUPPORTED_NETWORK_ERROR_STRING = f"//*[@class='android.widget.TextView' and @text='Current network is not supported by ZDX yet.']"
    ZDX_USERNAME = f'//android.widget.TextView[@resource-id="{zcc_package_name}:id/usernameString"]'
    ZDX_AUTH_STATUS_TEXT = f'//android.widget.TextView[@resource-id="{zcc_package_name}:id/authStatusText"]'
    ZDX_SERVER = f'//android.widget.TextView[@resource-id="{zcc_package_name}:id/server"]'
    ZDX_TIME_CONNECTED = f'//android.widget.TextView[@resource-id="{zcc_package_name}:id/timeConnected"]'
    ZDX_VERSION = f'//android.widget.TextView[@resource-id="{zcc_package_name}:id/serviceVersion"]'
    DIGITAL_POWER_BUTTON_ON = f"//*[@resource-id= '{zcc_package_name}:id/statusControlText' and @text='TURN ON']"
    DIGITAL_POWER_BUTTON_OFF = f"//*[@resource-id= '{zcc_package_name}:id/statusControlText' and @text='TURN OFF']"
    DIGITAL_EXPERIENCE_PASSWORD_BOX = f"//*[@resource-id= '{zcc_package_name}:id/passwordPromptEditText' and @text='Disable Password']"
    PASSWORD_DISABLE_REASON_OK_BUTTON = f"//*[@resource-id= '{zcc_package_name}:id/ok' and @text='DISABLE']"
    ZDX_DISABLE_INCORRECT_PASSWORD = f"//*[@resource-id= '{zcc_package_name}:id/errorText' and @text='This password is incorrect']"
    ZPA_REAUTH_NOTIFICATION = f"//*[@resource-id='{zcc_package_name}:id/reauthNotificationSwitch' and @checked=check]"
    REPORT_ISSUE_OK= f"//*[@resource-id='{zcc_package_name}:id/ok_button' and @text='OK']"
    ZPA_DISABLE_CANCEL=f"//*[@resource-id= '{zcc_package_name}:id/cancel' and @text='CANCEL']"
    SERVICE_STATUS_ON = f"//*[@resource-id= '{zcc_package_name}:id/statusText' and @text='ON']"
    SERVICE_STATUS_OFF = f"//*[@resource-id= '{zcc_package_name}:id/statusText' and @text='OFF']"
    CONNECTIVITY_HEADER = f"//android.widget.TextView[@resource-id='{zcc_package_name}:id/connectivity_sectionHeader']"
    TROUBLESHOOT_HEADER = f"//android.widget.TextView[@text='Troubleshoot']"
    NO_SERVICE_ASSIGN = "//*[@class='android.widget.TextView' and @text='No Zscaler Service is enabled for the user:  (3068)']"
    NO_SERVICE_ERROR_OK = "//*[@class='android.widget.Button' and @resource-id='zscaler.com.zscaler:id/ok_button']"
    NEW_ZCC_ACTIVITY = 'com.zscaler.tray.activities.MainActivity'
    OLD_ZCC_ACTIVITY = 'com.zscaler.activities.MainActivity'
    ANDROID_PACKAGE = 'zscaler.com.zscaler'
    CHROMEOS_PACKAGE = 'zscaler.com.zschromeosapp'
    ONEID_PASSWORD = '//android.widget.EditText'
    ONEID_SIGNIN = "//*[@class='android.widget.Button' and @text='Sign In']"
    OKTA_UNABLE_SIGNIN = "//*[@class='android.widget.TextView' and @text='Unable to sign in']"
    ONEID_USERNAME = "//android.widget.EditText"
    ONEID_USERNAME_NEXT = '//android.widget.Button[@text="Next"]'

################################################################
#                                                              #
#                                                              #
# AUTHOR : SOURAV MAGOTRA                                      #
################################################################

import os
from common_lib import Constants
from OS_Android.library.ops_system import SysOps
from common_lib.common.logger import Logger

os_version=os.environ.get("operating_system")


class InstallUninstall:
    """
    Work Flow
    ----------
        1. There are 2 important Central Functions
            a. install_zcc()
            b. uninstall_zcc()
    """
    def __init__(self,log_handle=None,cloud=None):
        self.os_version = os_version
        self.const = Constants.Utils
        self.sys_ops=SysOps(cloud=cloud)
        self.logger = (log_handle if log_handle else Logger.initialize_logger("ops_installation.log", log_level="INFO"))
    
    def install_zcc(self):
        """
        This function installs the ZCC APK file on an Android device using ADB (Android Debug Bridge).
        Parameters:
            self (object): The instance of the class.
        Returns:
            tuple: A tuple containing a boolean status and a string message indicating the success or failure of the installation.
        """
        status = False
        # Get the path to the APK files
        apk_path = os.path.join(os.getcwd(), '..', 'OS_Android', 'resource')
        # Get a list of all APK files in the directory
        apk_files = [file for file in os.listdir(apk_path) if file.endswith(".apk")]
        # Sort the APK files in reverse order
        apk_files = sorted(apk_files, reverse=True)
        # Filter the list to only include the APK file that matches the zcc_version environment variable
        build_to_install = [file for file in apk_files if os.environ.get("zcc_version") in file]
        # Check if any matching APK files were found
        if not build_to_install:
            self.logger.error("No matching APK file found for zcc_version")
            return (status, "No matching APK file found")
        # Install the APK file using ADB
        shell_output = self.sys_ops.adb_commands(f"adb install {build_to_install[0]}")
        shell_output = shell_output[2].decode('utf-8').split()
        # Check if the installation was successful
        if any("Success" in string for string in shell_output):
            self.logger.info("ZCC installed successfully")
            status = True
        else:
            self.logger.error("ZCC installation failed")
        return (status, "Success" if status else "Failure",None)
    def uninstall_zcc(self):
        """
        Attempts to uninstall the ZCC application from an Android device.
        Output:
        Returns a tuple. The first element is a boolean indicating whether the uninstallation was successful. 
        The second element is a string with the status message.
        """
        # Initialize status as False
        status = False
        # Execute the ADB command to uninstall the Zscaler application
        shell_output = self.sys_ops.adb_commands("adb uninstall zscaler.com.zscaler")
        # Decode and split the shell output into a list of strings
        shell_output = shell_output[2].decode('utf-8').split()
        # Check if the "Success" message is present in any of the strings in the shell output list
        if any("Success" in string for string in shell_output):
            # Log a success message if the uninstallation was successful
            self.logger.info("ZCC uninstalled successfully")   
            # Set status to True if the uninstallation was successful
            status = True
        # Return a tuple with the status and a message indicating the result of the uninstallation attempt
        return (status, "Success" if status else "Failure",None)
    
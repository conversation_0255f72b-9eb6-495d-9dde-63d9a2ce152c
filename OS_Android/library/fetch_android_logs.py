import os
import sys
import inspect
import zipfile
import shutil
import imaplib, email
from common_lib.common.constants import Utils
from common_lib.common.logger import Logger

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir)
from common_lib.common.log_ops import *
import logging, subprocess

from OS_Android.library.ops_system import SysOps
from zipfile import ZipFile
import os
from common_lib.helper_function import help_return

operating_system = os.environ.get("operating_system").title()
sys_ops = SysOps()
operating_system  = sys_ops.android_device_type()
if operating_system == "Android":
    zcc_package_name = 'zscaler.com.zscaler'

else:
    zcc_package_name = 'zscaler.com.zschromeosapp'


class FetchAndroidLogs:
    """
    This class is used for fetching zcc logs from gmail.
    """

    def __init__(self, log_handle=None):

        self.logger = (
            log_handle if log_handle else Logger.initialize_logger("fetch_android_logs.log", log_level="INFO"))
        self.const_obj = constants.Utils()
        self.sys_ops = SysOps(self.logger)

    def delete_zcc_logs(self):
        cmd = "adb shell rm -f /sdcard/Download/ZCC*"
        out = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=subprocess.PIPE)

    def unzip_log_files(self, unzip_path):
        """
        This method is not unzip logs file inside log folder
        and delete zip files
        """
        home_path = os.getcwd()

        unzip_path = unzip_path

        files = os.listdir(unzip_path)

        for file in files:
            if ".zip" in file:
                file_to_unzip = file
                with ZipFile(rf"{unzip_path}\{file_to_unzip}") as f:
                    f.extractall(path=fr"{unzip_path}")

                os.remove(rf"{unzip_path}\{file_to_unzip}")

    def download_logs(self, by_default_unzip: bool):
        """
        This method is to fetch logs from android device
        and delete zip folder file after fetching
        """
        status = False

        currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
        parentdir = os.path.dirname(currentdir)
        sys.path.insert(0, parentdir)
        working_directory = os.path.join(parentdir, "Temp_Logs")

        if os.path.isdir(working_directory):

            shutil.rmtree(working_directory)

            self.logger.info("CREATING NEW FOLDER")
            os.mkdir(working_directory)

        else:
            os.mkdir(working_directory)
            self.logger.info("Temp Logs is created")

        cmd = "adb shell ls -t /sdcard/Download/"

        out = subprocess.run(cmd, capture_output=True, text=True)

        for file in out.stdout.split('\n'):
            if 'ZCC_' in file:
                file_to_extract = file
                break

        cmd = f"adb pull /sdcard/Download/{file_to_extract} {working_directory}"
        out = subprocess.run(cmd, capture_output=True, text=True)
        if " 1 file pulled" in out.stdout or " 1 file pulled" in out.stderr :
            self.logger.info("Copying of zip file is successful")
            status = True
            msg = "Copying of zip file is successful"

        else:
            self.logger.info("Copying of zip file is not successful")
            msg = "Copying of zip file is not successful"

        folder_to_unzip = os.path.join(working_directory, file_to_extract)

        with zipfile.ZipFile(folder_to_unzip, 'r') as zip_ref:
            zip_ref.extractall(self.const_obj.LOG_PATH)
        os.remove(folder_to_unzip)
        self.delete_zcc_logs()

        cmd = f'adb shell dumpsys package {zcc_package_name} | findStr versionName'
        output = self.sys_ops.adb_commands(cmd)

        version = output[0], output[1], (output[2].strip()).decode()
        version = version[2].split("=")
        zcc = version[1].split(".")
        zcc = zcc[0] + "." + zcc[1]
        zcc = float(zcc)

        unzip_path = os.path.join(os.getcwd(), "OS_Android/Temp_Logs/")
        if zcc >= 3.7 and by_default_unzip:
            self.unzip_log_files(unzip_path)

        return status, msg

    def read_export_log_mail(self, by_default_unzip: bool = True):
        """
        This method is get logs from android device to
        local machine using test app
        """

        status, msg = self.download_logs(by_default_unzip)
        return status, msg

    def appinfo_file_values(self,
                            value: str):
        """

        This method is to fetch value from APpinfo file and return the value

        """
        const_obj = constants.Utils()
        logger_obj = logger.Logger.initialize_logger(log_file_name="zcc_misc.log", log_level="DEBUG")
        log_ops = LogOps(log_handle=logger_obj)

        result = help_return(log_ops.search_log_file, file="AppInfo", words_to_find_in_line=[value],
                             return_log_line=True,
                             directory=const_obj.LOG_PATH)
        build_array = result[2][2].split(":", maxsplit=1)
        build_id = build_array[-1].strip()
        return build_id

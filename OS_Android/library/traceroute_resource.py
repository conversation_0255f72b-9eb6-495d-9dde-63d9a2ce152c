import pytest, sys, ast, re, os, subprocess, pathlib, allure, json
from OS_Android.library.database_parse import <PERSON><PERSON><PERSON><PERSON><PERSON>
from OS_Android.library.database_parse import <PERSON>g<PERSON>andler
from common_lib.common.log_ops import *
const_obj  = constants.Utils()


class traceroute_resource:
    def __init__(self):
        self.db_handler = DbHandler()
        self.log_handler = LogHandler()
        self.logger = logger.Logger.initialize_logger(log_file_name="zcc_misc.log", log_level="DEBUG")
        self.db_path = const_obj.LOG_PATH + f"\\upm_traceroute.db"
        self.zia_tunnel_types = ['2', '3', '14', '15', '22', '23']
        self.zpa_tunnel_types = ['4', '16', '24']
        self.direct_tunnel_types = ['21']

        self.column_rules = {
            'appid': {'allow_empty': False, 'data_type': 'float', 'min_value': 1, 'max_value': 2 ** 32 - 1},
            'monid': {'allow_empty': False, 'data_type': 'float', 'min_value': 1, 'max_value': 2 ** 32 - 1},
            'domain': {'allow_empty': False, 'data_type': 'string'},
            'hopstodest': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 50},
            'unresponsivehopcount': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 50},
            'totalloss': {'allow_empty': False, 'data_type': 'float', 'min_value': -1, 'max_value': 100},
            'totallatency': {'allow_empty': False, 'data_type': 'float', 'min_value': -1, 'max_value': 1000},
            'egress': {'allow_empty': False, 'data_type': 'string'},
            'protocol': {'allow_empty': False, 'data_type': 'string'},
            'mtunnelid': {'allow_empty': False, 'data_type': 'string',
                          'is_zpa_check': {'is_zpa_0': {'allow_empty': True}, 'is_zpa_1': {'allow_empty': False}}},
            'resolvedip': {
                'allow_empty': False, 'data_type': 'string',
                'is_zpa_check': {'is_zpa_0': {'allow_empty': False}, 'is_zpa_1': {'allow_empty': True}}
            },
            'brokerResponse': {
                'allow_empty': True, 'data_type': 'string',
                'is_zpa_check': {'is_zpa_0': {'allow_empty': True}, 'is_zpa_1': {'allow_empty': False}}
            },
            'SMEResponse': {
                'allow_empty': True, 'data_type': 'string',
                'is_zpa_check': {'is_zpa_0': {'allow_empty': False}, 'is_zpa_1': {'allow_empty': True}}
            }
        }

    def verify_traceroute_probes(self, service_type, is_lt):
        """
        Validates traceroute probes from log data for the given service type and probe mode (LT or Regular).

        It checks for expected probe lifecycle events such as SD response, Zen request (For ZIA Probes only), printStatus,
        and database saving. Logs containing 'error' or 'exception' are also flagged.

        Args:
            service_type (int): 1 = ZIA_1_0, 2 = ZIA_2_0, 3 = ZPA.
            is_lt (int): 1 for LT probes, 0 for Regular probes.

        Returns:
            Tuple[bool, str]: Validation result and summary message.
        """
        try:
            service_type_map = {1: "ZIA_1_0", 2: "ZIA_2_0", 3: "ZPA"}
            sd_type = service_type_map.get(service_type, "")
            self.logger.info(f"\n--- Traceroute: Verifying {sd_type} {'LT' if is_lt else 'Regular'} Probes ---\n")

            logs_map = {
                'start_tr_probe': "ZUpmTracerouteThread:: run - In",
                'regular_or_lt': "isLT",
                'service_type': "Service type",
                'zpa': "zpa",
                'sd_response': "SD response",
                'zen_request': "completed with status",
                'print_status': 'ZUpmTracerouteThread:: printStatus',
                'saving_data': "saveToDatabase",
                'run_out_probe': "ZUpmTracerouteThread:: run - Out",
            }

            src = self.log_handler.get_latest_file_data_with_plugin(path=const_obj.LOG_PATH, plugin= 'traceroute')
            issues = []
            active_threads = {}
            matching_probe_found = 0

            for line in src.split("\n"):
                line_l = line.lower()

                if logs_map['start_tr_probe'].lower() in line_l:
                    match = re.search(r"\[(.*?)\]", line)
                    thread_id = match.group(1) if match else None
                    if not thread_id:
                        continue
                    if (is_lt == 0 and f"{logs_map['regular_or_lt']}=0".lower() in line_l) or (
                            is_lt == 1 and f"{logs_map['regular_or_lt']}=1".lower() in line_l):
                        if thread_id not in active_threads:
                            active_threads[thread_id] = {'run_in': 1, 'flag': 0, 'sd_response_found': 0,
                                                         'check_sd_url': 0, 'check_sd_ip': 0, 'check_sd_port': 0,
                                                         'check_sd_sme_ip': 0, 'check_sd_sme_port': 0,
                                                         'check_zen_request': 0,
                                                         'zen_response_code': 0, 'zen_request_text': '',
                                                         'check_print_status': 0, 'save_data_in_db': 0,
                                                         'save_data_text': '', 'sd_text': '', 'print_status_text': '',
                                                         'starting_text': line, 'is_zpa': False,
                                                         'matched_sd_type': False
                                                         }

                for thread_id in list(active_threads.keys()):
                    if thread_id not in line:
                        continue

                    flags = active_threads[thread_id]

                    if logs_map['service_type'].lower() in line_l:
                        if logs_map['zpa'].lower() in line_l:
                            flags['is_zpa'] = True
                        else:
                            flags['is_zpa'] = False

                        # Match correct sd_type
                        if sd_type.lower() in line_l:
                            flags['matched_sd_type'] = True
                            matching_probe_found += 1
                        else:
                            del active_threads[thread_id]
                            continue

                    if not flags.get('matched_sd_type'):
                        continue

                    if logs_map['sd_response'].lower() in line_l:
                        flags['flag'] = 1
                        flags['sd_response_found'] = 1
                        flags['sd_text'] = line
                        try:
                            flags['check_sd_url'] = bool(re.search(r"Url:\s*(\S+)(?=\s*(Success|$))", line))
                            flags['check_sd_ip'] = bool(
                                re.search(r"Destination Ips:\s*(\S+)(?=\s*(Destination Ports|$))", line))
                            flags['check_sd_port'] = bool(
                                re.search(r"Destination Ports:\s*(\S+)(?=\s*(SmeIp|$))", line))
                            flags['check_sd_sme_ip'] = bool(re.search(r"SmeIp:\s*(\S+)(?=\s*(SmePort|$))", line))
                            flags['check_sd_sme_port'] = bool(
                                re.search(r"SmePort:\s*(\S+)(?=\s*(SystemProxy|$))", line))
                            flags['check_broker_name'] = bool(
                                re.search(r"BrokerName:\s*(\S+)(?=\s*(BrokerIp|$))", line))
                            flags['check_broker_ip'] = bool(re.search(r"BrokerIp:\s*(\S+)(?=\s*(BindIp|$))", line))
                        except Exception as e:
                            self.logger.error(f"[Thread ID: {thread_id}] Error parsing SD line: {line} - {e}")
                            continue

                    if ("exception" in line_l and "error" in line_l):
                        self.logger.error(f"{line}")
                        issues.append(line)

                    if logs_map['zen_request'].lower() in line_l:
                        flags['check_zen_request'] = 1
                        flags['zen_request_text'] = line
                        match = re.search(r'\bstatus\s*(\d+)', line, re.IGNORECASE)
                        flags['zen_response_code'] = str(match.group(1)) if match else ''

                    if logs_map['print_status'].lower() in line_l:
                        if "success" in line_l:
                            flags['check_print_status'] = 1
                        flags['print_status_text'] = line

                    if logs_map['saving_data'].lower() in line_l:
                        flags['save_data_in_db'] = 1
                        flags['save_data_text'] = line

                    if logs_map['run_out_probe'].lower() in line_l:
                        issue_prefix = f"[Thread ID: {thread_id}]"
                        self.logger.info(f"{flags['starting_text']}")
                        self.logger.info(f"{flags['sd_text']}")
                        self.logger.info(f"{issue_prefix} SD Response Found: {flags['sd_response_found']}")
                        if not flags['is_zpa']:
                            self.logger.info(f"{flags['zen_request_text']}")

                        self.logger.info(f"{flags['print_status_text']}")
                        self.logger.info(f"{line}")

                        if not flags['is_zpa']:
                            flags['check_sd_sme_ip'] = True
                            flags['check_sd_sme_port'] = True
                            flags['check_zen_request'] = True
                        else:
                            flags['check_broker_name'] = True
                            flags['check_broker_ip'] = True

                        checks = [
                            ('check_sd_url', f"Missing SD URL. log - {flags['sd_text']}"),
                            ('check_sd_ip', f"Missing SD Destination Ip. log - {flags['sd_text']}"),
                            ('check_sd_port', f"Missing SD Destination Port. log - {flags['sd_text']}"),
                            ('check_print_status',
                             f"The traceroute probe did not generate a 'Success' entry in the 'ZUpmTracerouteThread::printStatus' log line . Log : {flags['print_status_text']}"),
                            ('save_data_in_db', "Missing 'saveToDatabase - Saving response into the database'")
                        ]

                        if flags['is_zpa']:
                            checks.extend([
                                ('check_broker_name', f"Missing Broker Name. log - {flags['sd_text']}"),
                                ('check_broker_ip', f"Missing Broker IP. log - {flags['sd_text']}"),
                            ])
                        else:
                            checks.extend([
                                ('check_sd_sme_ip', f"Missing SD SME IP. log - {flags['sd_text']}"),
                                ('check_sd_sme_port', f"Missing SD SME Port. log - {flags['sd_text']}"),
                                ('check_zen_request', "Zen Request failure."),
                            ])


                        for item in checks:
                            flag_key = item[0]
                            msg = item[1]
                            condition = True
                            if len(item) == 3:
                                condition = item[2]
                            if not flags.get(flag_key, False) and condition:
                                self.logger.info(f"{issue_prefix} {msg} Probe: {flags['starting_text']}")
                                issues.append(f"{issue_prefix} {msg} Probe: {flags['starting_text']}")

                        if not flags['is_zpa']:
                            if flags['zen_response_code'] and str(flags['zen_response_code']).startswith('4') or str(
                                    flags['zen_response_code']).startswith('5') or str(
                                    flags['zen_response_code']).startswith('-'):
                                self.logger.error(
                                    f"{issue_prefix} Invalid ZEN response status: {flags['zen_response_code']}. Log: {flags['zen_request_text']}")
                                issues.append(
                                    f"{issue_prefix} Invalid ZEN response status: {flags['zen_response_code']}. Log: {flags['zen_request_text']}")

                        if not any(k for k in issues if k.startswith(issue_prefix)):
                            self.logger.info(f"{issue_prefix} Probe Successful.")
                        self.logger.info("-" * 120)
                        del active_threads[thread_id]

            if matching_probe_found == 0:
                self.logger.error(
                    f"No {sd_type} {'LT' if is_lt else 'Regular'} probes found in the ZSAUpm_Traceroute logs.\n")
                pytest.skip(
                    f"No {sd_type} {'LT' if is_lt else 'Regular'} probes found in the ZSAUpm_Traceroute logs.\n")

            if issues:
                return False, f"Validation failed for {sd_type} {'LT' if is_lt else 'Regular'} probes:\n" + "\n\n\n".join(
                    issues)

            msg = f"All {sd_type} {'LT' if is_lt else 'Regular'} probes validated successfully."
            self.logger.info(msg)
            return True, msg

        except Exception as e:
            self.logger.error(f"Unexpected error in verify_traceroute_probes: {e}")
            return False, f"Exception occurred during probe verification: {e}"

    def verify_and_validate_traceroute_data(self, columns, islt, domain):
        """
        Validates traceroute data from the database for specified columns.

        Args:
            columns (List[str]): Columns to validate.
            islt (int): 1 for LT probes, 0 for regular.

        Returns:
            Tuple[bool, str]: Validation result and summary.
        """
        try:
            self.db_handler.create_db_connection(self.db_path)
            actual_table_name = "trltmain" if islt == 1 else "trmain"
            self.logger.info(f"Verifying data from {actual_table_name}...")
            rows_data = self.db_handler.execute_query(f"SELECT * FROM {actual_table_name} WHERE domain='{domain}'")
            self.db_handler.close_db_connection()

            if not rows_data:
                pytest.skip(f"'{actual_table_name}' is empty.")

            all_issues = []
            try:
                schema_query = f"PRAGMA table_info({actual_table_name});"
                self.db_handler.create_db_connection(self.db_path)
                schema = self.db_handler.execute_query(schema_query)
                self.db_handler.close_db_connection()
                db_columns = [col['name'] for col in schema]
            except Exception as e:
                self.logger.error(f"Error fetching table schema for '{actual_table_name}': {e}")
                return False, f"Error fetching table schema for '{actual_table_name}'."

            invalid_columns = [col for col in columns if col not in db_columns]
            if invalid_columns:
                invalid_columns_str = ", ".join(invalid_columns)
                self.logger.warn(
                    f"Columns {invalid_columns_str} do not exist in the table '{actual_table_name}'. Skipping these columns.")
                columns = [col for col in columns if col in db_columns]

            for row_idx, row_data in enumerate(rows_data, start=1):
                row_issues = []
                self.logger.info(f"{'#' * 20} Verifying row {row_idx}, table: '{actual_table_name}' {'#' * 20}\n")
                is_zpa, tunnel_type = '', ''
                try:
                    tr_json = row_data['json']
                    tr_json_parsed = json.loads(tr_json)
                    is_zpa = tr_json_parsed['is_zpa']
                    if 'legs' in tr_json_parsed:
                        for leg in tr_json_parsed['legs']:
                            leg = dict(leg)
                            if leg.get('src', '').lower() == "client":
                                tunnel_type = leg['tunnel_type'] if 'tunnel_type' in leg else ''
                                if is_zpa == 0 and not tunnel_type:
                                    msg = f"'tunnel_type' not present in leg (row {row_idx})"
                                    self.logger.error(msg)
                                    row_issues.append(msg)
                                break
                    elif is_zpa == 0:
                        msg = f"No 'legs' field in traceroute JSON (row {row_idx}). Skipping tunnel_type extraction"
                        self.logger.info(msg)
                        row_issues.append(msg)
                        continue


                except (json.JSONDecodeError, TypeError, KeyError) as e:
                    self.logger.error(f"Error parsing 'is_zpa' from JSON: {e}")

                for col in columns:
                    if col not in self.column_rules:
                        self.logger.info(f"Skipping column '{col}' as it is not defined in column rules.")
                        continue

                    rules = self.column_rules[col]
                    value_raw = row_data[col]
                    values_to_check = []

                    # Handle different types including list, stringified list, or scalar
                    if isinstance(value_raw, list):
                        values_to_check = value_raw
                    elif isinstance(value_raw, str) and value_raw.startswith('[') and value_raw.endswith(']'):
                        try:
                            parsed = ast.literal_eval(value_raw)
                            values_to_check = parsed if isinstance(parsed, list) else [parsed]
                        except Exception as e:
                            msg = f"Failed to parse list from string '{value_raw}' in column '{col}': {e}"
                            self.logger.error(msg)
                            row_issues.append(msg)
                            continue
                    else:
                        values_to_check = [value_raw]

                    if 'is_zpa_check' in rules:
                        zpa_check = rules['is_zpa_check'].get(f'is_zpa_{is_zpa}')
                        if zpa_check:
                            for key, val in zpa_check.items():
                                rules[key] = val

                    # Validate each value
                    for val in values_to_check:
                        val_str = str(val).strip()
                        self.logger.info(f"Verifying column '{col}': {val_str}")

                        # Special-case handling for SMEResponse based on tunnel_type
                        if col in ['SMEResponse', 'brokerResponse']:
                            if is_zpa == 0 and col == 'SMEResponse' and str(tunnel_type) not in self.zia_tunnel_types:
                                self.logger.info(
                                    f"Skipping SMEResponse validation for non-ZIA tunnel_type '{tunnel_type}'.")
                                continue

                            # Check for error string
                            if "error" in val_str.lower():
                                msg = f"Error found ({val}) in column '{col}' at row {row_idx}, table: '{actual_table_name}'."
                                self.logger.error(msg)
                                row_issues.append(msg)

                        if not val_str and not rules.get('allow_empty', True):
                            msg = f"Empty value found at '{col}' in row {row_idx}, table: '{actual_table_name}'. It should not be empty."
                            self.logger.error(msg)
                            row_issues.append(msg)
                            continue

                        if rules['data_type'] in ['float', 'int']:
                            try:
                                value_float = float(val_str)
                            except ValueError:
                                msg = f"Invalid value '{val_str}' for column '{col}' in row {row_idx}, table: '{actual_table_name}'. It should be a number."
                                self.logger.error(msg)
                                row_issues.append(msg)
                                continue

                            if 'min_value' in rules and value_float < rules['min_value']:
                                msg = f"Invalid value '{val_str}' for column '{col}' in row {row_idx}, table: '{actual_table_name}'. It must be >= {rules['min_value']}."
                                self.logger.error(msg)
                                row_issues.append(msg)
                            if 'max_value' in rules and value_float > rules['max_value']:
                                msg = f"Invalid value '{val_str}' for column '{col}' in row {row_idx}, table: '{actual_table_name}'. It must be <= {rules['max_value']}."
                                self.logger.error(msg)
                                row_issues.append(msg)

                        elif rules['data_type'] == 'string':
                            if not isinstance(val, str):
                                msg = f"Invalid value '{val}' for column '{col}' in row {row_idx}, table: '{actual_table_name}'. It should be a string."
                                self.logger.error(msg)
                                row_issues.append(msg)

                if not row_issues:
                    self.logger.info(
                        f"Row {row_idx} successfully verified with no issues, table: '{actual_table_name}'.")

                if row_issues:
                    all_issues.extend(row_issues)

            if all_issues:
                return False, "\n\n\n".join(all_issues)
            else:
                self.logger.info(f"Successfully verified all rows in table '{actual_table_name}'. No issues found.")
                return True, "SUCCESS"

        except Exception as e:
            self.logger.error(f"Unexpected error : {e}")
            return False, f"Exception occurred : {e}"
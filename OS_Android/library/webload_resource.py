import pytest, sys, ast, re, os, subprocess, pathlib, allure, json
from OS_Android.library.database_parse import <PERSON><PERSON><PERSON><PERSON><PERSON>
from OS_Android.library.database_parse import <PERSON>g<PERSON>andler
from common_lib.common.log_ops import *
const_obj  = constants.Utils()



class webload_resource:
    def __init__(self):
        self.db_handler = Db<PERSON>andler()
        self.log_handler = LogHandler()
        self.logger = logger.Logger.initialize_logger(log_file_name="zcc_misc.log", log_level="DEBUG")
        config_path = os.path.join(os.getcwd(), "config", os.environ.get("CONFIG_FILE"))
        with open(os.path.join(os.getcwd(), config_path)) as json_file:
            self.variables = json.load(json_file)

        self.db_path = const_obj.LOG_PATH + f"\\upm_webload.db"
        self.zia_tunnel_types = ['2', '3', '14', '15', '22', '23']
        self.zpa_tunnel_types = ['4', '16', '24']
        self.direct_tunnel_types = ['21']
        self.column_rules = {
            'guid': {'allow_empty': False, 'data_type': 'string'},
            'LTSessionId': {'allow_empty': False, 'data_type': 'float'},
            'dns_tm': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 90000},
            'Availability': {'allow_empty': False, 'data_type': 'float', 'min_value': 100, 'max_value': 90000},
            'DNSTime_ns': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': ************},
            'ttfb_tm': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 90000},
            'ttlb_tm': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 90000},
            'HTTPLatency_ns': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 90000000000},
            'total_tm': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 90000},
            'tcp_tm': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 90000},
            'PageFetchTime_ns': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 90000000000},
            'http_connect_tm': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 90000,
                                'zia_tunnel_check': True,  # ZIA probes should have values > 0
                                'zpa_tunnel_check': 0,  # ZPA probes should only have value 0
                                },
            'mtunnel_tm': {
                'allow_empty': False,
                'data_type': 'float',
                'min_value': 0,
                'max_value': 10000,
                'zia_tunnel_check': {
                    'mtunnelid_empty': True,  # For ZIA probes, mtunnelid should be empty
                    'mtunnel_tm': 0,  # For ZIA probes, mtunnel_tm should be 0
                },
                'zpa_tunnel_check': {
                    'mtunnelid_empty': False,  # For ZPA probes, mtunnelid should not be empty
                    'mtunnel_tm_min': 0,  # For ZPA probes, mtunnel_tm should be > 0
                    'mtunnel_tm_max': 10000,  # Custom threshold for ZPA probes
                },
            },
            'tot_payload_bytes': {
                'allow_empty': False,
                'data_type': 'float',
                'min_value': 0,
                'max_value': 2 ** 32 - 1
            },
            'statsV2': {
                'allow_empty': False,
                'data_type': 'dict'
            }
        }

    def collect_json_from_log(self, plugin_type: str = 'web'):
        """
        Parse and extract valid JSON objects from plugin-specific log data.

        Args:
            plugin_type (str): Type of plugin to filter logs. Defaults to 'web'.

        Returns:
            Tuple[bool, Union[str, List[dict]]]: Success flag and either JSON list or error message.
        """
        latest_webload_file_data , temp , json_list , adapter_name , total_e_links , tot_payload_bytes , bracket_count = '' , '' , [], False, False, False, 0
        try:
            latest_webload_file_data = self.log_handler.get_latest_file_data_with_plugin(path=const_obj.LOG_PATH, plugin= plugin_type)
        except Exception as e:
            return False, f"Failed to retrieve log data: {str(e)}"

        try:
            for item in latest_webload_file_data.split("\n"):
                if "adapter_name" in item and "DBG" not in item:
                    temp = '{'
                    adapter_name = True

                if adapter_name and "total_e_links" in item:
                    total_e_links = True

                if adapter_name and "tot_payload_bytes" in item:
                    tot_payload_bytes = True

                if adapter_name and "DBG" not in item:
                    temp += item

                if "Uploader string for Web-monitors module" in item:
                    temp = ""
                    adapter_name = total_e_links = tot_payload_bytes = False

                if "}" in item and total_e_links:
                    bracket_count += 1

                if bracket_count == 1:
                    adapter_name = False
                    bracket_count = 0
                    total_e_links = False
                    tot_payload_bytes = False
                    temp = temp.replace(" ", "")
                    temp += '}'

                    if len(temp) >= 1:
                        try:
                            json_obj = json.loads(temp)
                        except json.JSONDecodeError:
                            try:
                                temp = re.sub(r'([{,]\s*?"[^"]+"\s*:)\s*,', r'\1 null,', temp)
                                json_obj = json.loads(temp)
                            except json.JSONDecodeError:
                                continue
                        json_list.append(json_obj)
                    temp = ""

        except Exception as e:
            return False, f"Error while parsing log data: {str(e)}"

        if json_list:
            return True, json_list
        else:
            return False, "No valid JSON data found in the log."

    def verify_and_validate_webload_data(self, columns, islt, probe_url):
        """
        Validates specified columns in a database table against predefined rules.

        Parameters:
        - table_name (str): Base name of the table (prefix handled internally).
        - columns (list): List of columns to validate.
        - islt (int): Flag to determine if table is long-term; adds 'lt_session_id' if set.

        Returns:
        - (bool, str): True with "SUCCESS" if all validations pass, else False with issues listed.
        """
        try:
            self.db_handler.create_db_connection(self.db_path)
            actual_table_name = "WebDataLT" if islt == 1 else "WebData"
            columns.append('LTSessionId') if islt == 1 else None

            self.logger.info(f"Verifying data from {actual_table_name}...")
            rows_data = self.db_handler.execute_query(f"SELECT * FROM {actual_table_name} WHERE URL='{probe_url}'")
            self.db_handler.close_db_connection()

            if not rows_data:
                pytest.skip(f"'{actual_table_name}' is empty.")

            all_issues = []

            try:
                schema_query = f"PRAGMA table_info({actual_table_name});"
                self.db_handler.create_db_connection(self.db_path)
                schema = self.db_handler.execute_query(schema_query)
                self.db_handler.close_db_connection()
                db_columns = [col['name'] for col in schema]
            except Exception as e:
                self.logger.error(f"Error fetching table schema for '{actual_table_name}': {e}")
                return False, f"Error fetching table schema for '{actual_table_name}'."

            invalid_columns = [col for col in columns if col not in db_columns]
            if invalid_columns:
                invalid_columns_str = ", ".join(invalid_columns)
                self.logger.warn(
                    f"Columns {invalid_columns_str} do not exist in the table '{actual_table_name}'. Skipping these columns.")
                columns = [col for col in columns if col in db_columns]

            for row_idx, row_data in enumerate(rows_data, start=1):
                row_issues = []
                self.logger.info(f"{'#' * 20} Verifying row {row_idx}, table: '{actual_table_name}' {'#' * 20}\n\n")

                for col in columns:
                    if col not in self.column_rules:
                        self.logger.info(f"Skipping column '{col}' as it is not defined in column rules.")
                        continue

                    rules = self.column_rules[col]
                    value_raw = row_data[col]
                    values_to_check = []

                    # Handle different types including list, stringified list, or scalar
                    if isinstance(value_raw, list):
                        values_to_check = value_raw
                    elif isinstance(value_raw, str) and value_raw.startswith('[') and value_raw.endswith(']'):
                        try:
                            parsed = ast.literal_eval(value_raw)
                            values_to_check = parsed if isinstance(parsed, list) else [parsed]
                        except Exception as e:
                            msg = f"Failed to parse list from string '{value_raw}' in column '{col}': {e}"
                            self.logger.error(msg)
                            row_issues.append(msg)
                            continue
                    else:
                        values_to_check = [value_raw]

                    # Validate each value
                    for val in values_to_check:
                        val_str = str(val).strip()
                        self.logger.info(f"Verifying column '{col}': {val_str}")

                        if not val_str and not rules.get('allow_empty', True):
                            msg = f"Empty value found at '{col}' in row {row_idx}, table: '{actual_table_name}'. It should not be empty."
                            self.logger.error(msg)
                            row_issues.append(msg)
                            continue

                        if rules['data_type'] in ['float', 'int']:
                            try:
                                value_float = float(val_str)
                            except ValueError:
                                msg = f"Invalid value '{val_str}' for column '{col}' in row {row_idx}, table: {actual_table_name}. It should be a number."
                                self.logger.error(msg)
                                row_issues.append(msg)
                                continue

                            if 'min_value' in rules and value_float < rules['min_value']:
                                msg = f"Invalid value '{val_str}' for column '{col}' in row {row_idx}, table: {actual_table_name}. It must be >= {rules['min_value']}."
                                self.logger.error(msg)
                                row_issues.append(msg)
                            if 'max_value' in rules and value_float > rules['max_value']:
                                msg = f"Invalid value '{val_str}' for column '{col}' in row {row_idx}, table: {actual_table_name}. It must be <= {rules['max_value']}."
                                self.logger.error(msg)
                                row_issues.append(msg)

                if not row_issues:
                    self.logger.info(f"Row {row_idx} successfully verified with no issues, table: {actual_table_name}.")

                if row_issues:
                    all_issues.extend(row_issues)

            if all_issues:
                return False, "\n\n\n".join(all_issues)
            else:
                self.logger.info(f"Successfully verified all rows in table {actual_table_name}. No issues found.")
                return True, "SUCCESS"

        except Exception as e:
            self.logger.error(f"Unexpected error : {e}")
            return False, f"Exception occurred : {e}"

    def verify_http_connect_tm(self, islt,probe_url):
        """
        Validates 'http_connect_tm' values for ZIA and ZPA probes in the database.

        Args:
            islt (int): Session type flag; 0 for regular probes, 1 for LT sessions.

        Returns:
            tuple: A tuple (bool, str), where:
                - bool: True if all validations pass, False otherwise.
                - str: "SUCCESS" if validations pass, or a string listing any validation issues.
        """

        try:
            actual_table_name = "WebDataLT" if islt == 1 else "WebData"
            self.db_handler.create_db_connection(self.db_path)
            rows = self.db_handler.execute_query(f"SELECT * FROM {actual_table_name} WHERE URL='{probe_url}'")
            self.db_handler.close_db_connection()

            if not rows:
                pytest.skip(f"'{actual_table_name}' is empty.")

            issues = []
            for current_row, item in enumerate(rows, start=1):
                http_connect_tm_values = item['http_connect_tm'][1:-1].split(',')
                json_data = item['SDResponse'][1:-1]
                tunnel_type = json_data.partition('"tun_type":')[2][1:-1]

                for e in http_connect_tm_values:
                    e = str(e).strip()
                    if not e:
                        issues.append(f"Row {current_row}: 'http_connect_tm' is empty.")
                        continue
                    try:
                        e_value = float(e)
                    except ValueError:
                        issues.append(f"Row {current_row}: Invalid 'http_connect_tm' value '{e}' - not a number.")
                        continue

                    # ZIA probe validation
                    if tunnel_type in self.zia_tunnel_types:
                        max_value = self.column_rules.get('http_connect_tm', {}).get('max_value',
                                                                                     500)  # Default to 500 if not set
                        if e_value <= 0:
                            issues.append(f"Row {current_row}: 'http_connect_tm' for ZIA probe should be > 0.")
                        elif e_value >= max_value:
                            self.logger.warn(f"Row {current_row}: 'http_connect_tm' for ZIA probe is high ({e_value}).")
                            issues.append(f"Row {current_row}: 'http_connect_tm' for ZIA probe is high ({e_value}).")

                    # ZPA probe validation
                    if tunnel_type in self.zpa_tunnel_types and e_value != 0:
                        issues.append(
                            f"Row {current_row}: 'http_connect_tm' for ZPA probe should be 0, found {e_value}.")

            if issues:
                return False, "\n\n\n".join(issues)
            self.logger.info("Successfully verified 'http_connect_tm' for all rows.")
            return True, "SUCCESS"

        except Exception as e:
            self.logger.error(f"Error in 'verify_http_connect_tm_from_db': {e}")
            return False, f"Error occurred: {e}"

    def verify_mtunnel(self, islt,probe_url):
        """
        Verifies 'mtunnel_tm' and 'mtunnelid' values for ZIA and ZPA probes in WebData or WebDataLT.

        ZIA:
            - mtunnel_tm must be 0
            - mtunnelid must be empty

        ZPA:
            - mtunnel_tm must be > 0 and <= max threshold
            - mtunnelid must be alphanumeric and non-empty
        """
        try:
            self.logger.info(
                f"\n\n------------------------------ WEBLOAD: Verify MTunnel Fields (isLt={islt}) ------------------------------\n\n")

            self.db_handler.create_db_connection(self.db_path)
            table = "WebDataLT" if islt == 1 else "WebData"
            rows = self.db_handler.execute_query(f"SELECT * FROM {table} WHERE URL='{probe_url}'")
            self.db_handler.close_db_connection()
            issues = []
            rule = self.column_rules.get("mtunnel_tm", {})

            for idx, row in enumerate(rows, start=1):
                row = dict(row)
                url = row.get("URL", "")
                mtunnel_tm_raw = row.get("mtunnel_tm", "")
                mtunnelid_raw = row.get("MTunnelId", "")

                mtunnel_tm = [float(val.strip()) for val in mtunnel_tm_raw[1:-1].split(",") if val.strip()]
                mtid_list = [m.strip() for m in mtunnelid_raw.split(",") if m.strip()]
                tunnel_type = ''
                try:
                    sd_response = row.get("SDResponse", "")
                    sd_data = json.loads(sd_response)
                    tunnel_type = str(sd_data.get("tun_type", ""))
                except (json.JSONDecodeError, KeyError):
                    tunnel_type = ""

                if not mtunnel_tm:
                    msg = f"Row {idx}: Missing mtunnel_tm for URL {url}"
                    self.logger.warn(msg)
                    issues.append(msg)
                    continue

                for val in mtunnel_tm:
                    if val < 0:
                        msg = f"Row {idx}: Invalid mtunnel_tm={val} for {url}, expected >= 0"
                        self.logger.warn(msg)
                        issues.append(msg)
                        continue

                if tunnel_type in self.zia_tunnel_types:
                    if mtid_list:
                        msg = f"Row {idx}: ZIA ({url}) has non-empty mtunnelid: {mtid_list}"
                        self.logger.warn(msg)
                        issues.append(msg)
                    if any(mt != 0 for mt in mtunnel_tm):
                        msg = f"Row {idx}: ZIA ({url}) has non-zero mtunnel_tm values: {mtunnel_tm}. Expected 0."
                        self.logger.warn(msg)
                        issues.append(msg)

                elif tunnel_type in self.zpa_tunnel_types:
                    if not mtid_list:
                        msg = f"Row {idx}: ZPA ({url}) has empty mtunnelid, but should have non-empty values."
                        self.logger.warn(msg)
                        issues.append(msg)

                    if not all(re.match(r'^[a-zA-Z0-9+/=._-]+$', mtid) for mtid in mtid_list):
                        msg = f"Row {idx}: ZPA ({url}) has invalid mtunnelid: {mtid_list}, must be alphanumeric."
                        self.logger.warn(msg)
                        issues.append(msg)

                    if any(mt <= 0 for mt in mtunnel_tm):
                        msg = f"Row {idx}: ZPA ({url}) has invalid mtunnel_tm={mtunnel_tm}, expected > 0"
                        self.logger.warn(msg)
                        issues.append(msg)
                    if any(mt > rule["zpa_tunnel_check"]["mtunnel_tm_max"] for mt in mtunnel_tm):
                        msg = f"Row {idx}: ZPA ({url}) unusually high mtunnel_tm={mtunnel_tm}, expected < {rule['zpa_tunnel_check']['mtunnel_tm_max']}"
                        self.logger.warn(msg)
                        issues.append(msg)
                if not issues:
                    self.logger.info(
                        f"Row {idx}: Valid MTunnel: mtunnelid={mtid_list}, mtunnel_tm={mtunnel_tm} for {url}")
            if issues:
                return (False, "\n\n\n".join(issues))
            else:
                return (True, "All MTunnel checks passed.")

        except Exception as e:
            self.logger.error(f"Unexpected error : {e}")
            return False, f"Exception occurred : {e}"

    def verify_ssl_tm_and_smeid(self, islt,probe_url):
        """
        Validates 'ssl_tm' and 'SMEID' values from WebData/WebDataLT based on protocol and tunnel type.

        Rules:
        - HTTP URLs must have ssl_tm = 0
        - HTTPS URLs must have at least one ssl_tm > 0
        - ZIA tunnel types require SMEID > 0
        - ZPA tunnel types require SMEID = 0
        """
        try:
            self.logger.info(f"\n\n-------- WEBLOAD: Verifying 'ssl_tm' and 'SMEID' values (isLt={islt}) --------\n\n")

            table = "WebDataLT" if islt else "WebData"
            self.db_handler.create_db_connection(self.db_path)
            rows = self.db_handler.execute_query(f"SELECT * FROM {table} WHERE URL='{probe_url}'")
            self.db_handler.close_db_connection()

            issues = []
            success_log = []

            for idx, row in enumerate(rows, start=1):
                row = dict(row)
                url = row.get("URL", "").strip()
                ssl_tm = row.get("ssl_tm", "").strip()
                smeid = str(row.get("SMEID", "")).strip()
                sd_response = row.get("SDResponse", "")

                protocol = url.partition(':')[0].lower()

                try:
                    tunnel_type = str(json.loads(sd_response).get("tun_type", "")).strip()
                except Exception:
                    self.logger.warn(f"Row {idx}: Failed to parse tunnel type from SDResponse.")
                    tunnel_type = ""

                # Parse ssl_tm
                try:
                    ssl_tm_values = [float(val.strip()) for val in ssl_tm.strip("[]").split(",") if val.strip()]
                except Exception:
                    error_message = f"Row {idx}: Could not parse ssl_tm='{ssl_tm}'"
                    self.logger.error(error_message)
                    issues.append(error_message)
                    continue

                ssl_tm_passed = False
                smeid_passed = False

                # SSL_TM validation
                if protocol == "http":
                    if all(val == 0 for val in ssl_tm_values):
                        ssl_tm_passed = True
                    else:
                        error_message = f"Row {idx}: HTTP URL but ssl_tm > 0: {ssl_tm_values}"
                        self.logger.error(error_message)
                        issues.append(error_message)
                elif protocol == "https":
                    if any(val > 0 for val in ssl_tm_values):
                        ssl_tm_passed = True
                    else:
                        error_message = f"Row {idx}: HTTPS URL but no ssl_tm > 0 found: {ssl_tm_values}"
                        self.logger.error(error_message)
                        issues.append(error_message)
                else:
                    error_message = f"Row {idx}: Unknown protocol '{protocol}' in URL: {url}"
                    self.logger.error(error_message)
                    issues.append(error_message)

                # SMEID validation
                if tunnel_type in self.zia_tunnel_types:
                    if smeid.isdigit() and int(smeid) > 0:
                        smeid_passed = True
                    else:
                        error_message = f"Row {idx}: ZIA ({url}) has invalid SMEID: {smeid} (expected > 0)"
                        self.logger.error(error_message)
                        issues.append(error_message)
                elif tunnel_type in self.zpa_tunnel_types:
                    if smeid.isdigit() and int(smeid) == 0:
                        smeid_passed = True
                    else:
                        error_message = f"Row {idx}: ZPA ({url}) has invalid SMEID: {smeid} (expected 0)"
                        self.logger.error(error_message)
                        issues.append(error_message)

                if ssl_tm_passed and smeid_passed:
                    msg = f"Row {idx}: PASS — Protocol={protocol.upper()}, ssl_tm={ssl_tm_values} , TunnelType={tunnel_type}, SMEID={smeid}"
                    self.logger.info(msg)

            if issues:
                return False, "\n\n\n".join(issues)
            else:
                return True, f"SUCCESS :: All 'ssl_tm' and 'SMEID' checks passed for {table}"

        except Exception as e:
            self.logger.error("Unexpected error occurred during SSL_TM and SMEID verification.")
            return False, f"Internal error during verification: {str(e)}"

    def verify_web_probe_success(self, columns, islt,probe_url):
        """
        Checks if web probe entries are successful by validating the specified status code columns.

        Args:
            columns (list): List of status code-related columns to validate (e.g., ['StatusCode', 'http_status_code']).
            islt (int): If 1, use 'WebDataLT' table; else, use 'WebData'.

        Returns:
            tuple: (True, "All probes successful.") if all pass,
                (False, "<list of issues>") if any failures are found.
        """
        try:
            table_name = "WebDataLT" if islt == 1 else "WebData"
            self.logger.info(f"Checking Web Probe success from table '{table_name}' on columns {columns}...")

            query = f"SELECT * FROM {table_name} WHERE URL='{probe_url}'"
            self.db_handler.create_db_connection(self.db_path)
            rows_data = self.db_handler.execute_query(query)
            self.db_handler.close_db_connection()

            if not rows_data:
                self.logger.warn(f"Table '{table_name}' is empty. Skipping the test.")
                pytest.skip(f"'{table_name}' is empty. Skipping Web Probe success test.")
            issues = []

            for row_idx, row in enumerate(rows_data, start=1):
                availability = int(row['Availability']) if 'Availability' in row else 1
                ecode = int(row['ecode']) if 'ecode' in row else 0
                row_issues = []

                for col in columns:
                    col = col.strip()
                    if col not in dict(row):
                        self.logger.warn(f"Column '{col}' not found in row {row_idx}. Skipping.")
                        continue
                    col_value_raw = row[col]
                    values_to_check = []

                    if isinstance(col_value_raw, list):
                        values_to_check = col_value_raw
                    elif isinstance(col_value_raw, str) and col_value_raw.startswith('[') and col_value_raw.endswith(
                            ']'):
                        try:
                            parsed = ast.literal_eval(col_value_raw)
                            values_to_check = parsed if isinstance(parsed, list) else [parsed]
                        except Exception as e:
                            msg = f"Failed to parse list from string '{col_value_raw}' in column '{col}' in row {row_idx}: {e}"
                            self.logger.error(msg)
                            row_issues.append(msg)
                            continue
                    else:
                        values_to_check = [col_value_raw]

                    for val in values_to_check:
                        try:
                            val_float = float(val)
                        except Exception as e:
                            msg = f"Row {row_idx}: Invalid value '{val}' in column '{col}' - {e}"
                            self.logger.warn(msg)
                            row_issues.append(msg)
                            continue

                        if availability == 0 and val_float >= 400 and ecode != 0:
                            issue = (f"Row {row_idx}: Probe failed due to '{col}' >= 400 "
                                     f"(value={val_float}), Availability={availability}, Ecode={ecode}")
                            self.logger.warn(issue)
                            row_issues.append(issue)

                issues.extend(row_issues)

            if issues:
                return False, "\n\n\n".join(issues)
            else:
                self.logger.info(f"All probes in table '{table_name}' passed successfully.")
                return True, "All probes successful."

        except Exception as e:
            self.logger.error(f"Unexpected error : {e}")
            return False, f"Exception occurred : {e}"

    def verify_web_probes(self, service_type, is_lt):
        """
        Validates ZIA/ZPA web probes from the ZSAUpm_Webload log based on service type and probe type (LT or Regular).

        This method parses the latest log file related to the Webload plugin and identifies probe threads.
        It tracks the lifecycle of each probe and validates the presence of required log components such as
        Service Discovery (SD) responses, CONNECT requests/responses, headers, and X-UPM information.

        Args:
            service_type (int):
                The probe service type:
                  - 1: ZIA 1.0
                  - 2: ZIA 2.0
                  - 3: ZPA
            is_lt (int):
                Flag indicating whether the probe is LT:
                  - 1: LT probe
                  - 0: Regular probe

        Returns:
            tuple:
                - bool: True if all probes passed validation; False otherwise.
                - str: Success message or detailed list of validation failures.
        """
        service_type_map = {
            1: "ZIA_1_0",
            2: "ZIA_2_0",
            3: "ZPA",
        }
        sd_type = service_type_map.get(service_type, "")
        self.logger.info(f"\n\n--- Webload: Verifying {sd_type} {'LT' if is_lt else 'Regular'} Probes ---\n\n")

        logs_map = {
            'starting_monitor': "Starting monitor",
            'sessionid': "sessionid: -1",
            'service_type': "Service type",
            'zpa': "zpa",
            'sd_response': "Service Discovery",
            'connect_request': "Connect request:",
            'signxupmheader': "signXUPMHeader: Msg to sign",
            'resolved_ip': "resolved_ip",
            'x_upm_info': "X-UPM-INFO",
            'connect_response_code': "CONNECT Response Code",
            'response_status': "Response Status:",
            'saving_data': "Saving data for host:",
            'monitor_done': "Monitor done:",
        }
        try:
            src = self.log_handler.get_latest_file_data_with_plugin(path=const_obj.LOG_PATH, plugin='web')
            issues = []
            active_threads = {}
            matching_probe_found = 0

            for line in src.split("\n"):
                line_l = line.lower()

                if logs_map['starting_monitor'].lower() in line_l:
                    match = re.search(r"\[(.*?)\]", line)
                    thread_id = match.group(1) if match else None
                    if not thread_id:
                        continue
                    if (is_lt == 0 and logs_map['sessionid'].lower() in line_l) or (
                            is_lt == 1 and logs_map['sessionid'].lower() not in line_l):
                        if thread_id not in active_threads:
                            active_threads[thread_id] = {
                                'run_in': 1,
                                'flag': 0,
                                'sd_response_found': 0,
                                'check_sd_url': 0,
                                'check_sd_ip': 0,
                                'check_sd_port': 0,
                                'check_sd_sme_ip': 0,
                                'check_sd_sme_port': 0,
                                'check_broker_name': 0,
                                'check_broker_ip': 0,
                                'send_connect_request': 0,
                                'check_response_code': 0,
                                'check_response_status': 0,
                                'save_data_for_host': 0,
                                'x_upm_header_present': 0,
                                'x_upm_info_present': 0,
                                'connect_response_code': '',
                                'response_status': '',
                                'sd_text': '',
                                'starting_text': line,
                                'is_zpa': False,
                                'matched_sd_type': False
                            }

                for thread_id in list(active_threads.keys()):
                    if thread_id not in line:
                        continue

                    flags = active_threads[thread_id]

                    if logs_map['service_type'].lower() in line_l:
                        if logs_map['zpa'].lower() in line_l:
                            flags['is_zpa'] = True
                        else:
                            flags['is_zpa'] = False

                        # Match correct sd_type
                        if sd_type.lower() in line_l:
                            flags['matched_sd_type'] = True
                            matching_probe_found += 1
                        else:
                            del active_threads[thread_id]
                            continue

                    if not flags.get('matched_sd_type'):
                        continue

                    if logs_map['sd_response'].lower() in line_l:
                        flags['flag'] = 1
                        flags['sd_response_found'] = 1
                        flags['sd_text'] = line
                        try:
                            test = bool(re.search(r"Url:\s*(\S+)(?=\s*(Success|$))", line))
                            flags['check_sd_url'] = bool(re.search(r"Url:\s*(\S+)(?=\s*(Success|$))", line))
                            flags['check_sd_ip'] = bool(
                                re.search(r"Destination Ips:\s*(\S+)(?=\s*(Destination Ports|$))", line))
                            flags['check_sd_port'] = bool(
                                re.search(r"Destination Ports:\s*(\S+)(?=\s*(SmeIp|$))", line))
                            flags['check_sd_sme_ip'] = bool(re.search(r"SmeIp:\s*(\S+)(?=\s*(SmePort|$))", line))
                            flags['check_sd_sme_port'] = bool(
                                re.search(r"SmePort:\s*(\S+)(?=\s*(SystemProxy|$))", line))
                            flags['check_broker_name'] = bool(
                                re.search(r"BrokerName:\s*(\S+)(?=\s*(BrokerIp|$))", line))
                            flags['check_broker_ip'] = bool(re.search(r"BrokerIp:\s*(\S+)(?=\s*(BindIp|$))", line))

                        except Exception as e:
                            self.logger.error(f"[Thread ID: {thread_id}] Error parsing SD line: {line} - {e}")
                            continue

                    if logs_map['connect_request'].lower() in line_l:
                        flags['send_connect_request'] = 1

                    if logs_map['signxupmheader'].lower() in line_l and not flags['send_connect_request']:
                        flags['x_upm_header_present'] = 1
                        if service_type == 1 and logs_map['resolved_ip'].lower() in line_l:
                            issues.append(
                                f"[Thread ID: {thread_id}] T1.0 probe should not contain 'resolved_ip'. Log: {line}")
                        elif service_type == 2:
                            if logs_map['resolved_ip'].lower() not in line_l:
                                issues.append(f"[Thread ID: {thread_id}] T2.0 probe missing 'resolved_ip'. Log: {line}")
                            elif line.strip().endswith("resolved_ip:"):
                                issues.append(f"[Thread ID: {thread_id}] Empty 'resolved_ip'. Log: {line}")

                    if logs_map['x_upm_info'].lower() in line_l:
                        flags['x_upm_info_present'] = 1

                    if logs_map['connect_response_code'].lower() in line_l:
                        flags['connect_response_code'] = line.split("CONNECT Response Code:", 1)[-1].strip()
                        flags['check_response_code'] = 1

                    if logs_map['response_status'].lower() in line_l:
                        flags['response_status'] = line.split("Response Status:", 1)[-1].strip()
                        flags['check_response_status'] = 1

                    if logs_map['saving_data'].lower() in line_l:
                        flags['save_data_for_host'] = 1

                    if logs_map['monitor_done'].lower() in line_l:
                        issue_prefix = f"[Thread ID: {thread_id}]"
                        self.logger.info(f"{flags['starting_text']}")
                        self.logger.info(f"{flags['sd_text']}")
                        self.logger.info(f"{issue_prefix} SD Response Found: {flags['sd_response_found']}")
                        self.logger.info(f"{issue_prefix} CONNECT Response Code: {flags['connect_response_code']}")
                        self.logger.info(f"{issue_prefix} Response Status: {flags['response_status']}")

                        if flags['is_zpa']:
                            flags['check_sd_sme_ip'] = True
                            flags['check_sd_sme_port'] = True
                        else:
                            flags['check_broker_name'] = True
                            flags['check_broker_ip'] = True

                        checks = [
                            ('x_upm_info_present', "Missing 'X-UPM-INFO'."),
                            ('x_upm_header_present', "Missing 'signXUPMHeader'.", not is_lt),
                            ('check_response_code', "Missing CONNECT Response Code."),
                            ('check_response_status', "Missing Response Status."),
                            ('send_connect_request', "Missing CONNECT request log."),
                            ('save_data_for_host', "Missing 'Saving data for host'."),
                            ('check_sd_url', f"Missing SD URL. log - {flags['sd_text']}"),
                            ('check_sd_ip', f"Missing SD Destination IP. log - {flags['sd_text']}"),
                            ('check_sd_port', f"Missing SD Destination Port. log - {flags['sd_text']}"),
                            ('check_sd_sme_ip', f"Missing SD SME IP. log - {flags['sd_text']}"),
                            ('check_sd_sme_port', f"Missing SD SME Port. log - {flags['sd_text']}"),
                            ('check_broker_name', f"Missing Broker Name. log - {flags['sd_text']}"),
                            ('check_broker_ip', f"Missing Broker IP. log - {flags['sd_text']}"),
                        ]

                        for item in checks:
                            flag_key = item[0]
                            msg = item[1]
                            condition = True
                            if len(item) == 3:
                                condition = item[2]
                            if not flags.get(flag_key, False) and condition:
                                issues.append(f"{issue_prefix} {msg} Probe: {flags['starting_text']}")

                        if flags['response_status'].startswith('4'):
                            issues.append(
                                f"{issue_prefix} Invalid response status: {flags['response_status']}. Probe: {flags['starting_text']}")
                        if flags['connect_response_code'].startswith('4'):
                            issues.append(
                                f"{issue_prefix} Bad CONNECT request: {flags['connect_response_code']}. Probe: {flags['starting_text']}")

                        if not any(k for k in issues if k.startswith(issue_prefix)):
                            self.logger.info(f"{issue_prefix} Probe Successful.")
                        self.logger.info("-" * 120)
                        del active_threads[thread_id]

            if matching_probe_found == 0:
                self.logger.error(
                    f"No {sd_type} {'LT' if is_lt else 'Regular'} probes found in the ZSAUpm_Webload logs.\n\n")
                pytest.skip(f"No {sd_type} {'LT' if is_lt else 'Regular'} probes found in the ZSAUpm_Webload logs.\n\n")

            if issues:
                return False, f"Validation failed for {sd_type} {'LT' if is_lt else 'Regular'} probes:\n\n" + "\n\n\n".join(
                    issues)

            msg = f"All {sd_type} {'LT' if is_lt else 'Regular'} probes validated successfully."
            self.logger.info(msg)
            return True, msg

        except Exception as e:
            self.logger.error(f"Unexpected error in verify_web_probes: {e}")
            return False, f"Exception occurred during probe verification: {e}"

    def validate_attribute(self, value, attribute_name, current_json):
        """
        Validates a given attribute value based on predefined column rules.

        Args:
            value: The value to validate.
            attribute_name (str): Name of the attribute to validate.
            current_json (dict): The full JSON object being validated (used for logging).

        Returns:
            Tuple[bool, str]: Success flag and a message indicating the result.
        """
        try:
            column_rule = self.column_rules.get(attribute_name)

            if column_rule is None:
                self.logger.info(f"Attribute '{attribute_name}' has no validation rule defined.")
                return False, f"No validation rule defined for attribute '{attribute_name}'"

            if not column_rule.get('allow_empty', False) and value in [None, '', []]:
                self.logger.info(f"Value for '{attribute_name}' is empty but 'allow_empty' is False. Value: '{value}'")
                return False, f"Value for '{attribute_name}' is empty but 'allow_empty' is False"

            if column_rule.get('data_type') == 'float':
                try:
                    value = float(value)
                except (ValueError, TypeError):
                    self.logger.info(f"Value '{value}' for '{attribute_name}' cannot be converted to float.")
                    return False, f"Value '{value}' for '{attribute_name}' cannot be converted to float."

                min_val = column_rule.get('min_value')
                max_val = column_rule.get('max_value')

                if min_val is not None and value < min_val:
                    self.logger.info(
                        f"Value '{value}' for '{attribute_name}' is less than the minimum allowed value of {min_val}. Web JSON Data - {current_json}")
                    return False, f"Value '{value}' for '{attribute_name}' is less than the minimum allowed value of {min_val}. Web JSON Data - {current_json}"

                if max_val is not None and value > max_val:
                    self.logger.info(
                        f"Value '{value}' for '{attribute_name}' exceeds the maximum allowed value of {max_val}. Web JSON Data - {current_json}")
                    return False, f"Value '{value}' for '{attribute_name}' exceeds the maximum allowed value of {max_val}. Web JSON Data - {current_json}"

            self.logger.info(f"Attribute '{attribute_name}' with value '{value}' passed validation.")
            return True, f"Attribute '{attribute_name}' with value '{value}' is valid."

        except Exception as e:
            self.logger.error(f"Unexpected error : {e}")
            return False, f"Exception occurred : {e}"

    def verify_json_attribute_from_log(self, attribute_to_check,probe_url):
        """
        Validates a specific attribute across all JSON entries from the log.

        Args:
            attribute_to_check (str): The attribute key to validate (e.g., 'tot_payload_bytes', 'statsV2').

        Returns:
            Tuple[bool, str]: Success flag and message with details or errors.
        """
        try:
            issues = []
            success, json_list = self.collect_json_from_log()

            if not success:
                return False, f"Failed to collect JSON data: {json_list}"

            for idx, current_json in enumerate(json_list):
                try:
                    if attribute_to_check == 'tot_payload_bytes':
                        value = current_json.get('tags', {}).get(attribute_to_check)
                    else:
                        value = current_json.get(attribute_to_check)

                    is_valid, msg = self.validate_attribute(attribute_name=attribute_to_check, value=value,
                                                            current_json=current_json)

                    if not is_valid:
                        issues.append(f"[Entry {idx}] {msg}")
                    else:
                        self.logger.info(f"Validated {attribute_to_check}: {value}")


                except Exception as e:
                    return False, f"[Entry {idx}] Exception during validation: {str(e)}"

            if issues:
                return False, "\n\n\n".join(issues)

            self.logger.info(f"All entries passed validation for '{attribute_to_check}'")
            return True, f"All entries passed validation for '{attribute_to_check}'"

        except Exception as e:
            self.logger.error(f"Unexpected error : {e}")
            return False, f"Exception occurred : {e}"
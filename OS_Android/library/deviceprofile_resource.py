import pytest, sys, ast, re, os, subprocess, pathlib, allure, json
from OS_Android.library.database_parse import <PERSON><PERSON><PERSON><PERSON><PERSON>
from OS_Android.library.database_parse import <PERSON>g<PERSON>andler
from common_lib.common.log_ops import *
from OS_Android.library.ops_system import SysOps
from OS_Android.library.fetch_android_logs import FetchAndroidLogs
const_obj  = constants.Utils()




class deviceprofile_resource:
    def __init__(self):
        self.db_handler = DbHandler()
        self.log_handler = LogHandler()
        self.logger = logger.Logger.initialize_logger(log_file_name="zcc_misc.log", log_level="DEBUG")
        self.db_path = os.path.join(const_obj.LOG_PATH, f'upm_device_profile.db')
        self.sys_ops = SysOps(log_handle = self.logger)
        self.export_logs = FetchAndroidLogs()

    def verify_and_validate_deviceprofile_data(self, table_name, columns):
        """
        Validates the specified columns of a database table against predefined validation rules.
        Each column is checked for appropriate allowed ranges, and mandatory fields.
        The function also logs any issues or inconsistencies found during the validation.

        Parameters:
            table_name (str): The base name of the table (prefix 'tbl_' is added internally).
            columns (list): A list of column names to validate within the specified table.

        Returns:
            tuple: A tuple containing:
                - bool: `True` if all validations pass, `False` if any validation fails.
                - str: A success message if validations pass, or a string listing any validation issues found.
        """

        db_path = os.path.join(const_obj.LOG_PATH, f'upm_device_profile.db')
        self.db_handler.create_db_connection(db_path)

        actual_table_name = f"tbl_{table_name}"
        self.logger.info(f"Verifying data from {actual_table_name}...")
        rows_data = self.db_handler.execute_query(f"SELECT * FROM {actual_table_name}")

        if not rows_data:
            self.db_handler.close_db_connection()
            pytest.skip(f"'{actual_table_name}' is empty.")

        column_rules = {
            'pct_total': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'pct_kernel': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'pct_user': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'pct_idle': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'rssi_dbm': {'allow_empty': False, 'data_type': 'float', 'min_value': -100, 'max_value': 100},
            'tx_pkts': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100000000},
            'rx_pkts': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100000000},
            'rxmit_pkts': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100000000},
            'rxmit_pct': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'snr': {'allow_empty': False, 'data_type': 'float', 'min_value': -1, 'max_value': 100},
            'mcs': {'allow_empty': False, 'data_type': 'float', 'min_value': -1, 'max_value': 100},
            'signal_quality': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'rd_bps': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100000000000},
            'wt_bps': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100000000000},
            'free_mb': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 10000000000},
            'avg_diskq_len_scale_1000': {'allow_empty': False, 'data_type': 'float', 'min_value': 0,
                                         'max_value': 10000000000},
            'pct_used': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'cpu_total_pct': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'mem_wss_pct': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 100},
            'isWifi': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 1},
            'is_active': {'allow_empty': False, 'data_type': 'float', 'min_value': 0, 'max_value': 1},
            'cur_speed_mhz': {'allow_empty': False, 'data_type': 'float', 'min_value': 1, 'max_value': 10000000},
            'max_speed_mhz': {'allow_empty': False, 'data_type': 'float', 'min_value': 1, 'max_value': 10000000},
            'num_logical_processors': {'allow_empty': False, 'data_type': 'float', 'min_value': 1, 'max_value': 100},
            'num_of_cores': {'allow_empty': False, 'data_type': 'float', 'min_value': 1, 'max_value': 100},
            'bandwidth_mbps': {'allow_empty': False, 'data_type': 'float', 'min_value': -1, 'max_value': 10000000},
            'channel': {'allow_empty': False, 'data_type': 'float', 'min_value': 1, 'max_value': 165},
            'size': {'allow_empty': False, 'data_type': 'float', 'min_value': 1, 'max_value': 9999999999999999},
            'refid': {'allow_empty': True, 'data_type': 'string'},
            'ifname': {'allow_empty': True, 'data_type': 'string'},
            'timestamp': {'allow_empty': False, 'data_type': 'string'},
            'ssid': {'allow_empty': False, 'data_type': 'string'},
            'bssid': {'allow_empty': False, 'data_type': 'string'},
            'type': {'allow_empty': False, 'data_type': 'string'},
            'driver': {'allow_empty': False, 'data_type': 'string'},
            'used': {'allow_empty': False, 'data_type': 'string'},
            'sys_model': {'allow_empty': False, 'data_type': 'string'},
            'os_name': {'allow_empty': False, 'data_type': 'string'},
            'name': {'allow_empty': False, 'data_type': 'string'},
            'os_build': {'allow_empty': False, 'data_type': 'string'},
            'os_version': {'allow_empty': False, 'data_type': 'string'},
            'manufacturer': {'allow_empty': False, 'data_type': 'string'},
            'model': {'allow_empty': False, 'data_type': 'string'},
            'guid': {'allow_empty': False, 'data_type': 'string'},
            'hw_serial_no': {'allow_empty': False, 'data_type': 'string'},
            'sys_mfg': {'allow_empty': False, 'data_type': 'string'},
            'sys_type': {'allow_empty': False, 'data_type': 'string'},
            'gpu': {'allow_empty': False, 'data_type': 'string'},
            'mem_model': {'allow_empty': False, 'data_type': 'string'},
            'hostname': {'allow_empty': False, 'data_type': 'string'},
            'netbios_name': {'allow_empty': False, 'data_type': 'string'},
            'username': {'allow_empty': False, 'data_type': 'string'},
            'total_installed_mem': {'allow_empty': False, 'data_type': 'float', 'min_value': 1,
                                    'max_value': 9999999999999999},
            'total_phy_mem': {'allow_empty': False, 'data_type': 'float', 'min_value': 1,
                              'max_value': 9999999999999999},
            'avail_phy_mem': {'allow_empty': False, 'data_type': 'float', 'min_value': 1,
                              'max_value': 9999999999999999},
            'last_bootup_time': {'allow_empty': False, 'data_type': 'string'},
            'num_processors': {'allow_empty': False, 'data_type': 'float', 'min_value': 1, 'max_value': 100},
            'npcap_version': {'allow_empty': False, 'data_type': 'float', 'min_value': 1, 'max_value': 100},
        }

        all_issues = []

        try:
            schema_query = f"PRAGMA table_info({actual_table_name});"
            schema = self.db_handler.execute_query(schema_query)
            self.db_handler.close_db_connection()
            db_columns = [col['name'] for col in schema]
        except Exception as e:
            self.logger.error(f"Error fetching table schema for '{actual_table_name}': {e}")
            return False, f"Error fetching table schema for '{actual_table_name}'."

        invalid_columns = [col for col in columns if col not in db_columns]
        if invalid_columns:
            invalid_columns_str = ", ".join(invalid_columns)
            self.logger.warn(
                f"Columns {invalid_columns_str} do not exist in the table '{actual_table_name}'. Skipping these columns.")
            columns = [col for col in columns if col in db_columns]

        for row_idx, row_data in enumerate(rows_data, start=1):
            row_issues = []
            self.logger.info(f"{'#' * 20} Verifying row {row_idx}, table: '{actual_table_name}' {'#' * 20}\n")

            for col in columns:
                value = str(row_data[col])
                self.logger.info(f"Verifying column '{col}': {value}")

                if col not in column_rules:
                    self.logger.info(f"Skipping column '{col}' as it is not defined in column rules.")
                    continue

                rules = column_rules[col]

                # Special check for network_interface table
                if table_name == "network_interface" and col in ['bandwidth_mbps', 'send_bps', 'recv_bps']:
                    try:
                        is_active = float(row_data['is_active'])
                        value_float = float(value)
                        if is_active == 1 and value_float < 0:
                            msg = f"Invalid value '{value}' for '{col}' in row {row_idx}, table: '{actual_table_name}'. Should be non-negative when is_active=1."
                            self.logger.error(msg)
                            row_issues.append(msg)
                    except (ValueError, TypeError) as e:
                        msg = f"Error validating '{col}' in row {row_idx}, table: '{actual_table_name}': {e}"
                        self.logger.error(msg)
                        row_issues.append(msg)
                    continue  # skip normal rule check for these columns

                if not value and not rules.get('allow_empty', True):
                    msg = f"Empty value found at '{col}' in row {row_idx}, table: '{actual_table_name}'. It should not be empty."
                    self.logger.error(msg)
                    row_issues.append(msg)

                elif rules['data_type'] in ['float', 'int']:
                    try:
                        value_float = float(value)
                    except ValueError:
                        msg = f"Invalid value '{value}' for column '{col}' in row {row_idx}, table: '{actual_table_name}'. It should be a number."
                        self.logger.error(msg)
                        row_issues.append(msg)
                        continue

                    if 'min_value' in rules and value_float < rules['min_value']:
                        msg = f"Invalid value '{value}' for column '{col}' in row {row_idx}, table: '{actual_table_name}'. It must be >= {rules['min_value']}."
                        self.logger.error(msg)
                        row_issues.append(msg)
                    if 'max_value' in rules and value_float > rules['max_value']:
                        msg = f"Invalid value '{value}' for column '{col}' in row {row_idx}, table: '{actual_table_name}'. It must be <= {rules['max_value']}."
                        self.logger.error(msg)
                        row_issues.append(msg)

                elif rules['data_type'] == 'string':
                    if not isinstance(value, str):
                        msg = f"Invalid value '{value}' for column '{col}' in row {row_idx}, table: '{actual_table_name}'. It should be a string."
                        self.logger.error(msg)
                        row_issues.append(msg)

            if not row_issues:
                self.logger.info(f"Row {row_idx} successfully verified with no issues, table: '{actual_table_name}'.")

            if row_issues:
                all_issues.extend(row_issues)

        if all_issues:
            return False, "\n".join(all_issues)
        else:
            self.logger.info(f"Successfully verified all rows in table '{actual_table_name}'. No issues found.")
            return True, "SUCCESS"


    def validate_cpu_info(self):
        """
        Validates the consistency of CPU information stored in the device profile database.
        The function performs the following checks:
        - Ensures all rows in the 'cpu_info' table have non-empty and matching 'model' and 'manufacturer' fields.
        - Compares the reference values from the database against the actual system CPU information.

        Returns:
            tuple:
                - bool: True if all rows are consistent and match the system CPU info, otherwise False.
                - str | list: "SUCCESS" if verification passes, otherwise a list of issues found.
        """
        self.logger.info("Starting CPU verification against values stored in the device profile database.")
        issues = []
        actual_table_name = "tbl_cpu_info"
        system_info = {
            "num_logical_processors": self.sys_ops.get_no_cpu_cores(),
            "max_speed_mhz": self.sys_ops.get_max_freq(),
            "cur_speed_mhz": self.sys_ops.get_avg_freq(),
            "num_of_cores":  self.sys_ops.get_no_cpu_cores(),
            "manufacturer": "Google"
        }

        # Pending for Now, will add in some other PR while refactoring
        # adb shell getprop | findstr "ro.product.manufacturer"
        # output -- [ro.product.manufacturer]: [Google]
        # No way to fetch Model

        self.logger.info(f"System CPU Manufacturer                   : {system_info['manufacturer']}")
        self.logger.info(f"System CPU no. of logical Processor       : {system_info['num_logical_processors']}")
        self.logger.info(f"System CPU no. of cores                   : {system_info['num_of_cores']}")
        self.logger.info(f"System CPU max_speed_mhz                  : {system_info['max_speed_mhz']}")
        self.logger.info(f"System CPU current_speed_mhz              : {system_info['cur_speed_mhz']}")

        try:
            # Connect to the database
            db_path = os.path.join(const_obj.LOG_PATH, f'upm_device_profile.db')
            self.db_handler.create_db_connection(db_path)
            self.logger.info(f"Connected to device profile database at: {db_path}")

            rows_data = self.db_handler.execute_query(f"SELECT * FROM {actual_table_name}")
            self.db_handler.close_db_connection()
            self.logger.info(f"Fetched {len(rows_data)} record(s) from '{actual_table_name}'.")
            # If no data in the table
            if not rows_data:
                msg = f"No data found in table '{actual_table_name}'."
                issues.append(msg)
                self.logger.error(msg)
                return False, issues

            # Use reference values from the first row
            reference_values = rows_data[0]

            # Log reference values from the database (first row)
            self.logger.info(f"Database CPU Model                          : {reference_values['model']}")
            self.logger.info(f"Database CPU Manufacturer                   : {reference_values['manufacturer']}")
            self.logger.info(f"Database CPU no. of logical Processor       : {reference_values['num_logical_processors']}")
            self.logger.info(f"Database CPU no. of cores                   : {reference_values['num_of_cores']}")
            self.logger.info(f"Database CPU max_speed_mhz                  : {reference_values['max_speed_mhz']}")
            self.logger.info(f"Database CPU current_speed                  : {reference_values['cur_speed_mhz']}")

            # Data validation fields
            fields_to_validate = [
                "model", "manufacturer", "num_of_cores",
                "num_logical_processors", "max_speed_mhz", "cur_speed_mhz"
            ]

            # Validate each row in the table
            for row_idx, row_data in enumerate(rows_data, start=1):
                self.logger.info(f"Validating row {row_idx}...")
                for field in fields_to_validate:
                    value = row_data[field]
                    reference_value = reference_values[field]

                    # Check if fields are empty
                    if not value:
                        msg = f"Row {row_idx}: '{field}' field is empty."
                        issues.append(msg)
                        self.logger.error(msg)

                    # Check for mismatched values
                    elif value != reference_value:
                        msg = (f"Row {row_idx}: '{field}' mismatch. "
                               f"Expected '{reference_value}', found '{value}'.")
                        issues.append(msg)
                        self.logger.error(msg)

            # Compare system attributes to reference values
            for field, system_value in system_info.items():
                db_value = reference_values[field]
                if db_value != system_value:
                    msg = (f"Mismatch between system and DB '{field}': "
                           f"system = '{system_value}', DB = '{db_value}'.")
                    issues.append(msg)
                    self.logger.error(msg)

            # Log final results
            if issues:
                self.logger.warning(f"CPU verification completed with {len(issues)} issue(s).")
                return False, issues

            self.logger.info("CPU verification successful: all DB rows are consistent and match the system CPU info.")
            return True, "SUCCESS"

        except Exception as e:
            self.logger.error(f"CPU verification failed due to unexpected error: {e}")
            return False, [f"Exception occurred: {str(e)}"]

    def verify_and_compare_system_info(self):
        """
        Compares the current system information with the data stored in the database
        ('tbl_system_info') to ensure they match. The system information includes
        properties such as OS name, build number, hardware serial number, GPU, hostname,
        and total physical memory. Any mismatches or issues are logged, and the result
        is returned as a boolean indicating success or failure.

        Returns:
            tuple: A tuple containing:
                - bool: `True` if all values match, `False` if any mismatch is found.
                - str: A success message if all values match, or a string of error messages
                if any mismatches are detected.
        """
        table_name = "system_info"
        db_path = os.path.join(const_obj.LOG_PATH, f'upm_device_profile.db')
        self.db_handler.create_db_connection(db_path)
        rows = self.db_handler.execute_query(f"SELECT * FROM tbl_{table_name}")
        self.db_handler.close_db_connection()

        if not rows:
            pytest.skip(f"'tbl_{table_name}' is empty.")

        #'netbios_name': Pending
        #'total_phy_mem': Pending

        app_info_keys = ['UserName', 'Hardware Id', 'Build Id', 'Model', 'Manufacturer']
        app_info = {key: self.export_logs.appinfo_file_values(key) for key in app_info_keys}

        # Extract system-related details
        user = app_info['UserName'].split("@")[-1]
        hardware_id = app_info['Hardware Id']
        hostname = f"{hardware_id}@{user}"
        username = hostname  # Username and hostname are identical
        sys_type = os_name = 'Android'
        gpu = "ARM Mali-G710"  # Fixed GPU info
        os_version = self.sys_ops.and_os_version()
        num_processors = self.sys_ops.get_no_cpu_cores()
        last_bootup_time = self.sys_ops.get_last_bootup_time()

        # Update system and OS type for ChromeOS devices
        if self.sys_ops.android_device_type().lower() == "chromeos":
            sys_type = os_name = "Chrome OS"

        # Define the dictionary with all the required values
        current = {
            'os_name': os_name,
            'os_version': os_version,
            'os_build': app_info['Build Id'],
            'hw_serial_no': hardware_id,
            'sys_mfg': app_info['Manufacturer'],
            'sys_model': app_info['Model'],
            'sys_type': sys_type,
            'gpu': gpu,  # GPU information
            'hostname': hostname,
            'num_processors': num_processors,
            'username': username
        }



        issues = []

        for idx, row in enumerate(rows, start=1):
            row_dict = dict(row)

            for col, expected in current.items():
                if col not in row_dict.keys():
                    issues.append(f" Error Row {idx}: '{col}' is missing in DB.")
                    continue

                db_val = row_dict[col]
                db_str = str(db_val).strip().lower()
                expected_str = str(expected).strip().lower()

                if isinstance(db_val, tuple):
                    db_val = db_val[1]
                    db_str = str(db_val).strip().lower()

                if isinstance(expected, tuple):
                    expected = expected[1]
                    expected_str = str(expected).strip().lower()

                if col == 'gpu':
                    db_gpus = [g.strip().lower() for g in db_str.split(',') if g]
                    unmatched = [g for g in db_gpus if g not in expected_str]
                    if unmatched:
                        issues.append(f"Error Row {idx}: GPU mismatch. DB GPUs = {db_gpus}, System GPUs = {expected}")
                elif col == 'total_phy_mem':
                    try:
                        db_val_float = float(db_val)
                        expected_float = float(expected)
                        diff_pct = abs(db_val_float - expected_float) / expected_float * 100
                        if diff_pct > 2:
                            issues.append(f"Error Row {idx}: 'total_phy_mem' mismatch. DB = {db_val}, System = {expected}")
                    except Exception as e:
                        issues.append(f"Error Row {idx}: Error comparing 'total_phy_mem': {e}")
                elif col == "last_bootup_time":
                    if not last_bootup_time[0]:
                        issues.append(f"---- Failed to fetch 'last_bootup_time' from device----")
                        continue

                    last_bootup_time = last_bootup_time[1]
                    last_bootup_time = int(last_bootup_time)

                    value_from_db = int(db_val / 1000)
                    buffer = abs(value_from_db - last_bootup_time)
                    if buffer > 5:
                        issues.append(f"Error Row {idx}: 'last_bootup_time' mismatch. DB = {db_val}, System = {expected}")
                else:
                    if db_str != expected_str:
                        issues.append(f"Error Row {idx}: Mismatch in '{col}'. DB = '{db_str}' vs System = '{expected_str}'")

        if issues:
            for issue in issues:
                self.logger.error(issue)
            return False, "\n".join(issues)
        else:
            self.logger.info(f"SUCCESS All rows in 'tbl_{table_name}' matched current system values.")
            return True, "SUCCESS"


    def validate_wifi_device_profile(self):
        """
        Validates the WiFi configuration stored in the device profile database against the current system WiFi state.

        Returns:
            tuple: (True, "Success") if all fields match;
                (False, [list of issues]) if any mismatches or errors are found.

        This includes validation of:
            - Interface Name
            - Interface GUID
            - WiFi Channel
        """
        self.logger.info(" Starting WiFi DB validation...")
        issues = []

        db_path = os.path.join(const_obj.LOG_PATH, f'upm_device_profile.db')
        self.db_handler.create_db_connection(db_path)
        rows = self.db_handler.execute_query("SELECT * FROM tbl_wifi_info")
        self.db_handler.close_db_connection()

        if not rows:
            return False, ["No WiFi info found in 'tbl_wifi_info'"]

        act_net = self.export_logs.appinfo_file_values('Active Network')

        # Use the last row (latest entry) from the table
        row = rows[-1]
        ifname_db = str(row["ifname"]).lower()
        guid_db = str(row["guid"]).lower()
        channel_db = str(row["channel"])
        type_db = str(row["type"])

        active_interface_name = self.sys_ops.get_active_interface_details()
        if not active_interface_name[0] and len(act_net)==0:
            self.logger.warn(" WiFi interface not connected or unavailable.")
            return False, (f" Failed to fetch interface name via adb shell ip route: {active_interface_name[1]}"
                           f"\n And also with APP Info File:{act_net}")
        elif active_interface_name[0]:
            ifname_sys = guid_sys = active_interface_name[1]
        else:
            ifname_sys = guid_sys = act_net


        checks = [
            ("Interface Name", ifname_db, ifname_sys),
            ("GUID", guid_db, guid_sys),
            ("Channel", channel_db, ""),
            ("type", type_db, "")
        ]

        for label, db_val, sys_val in checks:
            if not db_val:
                msg = f"{label} is missing in DB."
                self.logger.error(msg)
                issues.append(msg)
            elif label == "Channel" or label == "type":
                if len(db_val) ==0:
                    msg = f"{label} not found on db."
                    self.logger.error(msg)
                    issues.append(msg)
            elif not sys_val:
                msg = f"{label} not found on system."
                self.logger.error(msg)
                issues.append(msg)
            elif db_val != sys_val:
                msg = f"{label} mismatch: DB='{db_val}' vs System='{sys_val}'"
                self.logger.error(msg)
                issues.append(msg)
            else:
                self.logger.info(f"SUCCESS {label} matched: {db_val}")

        self.logger.info(
            f"📌 Final Values:\nInterface: {ifname_db} | {ifname_sys}\nGUID: {guid_db} | {guid_sys}\nChannel: {channel_db} | {channel_db}")

        return (False, issues) if issues else (True, "Success")


    def verify_interface_details(self):
        """
        Validates network interface details from the database against live system values.

        This function fetches the active network interface details from the system,
        including interface name, type, status, and IP addresses (IPv4, IPv6, DNS).
        It then compares these values against the latest entry in the
        'tbl_network_interface' table from the device profile database.

        Returns:
            tuple: A tuple containing:
                - bool: True if all details match, False otherwise.
                - str: "SUCCESS" on match, or a string of error messages on mismatch.
        """
        self.logger.info("Starting network interface validation...")
        issues = []

        # 1. Fetch current system network details
        try:
            device_type = self.sys_ops.android_device_type()
            act_net = self.export_logs.appinfo_file_values('Active Network')

            if act_net.startswith("wlan"):
                nw_type = "Wireless"
            elif device_type.lower() == "chromeos" and act_net.startswith("eth"):
                nw_type = "Wireless"
            else:
                nw_type = "Cellular"

            ipv4_sys = self.sys_ops.active_ipv4(act_net)
            ipv6_list_sys = self.sys_ops.list_of_active_ipv6(act_net)
            ifname_sys = act_net
            status_sys = 'Connected'

            network_info_dict_result = self.sys_ops.get_network_info(ifname_sys)
            if not network_info_dict_result[0]:
                msg = f"Failed to get system network info: {network_info_dict_result[1]}"
                self.logger.error(msg)
                return False, msg

            data_dict = network_info_dict_result[2]
            dns_sys = data_dict.get('DnsAddresses', [])
            gateway = data_dict.get('gateway', [])


            system_values = {
                'adapter_name': ifname_sys,
                'ifname': ifname_sys,
                'type': nw_type,
                'status': status_sys,
                'ipv4': ipv4_sys,
                'dns_svrs': dns_sys,
                'ipv6': ipv6_list_sys,
                'gateway': gateway
            }
            self.logger.info("--- System Network Interface Details ---")
            for key, value in system_values.items():
                self.logger.info(f"System {key}: {value}")
            self.logger.info("------------------------------------")

        except Exception as e:
            msg = f"Failed to fetch system network details: {e}"
            self.logger.error(msg)
            return False, msg

        # 2. Fetch latest network details from DB
        try:
            self.db_handler.create_db_connection(self.db_path)
            rows = self.db_handler.execute_query("SELECT * FROM tbl_network_interface")
            self.db_handler.close_db_connection()

            if not rows:
                msg = "No data found in 'tbl_network_interface'"
                self.logger.error(msg)
                return False, msg

            latest_row = dict(rows[-1])
            self.logger.info("--- DB Network Interface Details (Latest Row) ---")
            for key, value in latest_row.items():
                self.logger.info(f"DB {key}: {value}")
            self.logger.info("---------------------------------------------")

        except Exception as e:
            msg = f"Failed to fetch DB network details: {e}"
            self.logger.error(msg)
            return False, msg

        # 3. Compare system and DB values
        for key, sys_val in system_values.items():
            db_val_raw = latest_row.get(key)
            if db_val_raw is None:
                issues.append(f"Error '{key}' not found in DB row.")
                continue

            if key in ['dns_svrs', 'ipv6']:
                try:
                    db_val_list = ast.literal_eval(db_val_raw) if isinstance(db_val_raw, str) and db_val_raw.startswith('[') else [str(db_val_raw)] if db_val_raw else []
                    if set(db_val_list) != set(sys_val):
                        issues.append(f"Error {key} mismatch: DB='{db_val_list}' vs System='{sys_val}'")
                except Exception as e:
                    issues.append(f"Error Could not parse/compare {key} from DB: '{db_val_raw}'. Error: {e}")
            elif key != 'adapter_name': # adapter_name is same as ifname, already checked
                if str(db_val_raw).strip().lower() != str(sys_val).strip().lower():
                    issues.append(f"Error {key} mismatch: DB='{db_val_raw}' vs System='{sys_val}'")

        if issues:
            for issue in issues:
                self.logger.error(issue)
            return False, "\n".join(issues)

        self.logger.info("SUCCESS All network interface details matched between DB and system.")
        return True, "SUCCESS"



import requests, platform, socket
import sys, os, json, time, datetime
import logging
from OS_Android.library import zdx_res
from OS_Android.library.ops_system import SysOps
from common_lib.common import logger, ops_common
from common_lib.adminzdx.zdxhelper import ZdxHelper

# import zdx_res
requests.packages.urllib3.disable_warnings()
import copy


class ZDX_ADMIN:
    def __init__(self, cloudname, configfile, logger=None, Variable=None):
        self.var = Variable if Variable else None
        self.cloudname = cloudname
        self.logger = logger if logger != None else self.Initialize_Logger("ZDX_ADMIN.log", Log_Level="INFO")
        if type(configfile) == dict:
            self.config = configfile;
            user = copy.deepcopy(self.config["ZIA_USER_ID"]);
            self.config = copy.deepcopy(
                self.config['ZDX_ADMIN']);
            self.config['ZDX_USERNAME'] = user;
            del (user)
        else:
            self.get_config(configfilename=configfile)
        self.resource = zdx_res.ZDX_RES(cloudname=self.cloudname, Variable=Variable)
        self.sys_ops = SysOps()
        self.device_name = self.sys_ops.get_device_name()
        self.session = requests.Session()
        self.session.verify = False
        self.applicationdata = {}
        self.dtsessionsinfo = {}
        self.dtsessionfailreason = {}
        self.dtsessionduration = -1

        self.config_path = os.path.join(os.getcwd(), "config", os.environ.get("CONFIG_FILE"))
        self.req = ops_common.Request(cloud="zscalerbeta", config=self.config_path, portal="zdx",
                                      log_handle=self.logger)
        self.zdxhelper = ZdxHelper(cloud="zscalerbeta", config=self.config_path, log_handle=self.logger)

    def get_config(self, configfilename):
        """
        Retrieves the configuration from a specified file.

        Args:
            configfilename (str): The name of the configuration file.

        Returns:
            None

        Raises:
            None
        """
        # Initialize the config attribute to None
        self.config = None

        # Construct the full path to the config directory
        configdir = os.getcwd() + f"\\config"

        # Check if the config directory exists
        if os.path.exists(configdir):
            # Attempt to open the config file in read mode
            with open(configdir) as config_file:
                # Load the JSON configuration from the file
                self.config = json.load(config_file)

                # Extract the ZIA_USER_ID from the config and store it in the user variable
                user = copy.deepcopy(self.config["ZIA_USER_ID"])

                # Update the config to only include the ZDX_ADMIN section
                self.config = copy.deepcopy(self.config['ZDX_ADMIN'])

                # Update the ZDX_USERNAME in the config to the extracted user ID
                self.config['ZDX_USERNAME'] = user

                # Remove the user variable to free up memory
                del (user)
        else:
            # Log an error if the config file does not exist or the path is invalid
            self.logger.error("Config file does not exist or invalid path specified")

    def REQ(self, reqtype, url, data=None, params=None, headers=None, retry=False):
        """
        Sends an HTTP request to the specified URL.

        Args:
            reqtype (str): The type of HTTP request to send. Can be "get", "post", "delete", or "put".
            url (str): The URL to send the request to.
            data (dict, optional): The data to send with the request. Defaults to None.
            params (dict, optional): The query parameters to send with the request. Defaults to None.
            headers (dict, optional): The headers to send with the request. Defaults to None.
            retry (bool, optional): Whether to retry the request if it fails. Defaults to False.

        Returns:
            dict: The response from the server, parsed as JSON.

        Raises:
            Exception: If the request fails or the response is not valid JSON.
        """
        # Check that the request type is valid
        assert reqtype in ["get", "post", "delete", "put"]

        # Initialize the response variable
        response = None

        try:
            # Send the request based on the request type
            if reqtype == "get":
                # Send a GET request
                response = self.session.get(url=url, headers=headers, json=data, params=params)
            elif reqtype == "post":
                # Send a POST request
                response = self.session.post(url=url, headers=headers, json=data, params=params)
            elif reqtype == "delete":
                # Send a DELETE request
                response = self.session.delete(url, headers=headers, json=data, params=params)
            elif reqtype == "put":
                # Send a PUT request
                response = self.session.put(url, headers=headers, json=data, params=params)

            # Check if the response was successful
            if str(response.status_code) >= str(200) and str(response.status_code) <= str(300):
                # Log a debug message if the response was successful
                self.logger.debug(f'Success - {response.status_code}')
            else:
                # Raise an exception if the response was not successful
                raise Exception(response)

        except Exception as E:
            # If retry is True, try to recover from the error
            if retry:
                # Store the error message and status code
                self.dtsessionfailreason['fail_reason'] = response.text
                self.dtsessionfailreason['fail_status_code'] = response.status_code
                # Raise an exception with the error message and status code
                raise Exception(
                    f'failed - {response.status_code} - {response.text}\n url: {url}\nHeaders: {headers}\ndata: {data}')

            try:
                # Update the session headers with the auth header
                self.session.headers.update(self.resource.authheader)
                # If the session is not verifying the SSL certificate and the auth token file exists
                if not self.session.verify and os.path.exists(self.resource.authtokenFile):
                    # Read the auth token from the file
                    with open(self.resource.authtokenFile, "r") as f:
                        new_data = f.read().split(";")
                        # Update the session headers with the auth token
                        self.session.headers['X-Csrf-Token'] = new_data[0]
                        # Update the session cookies with the JSESSIONID
                        self.session.cookies.set(**{"name": "JSESSIONID", "value": new_data[1]})
                        # Send a GET request to the auth URL
                        response = self.session.get(url=self.resource.authurl)
                        # Enable SSL verification for the session
                        self.session.verify = True

                # If the response is None, log a critical message and wait 30 seconds
                if response is None:
                    self.logger.critical(
                        f"Encountered error {E}\nResponse: {response}\nRetrying after 30 seconds...")
                    time.sleep(30)

                # If the response is None or the status code is not 200, reset the session
                if response is None or str(response.status_code) != str(200):
                    # Close the current session
                    self.session.close()
                    # Create a new session
                    self.session = requests.Session()
                    # Update the session headers with the auth header
                    self.session.headers.update(self.resource.authheader)
                    # Send a GET request to the auth URL
                    response = self.session.get(self.resource.authurl)
                    # Update the session headers with the CSRF token
                    self.session.headers['X-Csrf-Token'] = response.headers.get('X-Csrf-Token')
                    # Log a warning message
                    self.logger.warning("Resetting the session to cloud {}".format(self.cloudname))
                    # Login to the cloud
                    self.__login()
                else:
                    # Log an info message
                    self.logger.info(f"Using existing session for user {self.config['ZDX_ADMIN_ID']}")

                # Retry the request
                return self.REQ(reqtype=reqtype, url=url, data=data, params=params, headers=headers, retry=True)

            except Exception as E:
                # Raise an exception if there is an error
                raise Exception(E)

        try:
            # Try to parse the response as JSON
            info = json.loads(response.text)
        except Exception as E:
            # If the response is not valid JSON, log a debug message and return None
            self.logger.debug("Response seems to be in html format which is not useful. Ignoring this response.")
            self.logger.debug(response.text)
            return None

        # Return the parsed JSON response
        return info

    def __login(self) -> None:
        """
        Logs into the ZDX Cloud using the provided admin credentials.

        This method sends a POST request to the authentication URL with the admin ID and password.
        If the login is successful, it retrieves the CSRF token and JSESSIONID from the response
        and writes them to the authentication token file.

        Args:
            None

        Returns:
            None

        Raises:
            Exception: If the login fails with a non-200 status code.
        """
        # Send a POST request to the authentication URL with the admin credentials
        response = self.session.post(url=self.resource.authurl, json={"username": self.config['ZDX_ADMIN_ID'],
                                                                      "password": self.config['ZDX_ADMIN_PASSWORD']})

        # Check if the login was successful (200 status code)
        if str(response.status_code) != str(200):
            # Log an error message with the status code if the login fails
            self.logger.error(
                f"Unable to login specified user {self.config['ZDX_ADMIN_ID']} into ZDX Cloud {self.cloudname}\nResponse Status Code : {response.status_code}")
            # Raise an exception with a descriptive error message
            raise Exception(
                f"Unable to login specified user {self.config['ZDX_ADMIN_ID']} into ZDX Cloud {self.cloudname}")

        # Write the CSRF token and JSESSIONID to the authentication token file
        with open(self.resource.authtokenFile, 'w', encoding="utf-8") as f:
            # Join the CSRF token and JSESSIONID with a semicolon
            data = ';'.join([self.session.headers['X-Csrf-Token'], response.cookies.get('JSESSIONID')])
            # Write the data to the file
            f.write(data)

    def logout(self) -> None:
        """
        Logs out of the current session.

        This method sends a DELETE request to the authentication URL to invalidate the
        current session. If the logout is unsuccessful, an error message is logged.

        Args:
            None

        Returns:
            None
        """
        # Send a DELETE request to the authentication URL to invalidate the session
        response = self.session.delete(url=self.resource.authurl)

        # Check if the logout was successful (200 status code)
        if str(response.status_code) != str(200):
            # Log an error message if the logout was unsuccessful
            self.logger.error(f"Unable to logout \n {response.text}")

        # Close the session to free up resources
        self.session.close()
        # Generated by Devraj., assisted by ZCoder on 16-October-2024 <Please retain this marker>

    def activate(self) -> None:
        """
        Activates the pending changes in the ZDX Cloud.

        This method sends a PUT request to the activation URL to change the status from pending to active.
        If the activation is successful, it logs an info message indicating that the pending changes have been activated.

        Args:
            None

        Returns:
            None

        Raises:
            Exception: If the activation fails and the status is not changed to "active".
        """
        # Send a PUT request to the activation URL with an empty data payload
        response = self.req.api_call("put", url=self.resource.activateurl, headers=self.resource.zdx_headers,
                                     data=json.dumps({}))
        status, message, response = self.zdxhelper.translate_response(response)

        # Check if the activation was successful by verifying the status in the response
        if not response['status'] == "active":
            # Raise an exception if the activation failed
            raise Exception("Could not activate status from pending to active")

        # Log an info message indicating that the pending changes have been activated
        self.logger.info("Activated the pending changes")

    def list_all_users(self) -> dict:
        """
        Retrieves a list of all users in the ZDX Cloud.

        This method sends a GET request to the all users URL and returns the response.

        Args:
            None

        Returns:
            dict: The response from the server, containing a list of all users.

        Raises:
            Exception: If the request fails or the response is not valid JSON.
        """
        # Send a GET request to the all users URL to retrieve the list of users
        response = self.req.api_call("get", url=self.resource.allusersurl, headers=self.resource.zdx_headers)
        # Return the response from the server
        status, message, response = self.zdxhelper.translate_response(response)
        return response

    def check_probes_exist(self, application_id, probe_type):
        """Check if monitor/probe exist for an application

        Args:
            application_id (str): ID of the application
            probe_type (str): Type of probe such as web/tcp/udp/icmp

        Returns:
            tuple: status, message, response
        """
        response = self.req.api_call("get", url=self.resource.listallmonitorsurl, headers=self.resource.zdx_headers)
        status, message, response = self.zdxhelper.translate_response(response)

        if not status:
            return (status, message, response)
        probe = None
        for probes in response:
            if probes['application']['id'] == application_id and probes[
                'type'] == "WEB" and probe_type.lower() == "web": probe = {'id': probes['id'], 'name': probes['name'],
                                                                           'url': probes['url']}; self.logger.info(
                f"Web probe {probes['name']} already exist with id {probes['id']}"); break
            if probes['application']['id'] == application_id and probes['type'] == "TRACERT" and probes[
                'protocol'] == "TCP" and probe_type.lower() == "tcp": probe = {'id': probes['id'], 'name': probes[
                'name']}; self.logger.info(
                f"Traceroute probe {probes['name']} with protocol {probes['protocol']} already exist with id {probes['id']}"); break
            if probes['application']['id'] == application_id and probes['type'] == "TRACERT" and probes[
                'protocol'] == "UDP" and probe_type.lower() == "udp": probe = {'id': probes['id'], 'name': probes[
                'name']}; self.logger.info(
                f"Traceroute probe {probes['name']} with protocol {probes['protocol']} already exist with id {probes['id']}"); break
            if probes['application']['id'] == application_id and probes['type'] == "TRACERT" and probes[
                'protocol'] == "ICMP" and probe_type.lower() == "icmp": probe = {'id': probes['id'], 'name': probes[
                'name']}; self.logger.info(
                f"Traceroute probe {probes['name']} with protocol {probes['protocol']} already exist with id {probes['id']}"); break
        if probe:
            return (True, "Probe exist already", probe)
        return (False, "Probe does not exist", probe)

    def list_all_devices(self, userid: str) -> dict:
        """
        Retrieves a list of all devices associated with the specified user ID.

        This method sends a GET request to the devices URL for the specified user ID.
        The response from the server is returned as a dictionary.

        Args:
            userid (str): The ID of the user whose devices are to be retrieved.

        Returns:
            dict: A dictionary containing the list of devices associated with the user.

        Raises:
            Exception: If the request fails or the response is not valid JSON.
        """
        # Send a GET request to the devices URL for the specified user ID
        response = self.req.api_call("get", url=self.resource.devicesurl.format(userid),
                                     headers=self.resource.zdx_headers)
        # Return the response from the server
        status, message, response = self.zdxhelper.translate_response(response)
        return response

    def delete_application_and_monitors(self, configs=None, extra_web_mtr=False) -> None:
        """
        Deletes the specified applications and their associated monitors.

        This method retrieves the application data, identifies the monitors associated with each application,
        and deletes them. It also disables and deletes the web probes.

        Args:
            configs (list, optional): A list of configuration dictionaries containing application data.
                Defaults to None, in which case the method uses the 'ZDX_APPLICATION' config.
            extra_web_mtr (bool, optional): A flag indicating whether to include extra web monitors.
                Defaults to False.

        Returns:
            None

        Raises:
            Exception: If any issues occur during the deletion process.
        """

        # If configs is not provided, use the 'ZDX_APPLICATION' config
        if configs is None:
            configs = self.config['ZDX_APPLICATION']

        # Retrieve the list of applications
        app_response = self.req.api_call("get", url=self.resource.applicationurl, headers=self.resource.zdx_headers)
        status, message, app_response = self.zdxhelper.translate_response(app_response)

        # Initialize an empty list to store any issues that occur during deletion
        ListIssues = []
        app = None

        # Iterate over each configuration
        for config in configs:
            # Skip extra web monitors if extra_web_mtr is False
            if extra_web_mtr and config['application']['name'].lower() != "extra_for_update_policy":
                continue

            # Check if the application exists in the application data
            if config['application']['name'] in self.applicationdata:
                app = self.applicationdata[config['application']['name']]
            else:
                # Initialize an empty dictionary to store application data
                app = {"appid": None, "WEB": {}, "TCP": {}, "UDP": {}, "ICMP": {}}

                # Find the application ID
                for application in app_response:
                    if application['name'].lower() == config['application']['name'].lower():
                        app['appid'] = application['id']
                        break

                # If the application ID is not found, log a warning and skip
                if app['appid'] is None:
                    self.logger.warning(
                        f"Cannot delete application {config['application']['name']} as it does not exist")
                    continue

                # Retrieve the list of monitors
                response = self.req.api_call("get", url=self.resource.listallmonitorsurl,
                                             headers=self.resource.zdx_headers)
                status, message, response = self.zdxhelper.translate_response(response)

                # Iterate over each monitor and store its data in the application dictionary
                for probes in response:
                    if probes['application']['id'] == app['appid'] and probes['type'] == "WEB":
                        app['WEB']['id'] = probes['id']
                        app['WEB']['name'] = probes['name']
                    if probes['application']['id'] == app['appid'] and probes['type'] == "TRACERT" and probes[
                        'protocol'] == "TCP":
                        app['TCP']['id'] = probes['id']
                        app['TCP']['name'] = probes['name']
                    if probes['application']['id'] == app['appid'] and probes['type'] == "TRACERT" and probes[
                        'protocol'] == "UDP":
                        app['UDP']['id'] = probes['id']
                        app['UDP']['name'] = probes['name']
                    if probes['application']['id'] == app['appid'] and probes['type'] == "TRACERT" and probes[
                        'protocol'] == "ICMP":
                        app['ICMP']['id'] = probes['id']
                        app['ICMP']['name'] = probes['name']

            try:
                # Delete the ICMP monitor
                if app['ICMP']:
                    self.req.api_call("delete", url="/".join(
                        [self.resource.monitorurl.format(str(app['appid'])), str(app['ICMP']['id'])]),
                                      headers=self.resource.zdx_headers)
                    self.logger.info(f"Successfully deleted ICMP monitor with id {app['ICMP']}")

                # Delete the UDP monitor
                if app['UDP']:
                    self.req.api_call("delete", url=self.resource.monitorurl.format(str(app['appid'])) + "/" + str(
                        app['UDP']['id']), headers=self.resource.zdx_headers)
                    self.logger.info(f"Successfully deleted UDP monitor with id {app['UDP']}")

                # Delete the TCP monitor
                if app['TCP']:
                    self.req.api_call("delete", url=self.resource.monitorurl.format(str(app['appid'])) + "/" + str(
                        app['TCP']['id']), headers=self.resource.zdx_headers)
                    self.logger.info(f"Successfully deleted TCP monitor with id {app['TCP']}")

                # Disable and delete the web probe
                if app['WEB']:
                    payload = copy.deepcopy(self.resource.disablewebprobe)
                    payload['id'] = app['WEB']['id']
                    payload['name'] = app['WEB']['name']
                    payload['url'] = config['web_probe']['url']
                    self.req.api_call("put", url=self.resource.monitorurl.format(str(app['appid'])) + "/" + str(
                        app['WEB']['id']), data=json.dumps(payload), headers=self.resource.zdx_headers)
                    self.logger.info(f"Successfully disabled Web probe {config['web_probe']['name']}")
                    self.req.api_call("delete", url=self.resource.monitorurl.format(str(app['appid'])) + "/" + str(
                        app['WEB']['id']), headers=self.resource.zdx_headers)
                    self.logger.info(f"Successfully deleted Web probe {config['web_probe']['name']}")

                # Delete the application
                self.req.api_call("delete", url=self.resource.applicationurl + "/" + str(app['appid']),
                                  headers=self.resource.zdx_headers)
                self.logger.info(f"Successfully deleted application {config['application']['name']}")

                # Remove the application from the application data
                if config['application']['name'] in self.applicationdata:
                    self.applicationdata.pop(config['application']['name'])
            except Exception as E:
                # Log any issues that occur during deletion
                ListIssues.append(E)
                self.logger.error(E)

        # Activate the changes
        self.activate()

        # Raise an exception if any issues occurred during deletion
        if len(ListIssues) > 0:
            raise Exception(ListIssues)

    def create_application_and_monitors(self, configs=None, extra_web_mtr=False, follow_web_monitor=True):
        """
        Creates applications and monitors in the ZDX Cloud.

        This method creates applications and monitors based on the provided configurations.
        If an application or monitor already exists, it will be skipped.

        Args:
            configs (list, optional): A list of configuration dictionaries for applications and monitors. Defaults to None.
            extra_web_mtr (bool, optional): Whether to create an extra web monitor. Defaults to False.

        Returns:
            None

        Raises:
            Exception: If the creation of an application or monitor fails.
        """
        # If no configurations are provided, use the default configurations from the config file
        if configs is None:
            configs = self.config['ZDX_APPLICATION']
        # Get a list of existing applications
        app_response = self.req.api_call("get", url=self.resource.applicationurl, headers=self.resource.zdx_headers)
        status, message, app_response = self.zdxhelper.translate_response(app_response)
        # Iterate over each configuration
        for config in configs:
            # Skip the "extra_for_update_policy" application if extra_web_mtr is True
            if extra_web_mtr and config['application']['name'].lower() != "extra_for_update_policy":
                continue

            # Initialize a dictionary to store the application and monitor IDs
            probesexist = {"appid": None, "name": "", "WEB": {}, "TCP": {}, "UDP": {}, "ICMP": {}}

            # Check if the application already exists
            applicationid = None
            for application in app_response:
                if application['name'].lower() == config['application']['name'].lower():
                    probesexist['appid'] = application['id']
                    probesexist['name'] = application['name']
                    self.logger.info(f"Application {application['name']} already exists")
                    applicationid = application['id']

                    break

            # If the application does not exist, create it

            if probesexist['appid'] is None:
                payload = copy.deepcopy(self.resource.createApppayload)
                for key in config['application'].keys():
                    payload[key] = config['application'][key]
                response = self.req.api_call("post", url=self.resource.applicationurl, data=json.dumps(payload),
                                             headers=self.resource.zdx_headers)
                status, message, response = self.zdxhelper.translate_response(response)

                self.logger.info(f"Application {config['application']['name']} created")
                probesexist['appid'] = response['id']
                probesexist['name'] = response['name']
                applicationid = response['id']

            # Get a list of existing monitors for the application
            response = self.req.api_call("get", url=self.resource.listallmonitorsurl, headers=self.resource.zdx_headers)
            status, message, response = self.zdxhelper.translate_response(response)

            # Iterate over each monitor and check if it already exists
            for probes in response:

                if probes['application']['id'] == probesexist['appid'] and probes['type'] == "WEB":
                    probesexist['WEB']['id'] = probes['id']
                    probesexist['WEB']['name'] = probes['name']
                    self.logger.warning(f"Web probe {probes['name']} already exists with id {probes['id']}")
                elif probes['application']['id'] == probesexist['appid'] and probes['type'] == "TRACERT" and probes[
                    'protocol'] == "TCP":
                    probesexist['TCP']['id'] = probes['id']
                    probesexist['TCP']['name'] = probes['name']
                    self.logger.warning(
                        f"Traceroute probe {probes['name']} with protocol {probes['protocol']} already exists with id {probes['id']}")
                elif probes['application']['id'] == probesexist['appid'] and probes['type'] == "TRACERT" and probes[
                    'protocol'] == "UDP":
                    probesexist['UDP']['id'] = probes['id']
                    probesexist['UDP']['name'] = probes['name']
                    self.logger.warning(
                        f"Traceroute probe {probes['name']} with protocol {probes['protocol']} already exists with id {probes['id']}")
                elif probes['application']['id'] == probesexist['appid'] and probes['type'] == "TRACERT" and probes[
                    'protocol'] == "ICMP":
                    probesexist['ICMP']['id'] = probes['id']
                    probesexist['ICMP']['name'] = probes['name']
                    self.logger.warning(
                        f"Traceroute probe {probes['name']} with protocol {probes['protocol']} already exists with id {probes['id']}")

            # Create a web monitor if it does not exist
            if not probesexist['WEB']:
                payload = copy.deepcopy(self.resource.webprobepayload)
                for key in config['web_probe'].keys():
                    payload[key] = config['web_probe'][key]
                response = self.req.api_call("post", url=self.resource.monitorurl.format(str(probesexist['appid'])),
                                             data=json.dumps(payload), headers=self.resource.zdx_headers)

                status, message, response = self.zdxhelper.translate_response(response)
                probesexist['WEB']['id'] = response['id']
                probesexist['WEB']['name'] = response['name']
                self.logger.info(f"Web probe {response['name']} created successfully with id {response['id']}")

            # Create a TCP traceroute monitor if it does not exist and is configured
            if not probesexist['TCP'] and config.get("tracert_tcp") is not None:
                payload = copy.deepcopy(self.resource.tracertpayload)
                if follow_web_monitor:
                    payload["followWebMonitor"] = probesexist['WEB']
                payload["tcpPort"] = 443
                payload["protocol"] = "TCP"
                for key in config["tracert_tcp"].keys():
                    payload[key] = config["tracert_tcp"][key]
                response = self.req.api_call("post", url=self.resource.monitorurl.format(str(probesexist['appid'])),
                                             data=json.dumps(payload), headers=self.resource.zdx_headers)
                status, message, response = self.zdxhelper.translate_response(response)
                probesexist['TCP']['id'] = response['id']
                probesexist['TCP']['name'] = response['name']
                self.logger.info(
                    f"Traceroute probe {response['name']} with protocol {response['protocol']} created successfully with id {response['id']}")

            # Create a UDP traceroute monitor if it does not exist and is configured
            if not probesexist['UDP'] and config.get("tracert_udp") is not None:
                payload = copy.deepcopy(self.resource.tracertpayload)
                if follow_web_monitor:
                    payload["followWebMonitor"] = probesexist['WEB']
                payload["udpPort"] = 33434
                payload["protocol"] = "UDP"
                for key in config["tracert_udp"].keys():
                    payload[key] = config["tracert_udp"][key]
                response = self.req.api_call("post", url=self.resource.monitorurl.format(str(probesexist['appid'])),
                                             data=json.dumps(payload), headers=self.resource.zdx_headers)
                status, message, response = self.zdxhelper.translate_response(response)
                probesexist['UDP']['id'] = response['id']
                probesexist['UDP']['name'] = response['name']
                self.logger.info(
                    f"Traceroute probe {response['name']} with protocol {response['protocol']} created successfully with id {response['id']}")

            # Create an ICMP traceroute monitor if it does not exist and is configured
            if not probesexist['ICMP'] and config.get('tracert_icmp') is not None:
                payload = copy.deepcopy(self.resource.tracertpayload)
                if follow_web_monitor:
                    payload["followWebMonitor"] = probesexist['WEB']
                payload["protocol"] = "ICMP"
                for key in config["tracert_icmp"].keys():
                    payload[key] = config["tracert_icmp"][key]
                response = self.req.api_call("post", url=self.resource.monitorurl.format(str(probesexist['appid'])),
                                             data=json.dumps(payload), headers=self.resource.zdx_headers)
                status, message, response = self.zdxhelper.translate_response(response)
                probesexist['ICMP']['id'] = response['id']
                probesexist['ICMP']['name'] = response['name']
                self.logger.info(
                    f"Traceroute probe {response['name']} with protocol {response['protocol']} created successfully with id {response['id']}")

            # Store the application and monitor IDs in the application data dictionary
            self.applicationdata[config['application']['name']] = probesexist
            self.logger.debug(self.applicationdata)

        # Activate the pending changes if there are any applications created
        if len(self.applicationdata) > 0:
            self.activate()

    def create_application_and_monitors_support_monitor_dropped(self, configs=None):
        """
        Creates applications and monitors in the ZDX Cloud based on the provided configuration.

        Args:
            configs (dict, optional): A dictionary containing the configuration for the applications and monitors.
                Defaults to None.

        Returns:
            tuple: A tuple containing a boolean indicating whether the operation was successful and a message describing the result.

        Raises:
            Exception: If the configuration is not provided or if there is an error creating the applications or monitors.
        """

        # Check if the configuration is provided
        if configs is None:
            return False, "Please provide support_monitor_dropped Config file"

        # Extract the application configuration from the provided configuration
        configs = configs['ZDX_APPLICATION']

        # Get the list of existing applications
        app_response = self.req.api_call("get", url=self.resource.applicationurl, headers=self.resource.zdx_headers)
        status, message, app_response = self.zdxhelper.translate_response(app_response)
        # Iterate over the application configurations
        for config in configs:
            # Initialize a dictionary to store the probe information
            probesexist = {"appid": None, "name": "", "WEB": {}, "TCP": {}, "UDP": {}, "ICMP": {}}

            # Check if the application already exists
            for application in app_response:
                if application['name'].lower() == config['application']['name'].lower():
                    probesexist['appid'] = application['id']
                    probesexist['name'] = application['name']
                    self.logger.info(f"Application {application['name']} already exist")
                    break

            # If the application does not exist, create it
            if probesexist['appid'] is None:
                payload = copy.deepcopy(self.resource.createApppayload)
                for key in config['application'].keys():
                    payload[key] = config['application'][key]
                response = self.req.api_call("post", url=self.resource.applicationurl, data=json.dumps(payload),
                                             headers=self.resource.zdx_headers)
                status, message, response = self.zdxhelper.translate_response(response)
                self.logger.info(f"Application {config['application']['name']} created")
                probesexist['appid'] = response['id']
                probesexist['name'] = response['name']

            # Get the list of existing monitors
            response = self.req.api_call("get", url=self.resource.listallmonitorsurl, headers=self.resource.zdx_headers)
            status, message, response = self.zdxhelper.translate_response(response)

            # Check if the web probes already exist
            for webprobe in config["web_probe"]:
                for probes in response:
                    if probes['application']['id'] == probesexist['appid'] and probes['type'] == "WEB" and probes[
                        'name'] == webprobe["name"]:
                        web_probe_name = webprobe["name"]
                        probesexist['WEB'][web_probe_name] = probes['id']
                        self.logger.warning(f"Web probe {probes['name']} already exist with id {probes['id']}")

            # Check if the traceroute ICMP probes already exist
            for trace_icmp in config["tracert_icmp"]:
                for probes in response:
                    if probes['application']['id'] == probesexist['appid'] and probes['type'] == "TRACERT" and probes[
                        'protocol'] == "ICMP" and probes['name'] == trace_icmp["name"]:
                        traceroute_name = trace_icmp["name"]
                        probesexist['ICMP'][traceroute_name] = probes['id']
                        self.logger.warning(
                            f"Traceroute probe {probes['name']} with protocol {probes['protocol']} already exist with id {probes['id']}")

            # Create the web probes that do not exist
            for webprobe in config["web_probe"]:
                name = webprobe['name']
                if not probesexist['WEB'].get(name):
                    payload = copy.deepcopy(self.resource.webprobepayload)
                    for key in webprobe.keys():
                        payload[key] = webprobe[key]
                    response = self.req.api_call("post", url=self.resource.monitorurl.format(str(probesexist['appid'])),
                                                 data=json.dumps(payload), headers=self.resource.zdx_headers)
                    status, message, response = self.zdxhelper.translate_response(response)
                    probesexist['WEB'][name] = response['id']
                    self.logger.info(f"Web probe {response['name']} created successfully with id {response['id']}")

            # Create the traceroute ICMP probes that do not exist
            for traceroute in config["tracert_icmp"]:
                name = traceroute['name']
                if not probesexist['ICMP'].get(name):
                    payload = copy.deepcopy(self.resource.tracertpayload)
                    payload["followWebMonitor"] = probesexist['WEB']
                    payload["protocol"] = "ICMP"
                    for key in traceroute.keys():
                        payload[key] = traceroute[key]
                    response = self.req.api_call("post", url=self.resource.monitorurl.format(str(probesexist['appid'])),
                                                 data=json.dumps(payload), headers=self.resource.zdx_headers)
                    status, message, response = self.zdxhelper.translate_response(response)
                    probesexist['ICMP'][name] = response['id']
                    self.logger.info(
                        f"Traceroute probe {response['name']} with protocol {response['protocol']} created successfully with id {response['id']}")

            # Store the probe information in the application data
            self.applicationdata[config['application']['name']] = probesexist
            self.logger.debug(self.applicationdata)

        # Activate the pending changes if there are any applications created
        if len(self.applicationdata) > 0:
            self.activate()

        return True, "Applications and monitors created successfully"

    def delete_application_and_monitors_support_monitor_dropped(self, configs=None):
        """
        Deletes an application and its associated monitors.

        This method takes a configuration dictionary as input, which contains information about the application to be deleted.
        It first checks if the configuration is provided, and if not, returns an error message.
        Then, it retrieves the application data from the server and checks if the application exists.
        If the application exists, it deletes its associated monitors (WEB, TCP, UDP, and ICMP) and then deletes the application itself.
        If any errors occur during the deletion process, it logs the error and continues with the next application.

        Args:
            configs (dict, optional): A dictionary containing the configuration for the application to be deleted. Defaults to None.

        Returns:
            tuple: A tuple containing a boolean indicating whether the deletion was successful and a string with an error message if applicable.

        Raises:
            Exception: If any errors occur during the deletion process.
        """

        # Check if the configuration is provided
        if configs is None:
            return False, "Please provide support_monitor_dropped Config file"

        # Extract the application configuration from the input dictionary
        configs = configs['ZDX_APPLICATION']

        # Retrieve the application data from the server
        app_response = self.req.api_call("get", url=self.resource.applicationurl, headers=self.resource.zdx_headers)
        status, message, app_response = self.zdxhelper.translate_response(app_response)

        # Initialize an empty list to store any issues that occur during the deletion process
        ListIssues = []
        app = None

        # Iterate over each application configuration
        for config in configs:
            # Check if the application exists in the application data
            if config['application']['name'] in self.applicationdata:
                # If the application exists, retrieve its data
                app = self.applicationdata[config['application']['name']]
            else:
                # If the application does not exist, create a new dictionary to store its data
                app = {"appid": None, "WEB": {}, "TCP": {}, "UDP": {}, "ICMP": {}}

                # Iterate over each application in the response to find the application ID
                for application in app_response:
                    if application['name'].lower() == config['application']['name'].lower():
                        # If the application is found, store its ID
                        app['appid'] = application['id']
                        break

                # If the application ID is not found, log a warning and continue with the next application
                if app['appid'] is None:
                    self.logger.warning(
                        f"Cannot delete application {config['application']['name']} as it does not exist")
                    continue

                # Retrieve the list of monitors for the application
                response = self.req.api_call("get", url=self.resource.listallmonitorsurl,
                                             headers=self.resource.zdx_headers)
                status, message, response = self.zdxhelper.translate_response(response)

                # Iterate over each WEB probe in the configuration
                for webprobe in config["web_probe"]:
                    # Extract the name of the WEB probe
                    name = webprobe['name']

                    # Iterate over each monitor in the response to find the WEB probe ID
                    for probes in response:
                        if probes['application']['id'] == app['appid'] and probes['type'] == "WEB" and probes['name'] == \
                                webprobe["name"]:
                            # If the WEB probe is found, store its ID
                            app['WEB'][name] = probes['id']

                # Iterate over each ICMP probe in the configuration
                for traceroute in config["tracert_icmp"]:
                    # Extract the name of the ICMP probe
                    name = traceroute['name']

                    # Iterate over each monitor in the response to find the ICMP probe ID
                    for probes in response:
                        if probes['application']['id'] == app['appid'] and probes['type'] == "TRACERT" and probes[
                            'protocol'] == "ICMP" and probes['name'] == traceroute["name"]:
                            # If the ICMP probe is found, store its ID
                            app['ICMP'][name] = probes['id']

            try:
                # Iterate over each ICMP probe in the application data and delete it
                for key in app['ICMP']:
                    # Extract the name and ID of the ICMP probe
                    name = key
                    id = app['ICMP'][key]

                    # Delete the ICMP probe
                    self.req.api_call("delete", url=self.resource.monitorurl.format(str(app['appid'])) + "/" + str(id),
                                      headers=self.resource.zdx_headers)
                    self.logger.info(f"Successfully deleted {name} ICMP monitor with id {id}")

                # Iterate over each WEB probe in the application data and delete it
                for key in app['WEB']:
                    # Extract the name and ID of the WEB probe
                    name = key
                    id = app['WEB'][key]

                    # Disable the WEB probe
                    payload = copy.deepcopy(self.resource.disablewebprobe)
                    payload['id'] = key
                    payload['name'] = name
                    payload['url'] = config['web_probe'][0]['url']
                    self.req.api_call("put", url=self.resource.monitorurl.format(str(app['appid'])) + "/" + str(id),
                                      data=json.dumps(payload), headers=self.resource.zdx_headers)
                    self.logger.info(f"Successfully disabled Web probe = {name}")

                    # Delete the WEB probe
                    self.req.api_call("delete", url=self.resource.monitorurl.format(str(app['appid'])) + "/" + str(id),
                                      headers=self.resource.zdx_headers)
                    self.logger.info(f"Successfully deleted Web probe = {name}")

                # Delete the application
                self.req.api_call("delete", url=self.resource.applicationurl + "/" + str(app['appid']),
                                  headers=self.resource.zdx_headers)
                self.logger.info(f"Successfully deleted application {config['application']['name']}")

                # Remove the application from the application data
                if config['application']['name'] in self.applicationdata:
                    self.applicationdata.pop(config['application']['name'])
            except Exception as E:
                # If any errors occur during the deletion process, log the error and add it to the list of issues
                ListIssues.append(E)
                self.logger.error(E)

        # Activate the changes
        self.activate()

        # If any issues occurred during the deletion process, raise an exception
        if len(ListIssues) > 0:
            raise Exception(ListIssues)

    def init_wait_DT(self):
        """
        Initializes the waiting period for ongoing DT sessions.

        This function continuously checks the status of ongoing DT sessions until they are completed or failed.
        It uses the REQ function to send GET requests to the DT URL with the is_completed and is_failed parameters.
        If a session is completed or failed, it is removed from the dtsessionsinfo dictionary.
        If any sessions fail, the function returns False along with the list of failed sessions.
        If all sessions are completed successfully, the function returns True.

        Args:
            None

        Returns:
            list: A list containing a boolean indicating whether all sessions were completed successfully,
                  None, and a list of failed sessions (if any).

        Raises:
            None
        """
        # Check if there are any active DT sessions
        if not self.dtsessionsinfo:
            self.logger.info("No active DT session exist")
            return

        self.logger.info("Initialising waiting period for ongoing DT Sessions")

        # Initialize a list to store issues with DT sessions
        ListIssues = []

        # Continuously check the status of DT sessions until they are completed or failed
        while True:
            # Send a GET request to check if any DT sessions are completed
            response = self.req.api_call("get", url=self.resource.dturl + self.resource.iscompletedparams,
                                         headers=self.resource.zdx_headers)
            status, message, response = self.zdxhelper.translate_response(response)

            # Check if any DT sessions are completed
            if len(response) > 0:
                for res in response:
                    # Check if the completed session is in the dtsessionsinfo dictionary
                    if res['id'] in self.dtsessionsinfo:
                        self.logger.info(f"DT - {self.dtsessionsinfo[res['id']]['name']} completed successfully")
                        # Remove the completed session from the dtsessionsinfo dictionary
                        self.dtsessionsinfo.pop(res['id'])

            # Send a GET request to check if any DT sessions have failed
            response = self.req.api_call("get", url=self.resource.dturl + self.resource.isfailedparams,
                                         headers=self.resource.zdx_headers)
            status, message, response = self.zdxhelper.translate_response(response)

            # Check if any DT sessions have failed
            if len(response) > 0:
                for res in response:
                    # Check if the failed session is in the dtsessionsinfo dictionary
                    if res['id'] in self.dtsessionsinfo:
                        self.logger.info(f"DT - {self.dtsessionsinfo[res['id']]['name']} failed")
                        # Add the failed session to the ListIssues list
                        ListIssues.append(f"DT - {self.dtsessionsinfo[res['id']]['name']} failed")
                        # Remove the failed session from the dtsessionsinfo dictionary
                        self.dtsessionsinfo.pop(res['id'])

            # Check if there are any issues with DT sessions
            if ListIssues:
                # Return False along with the list of failed sessions
                return [False, None, ListIssues]

            # Check if all DT sessions are completed
            if len(self.dtsessionsinfo) == 0:
                # Return True indicating that all sessions were completed successfully
                return [True, None, None]

            # Wait for 30 seconds before checking the status of DT sessions again
            time.sleep(30)

    def create_dt_session(self, application="Google", configs=None, durationMinutes=-1, device_probing=True):
        """
        Creates a DT session in the ZDX Cloud.

        This method creates a DT session based on the provided configurations.
        If an application or monitor already exists, it will be skipped.

        Args:
            application (str, optional): The name of the application. Defaults to "Google".
            configs (list, optional): A list of configuration dictionaries for applications and monitors. Defaults to None.
            durationMinutes (int, optional): The duration of the DT session in minutes. Defaults to -1.
            device_probing (bool, optional): Whether to enable device probing. Defaults to True.

        Returns:
            list: A list containing a boolean indicating success, None, and a list of issues if any.

        Raises:
            Exception: If the creation of a DT session fails.
        """
        # If configs is None, use the default configs from the ZDX_APPLICATION configuration
        if configs == None:
            configs = self.config['ZDX_APPLICATION']

        # Initialize a list to store issues
        ListIssues = []

        # Iterate over each config in the configs list
        for config in configs:
            # Check if the application name matches and DT is not None
            if config['application']['name'].lower() == application.lower() and not config.get("DT") == None:
                # Create a deep copy of the DT list
                DTList = copy.deepcopy(config['DT'])

                # Iterate over each DT in the DT list
                for DT in DTList:
                    # Initialize an empty payload dictionary
                    payload = {}

                    # Initialize an empty parameters list
                    parameters = []

                    # Check if DT_PROTOCOL is not None and BANDWIDTH_TEST is True
                    if not DT.get("DT_PROTOCOL") == None and (
                            not DT.get("BANDWIDTH_TEST") == None and DT["BANDWIDTH_TEST"]):
                        # Log an error and append an issue to the ListIssues list
                        self.logger.error(
                            f"DT session - {DT['name']} cannot be performed in parallel with Bandwidth testing")
                        ListIssues.append(
                            f"DT session - {DT['name']} cannot be performed in parallel with Bandwidth testing")
                        continue

                    # Check if DT_PROTOCOL is not None and the application name is not in the application data
                    if not DT.get("DT_PROTOCOL") == None and not config['application']['name'] in self.applicationdata:
                        # Create an application and monitors
                        self.create_application_and_monitors(configs=list().append(config))

                    # Check if DT_PROTOCOL is not None
                    if not DT.get("DT_PROTOCOL") == None:
                        # Create a deep copy of the dtapppayload
                        dtapppayload = copy.deepcopy(self.resource.dtapppayload)

                        # Get the application ID and name from the application data
                        temp = self.applicationdata[config['application']['name']]
                        dtapppayload['application']['id'] = temp['appid']
                        dtapppayload['application']['name'] = temp['name']

                        # Set deviceMonitoringEnabled to False if device_probing is False
                        if not device_probing:
                            dtapppayload['deviceMonitoringEnabled'] = "false"

                        # Copy WEB keys from the application data to the dtapppayload
                        for key in temp['WEB'].keys():
                            dtapppayload['monitors'][0][key] = temp['WEB'][key]

                        # Check if the DT_PROTOCOL exists in the application data
                        if temp.get(DT['DT_PROTOCOL']) == None or not temp[DT['DT_PROTOCOL']]:
                            # Log an error and append an issue to the ListIssues list
                            self.logger.error(
                                f"Cannot create a DT session as type of DT_PROTOCOL {DT['DT_PROTOCOL']}(probe) does not exist for application {temp['name']}")
                            ListIssues.append(
                                f"Cannot create a DT session as type of DT_PROTOCOL {DT['DT_PROTOCOL']}(probe) does not exist for application {temp['name']}")
                            continue

                        # Copy DT_PROTOCOL keys from the application data to the dtapppayload
                        for key in temp[DT['DT_PROTOCOL']].keys():
                            dtapppayload['monitors'][1][key] = temp[DT['DT_PROTOCOL']][key]

                        # Append the dtapppayload to the parameters list
                        parameters.append(dtapppayload)

                    # Check if BANDWIDTH_TEST is not None and is True
                    if not DT.get("BANDWIDTH_TEST") == None and DT["BANDWIDTH_TEST"]:
                        # Append a BANDWIDTH_TEST parameter to the parameters list
                        parameters.append({"type": "BANDWIDTH_TEST"})

                    # Check if PCAP is not None
                    if not DT.get("PCAP") == None:
                        # Create a deep copy of the dtpcappayload
                        dtpcappayload = copy.deepcopy(self.resource.dtpcappayload)

                        # Copy PCAP keys from the DT to the dtpcappayload
                        for key in DT['PCAP'].keys():
                            dtpcappayload[key] = DT['PCAP'][key]

                        # Append the dtpcappayload to the parameters list
                        parameters.append(dtpcappayload)

                    # Check if the parameters list is empty
                    if len(parameters) <= 0:
                        # Log an error
                        self.logger.error(
                            "No valid parameter input passed. Check if application/bandwidth/pcap input exist in configuration file")
                        continue

                    # Get the list of all users
                    response = self.list_all_users()

                    # Find the user with the matching email
                    for res in response:
                        if self.config['ZDX_USERNAME'] in res['email']:
                            # Set the user ID and name in the payload
                            payload['user'] = {"id": res['id'], 'name': res['nameWithEmail']}
                            break

                    # Check if the user is not found
                    if payload.get("user") == None:
                        # Log an error and append an issue to the ListIssues list
                        self.logger.error(
                            f"Could not find the user - {self.config['ZDX_USERNAME']} defined in config registered with zdx")
                        ListIssues.append(
                            f"Could not find the user - {self.config['ZDX_USERNAME']} defined in config registered with zdx")
                        continue

                    # Get the list of all devices for the user
                    response = self.list_all_devices(userid=str(payload['user']['id']))

                    # Check if the DT name is None or empty
                    if DT.get("name") == None or len(DT['name']) == 0:
                        # Set the DT name to a default value
                        DT[
                            'name'] = f"{DT['devicename'] if DT.get('devicename') else self.device_name + '-' + str(datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S'))}"

                        # Truncate the DT name if it exceeds 64 characters
                        if len(DT['name']) > 64:
                            DT['name'] = f"ZDX-Automation-{str(datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S'))}"

                    # Check if the devicename is None or empty
                    if DT.get("devicename") == None or len(DT['devicename']) == 0:
                        # Log a warning and set the devicename to the hostname
                        self.logger.warning(
                            f"Devicename does not exist for the DT session - {DT['name']}\nAutomation will provide hostname on which automation is running as devicename")
                        DT['devicename'] = self.device_name

                    # Find the device with the matching name
                    for res in response:
                        if DT['devicename'] in res['name']:
                            # Set the device in the payload
                            payload['device'] = res
                            break

                    # Check if the device is not found
                    if payload.get("device") is None:
                        # Log an error and append an issue to the ListIssues list
                        self.logger.error(f"Device {DT['devicename']} is not registered")
                        ListIssues.append(f"Device {DT['devicename']} is not registered")
                        continue

                    # Set deviceFieldCleared to False
                    payload["deviceFieldCleared"] = False

                    # Set the duration minutes in the payload
                    if durationMinutes == -1:
                        payload["durationMinutes"] = DT["durationMinutes"]
                        self.dtsessionduration = DT["durationMinutes"]
                    else:
                        payload["durationMinutes"] = durationMinutes
                        self.dtsessionduration = durationMinutes

                    # Set the name and parameters in the payload
                    payload['name'] = DT['name']
                    payload['parameters'] = parameters

                    try:
                        # Send a POST request to create a DT session
                        response = self.req.api_call("post", url=self.resource.dturl, data=json.dumps(payload),
                                                     headers=self.resource.zdx_headers)
                        status, message, response = self.zdxhelper.translate_response(response)

                        # Log a success message
                        self.logger.info(
                            f"Successfully Created DT session {response['name']} with id - {response['id']}")

                        # Store the DT session information
                        self.dtsessionsinfo[response['id']] = response
                    except Exception as E:
                        # Log an error and append an issue to the ListIssues list
                        self.logger.error(E)
                        ListIssues.append(E)

        # Check if there are any issues
        if ListIssues:
            # Return False, None, and the list of issues
            return [False, None, ListIssues]

        # Return True, None, and None
        return [True, None, None]

    def dt_session_info_from_ma(self) -> tuple[bool, str, int]:
        """
        Retrieves DT session information from the current user's session data.

        Returns:
            tuple[bool, str, int]: A tuple containing the status of the operation,
                a message describing the result, and the DT session ID.
        """

        # Initialize the status and message variables with default values.
        status = True
        msg = "DT session id is"
        session_id = 0

        # Retrieve the DT session information from the object's attribute.
        dt_session = self.dtsessionsinfo

        # Check if there are any active DT sessions for the current user.
        if len(dt_session) == 0:
            # If no sessions are found, return an error message and a session ID of 0.
            return False, "No active DT session is present of current user", 0

        # Iterate over the DT session dictionary to retrieve the first session ID.
        for key, val in dt_session.items():
            # Store the session ID and break out of the loop.
            session_id = key
            break

        # Return the status, message, and session ID.
        return status, msg, session_id

    def session_duration(self) -> int:
        """
        Retrieves the duration of the current session.
        Directly return the session duration from the 'dtsessionduration' attribute
        This attribute is assumed to be set elsewhere in the class

        Returns:
            int: The duration of the session in seconds.

        Generated by Devraj., assisted by ZCoder on 16-October-2024 <Please retain this marker>
        """

        return self.dtsessionduration

    def abort_dt_session(self) -> tuple[bool, str, int]:
        """
        Aborts the active DT session for the current user.

        This method retrieves the active DT session ID, constructs the abort URL, and sends a PUT request to abort the session.
        If the request is successful, it logs an info message; otherwise, it logs an error message and returns a failure status.

        Args:
            None

        Returns:
            tuple[bool, str, int]: A tuple containing the status of the operation (True for success, False for failure),
                a message describing the outcome, and the ID of the aborted DT session.

        Raises:
            None
        """
        status = True  # Initialize the status flag to True
        msg = ""  # Initialize the message string
        session_id = 0  # Initialize the session ID to 0

        # Retrieve the DT session information
        dt_session = self.dtsessionsinfo

        # Check if there are any active DT sessions
        if len(dt_session) == 0:
            # If no sessions are found, return a failure status with a corresponding message
            return False, "No active DT session is present of current user", 0

        # Iterate over the DT session dictionary to retrieve the session ID
        for key, val in dt_session.items():
            # Extract the session ID from the dictionary
            session_id = key
            # Break the loop as we only need the first session ID
            break

        # Construct the abort URL using the session ID
        abort_url = self.resource.dturl + f"/{session_id}/stop"

        try:
            # Send a PUT request to abort the DT session
            response = self.req.api_call("put", url=abort_url, headers=self.resource.zdx_headers)
            status, message, response = self.zdxhelper.translate_response(response)
            # Log an info message if the request is successful
            self.logger.info(f"Successfully Aborted DT session {self.dtsessionsinfo[key]} with id - {key}")
        except Exception as E:
            # Log an error message if an exception occurs
            self.logger.error(E)
            # Set the status flag to False
            status = False
            # Update the message string to indicate failure
            msg = "Unable to abort dt session"

        # Return the status, message, and session ID
        return status, msg, session_id


if __name__ == "__main__":
    a = ZDX_ADMIN(cloudname="zdxbeta", configfile="config.json")
    # a.REQ(reqtype="get", url=a.resource.authurl)
    # a.create_application_and_monitors()
    # time.sleep(30)
    # a.delete_application_and_monitors()
    # a.create_dt_session()
    # a.init_wait_DT()
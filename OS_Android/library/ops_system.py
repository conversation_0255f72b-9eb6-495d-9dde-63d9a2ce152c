################################################################
#                       ops_System.Py                          #
#                                                              #
#                   AUTHOR : SOURAV MAGOTRA                    #
#                                                              #
#                                                              #
################################################################
import subprocess
import time
import os
import re
from typing import Any

import pytz
from common_lib.common.constants import Utils
from common_lib.common.logger import Logger
from datetime import datetime,timedelta


class SysOps:
    """
    This class will perform operations on Android system

    Methods:

    1. reboot_device(): For rebooting of device
    2. zcc_package_name(): For finding zcc package name
    3. get_android_version(): For finding android version of device
    4. toggle_wifi(): For toggling wifi
    5. toggle_mobile_data(): For toggling mobile data
    6. open_wireless_settings(): For opening wireless settings
    7. adb_commands(): For adb shell commands
    8. screen_lock(): For lock/unlock screen

    """
    def __init__(self,log_handle=None,cloud=None):
        self.logger = (log_handle if log_handle else Logger.initialize_logger("log_ops.log", log_level="INFO"))
        self.const = Utils()
        self.cloud = cloud

    def reboot_device(self):
        """
    This function will reboot an Android device and check if it's connected after reboot.
    
    Workflow:
    1. adb_commands("adb reboot") -> will reboot the device.
    2. Wait for 60 seconds to allow the device to reboot.
    3. adb_commands("adb devices") -> will find connected devices.
    4. Check if the device is listed as connected in the output.
    5. If the device is connected, return True and a success message,none. Otherwise, return False and a failure message, none
    """
        self.adb_commands("adb reboot")
        time.sleep(60)
        output=self.adb_commands("adb devices")
        output=output[2].decode('utf-8').splitlines()
        flag=(False,"Device reboot not success",None)
        while  not flag[0]:
            if any("device" in connected_device for connected_device in output):
                self.logger.info("ADB connected")
                flag=(True,"Device reboot success",None)
                break
            else:
                self.adb_commands("adb devices")
        return flag

    def zcc_package_name(self):
        """
    This function checks for the presence of Zscaler ZCC (Zscaler Client Connector) on an Android or ChromeOS device.
    It returns a tuple with a boolean value and a string representing the package name:
    (True,andr_zcc) if zscaler.com.zscaler is found (indicating ZCC is present on an Android device).
    (True,chromeos_zcc) if zscaler.com.zschromeosapp is found (indicating ZCC is present on a ChromeOS device).
    (False,"") if no package is found.
    The function first runs an ADB (Android Debug Bridge) command to list all installed packages on the device.
    It then checks if either 'zscaler.com.zscaler' or 'zscaler.com.zschromeosapp' is present in the list of installed packages.
    If either package is found, the function returns True and the corresponding package name,message. If neither package is found, it returns False,msg,None
    """
        status=(False,"Zcc package not found",None)
        package_name=self.adb_commands("adb shell pm list packages")
        package_name=package_name[2].decode('utf-8').split()
        if any('zscaler.com.zscaler'in package for package in package_name):
            self.logger.info("Android ZCC is present")
            andr_zcc="zscaler.com.zscaler"
            status=(True,andr_zcc,"Android package is present")
        else:
            if any('zscaler.com.zschromeosapp' in package for package in package_name):
                self.logger.info("Chrome ZCC is present")
                chromeos_zcc="zscaler.com.zschromeosapp"
                status=(True,chromeos_zcc,"Chromeos package is present")
        return status

    def get_android_version(self):
        """
    This function will return android version as a list of strings.
    The function uses the Android Debug Bridge (adb) command to fetch the version 
    information of the Android operating system from the device. The output is then 
    decoded into a list of lines.
    :return: True,Android version,None.
    """
        output=self.adb_commands("adb shell getprop ro.build.version.release")
        output=output[2].decode('utf-8').splitlines()
        print(output)
        return True, output
    
    def toggle_wifi(self,
                    enable:bool=False, #value should be True to enable
                    disable:bool=False, # value should be True to disabe
                    sleeptime=4
                    ):
        """
        This function will toggle wifi on android device

        Args:
           a) enable (bool): value should be True to enable wifi.
            returns-> True,success,none if Wifi is enabled successfully

            b) disable(bool): value should be True to disable wifi.
            return True,success,none if Wifi disabled successfully

        """
        status=(False,"Wifi toggling operation failed",None)
        if enable:
            self.adb_commands("adb shell svc wifi enable")
            time.sleep(sleeptime)
            output=self.adb_commands("adb shell dumpsys wifi | findstr Wi-Fi")
            output=output[2].decode('utf-8').splitlines()
            print(output)

            if any("Wi-Fi is enabled" in line for line in output):
                self.logger.info("Device is connected to wifi")
                status=(True,"Success",None)
        if disable:

            self.adb_commands("adb shell svc wifi disable")
            time.sleep(sleeptime)
            output=self.adb_commands("adb shell dumpsys wifi | findstr Wi-Fi")
            output=output[2].decode('utf-8').splitlines()
            if any("Wi-Fi is disabled" in line for line in output):
                self.logger.info("Device is not connected to wifi")
                status=(True,"Success",None)
        return status

    def toggle_mobile_data(self,
                           enable:bool=False,
                           disable:bool=False):
        """
        This function will toggle mobile data on android device

        Args:
           a) enable (bool): value should be True to enable mobiledata.
            returns True,"Success",None if mobiledata is enabled successfully

            b) disable(bool): value should be True to disable mobiledata.
            return True,"Success",None if mobiledata disabled successfully

        """
        status=(False,"Mobile data toggling failed",None)
        if enable:
            self.adb_commands("adb shell settings put global mobile_data 1")
            time.sleep(3)
            mobile_data_value=self.adb_commands("adb shell settings get global mobile_data")
            mobile_data_value=mobile_data_value[2].decode('utf-8').split()
            if all("1" in value for value in mobile_data_value):
                self.logger.info("Mobile data is connected")
                status=(True,"Success",None)
        if disable:
            self.adb_commands("adb shell settings put global mobile_data 0")
            time.sleep(3)
            mobile_data_value=self.adb_commands("adb shell settings get global mobile_data")
            mobile_data_value=mobile_data_value[2].decode('utf-8').split()
            if all("0" in value for value in mobile_data_value):
                self.logger.info("Mobile data is not connected")
                status=(True,"Success",None)
        return status

    def open_wireless_setting(self):
        """
        This function will open wireless settings in Samsung android device

        """
        status=(False,"Failure::Setting not opened",None)
        shell_output=self.adb_commands("adb shell am start -a android.settings.WIRELESS_SETTINGS")
        shell_output=shell_output[2].decode('utf-8').split()
        if any('Starting' in string for string in shell_output):
            self.logger.info("Success::Settings opened")
            status = True
        return status, "Success"

    def adb_commands(self, cmd):
        """
        This function is used to execute Android Debug Bridge (ADB) shell commands on an Android device. It takes a single argument, which is the ADB shell command to be executed as a string. The function utilizes the subprocess module to run the command, capturing any output or error messages generated by the command.

        Args:
            cmd (str): The ADB shell command to be executed.

        Returns:
             tuple-> True,output,None
        """
        out = subprocess.Popen(cmd,shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=subprocess.PIPE)
        output=out.communicate()[0]
        return True, "adb command pushed to android device", output

    def screen_lock(self,lock=False):
        """
        This function will lock or unlock screen.

        Args:
            lock: value should be True, if screen to lock
            unlock:value should be True, if screen to unlock

        Returns:
            status: A tuple containing a boolean,a string,none. The boolean indicates whether the operation was successful or not, and the string provides additional information about the operation and none
        """
        status=(False,"Screen operation failed",None)
        if lock:
            self.adb_commands("adb shell input keyevent 26")
            screen_status=self.adb_commands("adb shell dumpsys window policy|findStr screenState")
            screen_status=screen_status[2].decode('utf-8').strip().split('=')
            if any("SCREEN_STATE_OFF" in screen_state for screen_state in screen_status):
                self.logger.info("Screen Locked")
                status=(True,"Success",None)

        else:
            self.adb_commands("adb shell input keyevent 82")
            time.sleep(3)
            screen_status=self.adb_commands("adb shell dumpsys window policy|findStr screenState")
            screen_status=screen_status[2].decode('utf-8').strip().split('=')
            if any("SCREEN_STATE_ON" in screen_state for screen_state in screen_status):
                self.logger.info("Screen UnLocked")
                status=(True,"Success",None)
        return status

    def os_detection(self):
        """
        This function will detect the OS type i.e. Android or ChromeOS.

        Returns:
            tuple -> True, OS Type, None
        """

        cmd = "adb devices"
        os_type = ""
        out = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=subprocess.PIPE).communicate()[0].decode('utf-8').split()
        ip_port_pattern = r"^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):\d{1,5}$"
        if re.match(ip_port_pattern, out[4]):
            os_type = "chromeos"
        else:
            os_type = "android"

        return (True, os_type, None)

    def run_adb_command(self, command: str):
        """
                This function will run adb command and also gives error if any
        """
        result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        self.logger.info(f" ----------- Running Adb command {command} ------------")

        if result.returncode != 0:
            error = result.stderr.strip()
            self.logger.info(f"Error: {error}")
            return False, error

        self.logger.info(f"Command Successful")

        return True, ""

    def check_is_screen_on(self):
        """
        This function will check if device screen is on or not
        """
        command = "adb shell dumpsys power"
        result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        result = result.stdout.strip()
        self.logger.info(f" ----------- Checking if device screen is ON or OFF ------------")

        if "Display Power: state=ON" in result or "mWakefulness=Awake" in result or "mScreenOn=true" in result:
            self.logger.info(f"Screen is ON")
            return True, "Screen is ON"

        self.logger.info(f"Screen is OFF")
        return False, "Screen os OFF"

    def toggle_screen_on_off(self, screen: str = None, pin_or_password: str = None) -> tuple[bool, str]:
        """
        This function is used to lock and unlock device screen.

        :param screen: To turn screen 'ON' or 'OFF'
        :param pin_or_password: To use configured pin or password
        :type screen: str

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Devraj
        """

        status = False
        output = ""

        if screen == 'OFF':
            is_screen_on = self.check_is_screen_on()
            if is_screen_on[0]:
                turn_off_cmd = "adb shell input keyevent KEYCODE_POWER"
                result = self.run_adb_command(turn_off_cmd)
                if not result[0]:
                    return False, result[1]
                self.logger.info("Screen Turned OFF successfully")
                return True, "Screen Turned OFF successfully"
            else:
                self.logger.info("Device screen is already turned off")
                return True, "Device screen is already turned off"
        else:
            # Wake up the device
            cmd_wakeup = "adb shell input keyevent KEYCODE_WAKEUP"
            result = self.run_adb_command(cmd_wakeup)
            if not result[0]:
                return False, result[1]

            # Attempt a swipe to unlock if no PIN/Password
            cmd_unlock = "adb shell input keyevent KEYCODE_MENU"
            result = self.run_adb_command(cmd_unlock)
            if not result[0]:
                return False, result[1]

            time.sleep(1)

            if pin_or_password:
                # Enter Pin or password to unlock
                cmd_to_enter_pin = f"adb shell input text {pin_or_password}"
                result = self.run_adb_command(cmd_to_enter_pin)
                if not result[0]:
                    return False, result[1]

                # Confirm by pressing enter
                result = self.run_adb_command("adb shell input keyevent KEYCODE_ENTER")
                if not result[0]:
                    return False, result[1]
                self.logger.info("Device unlocked with PIN/password")
                return True, "Device unlocked with PIN/password"
            else:
                self.logger.info("Device unlocked with Swipe")
                return True, "Device unlocked with Swipe"


    def and_os_version(self) -> int:
        """
        This method returns the process ID (PID) of the Zscaler service.
        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """
        cmd = 'adb shell getprop ro.build.version.release'
        output = self.adb_commands(cmd)
        return output[2].decode().split()[0]


    def active_ipv4(self,
                    active_interface: str):
        """
        This method is fetch interface ipv6 address and retrun the value

        """

        cmd = f'adb shell ifconfig {active_interface}'
        output = subprocess.run(cmd, capture_output=True, text=True)
        lines = output.stdout.split('\n')
        new_lines = []
        for i in lines:
            i = i.strip()
            obj = re.findall('inet addr:(\d+.\d+.\d+.\d+)', i)
            if obj:
                break
        if len(obj) == 0:
            return ""
        return obj[0]

    def active_ipv6(self,
                    active_interface: str):
        """
        This method is fetch interface ipv6 address and retrun the value

        """

        cmd = f'adb shell ifconfig {active_interface}'
        output = subprocess.run(cmd, capture_output=True, text=True)
        lines = output.stdout.split('\n')
        new_lines = []
        for i in lines:
            i = i.strip()
            obj = re.findall('inet6 addr\: (\w+.*)\/', i)
            if obj:
                break
        if len(obj) == 0:
            return ""
        return obj[0]

    def list_of_active_ipv6(self,
                    active_interface: str):
        """
        This method is to fetch list interface ipv6 address and retrun the value

        """

        cmd = f'adb shell ip -6 addr show dev {active_interface}'
        output = subprocess.run(cmd, capture_output=True, text=True)
        lines = output.stdout.splitlines()
        active_ipv6_address = []
        for line in lines:
            match = re.search(r"inet6 ([a-fA-F0-9:]+)", line)
            if match:
                ipv6_address = match.group(1)
                # exclude link-local address (starting with "fe80")
                if not ipv6_address.startswith("fe80"):
                    active_ipv6_address.append(ipv6_address)

        return active_ipv6_address

    def get_no_cpu_cores(self):
        """
        This method returns the number of CPU cores on the device.
        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """
        cmd = 'adb shell ls /sys/devices/system/cpu/'
        output = self.adb_commands(cmd)
        result = re.findall('cpu\d', output[2].decode())
        return len(result)

    def get_all_frequencies(self):
        """
        This method returns a list of frequencies of all CPU cores on the device.
        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """
        cores = self.get_no_cpu_cores()
        frequency = []
        for index in range(cores):
            cmd = f"adb shell cat /sys/devices/system/cpu/cpu{index}//cpufreq/cpuinfo_max_freq"
            output = self.adb_commands(cmd)
            value = output[2].decode()
            value = value.strip()

            frequency.append(int(value))
        return frequency

    def get_max_freq(self):
        """
            This method returns the maximum frequency of all CPU cores on the device.
            Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
            """
        frequencies = self.get_all_frequencies()
        return max(frequencies) // 1000

    def get_avg_freq(self):
        """
            This method returns the average frequency of all CPU cores on the device.
            Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
            """
        from functools import reduce
        import math
        frequencies = self.get_all_frequencies()
        avg_frequency = reduce(lambda a, b: a + b, frequencies) / len(frequencies)

        return math.trunc(avg_frequency // 1000)

    def get_pid(self) -> int:
        """
            This method returns the process ID (PID) of the Zscaler service.
            Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
            """
        cmd = 'adb shell top -n 1 -b | findStr zscaler.com'
        output = self.adb_commands(cmd)
        pid = output[2].decode().split()[0]
        return int(pid)

    def get_zcc_version(self):
        """
        This function is used to fetch the ZCC version installed on an Android device.

        :return: The ZCC version.
        :rtype: str

        Generated by Dhruv Bansal, assisted by Zcoder on 24-April-2024 <Please retain this marker>
        """
        os_type = self.os_detection()[1]
        self.chromeos = True if os_type.lower() == "chromeos" else False
        os.environ["Andr_Platform"] = "ChromeOS" if self.chromeos else "Android"

        package_name = 'zscaler.com.zschromeosapp' if self.chromeos else 'zscaler.com.zscaler'
        cmd = f'adb shell dumpsys package {package_name} | findStr versionName'
        output = self.adb_commands(cmd)

        return output[0], output[1], (output[2].strip()).decode()

    def get_battery_percentage(self):
        """
        This method returns the battery percenatge of the Android device
        Generated by Devraj, assisted by Zcoder on 24-April-2024 <Please retain this marker>

        Args:
            self (object): Instance of the test class.

        Returns:
            Battery perct of the device
        """
        status = (False, "Failed to get the Battery details using adb command", 0)
        cmd = 'adb shell dumpsys battery'
        output = self.adb_commands(cmd)
        time.sleep(2)

        output = output[2].decode('utf-8').splitlines()

        for line in output:
            if "level" in line:
                status = (True, "Battery fetch success", int(line.split(": ")[1]))
                break

        return status



    def check_connection(self, connection: str) -> tuple[bool, str]:
        """
        This function is used to check current connection type
        Wi-fi, Mobile data or No active connection

        :return: A tuple containing a boolean status and a message string.
        :rtype: (bool, str)

        Generated by Devraj
        """

        try:
            # Check if Wi-Fi is enabled or not
            wifi_status = subprocess.check_output("adb shell settings get global wifi_on",
                                                  universal_newlines=True).strip()
            wifi_network_status = subprocess.check_output("adb shell dumpsys wifi | grep 'Wi-Fi is'",
                                                  universal_newlines=True).strip()
            mobile_data_status = subprocess.check_output("adb shell settings get global mobile_data",
                                                  universal_newlines=True).strip()

            if connection == "WIFI" and wifi_status == '1' and 'enabled' in wifi_network_status.lower():
                output = "WIFI is enabled"
                return True, output
            elif connection == "WIFI":
                output = "WIFI is not enabled"
                return False, output
            elif connection == "MOBILE_DATA" and mobile_data_status == '1':
                output = "MOBILE_DATA is connected"
                status = True
                return status, output
            elif connection == "MOBILE_DATA":
                output = "MOBILE_DATA is not connected"
                status = False
                return status, output
            else:
                output = "NO_ACTIVE_NETWORK"
                status = False
        except subprocess.CalledProcessError as e:
            status = False
            output = f"Error retrieving network Status: {e}"

        return status, output

    def get_android_time_zone_name(self, timezone_id) -> tuple[bool, str]:
        """
        This function is used to Fetch Android time zone name and return it

        :return: A tuple containing a boolean status and a message string conatins timezone.
        :rtype: (bool, str)

        Generated by Devraj

        """

        timezone_name_map = {
            "IST": "India Standard Time"
            # Add more abbreviations if needed
        }

        try:
            timezone = pytz.timezone(timezone_id)
            current_time = datetime.now(timezone)
            timezone_abbreviation = current_time.strftime('%Z')
            timezone_name = timezone_name_map.get(timezone_abbreviation,timezone_abbreviation)
            return True, timezone_name
        except pytz.UnknownTimeZoneError:
            return False, "Unknow Time Zone ID"

    def get_android_time_zone_info(self) -> tuple[bool, str]:
        """
        This function is used to Fetch Android time zone and return it

        :return: A tuple containing a boolean status and a message string conatins timezone.
        :rtype: (bool, str)

        Generated by Devraj
        """

        timezone_command = "adb shell getprop persist.sys.timezone"
        utc_offset_cmd = "adb shell date +%z"

        timezone_id = subprocess.run(timezone_command, stdout=subprocess.PIPE,text=True).stdout.strip()
        utc_offset_str = subprocess.run(utc_offset_cmd, stdout=subprocess.PIPE,text=True).stdout.strip()

        # Format UTC offset (e.g., from +0530 to +5:30)
        hours_offset = int(utc_offset_str[:3])
        minutes_offset = int(utc_offset_str[0] + utc_offset_str[3:])
        utc_offset_formatted = f"UTC{hours_offset:+}:{abs(minutes_offset):02}"

        time_zone_name = self.get_android_time_zone_name(timezone_id)
        if not time_zone_name[0]:
            return False, time_zone_name[1]

        time_zone_info = f"{utc_offset_formatted} {time_zone_name[1]} ({timezone_id})"
        return True, time_zone_info


    def android_device_type(self):
        """
        This method to verify devices connected to window client
        return: device_type Android/ChromeOS

        """
        device_type = "Android"
        cmd = "adb devices"
        output = self.adb_commands(cmd)
        if ":5555" in output[2].decode():
            device_type = "ChromeOS"
        return device_type


    def get_last_bootup_time(self) -> tuple[bool, str]:
        """
        This function is used to Fetch last bootup time of device using adb command adb shell "cat /proc/stat |grep btime"

        :return: A tuple containing a boolean status (True if successful, False otherwise) and the bootyp time
        :rtype: (bool, str)

        Example:

        (True, 'version')
        """

        status = False
        msg = "Bootup time fetch failed from device"
        cmd = 'adb shell "cat /proc/stat |grep btime"'

        try:
            result = subprocess.run(cmd, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
            output = result.stdout.decode('utf-8').strip()

            if result.returncode!=0 or not output:
                msg = "Failed to retrive btime value from device"
                return False,msg

            parts = output.split()
            if len(parts)<2 or parts[0]!= 'btime':
                msg = "Invalid output format while fetching device bootup time"
                return False, msg

            btime_value = parts[1]
            return True, btime_value
        except Exception as e:
            return False, f"Error occured while fetching device bootup time {e}"


    def get_device_name(self):
        """
        This method return device name
        return: device_name Android/ChromeOS

        """

        cmd = 'adb shell getprop ro.product.model'
        output = self.adb_commands(cmd)
        device_name = output[2].decode().split()[0]
        return device_name



    def validate_bypass_ips(self, bypass_list):
        """
        Retrieves a list of IP addresses pushed to ZCC for bypass.

        This function executes an ADB command to retrieve the IP routing table and
        then parses the output to extract IP addresses.

        Returns:
            list: A list of IP addresses pushed to ZCC for bypass.
        """
        msg = 'list of ips are by passed'
        status = True
        try:
            # Execute ADB command to retrieve IP routing table
            cmd = 'adb shell /system/bin/ip route show table 0'
            output = self.adb_commands(cmd)
            data = output[2].decode()
            # Extract IP addresses from the output using regular expression
            ip_list = re.findall('throw (\d+\.\d+\.\d+\.\d+)', string=data)
            self.logger.info({f"List of ip bypassed found in adb {ip_list}"})

            for ip in bypass_list:
                if ip not in ip_list:
                    status = False
                    msg = f"{ip} not in bypassed route"
            return status, msg
        except Exception as e:
            # Handle any exceptions that occur during the execution of the function
            print(f"An error occurred: {str(e)}")
            return []

    def no_ip_bypass(self):
        """
        Checks if any IP addresses are pushed to ZCC for bypass.

        This function executes an ADB command to retrieve the IP routing table and
        then parses the output to extract IP addresses. If no IP addresses are found,
        it returns True with a corresponding message.

        Returns:
            tuple: A boolean status and a message indicating whether any IP addresses
                are pushed to ZCC for bypass.
        """
        status = False
        msg = ''
        # Execute ADB command to retrieve IP routing table
        cmd = 'adb shell /system/bin/ip route show table 0'
        output = self.adb_commands(cmd)
        data = output[2].decode()
        # Extract IP addresses from the output using regular expression
        ip_list = re.findall('throw (\d+\.\d+\.\d+\.\d+)', string=data)
        if not ip_list:
            status = True
            msg = "No IPs are pushed to ZCC for bypass"

        return status, msg

    def zapp_screenshot(self, name):
        """
        This method is to take screen shot of zapp


        """
        status = False
        msg = "Screen shot not successful"

        cmd = f'adb shell screencap /sdcard/Download/{name}'
        output = self.adb_commands(cmd)
        cmd =  f'adb shell ls -alt /sdcard/Download/{name}'
        output = self.adb_commands(cmd)
        if name in output[2].decode():
            status = True
            msg = "screen shot captured successfuly"


        return status, msg

    def copy_screen_shot(self, name, destination_path):
        """

        copying screen shot
        """
        status = False
        msg = 'not coppied succesfull'
        cmd = f"adb pull /sdcard/Download/{name} {destination_path}"
        out = subprocess.run(cmd, capture_output=True, text=True)
        if " 1 file pulled" in out.stdout or " 1 file pulled" in out.stderr:
            self.logger.info("Copying of zip file is successful")
            status = True
            msg = "Copying of screenshot file is successful"

        else:
            self.logger.info("Copying of zip file is not successful")
            msg = "Copying of zip file is not successful"
            status = False
        return status, msg

    def get_patch_detail(self):
        """
        This method is to return patch number for android device
        return: string of patch version

        """
        status = False
        msg = "Enable to fetch details of patch"
        cmd = "adb shell getprop ro.vendor.build.security_patch"
        out = subprocess.run(cmd, capture_output=True, text=True)
        output = out.stdout.strip()
        match_string = re.findall('\d{4}\-\d{2}\-\d{2}', output)
        if match_string[0]:
            status = True
            msg = "Patch details successfully fetched"
    
        return match_string[0]

    def get_network_info(self,interface_name:str) -> tuple[bool, str, None] | tuple[bool, str, dict[str, str | list[Any] | Any]]:
        """
        Retrieves network information from an Android device.
    
        This function uses the Android Debug Bridge (ADB) to execute shell commands
        and retrieve network information. It returns a dictionary containing the
        interface name, link addresses, DNS addresses, SSID, BSSID, MAC address, and IP address.
    
        Returns:
            A dictionary containing network information, or an error message if the
            command fails.
    
        Raises:
            subprocess.CalledProcessError: If the ADB command fails.
        """

        # Execute the command and capture the output
        network_info_cmd = f"dumpsys connectivity | grep -A 1 '{interface_name}'"
        gateway = []
        try:
            # Execute the command and capture the output
            network_info_result = subprocess.run(["adb", "shell", network_info_cmd], capture_output=True, text=True,
                                                 check=True)

            network_info = network_info_result.stdout.strip()

            # Parse the network information output
            pattern = re.compile(
                r'InterfaceName: (\S+).*?'  # Matches the InterfaceName
                r'LinkAddresses: \[(.*?)\].*?'  # Matches all LinkAddresses inside square brackets
                r'DnsAddresses: \[(.*?)\].*?'  # Matches all DnsAddresses inside square brackets
                r'TransportInfo: <SSID: "(.*?)", BSSID: (.*?), MAC: (.*?), IP: (.*?), Security type',
                # Captures TransportInfo details
                re.DOTALL  # Allows "." to include newline characters
            )

            # Search using the regex
            match = pattern.search(network_info)

            # Step 1: Parse gateways (IPv4 and IPv6)
            # Gateway can match either IPv4 (e.g., ***********) or IPv6 (e.g., fe80::4424)
            ipv4_gateway_match = re.search(r'ServerAddress:\s*/(\d+\.\d+\.\d+\.\d+)', network_info)
            ipv6_gateway_match = re.search(r'ServerAddress:\s*/([a-fA-F0-9:]+)', network_info)
            if ipv4_gateway_match:
                gateway.append(ipv4_gateway_match.group(1))
            if ipv6_gateway_match:
                gateway.append(ipv6_gateway_match.group(1))


            if match:
                # Extract and clean up values
                data_dict = {
                    "InterfaceName": match.group(1).strip(),
                    "LinkAddresses": [addr.lstrip('\\').split('/')[0].strip() for addr in match.group(2).strip().split(',') if addr],
                    "DnsAddresses": [addr.lstrip('/').strip() for addr in match.group(3).strip().split(',')],
                    "SSID": match.group(4).strip(),
                    "BSSID": match.group(5).strip(),
                    "MAC": match.group(6).strip(),
                    "IP": match.group(7).lstrip('/').split('/')[0].strip(),  # Cleaned IP
                    "gateway": gateway,
                }

                return True, "Data Found", data_dict
            else:
                print("No match found.")
                return False, "No match found.", None

        except subprocess.CalledProcessError as e:
            # Return an error message if the command fails
            return False, f"Error: {e.stderr}", None

    def get_active_interface_details(self) -> tuple[bool, str]:
        """
                Retrieves active interface information from an Android device.

                This function uses the Android Debug Bridge (ADB) to execute shell commands

                Returns:
                    A tuple containing active interface information, or an error message if the

                Raises:
                    Exception: If the ADB command fails.
                """
        msg = ""
        status = True
        try:
            # Step 1: Get active interface name
            ip_output = subprocess.check_output(['adb', 'shell', 'ip', 'route'], encoding='utf-8')
            active_interface = None
            for line in ip_output.splitlines():
                if 'default' in line:
                    # Extract interface from 'dev <interface>'
                    parts = line.split()
                    active_interface = parts[parts.index('dev') + 1]
                    break

            if not active_interface:
                return False, "No active interface found using adb"  # No active interface found

            return True, active_interface

        except Exception as e:
            print(f"Error: {e}")
            return False, f"Error: {e}"

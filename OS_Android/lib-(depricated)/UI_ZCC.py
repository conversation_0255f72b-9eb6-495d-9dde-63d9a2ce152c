from common_lib.UI_Common import *
import os,sys
import copy
import time
from datetime import datetime
from functools import wraps
import pyautogui
import allure
from common_lib.Constants import *
from common_lib.OPS_ZPA import *
from OS_Android.lib.Fetch_Android_Logs import *
from selenium.webdriver.support.ui import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC

class android_elements:
    APP="zscaler.com.zscaler"
    USERNAME = "//*[@class='android.widget.EditText' and @text='Username']"
    PASSWORD = "//*[@class='android.widget.EditText' and @text='Password']"
    LOGIN_BUTTON = "//*[@class='android.widget.Button' and @text='Login']"
    ACCEPT_BUTTON = "acceptButton"
    INTERNET_UNREACHABLE_ERROR="//*[@text='Internet unreachable error']"
    SIM_CARD_MANAGER="//*[@text='SIM card manager']"
    SIM_2="//*[@text='SIM 2']"
    UPDATE_POLICY_TOAST="//*[@text='Policy update success']"
    MOBILE_DATA="//*[@text='Mobile data']"
    NETWORK_ERROR="//*[@text='Network Error']"
    AUTHENTICATE="//*[@text='AUTHENTICATE']"
    AUTHENTICATED="//*[@text='Authenticated']"
    CONNECTED="//*[@text='Connected']"
    PROGRESS_TEXT_LOGGING_IN = "progress_text"
    PROGRESS_ICON = "progress_icon"
    ALERT_TITLE_ALLOW_CONNECTION = "alertTitle"
    ALERT_BUTTON_CANCEL = "button2"
    ALERT_BUTTON_OK = "button1"
    STATUS_TEXT = "statusText"
    LOGOUT_PROMPT = "//*[@class='android.widget.TextView' and @text='LOGOUT']"
    STATUS_DETAIL = "statusDetail"
    STATUS_CONTROL_BUTTON = "statusControl"
    STATUS_CONTROL_BUTTON_TEXT = "statusControlText"
    USERNAME_TEXT = "usernameString"
    LOGOUT_BUTTON = "//*[@content-desc='Logout']"
    ALERT_WARNING_LOGOUT = "warningText"
    YOUTUBE_SHORTS="//*[@text='Shorts']"
    NOTIFICATION_REMOVE="//*[@class='android.widget.ImageView' and @resource-id='zscaler.com.zscaler:id/notificationRemove']"
   #LOGOUT_PASSWORD = "passwordPromptEditText"
    LOGOUT_PASSWORD="//*[@class='android.widget.EditText' and @resource-id='zscaler.com.zscaler:id/passwordPromptEditText']"
    #LOGOUT_PASSWORD_CANCEL_BUTTON = "cancel"
    LOGOUT_PASSWORD_CANCEL_BUTTON ="//*[@class='android.widget.EditText' and @resource-id='zscaler.com.zscaler:id/cancel']"
    #LOGOUT_PASSWORD_LOGOUT_BUTTON = "doAction"
    LOGOUT_PASSWORD_LOGOUT_BUTTON ="//*[@class='android.widget.TextView' and @resource-id='zscaler.com.zscaler:id/ok']"
    WEBSECURITY_TAB_XPATH = "//android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.widget.RelativeLayout[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.LinearLayout[2]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.widget.RelativeLayout[1]/android.support.v7.widget.RecyclerView[1]/android.widget.RelativeLayout[1]"
    NOTIFICATIONS_TAB_XPATH = "//android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.widget.RelativeLayout[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.LinearLayout[2]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.widget.RelativeLayout[1]/android.support.v7.widget.RecyclerView[1]/android.widget.RelativeLayout[2]"
    MORE_TAB_XPATH = "//android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.widget.RelativeLayout[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.LinearLayout[2]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.widget.RelativeLayout[1]/android.support.v7.widget.RecyclerView[1]/android.widget.RelativeLayout[3]"
    LOG_MODE_LIST = "text1"
    LOG_MODE_ERROR_XPATH = "android.widget.ListView[1]/android.widget.TextView[1]"
    USERNAME1="//hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/androidx.drawerlayout.widget.DrawerLayout/android.view.ViewGroup/android.widget.RelativeLayout/android.widget.ScrollView/android.widget.LinearLayout/android.widget.RelativeLayout/android.widget.RelativeLayout/android.widget.EditText"
    LOG_MODE_WARN_XPATH = "android.widget.ListView[1]/android.widget.TextView[2]"
    LOG_MODE_INFO_XPATH = "android.widget.ListView[1]/android.widget.TextView[3]"
    LOG_MODE_DEBUG_XPATH = "android.widget.ListView[1]/android.widget.TextView[4]"
    APP_VERSION = "appVersionString"
    DISABLE="//*[@class='android.widget.TextView' and @text='DISABLE']"
    DISABLE_PASSWORD="//*[@class='android.widget.EditText' and @text='Disable Password']"
    ZIA_ON_STATUS = "//*[@class='android.widget.TextView' and @text='ON']"
    ZIA_OFF_STATUS="//*[@class='android.widget.TextView' and @text='OFF']"
    ZPA_ON_STATUS = "//*[@class='android.widget.TextView' and @text='ON']"
    #USERNAME="//hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/androidx.drawerlayout.widget.DrawerLayout/android.view.ViewGroup/android.widget.RelativeLayout/android.widget.ScrollView/android.widget.LinearLayout/android.widget.RelativeLayout/android.widget.RelativeLayout/android.widget.EditText"
    BROWSER="//android.widget.TextView'[@content-desc='HTTP Shortcuts']"
    DOWNLOAD_BUTTON="//*[@class='android.widget.Button' and @id='button_primary']"
    XFF_HEADER = "/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.view.ViewGroup/android.webkit.WebView/android.webkit.WebView/android.view.View/android.widget.TextView[3]"
    NOT_GOING_VIA_ZSCALER = '''//*[@class='android.view.View' and @text="The request received from you didn't come from a Zscaler IP therefore you are not going through the Zscaler proxy service."]'''
    CANCEL_BUTTON="//*[@class='android.widget.Button' and @id='button_secondary']"
    NAME_REPORT_ISSUE="//*[@class='android.widget.EditText' and @text='Name [required]']"
    REPORT_ISSUE="//*[@class='android.widget.TextView' and @text='Report an Issue']"
    SUCCESS_REPORT_ISSUE="//*[@class='android.widget.TextView' and @text='Report issue successful']"
    REPORT_ISSUE_CLOSE="//*[@class='android.widget.TextView' and @text='CLOSE']"
    SEND="//*[@class='android.widget.Button' and @text='SEND']"
    GMAIL_EXPORT_LOGS="//*[@class='android.widget.TextView' and @text='Gmail']"
    ANDROID_GMAIL_TO="//*[@class='android.widget.EditText' and @text='']"
    GMAIL_TO="//*[@class='android.widget.EditText' and @text='']"
    ANDROID_GMAIL_SEND="//*[@class='android.widget.Button' and @content-desc='Send']"
    GMAIL_SEND="//*[@class='android.widget.TextView' and @content-desc='Send']"
    UPDATE="//*[@class='android.widget.TextView' and @text='Update Policy']"
    RESTART="//*[@class='android.widget.TextView' and @text='Restart Service']"
    CLEAR_LOGS="//*[@class='android.widget.TextView' and @id='clearLogs']"
    EXPORT_LOGS="//*[@class='android.widget.TextView' and @text='Export Logs']"
    WITHOUT_LOGIN_EXPORT_LOGS="//*[@class='android.widget.CheckedTextView' and @text='Export Logs']"
    CANCEL="//*[@class='android.widget.Button' and @id='cancel']"
    NOTIFICATION="//*[@class='android.widget.TextView' and @text='More']"
    YES_RESTART="//*[@class='android.widget.TextView' and @text='YES, RESTART']"
    CLIENT_IP='zscaler.com.zscaler:id/clientIp'
    PROXY_SERVER="//*[@class='android.widget.TextView' and @resource-id='zscaler.com.zscaler:id/server']"
    TIME_CONNECTED="//*[@class='android.widget.TextView' and @resource-id='zscaler.com.zscaler:id/timeConnected']"
    TOTAL_BYTES_SENT="//*[@class='android.widget.TextView' and @resource-id='zscaler.com.zscaler:id/bytesSent']"
    TOTAL_BYTES_RECEIVED="//*[@class='android.widget.TextView' and @resource-id='zscaler.com.zscaler:id/bytesReceived']"
    WEBSECURITY="//*[@class='android.widget.TextView' and @text='Internet Security']"
    #OKTA_USERNAME="/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/androidx.drawerlayout.widget.DrawerLayout/android.view.ViewGroup/android.widget.RelativeLayout/android.webkit.WebView/android.webkit.WebView/android.view.View/android.view.View[1]/android.view.View[2]/android.view.View/android.view.View[2]/android.view.View[1]/android.widget.EditText[1]"
    OKTA_USERNAME="//*[@class='android.widget.EditText' and @resource-id='okta-signin-username']"
    OKTA_PASSWORD="//*[@class='android.widget.EditText' and @resource-id='okta-signin-password']"
    OKTA_SIGNIN="//*[@class='android.widget.Button' and @text='Sign In']"
    PRIVATE_SECURITY="//*[@class='android.widget.TextView' and @text='Private Access']"
    PRIVATE_ACCESS_TURN_OFF="//*[@class='android.widget.TextView' and @text='TURN OFF']"
    PRIVATE_ACCESS_TURN_ON="//*[@class='android.widget.TextView' and @text='TURN ON']"
    DISABLED_SERVICE_STATUS = "//*[@class='android.widget.TextView' and @text='Disabled']"
    TRUSTED_NW_SERVICE_STATUS = "//*[@class='android.widget.TextView' and @text='Trusted Network']"
    OFF_TRUSTED_NW_SERVICE_STATUS = "//*[@class='android.widget.TextView' and @text='Off Trusted Network']"
    POLICY_NAME = "//*[@class='android.widget.TextView' and @text='automation_win_10']"
    BURGER_MENU	= "//android.widget.ImageButton[@content-desc='Open navigation drawer']"
    CLOUD_NAME = "/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/androidx.drawerlayout.widget.DrawerLayout/android.widget.FrameLayout/androidx.recyclerview.widget.RecyclerView/androidx.appcompat.widget.LinearLayoutCompat[5]/android.widget.CheckedTextView"
    CLOUD_NAME_FIELD ="//*[@class='android.widget.EditText' and @text='Cloud Name']"
    CLOUD_NAME_SAVE ="//*[@class='android.widget.TextView' and @text='SAVE']"
    BROKER_IP="//*[@resource-id='zscaler.com.zscaler:id/brokerIp']"
    EMPTY_CLOUD_NAME ="//*[@class='android.widget.TextView' and @text='Cloud name is empty']"
    CLOUD_NAME_CLEAR ="//*[@class='android.widget.TextView' and @text='CLEAR']"
    INVALID_CLOUD_NAME ="//*[@class='android.widget.TextView' and @resource-id='zscaler.com.zscaler:id/errorText']"
    ACTIVATE_ADMIN ="//*[@class='android.widget.Button' and @text='Activate']"
    CANCEL_ADMIN="//*[@class='android.widget.Button' and @text='Cancel']"
    KLMS_CHECKBOX="//*[@class='android.widget.CheckBox' and @resource-id='com.samsung.klmsagent:id/checkBox1']"
    KLMS_CONFIRM="//*[@class='android.widget.TextView' and @text='CONFIRM']"
    INSTALL_CERTIFICATES="//*[@class='android.widget.TextView' and @text='Install Certificates' and @resource-id='zscaler.com.zscaler:id/text']"
    ROOT_CERT="//*[@class='android.widget.TextView' and @text='Zscaler root cert' and @resource-id='zscaler.com.zscaler:id/zscaler_root_cert']"
    DEVICE_STORAGE="//*[@class='android.widget.TextView' and @text='Install from device storage' and @resource-id='zscaler.com.zscaler:id/install_from_device_storage']"
    CA_CERTIFICATE="//*[@class='android.widget.TextView' and @text='CA certificate']"
    ZSCALER_VPN="//*[@class='android.widget.TextView' and @text='Zscaler']"
    KLMS_CANCEL = "//*[@class='android.widget.TextView' and @text='CANCEL']"
    KLMS_AGREE="//*[@class='android.widget.TextView' and @text='NEXT']"
    UNINSTALL="//*[@class='android.widget.TextView' and @text='Uninstall']"
    UNINSTALL_WITHOUT_LOGIN="//*[@class='android.widget.CheckedTextView' and @text='Uninstall']"
    UNINSTALL_BUTTON="//*[@class='android.widget.TextView' and @text='UNINSTALL']"
    VPN_OK_BUTTON="//*[@class='android.widget.Button' and @text='OK']"
    CHROMEOS_VPN_OK_BUTTON='/hierarchy/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.ScrollView/android.widget.LinearLayout/android.widget.Button[2]'
    CHROMEOS_PROXY_SERVER='/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.view.ViewGroup/android.widget.RelativeLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.LinearLayout[1]/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.ScrollView/android.widget.LinearLayout/android.widget.LinearLayout/android.widget.LinearLayout[2]/android.widget.TextView[2]'
    CHROMEOS_TIME_CONNECTED='/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.view.ViewGroup/android.widget.RelativeLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.LinearLayout[1]/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.ScrollView/android.widget.LinearLayout/android.widget.LinearLayout/android.widget.LinearLayout[2]/android.widget.TextView[6]'
    CHROMEOS_TOTAL_BYTES_RECEIVED="//*[@class='android.widget.TextView' and @resource-id='zscaler.com.zschromeosapp:id/bytesReceived']"
    CHROMEOS_TOTAL_BYTES_SENT="//*[@class='android.widget.TextView' and @resource-id='zscaler.com.zschromeosapp:id/bytesSent']"
    AD_HOC_MAX_CHARS_TEXT="//*[@class='android.widget.TextView' and @text='Invalid cloud name. Less than 20 characters allowed.']"
    TRAFFIC_VIA_ZSCALER="//*[@text='You are accessing the Internet via a Zscaler BETA proxy hosted Singapore IV in the zscalerbeta.net cloud.']"
    CHROMEOS_SETTINGS_CREDENTIALS="//*[@class='android.widget.TextView' and @text='Credentials']"
    CHROMEOS_INSTALL_CERT_FROM_SD_CARD="//*[@class='android.widget.TextView' and @text='Install from SD card']"
    WIFI_SWITCH="//*[@class='android.widget.Switch' and @content-desc='Wi-Fi' and @resource-id='android:id/switch_widget']"
    CAUTION_RULE="//*[@class='android.widget.Button' and @text='Continue']"
    BLOCK_RULE="//*[@class='android.view.View' and @text='Website blocked']"
    AUP_TEXT="//*[@class='android.widget.TextView' and @text='Automated AUP']"
    AUP_ACCEPT="//*[@class='android.widget.Button' and @text='ACCEPT']"
    SSL_CERT_TEXT="//*[@class='android.widget.TextView' and @text='Verified By: Zscaler Inc.,ST=California']"
    LOCK_ICON="//*[@class='android.widget.ImageView' and @resource-id='org.mozilla.firefox:id/mozac_browser_toolbar_security_indicator']"
    SECURE_CONNECTION="//*[@class='android.widget.TextView' and @text='Connection is secure']"
    MOD_RESTART_ZCC="//*[@class='android.widget.TextView' and @text='Zscaler Client Connector']"
    RESTART_ZCC_OK="//*[@class='android.widget.TextView' and @text='OK']"
    MODZ_POP_UP="//*[@class='android.widget.Toast' and @text='Cloud name saved as mod.z']"
    ADHOC_VALIDATION_FAILED="//*[@class='android.widget.TextView' and @text='cloud name validation failed']"
    SETTINGS_SEARCH_BAR="//*[@class='android.view.ViewGroup' and @resource-id='com.android.settings:id/search_action_bar']"
    ANDROID_SEARCH_BAR="//*[@class='android.widget.Button' and @content-desc='Search']"
    ALLOW="//*[@class='android.widget.Button' and @text='Allow']"
    BAD_SERVER_CERT_XPATH="//*[@class='android.view.View' and @text='Website blocked']"
    AGREE="//*[@class='android.widget.Button' and @text='Agree']"
    INTERNET_SECURITY_TAB = "//*[@class='androidwidget.RelativeLayout' and @resource-id='zscaler.com.zscaler:id/rowLayout']"
    INTERNET_SECURITY_PASSWORD_BOX= "//*[@class='android.widget.EditText' and @resource-id='zscaler.com.zscaler:id/passwordPromptEditText']"
    INTERNET_SECURITY_PASSWORD_CANCEL_BUTTON="//*[@class='android.widget.TextView' and @resource-id='zscaler.com.zscaler:id/cancel']"
    INTERNET_SECURITY_PASSWORD_ACCEPT_BUTTON= "//*[@class='android.widget.TextView' and @resource-id='zscaler.com.zscaler:id/ok']"
    INTERNET_SECURITY_PASSWORD_CHROMEOS_ACCEPT_BUTTON= "//*[@class='android.widget.TextView' and @resource-id='zscaler.com.zschromeosapp:id/ok']"
    INTERNET_SECURITY_TURN_OFF_TAB="//*[@class='android.widget.TextView' and @text='TURN OFF']"
    INTERNET_SECURITY_TURN_ON_TAB = "//*[@class='android.widget.TextView' and @text='TURN ON']"
    PRIVATE_SECURITY="//*[@class='android.widget.TextView' and @text='Private Access']"
    PRIVATE_ACCESS_TURN_OFF="//*[@class='android.widget.TextView' and @text='TURN OFF']"
    PRIVATE_ACCESS_TURN_ON="//*[@class='android.widget.TextView' and @text='TURN ON']"


class ZCC():
    def __init__(self,
                 handle: str ="easy",
                 startApp:bool =False,
                 logger: bool =None):
        self.logger = (logger if logger else CustomLogger.Initialize_Logger(Log_File_Name="ZCC.log", Log_Level="INFO"))

        
        self.UI = GUI(handle=handle,logger=self.logger)
        
        self.operating_system = self.UI.operating_system
        self.logger.info("Initialized ZCC GUI")
        self.sleep_factor = 1
        self.startApp=startApp
        self.fetch_export_logs=Fetch_Android_Logs()
        self.zccBuild=''
        
        self.initialize_elements()
        self.startTimeForLogSearch=""
        try:
            from common_lib.log_ops import log_ops
        except Exception as E:
            self.logger.error(f"ERROR :: Unable to import log ops - {E}")
            raise Exception(f"ERROR :: Unable to import log ops - {E}")
        try:
            from OS_Android.lib.OPS_System import Sys_Ops
        except Exception as E:
            self.logger.error(f"ERROR :: Unable to import sys ops - {E}")
            raise Exception(f"ERROR :: Unable to import sys ops - {E}")
        self.SysOps = Sys_Ops(self.logger)
        self.logOps = log_ops(console_handle_needed=False)
        #try:
            #self.zccBuild = self.logOps.get_current_zcc_version()
        #except Exception as E:
            #self.logger.error(f"Unable to get zcc version :: {E}\n\n")
            #self.zccBuild = ''

    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def initialize_elements(self):  # initialize elements for win/mac
       
            
        if self.startApp:
            self.UI.start_app(app=android_elements.APP, sleep_time=30)
            self.elements = copy.deepcopy(android_elements)
            self.UI.app=self.elements.APP
            return

    # ------------------------------------------------------------------------------------------------------------------------------------------------------


    def explicit_wait(self,xpath,sleep=120):
        try:
            WebDriverWait(self.UI.driver,sleep).until(EC.visibility_of_element_located((By.XPATH,xpath)))
            self.logger.info("SUCCESS:: Element exists::{}".format(xpath))
        except:
            raise Exception("Failure::Element not visible:: {}".format(xpath))
        

    def validate_zpa_bytes(self,before_access=False,after_access=False):
        
        cmd="adb shell am start -n zscaler.com.zscaler/com.zscaler.activities.MainActivity"
        out = subprocess.Popen(cmd, shell=True,stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=subprocess.PIPE)
        time.sleep(5)
        if before_access:
            self.UI.click(self.elements.PRIVATE_SECURITY,sleep_time=1)

            before_accessing_sent_bytes=self.UI.click(self.elements.TOTAL_BYTES_SENT,sleep_time=1,do_click=False, returnText=True,)
            before_accessing_recvd_bytes=self.UI.click(self.elements.TOTAL_BYTES_RECEIVED,sleep_time=1,do_click=False, returnText=True,)
            
            self.logger.info("Bytes before accessing ZPA application: " + before_accessing_sent_bytes)
            return before_accessing_sent_bytes,before_accessing_recvd_bytes
        if after_access:
            self.UI.click(self.elements.PRIVATE_SECURITY,sleep_time=1)
            after_accessing_sent_bytes=self.UI.click(self.elements.TOTAL_BYTES_SENT,sleep_time=1,do_click=False, returnText=True,)
            after_accessing_recvd_bytes=self.UI.click(self.elements.TOTAL_BYTES_RECEIVED,sleep_time=1,do_click=False, returnText=True,)
            self.logger.info("Bytes before accessing ZPA application: " + after_accessing_sent_bytes)
            return after_accessing_sent_bytes,after_accessing_recvd_bytes
        
    def calculateFuncExecuteTime(func):
        '''
        Decorator used for calculating time taken for execution of a function
        Flow: ------------------------------
            1. Func is passed to calculateFuncExecuteTime which calls wrapper func
            2. Wrapper func then first gets the current time(start time) and then make call to the given func as arg in calculateFuncExecuteTime
            3. After func is executed, we then get the current time(end time) and then calculate endtime-starttime and get the time difference
        '''
        @wraps(func)
        def wrapper(*args,**kwargs):
            startTime = time.time()
            #print("START TIME : {}".format(startTime))
            func(*args,**kwargs)
            endTime = time.time()
            #print("END TIME : {}".format(endTime))
            args[0].FileTransferTime = int(endTime-startTime)
            print("\n")
            args[0].logger.critical(f"{'*'*50}")
            args[0].logger.critical(f"TIME TAKEN FOR {func.__name__} : {args[0].FileTransferTime} Seconds")
            args[0].logger.critical(f"{'*'*50}")
        return wrapper

    # ------------------------------------------------------------------------------------------------------------------------------------------------------   

    def validate_zcc_logged_in(self):
        i=0
        zccLoggedInValidated = False
        while i<=3:
            
            try:
               zccLoggedInValidated=self.UI.check_if_exists(self.elements.NOTIFICATION)
                
            except:
                i+=1
                time.sleep(5)
            else:
                break
        if zccLoggedInValidated:self.logger.info(f"Logout button exists, zcc logged in")
        else:
            self.logger.error("Logout button does not exists, please check logs and zcc state")
            raise Exception("Logout button does not exists, please check logs and zcc state")
    def bring_zcc_to_focus(self,
                        number_of_trails: int = 2,      # number of times to try to open Zcc before raising exception
                        sleep_before_focus: int = 0,    # int seconds to sleep before bringing zcc to focus
                           ):  
        time.sleep(sleep_before_focus)
        try:
            self.UI.driver.launch_app()
            self.logger.info("Success :: ZCC brought to Focus!")
            return
        except Exception as e:
            self.logger.info("Warning :: trying to bring ZCC to Focus again! ; {}\n".format(e))
        
        self.logger.error("Failed to bring ZCC to Focus")
        raise Exception("Error :: Failed to bring ZCC to Focus")
    
    # ---------------------------------------------------------- #
    #               HELPER FUNCTIONS FOR ZCC LOGIN               #   
    # ---------------------------------------------------------- #
    def Do_Okta_Login(self,     
                      user_id : str,                    # okta username
                      user_password : str,              # okta password
                      search_log_time : str,            # time to be used for log search - datetime.now() should be passed
                      partial_login : bool,             # defines whether to terminate login or do complete login
                      search_file: str = "latest"       # defines whether to search latest file or oldest file
                      ):
        if partial_login:
                self.bring_zcc_to_focus()
                self.logger.info("Aborting Okta Login , hitting back button on ZCC")
                self.UI.click(self.elements.ZCC_BACK_BUTTON_AT_OKTA,sleep_time=1, AEFC="Cannot click ZCC Back Button")
                self.startTimeForLogSearch = datetime.now()         
                return
        self.explicit_wait(self.elements.OKTA_USERNAME)
    
        self.UI.click(self.elements.OKTA_USERNAME,retry_frequency=5,max_threshold=10,sleep_time=1, AEFC="Cannot click okta username")
        self.UI.type(self.elements.OKTA_USERNAME, text=user_id, sleep_time=1, AEFC="Something wrong with username")
        self.UI.type(self.elements.OKTA_PASSWORD, text=user_password, sleep_time=1, AEFC="Something wrong with passwd")
        okta_sign_in=self.UI.check_if_exists(self.elements.OKTA_SIGNIN)
        if okta_sign_in:
            self.UI.click(self.elements.OKTA_SIGNIN)
        else:
            self.SysOps.adb_commands("adb shell input keyevent KEYCODE_BACK")
            self.UI.click(self.elements.OKTA_SIGNIN)
        
        try:
            self.explicit_wait(self.elements.VPN_OK_BUTTON,sleep=10)
            self.UI.click(self.elements.VPN_OK_BUTTON)
            self.explicit_wait(self.elements.ZPA_ON_STATUS)
        except:
            self.explicit_wait(self.elements.ZPA_ON_STATUS)

                
    def Initial_ZCC_Login_Window(self,      # helper function for initial userfield at zcc login page
                                 USERNAME: str):
        try:
            
            self.UI.type(self.elements.USERNAME, text=USERNAME, sleep_time=1, AEFC="Something wrong with username")
            self.UI.click(self.elements.LOGIN_BUTTON, sleep_time=0, AEFC="Unable to Click Login Button")
            self.startTimeForLogSearch = datetime.now()         
        except Exception as e:
            self.logger.error(f"Error :: Login Failed at USER_ID Page :: {e}")
            raise Exception(f"Error :: Login Failed at USER_ID Page :: {e}")
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------   
    def Handle_Aup(self,                    # helper function for handling zcc aup login
                   TIME_FOR_LOG_SEARCH,     # datetime.now()
                   ZIA_SAML_LOGIN: bool = False,
                   CANCEL_AUP: bool = False,
                   ):
        try: 
            self.bring_zcc_to_focus()
            self.UI.click(self.elements.EULA_DECLINE_BUTTON if CANCEL_AUP else self.elements.EULA_ACCEPT_BUTTON, sleep_time=1, AEFC="Cannot click AUP Decline" if CANCEL_AUP else "Cannot click AUP Accept")
            self.startTimeForLogSearch = datetime.now()         
        except Exception as e:
            self.logger.error(f"Error :: Login Failed at AUP Page :: {e}")
            raise Exception(f"Error :: Login Failed at AUP Page :: {e}")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------      
    def Handle_Zia_Saml(self,               # helper function for zia saml login
                        USERNAME: str,
                        PASSWORD : str,
                        TIME_FOR_LOG_SEARCH,        #datetime.now() object
                        CANCEL_ZIA_SAML_LOGIN : bool
                        ):
        try:
            #time.sleep(15)
            self.Do_Okta_Login(user_id=USERNAME,user_password=PASSWORD,search_log_time=TIME_FOR_LOG_SEARCH,partial_login=CANCEL_ZIA_SAML_LOGIN)
            if CANCEL_ZIA_SAML_LOGIN:
                self.logger.info("Login aborted at ZIA SAML")
                return
        except Exception as e:
            self.logger.error(f"Login failed at ZIA SAML OKTA Page :: {e}")
            raise Exception(f"Login failed at ZIA SAML OKTA Page :: {e}")
        
    # ------------------------------------------------------------------------------------------------------------------------------------------------------   
    def Handle_Zia_Login(self,              # helper function for zia login
                        PASSWORD: str,
                        TIME_FOR_LOG_SEARCH,    #datetime.now() object
                        CANCEL_ZIA_LOGIN: bool):
        try:
            if CANCEL_ZIA_LOGIN:
                self.logger.info("Aborting Login at ZIA, hitting back button on ZCC")
                self.UI.click(self.elements.ZCC_BACK_BUTTON,sleep_time=1, AEFC="Cannot click ZCC Back Button")
                return
            
            else:    
                self.explicit_wait(self.elements.PASSWORD)
                self.UI.type(self.elements.PASSWORD, text=PASSWORD, sleep_time=1, AEFC="Password button not found")
                self.UI.click(self.elements.LOGIN_BUTTON, sleep_time=0, AEFC="Unable to click Login Button")


        except Exception as e:
            self.logger.error(f"Error :: Login Failed at Form PASSWORD Page :: {e}")
            raise Exception(f"Error :: Login Failed at Form PASSWORD Page :: {e}")
        
    def Handle_Zia_One_Id_Login(self): # place holder for zia one id login
        pass

    # ------------------------------------------------------------------------------------------------------------------------------------------------------   
    def Handle_Zpa_Login(self,              # helper function for zpa login
                        USERNAME: str,
                        PASSWORD : str,
                        TIME_FOR_LOG_SEARCH,        #datetime.now() object
                        CANCEL_ZPA_LOGIN : bool
                        ):
        try:
            self.Do_Okta_Login(user_id=USERNAME,user_password=PASSWORD,search_log_time=TIME_FOR_LOG_SEARCH,partial_login=CANCEL_ZPA_LOGIN)
            self.logger.info("Success :: OKTA Login Succesfull!")
            if CANCEL_ZPA_LOGIN:
                self.logger.info("Login aborted at ZPA SAML")
                return
        except Exception as e:
            self.logger.error(f"Login failed at ZPA OKTA Page :: {e}")
            raise Exception(f"Login failed at ZPA OKTA Page :: {e}")

    # ------------------------------------------------------------------------------------------------------------------------------------------------------    
    @allure.step("zcc login")       
    @calculateFuncExecuteTime
    def Login(self,                              # PARENT FUNCTION FOR ZCC LOGIN
              ZIA_USER: str = None,              # zia user id
              ZIA_PASSWORD: str = None,          # zia user password
              ZPA_USER: str = None,              # zpa user id
              ZPA_PASSWORD: str = None,          # zpa user password
              AUP: bool= False,                  # Defines AUP enabled or not 
              ZIA_SAML_LOGIN: bool= False,       # Defines ZIA SAML enabled or not
              ZIA_ONE_ID_LOGIN: bool= False,       # Defines ZIA SAML enabled or not
              CANCEL_LOGIN_AT_ZIA: bool= False,  # Defines whether to cancel login at ZIA login page 
              CANCEL_LOGIN_AT_ZPA: bool= False,  # Defines whether to cancel login at ZPA login page
              CANCEL_AUP: bool= False,           # Defines whether to cancel or decline AUP
              sleep_time: int = 10               # Defines sleep time after login is done
              ):        
        
        ZIA,ZPA = False,False

        if ZIA_USER==ZIA_PASSWORD==ZPA_USER==ZPA_PASSWORD==None:
            # no zia user is given, check for zpa user
            self.logger.error("Neither ZIA or ZPA user given to be logged in, cannot login")
            raise Exception("Neither ZIA or ZPA user given to be logged in, cannot login")
        
        if ZIA_USER and ZIA_PASSWORD:ZIA=True
        if ZPA_USER and ZPA_PASSWORD: ZPA=True
        if ZIA and ZPA:self.logger.info("Given user details are ZIA+ZPA")
        if not ZIA:self.logger.info("Given user is ZPA only user")
       
       # Intial Username field -------------------------
        if ZIA or ZPA:
            self.Initial_ZCC_Login_Window(USERNAME=ZIA_USER if ZIA else ZPA_USER)
        
        # AUP -------------------------
        if AUP:
            self.Handle_Aup(TIME_FOR_LOG_SEARCH=self.startTimeForLogSearch,ZIA_SAML_LOGIN=ZIA_SAML_LOGIN,CANCEL_AUP=CANCEL_AUP)
        
        # ZIA -------------------------
        if ZIA:
            if ZIA_SAML_LOGIN:
                self.Handle_Zia_Saml(USERNAME=ZIA_USER,PASSWORD=ZIA_PASSWORD,TIME_FOR_LOG_SEARCH=self.startTimeForLogSearch,CANCEL_ZIA_SAML_LOGIN=CANCEL_LOGIN_AT_ZIA)
            elif ZIA_ONE_ID_LOGIN:
                self.Handle_Zia_One_Id_Login()
            else:
                self.Handle_Zia_Login(PASSWORD=ZIA_PASSWORD,TIME_FOR_LOG_SEARCH=self.startTimeForLogSearch,CANCEL_ZIA_LOGIN=CANCEL_LOGIN_AT_ZIA)
        
        # ZPA -------------------------
        if ZPA:
            self.Handle_Zpa_Login(USERNAME=ZPA_USER,PASSWORD=ZPA_PASSWORD,TIME_FOR_LOG_SEARCH=self.startTimeForLogSearch,CANCEL_ZPA_LOGIN=CANCEL_LOGIN_AT_ZPA)
            
        if sleep_time>0:
            print(f'Sleeping for {sleep_time}')
            time.sleep(sleep_time)
            
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @calculateFuncExecuteTime
    @allure.step("Logout ZCC")
    def Logout( self,
                password: str =None,            # Passowrd/OTP to be used for logout
                sleep_time: int =30,           # number of seconds to sleep when executed
                failure_expected: bool=False,   # Defines whether logout should fail - used with incorrect password
                cancel_Logout: bool = False     # Defines whether to cancel logout operation
               ):
    
        if sleep_time==0:sleep_time=30
        try:
            
            self.UI.click(self.elements.LOGOUT_BUTTON, sleep_time=1 * self.sleep_factor,AEFC="Unable to click Logout Button")

            if cancel_Logout:
                self.logger.info("Aborting Logout")
                self.UI.click(self.elements.LOGOUT_PASSWORD_CANCEL_BUTTON, sleep_time=0, AEFC="Unable to confirm Logout disable")
                return
            if password: 
                self.UI.type(self.elements.LOGOUT_PASSWORD, text=password, sleep_time=1*self.sleep_factor, AEFC="Unable to enter password")
                self.UI.click(self.elements.LOGOUT_PASSWORD_LOGOUT_BUTTON, sleep_time=0*self.sleep_factor, AEFC="Unable to confirm Logout")
                if failure_expected: 
                    try:# mostly used when incorrect password is given intentionally 
                        self.UI.click(self.elements.LOGOUT_PASSWORD_FAILED_OK_BUTTON, sleep_time=0, AEFC="Unable to confirm Logout disable")
                        return
                    except:
                        print("Wait for the operation to complete")
                else:
                    self.UI.click(self.elements.LOGOUT_PASSWORD_CANCEL_BUTTON, sleep_time=0, AEFC="Unable to confirm Logout disable")
                    return

            else:
                self.UI.click(self.elements.LOGOUT_PROMPT,sleep_time=1,AEFC="unable to logout prompt")
                self.explicit_wait(self.elements.USERNAME)
    
        except Exception as e:
            print("adding for code modify") 
            self.logger.error("Logout Failed at ZSTray Page ; {}\n".format(e))
            raise Exception("Error :: Logout Failed at ZSTray Page ; {}\n".format(e))
        
    
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("validate zcc logged in")
    def Validate_Zcc_Logged_In(self,
                               return_bool: bool = False     # Defines whether to return bool value to be used by other functions
                               ):
        i=0
        zccStateValidated = False
        while i<2:
            try:
                
                self.UI.click(path=self.elements.LOGOUT_BUTTON,sleep_time=0, AEFC="Unable to find logout button on ZCC",do_click=False,max_threshold=1,retry_frequency=1)
                zccStateValidated=True
            except:
                i+=1
                time.sleep(2)
            else:
                break
        if zccStateValidated:
            self.logger.info("Logout button exists, zcc logged in")
            if return_bool:return True
        else:
            self.logger.info("Logout button does not exists, zcc not logged in")
            if return_bool:return False
            else:raise Exception("Logout button does not exists, zcc not logged in")
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("validate zcc logged out")
    def Validate_Zcc_Logged_Out(self,
                               return_bool: bool = False     # Defines whether to return bool value to be used by other functions
                               ):
        i=0
        zccStateValidated = False
        while i<2:
            try:
                self.bring_zcc_to_focus()
                self.UI.click(path=self.elements.LOGIN_BUTTON,sleep_time=0, AEFC="Unable to find login button on ZCC",do_click=False,max_threshold=1,retry_frequency=1)
                zccStateValidated=True
            except:
                i+=1
                time.sleep(2)
            else:
                break
        if zccStateValidated:
            self.logger.info("Login button exists, zcc logged out")
            if return_bool:return True
        else:
            self.logger.info("Login button does not exists, zcc not logged out")
            if return_bool:return False
            else:raise Exception("Login button does not exists, zcc not logged out")
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @calculateFuncExecuteTime
    @allure.step("Enable/disable Service ZIA/ZPA/ZDX")
    def Toggle_Service( self,
                        service: str,                   # Which service to Toggle (ZIA/ZPA/ZDX)
                        action: bool =True,             # Turn on if True, otherwise Turn off
                        password: str =None,            # Password/OTP for disabling service
                        disable_reason: str =None,      # Disable reason to enter while disabling any zscaler service (zia/zpa/zdx)
                        sleep_time=10,                  # Number of seconds to sleep after execution
                        cancel_toggle: bool =False,     # Determines whether to cancel toggle operation or not
                        failure_expected: bool =False   # Expect that password will not be accepted. deal accordingly
                        ):                
        if service=="ZIA":
            TAB = self.elements.WEBSECURITY
            TOGGLE_ON = self.elements.INTERNET_SECURITY_TURN_ON_TAB
            TOGGLE_OFF = self.elements.INTERNET_SECURITY_TURN_OFF_TAB
            PASSOWRD_BOX = self.elements.INTERNET_SECURITY_PASSWORD_BOX
            #PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
            PASSWORD_ACCEPT_BUTTON = self.elements.INTERNET_SECURITY_PASSWORD_ACCEPT_BUTTON
            # CONFIRM_ACCEPT_BUTTON = self.elements.CONFIRM_ACCEPT_BUTTON
            # FAILED_CONFIRM_BUTTON = self.elements.INTERNET_SECURITY_PASSWORD_FAILED_OK_BUTTON
            CANCEL_BUTTON = self.elements.INTERNET_SECURITY_PASSWORD_CANCEL_BUTTON
        elif service=="ZDP":
            TAB = self.elements.DATA_PREVENTION_TAB
            TOGGLE_ON = self.elements.DATA_PREVENTION_POWER_BUTTON_ON
            TOGGLE_OFF = self.elements.DATA_PREVENTION_POWER_BUTTON_OFF
            PASSOWRD_BOX = self.elements.DATA_PREVENTION_PASSWORD_BOX
            DISABLE_REASON_BOX_WITH_PASSWORD = self.elements.PASSWORD_DISABLE_REASON_BOX
            DISABLE_REASON_BOX = self.elements.DISABLE_REASON_BOX
            PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
            PASSWORD_ACCEPT_BUTTON = self.elements.DATA_PREVENTION_PASSWORD_ACCEPT_BUTTON
            CONFIRM_ACCEPT_BUTTON = self.elements.CONFIRM_ACCEPT_BUTTON
            FAILED_CONFIRM_BUTTON = self.elements.DATA_PREVENTION_PASSWORD_FAILED_OK_BUTTON
            CANCEL_BUTTON = self.elements.DATA_PREVENTION_PASSWORD_CANCEL_BUTTON
        elif service == "ZPA":
            TAB = self.elements.PRIVATE_SECURITY
            TOGGLE_ON = self.elements.PRIVATE_ACCESS_TURN_ON
            TOGGLE_OFF = self.elements.PRIVATE_ACCESS_TURN_ON
            # PASSOWRD_BOX = self.elements.PRIVATE_ACCESS_PASSWORD_BOX
            # DISABLE_REASON_BOX_WITH_PASSWORD = self.elements.PASSWORD_DISABLE_REASON_BOX
            # DISABLE_REASON_BOX = self.elements.DISABLE_REASON_BOX
            # PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
            # PASSWORD_ACCEPT_BUTTON = self.elements.PRIVATE_ACCESS_PASSWORD_ACCEPT_BUTTON
            # CONFIRM_ACCEPT_BUTTON = self.elements.CONFIRM_ACCEPT_BUTTON
            # FAILED_CONFIRM_BUTTON = self.elements.PRIVATE_ACCESS_PASSWORD_FAILED_OK_BUTTON
            # CANCEL_BUTTON = self.elements.PRIVATE_ACCESS_PASSWORD_CANCEL_BUTTON
        elif service == "ZDX":
            TAB = self.elements.DIGITAL_EXPERIENCE_TAB
            TOGGLE_ON = self.elements.DIGITAL_POWER_BUTTON_ON
            TOGGLE_OFF = self.elements.DIGITAL_POWER_BUTTON_OFF
            PASSOWRD_BOX = self.elements.DIGITAL_EXPERIENCE_PASSWORD_BOX
            DISABLE_REASON_BOX_WITH_PASSWORD = self.elements.PASSWORD_DISABLE_REASON_BOX
            DISABLE_REASON_BOX = self.elements.DISABLE_REASON_BOX
            PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
            PASSWORD_ACCEPT_BUTTON = self.elements.DIGITAL_EXPERIENCE_PASSWORD_ACCEPT_BUTTON
            CONFIRM_ACCEPT_BUTTON = self.elements.CONFIRM_ACCEPT_BUTTON
            FAILED_CONFIRM_BUTTON = self.elements.DIGITAL_EXPERIENCE_PASSWORD_CANCEL_BUTTON
            CANCEL_BUTTON = self.elements.DIGITAL_EXPERIENCE_PASSWORD_CANCEL_BUTTON
        
        try:
            #self.bring_zcc_to_focus()
            self.UI.click(TAB, sleep_time=1*self.sleep_factor,AEFC=f"Unable to open {service} tab")

            if action:
                self.UI.click(TOGGLE_ON ,sleep_time=1*self.sleep_factor, AEFC=f"Unable to turn on {service}")
                self.explicit_wait(self.elements.ZIA_ON_STATUS)
            else:
                self.UI.click(TOGGLE_OFF, sleep_time=1*self.sleep_factor, AEFC=f"Unable to turn off {service}")
        
                if password and disable_reason:
                    self.UI.type(DISABLE_REASON_BOX_WITH_PASSWORD, text=str(disable_reason),sleep_time=1 * self.sleep_factor, AEFC="Unable to enter disable reason")
                    self.UI.type(PASSOWRD_BOX, text=password,sleep_time=1 * self.sleep_factor, AEFC="Unable to enter password")
                    # password ok button
                    if cancel_toggle:
                        self.logger.info("Aborting Toggle")
                        self.UI.click(CANCEL_BUTTON,sleep_time=1*self.sleep_factor,AEFC=f"Unable to cancel {service} disable")
                    else:
                        self.UI.click(PASSWORD_DISABLE_OK_BUTTON,sleep_time=1*self.sleep_factor,AEFC=f"Unable to confirm {service} disable")
                else:
                        if cancel_toggle:
                            self.UI.click(self.elements.INTERNET_SECURITY_PASSWORD_CANCEL_BUTTON)
                        else:
                            try:
                                self.UI.click(self.elements.INTERNET_SECURITY_PASSWORD_ACCEPT_BUTTON)
                            except:
                                self.UI.click(self.elements.INTERNET_SECURITY_PASSWORD_CHROMEOS_ACCEPT_BUTTON)

                            self.explicit_wait(self.elements.ZIA_OFF_STATUS)
        except Exception as e:
            self.logger.error(f"Unable to Toggle Service: {service} {action} :: {e}")
            raise Exception(f"Unable to Toggle Service: {service} {action} :: {e}")
        

    
    @calculateFuncExecuteTime
    @allure.step("Update Policy")
    def Update_Policy(self,validate_through_logs=True):

        if self.zccBuild=='':
            self.zccBuild = os.environ.get("zcc_version")
            self.logger.info(f"ZCC BUILD :: {self.zccBuild}\n\n")
        try:
            
            self.UI.click(self.elements.NOTIFICATION, sleep_time=1*self.sleep_factor, AEFC="Unable to click More button on ZCC")
            startTimeForLogSearch = datetime.now()
            self.UI.click(self.elements.UPDATE, sleep_time=0, AEFC="Unable to click Update Policy button")
            WebDriverWait(self.UI.driver,120).until(EC.presence_of_element_located((By.XPATH,self.elements.UPDATE_POLICY_TOAST)))
        except Exception as e: 
            self.logger.error("Update Policy Toast not validated ; {}\n".format(e))
            raise Exception("Error :: Update Policy Failed ; {}\n".format(e))
        
        # isUpdatePolicyValidated = False
        # try:
        #     self.Export_Logs()
        #     self.fetch_export_logs.read_exportlog_mail()
        #     self.logOps.search_log_file(file="ZCC_Android" ,words_to_find_in_line=['Policy update success'],search_mode=None,start_timestamp=startTimeForLogSearch)
        #     isUpdatePolicyValidated=True
        # except Exception as E:
        #     isUpdatePolicyValidated=False
        
        # if isUpdatePolicyValidated:
            
        #     self.logger.info("Success :: Updated Policy!")
        # else:
        #     self.logger.error(f"Update policy not validated")
        #     raise Exception(f"Update policy not validated")
    
    # ------------------------------------------------------------------------------------------------------------------------------------------------------        

    @calculateFuncExecuteTime
    @allure.step("Restart Service")
    def Restart_Service(self,
                        sleep_time: int =60,                    # Number of seconds to sleep after execution
                             
                        ):
        
        try:
            #self.bring_zcc_to_focus()
            self.UI.click(self.elements.NOTIFICATION, sleep_time=0, AEFC="Unable to click More button on ZCC")
            self.UI.click(self.elements.RESTART, sleep_time=3,AEFC="Unable to click Restart Service button on ZCC")
            
            self.UI.click(self.elements.YES_RESTART, sleep_time=0,AEFC="Unable to confirm ZCC Restart")
            WebDriverWait(self.UI.driver,180).until(EC.visibility_of_element_located((By.XPATH,self.elements.WEBSECURITY)))
            self.UI.click(self.elements.WEBSECURITY, sleep_time=0,AEFC="Unable to click on Internet Security")
            WebDriverWait(self.UI.driver,120).until(EC.visibility_of_element_located((By.XPATH,self.elements.ZIA_ON_STATUS)))
            
        except Exception as e:
            self.logger.error("Restart Service Failed ; {}\n".format(e))
            raise Exception("Error :: Restart Service Failed ; {}\n".format(e))
        
        
        self.logger.info("Success :: Restarted Service!")

    @calculateFuncExecuteTime
    def Export_Logs(self, 
                    after_login: bool = True,
                    sleep_time=120):     
        try:
            
            
            if after_login:

                self.UI.click(self.elements.NOTIFICATION, sleep_time=0, AEFC="Unable to click More button on ZCC")
                self.UI.click(self.elements.EXPORT_LOGS, sleep_time=3, AEFC="Unable to click on export logs")
                self.UI.click(self.elements.GMAIL_EXPORT_LOGS, sleep_time=3, AEFC="Unable to click on export logs")
                self.UI.type(self.elements.ANDROID_GMAIL_TO,text=Utils.export_email_id,AEFC="Unable to enter email id")
                self.UI.click(self.elements.ANDROID_GMAIL_SEND, sleep_time=1, AEFC="Unable to click on send  button")
                time.sleep(sleep_time)
            else:
                self.UI.click(self.elements.BURGER_MENU, sleep_time=1, AEFC="Unable to click on export logs")
                self.UI.click(self.elements.WITHOUT_LOGIN_EXPORT_LOGS, sleep_time=1, AEFC="Unable to click on export logs")
                self.UI.click(self.elements.GMAIL_EXPORT_LOGS, sleep_time=1, AEFC="Unable to click on export logs")
                self.UI.type(self.elements.ANDROID_GMAIL_TO,text=Utils.export_email_id,AEFC="Unable to enter email id")
                self.UI.click(self.elements.ANDROID_GMAIL_SEND, sleep_time=1, AEFC="Unable to click on send  button")
                time.sleep(sleep_time)


                
                
        except Exception as e: 
            self.logger.error("Export Logs Failed ; {}\n".format(e))
            raise Exception("Error :: Export Logs Failed ; {}\n".format(e))
    
    @calculateFuncExecuteTime
    def Uninstall_ZCC(self,after_login=False):
        if not after_login:
            self.UI.click(self.elements.BURGER_MENU,sleep_time=1, AEFC="Unable to click on burger menu")
            self.UI.click(self.elements.UNINSTALL_WITHOUT_LOGIN,sleep_time=1, AEFC="Unable to click on burger menu")
            
            self.UI.click(self.elements.UNINSTALL_BUTTON,sleep_time=1, AEFC="Unable to click on burger menu")
            self.UI.click("//*[@class='android.widget.Button' and @text='OK']",sleep_time=1, AEFC="Unable to click on burger menu")
            time.sleep(10)

    def fetch_proxy_server(self):
        
        
        self.UI.click(self.elements.NOTIFICATION,sleep_time=1, AEFC="Unable to click on burger menu")
        self.UI.click(self.elements.UPDATE,sleep_time=20, AEFC="Unable to click")
        
        self.UI.click(self.elements.WEBSECURITY,sleep_time=1, AEFC="Unable to click")
        try:
            proxy_server=self.UI.click(self.elements.PROXY_SERVER,do_click=False,returnText=True)
            proxy_server = proxy_server.split(':')
            self.logger.info(proxy_server[0])
            return proxy_server[0]
        except:
            raise Exception("Failure:: SME not found")
        
    def Validate_traffic_going_through_tunnel(self,is_zia_enable=False,is_zia_disable=False):
        if is_zia_disable:
            cmd="adb shell am start -a android.intent.action.VIEW -d http://ip.zscaler.com"
            out = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=subprocess.PIPE)
            
            time.sleep(10)
            x=self.UI.check_if_exists(self.elements.NOT_GOING_VIA_ZSCALER)
            #print x
            if x:
                self.UI.click(self.elements.NOT_GOING_VIA_ZSCALER,do_click=False)  
                self.logger.info("Traffic is not going through cloud")
            else:
                xx=self.UI.check_if_exists(self.elements.TRAFFIC_VIA_ZSCALER)
                if xx:
                    raise Exception("Failure:: traffic still going through cloud")
        if is_zia_enable:
             
            cmd="adb shell am start -a android.intent.action.VIEW -d http://ip.zscaler.com"
            out = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=subprocess.PIPE)
            try:
                WebDriverWait(self.UI.driver,120).until(EC.visibility_of_element_located((By.XPATH,self.elements.TRAFFIC_VIA_ZSCALER)))
            except:
                raise Exception("Timeout:: Element not found {}".format(self.elements.TRAFFIC_VIA_ZSCALER))
            
            y=self.UI.check_if_exists(self.elements.TRAFFIC_VIA_ZSCALER)
            #print y
            if y:
                self.UI.click(self.elements.TRAFFIC_VIA_ZSCALER,do_click=False)
            else:
                yy=self.UI.check_if_exists(self.elements.NOT_GOING_VIA_ZSCALER)
                if yy:
                    raise Exception("Failure")
        self.bring_zcc_to_focus()
                
    def Validate_tunnel_status(self,zia_only_company=False,zpa_only_company=False,zia_on=False,zpa_on=False):
        if zia_only_company:
            if zia_on:
                self.UI.driver.find_element(By.XPATH,self.elements.WEBSECURITY).click()
                time.sleep(3)
                zia_status=self.UI.check_if_exists(self.elements.ZIA_ON_STATUS)
                if zia_status:
                    self.logger.info("ZIA Status is ON")
                else:
                    raise Exception("Failure:: ZIA Status is not On")
            else:
                self.UI.driver.find_element(By.XPATH,self.elements.WEBSECURITY).click()
                zia_off_status=self.UI.check_if_exists(self.elements.ZIA_OFF_STATUS)
                if zia_off_status:
                    self.logger.info("ZIA state is OFF")
                else:
                    raise Exception("Failure:: ZIA state is not OFF")
        if zpa_only_company:

            if zpa_on:
                self.UI.driver.find_element(By.XPATH,self.elements.PRIVATE_SECURITY).click()
                zpa_status=self.UI.check_if_exists(self.elements.ZIA_ON_STATUS)
                if zpa_status:
                    self.logger.info("ZPA status is On")
                else:
                    raise Exception("Failure:: ZPA Status is not On")
            else:
                self.UI.driver.find_element(By.XPATH,self.elements.PRIVATE_SECURITY).click()
                zpa_off_status=self.UI.check_if_exists(self.elements.ZIA_OFF_STATUS)
                if zpa_off_status:
                    self.logger.info("ZpA state is OFF")
                else:
                    raise Exception("Failure:: ZPA state is not OFF")
        if not zia_only_company and not zpa_only_company:
            if zia_on:
                self.UI.driver.find_element(By.XPATH,self.elements.WEBSECURITY).click()
                zia_status=self.UI.check_if_exists(self.elements.ZIA_ON_STATUS)
                if zia_status:
                    self.logger.info("ZIA Status is ON")
                else:
                    raise Exception("Failure:: ZIA Status is not On")
            else:
                self.UI.driver.find_element(By.XPATH,self.elements.WEBSECURITY).click()
                zia_off_status=self.UI.check_if_exists(self.elements.ZIA_OFF_STATUS)
                if zia_off_status:
                    self.logger.info("ZIA state is OFF")
                else:
                    raise Exception("Failure:: ZIA state is not OFF")
            if zpa_on:
                self.UI.driver.find_element(By.XPATH,self.elements.PRIVATE_SECURITY).click()
                zpa_status=self.UI.check_if_exists(self.elements.ZIA_ON_STATUS)
                if zpa_status:
                    self.logger.info("ZPA status is On")
                else:
                    raise Exception("Failure:: ZPA Status is not On")
            else:
                self.UI.driver.find_element(By.XPATH,self.elements.PRIVATE_SECURITY).click()
                zpa_off_status=self.UI.check_if_exists(self.elements.ZIA_OFF_STATUS)
                if zpa_off_status:
                    self.logger.info("ZpA state is OFF")
                else:
                    raise Exception("Failure:: ZPA state is not OFF")
    
    def packet_capture(self,
                       enable: bool=False,  # value should be True: For enabling packet capture
                       disable:bool=False,  # value should be False: For disabling packet capture
                      ):
        
        if enable:
            self.UI.click("//*[@class='android.widget.TextView' and @text='Notifications']",sleep_time=1)
            try:
                
                self.UI.click(self.elements.REPORT_ISSUE_CLOSE)
            except:
                self.UI.click(self.elements.NOTIFICATION_REMOVE,sleep_time=1,AEFC="Unable to click")
                self.UI.click("//*[@class='android.widget.TextView' and @resource-id='zscaler.com.zscaler:id/ok']",sleep_time=1)
            
            self.UI.click(self.elements.NOTIFICATION)
            self.UI.click("//*[@class='android.widget.Switch' and @resource-id='zscaler.com.zscaler:id/packetCaptureSwitch']",sleep_time=10,AEFC="Unable to click packet capture")
        
            self.UI.click("//*[@class='android.widget.TextView' and @text='Notifications']",sleep_time=1)
            self.UI.check_if_exists("//*[@class='android.widget.TextView' and @text='Started Packet Capture']",AEFC="Started Packet Capture not found")

        if disable:
            self.UI.click("//*[@class='android.widget.TextView' and @text='Notifications']",sleep_time=1)
            try:
                self.UI.click(self.elements.REPORT_ISSUE_CLOSE)
            except:
                self.UI.click(self.elements.NOTIFICATION_REMOVE,sleep_time=1,AEFC="Unable to click")
                self.UI.click("//*[@class='android.widget.TextView' and @resource-id='zscaler.com.zscaler:id/ok']",sleep_time=1)
            self.UI.click(self.elements.NOTIFICATION)
            self.UI.click("//*[@class='android.widget.Switch' and @resource-id='zscaler.com.zscaler:id/packetCaptureSwitch']",sleep_time=10,AEFC="Unable to click packet capture")
        
            self.UI.click("//*[@class='android.widget.TextView' and @text='Notifications']",sleep_time=1)
            self.UI.check_if_exists("//*[@class='android.widget.TextView' and @text='Stopped Packet Capture']",AEFC="Stopped Packet Capture not found")
    

    def zpa_reauthentication(self,
                             userid:str=None,
                             userpassword:str=None                      
                            ):
        
        
        self.UI.click(self.elements.AUTHENTICATE)
        WebDriverWait(self.UI.driver,120).until(EC.visibility_of_element_located((By.XPATH,self.elements.OKTA_USERNAME)))
        self.Do_Okta_Login(user_id=userid,user_password=userpassword,search_log_time=self.startTimeForLogSearch,partial_login=False)
        WebDriverWait(self.UI.driver,120).until(EC.visibility_of_element_located((By.XPATH,self.elements.AUTHENTICATED)))
        
        
       

        

        

            


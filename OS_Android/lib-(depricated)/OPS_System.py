################################################################
#                       SYSTEM_OPS.PY                          #
#                                                              #
# AUTHOR : SAHIL KUMAR (<EMAIL>)               #
# CREDITS : SRINIVASA BHASWANTH, ABHIMANYU SHARMA              #
#                                                              #
################################################################
'''
Last Changes Made : 25/05/2022 by <PERSON>hil
Details : Added Installed Zscaler Root CA cert support

'''
import subprocess,os,time
import socket
import OpenSSL,ssl
from datetime import date
import shutil 
from zipfile import ZipFile
import os.path
from threading import Thread
import sys
import logging
import psutil
#import Constants
#from log_ops import log_ops
from common_lib import Constants
from common_lib.log_ops import log_ops
import allure
import json
import platform
import dns.resolver
from datetime import datetime
import pytz
if platform.uname()[0]=="windows":
    import winreg

class ThreadWithReturnValue(Thread):
        '''
        This class is a subclass created to get return value from threads
        By default threads dont send return value
        '''
        def __init__(self, group=None, target=None, name=None,
                 args=(), kwargs={}, Verbose=None):
            Thread.__init__(self, group, target, name, args, kwargs)
            self._return = None
        def run(self):
            print(type(self._target))
            if self._target is not None:
                self._return = self._target(*self._args,
                                                    **self._kwargs)
        def join(self, *args):
            Thread.join(self, *args)
            return self._return

class Sys_Ops:
    def __init__(self,logger=None, cloud=None):
        self.logger = self.Initialize_Logger("Sys_Ops.log", Log_Level="DEBUG")
        self._os_version = Constants.os_version
        if self._os_version=="Windows":
            try:
                import winreg
            except:
                pass
        self.WinFWRuleName = []
        self.firewallRuleStringMac = []
        self.CONST = Constants.Utils
        self.cloud=cloud
    
    # --------------------------------------------------------------------------------------------------------------------------
    def Initialize_Logger(self, 
    Log_File_Name,                      # (str)     Name of the Log file to be created 
    Log_Level,                          # (str)     Level of logging (INFO/DEBUG/WARNING/CRICITCAL etc)
    console_handle_needed=True          # (bool)    print on command line if True only
    ):
        """
        Target: Create Logger object.
        Work Flow
        ----------
            1. Create a logger object.
            2. Create a console handler if required
                - console handler is used to print required info on command line window.
            3. Create a file handler -> mandatory
                - Make sure 'Logs' folder exists, as this file handler will be created in this folder only.
                - file handler is used to send all information to a log file in specified folder.
            4. Set the debug level (INFO/DEBUG/WARNING/CRITICAL etc). Default: INFO
            5. Specify how the logging must happen (with/without datetime/loglevel/username)
            6. Add all created handlers to the logger instance created in step 1.
            7. return the logger instance.
        """
        # 1, 2 & 3
        self.Logs_Directory = os.path.join(os.path.abspath(os.path.dirname(os.path.abspath("Automation.py"))), "Logs")
        logger = logging.getLogger(Log_File_Name)
        if console_handle_needed: console_handler = logging.StreamHandler()
        if not os.path.exists(self.Logs_Directory): os.mkdir(self.Logs_Directory)
        file_handler = logging.FileHandler(os.path.join(self.Logs_Directory, Log_File_Name))

        # 4
        if Log_Level:
            logger.setLevel(eval("logging.{}".format(Log_Level)))
            if console_handle_needed: console_handler.setLevel(eval("logging.{}".format(Log_Level)))
            file_handler.setLevel(eval("logging.{}".format(Log_Level)))

        # 5
        if console_handle_needed: console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))

        # 6
        if console_handle_needed: logger.addHandler(console_handler)
        logger.addHandler(file_handler)
        # 7
        return logger

    #----------------------------------------------------------------------------------------------------
    def reboot_device(self):
        self.adb_commands("adb reboot")
        time.sleep(60)
        output=self.adb_commands("adb devices")
        output=output.decode('utf-8').splitlines()
        flag=False
        while  not flag:
            if any("device" in connected_device for connected_device in output):
                self.logger.info("ADB connected")
                flag=True
                break
            else:
                self.adb_commands("adb devices")
        return flag
    
    def zcc_package_name(self):
        andr_zcc=""
        chromeos_zcc=""
        package_name=self.adb_commands("adb shell pm list packages")
        package_name=package_name.decode('utf-8').split()
        if any('zscaler.com.zscaler'in package for package in package_name):
            self.logger.info("Android ZCC is present")
            andr_zcc="zscaler.com.zscaler"
            return andr_zcc
        else:
            if any('zscaler.com.zschromeosapp' in package for package in package_name):
                self.logger.info("Chrome ZCC is present")
                chromeos_zcc="zscaler.com.zschromeosapp"
                return chromeos_zcc
    
    def get_android_version(self):
        output=self.adb_commands("adb shell getprop ro.build.version.release")
        output=output.decode('utf-8').splitlines()
        print(output)
        return output
    
    def toggle_wifi(self,
                    enable:bool=False, #value should be True to enable
                    disable:bool=False, # value should be True to disabe
                    sleeptime=3
                    ):
        if enable:
            self.adb_commands("adb shell svc wifi enable")
            time.sleep(sleeptime)
            output=self.adb_commands("adb shell dumpsys wifi | findstr Wi-Fi")
            output=output.decode('utf-8').splitlines()
            print(output)
            if any("Wi-Fi is enabled" in line for line in output):
                self.logger.info("Device is connected to wifi")
            else:
                raise Exception("Failure:: Wifi not enabled successfully")
        if disable:
            self.adb_commands("adb shell svc wifi disable")
            output=self.adb_commands("adb shell dumpsys wifi | findstr Wi-Fi")
            output=output.decode('utf-8').splitlines()
            if any("Wi-Fi is disabled" in line for line in output):
                self.logger.info("Device is not connected to wifi")
            else:
                raise Exception("Failure:: Wifi not disabled successfully")
    
    def toggle_mobile_data(self,
                           enable:bool=False,
                           disable:bool=False):
        if enable:
            self.adb_commands("adb shell settings put global mobile_data 1")
            mobile_data_value=self.adb_commands("adb shell settings get global mobile_data")
            mobile_data_value=mobile_data_value.decode('utf-8').split()
            if all("1" in value for value in mobile_data_value):
                self.logger.info("Mobile data is connected")
            else:
                raise Exception("Failure::Mobile data not connected")
            
        if disable:
            self.adb_commands("adb shell settings put global mobile_data 0")
            mobile_data_value=self.adb_commands("adb shell settings get global mobile_data")
            mobile_data_value=mobile_data_value.decode('utf-8').split()
            if all("0" in value for value in mobile_data_value):
                self.logger.info("Mobile data is not connected")
            else:
                raise Exception("Failure::Mobile data not connected")
            
    def open_wireless_setting(self):
        self.adb_commands("adb shell am start -a android.settings.WIRELESS_SETTINGS")


    def adb_commands(self, cmd=None):
        if cmd!=None:
            out = subprocess.Popen(cmd,shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=subprocess.PIPE)
            output=out.communicate()[0]
            return output
    
    def screen_lock(self,lock=False,unlock=False):
        if lock:
            self.adb_commands("adb shell input keyevent 26")
            screen_status=self.adb_commands("adb shell dumpsys window policy|findStr screenState")
            screen_status=screen_status.decode('utf-8').strip().split('=')
            if any("SCREEN_STATE_OFF" in screen_state for screen_state in screen_status):
                self.logger.info("Screen Locked")
            else:
                raise Exception("Screen not Locked")

        else:
            self.adb_commands("adb shell input keyevent 82")
            time.sleep(3)
            screen_status=self.adb_commands("adb shell dumpsys window policy|findStr screenState")
            screen_status=screen_status.decode('utf-8').strip().split('=')
            if any("SCREEN_STATE_ON" in screen_state for screen_state in screen_status):
                self.logger.info("Screen UnLocked")
            else:
                raise Exception("Screen not Unlocked")


    
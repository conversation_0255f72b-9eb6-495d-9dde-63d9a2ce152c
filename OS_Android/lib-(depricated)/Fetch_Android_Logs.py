import os,sys
import subprocess
import inspect
import glob
import logging
from zipfile import ZipFile
import zipfile
import string, time, random
import shutil
import imaplib,email
from common_lib.Constants import *


# #sys.path.append('../common_lib')
# os.chdir("..\\")
# # from common_lib.Constants import Utils
# #current_dir = os.path.dirname(os.path.realpath(__file__))
# os.chdir(r"C:\Users\<USER>\Documents\EndpointAutomation2\android\mobiledevautomation\EndpointAutomation\Android")
currentdir=os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
#print (currentdir)
parentdir = os.path.dirname(currentdir)
#print (parentdir)
sys.path.insert(0,parentdir)



class Fetch_Android_Logs:

    def __init__(self,logger=None,console_handle_needed=True):
        self.logger = (logger if logger else self.Initialize_Logger("log_ops.log", Log_Level="INFO",console_handle_needed=console_handle_needed))
        self.CONST = Utils()

    def Initialize_Logger(self, 
    Log_File_Name,                      # (str)     Name of the Log file to be created 
    Log_Level,                          # (str)     Level of logging (INFO/DEBUG/WARNING/CRICITCAL etc)
    console_handle_needed=True          # (bool)    print_ on command line if True only
    ):
        """
        Target: Create Logger object.
        Work Flow
        ----------
            1. Create a logger object.
            2. Create a console handler if required
                - console handler is used to print_ required info on command line window.
            3. Create a file handler -> mandatory
                - Make sure 'Logs' folder exists, as this file handler will be created in this folder only.
                - file handler is used to send all information to a log file in specified folder.
            4. Set the debug level (INFO/DEBUG/WARNING/CRITICAL etc). Default: INFO
            5. Specify how the logging must happen (with/without datetime/loglevel/username)
            6. Add all created handlers to the logger instance created in step 1.
            7. return the logger instance.
        """
        # 1, 2 & 3
        self.Logs_Directory = os.path.join(os.path.abspath(os.path.dirname(os.path.abspath("Automation.py"))), "Logs")
        logger = logging.getLogger(Log_File_Name)
        if console_handle_needed: console_handler = logging.StreamHandler()
        if not os.path.exists(self.Logs_Directory): os.mkdir(self.Logs_Directory)
        file_handler = logging.FileHandler(os.path.join(self.Logs_Directory, Log_File_Name))

        # 4
        if Log_Level:
            logger.setLevel(eval("logging.{}".format(Log_Level)))
            if console_handle_needed: 
                console_handler.setLevel(eval("logging.{}".format(Log_Level)))
            file_handler.setLevel(eval("logging.{}".format(Log_Level)))

        # 5
        if console_handle_needed: 
            console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s :: %(message)s'))

        # 6
        if console_handle_needed: 
            logger.addHandler(console_handler)
        logger.addHandler(file_handler)

        # 7
        logger.info("If you see this, logger console is ON")
        return logger

#########################################################################################################
    
    def read_exportlog_mail(self):
        # Enter username and pd of email where logs are exported
        # enable "imap" and "less secure app" from gmail settings( do not use corp email because less secure app can't enable on it)
        #global filePath
        try:
            user='<EMAIL>'
            pwd='mrelqgudaoooyhdd'
            imap_url='imap.gmail.com'
            con=imaplib.IMAP4_SSL(imap_url)
            con.login(user,pwd)
            con.select('INBOX')
            data = con.search(None, "ALL")
            bytes_mail_id= data[1]
            mail_id=[items.decode('utf-8') for items in bytes_mail_id]

            
            id_list=mail_id[0].split()
            latest_email_id=int(id_list[-1])
            latest_email_id=id_list[-1]
            
            
            data=con.fetch(latest_email_id,'RFC822')
            
            for response in data:
                arr=response[0]
                
                if isinstance(arr, tuple):
                    
                    
                    # parse a bytes email into a message object
                    msg = email.message_from_string(arr[1].decode('utf-8'))
                    
                    if msg.is_multipart():
                        
                        
                        # iterate over email parts
                        for part in msg.walk():
                            content_disposition = str(part.get("Content-Disposition"))
                            
                                # get the email body
                            #body = part.get_payload(decode=True).decode()
                            
                            if "attachment" in content_disposition:
                                
                                filename = part.get_filename()
                                working_directory=os.path.join(parentdir,"Temp_Logs")
                                if os.path.isdir(working_directory):
                                    
                                    shutil.rmtree(working_directory)

                                    self.logger.info ("CREATING NEW FOLDER")
                                    os.mkdir(working_directory)
                                        
                                else:
                                    os.mkdir(working_directory)
                                    self.logger.info("Temp Logs is created")
                    
                                filePath = os.path.join(working_directory, filename)
                                with open(filePath, 'wb') as f:
                                    f.write(part.get_payload(decode=True))
                                   
             
                                with zipfile.ZipFile(filePath, 'r') as zip_ref:
                                    zip_ref.extractall(self.CONST.log_path)
                                os.remove(filePath)  


        except:
            raise Exception("Failure:: Fetching of logs failed")

####################################################################################################
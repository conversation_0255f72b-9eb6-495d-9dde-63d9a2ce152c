import os
import time
import subprocess
import pyshark
from scapy import all
from common_lib.common import constants
from common_lib.common.logger import Logger
import logging
import asyncio



class OpsPacket:
    """
    Provides method to capture packets & read packets
    """
    def __init__(self, log_handle:logging.Logger=None):
        self.logger = (log_handle if log_handle else Logger.initialize_logger("OpsPacket.log", log_level="DEBUG"))
        self.const = constants.Utils

    def start_packet_capture(
        self,
        pkt_capture_output_file:str,
        pkt_capture_interface:list[str],
        pkt_capture_duration:int=10,
        num_of_pkt_to_capture:int=0,
    )->tuple[bool, str, None]:
        """
        Initiates a packet capture using tshark.

        Args:
        - pkt_capture_output_file (str): The output file for the packet capture.
        - pkt_capture_interface (list[str]): A list of interfaces to capture packets from.
        - pkt_capture_duration (int, optional): The duration of the packet capture in seconds. Defaults to 10.
        - num_of_pkt_to_capture (int, optional): The number of packets to capture. Defaults to 0.

        Returns:
        - tuple[bool, str, None]: A tuple containing a boolean indicating success, a message, and None.
        """
        try:
            start_time = time.time()
            self.logger.info(f"Initiating packet capture object: {pkt_capture_output_file}, Interfaces: {pkt_capture_interface}")
            tshark_command = f"sudo tshark -w {pkt_capture_output_file} "
            if pkt_capture_duration:
                tshark_command += f" -a duration:{pkt_capture_duration} "
            if pkt_capture_interface:
                interface_option_string = ' '.join(
                    [f"-i {each_interface}" for each_interface in pkt_capture_interface]
                )
                tshark_command += interface_option_string
            if num_of_pkt_to_capture > 0:
                tshark_command += f" -c {num_of_pkt_to_capture}"
            self.logger.info(f"Starting packet capture, Command: {tshark_command}")
            pkt_capture_result = subprocess.run(
                args=tshark_command,
                stderr=subprocess.PIPE,
                stdout=subprocess.PIPE,
                shell=True, 
            )
            self.logger.info(f"Packet capture return code: {pkt_capture_result.returncode}")
            self.logger.info(f"Packet capture stdout: {pkt_capture_result.stdout}")
            if pkt_capture_result.returncode != 0:
                self.logger.error(
                    f"Packet capture failed with return code {pkt_capture_result.returncode}"
                )
                self.logger.error(f"Packet capture stderr: {pkt_capture_result.stderr}")
                return False, f"Packet capture failed with return code {pkt_capture_result.returncode}\nPacket capture stderr: {pkt_capture_result.stderr}", None


            # Lines below are hashed out for debugging purposes, future plan is to utilize pyshark but has trouble with threading
            
            # pkt_capture = pyshark.LiveCapture(
            #     interface=pkt_capture_interface,
            #     output_file=pkt_capture_output_file
            # )
            # pkt_capture.sniff(
            #     packet_count=num_of_pkt_to_capture,
            #     timeout=pkt_capture_duration
            # )
            # pkt_capture.clear()
            # self._packets=[]
            # def pkt_capture_callback(pkt):
            #     self._packets.append(pkt)
            # pkt_capture.apply_on_packets(
            #     callback=pkt_capture_callback,
            #     packet_count=num_of_pkt_to_capture,
            #     timeout=pkt_capture_duration
            # )
            # print("async_pkt_capture : ", pkt_capture_coroutine_object)
            # packet_capture_length = len(pkt_capture)
            
        except Exception as error:
            self.logger.error(f"Exception occured during capturing packets: {error}")
            return False, f"Exception occured during capturing packets: {error}", None
        else:
            end_time = time.time()
            self.logger.info(f"Successfully captured packets, Total time taken: {start_time - end_time}")
            return True, f"Successfully captured packets, Total time taken: {start_time - end_time}", None
    
    def read_packet_from_pcapng_file(
        self,
        pkt_capture_file:str,
        filter:str=None,
    )->tuple[bool, str, list|None]:
        """
        Reads packets from a pcapng file using pyshark.

        Args:
        - pkt_capture_file (str): The path to the pcapng file.
        - filter (str, optional): A display filter to apply to the packets. Defaults to None.

        Returns:
        - tuple[bool, str, list | None]: A tuple containing a boolean indicating success, a message, and a list of packets or None.
        """
        try:
            self.logger.info(f"Initialising packet capture object from: {pkt_capture_file}")
            self.logger.info(f"Search Filter: {filter}")
            pkt_capture = pyshark.FileCapture(
                keep_packets = True,
                input_file=pkt_capture_file,
                display_filter=filter,
            )
            pkt_capture.load_packets()
        except Exception as error:
            self.logger.error(f"Exception occured during creating packet capture object: {error}")
            return False, f"Exception occured during creating packet capture object: {error}", None
        else:
            self.logger.info(f"Successfully initialized packet capture object, type: {type(pkt_capture)}")
            return True, f"Successfully initialized packet capture object, type: {type(pkt_capture)}", pkt_capture._packets
    
    def fetch_capturable_interfaces(
        self
    )->tuple[bool, str, list[str]|None]:
        """
        Fetch list of interfaces that tshark can capture packets from.
        """

        try:
            command = ["tshark","-D"]
            # output = os.popen(cmd=command)
            output = subprocess.run(
                args=command,
                capture_output=True,
                text=True
            )
            self.logger.info(f"Fetch capturable interfaces return code: {output.returncode}")
            self.logger.info(f"Fetch capturable interfaces stdout: {output.stdout}")
            if output.returncode != 0:
                self.logger.error(
                    f"Fetch capturable interfaces failed with return code {output.returncode}"
                )
                self.logger.error(f"Fetch capturable interfaces stderr: {output.stderr}")
            interface_output = output.stdout.strip()
            interface_list = interface_output.split("\n")
            interfaces_to_exclude = ["ciscodump", "randpkt", "sshdump", "udpdump", "wifidump"]
            interface_list = [each_interface.split(" ")[1] for each_interface in interface_list if each_interface.split(" ")[1] not in interfaces_to_exclude]
            
        except Exception as error:
            self.logger.error(f"Exception occured when fetching list of interfaces: {error}")
            return False, f"Exception occured when fetching list of interfaces: {error}", None
        else:
            self.logger.info(f"Fetched interface list via tshark -D: {interface_list}")
            return True, f"Fetched interface list via tshark -D: {interface_list}", interface_list
        


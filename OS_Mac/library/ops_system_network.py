import os
import time
import subprocess
from common_lib.common import constants
from common_lib.common.logger import Logger
import logging



class SysOpsNetworkSetup:
    def __init__(self, log_handle:logging.Logger=None):
        self.logger = (log_handle if log_handle else Logger.initialize_logger("SysOps.log", log_level="DEBUG"))
        self.const = constants.Utils
    
    def fetch_network_services(self)->tuple[bool, str, list|None]:
        """
        Returns a list of network services available in the system
        """
        list_network_services_command = "networksetup -listallnetworkservices"
        try:
            output = subprocess.run(list_network_services_command, shell=True, stdout=subprocess.PIPE)
            if output.returncode!=0:
                raise Exception(output.stdout)
            network_services_list = str(output.stdout).split('\\n')[1:-1:]
        except Exception as e:
            self.logger.info(f"Exception when listing network services: {e}")
            return False, f"Exception when listing network services: {e}", None
        else:
            return True, "Successfully listed all network services", network_services_list
    
    def fetch_network_service_order(self)->tuple[bool, str, dict[dict]|None]:
        """
        Main usage of this function is to find the name, service order of the default interface.
        The usage would be like below:
            1. Find the default interface using a different method
            2. Use the default interfaces name ie 'en9' to fetch its hardware name / network service & service order
        Returns a dictionary of the format:
            en9 - Interface Name, Needs to be fetched separately
            hardware_name - network service name used by networksetup to change anything related to it
            service_order - indicates what priority is assigned to each network service if its connected

        {
            'en9': 
                {
                    'hardware_name': 'USB 10/100/1G/2.5G LAN', 
                    'service_order': 1
                }
        }

        """
        list_network_services_order_command = "networksetup -listnetworkserviceorder"
        try:
            output = subprocess.run(list_network_services_order_command, shell=True, stdout=subprocess.PIPE)
            if output.returncode!=0:
                raise Exception(output.stdout)
            network_services_order_list = str(output.stdout).split('\\n')[2::3]
            network_service_info = {}
            for each_network_service in network_services_order_list:
                service_order = network_services_order_list.index(each_network_service) + 1
                each_network_service = each_network_service.replace("(","").replace(")","").split(", ")
                hardware_port_name = each_network_service[0].split(": ")[1]
                interface_name = each_network_service[1].split(": ")[1]
                each_network_service_info = {
                    interface_name : {
                        "hardware_name" : hardware_port_name,
                        "service_order" : service_order
                    }
                }
                network_service_info.update(each_network_service_info)
        except Exception as e:
            self.logger.info(f"Exception when listing network services order: {e}")
            return False, f"Exception when listing network services order: {e}", None
        else:
            self.logger.info("Successfully listed all network services order")
            self.logger.info(network_service_info)
            return True, "Successfully listed all network services order", network_service_info

    def fetch_hardware_port_info(
        self, 
        network_service_name:str,   # Name of the interface
    )->tuple[bool, str, dict|None]:
        """
        Returns the details of a specific network service, exact name of the network service needs to be given
        
        It is essential to find out what port is currently connected, before calling this method
        
        Example: 
            network_service_name = "USB 10/100/1000 LAN"
            network_service_name = "Wi-Fi"
    
        Returned output format:
            Note: Some keys maybe available only if the port is connected
            {
                'configuration_type': '', 
                'ip_address': '', 
                'subnet_mask': '', 
                'router': '', 
                'ipv6': '', 
                'ipv6_ip_address': '', 
                'ipv6_router': '', 
                'ethernet_address': ''
            }
        """
        get_hardware_port_info_command = f'networksetup -getinfo "{network_service_name}"'
        try:
            output = subprocess.run(get_hardware_port_info_command, shell=True, stdout=subprocess.PIPE)
            if output.returncode!=0:
                raise Exception(output.stdout)
            hardware_port_info_list = str(output.stdout).split('\\n')
            hardware_port_info = {}
            for each_hardware_port_info in hardware_port_info_list:
                each_hardware_port_info = each_hardware_port_info.lower()
                if "configuration" in each_hardware_port_info:
                    key = "configuration_type"
                    value = each_hardware_port_info.split(" ")[0].replace("b'","")
                
                if ": " in each_hardware_port_info:
                    key = each_hardware_port_info.split(": ")[0].replace(" ","_")
                    value = each_hardware_port_info.split(": ")[1]
                    if key == "wi-fi_id":
                        key = "ethernet_address"

                hardware_port_info.update({key:value})

        except Exception as e:
            self.logger.info(f"Exception when listing hardware port info: {e}")
            return False, f"Exception when listing hardware port info: {e}", None
        else:
            self.logger.info("Successfully listed all hardware port info")
            self.logger.info(hardware_port_info)
            return True, "Successfully listed all hardware port info", hardware_port_info

    def switch_to_ipv6_automatic(
        self, 
        network_service_name:str,   # Name of the interface
    )->tuple[bool, str, None]:
        """
        Switch the given network service to IPv6 automatic
        """
        switch_to_ipv6_command = f"networksetup -setv6automatic {network_service_name}"
        try:
            output = subprocess.run(switch_to_ipv6_command, shell=True, stdout=subprocess.PIPE)
            if output.returncode!=0:
                raise Exception(output.stdout)
            self.logger.info(f"Output of IPv6 automatic: {output}")
        except Exception as e:
            self.logger.error(f"Exception occured when switching to IPv6 automatic: {e}")
            return False, f"Exception occured when switching to IPv6 automatic: {e}", None
        else:
            self.logger.info("Successfully switched to IPv6 automatic")
            return True, "Successfully switched to IPv6 automatic", None
    
    def switch_to_ipv6_manual(
        self, 
        network_service_name    :str,   # Name of the interface
        ipv6_address            :str,   # Ipv6 address to be assigned to the interface
        prefix_length           :str,   # Prefix length 
        router                  :str,   # Router's address
    )->tuple[bool, str, None]:
        """
        Switch the given network service to IPv6 manual
        """
        switch_to_ipv6_command = f"networksetup -setv6manual {network_service_name} {ipv6_address} {prefix_length} {router}"
        try:
            output = subprocess.run(switch_to_ipv6_command, shell=True, stdout=subprocess.PIPE)
            if output.returncode!=0:
                raise Exception(output.stdout)
            self.logger.info(f"Output of IPv6 manual: {output}")
        except Exception as e:
            self.logger.error(f"Exception occured when switching to IPv6 manual: {e}")
            return False, f"Exception occured when switching to IPv6 manual: {e}", None
        else:
            self.logger.info("Successfully switched to IPv6 manual")
            return True, "Successfully switched to IPv6 manual", None
        
    def switch_to_ipv6_linklocal(
        self,
        network_service_name:str,   # Name of the interface
    )->tuple[bool, str, None]:
        """
        Switch the given network service to IPv6 linklocal
        """
        switch_to_ipv6_command = f"networksetup -setv6linklocal {network_service_name}"
        try:
            output = subprocess.run(switch_to_ipv6_command, shell=True, stdout=subprocess.PIPE)
            if output.returncode!=0:
                raise Exception(output.stdout)
            self.logger.info(f"Output of IPv6 linklocal: {output}")
        except Exception as e:
            self.logger.error(f"Exception occured when switching to IPv6 linklocal: {e}")
            return False, f"Exception occured when switching to IPv6 linklocal: {e}", None
        else:
            self.logger.info("Successfully switched to IPv6 linklocal")
            return True, "Successfully switched to IPv6 linklocal", None

    def switch_to_ipv6_off(
        self, 
        network_service_name:str,   # Name of the interface
    )->tuple[bool, str, None]:
        """
        Switch the given network service to IPv6 off
        """
        switch_to_ipv6_command = f"networksetup -setv6off {network_service_name}"
        try:
            output = subprocess.run(switch_to_ipv6_command, shell=True, stdout=subprocess.PIPE)
            if output.returncode!=0:
                raise Exception(output.stdout)
            self.logger.info(f"Output of IPv6 off: {output}")
        except Exception as e:
            self.logger.error(f"Exception occured when switching to IPv6 off: {e}")
            return False, f"Exception occured when switching to IPv6 off: {e}", None
        else:
            self.logger.info("Successfully switched to IPv6 off")
            return True, "Successfully switched to IPv6 off", None

    def switch_to_ipv4_automatic(
        self, 
        network_service_name    :str,       # Name of the interface
        client_id               :str="",    # Client Id
    )->tuple[bool, str, None]:
        """
        Switch the given network service to IPv4 automatic
        """
        switch_to_ipv6_command = f"networksetup -setdhcp {network_service_name} {client_id}"
        try:
            output = subprocess.run(switch_to_ipv6_command, shell=True, stdout=subprocess.PIPE)
            if output.returncode!=0:
                raise Exception(output.stdout)
            self.logger.info(f"Output of IPv4 automatic: {output}")
        except Exception as e:
            self.logger.error(f"Exception occured when switching to IPv4 automatic: {e}")
            return False, f"Exception occured when switching to IPv4 automatic: {e}", None
        else:
            self.logger.info("Successfully switched to IPv4 automatic")
            return True, "Successfully switched to IPv4 automatic", None
    
    def switch_to_ipv4_manual(
        self, 
        network_service_name    :str,   # Name of the interface
        ipv4_address            :str,   # Ipv4 address to be assigned to the interface
        subnet                  :str,   # Subnet address 
        router                  :str,   # Router address
    )->tuple[bool, str, None]:
        """
        Switch the given network service to IPv4 manual
        """
        switch_to_ipv6_command = f"networksetup -setmanual {network_service_name} {ipv4_address} {subnet} {router}"
        try:
            output = subprocess.run(switch_to_ipv6_command, shell=True, stdout=subprocess.PIPE)
            if output.returncode!=0:
                raise Exception(output.stdout)
            self.logger.info(f"Output of IPv4 manual: {output}")
        except Exception as e:
            self.logger.error(f"Exception occured when switching to IPv4 manual: {e}")
            return False, f"Exception occured when switching to IPv4 manual: {e}", None
        else:
            self.logger.info("Successfully switched to IPv4 manual")
            return True, "Successfully switched to IPv4 manual", None

    def switch_to_ipv4_off(
        self, 
        network_service_name:str,   # Name of the interface
    )->tuple[bool, str, None]:
        """
        Switch the given network service to IPv4 off
        """
        switch_to_ipv6_command = f"networksetup -setv4off {network_service_name}"
        try:
            output = subprocess.run(switch_to_ipv6_command, shell=True, stdout=subprocess.PIPE)
            if output.returncode!=0:
                raise Exception(output.stdout)
            self.logger.info(f"Output of IPv4 off: {output}")
        except Exception as e:
            self.logger.error(f"Exception occured when switching to IPv4 off: {e}")
            return False, f"Exception occured when switching to IPv4 off: {e}", None
        else:
            self.logger.info("Successfully switched to IPv4 off")
            return True, "Successfully switched to IPv4 off", None

    def get_dns_servers(
        self, 
        network_service_name:str,   # Name of the interface
    )->tuple[bool, str, list|None]:
        """
        Note: networksetup way of fetching DNS server works only for systems with manually assigned DNS settings, If DNS is assigned via DHCP then the DNS entries are not fetched
        """
        get_dns_command = f"networksetup -getdnsservers {network_service_name}"

        try:
            output = subprocess.run(get_dns_command, shell=True, stdout=subprocess.PIPE)
            if output.returncode!=0:
                raise Exception(output.stdout)
            self.logger.info(f"Output of get dns servers: {output}")
        except Exception as e:
            self.logger.error(f"Exception occured when fetching dns servers: {e}")
            return False, f"Exception occured when fetching dns servers: {e}", None
        else:
            self.logger.info(f"Successfully fetched dns servers for {network_service_name}")
            return True, f"Successfully fetched dns servers for {network_service_name}", None

    def fetch_ip_via_nslookup(
        self, 
        application:str,            # Application to which nslookup should be performed 
        query_type:str="a",         # Type of nslookup query, 'a' or 'aaaa' 
        external_dns_server:str="", # Pass the dns server via which nslookup must be performed
        is_via_tcp:bool=False,      # Should the dns request be performed via TCP, by default UDP
    )->tuple[bool, str, dict|None]:
        
        nslookup_command = f"nslookup -q={query_type} {application} {external_dns_server}"
        if is_via_tcp:
            nslookup_command = f"nslookup -vc -q={query_type} {application} {external_dns_server}"
            
        try:
            self.logger.info(f"Fetching IP via nslookup for application: {application}, dns query type: {query_type.upper()}")
            output = subprocess.run(nslookup_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
            output_list = str(output.stdout).replace("b'","").replace("\\t"," ").split("\\n")
        except Exception as e:
            self.logger.info(f"Exception occured when fetching IP via nslookup for application: {application}, dns query type: {query_type.upper()}")
            return False, f"Exception occured when fetching IP via nslookup for application: {application}, dns query type: {query_type.upper()}", None
        else:
            dns_server = ':'.join(output_list[0].split(":")[1:]).strip()
            dns_server_address = ':'.join(output_list[1].split(":")[1:]).strip()
            resolved_ips = []
            cname_output_dictionary = {}
            txt_records = []
            if query_type == "a":
                split_at_text = "address:"
            elif query_type == "aaaa":
                split_at_text = "has aaaa address"
            elif query_type == "txt":
                split_at_text = "text = "
            elif query_type == "cname":
                split_at_text = " = "
            
            for each_line in output_list[3:]:
                each_line = each_line.lower()
                if split_at_text in each_line:
                    value = each_line.split(split_at_text)[1].strip()
                    if query_type == "cname":
                        key = each_line.split(split_at_text)[0].strip().replace(" ","_")
                        cname_output_dictionary[key] = value
                    else:
                        if query_type == "txt":
                            value = value.replace('"','')
                            txt_records.append(value)
                        else:
                            resolved_ips.append(value)
            
            output_dictionary = {
                "dns_server":dns_server,
                "dns_server_address":dns_server_address,
                "resolved_ips":resolved_ips,
                "txt_records":txt_records if query_type == "txt" else None,
                "cname":cname_output_dictionary if query_type == "cname" else None
            }
            self.logger.info(f"Successfully resolved & fetched result for {application}, query type: {query_type}")
            self.logger.info(f"Output: {output_dictionary}")
            return True, f"Successfully resolved & fetched result for {application}, query type: {query_type}", output_dictionary
    
    def get_proxy_status(
        self, 
        network_service_name:str,   # Name of the interface
    )->tuple[bool, str, dict|None]:
        """
        Returns the details of Auto Proxy Status in the following format:
            {
                'proxy_url': '', 
                'enablement_status': '', 
            }
        
        Note:
        It is essential to find out what interface is currently connected, before calling this method
        So, we pass "network_service_name" as an argument to this method.
        Example of Network Service Name and how it appears on CLI are as follows:
            network_service_name = "USB 10/100/1000 LAN"
            network_service_name = "Wi-Fi"
       """
       
        get_proxy_cli = f'networksetup -getautoproxyurl "{network_service_name}"'
        try:
            output = subprocess.run(get_proxy_cli, shell=True, stdout=subprocess.PIPE)
            if output.returncode!=0:
                raise Exception(output.stdout)
            proxy_info_list = str(output.stdout).split('\\n')
            proxy_info = {"proxy_url":'', "enablement_status":''}
            for each_proxy_info in proxy_info_list:
                #each_proxy_info = each_proxy_info.lower()
                if "URL" in each_proxy_info:
                    proxy_info["proxy_url"] = each_proxy_info.split(": ")[1]
                if "Enabled" in each_proxy_info:
                    proxy_info["enablement_status"] = each_proxy_info.split(": ")[1]

        except Exception as e:
            self.logger.info(f"Exception when listing proxy info: {e}")
            return False, f"Exception when listing proxy info: {e}", None

        else:
            self.logger.info("Successfully listed all proxy info")
            self.logger.info(proxy_info)
            return True, "Successfully listed all proxy info", proxy_info
        
    def set_proxy_status(
        self,
        network_service_name:str,   # Name of the interface
        pac_url:str, #pac_url to be set in system settings via CLI 
    )->tuple[bool, str, dict|None]:
        """
            This function is used to execute the command to set Proxy URL (PAC) in System Settings under Autoproxy
            CLI Executed: 'networksetup -setautoproxyurl "{network_service_name}" "{pac_url}" 
            It returns a Tuple as follows:
            return: (True/False, Custom Success/Failure Message, Boolean 1:0)
        """
        
        set_proxy_cli = f'networksetup -setautoproxyurl "{network_service_name}" "{pac_url}"'        
        self.logger.info("CLI executed to Set Proxy is: " + set_proxy_cli)
        
        try:
            output = subprocess.run(set_proxy_cli,shell=True,stdout=subprocess.PIPE)
            self.logger.info(output)

            if output.returncode!=0:
                self.logger.info("Set Proxy Command NOT Implemented")
                return(False,"Set Proxy Command NOT Implemented", 0)
            else:
                self.logger.info("Set Proxy Command Implemented")
                return(True,"Set Proxy Command Implemented", 1)

        except Exception as e:
            self.logger.info(f"Exception while setting proxy: {e}")
            return False, f"Exception while setting proxy: {e}", None

        


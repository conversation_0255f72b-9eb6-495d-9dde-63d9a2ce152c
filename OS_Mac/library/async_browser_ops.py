import copy
import time, os
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>
from functools import wraps
from OS_Mac.library.ops_system import SysOps 
from common_lib.common import log_ops
from common_lib.helper_function import *
from common_lib.common.logger import Logger



class AsyncBrowserOps():
    def __init__(
        self,
        log_handle: bool = None
    ):
        self.logger = (log_handle if log_handle else Logger.initialize_logger("Browser_Ops.log", log_level="INFO"))
        self.sys_ops = SysOps(log_handle=self.logger)
        self.log_ops=log_ops.LogOps(log_handle=self.logger)
        self.playwright_context_manager = None

    async def initialize_browser(
        self,
        browser:str="chrome",
        is_headless:bool=False
    )->tuple[bool, str, Browser|None]:
        """
        Initializes a browser object using the Playwright library.

        Args:
            browser (str): The type of browser to initialize (chrome, firefox, webkit).
            is_headless (bool): Whether to launch the browser in headless mode.

        Returns:
            tuple: A tuple containing a boolean success indicator, a descriptive message, and the initialized browser object.
        """
        try:
            self.playwright_context_manager = await async_playwright().start()
            if browser == "chrome":
                browser_object = await self.playwright_context_manager.chromium.launch(
                    headless=is_headless
                )
            elif browser == "firefox":
                browser_object = await self.playwright_context_manager.firefox.launch(
                    headless=is_headless
                )
            elif browser == "webkit":
                browser_object = await self.playwright_context_manager.webkit.launch(
                    headless=is_headless
                )  
            
        except Exception as error:
            self.logger.error(f"Unable to initialize browser object: {error}")
            return False, f"Unable to initialize browser object: {error}", None
        else:
            self.logger.info("Initialized browser object")
            return True, "Initialized browser object", browser_object

    async def initialize_page(
        self,
        browser_object:Browser
    )->tuple[bool, str, Page|None]:
        """
        Initializes a new page object within a given browser object.

        Args:
            browser_object (Browser): The browser instance in which to create a new page.

        Returns:
            tuple: A tuple containing a boolean success indicator, a descriptive message, and the initialized page object.
        """
        try:
            page_object = await browser_object.new_page()
        except Exception as error:
            self.logger.error(f"Unable to initialize a page: {error}")
            return False, f"Unable to initialize a page: {error}", None
        else:
            self.logger.info("Initialized page object")
            return True, "Initialized page object", page_object

    async def close_browser(
        self,
        browser_object:Browser
    )->tuple[bool, str, None]:
        """
        Closes a given browser object and stops the Playwright context manager.

        Args:
            browser_object (Browser): The browser instance to be closed.

        Returns:
            tuple: A tuple containing a boolean success indicator, a descriptive message, and None.
        """
        try:
            await browser_object.close()
            await self.playwright_context_manager.stop()
        except Exception as error:
            self.logger.error(f"Unable to close browser: {error}")
            return False, f"Unable to close browser: {error}", None
        else:
            self.logger.info("Closed browser successfully")
            return True, "Closed browser successfully", None

    async def open_page(
        self,
        url:str
    )->tuple[bool, str, None]:
        """
        Opens a given URL in a Chrome browser, retrieves the page title, and then closes the browser.

        Args:
            url (str): The URL to be opened.

        Returns:
            tuple: A tuple containing a boolean success indicator, a descriptive message, and None.
        """
        try:
            browser_object = await self.initialize_browser(browser="chrome", is_headless=False)
            browser_object = browser_object[-1]
            page = await self.initialize_page(browser_object=browser_object)
            page = page[-1]
            await page.goto(url)
            page_title = await page.title()
            self.logger.info(f"Page Title: {page_title}")
            time.sleep(5)
            await self.close_browser(browser_object=browser_object)
        except Exception as error:
            self.logger.info(f"Unable to open page to {url}, Error: {error}")
            return False, f"Unable to open page to {url}, Error: {error}", None
        else:
            self.logger.info(f"Successfully opened page to {url}")
            return True, f"Successfully opened page to {url}", None
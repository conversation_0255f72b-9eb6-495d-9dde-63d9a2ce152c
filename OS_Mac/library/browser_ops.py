import copy
import time, os
import pyautogui
import subprocess
import pyautogui
from playwright.sync_api import sync_playwright, <PERSON><PERSON>er
from functools import wraps
from selenium.webdriver.common.by import By
from OS_Mac.library.ops_system import SysOps 
from common_lib.common import logger,log_ops
from common_lib.helper_function import *
from common_lib.common.logger import Logger
from selenium import webdriver
from selenium.webdriver.common.keys import Keys 
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options


class BrowserElements:
    BROWSER_OKTA_USERNAME = '//*[@id="input28"]'   
    BROWSER_OKTA_PASSWORD = '//*[@id="input36"]'
    BROWSER_OKTA_SIGN_BUTTON = '//*[@id="form20"]/div[2]/input'
    BROWSER_OKTA_LAUNCH_BUTTON = '//*[@id="button"]'
    BROWSER_OKTA_LOGIN_HINT_PASSWORD='//*[@id="input29"]' 
    BROWSER_OKTA_LOGIN_HINT_VERIFY_BUTTON='//*[@id="form21"]/div[2]/input'
    BROWSER_OKTA_VERIFY_TEXT_LABEL='//*[@id="form21"]/div[1]/h2'

class BrowserOps():
    def __init__(
        self,
        log_handle: bool = None
    ):
        self.logger = (log_handle if log_handle else Logger.initialize_logger("Browser_Ops.log", log_level="INFO"))
        self.initialize_elements()
        self.sys_ops = SysOps(log_handle=self.logger)
        self.log_ops=log_ops.LogOps(log_handle=self.logger)
        self.playwright_context_manager = None

    def initialize_elements(self):  # initialize elements for win/mac
        self.elements = copy.deepcopy(BrowserElements)
        return

    def initialize_browser(
        self,
        browser:str="chrome",
        is_headless:bool=False
    ):
        try:
            self.playwright_context_manager = sync_playwright().start()
            if browser == "chrome":
                browser_object = self.playwright_context_manager.chromium.launch(
                    headless=is_headless
                )
            elif browser == "firefox":
                browser_object = self.playwright_context_manager.firefox.launch(
                    headless=is_headless
                )
            elif browser == "webkit":
                browser_object = self.playwright_context_manager.webkit.launch(
                    headless=is_headless
                )  
            
        except Exception as error:
            self.logger.error(f"Unable to initialize browser object: {error}")
            return False, f"Unable to initialize browser object: {error}", None
        else:
            self.logger.info("Initialized browser object")
            return True, "Initialized browser object", browser_object
    
    def initialize_page(
        self,
        browser_object:Browser
    ):
        try:
            page_object = browser_object.new_page()
        except Exception as error:
            self.logger.error(f"Unable to initialize a page: {error}")
            return False, f"Unable to initialize a page: {error}", None
        else:
            self.logger.info("Initialized page object")
            return True, "Initialized page object", page_object

    def close_browser(
        self,
        browser_object:Browser
    ):
        try:
            browser_object.close()
            self.playwright_context_manager.stop()
        except Exception as error:
            self.logger.error(f"Unable to close browser: {error}")
            return False, f"Unable to close browser: {error}", None
        else:
            self.logger.info("Closed browser successfully")
            return True, "Closed browser successfully", None
    
    def open_page(
        self,
        url:str
    ):
        try:
            browser_object = self.initialize_browser(browser="chrome")[-1]
            page = self.initialize_page(browser_object=browser_object)[-1]
            page.goto(url)
            self.logger.info(f"Page Title: {page.title()}")
            self.close_browser(browser_object=browser_object)
        except Exception as error:
            self.logger.info(f"Unable to open page to {url}, Error: {error}")
            return False, f"Unable to open page to {url}, Error: {error}", None
        else:
            self.logger.info(f"Successfully opened page to {url}")
            return True, f"Successfully opened page to {url}", None

    def perform_browser_based_auth(self, username: str, password: str, chrome:bool,use_login_hint:bool=False,zia_saml_login:bool=False)->tuple[bool,str,str]:

        """
        This function handles browser based authentication for ZCC

        as of now it supports chrome browser only , so user has to pass chrome as a parameter to start the execution

        Args:
        username (str): Username of the user to log in to ZPA 
        password (str): Password of the user to log in to ZPA
        chrome (bool): Boolean indicating if chrome browser is used or not.
        use_login_hint (bool) : Boolean indicating if login_hint is used or not.
        zia_saml_login (bool): Boolean indicating if saml login is enabled for zia if enabled the steps to enter creds wont be needed again.

        :return: A tuple containing a boolean and a string. The boolean indicates success or failure, and the string provides additional information.


        """
        if chrome:
            try:
                time.sleep(10)
                
                self.logger.info("Connecting to an exsisting browser session")
                with sync_playwright() as playwright:
                    browser = playwright.chromium.connect_over_cdp('http://localhost:9222/')
                    # browser = self.connect_to_browser()
                    # if browser[0]==False:
                    #     self.logger.error(browser[1])
                    #     return browser
                    # else:
                    #     playwright_obj=browser[-1][0]
                    #     browser=browser[-1][1]
                    defaultContext = browser.contexts[0]
                    page = defaultContext.pages[0]
                    self.logger.info("Sleeping for 10 seconds to be safe")
                    time.sleep(10)
                    if not zia_saml_login:
                        if use_login_hint:
                            self.logger.info("Clicking on verify text label")
                            page.locator(self.elements.BROWSER_OKTA_VERIFY_TEXT_LABEL).click()
                            self.logger.info("Fill in the password")
                            page.locator(self.elements.BROWSER_OKTA_LOGIN_HINT_PASSWORD).fill(password)
                            self.logger.info("Click on the verify button")
                            page.locator(self.elements.BROWSER_OKTA_LOGIN_HINT_VERIFY_BUTTON).click()
                        else:
                            self.logger.info("Fill in the username")
                            page.locator(self.elements.BROWSER_OKTA_USERNAME).fill(username)
                            self.logger.info("Fill in the password")
                            page.locator(self.elements.BROWSER_OKTA_PASSWORD).fill(password)
                            self.logger.info("Click on the verify button")
                            page.locator(self.elements.BROWSER_OKTA_SIGN_BUTTON).click()
                    self.logger.info("URL:" ,page.url)
                    self.logger.info("Click in the launch button")
                    page.locator(self.elements.BROWSER_OKTA_LAUNCH_BUTTON).click()
                    self.logger.info("Clicking Open Zscaler on dialog box")
                    time.sleep(10)
                    pyautogui.press('left')
                    pyautogui.press('space')
                # browser.close()
                # playwright_obj.stop()
                return True, f"Browser Based Auth Login Success", None 
            except Exception as e:
                return False, f"Browser Based Auth Login Failed :: {e}", None
        
        else:
            return False,"No browser is chosen to do the authentication",None
        
    def launch_browser(self,port_no:str=None,browser_path:str=None)->tuple[bool,str,str]:
        """
        This function launches a browser window at certain port number .
        Args:
        port_no(str): Port No at which the browser should be launched
        browser_path(str): The path of the browser which is to be launched.
        """
        try:
            self.logger.info("Opening a browser window in debug mode at defined port no")
            subprocess.Popen(['open','OS_Mac/resource/launch_browser_debugger.command'])
            return True, "Opened a browser window sucessfully", None
        except Exception as e:
            return False,"Failed to open a browser window",None

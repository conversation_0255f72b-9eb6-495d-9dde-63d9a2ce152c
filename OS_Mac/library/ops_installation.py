################################################################
#               DOCUMENTATION FOR GUI.PY                       #
#                                                              #
# AUTHOR : SRINIVASA BHASWANTH (<EMAIL>)        #
################################################################
__author__ = '{srinivasa bhaswanth}'
__credits__ = ['{srinivasa bhaswanth, abhimanyu sharma, sahil kumar}']
__email__ = '{<EMAIL>}'

######  Module ##############################################################################################################################################
#from concurrent.futures import process
import subprocess
import os
from common_lib.common import constants
from common_lib.common.logger import Logger
import time


##############################################################################################################################################################

class InstallUninstall:
    """
    Base class to be used for MA/ZIA related actions
    any class can import this for performing its own set of MA actions
    Environment
    ------------
        1. operating_system     (str)   name of operating system ('mac')
        2. const                (class) a class of variables used through out this script
    Work Flow
    ----------
        1. There are 2 important Central Functions
            a. install_zcc()
            b. uninstall_zcc()
        2. these functions can be used with various parameters to install/uninstall zcc on mac
    """

    def __init__(self, verify_installers_exist=False, log_handle=None):
        self.const = constants.Utils
        if log_handle is None:
            self.logger = Logger.initialize_logger("Install_Uninstall.log", log_level="DEBUG")
        else:
            self.logger = log_handle
        self.zapp_archive_path = os.path.join(os.getcwd(), "OS_Mac", "resource")
        self.is_zcc_installed = self.check_if_zcc_exists()[0]
        self.architecture = None
        if verify_installers_exist:
            assert self.verify_installers_exist()[0]
        self.recently_installed_zcc_version = None
        assert os.path.exists(self.zapp_archive_path)
        self.logger.info("in Install_Uninstall")

    def list_all_available_zcc_installers(self):  # Function to list all available installers in archive
        """
        Work Flow
        ----------
            1. check if zapp_archive folder exists
            2. if yes, return all available installers for that operating system
        """
        assert os.path.exists(self.zapp_archive_path)
        installers = [files for files in sorted(os.listdir(self.zapp_archive_path), reverse=True) if (".app" in files)]
        if len(installers) == 0:
            self.logger.error("no installers present")
            return (False, "Error : No installer found Check Resource Folder", None)
        return (True, "Success : Installer Found", installers)

    def verify_installers_exist(self):  # Function to make sure all prerequisites exist already
        """
        Work Flow
        ----------
            1. make sure zapp_archive folder exists
            2. make sure all versions of zapp exist as specified in CONST
        """
        assert os.path.exists(self.zapp_archive_path)
        for each in [self.const.LATEST_ZCC_APP, self.const.OLD_ZCC_APP]:
            if os.path.exists(self.zapp_archive_path + str(each)) is False:
                return (False, "Error : Installer missing, Check Resource Folder", None)
        return (True, "Success : Installer Found", None)

    def install_zcc(self,  # Function to install zcc on Mac
                    zcc_version: str = "latest",
                    # (str)     select version to install zcc (latest/older/custom_zcc_installer_name)
                    install_only_if_uninstalled: bool = False,
                    # (bool)    verify zcc installation doesn't exist before installation
                    expect_error_popups: bool = False,
                    # (bool)    Expect any error popups if installation command fails, only applicable if installation is done without unattended mode
                    command_timeout: int = 120,
                    # (int)     Set timeout value for subprocess to get out of the stuck state, incase installation is not successfull
                    command_mode: str="unattended",
                    #(str) command mode under which zcc is to be installed
                    pkg: bool=False,
                    #(bool) if the installation is done by pkg file
                    **kwargs):  # (**) All the keys and values of kwargs are appended into the installation command in the format "--key value"
        """
        Work Flow
        ----------
            1. make sure zcc doesn't exist
            2. if zcc_version is "older" or "latest", install as specified by consts
            3. otherwise expect a custom installer to be used for installation
            4. before installation make sure these installers exist in zapp_archive
            5. when required, perform background process check for windows only to see installer is running
            6. sleep for sometime
            7. make sure zcc exists
            8. for recent zcc versions, app/uninstall shortcuts are different. place them in right folders properly
        """
        try:
            self.logger.info(f"Starting installation of ZCC")

            installation_command = ['start "" ']

            if install_only_if_uninstalled:
                assert not self.check_if_zcc_exists()[0]

            if pkg==True:
                if zcc_version == "latest" or zcc_version == "older":
                    self.const.CURRENT_INSTALLED_VERSION = str(self.const.LATEST_ZCC_PKG if zcc_version == "latest" else self.const.OLD_ZCC_PKG).split("-")[2]
                    assert os.path.exists(os.path.join(self.zapp_archive_path, str(self.const.LATEST_ZCC_PKG if zcc_version == "latest" else self.const.OLD_ZCC_PKG)))
                    zcc_installer_path = os.path.join(self.zapp_archive_path, str(self.const.LATEST_ZCC_PKG if zcc_version == "latest" else self.const.OLD_ZCC_PKG))
                    installation_command = "sudo installer -pkg {} -target /".format(zcc_installer_path)
                    self.recently_installed_zcc_version = (self.const.LATEST_ZCC_PKG if zcc_version == "latest" else self.const.OLD_ZCC_PKG)
                else:
                    assert os.path.exists(os.path.join(self.zapp_archive_path, str(zcc_version)))
                    zcc_installer_path = os.path.join(self.zapp_archive_path, str(zcc_version))
                    installation_command = "sudo installer -pkg {} -target /".format(zcc_installer_path)
                    self.recently_installed_zcc_version = zcc_version
            else:
                if zcc_version == "latest" or zcc_version == "older":
                    self.const.CURRENT_INSTALLED_VERSION = str(self.const.LATEST_ZCC_APP if zcc_version == "latest" else self.const.OLD_ZCC_APP).split("-")[2]
                    assert os.path.exists(os.path.join(self.zapp_archive_path, str(self.const.LATEST_ZCC_APP if zcc_version == "latest" else self.const.OLD_ZCC_APP)))
                    zcc_installer_path = os.path.join(self.zapp_archive_path, str(self.const.LATEST_ZCC_APP if zcc_version == "latest" else self.const.OLD_ZCC_APP))
                    installation_command = "sudo sh '{}/Contents/MacOS/installbuilder.sh' --mode {} --unattendedmodeui none".format(zcc_installer_path,command_mode)
                    self.recently_installed_zcc_version = (self.const.LATEST_ZCC_APP if zcc_version == "latest" else self.const.OLD_ZCC_APP)
                else:
                    assert os.path.exists(os.path.join(self.zapp_archive_path, str(zcc_version)))
                    zcc_installer_path = os.path.join(self.zapp_archive_path, str(zcc_version))
                    installation_command = "sudo sh '{}/Contents/MacOS/installbuilder.sh' --mode {} --unattendedmodeui none".format(zcc_installer_path,command_mode)
                    self.recently_installed_zcc_version = zcc_version

            # key word arguments part
            for each_key, each_value in kwargs.items():
                installation_command = installation_command + " --" + str(each_key) + " " + str(each_value)

            self.logger.info(f"Starting execution of installation command :: {installation_command}")

            try:
                subprocess.check_output(installation_command, shell=True)
            except Exception as e:
                raise Exception(f"An exception occured when executing {installation_command} :: ERROR :: {e}")
            else:
                self.is_zcc_installed = True
                self.logger.info(
                    "ZCC Installed successfully :: Installed Version :: {}".format(self.recently_installed_zcc_version))
        except Exception as e:
            self.logger.error(f"ZCC Installation failed :: {e}")
            return (False, f"ZCC Installation failed :: {e}", None)
        return (True, "Success, ZCC Installed Successfully", None)

    def uninstall_zcc(self,  # Function to uninstall zcc on Mac
                      uninstall_only_if_installed=False,  # (bool) verify zcc installation before uninstalling
                      ):
        """ 
        Work Flow 
        ----------
            1. find out the operating system (mac)
            2. verify if zcc installation exists already
            2. execute the cmd line command from python
            3. verify if zcc installation doesn't exist
        """
        try:
            if uninstall_only_if_installed:
                assert self.check_if_zcc_exists()[0]
            cmd = "sudo sh " + self.const.MAC_ZCC_UNINSTALLER_PATH
            self.logger.info(f"Uninstalling with command : {cmd}")
            subprocess.check_output(cmd, shell=True)
            self.is_zcc_installed = False
            self.logger.info(
                "Successfully uninstalled ZCC, Uninstalled Version :: {}".format(self.recently_installed_zcc_version))
        except Exception as e:
            self.logger.error(f"Error Uninstalling ZCC :: {e}")
            return (False, f"Error Uninstalling ZCC :: {e}", None)
        return (True, "Success : ZCC Uninstalled Successfully", None)

    def check_if_zcc_exists(self):
        """ 
        Work Flow 
        ----------
            1. find out the operating system (mac)
            2. assert if the uninstaller exists already.
            3. uninstaller existence implies ZCC installed already. same in reverse
        """
        if os.path.exists(self.const.MAC_ZCC_UNINSTALLER_PATH):
            return (True, "Success", "ZCC is installed", None)
        else:
            return (False, "Error", "ZCC is not installed", None)

    def revert_zcc_via_cli(self, zcc_version: str, mode: str = "unattended", export_logs: bool = False):
        """
        Executes the command to revert ZCC, applicable only for ZCC 4.1+, if export logs is set to true, the revert takes 7 mins approx to complete. This is an internal logic by ZCC
        """
        export_logs_value = "1" if export_logs else "0"
        zcc_installer = f"Zscaler-osx-{zcc_version}-installer.app"
        revert_command = f"sudo sh /Applications/Zscaler/RevertZcc/{zcc_installer}/Contents/MacOS/installbuilder.sh --revertzcc 1 --exportLogs {export_logs_value} --mode {mode}"

        try:
            self.logger.info(f"Starting revert using command: {revert_command}")
            process_output = subprocess.run(revert_command, shell=True)
        except Exception as e:
            self.logger.error(f"Error executing the command for reverting zcc via cli, Error: {e}")
            return False, f"Error executing the command for reverting zcc via cli, Error: {e}", None
        else:
            self.logger.info(f"Successfully executed revert command via cli, output: {process_output}")
            return True, "Successfully executed revert command via cli", None

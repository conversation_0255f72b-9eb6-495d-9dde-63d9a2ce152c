class Zcc_Elements_Mac_4_3_0_1_Below:
    """ Class that contains Zscaler Mac production build app element IDs  """
    APP         = "Zscaler"
    APP_BUNDLE  = "com.zscaler.zscaler"
    ZCC         = 'window_zs'

    # Menu Bar
    ZCC_TRAY_ICON                       = ("","//XCUIElementTypeStatusItem")
    ZCC_OPEN_MENUBAR_BUTTON             = ("show",'//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Open"]')
    ZCC_EXPORT_LOGS_MENUBAR_BUTTON      = ('exportLogs:','//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Export Logs"]')
    ZCC_REPORT_AN_ISSUE_MENUBAR_BUTTON  = ("reportAnIssue:",'//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Report An Issue"]')
    ZCC_EXIT_MENUBAR_BUTTON             = ("showExitViewController",'//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Exit"]')

    # Zscaler Build Type
    ZCC_WINDOW_PRODUCTION_BUILD_LABEL   = '//XCUIElementTypeWindow[@title="Zscaler Client Connector"]'
    ZCC_WINDOW_TEST_BUILD_LABEL         = '//XCUIElementTypeWindow[@title="Zscaler Client Connector: Test Build'

    # Notifications
    NOTIFICATIONS_TAB               = ''
    APP_MORE                        = ('btn_more', '//XCUIElementTypeButton[@label="More"]')
    CLEAR_ALL_NOTIFICATIONS         = ( "btn_notifications_clearall","//XCUIElementTypeWindow/XCUIElementTypeButton[10]")
    CLEAR_NOTIFICATIONS_CONTINUE    = ("_NS:8","//XCUIElementTypeWindow/XCUIElementTypeButton[4]")

    # APP MORE
    APP_UPDATE_POLICY                                   = ('btn_update_policy_settings', '//XCUIElementTypeWindow/XCUIElementTypeGroup[@identifier="box3_settings"]/XCUIElementTypeButton[@title="Update App"]')
    CLEAR_LOGS                                          = ('btn_clear_logs_settings', '//XCUIElementTypeWindow/XCUIElementTypeGroup[@identifier="box2_settings"]/XCUIElementTypeButton[@title="Clear Logs"]')
    RESTART_SERVICE                                     = ('lbl_restart_service_settings', '//XCUIElementTypeWindow/XCUIElementTypeGroup[@identifier="box2_settings"]/XCUIElementTypeButton[@title="Restart Service"]')
    RESTART_SERVICE_ACCEPT                              = ('btn_ok_turnoff', '//XCUIElementTypeDialog/XCUIElementTypeButton[@title="Ok"]')
    RESTART_SERVICE_CANCEL                              = ('btn_cancel_turnoff', '//XCUIElementTypeDialog/XCUIElementTypeButton[@title="Cancel"]')
    REPORT_AN_ISSUE                                     = ('btn_report_issue_settings', '//XCUIElementTypeWindow/XCUIElementTypeGroup[@identifier="box2_settings"]/XCUIElementTypeButton[@title="Report An Issue"]')
    START_PACKET_CAPTURE                                = ('btn_show_packet_settings', '//XCUIElementTypeWindow/XCUIElementTypeGroup[@identifier="box2_settings"]/XCUIElementTypeButton[@title="Start Packet Capture"]')
    STOP_PACKET_CAPTURE                                 = ('btn_show_packet_settings', '//XCUIElementTypeWindow/XCUIElementTypeGroup[@identifier="box2_settings"]/XCUIElementTypeButton[@title="Stop Packet Capture"]')
    CAPTURE_STATUS                                      = ('_NS=45', '//XCUIElementTypeStaticText[@value="Capturing ..."]')
    EXPORT_LOGS                                         = ('btn_report_issue_settings','//XCUIElementTypeButton[@title="Export Logs"]')
    ZCC_EXPORT_LOGS_MENUBAR_BUTTON                      = ''
    UPDATE_APP                                          = ('btn_update_app_settings','//XCUIElementTypeButton[@title="Update App"]')
    LICENSE_AGREEMENT                                   = 'btn_license_settings'
    LICENSE_AGREEMENT_CLOSE                             = 'btn_close_license'
    SHOW_NOTIFICATIONS_ON_APP                           = 'btn_show_notifications_turnoff_settings'
    SHOW_NOTIFICATIONS_FOR_ZPA_AUTH                     = 'btn_show_notifications_turnoff_settings'
    SHOW_NOTIFICATIONS_FOR_ZPA_AUTH_VALUE               = 'txt_notifications_settings'
    SHOW_NOTIFICATIONS_FOR_SSIT_AUTH_OFF_BUTTON         = ('btn_more_settings_ssitnotification','//XCUIElementTypeButton[@title="TURN OFF"]')
    SHOW_NOTIFICATIONS_FOR_SSIT_AUTH_ON_BUTTON          =('btn_more_settings_ssitnotification','//XCUIElementTypeButton[@title="TURN ON"]')
    SHOW_NOTIFICATIONS_FOR_SSIT_AUTH_STATUS_LABEL_ON    =('txt_ssit_notifications_settings','//XCUIElementTypeStaticText[@value="ON"]')

    # LOGIN ZIA
    USER_NAME               = ('Username', '//XCUIElementTypeTextField[@identifier="Username"]')
    LOGIN_BUTTON            = ('btn_login', '//XCUIElementTypeButton[@title="login"]')
    EULA_ACCEPT_BUTTON      = ('btn_accept_cloud_selection', '//XCUIElementTypeButton[@title="Accept"]')
    EULA_DECLINE_BUTTON     = ('btn_decline_cloud_selection', '//XCUIElementTypeButton[@title="Decline"]')
    PASSWORD_BOX            = ('Password', '//XCUIElementTypeSecureTextField[@identifier="Password"]')
    LOGIN_BUTTON_PASSWORD   = ('btn_login', '//XCUIElementTypeButton[@title="login"]')
    LOGIN_ERROR_OK_BUTTON   = ('_NS:82','//XCUIElementTypeOther[@title="Ok"]')
    ZCC_BACK_BUTTON         = ''
    INITIAL_LOGIN_BUTTON    = ('btn_login','//XCUIElementTypeButton[@label="Login"]')


    # LOGOUT
    LOGOUT_BUTTON           = ('btn_logout', '//XCUIElementTypeWindow/XCUIElementTypeButton[@identifier="btn_logout"]')
    LOGOUT_ACCEPT_BUTTON    = ('btn_ok_turnoff', '//XCUIElementTypeDialog/XCUIElementTypeButton[@title="Ok"]')
    LOGOUT_CANCEL_BUTTON    = ('btn_cancel_turnoff', '//XCUIElementTypeDialog/XCUIElementTypeButton[@title="Cancel"]')
    LOGOUT_PASSWORD_BOX     = ('txt_password_turnoff', '//XCUIElementTypeDialog/XCUIElementTypeButton[@title="Cancel"]')

    # LOGIN ZPA OKTA
    OKTA_SIGNIN_PAGE                    = ''
    OKTA_SIGNIN_PAGE_2                  = ''
    OKTA_USERNAME                       = '//XCUIElementTypeTextField'
    OKTA_PASSWORD_BOX                   = '//XCUIElementTypeSecureTextField'
    REAUTH_OKTA_USERNAME                = ''
    REAUTH_OKTA_PASSWORD_BOX            = ''
    OKTA_SIGN_IN_BUTTON                 = "//XCUIElementTypeWebView[starts-with(@label, 'okta')]/XCUIElementTypeGroup/XCUIElementTypeGroup[4]/XCUIElementTypeButton"
    ZCC_BACK_BUTTON_AT_OKTA             = ""
    OKTA_LOGIN_HINT_PASSWORD_IMAGE      = '//XCUIElementTypeImage[@label="loader"]'
    OKTA_LOGIN_HINT_VERIFY_TEXT_LABEL   = '//XCUIElementTypeStaticText[@value="Verify with your password"]'
    OKTA_LOGIN_HINT_VERIFY_BUTTON       = '//XCUIElementTypeButton[@title="Verify"]'

    # INTERNET SECURITY
    INTERNET_SECURITY_TAB                               = ('btn_internet_security', '//XCUIElementTypeButton[@label="Internet Security"]')
    INTERNET_SECURITY_POWER_BUTTON_ON                   = ('btn_turn_onoff_web_security', '//XCUIElementTypeButton[@title="TURN ON"]')
    INTERNET_SECURITY_POWER_BUTTON_OFF                  = ('btn_turn_onoff_web_security', '//XCUIElementTypeButton[@title="TURN OFF"]')
    INTERNET_SECURITY_POWER_BUTTON_OFF_ACCEPT_BUTTON    = ('btn_ok_turnoff', '//XCUIElementTypeButton[@title="Ok"]')
    INTERNET_SECURITY_POWER_BUTTON_OFF_CANCEL_BUTTON    = ('btn_cancel_turnoff', '//XCUIElementTypeButton[@title="Cancel"]')
    INTERNET_SECURITY_PASSWORD_BOX                      = ('txt_password_turnoff','//XCUIElementTypeSecureTextField')
    INTERNET_SECURITY_PASSWORD_ACCEPT_BUTTON            = ('btn_ok_turnoff', '//XCUIElementTypeButton[@title="Ok"]')
    INTERNET_SECURITY_PASSWORD_CANCEL_BUTTON            = ('btn_cancel_turnoff', '//XCUIElementTypeButton[@title="Cancel"]')
    INTERNET_SECURITY_RETRY_BUTTON                      = ('btn_turn_onoff_web_security','//XCUIElementTypeButton[@title="RETRY"]')
    INTERNET_SECURITY_DISABLE_REASON                    = ('_NS=79','//XCUIElementTypeTextView[@identifier="tv_turnoff_popup_reason"]')
    INTERNET_SECURITY_CANCEL                            = ('btn_cancel_turnoff', '//XCUIElementTypeButton[@title="Cancel"]')
    INTERNET_SECURITY_ACCEPT                            = ('btn_ok_turnoff', '//XCUIElementTypeDialog/XCUIElementTypeButton[@title="Ok"]')
    INTERNET_SECURITY_PASSWORD_FAILED_OK_BUTTON         = ''

    # PRIVATE ACCESS
    PRIVATE_ACCESS_TAB                              = ('btn_secure_access', '//XCUIElementTypeButton[@label="Private Access"]')
    PRIVATE_ACCESS_POWER_BUTTON_ON                  = ('btn_refresh_secure_access', '//XCUIElementTypeWindow/XCUIElementTypeButton[@title="TURN ON"]')
    PRIVATE_ACCESS_POWER_BUTTON_OFF                 = ('btn_refresh_secure_access', '//XCUIElementTypeWindow/XCUIElementTypeButton[@title="TURN OFF"]')
    PRIVATE_ACCESS_POWER_BUTTON_OFF_ACCEPT_BUTTON   = ('btn_ok_turnoff', '//XCUIElementTypeDialog/XCUIElementTypeButton[@title="Ok"]')
    PRIVATE_ACCESS_POWER_BUTTON_OFF_CANCEL_BUTTON   = ('btn_cancel_turnoff', '//XCUIElementTypeDialog/XCUIElementTypeButton[@title="Cancel"]')
    PRIVATE_ACCESS_PASSWORD_BOX                     = ('txt_password_turnoff','//XCUIElementTypeSecureTextField')
    PRIVATE_ACCESS_PASSWORD_ACCEPT_BUTTON           = ('btn_ok_turnoff', '//XCUIElementTypeButton[@title="Ok"]')
    PRIVATE_ACCESS_PASSWORD_CANCEL_BUTTON           = ('btn_cancel_turnoff', '//XCUIElementTypeButton[@title="Cancel"]')
    PRIVATE_ACCESS_REAUTH_BUTTON                    = ('btn_zpa_authenticate','//XCUIElementTypeWindow/XCUIElementTypeButton[6]')
    PRIVATE_ACCESS_REAUTH_BUTTON_2                  = ''
    PRIVATE_ACCESS_EARLY_REAUTH_BUTTON              = ('btn_re_auth_secure_access','//XCUIElementTypeButton[@title="AUTHENTICATE EARLY"]')
    PRIVATE_ACCESS_DISABLE_REASON                   = ('_NS=79','//XCUIElementTypeTextView')
    PRIVATE_ACCESS_CANCEL                           = ('btn_cancel_turnoff', '//XCUIElementTypeButton[@title="Cancel"]')
    PRIVATE_ACCESS_ACCEPT                           = ('btn_ok_turnoff', '//XCUIElementTypeButton[@title="Ok"]')
    PRIVATE_ACCESS_PASSWORD_FAILED_OK_BUTTON        = ''

    # REPORT AN ISSUE
    REPORT_AN_ISSUE_BUTTON      = ('btn_report_issue_settings','//XCUIElementTypeButton[@title="Report An Issue"]')
    REPORT_AN_ISSUE_NAME_BOX    = ('txt_name_report_issue', '//XCUIElementTypeWindow/XCUIElementTypeTextField[@identifier="txt_name_report_issue"]')
    REPORT_AN_ISSUE_EMAIL_BOX   = ('txt_email_report_issue','//XCUIElementTypeDialog/XCUIElementTypeStaticText[1]')
    REPORT_AN_ISSUE_COMMENTS    = ('_NS=135','//XCUIElementTypeWindow/XCUIElementTypeScrollView/XCUIElementTypeTextView')
    REPORT_AN_ISSUE_CHOOSE_FILE = ('btn_choose_file_report_issue','//XCUIElementTypeButton[@title="Choose File"]')
    REPORT_AN_ISSUE_SEND_BUTTON = ('btn_send_report_issue', '//XCUIElementTypeWindow/XCUIElementTypeButton[@title="Send"]')
    REPORT_AN_ISSUE_CC          = ('txt_email_cc_report_issue','//XCUIElementTypeWindow/XCUIElementTypeTextField[2]')
    REPORT_AN_ISSUE_CANCEL      = ('btn_cancel_report_issue','//XCUIElementTypeButton[@title="Cancel"]')

    # DIGITAL EXPERIENCE
    DIGITAL_EXPERIENCE_TAB                      = ('btn_upm', '//XCUIElementTypeButton[@label="Digital Experience"]')
    DIGITAL_POWER_BUTTON_ON                     = ('btn_refresh_zdx_access', '//XCUIElementTypeWindow/XCUIElementTypeGroup[@identifier="box_upm"]/XCUIElementTypeButton[@title="TURN ON"]')
    DIGITAL_POWER_BUTTON_OFF                    = ('btn_refresh_zdx_access', '//XCUIElementTypeWindow/XCUIElementTypeGroup[@identifier="box_upm"]/XCUIElementTypeButton[@title="TURN OFF"]')
    DIGITAL_EXPERIENCE_TURN_OFF_ACCEPT          = ('btn_ok_turnoff', '//XCUIElementTypeDialog/XCUIElementTypeButton[@title="Ok"]')
    DIGITAL_EXPERIENCE_TURN_OFF_CANCEL          = ('btn_cancel_turnoff', '//XCUIElementTypeDialog/XCUIElementTypeButton[@title="Cancel"]')
    DIGITAL_EXPERIENCE_PASSWORD_BOX             = ('txt_password_turnoff','//XCUIElementTypeSecureTextField')
    DIGITAL_EXPERIENCE_PASSWORD_ACCEPT_BUTTON   = ('btn_ok_turnoff', '//XCUIElementTypeButton[@title="Ok"]')
    DIGITAL_EXPERIENCE_PASSWORD_CANCEL_BUTTON   = ('btn_cancel_turnoff', '//XCUIElementTypeButton[@title="Cancel"]')
    DIGITAL_EXPERIENCE_CLEAR_DATA               = ('_NS=39','//XCUIElementTypeButton[@title="Clear ZDX Data"]')
    DIGITAL_EXPERIENCE_RESTART_ZDX              = ('_NS=45','//XCUIElementTypeButton[@title="Restart ZDX Service"]')
    DIGITAL_EXPERIENCE_DISABLE_REASON           = ('_NS=79','//XCUIElementTypeTextView')
    DIGITAL_EXPERIENCE_CANCEL                   = ('btn_cancel_turnoff', '//XCUIElementTypeButton[@title="Cancel"]')
    DIGITAL_EXPERIENCE_ACCEPT                   = ('btn_ok_turnoff', '//XCUIElementTypeButton[@title="Ok"]')

    # ZCC burger icon menu for SE and Machine Tunnel checks
    ZCC_SIDE_MENU_ICON                              = ("btn_login_menu",'//XCUIElementTypeButton[@label="Menu"]')
    ZCC_UPDATE_POLICY_FROM_SIDE_MENU_BUTTON         = ("btn_menu_policy",'//XCUIElementTypeButton[@title="Update Policy"]')
    ZCC_START_PACKET_CAPTURE_FROM_SIDE_MENU_BUTTON  = ("btn_menu_packet",'//XCUIElementTypeButton[@title="Start Packet Capture"]')
    ZCC_STOP_PACKET_CAPTURE_FROM_SIDE_MENU_BUTTON   = ("btn_menu_packet",'//XCUIElementTypeButton[@title="Stop Packet Capture"]')
    ZCC_REPORT_AN_ISSUE_FROM_SIDE_MENU_BUTTON       = ("btn_menu_report",'//XCUIElementTypeButton[@title="Report an Issue"]')
    ZCC_CLOUD_NAME_FROM_SIDE_MENU_BUTTON            = ("btn_menu_cloud",'//XCUIElementTypeButton[@title="Cloud Name"]')
    ZCC_LICENSE_AGREEMENT_FROM_SIDE_MENU_BUTTON     = ("btn_menu_license",'//XCUIElementTypeButton[@title="License Agreement"]')
    ZCC_ABOUT_FROM_SIDE_MENU_BUTTON                 = ("btn_menu_about",'//XCUIElementTypeButton[@title="About"]')

    # REVERT 
    REVERT_ZCC                          = '//XCUIElementTypeButton[@title="Revert App"]'
    REVERT_ZCC_PASSWORD_BOX             = ('txt_password_turnoff','//XCUIElementTypeSecureTextField')
    REVERT_ZCC_PASSWORD_ACCEPT_BUTTON   = ('btn_ok_turnoff', '//XCUIElementTypeButton[@title="Ok"]')
    REVERT_ZCC_PASSWORD_CANCEL_BUTTON   = ('btn_cancel_turnoff', '//XCUIElementTypeButton[@title="Cancel"]')
    
    # DISABLE REASON
    PASSWORD_DISABLE_REASON_BOX         = '//XCUIElementTypeTextView'
    DISABLE_REASON_BOX                  = '//XCUIElementTypeTextView'
    PASSWORD_DISABLE_REASON_OK_BUTTON   = '//XCUIElementTypeButton[@title="Ok"]'
    CONFIRM_ACCEPT_BUTTON               = '//XCUIElementTypeButton[@title="Ok"]' 
    DISABLE_REASON_CANCEL_BUTTON        = '//XCUIElementTypeButton[@title="Cancel"]'
    DISABLE_REASON_OK_BUTTON            = '//XCUIElementTypeButton[@title="Ok"]'

    # PARTNER LOGIN FEATURE 
    ADD_PARTNER_TENANT_BUTTON                       = ''
    PARTNER_TENANT_USER_ID_BOX                      = ""
    PARTNER_TENANT_USER_ID_CONFIRM                  = ""
    PARTNER_TENANT_USER_ID_CANCEL                   = ""
    PARTNER_TENANT_LOGIN_ERROR_OK_BUTTON            = '//XCUIElementTypeButton[@title="OK"]'
    MAIN_TENANT_USER_NAME_BUTTON                    = ""
    MAIN_TENANT_SERVICE_STATUS                      = ""
    MAIN_TENANT_POWER_BUTTON                        = ""
    MAIN_TENANT_POWER_PASSWORD_BOX                  = ""
    MAIN_TENANT_POWER_DISABLE_REASON_BOX            = ""
    MAIN_TENANT_POWER_PASSWORD_DISABLE_REASON_BOX   = ""
    MAIN_TENANT_POWER_DISABLE_PASSWORD_OK_BUTTON    = ""
    MAIN_TENANT_POWER_DISABLE_CONFIRM               = ""
    MAIN_TENANT_POWER_DISABLE_CANCEL                = ""
    PARTNER_TENANT_USER_NAME_BUTTON                 = ""
    PARTNER_TENANT_POWER_BUTTON                     = ""
    PARTNER_TENANT_POWER_PASSWORD_BOX               = ""
    PARTNER_TENANT_POWER_DISABLE_REASON_BOX         = ""
    PARTNER_TENANT_POWER_DISABLE_CONFIRM            = ""
    PARTNER_TENANT_POWER_DISABLE_CANCEL             = ""
    REMOVE_PARTNER_TENANT                           = "" 
    REMOVE_PARTNER_TENANT_CONFIRM                   = ""
    REMOVE_PARTNER_TENANT_CANCEL                    = ""

    
    # ZDP
    DATA_PREVENTION_TAB                         = ''
    DATA_PREVENTION_POWER_BUTTON_ON             = ''
    DATA_PREVENTION_POWER_BUTTON_OFF            = ''
    DATA_PREVENTION_PASSWORD_BOX                = ''
    DATA_PREVENTION_PASSWORD_ACCEPT_BUTTON      = ''
    DATA_PREVENTION_PASSWORD_FAILED_OK_BUTTON   = ''
    DATA_PREVENTION_PASSWORD_CANCEL_BUTTON      = ''


    # UNINSTALL PASSWORD
    UNINSTALL_PASSWORD_BOX                              = ''
    UNINSTALL_PASSWORD_OK_BUTTON                        = ''
    UNINSTALL_PASSWORD_FAILURE_OK_BUTTON                = ''
    ZIA_RETRY_CONNECTION_ERROR                          = ''
    DISABLE_ANTI_TAMPERING                              = ''
    ENABLE_ANTI_TAMPERING                               = ''
    ENABLE_ANTI_TAMPERING_SUCCESS_OK_BUTTON             = ''
    DISABLE_ANTI_TAMPERING_PASSWORD_BOX                 = ''
    DISABLE_ANTI_TAMPERING_PASSWORD_BOX_OK_BUTTON       = ''
    DISABLE_ANTI_TAMPERING_PASSWORD_FAILED_OK_BUTTON    = ''

    # ERROR/WARNING
    ERROR_POPUP_OK_BUTTON       = ("_NS:8",'//XCUIElementTypeSheet[@label="alert"]/XCUIElementTypeButton[@title="OK"]')
    APP_ERROR_POPUP_OK_BUTTON   = ("_NS:82",'///XCUIElementTypeWindow/XCUIElementTypeOther[4]/XCUIElementTypeButton')
    WARNING_POPUP_OK_BUTTON     = ("_NS:82",'//XCUIElementTypeWindow/XCUIElementTypeOther[4]/XCUIElementTypeButton')

    # EXIT ZCC
    EXIT_ZCC_PASSWORD_BOX   = ('//XCUIElementTypeSecureTextField')
    EXIT_ZCC_OK_BUTTON      = ('//XCUIElementTypeButton[@title="Ok"]')
    EXIT_ZCC_CANCEL_BUTTON  = ('//XCUIElementTypeButton[@title="Cancel"]')

    # Alert Box - XPATH Currently defined for SE, accessibility ID can be used for any similar alert
    ALERT_MESSAGE_LABEL = ("lbl_alert_message",'//XCUIElementTypeStaticText[@value="Strict Enforcement setup failed (3175)"]')
    
    # Invalid device Token
    INVALID_DEVICE_TOKEN = ("",'//XCUIElementTypeStaticText[@value="Invalid device token"]')

    # ZCC UI Language
    OPEN_ZSCALER_LANGUAGE_MAPPING = {
        "english": "Open"
    }
    
    # Cloud Name
    MENU_BUTTON         = ("btn_login_menu",'//XCUIElementTypeButton[@label="Menu"]')
    CLOUD_NAME_BUTTON   = ("btn_menu_cloud",'//XCUIElementTypeWindow/XCUIElementTypeButton[6]')
    CLOUD_NAME_TEXT     = ("tf_cloud_name",'//XCUIElementTypeTextField')
    CLOUD_NAME_SAVE     = ("btn_cloud_save",'//XCUIElementTypeWindow/XCUIElementTypeButton[6]')
    CLOUD_NAME_OK       = ""

class Zcc_Elements_Mac_4_3_0_170_Above:
    """ Class that contains Zscaler Mac production build app element IDs - NOTE: APPLICABLE ONLY FOR 4.3.0.171 BUILDS AND ABOVE  """
    APP         = "Zscaler"
    APP_BUNDLE  = "com.zscaler.zscaler"
    ZCC         = 'window_zs'

    # Menu Bar
    ZCC_TRAY_ICON                       = ("","//XCUIElementTypeStatusItem")
    ZCC_OPEN_MENUBAR_BUTTON             = ("show",'//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Open Zscaler"]')
    ZCC_EXPORT_LOGS_MENUBAR_BUTTON      = ('exportLogs:','//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Export Logs"]')
    ZCC_REPORT_AN_ISSUE_MENUBAR_BUTTON  = ("reportAnIssue:",'//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Report An Issue"]')
    ZCC_EXIT_MENUBAR_BUTTON             = ("showExitViewController",'//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Exit"]')
    ZCC_REGISTER_MENUBAR_BUTTON             = ("handleZPAMenuAction:",'//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="register"]')
    ZCC_AUTHENTICATE_MENUBAR_BUTTON         = ("handleZPAMenuAction:",'//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Authenticate"]')
    ZCC_AUTHENTICATE_EARLY_MENUBAR_BUTTON   = ("handleZPAMenuAction:",'//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Authenticate Early"]')

    # Zscaler Build Type
    ZCC_WINDOW_PRODUCTION_BUILD_LABEL   = '//XCUIElementTypeWindow[@title="Zscaler Client Connector"]'
    ZCC_WINDOW_TEST_BUILD_LABEL         = '//XCUIElementTypeWindow[@title="Zscaler Client Connector: Test Build"]'

    # Notifications
    NOTIFICATIONS_TAB                                       = ('btn_home_notifications','//XCUIElementTypeButton[@label="Notifications"]')
    APP_MORE                                                = ('btn_home_more', '//XCUIElementTypeButton[@label="More"]')
    CLEAR_ALL_NOTIFICATIONS                                 = ('btn_notifications_clearall', '//XCUIElementTypeWindow/XCUIElementTypeButton[@title="Clear All"]')
    CLEAR_NOTIFICATIONS_CONTINUE                            = ('_NS:8', '//XCUIElementTypeWindow/XCUIElementTypeButton[@title="OK"]')
    NOTIFICATIONS_CATEGORY_LABEL                            = 'lbl_notification_category_{0}'
    NOTIFICATIONS_TIME_LABEL                                = 'lbl_notification_time_{0}'
    NOTIFICATIONS_MESSAGE_LABEL                             = 'lbl_notification_message_{0}'
    NOTIFICATIONS_LEARN_MORE_BUTTON                         = 'btn_notification_learnmore_{0}'
    NOTIFICATION_LEARN_MORE_POPUP_EXIT_BUTTON               = ('btn_modal_close', '//XCUIElementTypeWindow/XCUIElementTypeButton[4]')
    NOTIFICATION_LEARN_MORE_DLP_BLOCK_POLICY_REVIEW_BUTTON  = '//XCUIElementTypeStaticText[@value="Click to request policy review."]'
    NOTIFICATION_LEARN_MORE_DLP_BLOCK_USE_POLICY_BUTTON     = '//XCUIElementTypeStaticText[@value="See our internet use policy."]'
    NOTIFICATION_LEARN_MORE_POSITIVE_FEEDBACK               = ('btn_feedback_yes','//XCUIElementTypeWindow/XCUIElementTypeButton[5]')
    NOTIFICATION_LEARN_MORE_NEGATIVE_FEEDBACK               = ('btn_feedback_no','//XCUIElementTypeWindow/XCUIElementTypeButton[6]')

    # APP MORE
    APP_UPDATE_POLICY                                   = ('btn_more_about_updatepolicy', '//XCUIElementTypeButton[@title="Update Policy"]')
    CLEAR_LOGS                                          = ('btn_more_troubleshoot_clearlogs', '//XCUIElementTypeButton[@title="Clear Logs"]')
    RESTART_SERVICE                                     = ('btn_more_troubleshoot_restartservice', '//XCUIElementTypeButton[@title="Restart Service"]')
    RESTART_SERVICE_ACCEPT                              = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="OK"]')
    RESTART_SERVICE_CANCEL                              = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    REPORT_AN_ISSUE                                     = ('btn_more_troubleshoot_reportanissue', '//XCUIElementTypeButton[@title="Report An Issue"]')
    START_PACKET_CAPTURE                                = ('btn_more_troubleshoot_packetcapture', '//XCUIElementTypeButton[@title="Start Packet Capture"]')
    STOP_PACKET_CAPTURE                                 = ('btn_more_troubleshoot_packetcapture', '//XCUIElementTypeButton[@title="Stop Packet Capture"]')
    CAPTURE_STATUS                                      = ('lbl_more_troubleshoot_packetcapture_status', '//XCUIElementTypeStaticText[@value="Capturing ..."]')
    EXPORT_LOGS                                         = ('btn_more_troubleshoot_reportanissue','//XCUIElementTypeButton[@title="Export Logs"]')
    UPDATE_APP                                          = ('btn_more_about_updateapp','//XCUIElementTypeButton[@title="Update App"]')
    LICENSE_AGREEMENT                                   = ('btn_more_about_license','//XCUIElementTypeButton[@title="License Agreement"]')
    LICENSE_AGREEMENT_CLOSE                             = ('btn_modal_close','//XCUIElementTypeButton[@title="Close"]')
    SHOW_NOTIFICATIONS_ON_APP_OFF_BUTTON                = ('btn_more_settings_appicon','//XCUIElementTypeButton[@title="TURN OFF"]')
    SHOW_NOTIFICATIONS_ON_APP_ON_BUTTON                 = ('btn_more_settings_appicon','//XCUIElementTypeButton[@title="TURN ON"]')
    SHOW_NOTIFICATIONS_ON_APP_STATUS_LABEL_ON           = ('txt_notifications_settings','//XCUIElementTypeStaticText[@value="ON"]')
    SHOW_NOTIFICATIONS_ON_APP_STATUS_LABEL_OFF          = ('txt_notifications_settings','//XCUIElementTypeStaticText[@value="OFF"]')
    LOG_MODE_DROP_DOWN                                  = ('pop_more_troubleshoot_logmode','//XCUIElementTypePopUpButton')
    LOG_MODE_DROP_DOWN_DEBUG                            = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[4]'
    LOG_MODE_DROP_DOWN_INFO                             = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[3]'
    LOG_MODE_DROP_DOWN_WARN                             = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[2]'
    LOG_MODE_DROP_DOWN_ERROR                            = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[1]'
    SHOW_NOTIFICATIONS_FOR_ZPA_AUTH_OFF_BUTTON          = ('btn_more_settings_zpareauthentication','//XCUIElementTypeButton[@title="TURN OFF"]')
    SHOW_NOTIFICATIONS_FOR_ZPA_AUTH_ON_BUTTON           = ('btn_more_settings_zpareauthentication','//XCUIElementTypeButton[@title="TURN ON"]')
    SHOW_NOTIFICATIONS_FOR_ZPA_AUTH_STATUS_LABEL_ON     = ('txt_notifications_settings','//XCUIElementTypeStaticText[@value="ON"]')
    SHOW_NOTIFICATIONS_FOR_ZPA_AUTH_STATUS_LABEL_OFF    = ('txt_notifications_settings','//XCUIElementTypeStaticText[@value="OFF"]')
    ABOUT_APP_VERSION_STATIC_TEXT_LABEL                 = ('lbl_app_version_settings', '//XCUIElementTypeStaticText[@value="App Version"]')
    ABOUT_APP_VERSION_LABEL                             = ('lbl_more_about_appversion', '')
    ABOUT_APP_POLICY_STATIC_TEXT_LABEL                  = ('lbl_app_policy_settings', '//XCUIElementTypeStaticText[@value="App Policy"]')
    ABOUT_APP_POLICY_LABEL                              = ('lbl_more_about_appPolicy', '')
    SHOW_NOTIFICATIONS_FOR_SSIT_AUTH_OFF_BUTTON         = ('btn_more_settings_ssitnotification','//XCUIElementTypeButton[@title="TURN OFF"]')
    SHOW_NOTIFICATIONS_FOR_SSIT_AUTH_ON_BUTTON          = ('btn_more_settings_ssitnotification','//XCUIElementTypeButton[@title="TURN ON"]')
    SHOW_NOTIFICATIONS_FOR_SSIT_AUTH_STATUS_LABEL_ON    = ('txt_ssit_notifications_settings','//XCUIElementTypeStaticText[@value="ON"]')

    # LOGIN ZIA
    USER_NAME                   = ('tf_login_emailId', '//XCUIElementTypeTextField[@label="Email ID"]')
    LOGIN_BUTTON                = ('btn_login_loginbtn', '//XCUIElementTypeButton[@title="Login"]')
    EULA_ACCEPT_BUTTON          = ('btn_aup_accept', '//XCUIElementTypeButton[@title="Accept"]')
    EULA_DECLINE_BUTTON         = ('btn_aup_decline', '//XCUIElementTypeButton[@title="Decline"]')
    PASSWORD_BOX                = ('tf_login_password', '//XCUIElementTypeSecureTextField[@label="Password"]')
    LOGIN_BUTTON_PASSWORD       = ('btn_login_loginbtn', '//XCUIElementTypeButton[@title="Login"]')
    LOGIN_ERROR_OK_BUTTON       = ('_NS:82','//XCUIElementTypeOther[@title="OK"]')
    ZCC_BACK_BUTTON             = ('btn_login_back','//XCUIElementTypeButton[@identifier="btn_login_back"]')
    INITIAL_LOGIN_BUTTON        = ('btn_login_loginbtn','//XCUIElementTypeButton[@label="Login"]')


    # LOGOUT
    LOGOUT_BUTTON           = ('btn_home_logout', '//XCUIElementTypeWindow/XCUIElementTypeButton[12]')
    LOGOUT_ACCEPT_BUTTON    = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="OK"]')
    LOGOUT_CANCEL_BUTTON    = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    LOGOUT_PASSWORD_BOX     = ('tf_turnoff_popup_password', '//XCUIElementTypeSecureTextField')

    # LOGIN ZPA OKTA
    OKTA_SIGNIN_PAGE                    = ''
    OKTA_SIGNIN_PAGE_2                  = ''
    OKTA_USERNAME                       = '//XCUIElementTypeTextField'
    OKTA_PASSWORD_BOX                   = '//XCUIElementTypeSecureTextField'
    REAUTH_OKTA_USERNAME                = ''
    REAUTH_OKTA_PASSWORD_BOX            = '//XCUIElementTypeSecureTextField'
    OKTA_SIGN_IN_BUTTON                 = '//XCUIElementTypeButton[@title="Sign in"]'
    ZCC_BACK_BUTTON_AT_OKTA             = ('btn_login_back','//XCUIElementTypeButton[@identifier="btn_login_back"]')
    OKTA_LOGIN_HINT_PASSWORD_IMAGE      = '//XCUIElementTypeImage[@label="loader"]'
    OKTA_LOGIN_HINT_VERIFY_TEXT_LABEL   = '//XCUIElementTypeStaticText[@value="Verify with your password"]'
    OKTA_LOGIN_HINT_VERIFY_BUTTON       = '//XCUIElementTypeButton[@title="Verify"]'

    # INTERNET SECURITY
    INTERNET_SECURITY_TAB                               = ('btn_home_zia', '//XCUIElementTypeButton[@label="Internet Security"]')
    INTERNET_SECURITY_POWER_BUTTON_ON                   = ('btn_zia_switch', '//XCUIElementTypeButton[@title="TURN ON"]')
    INTERNET_SECURITY_POWER_BUTTON_OFF                  = ('btn_zia_switch', '//XCUIElementTypeButton[@title="TURN OFF"]')
    INTERNET_SECURITY_POWER_BUTTON_OFF_ACCEPT_BUTTON    = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="OK"]')
    INTERNET_SECURITY_POWER_BUTTON_OFF_CANCEL_BUTTON    = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    INTERNET_SECURITY_PASSWORD_BOX                      = ('tf_turnoff_popup_password','//XCUIElementTypeSecureTextField')
    INTERNET_SECURITY_PASSWORD_ACCEPT_BUTTON            = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="OK"]')
    INTERNET_SECURITY_PASSWORD_CANCEL_BUTTON            = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    INTERNET_SECURITY_RETRY_BUTTON                      = ('btn_zia_switch','//XCUIElementTypeButton[@title="RETRY"]')
    INTERNET_SECURITY_DISABLE_REASON                    = ('tv_turnoff_popup_reason','//XCUIElementTypeTextView[@identifier="tv_turnoff_popup_reason"]')
    INTERNET_SECURITY_DISABLE_REASON_CANCEL             = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    INTERNET_SECURITY_DISABLE_REASON_ACCEPT             = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="OK"]')
    INTERNET_SECURITY_PASSWORD_FAILED_OK_BUTTON         = ''
    INTERNET_SECURITY_SERVER_IP                         = ('lbl_zia_server')
    INTERNET_SECURITY_CLIENT_IP                         = ('lbl_zia_clientip')
    INTERNET_SECURITY_TIME_CONNECTED                    = ('lbl_zia_timeconnected')

    # PRIVATE ACCESS
    PRIVATE_ACCESS_TAB                              = ('btn_home_zpa', '//XCUIElementTypeButton[@label="Private Access"]')
    PRIVATE_ACCESS_POWER_BUTTON_ON                  = ('btn_zpa_switch', '//XCUIElementTypeButton[@title="TURN ON"]')
    PRIVATE_ACCESS_POWER_BUTTON_OFF                 = ('btn_zpa_switch', '//XCUIElementTypeButton[@title="TURN OFF"]')
    PRIVATE_ACCESS_POWER_BUTTON_OFF_ACCEPT_BUTTON   = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="OK"]')
    PRIVATE_ACCESS_POWER_BUTTON_OFF_CANCEL_BUTTON   = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    PRIVATE_ACCESS_PASSWORD_BOX                     = ('tf_turnoff_popup_password','//XCUIElementTypeSecureTextField')
    PRIVATE_ACCESS_PASSWORD_ACCEPT_BUTTON           = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="OK"]')
    PRIVATE_ACCESS_PASSWORD_CANCEL_BUTTON           = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    PRIVATE_ACCESS_REAUTH_BUTTON                    = ('btn_zpa_authenticate','//XCUIElementTypeWindow/XCUIElementTypeButton[6]')
    PRIVATE_ACCESS_REAUTH_BUTTON_2                  = ''
    PRIVATE_ACCESS_EARLY_REAUTH_BUTTON              = ('btn_zpa_authenticate','//XCUIElementTypeButton[@title="AUTHENTICATE EARLY"]')
    PRIVATE_ACCESS_DISABLE_REASON                   = ('tv_turnoff_popup_reason','//XCUIElementTypeTextView[@identifier="tv_turnoff_popup_reason"]')
    PRIVATE_ACCESS_DISABLE_REASON_CANCEL            = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    PRIVATE_ACCESS_DISABLE_REASON_ACCEPT            = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="OK"]')
    PRIVATE_ACCESS_PASSWORD_FAILED_OK_BUTTON        = ''
    PRIVATE_ACCESS_BROKER_IP                        = ('lbl_zpa_broker','')
    PRIVATE_ACCESS_CLIENT_IP                        = ('lbl_zpa_client','')                                   
    PRIVATE_ACCESS_TIME_CONNECTED                   = ('lbl_zpa_timeconneted','')	

    # REPORT AN ISSUE
    REPORT_AN_ISSUE_BUTTON                                  = ('btn_more_troubleshoot_reportanissue','//XCUIElementTypeButton[@title="Report An Issue"]')
    REPORT_AN_ISSUE_NAME_BOX                                = ('tf_reportissue_name', '//XCUIElementTypeWindow/XCUIElementTypeTextField[1]')
    REPORT_AN_ISSUE_EMAIL_BOX                               = ('tf_reportissue_email','//XCUIElementTypeDialog/XCUIElementTypeStaticText[1]')
    REPORT_AN_ISSUE_COMMENTS                                = ('tv_reportissue_comments','//XCUIElementTypeWindow/XCUIElementTypeScrollView/XCUIElementTypeTextView')
    REPORT_AN_ISSUE_CHOOSE_FILE                             = ('btn_reportissue_choosefile','//XCUIElementTypeButton[@title="Choose File"]')
    REPORT_AN_ISSUE_SEND_BUTTON                             = ('btn_reportissue_send', '//XCUIElementTypeButton[@title="Send"]')
    REPORT_AN_ISSUE_CC                                      = ('tf_reportissue_cc','//XCUIElementTypeWindow/XCUIElementTypeTextField[2]')
    REPORT_AN_ISSUE_CANCEL                                  = ('btn_reportissue_cancel','//XCUIElementTypeButton[@title="Cancel"]')
    REPORT_AN_ISSUE_PROBLEM_DROPDOWN                        = ('pop_reportissue_problem','//XCUIElementTypeWindow/XCUIElementTypePopUpButton[1]')
    REPORT_AN_ISSUE_PROBLEM_DROPDOWN_USER_INTERFACE         = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="User Interface"]'
    REPORT_AN_ISSUE_PROBLEM_DROPDOWN_AUTHENTICATION         = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Authentication"]'
    REPORT_AN_ISSUE_PROBLEM_DROPDOWN_NETWORK_CONNECTIVITY   = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Network Connectivity"]'
    REPORT_AN_ISSUE_PROBLEM_DROPDOWN_PRIVATE_ACCESS         = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Private Access"]'
    REPORT_AN_ISSUE_PROBLEM_DROPDOWN_OTHERS                 = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Others"]'
    REPORT_AN_ISSUE_PRIORITY_DROPDOWN                       = ('pop_reportissue_priority','//XCUIElementTypeWindow/XCUIElementTypePopUpButton[2]')
    REPORT_AN_ISSUE_PRIORITY_DROPDOWN_URGENT                = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Urgent"]'
    REPORT_AN_ISSUE_PRIORITY_DROPDOWN_HIGH                  = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="High"]'
    REPORT_AN_ISSUE_PRIORITY_DROPDOWN_NORMAL                = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Normal"]'
    REPORT_AN_ISSUE_PRIORITY_DROPDOWN_LOW                   = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Low"]'


    # DIGITAL EXPERIENCE
    DIGITAL_EXPERIENCE_TAB                      = ('btn_home_zdx', '//XCUIElementTypeButton[@label="Digital Experience"]')
    DIGITAL_POWER_BUTTON_ON                     = ('btn_zdx_switch', '//XCUIElementTypeButton[@title="TURN ON"]')
    DIGITAL_POWER_BUTTON_OFF                    = ('btn_zdx_switch', '//XCUIElementTypeButton[@title="TURN OFF"]')
    DIGITAL_EXPERIENCE_TURN_OFF_ACCEPT_BUTTON   = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="OK"]')
    DIGITAL_EXPERIENCE_TURN_OFF_CANCEL_BUTTON   = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    DIGITAL_EXPERIENCE_PASSWORD_BOX             = ('tf_turnoff_popup_password','//XCUIElementTypeSecureTextField')
    DIGITAL_EXPERIENCE_PASSWORD_ACCEPT_BUTTON   = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="OK"]')
    DIGITAL_EXPERIENCE_PASSWORD_CANCEL_BUTTON   = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    DIGITAL_EXPERIENCE_CLEAR_DATA               = ('btn_zdx_clear','//XCUIElementTypeButton[@title="Clear ZDX Data"]')
    DIGITAL_EXPERIENCE_RESTART_ZDX              = ('btn_zdx_restart','//XCUIElementTypeButton[@title="Restart ZDX Service"]')
    DIGITAL_EXPERIENCE_DISABLE_REASON           = ('tv_turnoff_popup_reason','//XCUIElementTypeTextView[@identifier="tv_turnoff_popup_reason"]')
    DIGITAL_EXPERIENCE_DISABLE_REASON_CANCEL    = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    DIGITAL_EXPERIENCE_DISABLE_REASON_ACCEPT    = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="OK"]')
    DIGITAL_EXPERIENCE_ACCEPT                   = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="OK"]')
    DIGITAL_EXPERIENCE_SERVER_IP                = ('lbl_zdx_serveraddress')
    DIGITAL_EXPERIENCE_START_TIME               = ('lbl_zdx_timeconnected')
    DIGITAL_EXPERIENCE_SERVER_VERSION           = ('lbl_zdx_serviceversion')

    # ZCC burger icon menu for SE and Machine Tunnel checks
    ZCC_SIDE_MENU_ICON                                                  = ("btn_login_menu",'//XCUIElementTypeButton[@label="Menu"]')
    ZCC_UPDATE_POLICY_FROM_SIDE_MENU_BUTTON                             = ("btn_menu_policy",'//XCUIElementTypeButton[@title="Update Policy"]')
    ZCC_START_PACKET_CAPTURE_FROM_SIDE_MENU_BUTTON                      = ("btn_menu_packet",'//XCUIElementTypeButton[@title="Start Packet Capture"]')
    ZCC_STOP_PACKET_CAPTURE_FROM_SIDE_MENU_BUTTON                       = ("btn_menu_packet",'//XCUIElementTypeButton[@title="Stop Packet Capture"]')
    ZCC_REPORT_AN_ISSUE_FROM_SIDE_MENU_BUTTON                           = ("btn_menu_report",'//XCUIElementTypeButton[@title="Report an Issue"]')
    ZCC_CLOUD_NAME_FROM_SIDE_MENU_BUTTON                                = ("btn_menu_cloud",'//XCUIElementTypeButton[@title="Cloud Name"]')
    ZCC_LICENSE_AGREEMENT_FROM_SIDE_MENU_BUTTON                         = ("btn_menu_license",'//XCUIElementTypeButton[@title="License Agreement"]')
    ZCC_ABOUT_FROM_SIDE_MENU_BUTTON                                     = ("btn_menu_about",'//XCUIElementTypeButton[@title="About"]')
    ZCC_TUNNEL_STATUS_FROM_SIDE_MENU_BUTTON                             = ("btn_menu_tunnel_status",'//XCUIElementTypeButton[@title="Tunnel Status"]')
    ZCC_CLEAR_LOGS_FROM_TUNNEL_STATUS_SIDE_MENU_BUTTON                  = ("_NS:21",'//XCUIElementTypeButton[@title="Clear Logs"]')   
    ZCC_UPDATE_POLICY_FROM_TUNNEL_STATUS_SIDE_MENU_BUTTON               = ("_NS:28",'//XCUIElementTypeButton[@title="Update Policy"]')   
    ZCC_RESTART_SERVICE_FROM_TUNNEL_STATUS_SIDE_MENU_BUTTON             = ("_NS:35",'//XCUIElementTypeButton[@title="Restart Service"]')   
    ZCC_START_PACKET_CAPTURE_FROM_TUNNEL_STATUS_SIDE_MENU_BUTTON        = ("_NS:42",'//XCUIElementTypeButton[@title="Start Packet Capture"]')  
    ZCC_STOP_PACKET_CAPTURE_FROM_TUNNEL_STATUS_SIDE_MENU_BUTTON         = ("_NS:42",'//XCUIElementTypeButton[@title="Stop Packet Capture"]')
    ZCC_OK_TUNNEL_STATUS_SIDE_MENU_BUTTON                               = ("btn_warning_ok",'//XCUIElementTypeButton[@title="Ok"]')


    # REVERT 
    REVERT_ZCC                          = ("btn_more_troubleshoot_revertapp", '//XCUIElementTypeButton[@title="Revert App"]')
    REVERT_ZCC_PASSWORD_BOX             = ('tf_turnoff_popup_password','//XCUIElementTypeSecureTextField')
    REVERT_ZCC_PASSWORD_ACCEPT_BUTTON   = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="OK"]')
    REVERT_ZCC_PASSWORD_CANCEL_BUTTON   = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    
    # DISABLE REASON
    PASSWORD_DISABLE_REASON_BOX         = ('tv_turnoff_popup_reason','//XCUIElementTypeTextView[@identifier="tv_turnoff_popup_reason"]')
    DISABLE_REASON_BOX                  = ('tv_turnoff_popup_reason','//XCUIElementTypeTextView[@identifier="tv_turnoff_popup_reason"]')
    PASSWORD_DISABLE_REASON_OK_BUTTON   = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="OK"]')
    CONFIRM_ACCEPT_BUTTON               = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="OK"]')
    DISABLE_REASON_CANCEL_BUTTON        = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    DISABLE_REASON_OK_BUTTON            = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="OK"]')

    # PARTNER LOGIN FEATURE 
    ADD_PARTNER_TENANT_BUTTON                       = ('btn_zpa_addPartner','//XCUIElementTypeButton[@title="+ Add Partner Tenant"]')
    ADD_PARTNER_TENANT_AT_ZPA_PARTNER_BUTTON        = ('btn_partner_addtenant', '//XCUIElementTypeButton[@title="+ Add Partner Tenant"]')
    PARTNER_TENANT_USER_ID_BOX                      = ("tf_addpartner_popup_username",'//XCUIElementTypeTextField')
    PARTNER_TENANT_USER_ID_CONFIRM                  = ("btn_addpartner_popup_submit",'//XCUIElementTypeButton[@title="Submit"]')
    PARTNER_TENANT_USER_ID_CANCEL                   = ("btn_addpartner_popup_cancel",'//XCUIElementTypeButton[@title="Cancel"]')
    PARTNER_TENANT_LOGIN_ERROR_OK_BUTTON            = ("_NS:8",'//XCUIElementTypeButton[@title="OK"]')
    MAIN_TENANT_USER_NAME_BUTTON                    = ("_NS:42","//XCUIElementTypeWindow/XCUIElementTypeButton[9]")
    MAIN_TENANT_SERVICE_STATUS_LABEL                = "lbl_partner_main_status"
    MAIN_TENANT_SERVICE_STATUS_PARTNER_ACTIVE       = ("lbl_partner_main_status",'//XCUIElementTypeStaticText[@value="PARTNER ACTIVE"]')
    MAIN_TENANT_SERVICE_STATUS_OFF                  = ("lbl_partner_main_status",'//XCUIElementTypeStaticText[@value="OFF"]')
    MAIN_TENANT_SERVICE_STATUS_ON                   = ("lbl_partner_main_status",'//XCUIElementTypeStaticText[@value="ON"]')
    MAIN_TENANT_POWER_BUTTON                        = ("btn_partner_main_switch","//XCUIElementTypeWindow/XCUIElementTypeButton[13]")
    MAIN_TENANT_POWER_PASSWORD_BOX                  = ('tf_turnoff_popup_password','//XCUIElementTypeSecureTextField')
    MAIN_TENANT_POWER_DISABLE_REASON_BOX            = ("tv_turnoff_popup_reason",'//XCUIElementTypeTextView[@label="Enter reason for disabling the service"]')
    MAIN_TENANT_POWER_PASSWORD_DISABLE_REASON_BOX   = ("tv_turnoff_popup_reason",'//XCUIElementTypeTextView[@label="Enter reason for disabling the service"]')
    MAIN_TENANT_POWER_DISABLE_PASSWORD_OK_BUTTON    = ("btn_turnoff_popup_ok",'//XCUIElementTypeDialog/XCUIElementTypeButton[@title="OK"]')
    MAIN_TENANT_POWER_DISABLE_CONFIRM               = ("btn_turnoff_popup_ok",'//XCUIElementTypeDialog/XCUIElementTypeButton[@title="OK"]')
    MAIN_TENANT_POWER_DISABLE_CANCEL                = ("btn_turnoff_popup_cancel",'//XCUIElementTypeDialog/XCUIElementTypeButton[@title="Cancel"]')
    PARTNER_TENANT_USER_NAME_BUTTON                 = ("tbl_btn_partner_username_{0}", "//XCUIElementTypeCell/XCUIElementTypeButton[1]")
    PARTNER_TENANT_POWER_BUTTON                     = ("tbl_btn_partner_action_{0}","//XCUIElementTypeCell/XCUIElementTypeButton[3]")
    PARTNER_TENANT_POWER_PASSWORD_BOX               = ""
    PARTNER_TENANT_POWER_DISABLE_REASON_BOX         = ""
    PARTNER_TENANT_POWER_DISABLE_CONFIRM            = ""
    PARTNER_TENANT_POWER_DISABLE_CANCEL             = ""
    PARTNER_TENANT_SERVICE_STATUS_LABEL             = "tbl_lbl_partner_status_{0}"
    PARTNER_TENANT_SERVICE_STATUS_OFF               = ("tbl_lbl_partner_status_{0}",'//XCUIElementTypeStaticText[@value="OFF"]')
    PARTNER_TENANT_SERVICE_STATUS_ON                = ("tbl_lbl_partner_status_{0}",'//XCUIElementTypeStaticText[@value="ON"]')
    REMOVE_PARTNER_TENANT                           = ("tbl_btn_partner_delete_{0}",'//XCUIElementTypeCell/XCUIElementTypeButton[2]')
    REMOVE_PARTNER_TENANT_CONFIRM                   = ("btn_turnoff_popup_ok",'//XCUIElementTypeDialog/XCUIElementTypeButton[@title="OK"]')
    REMOVE_PARTNER_TENANT_CANCEL                    = ("btn_turnoff_popup_cancel",'//XCUIElementTypeDialog/XCUIElementTypeButton[@title="Cancel"]')

    
    # ZDP
    DATA_PREVENTION_TAB                         = ''
    DATA_PREVENTION_POWER_BUTTON_ON             = ''
    DATA_PREVENTION_POWER_BUTTON_OFF            = ''
    DATA_PREVENTION_PASSWORD_BOX                = ''
    DATA_PREVENTION_PASSWORD_ACCEPT_BUTTON      = ''
    DATA_PREVENTION_PASSWORD_FAILED_OK_BUTTON   = ''
    DATA_PREVENTION_PASSWORD_CANCEL_BUTTON      = ''


    # UNINSTALL PASSWORD
    UNINSTALL_PASSWORD_BOX                              = ''
    UNINSTALL_PASSWORD_OK_BUTTON                        = ''
    UNINSTALL_PASSWORD_FAILURE_OK_BUTTON                = ''
    ZIA_RETRY_CONNECTION_ERROR                          = ''
    DISABLE_ANTI_TAMPERING                              = ''
    ENABLE_ANTI_TAMPERING                               = ''
    ENABLE_ANTI_TAMPERING_SUCCESS_OK_BUTTON             = ''
    DISABLE_ANTI_TAMPERING_PASSWORD_BOX                 = ''
    DISABLE_ANTI_TAMPERING_PASSWORD_BOX_OK_BUTTON       = ''
    DISABLE_ANTI_TAMPERING_PASSWORD_FAILED_OK_BUTTON    = ''

    # EXIT ZCC
    EXIT_ZCC_PASSWORD_BOX   = ("tf_confirm_popup_password",'//XCUIElementTypeSecureTextField')
    EXIT_ZCC_OK_BUTTON      = ("btn_confirm_popup_ok",'//XCUIElementTypeButton[@title="OK"]')
    EXIT_ZCC_CANCEL_BUTTON  = ("btn_confirm_popup_cancel",'//XCUIElementTypeButton[@title="Cancel"]')

    # ERROR/WARNING
    ERROR_POPUP_OK_BUTTON       = ("_NS:8",'//XCUIElementTypeSheet[@label="alert"]/XCUIElementTypeButton[@title="OK"]')
    SE_ERROR_POPUP_OK_BUTTON    = ("_NS:81",'//XCUIElementTypeButton[@title="Ok"]')
    CONFIRM_POPUP_OK_BUTTON     = ("btn_confirm_popup_ok",'//XCUIElementTypeButton[@title="OK"]')
    WARNING_POPUP_OK_BUTTON     = ("btn_warning_ok",'//XCUIElementTypeButton[@title="OK"]')
    APP_ERROR_POPUP_OK_BUTTON   = ("btn_warning_ok",'//XCUIElementTypeWindow/XCUIElementTypeOther[3]/XCUIElementTypeButton')
    ERROR_POPUP_OK_BUTTON_2     = ("_NS:81",'//XCUIElementTypeWindow/XCUIElementTypeOther[2]')


    # Alert Box - XPATH Currently defined for SE, accessibility ID can be used for any similar alert
    ALERT_MESSAGE_LABEL = ("lbl_warning_title",'//XCUIElementTypeStaticText[@value="Strict Enforcement setup failed (3175)"]')

    # Strict enforcement login page elements
    STRICT_ENFORCEMENT_MESSAGE_TITLE_LABEL          = ("lbl_login_se_title",'//XCUIElementTypeButton[@title="Internet Access Blocked"]')
    STRICT_ENFORCEMENT_MESSAGE_DESCRIPTION_LABEL    = ("lbl_login_se_description",'//XCUIElementTypeStaticText[@value="Internet access is blocked on this computer as per your company\'s policy unless you sign into Zscaler Client Connector."]')

    #Invalid Device Token
    INVALID_DEVICE_TOKEN    = ("",'//XCUIElementTypeStaticText[@value="Invalid device token"]')

    # ZCC UI Language
    OPEN_ZSCALER_LANGUAGE_MAPPING = {
        "english": "Open Zscaler",
        "french": "Ouvrir Zscaler"
    }
    # Cloud Name
    MENU_BUTTON         = ("btn_login_menu",'//XCUIElementTypeButton[@label="Menu"]')
    CLOUD_NAME_BUTTON   = ("btn_menu_cloud",'//XCUIElementTypeWindow/XCUIElementTypeButton[6]')
    CLOUD_NAME_TEXT     = ("tf_cloud_name",'//XCUIElementTypeTextField')
    CLOUD_NAME_SAVE     = ("btn_cloud_save",'//XCUIElementTypeWindow/XCUIElementTypeButton[6]')
    CLOUD_NAME_OK       = ""
    #ONE ID
    ONEID_LOGIN_ERROR_OK_BUTTON             = ('btn_warning_ok','//XCUIElementTypeWindow/XCUIElementTypeOther[3]/XCUIElementTypeButton')
    ONEID_LOGIN_ERROR_LEARN_MORE            = ('_NS:87','//XCUIElementTypeWindow/XCUIElementTypeOther[2]')
    ONEID_LOGIN_PAGE_PASSWORD_BOX           = '//XCUIElementTypeSecureTextField[@value="Enter Your Password..."]/XCUIElementTypeGroup'
    ONEID_LOGIN_PAGE_SIGNIN_BUTTON          = '//XCUIElementTypeWebView[@label="Zscaler Login"]/XCUIElementTypeGroup/XCUIElementTypeButton[3]'
    ONEID_LOGIN_PAGE_BACK_BUTTON            = ('btn_login_back','//XCUIElementTypeWindow/XCUIElementTypeButton[4]')
    ONEID_LOGIN_PAGE_TROUBLE_SIGNIN_BUTTON  = '//XCUIElementTypeWebView[@label="Zscaler Login"]/XCUIElementTypeGroup[1]/XCUIElementTypeButton[3]'
    #ONEID_LOGIN_PAGE_ERROR_SIGNIN_POPUP    = '//XCUIElementTypeWebView[@label="Zscaler Login"]/XCUIElementTypeGroup[4]/XCUIElementTypeGroup'	
    ONEID_LOGIN_PAGE_ERROR_SIGNIN_POPUP     = '//XCUIElementTypeStaticText[@value="Log into your Zscaler Identity account."]'
    ONEID_AUP_ACCEPT                        = ('btn_aup_accept','//XCUIElementTypeWindow/XCUIElementTypeButton[6]')
    ONEID_AUP_DECLINE                       = ('btn_aup_decline','//XCUIElementTypeWindow/XCUIElementTypeButton[5]')
    ONE_ID_SECOND_LOGIN_BUTTON              = '//XCUIElementTypeTextField/XCUIElementTypeGroup'
    ONE_ID_SECOND_LOGIN_BUTTON_NEXT         = '//XCUIElementTypeWebView[@label="Zscaler Login"]/XCUIElementTypeGroup/XCUIElementTypeButton'

    # Scaling Zscaler UI
    SCALE_ZSCALER_UI    = ("",'//XCUIElementTypeWindow/XCUIElementTypeButton[3]')

class Zcc_Elements_Mac_4_3_0_1_Above:
    """ Class that contains Zscaler Mac production build app element IDs - NOTE: APPLICABLE ONLY FOR builds greater 4.3.0.0 and lesser than 4.3.0.170 """
    APP             = "Zscaler"
    APP_BUNDLE      = "com.zscaler.zscaler"
    ZCC             = 'window_zs'

    # Menu Bar
    ZCC_TRAY_ICON                           = ("","//XCUIElementTypeStatusItem")
    ZCC_OPEN_MENUBAR_BUTTON                 = ("show",'//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Open"]')
    ZCC_EXPORT_LOGS_MENUBAR_BUTTON          = ('exportLogs:','//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Export Logs"]')
    ZCC_REPORT_AN_ISSUE_MENUBAR_BUTTON      = ("reportAnIssue:",'//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Report An Issue"]')
    ZCC_EXIT_MENUBAR_BUTTON                 = ("showExitViewController",'//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Exit"]')
    ZCC_REGISTER_MENUBAR_BUTTON             = ("handleZPAMenuAction:",'//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="register"]')
    ZCC_AUTHENTICATE_MENUBAR_BUTTON         = ("handleZPAMenuAction:",'//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Authenticate"]')
    ZCC_AUTHENTICATE_EARLY_MENUBAR_BUTTON   = ("handleZPAMenuAction:",'//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Authenticate Early"]')

    # Zscaler Build Type
    ZCC_WINDOW_PRODUCTION_BUILD_LABEL   = '//XCUIElementTypeWindow[@title="Zscaler Client Connector"]'
    ZCC_WINDOW_TEST_BUILD_LABEL         = '//XCUIElementTypeWindow[@title="Zscaler Client Connector: Test Build"]'

    # Notifications
    NOTIFICATIONS_TAB                           = ('btn_home_notifications','//XCUIElementTypeButton[@label="Notifications"]')
    APP_MORE                                    = ('btn_home_more', '//XCUIElementTypeButton[@label="More"]')
    NOTIFICATIONS_CATEGORY_LABEL                = 'lbl_notification_category_{0}'
    NOTIFICATIONS_TIME_LABEL                    = 'lbl_notification_time_{0}'
    NOTIFICATIONS_MESSAGE_LABEL                 = 'lbl_notification_message_{0}'
    NOTIFICATIONS_LEARN_MORE_BUTTON             = 'btn_notification_learnmore_{0}'
    NOTIFICATION_LEARN_MORE_POPUP_EXIT_BUTTON   = ('btn_modal_close', '//XCUIElementTypeWindow/XCUIElementTypeButton[4]')
    NOTIFICATION_LEARN_MORE_POSITIVE_FEEDBACK   = ('btn_feedback_yes','//XCUIElementTypeWindow/XCUIElementTypeButton[5]')
    NOTIFICATION_LEARN_MORE_NEGATIVE_FEEDBACK   = ('btn_feedback_no','//XCUIElementTypeWindow/XCUIElementTypeButton[6]')

    # APP MORE
    APP_UPDATE_POLICY                                   = ('btn_more_about_updatepolicy', '//XCUIElementTypeButton[@title="Update Policy"]')
    CLEAR_LOGS                                          = ('btn_more_troubleshoot_clearlogs', '//XCUIElementTypeButton[@title="Clear Logs"]')
    RESTART_SERVICE                                     = ('btn_more_troubleshoot_restartservice', '//XCUIElementTypeButton[@title="Restart Service"]')
    RESTART_SERVICE_ACCEPT                              = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="Ok"]')
    RESTART_SERVICE_CANCEL                              = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    REPORT_AN_ISSUE                                     = ('btn_more_troubleshoot_reportanissue', '//XCUIElementTypeButton[@title="Report An Issue"]')
    START_PACKET_CAPTURE                                = ('btn_more_troubleshoot_packetcapture', '//XCUIElementTypeButton[@title="Start Packet Capture"]')
    STOP_PACKET_CAPTURE                                 = ('btn_more_troubleshoot_packetcapture', '//XCUIElementTypeButton[@title="Stop Packet Capture"]')
    CAPTURE_STATUS                                      = ('lbl_more_troubleshoot_packetcapture_status', '//XCUIElementTypeStaticText[@value="Capturing ..."]')
    EXPORT_LOGS                                         = ('btn_more_troubleshoot_reportanissue','//XCUIElementTypeButton[@title="Export Logs"]')
    UPDATE_APP                                          = ('btn_more_about_updateapp','//XCUIElementTypeButton[@title="Update App"]')
    LICENSE_AGREEMENT                                   = ('btn_more_about_license','//XCUIElementTypeButton[@title="License Agreement"]')
    LICENSE_AGREEMENT_CLOSE                             = ('btn_modal_close','//XCUIElementTypeButton[@title="Close"]')
    SHOW_NOTIFICATIONS_ON_APP_OFF_BUTTON                = ('btn_more_settings_appicon','//XCUIElementTypeButton[@title="TURN OFF"]')
    SHOW_NOTIFICATIONS_ON_APP_ON_BUTTON                 = ('btn_more_settings_appicon','//XCUIElementTypeButton[@title="TURN ON"]')
    SHOW_NOTIFICATIONS_ON_APP_STATUS_LABEL_ON           = ('txt_notifications_settings','//XCUIElementTypeStaticText[@value="ON"]')
    SHOW_NOTIFICATIONS_ON_APP_STATUS_LABEL_OFF          = ('txt_notifications_settings','//XCUIElementTypeStaticText[@value="OFF"]')

    SHOW_NOTIFICATIONS_FOR_ZPA_AUTH_OFF_BUTTON          = ('btn_more_settings_zpareauthentication','//XCUIElementTypeButton[@title="TURN OFF"]')
    SHOW_NOTIFICATIONS_FOR_ZPA_AUTH_ON_BUTTON           = ('btn_more_settings_zpareauthentication','//XCUIElementTypeButton[@title="TURN ON"]')
    SHOW_NOTIFICATIONS_FOR_ZPA_AUTH_STATUS_LABEL_ON     = ('txt_notifications_settings','//XCUIElementTypeStaticText[@value="ON"]')
    SHOW_NOTIFICATIONS_FOR_ZPA_AUTH_STATUS_LABEL_OFF    = ('txt_notifications_settings','//XCUIElementTypeStaticText[@value="OFF"]')

    # LOGIN ZIA
    USER_NAME               = ('tf_login_emailId', '//XCUIElementTypeTextField[@label="Email ID"]')
    LOGIN_BUTTON            = ('btn_login_loginbtn', '//XCUIElementTypeButton[@title="Login"]')
    EULA_ACCEPT_BUTTON      = ('btn_aup_accept', '//XCUIElementTypeButton[@title="Accept"]')
    EULA_DECLINE_BUTTON     = ('btn_aup_decline', '//XCUIElementTypeButton[@title="Decline"]')
    PASSWORD_BOX            = ('tf_login_password', '//XCUIElementTypeSecureTextField[@label="Password"]')
    LOGIN_BUTTON_PASSWORD   = ('btn_login_loginbtn', '//XCUIElementTypeButton[@title="Login"]')
    LOGIN_ERROR_OK_BUTTON   = ('_NS:82','//XCUIElementTypeOther[@title="Ok"]')
    ZCC_BACK_BUTTON         = ('btn_login_back','//XCUIElementTypeButton[@identifier="btn_login_back"]')
    INITIAL_LOGIN_BUTTON    = ('btn_login_loginbtn','//XCUIElementTypeButton[@label="Login"]')

    # LOGOUT
    LOGOUT_BUTTON           = ('btn_home_logout', '//XCUIElementTypeWindow/XCUIElementTypeButton[12]')
    LOGOUT_ACCEPT_BUTTON    = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="Ok"]')
    LOGOUT_CANCEL_BUTTON    = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    LOGOUT_PASSWORD_BOX     = ('tf_turnoff_popup_password', '//XCUIElementTypeSecureTextField')

    # LOGIN ZPA OKTA
    OKTA_SIGNIN_PAGE                    = ''
    OKTA_SIGNIN_PAGE_2                  = ''
    OKTA_USERNAME                       = '//XCUIElementTypeTextField'
    OKTA_PASSWORD_BOX                   = '//XCUIElementTypeSecureTextField'
    REAUTH_OKTA_USERNAME                = ''
    REAUTH_OKTA_PASSWORD_BOX            = '//XCUIElementTypeSecureTextField'
    OKTA_SIGN_IN_BUTTON                 = '//XCUIElementTypeButton[@title="Sign in"]'
    ZCC_BACK_BUTTON_AT_OKTA             = ('btn_login_back','//XCUIElementTypeButton[@identifier="btn_login_back"]')
    OKTA_LOGIN_HINT_PASSWORD_IMAGE      = '//XCUIElementTypeImage[@label="loader"]'
    OKTA_LOGIN_HINT_VERIFY_TEXT_LABEL   = '//XCUIElementTypeStaticText[@value="Verify with your password"]'
    OKTA_LOGIN_HINT_VERIFY_BUTTON       = '//XCUIElementTypeButton[@title="Verify"]'

    # INTERNET SECURITY
    INTERNET_SECURITY_TAB                               = ('btn_home_zia', '//XCUIElementTypeButton[@label="Internet Security"]')
    INTERNET_SECURITY_POWER_BUTTON_ON                   = ('btn_zia_switch', '//XCUIElementTypeButton[@title="TURN ON"]')
    INTERNET_SECURITY_POWER_BUTTON_OFF                  = ('btn_zia_switch', '//XCUIElementTypeButton[@title="TURN OFF"]')
    INTERNET_SECURITY_POWER_BUTTON_OFF_ACCEPT_BUTTON    = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="Ok"]')
    INTERNET_SECURITY_POWER_BUTTON_OFF_CANCEL_BUTTON    = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    INTERNET_SECURITY_PASSWORD_BOX                      = ('tf_turnoff_popup_password','//XCUIElementTypeSecureTextField')
    INTERNET_SECURITY_PASSWORD_ACCEPT_BUTTON            = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="Ok"]')
    INTERNET_SECURITY_PASSWORD_CANCEL_BUTTON            = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    INTERNET_SECURITY_RETRY_BUTTON                      = ('btn_zia_switch','//XCUIElementTypeButton[@title="RETRY"]')
    INTERNET_SECURITY_DISABLE_REASON                    = ('tv_turnoff_popup_reason','//XCUIElementTypeTextView[@identifier="tv_turnoff_popup_reason"]')
    INTERNET_SECURITY_DISABLE_REASON_CANCEL             = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    INTERNET_SECURITY_DISABLE_REASON_ACCEPT             = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="Ok"]')
    INTERNET_SECURITY_PASSWORD_FAILED_OK_BUTTON         = ''

    # PRIVATE ACCESS
    PRIVATE_ACCESS_TAB                              = ('btn_home_zpa', '//XCUIElementTypeButton[@label="Private Access"]')
    PRIVATE_ACCESS_POWER_BUTTON_ON                  = ('btn_zpa_switch', '//XCUIElementTypeButton[@title="TURN ON"]')
    PRIVATE_ACCESS_POWER_BUTTON_OFF                 = ('btn_zpa_switch', '//XCUIElementTypeButton[@title="TURN OFF"]')
    PRIVATE_ACCESS_POWER_BUTTON_OFF_ACCEPT_BUTTON   = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="Ok"]')
    PRIVATE_ACCESS_POWER_BUTTON_OFF_CANCEL_BUTTON   = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    PRIVATE_ACCESS_PASSWORD_BOX                     = ('tf_turnoff_popup_password','//XCUIElementTypeSecureTextField')
    PRIVATE_ACCESS_PASSWORD_ACCEPT_BUTTON           = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="Ok"]')
    PRIVATE_ACCESS_PASSWORD_CANCEL_BUTTON           = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    PRIVATE_ACCESS_REAUTH_BUTTON                    = ('btn_zpa_authenticate','//XCUIElementTypeWindow/XCUIElementTypeButton[6]')
    PRIVATE_ACCESS_REAUTH_BUTTON_2                  = ''
    PRIVATE_ACCESS_EARLY_REAUTH_BUTTON              = ('btn_zpa_authenticate','//XCUIElementTypeButton[@title="AUTHENTICATE EARLY"]')
    PRIVATE_ACCESS_DISABLE_REASON                   = ('tv_turnoff_popup_reason','//XCUIElementTypeTextView[@identifier="tv_turnoff_popup_reason"]')
    PRIVATE_ACCESS_DISABLE_REASON_CANCEL            = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    PRIVATE_ACCESS_DISABLE_REASON_ACCEPT            = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="Ok"]')
    PRIVATE_ACCESS_PASSWORD_FAILED_OK_BUTTON        = ''

    # REPORT AN ISSUE
    REPORT_AN_ISSUE_BUTTON                                  = ('btn_more_troubleshoot_reportanissue','//XCUIElementTypeButton[@title="Report An Issue"]')
    REPORT_AN_ISSUE_NAME_BOX                                = ('tf_reportissue_name', '//XCUIElementTypeWindow/XCUIElementTypeTextField[1]')
    REPORT_AN_ISSUE_EMAIL_BOX                               = ('tf_reportissue_email','//XCUIElementTypeDialog/XCUIElementTypeStaticText[1]')
    REPORT_AN_ISSUE_COMMENTS                                = ('tv_reportissue_comments','//XCUIElementTypeWindow/XCUIElementTypeScrollView/XCUIElementTypeTextView')
    REPORT_AN_ISSUE_CHOOSE_FILE                             = ('btn_reportissue_choosefile','//XCUIElementTypeButton[@title="Choose File"]')
    REPORT_AN_ISSUE_SEND_BUTTON                             = ('btn_reportissue_send', '//XCUIElementTypeButton[@title="Send"]')
    REPORT_AN_ISSUE_CC                                      = ('tf_reportissue_cc','//XCUIElementTypeWindow/XCUIElementTypeTextField[2]')
    REPORT_AN_ISSUE_CANCEL                                  = ('btn_reportissue_cancel','//XCUIElementTypeButton[@title="Cancel"]')
    REPORT_AN_ISSUE_PROBLEM_DROPDOWN                        = ('pop_reportissue_problem','//XCUIElementTypeWindow/XCUIElementTypePopUpButton[1]')
    REPORT_AN_ISSUE_PROBLEM_DROPDOWN_USER_INTERFACE         = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="User Interface"]'
    REPORT_AN_ISSUE_PROBLEM_DROPDOWN_AUTHENTICATION         = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Authentication"]'
    REPORT_AN_ISSUE_PROBLEM_DROPDOWN_NETWORK_CONNECTIVITY   = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Network Connectivity"]'
    REPORT_AN_ISSUE_PROBLEM_DROPDOWN_PRIVATE_ACCESS         = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Private Access"]'
    REPORT_AN_ISSUE_PROBLEM_DROPDOWN_OTHERS                 = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Others"]'
    REPORT_AN_ISSUE_PRIORITY_DROPDOWN                       = ('pop_reportissue_priority','//XCUIElementTypeWindow/XCUIElementTypePopUpButton[2]')
    REPORT_AN_ISSUE_PRIORITY_DROPDOWN_URGENT                = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Urgent"]'
    REPORT_AN_ISSUE_PRIORITY_DROPDOWN_HIGH                  = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="High"]'
    REPORT_AN_ISSUE_PRIORITY_DROPDOWN_NORMAL                = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Normal"]'
    REPORT_AN_ISSUE_PRIORITY_DROPDOWN_LOW                   = '//XCUIElementTypeMenu/XCUIElementTypeMenuItem[@title="Low"]'


    # DIGITAL EXPERIENCE
    DIGITAL_EXPERIENCE_TAB                                  = ('btn_home_zdx', '//XCUIElementTypeButton[@label="Digital Experience"]')
    DIGITAL_POWER_BUTTON_ON                                 = ('btn_zdx_switch', '//XCUIElementTypeButton[@title="TURN ON"]')
    DIGITAL_POWER_BUTTON_OFF                                = ('btn_zdx_switch', '//XCUIElementTypeButton[@title="TURN OFF"]')
    DIGITAL_EXPERIENCE_TURN_OFF_ACCEPT_BUTTON               =  ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="Ok"]')
    DIGITAL_EXPERIENCE_TURN_OFF_CANCEL_BUTTON               = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    DIGITAL_EXPERIENCE_PASSWORD_BOX                         = ('tf_turnoff_popup_password','//XCUIElementTypeSecureTextField')
    DIGITAL_EXPERIENCE_PASSWORD_ACCEPT_BUTTON               = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="Ok"]')
    DIGITAL_EXPERIENCE_PASSWORD_CANCEL_BUTTON               = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    DIGITAL_EXPERIENCE_CLEAR_DATA                           = ('btn_zdx_clear','//XCUIElementTypeButton[@title="Clear ZDX Data"]')
    DIGITAL_EXPERIENCE_RESTART_ZDX                          = ('btn_zdx_restart','//XCUIElementTypeButton[@title="Restart ZDX Service"]')
    DIGITAL_EXPERIENCE_DISABLE_REASON                       = ('tv_turnoff_popup_reason','//XCUIElementTypeTextView[@identifier="tv_turnoff_popup_reason"]')
    DIGITAL_EXPERIENCE_DISABLE_REASON_CANCEL                = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    DIGITAL_EXPERIENCE_DISABLE_REASON_ACCEPT                = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="Ok"]')

    # ZCC burger icon menu for SE and Machine Tunnel checks
    ZCC_SIDE_MENU_ICON                              = ("btn_login_menu",'//XCUIElementTypeButton[@label="Menu"]')
    ZCC_UPDATE_POLICY_FROM_SIDE_MENU_BUTTON         = ("btn_menu_policy",'//XCUIElementTypeButton[@title="Update Policy"]')
    ZCC_START_PACKET_CAPTURE_FROM_SIDE_MENU_BUTTON  = ("btn_menu_packet",'//XCUIElementTypeButton[@title="Start Packet Capture"]')
    ZCC_STOP_PACKET_CAPTURE_FROM_SIDE_MENU_BUTTON   = ("btn_menu_packet",'//XCUIElementTypeButton[@title="Stop Packet Capture"]')
    ZCC_REPORT_AN_ISSUE_FROM_SIDE_MENU_BUTTON       = ("btn_menu_report",'//XCUIElementTypeButton[@title="Report an Issue"]')
    ZCC_CLOUD_NAME_FROM_SIDE_MENU_BUTTON            = ("btn_menu_cloud",'//XCUIElementTypeButton[@title="Cloud Name"]')
    ZCC_LICENSE_AGREEMENT_FROM_SIDE_MENU_BUTTON     = ("btn_menu_license",'//XCUIElementTypeButton[@title="License Agreement"]')
    ZCC_ABOUT_FROM_SIDE_MENU_BUTTON                 = ("btn_menu_about",'//XCUIElementTypeButton[@title="About"]')


    # REVERT 
    REVERT_ZCC                          = ("btn_more_troubleshoot_revertapp", '//XCUIElementTypeButton[@title="Revert App"]')
    REVERT_ZCC_PASSWORD_BOX             = ('tf_turnoff_popup_password','//XCUIElementTypeSecureTextField')
    REVERT_ZCC_PASSWORD_ACCEPT_BUTTON   = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="Ok"]')
    REVERT_ZCC_PASSWORD_CANCEL_BUTTON   = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    
    # DISABLE REASON
    PASSWORD_DISABLE_REASON_BOX         = ('tv_turnoff_popup_reason','//XCUIElementTypeTextView[@identifier="tv_turnoff_popup_reason"]')
    DISABLE_REASON_BOX                  = ('tv_turnoff_popup_reason','//XCUIElementTypeTextView[@identifier="tv_turnoff_popup_reason"]')
    PASSWORD_DISABLE_REASON_OK_BUTTON   = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="Ok"]')
    CONFIRM_ACCEPT_BUTTON               = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="Ok"]')
    DISABLE_REASON_CANCEL_BUTTON        = ('btn_turnoff_popup_cancel', '//XCUIElementTypeButton[@title="Cancel"]')
    DISABLE_REASON_OK_BUTTON            = ('btn_turnoff_popup_ok', '//XCUIElementTypeButton[@title="Ok"]')

    # PARTNER LOGIN FEATURE 
    ADD_PARTNER_TENANT_BUTTON                       = ('btn_zpa_addPartner','//XCUIElementTypeButton[@title="+ Add Partner Tenant"]')
    ADD_PARTNER_TENANT_AT_ZPA_PARTNER_BUTTON        = ('btn_partner_addtenant', '//XCUIElementTypeButton[@title="+ Add Partner Tenant"]')
    PARTNER_TENANT_USER_ID_BOX                      = ("tf_addpartner_popup_username",'//XCUIElementTypeTextField')
    PARTNER_TENANT_USER_ID_CONFIRM                  = ("btn_addpartner_popup_submit",'//XCUIElementTypeButton[@title="Submit"]')
    PARTNER_TENANT_USER_ID_CANCEL                   = ("btn_addpartner_popup_cancel",'//XCUIElementTypeButton[@title="Cancel"]')
    PARTNER_TENANT_LOGIN_ERROR_OK_BUTTON            = ("_NS:8",'//XCUIElementTypeButton[@title="OK"]')
    MAIN_TENANT_USER_NAME_BUTTON                    = ("_NS:42","//XCUIElementTypeWindow/XCUIElementTypeButton[9]")
    MAIN_TENANT_SERVICE_STATUS_LABEL                = "lbl_partner_main_status"
    MAIN_TENANT_SERVICE_STATUS_PARTNER_ACTIVE       = ("lbl_partner_main_status",'//XCUIElementTypeStaticText[@value="PARTNER ACTIVE"]')
    MAIN_TENANT_SERVICE_STATUS_OFF                  = ("lbl_partner_main_status",'//XCUIElementTypeStaticText[@value="OFF"]')
    MAIN_TENANT_SERVICE_STATUS_ON                   = ("lbl_partner_main_status",'//XCUIElementTypeStaticText[@value="ON"]')
    MAIN_TENANT_POWER_BUTTON                        = ("btn_partner_main_switch","//XCUIElementTypeWindow/XCUIElementTypeButton[13]")
    MAIN_TENANT_POWER_PASSWORD_BOX                  = ('tf_turnoff_popup_password','//XCUIElementTypeSecureTextField')
    MAIN_TENANT_POWER_DISABLE_REASON_BOX            = ("tv_turnoff_popup_reason",'//XCUIElementTypeTextView[@label="Enter reason for disabling the service"]')
    MAIN_TENANT_POWER_PASSWORD_DISABLE_REASON_BOX   = ("tv_turnoff_popup_reason",'//XCUIElementTypeTextView[@label="Enter reason for disabling the service"]')
    MAIN_TENANT_POWER_DISABLE_PASSWORD_OK_BUTTON    = ("btn_turnoff_popup_ok",'//XCUIElementTypeDialog/XCUIElementTypeButton[@title="Ok"]')
    MAIN_TENANT_POWER_DISABLE_CONFIRM               = ("btn_turnoff_popup_ok",'//XCUIElementTypeDialog/XCUIElementTypeButton[@title="Ok"]')
    MAIN_TENANT_POWER_DISABLE_CANCEL                = ("btn_turnoff_popup_cancel",'//XCUIElementTypeDialog/XCUIElementTypeButton[@title="Cancel"]')
    PARTNER_TENANT_USER_NAME_BUTTON                 = ("tbl_btn_partner_username_{0}", "//XCUIElementTypeCell/XCUIElementTypeButton[1]")
    PARTNER_TENANT_POWER_BUTTON                     = ("tbl_btn_partner_action_{0}","//XCUIElementTypeCell/XCUIElementTypeButton[3]")
    PARTNER_TENANT_POWER_PASSWORD_BOX               = ""
    PARTNER_TENANT_POWER_DISABLE_REASON_BOX         = ""
    PARTNER_TENANT_POWER_DISABLE_CONFIRM            = ""
    PARTNER_TENANT_POWER_DISABLE_CANCEL             = ""
    PARTNER_TENANT_SERVICE_STATUS_LABEL             = "tbl_lbl_partner_status_{0}"
    PARTNER_TENANT_SERVICE_STATUS_OFF               = ("tbl_lbl_partner_status_{0}",'//XCUIElementTypeStaticText[@value="OFF"]')
    PARTNER_TENANT_SERVICE_STATUS_ON                = ("tbl_lbl_partner_status_{0}",'//XCUIElementTypeStaticText[@value="ON"]')
    REMOVE_PARTNER_TENANT                           = ("tbl_btn_partner_delete_{0}",'//XCUIElementTypeCell/XCUIElementTypeButton[2]')
    REMOVE_PARTNER_TENANT_CONFIRM                   = ("btn_turnoff_popup_ok",'//XCUIElementTypeDialog/XCUIElementTypeButton[@title="Ok"]')
    REMOVE_PARTNER_TENANT_CANCEL                    = ("btn_turnoff_popup_cancel",'//XCUIElementTypeDialog/XCUIElementTypeButton[@title="Cancel"]')

    
    # ZDP
    DATA_PREVENTION_TAB                         = ''
    DATA_PREVENTION_POWER_BUTTON_ON             = ''
    DATA_PREVENTION_POWER_BUTTON_OFF            = ''
    DATA_PREVENTION_PASSWORD_BOX                = ''
    DATA_PREVENTION_PASSWORD_ACCEPT_BUTTON      = ''
    DATA_PREVENTION_PASSWORD_FAILED_OK_BUTTON   = ''
    DATA_PREVENTION_PASSWORD_CANCEL_BUTTON      = ''


    # UNINSTALL PASSWORD
    UNINSTALL_PASSWORD_BOX                              = ''
    UNINSTALL_PASSWORD_OK_BUTTON                        = ''
    UNINSTALL_PASSWORD_FAILURE_OK_BUTTON                = ''
    ZIA_RETRY_CONNECTION_ERROR                          = ''
    DISABLE_ANTI_TAMPERING                              = ''
    ENABLE_ANTI_TAMPERING                               = ''
    ENABLE_ANTI_TAMPERING_SUCCESS_OK_BUTTON             = ''
    DISABLE_ANTI_TAMPERING_PASSWORD_BOX                 = ''
    DISABLE_ANTI_TAMPERING_PASSWORD_BOX_OK_BUTTON       = ''
    DISABLE_ANTI_TAMPERING_PASSWORD_FAILED_OK_BUTTON    = ''
    
    # EXIT ZCC
    EXIT_ZCC_PASSWORD_BOX       = ("tf_confirm_popup_password",'//XCUIElementTypeSecureTextField')
    EXIT_ZCC_OK_BUTTON          = ("btn_confirm_popup_ok",'//XCUIElementTypeButton[@title="Ok"]')
    EXIT_ZCC_CANCEL_BUTTON      = ("btn_confirm_popup_cancel",'//XCUIElementTypeButton[@title="Cancel"]')

    # ERROR/WARNING
    ERROR_POPUP_OK_BUTTON       = ("_NS:8",'//XCUIElementTypeSheet[@label="alert"]/XCUIElementTypeButton[@title="OK"]')
    SE_ERROR_POPUP_OK_BUTTON    = ("_NS:81",'//XCUIElementTypeButton[@title="Ok"]')
    CONFIRM_POPUP_OK_BUTTON     = ("btn_confirm_popup_ok",'//XCUIElementTypeButton[@title="Ok"]')
    WARNING_POPUP_OK_BUTTON     = ("btn_warning_ok",'//XCUIElementTypeButton[@title="Ok"]')
    ERROR_POPUP_OK_BUTTON_2     = ("_NS:81",'//XCUIElementTypeWindow/XCUIElementTypeOther[2]')
    APP_ERROR_POPUP_OK_BUTTON   = ("btn_warning_ok",'//XCUIElementTypeWindow/XCUIElementTypeOther[3]/XCUIElementTypeButton')


    # Alert Box - XPATH Currently defined for SE, accessibility ID can be used for any similar alert
    ALERT_MESSAGE_LABEL = ("lbl_warning_title",'//XCUIElementTypeStaticText[@value="Strict Enforcement setup failed (3175)"]')

    #ONE ID
    ONEID_LOGIN_ERROR_OK_BUTTON             = ('btn_warning_ok','//XCUIElementTypeWindow/XCUIElementTypeOther[3]/XCUIElementTypeButton')
    ONEID_LOGIN_ERROR_LEARN_MORE            = ('_NS:87','//XCUIElementTypeWindow/XCUIElementTypeOther[2]')
    ONEID_LOGIN_PAGE_PASSWORD_BOX           = '//XCUIElementTypeSecureTextField[@value="Enter Your Password..."]/XCUIElementTypeGroup'
    ONEID_LOGIN_PAGE_SIGNIN_BUTTON          = '//XCUIElementTypeWebView[@label="Zscaler Login"]/XCUIElementTypeGroup[1]/XCUIElementTypeButton[2]'
    ONEID_LOGIN_PAGE_BACK_BUTTON            = ('btn_login_back','//XCUIElementTypeWindow/XCUIElementTypeButton[4]')
    ONEID_LOGIN_PAGE_TROUBLE_SIGNIN_BUTTON  = '//XCUIElementTypeWebView[@label="Zscaler Login"]/XCUIElementTypeGroup[1]/XCUIElementTypeButton[3]'
    #ONEID_LOGIN_PAGE_ERROR_SIGNIN_POPUP    = '//XCUIElementTypeWebView[@label="Zscaler Login"]/XCUIElementTypeGroup[4]/XCUIElementTypeGroup'
    ONEID_LOGIN_PAGE_ERROR_SIGNIN_POPUP     = '//XCUIElementTypeStaticText[@value="Log into your Zscaler Identity account."]'
    ONEID_AUP_ACCEPT                        = ('btn_aup_accept','//XCUIElementTypeWindow/XCUIElementTypeButton[6]')
    ONEID_AUP_DECLINE                       = ('btn_aup_decline','//XCUIElementTypeWindow/XCUIElementTypeButton[5]')
    
    #Invalid Device Token
    INVALID_DEVICE_TOKEN    = ("",'//XCUIElementTypeStaticText[@value="Invalid device token"]')

    # ZCC UI Language
    OPEN_ZSCALER_LANGUAGE_MAPPING = {
        "english": "Open"
    }
    # Cloud Name
    MENU_BUTTON         = ("btn_login_menu",'//XCUIElementTypeButton[@label="Menu"]')
    CLOUD_NAME_BUTTON   = ("btn_menu_cloud",'//XCUIElementTypeWindow/XCUIElementTypeButton[6]')
    CLOUD_NAME_TEXT     = ("tf_cloud_name",'//XCUIElementTypeTextField')
    CLOUD_NAME_SAVE     = ("btn_cloud_save",'//XCUIElementTypeWindow/XCUIElementTypeButton[6]')
    CLOUD_NAME_OK       = ""

    # Scaling Zscaler UI
    SCALE_ZSCALER_UI=("",'//XCUIElementTypeWindow/XCUIElementTypeButton[3]')
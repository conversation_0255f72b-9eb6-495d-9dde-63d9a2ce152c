class JenkinsSuiteConfig():
    """
    Class which contains the mapping of configs & testcase paths to each test suite for jenkins runs
    """
    def __init__(self, config:str, extra_config:str, zcc_version:str):
        self.suite_owners = {
            1:'<EMAIL>',
            2:'cka<PERSON><EMAIL>',
            3:'ssalma<PERSON><PERSON><PERSON>@zscaler.com',
            4:'<EMAIL>',
            5:'ssi<PERSON><PERSON>@zscaler.com',
            6:'<EMAIL>'
        }
        
        self.suite_config_mapping = {
            "POC Test":{
                "testcases":"pytest_mac/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version
            },
            "Auto Upgrade":{
                "testcases":"zcc_misc/zcc_auto_upgrade/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[5]
            },
            "Login Hint":{
                "testcases":"zcc_misc/zcc_login_hint/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[5]
            },
            "Disable Passwords":{
                "testcases":"zcc_misc/zcc_password_regression/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[2]
            }, 
            "Revert":{
                "testcases":"zcc_misc/zcc_revert/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[5]
            },
            "Strict Enforcement":{
                "testcases":"zcc_misc/zcc_strict_enforcement/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[2]
            },
            "Synthetic IP Configuration":{
                "testcases":"zcc_misc/zcc_synthetic_ip_configuration/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[1]
            },
            "ZCC F5 Split Tunnel VPN Interop":{
                "testcases":"zcc_vpn_interop/vpn_f5/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[2]
            },
            "ZIA Posture":{
                "testcases":"zia/zia_posture/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[3]
            },
            "Partner Login":{
                "testcases":"zpa/zcc_partner_login/",
                "config":config,
                "extraConfig":extra_config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[1]
            },
            "BBA":{
                "testcases":"zcc_misc/zcc_bba_auth/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[2]
            },
            "MA as IdP":{
                "testcases":"zcc_misc/zcc_ma_idp/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[2]
            },
            "Posture Based App Profile Assignment":{
                "testcases":"zcc_misc/zcc_posture_based_app_profile/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[3]
            },
            "Posture Based Service Entitlement":{
                "testcases":"zcc_misc/zcc_posture_based_service/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[3]
            },
            "Login":{
                "testcases":"zcc_misc/zcc_auth_saml_form/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[5]
            },
            "Zoom Bypass":{
                "testcases":"zia/zcc_tunnel_bypass/zcc_zoom_bypass/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[2]
            },
            "Device Posture":{
                "testcases":"zpa/zcc_device_postures/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[3]
            },
            "Zcc Sanity":{
                "testcases":"daily_sanity/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[2]
            },
            "Broker Failover Mechanism":{
                "testcases":"zpa/broker_failover_mechanism/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[4]
            },
            "Tunnel 2 Sanity":{
                "testcases":"zia/tunnel_2/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[1]
            },
            "Tunnel 1 Sanity":{
                "testcases":"zia/tunnel_1/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[1]
            },
            "Tunnel TWLP Sanity":{
                "testcases":"zia/tunnel_twlp/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[1]
            },
            "Tunnel Enforce Proxy Sanity":{
                "testcases":"zia/tunnel_enforce_proxy/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[1]
            },
            "ZPA Regression":{
                "testcases":"zpa/zcc_zpa_regression/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[4]
            },
            "Installation":{
                "testcases":"zcc_misc/zcc_install/",
                "config":config,
                "extraConfig":extra_config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[2]
            },
            "ZCC Cisco Anyconnect Split Tunnel VPN Interop":{
                "testcases":"zcc_vpn_interop/cisco_anyconnect/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[2]
            },
            "ZPA Reauth":{
                "testcases":"zpa/zcc_zpa_reauth/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[4]
            },
            "DLP Notifications":{
                "testcases":"zcc_misc/zcc_dlp_notifications/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version
            },
            "Tunnel 2 Fallback":{
                "testcases":"zia/tunnel_2_fallback/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[2]
            },
            "DNS Regression":{
                "testcases":"zia/tunnel_2_dns/",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[2]
            },
            "One Id":{
                "testcases":"one_id/one_id_phase_1",
                "config":config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[5]
            },
            "Proxy Caching":{
                "testcases":"zia/proxy_caching/",
                "config":config,
                "extraConfig":extra_config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[2]
            },
            "External Proxy Caching":{
                "testcases":"zia/external_proxy/",
                "config":config,
                "extraConfig":extra_config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[2]
            },
            "Daily Sanity Zpa IPv6":{
                "testcases":"zpa/daily_sanity_zpa_ipv6/",
                "config":config,
                "extraConfig":extra_config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[5]
            },
            "ZPA Trusted Networks":{
                "testcases":"zpa/zpa_trusted_networks/",
                "config":config,
                "extraConfig":extra_config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[4]
            },
            "Adaptive Device Postures":{
                "testcases":"zcc_misc/zcc_adaptive_postures/test_adaptive_firewall_posture.py zcc_misc/zcc_adaptive_postures/test_adaptive_crowdstrike_posture.py zcc_misc/zcc_adaptive_postures/test_adaptive_crowdstrike_zta_Score_posture.py",
                "config":config,
                "extraConfig":extra_config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[3]
            },
            "Zcc Firewall":{
                "testcases":"zia/zcc_firewall/persistent_false/",
                "config":config,
                "extraConfig":extra_config,
                "zccInstall":"true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[2]
            },
            "DLP":{
                "testcases": "zdp/",
                "config": config,
                "extraConfig": extra_config,
                "zccInstall": "true",
                "zccVersion": zcc_version,
                "suiteOwner": self.suite_owners[1]
            }
        }
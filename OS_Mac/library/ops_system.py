################################################################
#                       SYSTEM_OPS.PY                          #
#                                                              #
# AUTHOR : SAHIL KUMAR (<EMAIL>)               #
# CREDITS : SRINIVASA BHASWANTH, ABHIMANYU SHARMA              #
#                                                              #
################################################################
'''
Last Changes Made : 25/05/2022 by <PERSON>hil
Details : Added Installed Zscaler Root CA cert support

'''
import subprocess
import os
import time
import socket
import OpenSSL
import ssl
from datetime import date
import shutil
from zipfile import ZipFile
import os.path
from threading import Thread
from common_lib.common import constants
import allure
import json
import dns.resolver
from datetime import datetime
import pytz
from common_lib.common.logger import Logger
import netifaces
import ipaddress
import re
import hashlib


class SysOps:
    def __init__(self, log_handle=None, cloud=None):
        self.logger = (log_handle if log_handle else Logger.initialize_logger("SysOps.log", log_level="DEBUG"))
        self.firewall_rule_string_mac = []
        self.const = constants.Utils
        self.cloud = cloud

    def execute_command(
            self,
            command: str,  # Command to be ran like curl or ping
            application=None,  # Application to be accessed via command
            fetch_ip=False,  # Decides to fetch ip of app or not
    ):
        '''
        Flow:  ------------------------------
            1. For now it does curl or ping to the given application
            2. Then it fetches the ip of the given application
            3. Returns the result of curl or ping command and ip of the application
        '''
        self.logger.info('COMMAND : {}'.format(command))
        try:
            p1 = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            result = p1.communicate()
            result = list(result)
            for i in range(len(result)):
                result[i] = result[i].decode('ascii')
        except Exception as e:
            self.logger.error("ERROR WITH TERMINAL COMMAND :: {}".format(e))
            raise Exception("ERROR WITH TERMINAL COMMAND :: {}".format(e))
        if not fetch_ip:
            self.logger.info(f"Returned Result")
            return result
        else:
            ip = self.fetch_ip(application)[2]

            self.logger.info("Returned Result & ip")
            return result, ip

    def fetch_ip(self,
                 application):
        """
        Fetches the IP address for a given application URL.

        Args:
            application (str): The URL of the application for which the IP address is to be fetched.

        Returns: a Tuple with 3 values ,
            bool : whether the function execution was succes or not
            str : Error or success message
            str: The IP address of the application, or None if the IP address could not be fetched.
        """
        ip = ''
        self.logger.info(f"Fetching ip for  : {application}")
        if 'https://' in application:
            application = application.split('//')[1]
        try:
            ip = socket.gethostbyname(application)
        except socket.gaierror:
            ip = None
            return False, "Error : gai error while fetching IP", ip
        except socket.getaddrinfo:
            ip = None
            return False, "Error : get address info error while fetching IP", ip
        except Exception as e:
            return False, f"Unknown error occurred when fetching ip via socket: {e}", None
        return True, "Success : IP fetched successfully", ip

    def get_certificate(self, host, port=443, timeout=10, proxy=False):
        '''
        Flow : ------------------------------
            This Function hits traffic onto given host and gets its SSL certificate
        Returns: Tuple with 3 values
            bool : True for succes and False for error
            str: A message indicating the success or failure of the download.
            str: The SSL certificate in PEM format.
        '''
        try:
            self.logger.info("Fetching certificate for : {} ".format(host))
            if proxy:
                self.logger.info("Going Via Proxy")
                proxy_host, proxy_port = ('127.0.0.1', 9000)
                connect = f"CONNECT {host}:{port} HTTP/1.0\r\nConnection: close\r\n\r\n"
                conn = socket.socket()
                conn.connect((proxy_host, proxy_port))
                conn.send(str.encode(connect))
                conn.recv(4096)
                context = ssl.SSLContext(ssl.PROTOCOL_SSLv23)
            else:
                self.logger.info("Not Going Via Proxy")
                context = ssl._create_unverified_context()  # this line is edited from previous code, in previous code context was -> ssl.create_default_context(), but at times zscaler cert is causing ssl verification handshake failed<(_ssl.c:726)>, so we changed context to ssl._create_unverified_context()
                conn = socket.create_connection((host, port))
            sock = context.wrap_socket(conn, server_hostname=host)
            sock.settimeout(timeout)
            print('Host is {}'.format(host))
            try:
                der_cert = sock.getpeercert(True)
            finally:
                sock.close()
        except Exception as e:
            self.logger.error(f'Error : Unable to get ssl certificate :: {e}')
            return False, f"Error : Unable to get ssl certificate :: {e}", None
        return True, 'Success : Got the ssl certificate', ssl.DER_cert_to_PEM_cert(der_cert)

    def download_files(self,
                       url,
                       file_type
                       ):
        """
        Downloads files from a given URL and saves them with a specific file type.

        Args:
            url (str): The URL from which the file will be downloaded.
            file_type (str): The file type to be used when saving the downloaded file.

        Returns: Tuple with 3 values
            bool : True for succes and False for error
            str: A message indicating the success or failure of the download.
            str: Download results and in case of failure None
        """
        try:
            self.logger.info("Download URL : {}".format(url))
            today = date.today()
            file_name = os.getcwd() + '/' + today.strftime("%b-%d-%Y") + file_type
            command = "curl {} --output {}".format(url, file_name)
            download_result = self.execute_command(command=command)
            print(download_result[-1])
        except Exception as e:
            return (False, f"Error : In downloading file {e}", None)
        return (True, "Success : Downloaded the file successfully", download_result[-1])

    @allure.step("Access Application")
    def access_application(self,
                           # (str)     application to be accessed
                           application,
                           # (str)     defines whether to curl or ping application, by default curl
                           access_type='curl',
                           # (bool)    defines whether app should be accessed or not, by default true
                           access_flag=True,
                           # (str/bool) defines proxy via which traffic should be sent/in case of cert check via proxy give True
                           proxy=None,
                           # (bool)   Give True if cert should be of zscaler, else give false, give none if cert is not to be checked
                           is_zcert=None,
                           # (bool)    When ZCC is installed in SE mode the Applications/Internet is accessable or not.
                           se_access_app_block=False,
                           ping_size=None,  # (int) Size of ping/icmp
                           # (str) ip of application to access - Used for DNS access
                           ip=None,
                           # (str) can take values to check if proxy agent is fine.
                           proxy_agent='Zscaler',
                           # (str) For Curl Request can be bind to local port
                           source_port=None,
                           # (bool) set it to True, to enforce '-4' switch in curl. Mainly used for dual stack machines which have ipv6 DNS server and user wants to access application via ipv4 only
                           enforce_v4=False,
                           # (str) you can give any dns server ip
                           dns_server_change=None
                           ):
        '''
        Flow : ------------------------------
            1. We check if we want to validate certificate or just hit normal traffic
            2. If validate certificate:
                2.1 We use get_certificate which returns the certificate for given application(host) 
                2.2 If you are sending traffic via proxy and want to validate certifciate, give proxy=True
                2.2 Then according to the value of is_zcert(true/false), we check the cert
                    2.2.1 If isZscert==True -> Zscaler Certificate is expected
                    2.2.2 If isZscert==False -> Non Zscaler Certificate is expected
            3. Else we make command according to given traffic type ie. curl or ping
            4. If you want to send traffic via proxy, give proxy in string format like 127.0.0.1:9000
            2. THen we validate results,
                2.1 If curl -> we see if there is 200 OK in response
                2.2 If ping -> we determine packet loss % 
        Returns: Tuple with 3 values
            bool : True for succes and False for error
            str: A message indicating the success or failure of the download.
            str: None
        '''
        try:
            if is_zcert is not None:  # Should be True or False
                self.logger.info("Checking for Certificate")
                certificate = self.get_certificate(application, proxy=proxy)[2]

                x509 = OpenSSL.crypto.load_certificate(
                    OpenSSL.crypto.FILETYPE_PEM,
                    certificate
                )
                issuer = dict(x509.get_issuer().get_components())
                print(issuer)
                try:
                    issuer = str(issuer['CN'])
                except:
                    issuer = str(issuer[b'CN'])
                self.logger.info(
                    'CN in Issuer is {}, {}'.format(issuer, type(issuer)))

                if (is_zcert is True and 'Zscaler' in issuer) or (is_zcert is False and 'Zscaler' not in issuer):
                    self.logger.info('Matching Certificate Found!')
                if is_zcert is True and 'Zscaler' not in issuer:
                    raise Exception("Zscaler Certificate was expected for {}, but got {}".format(
                        application, issuer))
                if is_zcert is False and 'Zscaler' in issuer:
                    raise Exception("Zscaler Certificate was NOT expected for {}, but got {}".format(
                        application, issuer))
            else:
                if 'curl' in access_type:
                    if enforce_v4:
                        if proxy:  # curl -v domain -x 127.0.0.1:9000
                            if proxy == True:
                                command = 'curl -LI -4 {} -x 127.0.0.1:9000'.format(application)
                            else:
                                command = 'curl -LI -4 {} -x {}'.format(application, proxy)
                        else:
                            command = 'curl -LI -k -4 {}'.format(application)
                    else:
                        if proxy:  # curl -v domain -x 127.0.0.1:9000
                            if proxy == True:
                                command = 'curl -LI {} -x 127.0.0.1:9000'.format(application)
                            else:
                                command = 'curl -LI {} -x {}'.format(
                                    application, proxy)
                        else:
                            command = 'curl -LI -k {}'.format(application)

                    if source_port is not None:
                        command = command + " --local-port {}".format(source_port)

                    command += " -m 60"

                if 'ping' in access_type or 'icmp' in access_type:
                    command = 'ping -c 5 {}'.format(application)
                    if ping_size is not None:
                        command = f'{command} -s {ping_size}'
                if 'dns' in access_type or 'nslookup' in access_type:
                    if dns_server_change is not None:
                        command = f'nslookup {application} {dns_server_change}'
                    else:
                        command = f'nslookup {application}'

                result = self.execute_command(command=command, fetch_ip=False)
                self.logger.info("Access Application Result:\n{}\n".format(result[0], result[1]))
                error_strings = [
                    "HTTP/1.1 301 Moved Permanently",
                    "Failed to connect",
                    "Empty reply from server",
                    "SSL_ERROR_SYSCALL",
                    "Connection timed out",
                    "Could not resolve host:",
                    "CONNECT tunnel failed, response 403",
                    'HTTP/1.1 403 Forbidden',
                    'recv failure',
                    'socket is not connected',
                    "HTTP/1.1 403",
                    "SSL/TLS connection failed"
                ]
                if 'curl' in access_type:
                    if se_access_app_block == True:
                        if (
                                len(result[0]) == 0
                                or any(
                            [
                                True if (each_error_string.lower() in result[0].lower() or each_error_string.lower() in
                                         result[1].lower()) else False for each_error_string in error_strings
                            ]
                        )
                        ):
                            self.logger.info(
                                'Access to Application {} blocked by Strict Enforcement!'.format(application))
                        else:
                            line = 'Request to Application {} made in Strict Enforcement mode::FAIL'.format(application)
                            self.logger.error(line)
                            raise Exception(line)
                    elif access_flag:
                        if "200 OK" in result[0] or "HTTP/2 200" in result[0] or "HTTP/1.1 200" in result[0]:
                            if proxy:
                                if proxy_agent == None:
                                    self.logger.info("Proxy check not required! Skipping.")
                                # Proxy-Agent: Ztunnel/1.0
                                if f"Proxy-Agent: {proxy_agent}" in result[0]:
                                    self.logger.info('App accessed Successfully')
                                else:
                                    raise Exception("ERROR :: Proxy Expected, but not found in curl output")
                            else:
                                self.logger.info('App accessed Successfully')
                        elif len(result[1]) is not 0:
                            self.logger.info(result[1])
                            raise Exception("ERROR :: Access Flag was True, but no result fetched from command")
                        else:
                            raise Exception("ERROR :: 200 OK not found in curl request result")
                    else:
                        if (
                                len(result[1]) == 0
                                or any(
                            [
                                True if (each_error_string.lower() in result[0].lower() or each_error_string.lower() in
                                         result[1].lower()) else False for each_error_string in error_strings
                            ]
                        )
                        ):
                            self.logger.info("App not accessed, case passed")
                        else:
                            raise Exception("ERROR :: Access Flag was False, but result fetched from command")

                if 'icmp' in access_type or 'ping' in access_type:
                    failed_packets = []
                    ping_result = result[0].split('\n')
                    packet_loss = -1
                    for item in ping_result[1:]:
                        if 'Request timeout for icmp_seq' in item:
                            failed_packets.append(item)
                        if 'transmitted' and 'received' in item:
                            item = item.split(',')[-1]
                            packet_loss = int(item.split('.')[0])
                        else:
                            pass
                    if access_flag:
                        if packet_loss > 0:
                            raise Exception(
                                f'Error, ICMP packet loss observed when it is not expected, loss % : {packet_loss}')
                        else:
                            self.logger.info("ICMP packet not lost, loss % : {}".format(packet_loss))
                    else:
                        if packet_loss == -1:
                            self.logger.info(
                                f'ICMP connection was not made, access failed as expected, loss % : {packet_loss}')
                        elif packet_loss < 100:
                            self.logger.error('ICMP packet recieved , loss % : {}'.format(packet_loss))
                            raise Exception('Error: ICMP packets were recieved, ICMP access was not expected')
                        else:
                            self.logger.info(
                                'ICMP packets were not recieved as expected, loss % : {}'.format(packet_loss))

                if 'dns' in access_type or 'nslookup' in access_type:
                    self.logger.info("Access Application Result:\n{}\n".format(result[0], result[1]))

                    to_search = f'Name:\t{application}'

                    if ip is not None:
                        to_search = f'{to_search}\nAddress: {ip}'

                    self.logger.info(f'Searching for {to_search}')

                    if to_search.lower().replace(' ', '').replace('\t', '') in result[0].lower().replace(' ',
                                                                                                         '').replace(
                        '\t', ''):
                        if access_flag == True:
                            self.logger.info(
                                f'DNS access to {application} successful!')
                        else:
                            line = f'DNS access to {application} successful, but it should not be accessable'

                            self.logger.error(line)
                            raise Exception(line)
                    else:
                        if access_flag == True:
                            line = f'DNS access to {application} not successful, but it should be accessable'

                            self.logger.error(line)
                            raise Exception(line)
                        else:
                            self.logger.info(
                                f'DNS access to {application} not successful!')
        except Exception as e:
            self.logger.error(f"Error while validating certificate - {e}")
            return False, f"Error : {e}", None
        return True, "Success : Application accessed successfully" if access_flag else "Success :  Application  not accessed ", None

    def access_ip_zscaler(
            self,
            cloud_name,
            failure_expected=False,
            proxy=False,
            retry_count=0
    ):
        """
            Accesses the Zscaler IP and validates the access.

            :param self: The object itself.
            :param cloud_name: The name of the cloud.
            :param failure_expected: A boolean indicating if a failure is expected. Default is False.
            :param proxy: A boolean indicating if a proxy is to be used. Default is False.
            :param retry_count: The number of times to retry in case of failure. Default is 0.
            :return: A tuple containing a boolean indicating success or failure, a message, and None.
        """
        try:
            return_text = self.execute_command(
                command="curl -v https://ip.zscaler.com" if not proxy else "curl -v https://ip.zscaler.com -x 127.0.0.1:9000")
            validate_string = 'You are accessing the Internet via a Zscaler' if 'beta' in cloud_name.lower() else 'You are accessing the Internet via Zscaler'
            if validate_string in str(return_text):
                if not failure_expected:
                    self.logger.info("Access to ip.zscaler.com validated")
                else:
                    raise Exception("Failure was expected for ip.zscaler.com access, but request went via zscaler")
            else:
                if not failure_expected:
                    if retry_count == 0:
                        print('Retrying one more time after 10 second sleep')
                        time.sleep(10)
                        self.access_ip_zscaler(
                            cloud_name=cloud_name, failure_expected=failure_expected, retry_count=1)
                    else:
                        self.logger.error(f'{return_text}')
                        raise Exception(
                            "Access to ip.zscaler.com validation failed")
                else:
                    self.logger.info("Access to ip.zscaler.com validation failed ~ expected failure")
        except Exception as e:
            return False, f"Error :  {e}", None
        return True, "Success : Application ip.zscaler.com accessed successfully" if not failure_expected else "Success :  Access to ip.zscaler.com validation failed ~ expected failure", None

    def access_application_ipv6(
            self,
            access_type='curl',
            # Access type can be curl, curl full where curl has the command of "curl -LI" and curl full has the command "curl -v" and ping/icmp too
            # Which website/application to access, by default "www.cnn.com" is used.
            website_to_access=None,
            # (bool)    defines whether app should be accessed or not, by default true
            access_flag=True,
            # (bool) defines proxy via which traffic should be sent/in case of cert check via proxy give True
            proxy=None,
            # (bool)    When ZCC is installed in SE mode the Applications/Internet is accessable or not.
            se_access_app_block=False,
            ping_size=None,  # (int) Size of ping/icmp
            # (str) can take values to check if proxy agent is fine.
            proxy_agent='Ztunnel'
    ):
        """
        This function is used to access an application using IPv6. It supports various access types like curl, curl full, ping, and icmp.
        It also supports proxy and strict enforcement mode. It logs the result of the command executed for debugging purposes.
        
        Args:
            access_type (str): The type of access to use. Defaults to 'curl'.
            website_to_access (str): The website or application to access. Defaults to None.
            access_flag (bool): Whether the application should be accessed or not. Defaults to True.
            proxy (bool): Whether to use a proxy for the traffic. Defaults to None.
            se_access_app_block (bool): Whether the application should be blocked in strict enforcement mode. Defaults to False.
            ping_size (int): The size of the ping/icmp packet. Defaults to None.
            proxy_agent (str): The proxy agent to use for the check. Defaults to 'Ztunnel'.
        
        Returns:
            tuple: A tuple containing a boolean indicating success or failure, a message, and None.
        """

        try:
            if website_to_access == None:
                website_to_access = 'www.cnn.com'

            # if not('www.' in website_to_access):
            #     website_to_access = f'www.{website_to_access}'

            if access_type == 'curl':
                if proxy == True:
                    cmd = f'curl -6 -LI https://{website_to_access} -k -x [::1]:9000' if 'http' not in website_to_access else f'curl -6 -LI {website_to_access} -k -x [::1]:9000'
                else:
                    cmd = f'curl -6 -LI https://{website_to_access} -k' if 'http' not in website_to_access else f'curl -6 -LI {website_to_access} -k'
            if access_type == 'curl-full':
                if proxy == True:
                    cmd = f'curl -6 -v https://{website_to_access} -k -x [::1]:9000'
                else:
                    cmd = f'curl -6 -v https://{website_to_access} -k'

            if 'ping' in access_type or 'icmp' in access_type:
                cmd = 'ping6 -c 5 {}'.format(website_to_access)
                if ping_size is not None:
                    cmd = f'{cmd} -s {ping_size}'
            result = self.execute_command(command=cmd, fetch_ip=False)
            self.logger.info(
                "RESULT : \n{}\n*************************\n{}".format(result[0], result[1]))

            if 'curl' in access_type:
                if se_access_app_block == True:
                    if ("HTTP/1.1 403" in result[0] or 'SSL_ERROR_SYSCALL' in result[
                        0] or "SSL/TLS connection failed" in result[0] or len(result[0]) == 0):
                        self.logger.info(
                            'Access to ipv6 Application {} blocked by Strict Enforcement!'.format(website_to_access))
                    else:
                        line = 'Request to Ipv6 Application {} made in Strict Enforcement mode::FAIL'.format(
                            website_to_access)
                        self.logger.error(line)
                        raise Exception(line)
                elif access_flag:
                    if "200 OK" in result[0] or "HTTP/2 200" in result[0] or "HTTP/1.1 200" in result[0]:
                        if proxy:
                            if proxy_agent == None:
                                self.logger.info(
                                    "Proxy check not required! Skipping.")

                            # Proxy-Agent: Ztunnel/1.0
                            if f"Proxy-Agent: {proxy_agent}" in result[0]:
                                self.logger.info('ipv6 App accessed Successfully')
                            else:
                                raise Exception(
                                    "ERROR :: Proxy Expected, but not found in curl output")
                        else:
                            self.logger.info('App accessed Successfully')
                    elif len(result[1]) is not 0:
                        self.logger.info(result[1])
                        raise Exception(
                            "ERROR :: Access Flag was True, but no result fetched from command")
                    else:
                        raise Exception(
                            "ERROR :: 200 OK not found in curl request result")
                else:
                    if len(result[1]) == 0 or "Empty reply from server" in result[1] or "SSL_ERROR_SYSCALL" in result[
                        1] or "Connection timed out" in result[1] or "403 Forbidden" in result[0]:
                        self.logger.info("ipv6 App not accessed, case passed")
                    else:
                        raise Exception(
                            "ERROR :: Access Flag was False, but result fetched from command")

            if 'icmp' in access_type or 'ping' in access_type:
                failed_packets = []
                result = result[0]
                result = result.split('\n')
                for item in result[1:]:
                    if 'Request timeout for icmp_seq' in item:
                        failed_packets.append(item)
                    if 'transmitted' and 'received' in item:
                        item = item.split(',')[-1]
                        packet_loss = int(item.split('.')[0])
                    else:
                        pass

                if access_flag:
                    if packet_loss > 0:
                        raise Exception('Error ::ICMP PACKET LOST DETECTED :')
                    else:
                        self.logger.info(
                            "ICMP PACKET NOT LOST, LOST % : {}".format(packet_loss))
                else:
                    if packet_loss < 100:
                        self.logger.error(
                            'ICMP Packet Recieved , LOSS % : {}'.format(packet_loss))
                        raise Exception('ERROR :: ICMP PACKET RECIEVED DETECTED')
                    else:
                        self.logger.info(
                            'ICMP PACKETS NOT RECIEVED, LOSS % : {}'.format(packet_loss))
        except Exception as e:
            return (False, f"Error :  {e}", None)
        return (
            True,
            "Success : Application accessed successfully" if access_flag else "Success :  Application  not accessed ",
            None)

    def validate_certificate(self,  # function for validating traffic
                             # (str) takes values such as tunnel_1, twlp, tunnel_2_dtls, tunnel_2_tls
                             tunnel_version=None
                             ):
        """
        This function validates the certificate based on the tunnel version provided.
        It checks for different tunnel versions like tunnel_1, twlp, tunnel_2_dtls, and tunnel_2_tls.
        If no tunnel version is provided, it raises an error.
        It accesses the application using the access_application method.
        If there is any exception during the process, it returns False and the error message.
        Otherwise, it returns True and a success message.

        Args:
            tunnel_version (str): The version of the tunnel to validate.

        Returns:
            tuple: A tuple containing a boolean value and a message indicating whether the operation was successful or not.
        """
        try:
            if tunnel_version == None:
                line = "No tunnel Version provided to search!"
                self.logger.error(line)
                raise Exception(line)

            application_to_visit = "indiatvnews.com"

            if tunnel_version == "tunnel_1" or tunnel_version == "tunnel_2_dtls" or tunnel_version == "tunnel_2_tls":

                line = "Checking for {} mode".format(tunnel_version)
                self.logger.info(line)

                app_access = self.access_application(application_to_visit, is_zcert=True)
                if app_access[0] == False:
                    raise Exception(app_access[2])

            else:
                line = "Checking for TWLP mode"
                self.logger.info(line)

                app_access = self.access_application(application_to_visit, is_zcert=False)
                if app_access[0] == False:
                    raise Exception(app_access[2])
                app_access = self.access_application(application_to_visit, proxy=True, is_zcert=True)
                if app_access[0] == False:
                    raise Exception(app_access[2])
        except Exception as e:
            return (False, f"Error : {e} ", None)
        return (True, "Success : Certificate Validated successfully", None)

    def get_zcc_installed_certs(self,
                                # (bool)    defines if cert should be present or not
                                is_cert_required=True,
                                custom_cert=False,
                                non_automation_custom_cert_string=None
                                ):
        """
        This function validates if zscaler root cert is installed on system.
        
        Args:
            is_cert_required (bool): Defines whether cert should be present or not.
            custom_cert (bool): If set to True, the function will look for the custom automation cert.
            non_automation_custom_cert_string (str): If set, the function will look for a custom cert with this label.
        
        Returns:
            tuple: A tuple containing a boolean indicating success or failure, a message, and None.
        """
        try:
            cert_found = False

            if non_automation_custom_cert_string == 'CN=Gurmeet':
                non_automation_custom_cert_string = 'zcc'
            command = 'security find-certificate -a'
            cert_list = self.execute_command(command=command)
            if len(cert_list[0]) > 0 and 'attributes:' in cert_list[0]:
                cert_list = cert_list[0].split('attributes:')
            else:
                raise Exception(
                    "ERROR :: got unexpected value from terminal output")
            # cert_search_string = '"labl"<blob>="Zscaler Root CA"' if custom_cert==None else '"labl"<blob>="Automation_Cert"'
            if custom_cert:
                cert_search_string = '"labl"<blob>="Automation_Cert"'
            elif non_automation_custom_cert_string:
                cert_search_string = f'"labl"<blob>="{non_automation_custom_cert_string}"'
            else:
                cert_search_string = '"labl"<blob>="Zscaler Root CA"'
            self.logger.info("Searching for {}".format(cert_search_string))
            for certs in cert_list:
                if cert_search_string in str(certs):
                    if custom_cert:
                        self.logger.info("Got Custom Automation Cert")
                    else:
                        self.logger.info("Got Zscaler Root CA Cert")
                    cert_found = True
            if is_cert_required:
                if cert_found:
                    self.logger.info("Required Cert is present on system, validated !")
                else:
                    raise Exception("ERROR :: Required Cert not found on system")
            else:
                if not cert_found:
                    self.logger.info("Required Cert is not present on system, validated !")
                else:
                    raise Exception("ERROR :: Required Cert found on system")
        except Exception as e:
            return (False, f"Error :  {e}", None)
        return (True,
                "Success : Required Cert is present on system, validated !" if is_cert_required else "Success :  Required Cert is not present on system, validated ! ",
                None)

    def delete_zcc_installed_certs(self,
                                   custom_cert=None,
                                   non_automation_custom_cert_string=None
                                   ):
        '''

        This function deletes zscaler root certificate installed on system
        If this function keeps on failing for windows, please fetch the latest thumbprint of Zscaler Root CA cert and update it in the command vairable for windows
        '''

        if non_automation_custom_cert_string == 'CN=Gurmeet':
            non_automation_custom_cert_string = 'zcc'
        if custom_cert == None:
            command = 'sudo security delete-certificate -c "Zscaler Root CA"'
        else:
            command = 'sudo security delete-certificate -c "Automation_Cert"'

        if non_automation_custom_cert_string:
            command = f'sudo security delete-certificate -c "{non_automation_custom_cert_string}"'

        self.logger.info(f"Certificate delete command is : {command}")
        delete_command = self.execute_command(command=command)
        if len(delete_command[1]) > 0:
            err = 'ERROR :: ' + str(delete_command[1])
            self.logger.info(err)
            self.logger.info(
                '\n\n************************\nCert not deleted, happens if cert is not there on the system\n************************\n\n')
            return (False, "Error : Cert not deleted, happens if cert is not there on the system", None)
        else:
            self.logger.info("Certificate Deleted from KeyChain Access")
            time.sleep(3)
            return (True, "Success : Certificate Deleted from KeyChain Access", None)

    def apply_pf_rules(
            self,
            reset: bool = False,
            # Defines whether pf should be reset or not, if true, applied rules shoule not be present in pf.conf
            skip: bool = False,
    ):
        '''
        This function just enables or resets the pf rules on MAC
        '''
        try:
            self.logger.info("Now enabling firewall via pfctl")
            commands = [
                'sudo pfctl -f /etc/pf.conf',
                'sudo pfctl -E',
                'sudo pfctl -s rules',
            ]
            for command in commands:
                result = self.execute_command(command=command)
                self.logger.info('\nSTDOUT :: {}\nSTDERR :: {}n\n'.format(result[0].split('\n'), result[1]))
                if command == commands[0] and 'pfctl: Syntax error in config file: pf rules not loaded' in result[1]:
                    raise Exception("ERROR :: Invalid Syntax for PF File, rules not loaded")
                if command == commands[-1] and (not skip):
                    if not reset:
                        for block_rule in self.firewall_rule_string_mac:
                            self.logger.info("Searching for : {}".format(block_rule))
                            if block_rule in result[0]:
                                self.logger.info("RULE APPLIED - {}".format(block_rule))
                            else:
                                raise Exception("{} NOT FOUND IN pfctl -s rules".format(block_rule))
                    else:
                        self.logger.info("PF should be reset")
                        for block_rule in self.firewall_rule_string_mac:
                            if block_rule in result[0]:
                                raise Exception("{} found in pfctl -s rules".format(block_rule))
                            else:
                                self.logger.info("{} not found, pf file reset".format(block_rule))
                                self.firewall_rule_string_mac = []
        except Exception as e:
            return False, f"Error :  {e}", None
        return True, "Success : RULE APPLIED - {}".format(
            block_rule) if not reset else "Success :  {} not found, pf file reset".format(block_rule), None

    def add_delay(
            self,
            dst_ip,
            delay=500
    ):
        """
        This function adds a delay of given milliseconds for the given destination
        """
        try:
            self.logger.info("Now adding delay via pfctl")
            commands = [
                '(cat /etc/pf.conf && echo "dummynet-anchor \"deeelay\"" && echo "anchor \"deeelay\"") | sudo pfctl -f -',
                f'echo "dummynet out from any to {dst_ip} pipe 1" | sudo pfctl -a deeelay -f -',
                f'sudo dnctl pipe 1 config delay {delay}',
                'sudo pfctl -sd',
                'sudo pfctl -a "deeelay" -sd',
                'sudo pfctl -e',
                'sudo dnctl show'
            ]
            for command in commands:
                result = self.execute_command(command=command)
                self.logger.info('\nSTDOUT :: {}\nSTDERR :: {}n\n'.format(result[0].split('\n'), result[1]))
        except Exception as e:
            return False, f"Error: {e}", None

        return True, "Success", None

    def stop_delay(self):
        """
        This function stops the delay added through pfctl
        """
        try:
            self.logger.info("Now stopping delay via pfctl")
            commands = [
                'sudo pfctl -d',
                'sudo pfctl -a ts -F all',
                'sudo dnctl -q flush'
            ]
            for command in commands:
                result = self.execute_command(command=command)
                self.logger.info('\nSTDOUT :: {}\nSTDERR :: {}n\n'.format(result[0].split('\n'), result[1]))
        except Exception as e:
            return False, f"Error: {e}", None

        return True, "Success", None

    def reset_firewall_rule(self):
        '''
        Flow:  ------------------------------
            1. FOR MAC :
                1.1 We pick up data from pf_file_copy.txt in TMP folder and write that it pf.conf in etc folder 
                    and then call apply_pf_rules to enable updated pf file  
        '''
        try:
            self.logger.info('NOW RESETTING FIREWALL')
            pf_file_copy = os.path.join(os.getcwd(), 'orignal_pf_copy.txt')
            original_pf_file = '/etc/pf.conf'
            lines = open(pf_file_copy, 'r').readlines()
            with open(original_pf_file, 'w') as f:
                for line in lines:
                    f.write(line)
            pf_rule_applied = self.apply_pf_rules(reset=True)
            if not pf_rule_applied[0]:
                raise Exception(pf_rule_applied[2])
            self.logger.info("Removing copy pf file")
            os.remove(path=pf_file_copy)
        except Exception as e:
            self.logger.info(f"Error in resetting firewall:  {e}")
            return False, f"Error in resetting firewall:  {e}", None
        self.logger.info("Success: Firewall reset Completed")
        return True, "Success: Firewall reset Completed", None

    def add_firewall_rule(self,
                          skip: bool = False,
                          auto_apply: bool = True,
                          # Automatically call apply_pf_rules function, give false when multiple lines to be added and then explicitly call apply_pf_rules
                          reset_rule_list=False,
                          # Reset the applied rule list, used for mac, for working, read note in docstring below
                          **kwargs,  # (key=val)   keyword arguments
                          ):
        '''
        NOTE ::
            reset_rule_list : this variable is used for mac only
                            when we add rule to disable dtls and tls on sme ip to fallback to tunnel1 from tunnel2
                            and later block complete ip to block tunnel 1 as well, then on apply_pf_rules function
                            when we apply pf and check for applied rules, only complete sme block rule is shown
                            i.e rules with protocol and port combination doesnt come in pfctl -s rules command
                            and only block <smeIP> rule is shown. 
                            So to over come this, we reset the list here for tunnel 2 to tunnel 1 fallback rule
        Flow:  ------------------------------
            1. Accepts key=val pairs, these pairs would be different for mac and windows
            2. if os is MAC:
                2.1 We create a copy of the existing pf.conf file and save it in TMP folder
                2.2 Then we add the key,val pairs into the base firewall_rule_string
                2.3 At last, we append the created firewall_rule_string into original pf.conf

        Key,Val Pairs: ----- THESE KEYS NAMES SHOULD BE EXACT AS DESCRIBED -----
            MAC ->  proto : defines protocol
                    remoteip : remoteip for which rule is to be added or any to block all ips
                    remoteport : remoteport for which rule is to be added
                    command : used to give a predefined or custom command
        '''
        # ******************** FIREWALL RULES FOR MAC ********************
        try:
            original_pf_file = '/etc/pf.conf'
            pf_file_copy = os.path.join(os.getcwd(), 'orignal_pf_copy.txt')

            if reset_rule_list:
                self.firewall_rule_string_mac.clear()
                self.logger.info("Firewall rule list for mac cleared")

            if 'command' in kwargs.keys():
                firewall_rule_string = kwargs['command']
            if len(kwargs.keys()) == 0:
                self.logger.info(
                    "No rules specified, give somehting like remoteip, remoteport or complete command  etc..")
                self.logger.info("Refer to function docstring for usage")
                raise Exception("ERROR :: No command or args given for applying Mac PF Rules")
            else:

                if 'remoteip' not in kwargs.keys():
                    if 'protocol' not in kwargs.keys() and 'remoteport' not in kwargs.keys():
                        self.logger.info("no protocol,ip or port given")
                        firewall_rule_string = "block drop inet from any to any"
                    elif 'protocol' in kwargs.keys() and 'remoteport' in kwargs.keys():
                        self.logger.info("proto and port given, ip not")
                        firewall_rule_string = "block drop inet proto {} from any to any port = {}".format(
                            kwargs['protocol'], kwargs['remoteport'])
                    elif 'protocol' in kwargs.keys() and 'remoteport' not in kwargs.keys():
                        self.logger.info("only proto given")
                        firewall_rule_string = "block drop inet proto {} from any to any".format(kwargs['protocol'])
                    else:
                        raise Exception("Invalid Command")
                else:
                    if 'protocol' not in kwargs.keys() and 'remoteport' not in kwargs.keys():
                        self.logger.info("only ip given")
                        firewall_rule_string = "block drop inet from any to {}".format(kwargs['remoteip'])
                    elif 'protocol' in kwargs.keys() and 'remoteport' in kwargs.keys():
                        self.logger.info("proto,ip and port given")
                        firewall_rule_string = "block drop inet proto {} from any to {} port = {}".format(
                            kwargs['protocol'], kwargs['remoteip'], kwargs['remoteport'])
                    elif 'protocol' in kwargs.keys() and 'remoteport' not in kwargs.keys():
                        self.logger.info("proto and ip given")
                        firewall_rule_string = "block drop inet proto {} from any to {}".format(kwargs['protocol'],
                                                                                                kwargs['remoteip'])
                    else:
                        raise Exception("Invalid Command")

            firewall_rule_string += '\n'
            self.firewall_rule_string_mac.append(firewall_rule_string.lower().replace('  ', ''))
            if not os.path.exists(pf_file_copy):
                self.logger.info("{} not exists, creating it !".format(pf_file_copy))
                original_file_data = open(original_pf_file, 'r').readlines()
                with open(pf_file_copy, 'w') as f:
                    for l in original_file_data:
                        f.write(l)
            self.logger.info(f"Firewall string: {self.firewall_rule_string_mac}")
            try:
                with open(original_pf_file, 'a') as f:
                    self.logger.info('Line to be appended is: {}'.format(self.firewall_rule_string_mac[-1]))
                    f.write(self.firewall_rule_string_mac[-1])
                    self.logger.info("Firewall rule has been written to the pf conf file")
            except Exception as e:
                self.logger.error(f'Error when writing rules to pf.conf file: {e}')
                raise Exception(f'Error when writing rules to pf.conf file: {e}')

            if auto_apply:
                pf_rule_applied = self.apply_pf_rules(skip=skip)
                if not pf_rule_applied[0]:
                    raise Exception(pf_rule_applied[2])
        except Exception as e:
            self.logger.error(f"Firewall rules not applied as expected, Error: {e}")
            return False, f"Firewall rules not applied as expected, Error: {e}", None
        self.logger.info("Success:  Firewall rule added")
        return True, "Success:  Firewall rule added", None

    def add_host_entry(self,
                       # (list) list containing string in format -> 'hostname resolvedIP'
                       resolve_entry
                       ):
        """
        Add host entry to the hosts file.

        :param resolve_entry: List containing string in format 'hostname resolvedIP'
        :type resolve_entry: list
        :return: Tuple containing status, message, and exception (if any)
        :rtype: tuple
        """
        try:
            original_etc_file = constants.Utils.HOSTS_PATH
            etc_file_copy = os.path.join(
                (os.path.join(os.getcwd(), "OS_Mac", "resource")), 'orignalPfCopy.txt')
            if not os.path.exists(etc_file_copy):
                self.logger.info(
                    "{} not exists, creating it !".format(etc_file_copy))
                original_file_data = open(original_etc_file, 'r').readlines()
                with open(etc_file_copy, 'w') as f:
                    for l in original_file_data:
                        f.write(l)
            try:
                with open(original_etc_file, 'a') as f:
                    for item in resolve_entry:
                        self.logger.info(f'Line to be appended is: {item}' + '\n')
                        f.write(item + '\n')
                        self.logger.info("written")
            except Exception as e:
                self.logger.error('Error Writing in etc/host file!')
                self.logger.error("ERROR :: {}".format(e))
                raise Exception(f'Error Writing in etc/host file! :: {e}')
        except Exception as e:
            return (False, f"Error :  {e}", None)
        return (True, "Success :  Host Entry added", None)

    def reset_host_entry(self):
        """
        Reset the host entry in the etc/host file.

        This function reads the original hosts file from the resource folder and
        replaces the current hosts file with it. It also logs the process.

        Returns:
            tuple: A tuple containing a boolean status, a message, and None.
        """
        try:
            original_etc_file = constants.Utils.HOSTS_PATH
            etc_file_copy = os.path.join(
                (os.path.join(os.getcwd(), "OS_Mac", "resource")), 'orignalPfCopy.txt')
            lines = open(etc_file_copy, 'r').readlines()
            self.logger.info(
                "Now resetting etc/host file with etc_file_copy from resource folder")
            with open(original_etc_file, 'w') as f:
                for line in lines:
                    f.write(line)
        except Exception as e:
            return (False, f"Error : {e}", None)
        return (True, "Success :  Host Entry Rest done", None)

    def remove_directory(self,
                         path  # (str)   path of directory to be deleted
                         ):
        '''
        Flow:  ------------------------------
            This function is used to remove a particular directory with files inside it
        '''
        try:
            for filename in os.listdir(path):
                file_path = os.path.join(path, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                except Exception as e:
                    self.logger.error(e)
                    raise Exception(e)
            os.rmdir(path)
        except Exception as e:
            return (False, f"Error :  {e}", None)
        return (True, "Success : Directory removed", None)

    def unzip_files(self,
                    file,
                    path_of_zip_file=None
                    ):
        '''
        Flow:  ------------------------------
            This function is used to unzip the files
        '''
        try:
            time.sleep(10)
            self.logger.info("FILE TO BE UNZIPPED: {}".format(file))

            if not '.zip' in file:
                raise Exception("ERROR :: Given File not a zip file")
            path = path_of_zip_file if path_of_zip_file else self.const.LOG_PATH
            for files in sorted(os.listdir(path)):
                if file in files:
                    with ZipFile(path + file, 'r') as zip_obj:
                        zip_obj.extractall(path)
                    break
            os.remove(path + file)
            file = file.replace('.zip', '')
            if os.path.exists(path + file):
                self.logger.info("{} unzipped at {}".format(file, path))
            else:
                self.logger.info("{} unzipped not found at {}".format(file, path))
                raise Exception("ERROR :: Unzipped File not found")
        except Exception as e:
            return (False, f"Error :  {e}", None)
        return (True, "Success : File Unzipped", None)

    def iperf_traffic_generator(self,
                                protocol="",  # default protocol will be Tcp until udp is specified
                                # default interface to make this request will be ipv4 until v6 is specified
                                network_type="ipv4",
                                server_port=constants.Utils.IPERF3_AWS_SERVER_PORT,
                                server_ip=constants.Utils.IPERF3_AWS_SERVER_ADDRESS,
                                # this is the time for which this test will run
                                time_for_test="2",
                                local_server_mode=False,
                                # if want to enable one_off_mode then pass "-1"
                                one_off_mode="",
                                thread_name=""
                                ):
        """
        This function generate TCP/UDP Data using iperf2 to iperf 3 AWS server. This can be used for both download/Upload.

        Args:
            protocol (str, optional): TCP/UDP. Defaults to "".
            network_type (str, optional): IPv4 or ipv6. Defaults to ipv4.
            server_port (str, optional): Iperf3 Server Port. Defaults to constants.Utils.iperf3_aws_server_port.
            server_ip (str, optional): IPerf3 Server Address. Defaults to constants.Utils.iperf3_aws_server_address.
            time_for_test (str, optional): Seconds for which you want to generate TCP?UDP traffic. Defaults to "2"#thisisthetimeforwhichthistestwillrun.
            local_server_mode (bool, optional): If you want to run the Iperf Server on Loopback address. Defaults to False
            one_off_mode (str, optional): If you want the iperf server to exit after handling one Client Connection.

        Functioning:
        iperf3 binary is loaded from the Drivers Folder. Then a connnection based on the options is made.
        if local_server_mode is chosen then using thread a local iperf3 server is started in one_off_mode.
        The thread is called from inside the function before the actual request from Testcase is executed. So thread Creates server and based on thread name server part is executed.
        The actual request from the testcase makes the client request.
        The output from client request and server request is in the form of JSON. Only client request  is parsed to see connection state and Data Transfer.
        If iperf connection succeeds then interval key length won't be zero. As there will be multiple connections opened.
        If interval is zero then we return False, json.
        Else if the connection succeeds we return true, json.

        Return TYPe:
        (Boolean,Message of succes or error ,JSON) ==> True/False,Success/Failure , JSON

        """

        iperf_connection_success = True
        json_result = ""

        if protocol.lower() == "udp":
            protocol = "-u"

        if network_type.lower() == "ipv6" or network_type == "-6":
            network_type = "-6"
        else:
            network_type = "-4"

        # self.logger.info("Start Of Function: \n Protocol: {} \n network_type: {} \n, server_port={} \n , server_ip: {} \n time_for_test: {} \n, local_server_mode: {} \n, one_off_mode: {} \n thread_name:{} \n".format(protocol, network_type, server_port, server_ip, time_for_test, local_server_mode, one_off_mode, thread_name) )

        if local_server_mode == True:
            self.logger.info(
                "Local_server_mode is set as true moving to multi thread mode to start iperf client and server")
            server = Thread(target=self.iperf_traffic_generator, args=(
                protocol, network_type, server_port, server_ip, time_for_test, False, "-1", "server",))
            # client=Thread(target=self.iperfTrafficGenerator, args=(protocol, network_type, server_port, server_ip, time_for_test, False, "", "client"  ))
            self.logger.info("Starting Server Thread")
            server.start()
            # self.logger.info("Starting Client Thread")
            # client.start()

        if thread_name.lower() == "server":
            self.logger.info("Recieved request to Start Iperf Server \n")
            self.logger.info(
                "Server_details: \n Protocol: {} \n network_type: {} \n, server_port={} \n , server_ip: {} \n time_for_test: {} \n, local_server_mode: {} \n, one_off_mode: {} \n thread_name:{} \n".format(
                    protocol, network_type, server_port, server_ip, time_for_test, local_server_mode, one_off_mode,
                    thread_name))
            server_result = subprocess.run([os.path.join(os.getcwd(), constants.Utils.IPERF3_UTILITY_PATH),
                                            "-s", "-p", server_port, one_off_mode, "-J"], capture_output=True,
                                           text=True)
            self.logger.info(
                "Printing output of Server_result: \n {} ".format(server_result))

        else:
            # iperfCommand = f"{constants.Utils.iperf3_utility_path} -c {server_ip} -p {server_port} {protocol} "
            self.logger.info(
                "Recieved request to Start Iperf Client trying to connect Iperf Server on this address {}:{} \n".format(
                    server_ip, server_port))
            self.logger.info(
                "Client Details: \n Protocol: {} \n network_type: {} \n, server_port={} \n , server_ip: {} \n time_for_test: {} \n, local_server_mode: {} \n, one_off_mode: {} \n thread_name:{} \n".format(
                    protocol, network_type, server_port, server_ip, time_for_test, local_server_mode, one_off_mode,
                    thread_name))
            client_result = subprocess.run([os.path.join(os.getcwd(), constants.Utils.IPERF3_UTILITY_PATH), "-c",
                                            server_ip, "-p", server_port, protocol, "-J", "-t", time_for_test],
                                           capture_output=True, text=True)
            self.logger.info(
                "Output of IPERF Command \n\n {}".format(client_result))
            json_result = json.loads(client_result.stdout)

        '''

        iperf output is in this format:

        For success case:
        '{\n\t"start":\t{\n\t\t"connected":\t[{\n\t\t\t\t"socket":\t4,\n\t\t\t\t"local_host":\t"*************",\n\t\t\t\t"local_port":\t64136,\n\t\t\t\t"remote_host":\t"*************",\n\t\t\t\t"remote_port":\t10042\n\t\t\t}],\n\t\t"version":\t"iperf 3.1.3",\n\t\t"system_info":\t"CYGWIN_NT-10.0 DESKTOP-S1DG72F 2.5.1(0.297/5/3) 2016-04-21 22:14 x86_64",\n\t\t"timestamp":\t{\n\t\t\t"time":\t"Tue, 11 Apr 2023 18:12:54 GMT",\n\t\t\t"timesecs":\t1681236774\n\t\t},\n\t\t"connecting_to":\t{\n\t\t\t"host":\t"ec2-3-111-188-105.ap-south-1.compute.amazonaws.com",\n\t\t\t"port":\t10042\n\t\t},\n\t\t"cookie":\t"DESKTOP-S1DG72F.1681236774.446866.25",\n\t\t"tcp_mss_default":\t0,\n\t\t"test_start":\t{\n\t\t\t"protocol":\t"TCP",\n\t\t\t"num_streams":\t1,\n\t\t\t"blksize":\t131072,\n\t\t\t"omit":\t0,\n\t\t\t"duration":\t1,\n\t\t\t"bytes":\t0,\n\t\t\t"blocks":\t0,\n\t\t\t"reverse":\t0\n\t\t}\n\t},\n\t"intervals":\t[{\n\t\t\t"streams":\t[{\n\t\t\t\t\t"socket":\t4,\n\t\t\t\t\t"start":\t0,\n\t\t\t\t\t"end":\t1.013039,\n\t\t\t\t\t"seconds":\t1.013039,\n\t\t\t\t\t"bytes":\t524288,\n\t\t\t\t\t"bits_per_second":\t4140317.930413,\n\t\t\t\t\t"omitted":\tfalse\n\t\t\t\t}],\n\t\t\t"sum":\t{\n\t\t\t\t"start":\t0,\n\t\t\t\t"end":\t1.013039,\n\t\t\t\t"seconds":\t1.013039,\n\t\t\t\t"bytes":\t524288,\n\t\t\t\t"bits_per_second":\t4140317.930413,\n\t\t\t\t"omitted":\tfalse\n\t\t\t}\n\t\t}],\n\t"end":\t{\n\t\t"streams":\t[{\n\t\t\t\t"sender":\t{\n\t\t\t\t\t"socket":\t4,\n\t\t\t\t\t"start":\t0,\n\t\t\t\t\t"end":\t1.013039,\n\t\t\t\t\t"seconds":\t1.013039,\n\t\t\t\t\t"bytes":\t524288,\n\t\t\t\t\t"bits_per_second":\t4140317.930413\n\t\t\t\t},\n\t\t\t\t"receiver":\t{\n\t\t\t\t\t"socket":\t4,\n\t\t\t\t\t"start":\t0,\n\t\t\t\t\t"end":\t1.013039,\n\t\t\t\t\t"seconds":\t1.013039,\n\t\t\t\t\t"bytes":\t524288,\n\t\t\t\t\t"bits_per_second":\t4140317.930413\n\t\t\t\t}\n\t\t\t}],\n\t\t"sum_sent":\t{\n\t\t\t"start":\t0,\n\t\t\t"end":\t1.013039,\n\t\t\t"seconds":\t1.013039,\n\t\t\t"bytes":\t524288,\n\t\t\t"bits_per_second":\t4140317.930413\n\t\t},\n\t\t"sum_received":\t{\n\t\t\t"start":\t0,\n\t\t\t"end":\t1.013039,\n\t\t\t"seconds":\t1.013039,\n\t\t\t"bytes":\t524288,\n\t\t\t"bits_per_second":\t4140317.930413\n\t\t},\n\t\t"cpu_utilization_percent":\t{\n\t\t\t"host_total":\t14.685771,\n\t\t\t"host_user":\t6.509650,\n\t\t\t"host_system":\t8.176121,\n\t\t\t"remote_total":\t0.012899,\n\t\t\t"remote_user":\t0.012899,\n\t\t\t"remote_system":\t0\n\t\t}\n\t}\n}\n'
        For Failure case:
        '{\n\t"start":\t{\n\t\t"connected":\t[],\n\t\t"version":\t"iperf 3.1.3",\n\t\t"system_info":\t"CYGWIN_NT-10.0 DESKTOP-S1DG72F 2.5.1(0.297/5/3) 2016-04-21 22:14 x86_64"\n\t},\n\t"intervals":\t[],\n\t"end":\t{\n\t},\n\t"error":\t"error - unable to connect to server: Connection timed out"\n}\n'

        '''

        if len(json_result['intervals']) == 0:
            self.logger.error(
                "IPerf Connection Failed with this error: '{}'' ".format(json_result['error']))
            return (not iperf_connection_success,
                    "Error :IPerf Connection Failed with this error: '{}'' ".format(json_result['error']), json_result)

        else:
            self.logger.info(
                "Iperf Connection succeeded returning the json and connection success as True")
            return (iperf_connection_success, "Succes : Iperf Connection succeeded ", json_result)

    def generate_dns_request(self,
                             domain="example.com",
                             tcp=False,
                             record_type="A",
                             dns_server_ip=constants.Utils.IPV4_GOOGLE_DNS_SERVER,
                             source_port=constants.Utils.DNS_REQUEST_SOURCE_PORT
                             ):
        """_summary_

        Args:
            domain (str, optional): This is the domains for which you want to resolve DNS. Defaults to "example.com".
            tcp (bool, optional): This flag tells whether to use TCP for making DNS request. Defaults to False.
            record_type (str, optional): Which Record Type you want to request. Defaults to "A".
            dns_server_ip (_type_, optional): Server ip to which you want to make DNS requests. Defaults to constants.Utils.ipv4_google_dns_server.
            source_port (_type_, optional): Source Port with which you want to bind DNS request. Defaults to constants.Utils.dns_request_source_port.

        Raises:
            Exception: _description_
        """
        try:
            self.logger.info(
                "Generating {} record request for domain {} to this DNS Sever {} over {} on this port {}".format(
                    record_type, domain, dns_server_ip, "UDP" if tcp == False else "TCP", source_port))
            resolver_object = dns.resolver.Resolver(configure=True)
            resolver_object.nameservers.append(dns_server_ip)
            try:
                print("inside try")
                resolver_object.resolve(
                    domain, record_type, tcp=tcp, source_port=source_port)
                self.logger.info("DNS Request made successfully")
            except Exception as e:
                self.logger.error(e)
                raise Exception(e)
        except Exception as e:
            return (False, f"Error : {e}", None)
        return (True, "Success : DNS Request made successfully", None)

    def get_current_system_time(self,
                                time_zone="pytz.utc",
                                time_format="%Y-%m-%d %H:%M:%S.%f",
                                ):
        """
            This Function return the current system time.
        """
        tz = ""

        if time_zone == "pytz.utc":
            tz = pytz.utc
        else:
            tz = pytz.timezone(time_zone)

        return datetime.now(tz).strftime(time_format)

    def detect_greoripsec_in_network(self):
        """This function gets cloudname when someone creates object of this class.
        Then we run the gateway.cloudname.net/get_loc function and based on its response we detect whether its GRE/IPSec or Direct case

        Raises:
            Exception: Exception is raised if while creating object of sys_ops we do not pass cloudname
            Secondly if the API fails and have empty response.
            Thirdly there is case where zviam key is missing then we raise the Exception

        Returns: A tuple with 3 Values
           Bool: This will return TRUE if GRE/IPSEC is detected otherwise it will return False. 
           str : Success or Error Message
           str : Exceptions if any
        """
        try:
            if self.cloud == None:
                self.logger.error("No Cloud is passed")
                raise Exception("No Cloudname Passed")
            else:
                result = subprocess.run(["curl", " https://gateway.{}.net/get_loc".format(
                    self.cloud), "--max-time", "10"], capture_output=True, text=True)

            if result.stdout == "":
                self.logger.error(
                    "Exception : Get Loc API Failed, API response is empty")
                raise Exception(
                    "Exception : Get Loc API Failed, API response is empty")
            else:
                result_json = json.loads(result.stdout)
            ipsec_or_gre_detected = False
            if "zviam" in result_json.keys():
                self.logger.info("Get Loc API is successful and ZVIAM response is {}".format(
                    result_json["zviam"]))
                if result_json["zviam"] == "GRE" or result_json["zviam"] == "IPSEC":
                    ipsec_or_gre_detected = True
                else:
                    self.logger.error("zviam response is not GRE or IPSec")
                    ipsec_or_gre_detected = False
            else:
                self.logger.error(
                    "Exeption: zviam key is missing, dumping result: \n {} ".format(result.stdout))
                raise Exception("zviam Key is missinig")
        except Exception as e:
            return (False, f"Error : {e}", None)
        return (ipsec_or_gre_detected,
                "Success : GRE or IPSec Detected " if ipsec_or_gre_detected else "Error :  zviam response is not GRE or IPSec",
                None)

    def fetch_application_version(self, application: str = "Zscaler/Zscaler.app") -> tuple[bool, str, str | None]:
        """
            Fetches the version of the given application.

            Args:
                application (str): The name or the full path of the application.

            Returns:
                tuple[bool, str, str|None]: A tuple containing a boolean indicating success or failure, a message, and the application version (or None if not found).
        """
        # Check if the input given is a complete path or just the app name, if app name is given then we by default check at /Applications, if an app is enclosed in a folder please provide the complete path
        application_to_check = None
        default_application_path_to_check = "/Applications/"
        application_version = None
        try:
            application_to_check = application
            if (os.path.exists(path=application)):
                self.logger.debug(f"The input given is a complete path :: {application_to_check}")
            else:
                self.logger.debug("The input given is not the complete application path")
                application_to_check = os.path.join(default_application_path_to_check, application)
                if (os.path.exists(application_to_check)):
                    self.logger.debug(
                        f"The input application given is present at default application location :: {application_to_check}")
                else:
                    raise Exception(
                        f"The given application {application} does not exist at {application} & {application_to_check}, please provide a valid application path or application")
        except Exception as e:
            self.logger.error(f"Unable to fetch application version :: {e}")
            return False, f"Unable to fetch application version :: {e}", application_version
        else:
            self.logger.debug("Succesfully verified the application path")
            command_to_fetch_application_version = f'defaults read {application_to_check}/Contents/Info.plist CFBundleShortVersionString'

            try:
                self.logger.debug(
                    f"Executing command to fetch application version :: {command_to_fetch_application_version}")
                fetch_application_version_result = os.popen(cmd=command_to_fetch_application_version)
            except Exception as e:
                self.logger.error(f"Error while executing command :: {command_to_fetch_application_version} - {e}")
                return False, f"Error while executing command :: {command_to_fetch_application_version} - {e}", application_version
            else:
                application_version = fetch_application_version_result.read().strip()
                self.logger.info(f"Application version fetched :: {application_version}")
                if application_version in ('', ""):
                    application_version = None
                    self.logger.error(
                        f"The Info.plist of the given application {application_to_check} might not exist, Application version fetched is None/Empty string")
                    return False, f"The Info.plist of the given application {application_to_check} might not exist, Application version fetched is None/Empty string", application_version

        return True, f"Application version fetched :: {application_version}", application_version

    def is_application_running(self, application: str = "Zscaler") -> tuple[bool, str, bool | None]:
        """
            Checks if the given application or process is running.

            Args:
                application (str): The name of the application or process.

            Returns:
                tuple[bool, str, bool|None]: A tuple containing a boolean indicating success or failure, a message, and a boolean indicating whether the application is running (or None if not found).
        """
        self.logger.info(f"Checking if the given application/process is running - {application}")
        command_to_execute = f"pgrep {application}"
        is_application_running = False
        try:
            process_check = os.popen(cmd=command_to_execute)
        except Exception as e:
            self.logger.error(
                f"Exception occured during execution of command - {command_to_execute} :: {e} - Unable to check if application is running")
            return False, f"Exception occured during execution of command - {command_to_execute} :: {e} - Unable to check if application is running", None
        else:
            process_check_result = process_check.read()
            process_check_result = process_check_result.split("\n")
            if len(process_check_result) > 0 and not process_check_result[0]:
                self.logger.info(f"Given application/process is not running :: PID is empty :: {process_check_result}")
            elif len(process_check_result) > 0 and process_check_result[0]:
                self.logger.info(
                    f"Given application/process is running :: PID list has been fetched :: {process_check_result}")
                is_application_running = True

        return True, "Successfully checked if application is running or not", is_application_running

    def fetch_network_interface_info(self, interface: str = None) -> tuple[bool, str, dict | None]:
        """
        Fetches the information for the interface provided in the parameter
        Uses the module "netifaces-plus"
        Fetches the interface dynamically if no interface is provided, NOTE: This can go wrong since multiple interfaces can be connected at the same time. Make sure only a single interface is connected for perfect results. Else please provided the interface
        """
        self.logger.info("Fetching all the available network interfaces in the system")
        network_interfaces = []
        ipv4_address = None
        ipv4_netmask = None
        ipv4_broadcast_address = None
        ipv4_network_address = None
        ipv6_address = None
        ipv6_netmask = None
        ipv6_flags = None
        mac_address = None
        interface_information = {
            "ipv4_address": ipv4_address,
            "ipv4_netmask": ipv4_netmask,
            "ipv4_broadcast_address": ipv4_broadcast_address,
            "ipv4_network_address": ipv4_network_address,
            "ipv6_address": ipv6_address,
            "ipv6_netmask": ipv6_netmask,
            "ipv6_flags": ipv6_flags,
            "mac_address": mac_address
        }

        try:
            network_interfaces = netifaces.interfaces()
            self.logger.info(f"Interfaces available: {network_interfaces}")
        except Exception as e:
            self.logger.error(f"Unable to fetch network interfaces, Error: {e}")
            return False, f"Unable to fetch network interfaces, Error: {e}", None

        if interface == None:
            self.logger.info("No interface info given by user, finding default ipv4 interface")
            default_interface_info = self.fetch_default_gateway_and_interface()
            if not default_interface_info[0]:
                self.logger.error(
                    "Failed to fetch default interface info and interface was not provided by user, please check manually")
                return False, default_interface_info[
                                  1] + "Failed to fetch default interface info and interface was not provided by user, please check manually", None
            else:
                interface = default_interface_info[2]["ipv4_default_interface"] if default_interface_info[2][
                                                                                       "ipv4_default_interface"] != None else \
                    default_interface_info[2]["ipv6_default_interface"]

        if interface not in network_interfaces:
            self.logger.error(f"Given interface {interface} is not available in the current interfaces")
            return False, f"Given interface {interface} is not available in the current interfaces", None

        try:
            netifaces_interface_result = netifaces.ifaddresses(interface)
            self.logger.info(
                f"Information has been fetched for {interface}\n{netifaces_interface_result}\nstarting to extract information meaningfully.")
            try:
                ip_v4_result = netifaces_interface_result[netifaces.AF_INET]
            except Exception as e:
                self.logger.error(f"Unable to fetch ipv4 information for the interface {interface}, Error: {e}")
            else:
                if len(ip_v4_result) > 0:
                    keys = ip_v4_result[0].keys()
                    interface_information["ipv4_address"] = ip_v4_result[0]['addr'] if 'addr' in keys else None
                    interface_information["ipv4_netmask"] = ip_v4_result[0]['netmask'] if 'netmask' in keys else None
                    interface_information["ipv4_broadcast_address"] = ip_v4_result[0][
                        'broadcast'] if 'broadcast' in keys else None
                    if interface_information["ipv4_netmask"] != None and interface_information["ipv4_address"] != None:
                        ip_address = interface_information["ipv4_address"] + "/" + interface_information["ipv4_netmask"]
                        interface_information["ipv4_network_address"] = str(
                            ipaddress.ip_network(ip_address, strict=False))

            try:
                ip_v6_result = netifaces_interface_result[netifaces.AF_INET6]
            except Exception as e:
                self.logger.error(f"Unable to fetch ipv6 information for the interface {interface}, Error: {e}")
            else:
                if len(ip_v6_result) > 0:
                    keys = ip_v6_result[0].keys()
                    interface_information["ipv6_address"] = ip_v6_result[0]['addr'] if 'addr' in keys else None
                    interface_information["ipv6_netmask"] = ip_v6_result[0]['netmask'] if 'netmask' in keys else None
                    interface_information["ipv6_flags"] = ip_v6_result[0]['flags'] if 'flags' in keys else None

            try:
                link_layer_result = netifaces_interface_result[netifaces.AF_LINK]
            except Exception as e:
                self.logger.error(f"Unable to fetch link layer information for the interface {interface}, Error: {e}")
            else:
                if len(link_layer_result[0]) > 0:
                    keys = link_layer_result[0].keys()
                    interface_information["mac_address"] = link_layer_result[0]['addr'] if 'addr' in keys else None

        except Exception as e:
            self.logger.error(f"The following issue occured when fetching network interface information, Error: {e}")
            return False, f"The following issue occured when fetching network interface information, Error: {e}", None
        else:
            self.logger.info(f"Successfully fetched all the information for given interface {interface}")
            return True, f"Successfully fetched all the information for given interface {interface}", interface_information

    def fetch_all_resolved_ips(self, application: str, application_type: str = "v4") -> tuple[bool, str, list | None]:
        """
        Fetches the list of all the ips an application can resolve to.
        """
        try:
            self.logger.info(f"Fetching IP list for {application}")
            ip_list_result = socket.gethostbyname_ex(application)
        except Exception as e:
            self.logger.error(f"An error occured while fetching the IP list for {application}, Error: {e}")
            return False, f"An error occured while fetching the IP list for {application}, Error: {e}", None
        else:
            ip_list = ip_list_result[2]
            self.logger.info(f"IP list fetch success, fetched result: {ip_list_result}")
            return True, f"IP list fetch success, fetched result: {ip_list_result}", ip_list

    def convert_hex_netmask_to_bit_decimal_netmask_ipv4(self, hex_netmask: str) -> tuple[bool, str, dict | None]:
        """
        Method converts a hexa decimal netmask to bit & dotted decimal netmask
        Returns a tuple with (Execution status, Execution message, dictionary containing "dotted_decimal_netmask" & "bit_netmask")
        Please pass in hexnetmask without the intial hex notation, example: If hex netmask is "0xffffff00" , please pass only "0xffffff00", do not trim the values
        Applies only to IPV4
        """
        try:
            # Convert hex to int
            decimal_notation = int(hex_netmask[2:], 16)
            # Convert int to binary, Slice the binary notation to start from 2nd index since bin() always returns a value with '0b' attached at beginning
            binary_notation = bin(decimal_notation)[2:]
            # Number of 1's represent the netmask
            bit_netmask = f'/{binary_notation.count("1")}'
            dotted_decimal_netmask = f"{int(binary_notation[0:8], 2)}" + "." + f"{int(binary_notation[8:16], 2)}" + "." + f"{int(binary_notation[16:24], 2)}" + "." + f"{int(binary_notation[24:32], 2)}"
            netmask_mapping = {
                "dotted_decimal_mask": dotted_decimal_netmask,
                "bit_netmask": bit_netmask
            }
        except Exception as e:
            self.logger.error(f"An exception occured while converting the hex netmask, Error: {e}")
            return False, f"An exception occured while converting the hex netmask, Error: {e}", None
        else:
            self.logger.info(f"Successfully converted hex to dotted decimal and bits format\n{netmask_mapping}")
            return True, "Successfully converted hex to dotted decimal and bits format", netmask_mapping

    def fetch_system_dns_server_ips(self) -> tuple[bool, str, list | None]:
        """
        Fetches the list of dns server list for current resolver
        Note: dns.resolver.Resolver() fetched dns information from the file /etc/resolv.conf by default.
        If the entries in the file differs from that in the system settings then it may lead to unexpected behaviour
        """
        try:
            self.logger.info("Fetching system DNS servers")
            dns_resolver = dns.resolver.Resolver()
            dns_server_list = dns_resolver.nameservers
        except Exception as e:
            self.logger.error(f"Exception during fetching system DNS server list, Error: {e}")
            return False, f"Exception during fetching system DNS server list, Error: {e}", None
        else:
            self.logger.info(f"Fetched system DNS servers, Result: {dns_server_list}")
            return True, f"Fetched system DNS servers, Result: {dns_server_list}", dns_server_list

    def fetch_system_routing_table(self) -> tuple[bool, str, dict[str, list[dict]] | None]:

        """
        Fetches the entire routing table information
        Returns a dictionary of the format:
        {
            "IPV4":[
                {"Destination":"","Gateway": "", "Flags": "", "Netif": ""},
                {"Destination":"","Gateway": "", "Flags": "", "Netif": ""}
            ], 
            "IPV6":[
                {"Destination":"","Gateway": "", "Flags": "", "Netif": ""},
                {"Destination":"","Gateway": "", "Flags": "", "Netif": ""}
            ]
        }
        """

        try:
            command_to_execute = "netstat -rn"
            self.logger.info(f"Fetching routing table info using: {command_to_execute}")
            command_output = subprocess.run(command_to_execute, shell=True, stdout=subprocess.PIPE)
        except Exception as e:
            self.logger.error(f"Exception occured while fetching routing table info, Error: {e}")
            return False, f"Exception occured while fetching routing table info, Error: {e}", None
        else:
            command_output_string = str(command_output.stdout)
            routing_table_info_list = command_output_string.split("\\n")
            # Replace multiple occurence of white space with single white space in each route string
            routing_table_info_list = [re.sub(r" +", " ", each_route) for each_route in routing_table_info_list]
            ipv4_route_list = routing_table_info_list[4: routing_table_info_list.index("Internet6:") - 1]
            ipv6_route_list = routing_table_info_list[
                              routing_table_info_list.index("Internet6:") + 2: len(routing_table_info_list) - 1]
            ipv4_routing_table = []
            ipv6_routine_table = []

            for each_route in ipv4_route_list:
                each_route_info_list = each_route.split(" ")
                destination = each_route_info_list[0]
                gateway = each_route_info_list[1]
                flags = each_route_info_list[2]
                netif = each_route_info_list[3]
                expire = each_route_info_list[4]
                each_route_info_dict = {
                    "Destination": destination,
                    "Gateway": gateway,
                    "Flags": flags,
                    "Netif": netif,
                    "Expire": expire
                }
                ipv4_routing_table.append(each_route_info_dict)

            print(len(ipv6_route_list))
            for each_route in ipv6_route_list:
                print(ipv6_route_list.index(each_route))
                each_route_info_list = each_route.split(" ")
                destination = each_route_info_list[0]
                gateway = each_route_info_list[1]
                flags = each_route_info_list[2]
                netif = each_route_info_list[3]
                expire = each_route_info_list[4]
                each_route_info_dict = {
                    "Destination": destination,
                    "Gateway": gateway,
                    "Flags": flags,
                    "Netif": netif,
                    "Expire": expire
                }
                ipv6_routine_table.append(each_route_info_dict)

            routing_table = {
                "IPV4": ipv4_routing_table,
                "IPV6": ipv6_routine_table
            }

            self.logger.info("Routing table has been fetched and parsed")
            self.logger.info(f"{routing_table}")
            return True, "Routing table has been fetched and parsed", routing_table

    def fetch_default_gateway_and_interface(self) -> tuple[bool, str, dict[str, str]]:
        """
        Use this method to fetch the default gateway ip & interface name
        Returns a tuple of (Execution status, Execution message, Result)
        Result is a dictionary containing:
            route_info = {
                    "ipv4_default_gateway":"",
                    "ipv4_default_interface":"",
                    "ipv6_default_gateway":"",
                    "ipv6_default_interface":""
            }
        """
        try:
            self.logger.info("Starting to fetch the default route's interface and gateway ip")
            default_route_interface_info = netifaces.gateways()["default"]
            route_info = {
                "ipv4_default_gateway": None,
                "ipv4_default_interface": None,
                "ipv6_default_gateway": None,
                "ipv6_default_interface": None
            }

            if netifaces.AF_INET in default_route_interface_info.keys():
                route_info["ipv4_default_gateway"] = default_route_interface_info[netifaces.AF_INET][0]
                route_info["ipv4_default_interface"] = default_route_interface_info[netifaces.AF_INET][1]

            if netifaces.AF_INET6 in default_route_interface_info.keys():
                route_info["ipv6_default_gateway"] = default_route_interface_info[netifaces.AF_INET6][0]
                route_info["ipv6_default_interface"] = default_route_interface_info[netifaces.AF_INET6][1]

        except Exception as e:
            self.logger.error(f"Unable to fetch default interface info, Error: {e}")
            return False, f"Unable to fetch default interface info, Error: {e}", None
        else:
            self.logger.info(f"Fetched the default route's interface and gateway ip, Result: {route_info}")
            return True, f"Fetched the default route's interface and gateway ip, Result: {route_info}", route_info

    def kill_process_mac(self, process_name: str):
        """
        Kills any application that is running in mac
        example: self.kill_process_mac(process_name="System Events")
        """
        try:
            if not process_name:
                raise Exception("Process name is not defined, please pass in the right process name")

            process_name = process_name.replace(" ", "\\ ")
            cmd = f"sudo killall {process_name}"

            try:
                self.logger.debug(f"Executing command: '{cmd}' to kill the process {process_name}")
                return_code = os.system(cmd)
            except Exception as e:
                self.logger.error(f"Exception while executing the command: {cmd}, Error: {e}")
                raise Exception(f"Exception while executing the command: {cmd}, Error: {e}")
            else:
                self.logger.info(f"Process: {process_name} has been killed")
                time.sleep(1)
        except Exception as e:
            self.logger.info(f"An exception occured while killing the process: {process_name}, Error: {e}")
            return False, f"An exception occured while killing the process: {process_name}, Error: {e}", None
        else:
            return True, f"Process: {process_name} has been killed", None

    def get_current_logged_in_user(self) -> tuple[bool, str, str | None]:
        """
        Jenkins launchd process always runs as a root user, hence any module used to get current loggedin user returns the user as root which is not right
        Automation triggered via jenkins looks in root directory for zscaler related logs
        Fetch the first value returned by the command 'who'
        """

        try:
            self.logger.info("Fetching current logged in user")
            command = "who"
            process = os.popen(cmd=command)
            process_output = process.read()
            loggedin_username = process_output.split(" ")[0]
        except Exception as e:
            self.logger.error(f"Error while fetching current logged in user: {e}")
            return False, f"Error while fetching current logged in user: {e}", None
        else:
            self.logger.info(f"Current logged in user: {loggedin_username}")
            return True, f"Current logged in user: {loggedin_username}", loggedin_username

    def fetch_network_dns_search_domains(self) -> tuple[bool, str, list | None]:
        '''
        Fetches the DNS search domains on current network.
        Uses cmd: 'scutil --dns' to find dns details.
        Returns a list of all DNS Search Domains on the network.
        '''
        try:
            cmd = 'scutil --dns | grep "search domain"'

            self.logger.info(f"Exuting command '{cmd}' to fetch DNS Search Domains")
            search_domains_raw = self.execute_command(command=cmd)
            search_domains_raw = list(map(str.strip, search_domains_raw[0].strip().split('\n')))

            dns_search_domains = set()
            for domain in search_domains_raw:
                if domain:
                    dns_search_domains.add(domain.split(':')[1].strip())

            dns_search_domains = list(dns_search_domains)
        except Exception as e:
            self.logger.error(f"Unable to fetch the network DNS search domains, Error: {e}")
            return False, f"Unable to fetch the network DNS search domains, Error: {e}", None
        else:
            self.logger.info(f"Successfully fetched the network DNS search domains, Result: {dns_search_domains}")
            return True, f"Successfully fetched the network DNS search domains, Result: {dns_search_domains}", dns_search_domains

    def fetch_current_os_version_number(self) -> tuple[bool, str, str | None]:
        """
            Fetch current os version number
            
            Returns:
                tuple[bool, str, str|None]: A tuple containing a boolean indicating success or failure, a message, and the os version number (or None if not found).
        """
        command_to_execute = "sw_vers -productVersion"
        try:
            os_version_number = os.popen(cmd=command_to_execute)
            os_version_number = os_version_number.read()
            os_version_number = os_version_number.split("\n")
            os_version_number = str(os_version_number[0])
        except Exception as e:
            self.logger.error(
                f"Exception occured during execution of command - {command_to_execute} :: {e} - Unable to fetch os version")
            return False, f"Exception occured during execution of command - {command_to_execute} :: {e} - Unable to fetch os version", None
        else:
            self.logger.info(f"OS version number: {os_version_number}")
            return True, f"OS version number: {os_version_number}", os_version_number

    def fetch_all_network_interfaces(self) -> tuple[bool, str, list[str] | None]:
        """
        Returns all the available interfaces in the system
        """
        try:
            interfaces_list = netifaces.interfaces()
        except Exception as e:
            self.logger.error(f"Error fetching interfaces: {e}")
            return False, f"Error fetching interfaces: {e}", None
        else:
            self.logger.info(f"Interfaces Fetched: {interfaces_list}")
            return True, f"Interfaces Fetched: {interfaces_list}", interfaces_list

    def get_hashed_user_sid(self)->tuple[bool, str, str]:
        """
        Fetch the first 10 digits of the hashed user id (used to find out tunnel log folder)
        """
        try:
            self.logger.info(f"Fetching hashed user SID")
            current_user = os.getlogin()
            # If the current logged in user is not root, but the python process is launched by a root process then the logged in user is also picked up as root
            # Usually none of the user logs into the system as root hence Zcc will fetch the hashed sid of the logged in user and not root.
            # Even if a root user logged in to system, the code block within if condition will fetch the right user again
            if current_user == 'root':
                current_user = self.get_current_logged_in_user()[-1]
            hash_object = hashlib.sha256()
            hash_object.update(bytes(current_user, 'utf-8'))
            # Only the initial 10 characters are chosen for the log folder naming
            hashed_value = hash_object.hexdigest()[0:10]
        except Exception as e:
            self.logger.error(f"Error while fetching hashed user SID: {e}")
            return False, f"Error while fetching hashed user SID: {e}", None
        return True, "Successfully retrieved hasehd SID", hashed_value
import threading

class AutomationThread(threading.Thread):
    """
    Wrapper over Thread to return an output once the thread is done executing

    Calling the AutomationThread.join() method will return an output from the target while threading.Thread.join() will not return an output
    """
    def __init__(self, group = None, target = None, name = None, args = ..., kwargs = None, *, daemon = None):
        super().__init__(group, target, name, args, kwargs, daemon=daemon)
        self._return = None

    def run(self):
        """
        Store the value returned by the target assigned to the thread object
        """
        if self._target is not None:
            self._return = self._target(*self._args, **self._kwargs)
    
    def join(self, timeout:float|None=None):
        """
        Wrapper over threading.Threading.join()
        Returns the output returned by the target that the thread was assigned
        """
        super().join(timeout=timeout)
        return self._return
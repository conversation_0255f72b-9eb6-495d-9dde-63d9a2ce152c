import paramiko
import os
import time
import logging
from common_lib.common.logger import Logger


class OpsSsh():
    def __init__(
        self,
        host:str,
        user:str,
        password:str,
        port:int=22,
        log_handle:logging.Logger=None
    ):
        self.logger = Logger.initialize_logger(log_file_name="ops_ssh.log", log_level="DEBUG") if log_handle==None else log_handle
        self.host = host
        self.user = user
        self.password = password
        self.port = port
        self.ssh_connection = None

    def create_connection(
        self
    )->tuple[bool, str, None]:
        try:
            self.logger.info(f"Creating SSH Connection to {self.host} with {self.user}")
            self.ssh_connection = paramiko.SSHClient()
            self.ssh_connection.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh_connection.connect(
                hostname=self.host,
                port=self.port,
                username=self.user,
                password=self.password
            )
        except Exception as error:
            self.logger.error(f"Error while creating connection: {error}")
            return False, f"Error while creating connection: {error}", None
        else:
            self.logger.info("SSH connection successfully created")
            return True, "SSH connection successfully created", None
    

    def close_connection(
            self,
        ):
        try:
            self.logger.info(f"Closing existing connection")
            self.ssh_connection.close()
        except Exception as error:
            self.logger.error(f"Error while closing connection: {error}")
            return False, f"Error while closing connection: {error}", None
        else:
            self.logger.info("Successfully closed connection")
            return True, "Successfully closed connection", None

        
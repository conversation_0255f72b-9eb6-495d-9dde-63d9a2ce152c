import os
import time
import copy
import allure
import pyautogui
import subprocess
from functools import wraps
from multiprocessing import Process
from packaging.version import Version
from datetime import datetime, timedelta
from common_lib.common import common_ui
from common_lib.common import constants
from common_lib.common.logger import Logger
from common_lib.common.log_ops import LogOps
from OS_Mac.library.ops_installation import InstallUninstall
from OS_Mac.library.ops_system import SysOps
from OS_Mac.library.browser_ops import BrowserOps
from OS_Mac.library.ui_zcc_elements import Zcc_Elements_Mac_4_3_0_1_Below, Zcc_Elements_Mac_4_3_0_170_Above, Zcc_Elements_Mac_4_3_0_1_Above

class Zcc():
    def __init__(self,
                 handle: str ="easy",
                 dynamic_element_check: bool = True,
                 log_handle: bool =None,
                 language: str = "english"):
        self.logger = (log_handle if log_handle else Logger.initialize_logger(log_file_name="ZCC.log", log_level="INFO"))
        self.ui = common_ui.Gui(handle=handle,log_handle=self.logger)
        self.sys_ops = SysOps()
        self.operating_system = self.ui.operating_system
        self.logger.info("Initialized ZCC GUI")
        self.dynamic_element_check = dynamic_element_check
        self.sleep_factor = 1
        self.initialize_elements()
        self.ui.start_app(app=self.ui.app, sleep_time=10)
        self.start_time_for_log_search=""
        self.log_ops = LogOps(console_handle_needed=False)
        self.browser_ops=BrowserOps()
        self.language = language.lower()
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def initialize_elements(self)->None:  # initialize elements for win/mac
            
        try:
            fetch_app_version_result=self.sys_ops.fetch_application_version()
            if not fetch_app_version_result[0]:
                raise Exception(fetch_app_version_result[1])
        except Exception as e:
            self.logger.error(f"Unable to find ZCC version, ZCC probably not installed :: {e}, Initializing elements for older ZCC version")
            self.elements = copy.deepcopy(Zcc_Elements_Mac_4_3_0_1_Below)
            self.ui.app = self.elements.APP
        else:
            self.zcc_build = fetch_app_version_result[2]
            if Version(self.zcc_build)<Version("4.3.0.0"):
                self.elements = copy.deepcopy(Zcc_Elements_Mac_4_3_0_1_Below)
            else:
                if Version(self.zcc_build)<Version("4.3.0.170"):
                    self.elements = copy.deepcopy(Zcc_Elements_Mac_4_3_0_1_Above)
                else:
                    self.elements = copy.deepcopy(Zcc_Elements_Mac_4_3_0_170_Above)
            self.ui.app = self.elements.APP
            self.logger.info(f"Successfully initialized ZCC GUI Elements for {self.zcc_build}")
            return

    def calculate_func_execute_time(func):
        '''
        Decorator used for calculating time taken for execution of a function
        Flow: ------------------------------
            1. Func is passed to calculate_func_execute_time which calls wrapper func
            2. Wrapper func then first gets the current time(start time) and then make call to the given func as arg in calculate_func_execute_time
            3. After func is executed, we then get the current time(end time) and then calculate endtime-starttime and get the time difference
        '''
        @wraps(func)
        def wrapper(*args,**kwargs):
            start_time = time.time()
            result = func(*args,**kwargs)
            end_time = time.time()
            args[0].file_transfer_time = int(end_time-start_time)
            args[0].logger.critical(f"{'*'*50}")
            args[0].logger.critical(f"TIME TAKEN FOR {func.__name__} : {args[0].file_transfer_time} Seconds")
            args[0].logger.critical(f"{'*'*50}")
            return result
        return wrapper

    def bring_zcc_to_focus(self,
                        number_of_trails: int = 2,      # number of times to try to open Zcc before raising exception
                        sleep_before_focus: int = 0,    # int seconds to sleep before bringing zcc to focus
                           ):  
        """
        Bring Zscaler Connect Client (ZCC) to focus.

        This function tries to open Zscaler Connect Client (ZCC) by running an AppleScript. If it fails to open ZCC, it raises an exception.

        Args:
            number_of_trails (int): Number of times to try to open ZCC before raising exception. Default is 2.
            sleep_before_focus (int): Int seconds to sleep before bringing ZCC to focus. Default is 0.

        Returns:
            tuple: A tuple containing a boolean value and a string message. The boolean value indicates whether the operation was successful or not. The string message provides more information about the result of the operation.
        """
        self.initialize_elements()
        self.logger.info("in bring_zcc_to_focus")
        time.sleep(sleep_before_focus)
        self.sys_ops.kill_process_mac(process_name="System Events")
        open_zscaler_text = self.elements.OPEN_ZSCALER_LANGUAGE_MAPPING[self.language]
        scpt = f'''
                tell application "System Events" to tell process "Zscaler"
                    tell menu bar item 1 of menu bar 2
                        click
                        click menu item "{open_zscaler_text}" of menu 1
                    end tell
                end tell'''
        try:
            p = subprocess.Popen(['osascript'], stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            stdout, stderr = p.communicate(str.encode(scpt))
            self.logger.info("Mac app restarted")
        except Exception as e:
            self.logger.error("Warning :: Unable to open app - {}".format(e))
            return (False,"Error :: Unable to open app - {}".format(e),None)
        return (True,"Success :: Zapp Launched Successfully",None)
    
    def read_zstatus_mac(self):
        '''
        Flow:  ------------------------------
            This function is helper function for get_zcc_ui_values_mac. It reads zstatus log file which has mac UI data
            like tunnel value, sent recieved bytes etc
        '''

        self.logger.info('START READING ZSTATUS FILE')
        self.log_contents=[]

        zstatus_file_path = self.log_ops.get_log_path_for_current_zcc()
            
        zstatus_file = sorted([files for files in os.listdir(zstatus_file_path) if 'ztstatus_' in files])

        if len(zstatus_file)>0:
            for file in zstatus_file:
                os.remove(zstatus_file_path+file)
                self.logger.info(file+' removed\n')
            time.sleep(5)
        else:
            raise Exception('No Zstatus file found')
        zstatus_file = sorted([files for files in os.listdir(zstatus_file_path) if 'ztstatus_' in files])[0]
        self.logger.info('READING ZSTATUS FILE : {}'.format(zstatus_file))
        with open('{}{}'.format(zstatus_file_path,zstatus_file),'r') as log_file:
            self.log_contents = log_file.readlines()
        self.log_contents=self.log_contents[4:-2]
        self.logger.info('DONE READING ZSTATUS FILE')

    def get_zcc_ui_values_mac(self):
        '''
        Flow:  ------------------------------
            This function is used to fetch the UI data on Mac ZCC
            It calls the read_zstatus_mac which is a type of log file containing mac UI data
            Then we get the data and format it and store the data in data_dict dictionary
        '''
        try:
            data_dict=dict()
            self.read_zstatus_mac()
            if len(self.log_contents)==0:
                raise Exception('DATA LIST EMPTY')
            else:
                for index,item in enumerate(self.log_contents):
                    if index%2==0:
                        item=item.replace('<key>','').replace('</key>','').replace('<string>','').replace('</string>','').replace('\t','').replace('\n','')
                        data_dict[item]=self.log_contents[index+1].replace('<key>','').replace('</key>','').replace('<string>','').replace('</string>','').replace('\t','').replace('\n','')
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,f"Succes :: Data read successfully ",data_dict)
    
    def do_okta_login(self,     
                      user_id : str,                    # okta username
                      user_password : str,              # okta password
                      search_log_time : str,            # time to be used for log search - datetime.now() should be passed
                      partial_login : bool,             # defines whether to terminate login or do complete login
                      search_file: str = "latest",      # defines whether to search latest file or oldest file
                      use_login_hint: bool = False,     # Indicates if login_hint is used 
                      zpa_reauth: bool=False,           # Indicates if zpa_reauth should be peformed
                      ):
        """ 
        This function performs an Okta login.
        """
        if partial_login:
            self.bring_zcc_to_focus()
            self.logger.info("Aborting Okta login , hitting back button on ZCC")
            self.ui.click(self.elements.ZCC_BACK_BUTTON_AT_OKTA,sleep_time=1, AEFC="Cannot click ZCC Back Button")
            self.start_time_for_log_search = datetime.now()
            return (True,"Success :: Partial OKTA Login Successfull",None)
        if use_login_hint:
            if not zpa_reauth:
                self.ui.click(path=self.elements.OKTA_LOGIN_HINT_PASSWORD_IMAGE, sleep_time=1, AEFC="Unable to click on the password lock image in OKTA page")
            self.ui.click(path=self.elements.OKTA_LOGIN_HINT_VERIFY_TEXT_LABEL, sleep_time=1, AEFC="Unable to click on Verify button in OKTA page")
        else:
            self.ui.type(self.elements.OKTA_USERNAME, text=user_id, sleep_time=1, AEFC="Something wrong with username")
        
        self.ui.type(self.elements.OKTA_PASSWORD_BOX, text=user_password, sleep_time=1, AEFC="Something wrong with password")
        # self.ui.click(self.elements.OKTA_SIGN_IN_BUTTON)
        if use_login_hint:
            self.ui.click(self.elements.OKTA_LOGIN_HINT_VERIFY_BUTTON, sleep_time=1, AEFC="Unable to click on verify button to login")
        else:
            pyautogui.press('enter')
        
        self.start_time_for_log_search = datetime.now() 
        return (True,"Success :: OKTA Login Successfull",None)
    
    def initial_zcc_login_window(self,      # helper function for initial userfield at zcc login page
                                 username: str):
        """
        Helper function for initial userfield at ZCC login page.

        :param self: Self object.
        :param username: Username as a string.
        """
        try:
            self.bring_zcc_to_focus()
            self.ui.type(self.elements.USER_NAME, text=username, sleep_time=1, AEFC="Something wrong with username")
            self.ui.click(self.elements.LOGIN_BUTTON, sleep_time=0, AEFC="Unable to Click login Button")
            self.start_time_for_log_search = datetime.now()         
        except Exception as e:
            self.logger.error(f"Error :: login Failed at USER_ID Page :: {e}")
            raise Exception(f"Error :: login Failed at USER_ID Page :: {e}")
    
    def handle_aup(self,                    # helper function for handling zcc aup login
                   time_for_log_search,     # datetime.now()
                   zia_saml_login: bool = False,
                   cancel_aup: bool = False,
                   ):
        """
            handle_aup is a helper function for handling zcc aup login.
            
            Args:
                time_for_log_search (datetime.now()): Current datetime.
                zia_saml_login (bool, optional): Whether to use ZIA SAML login. Defaults to False.
                cancel_aup (bool, optional): Whether to cancel the AUP. Defaults to False.
            
            Raises:
                Exception: If the login fails at the AUP page.
        """ 
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(self.elements.EULA_DECLINE_BUTTON if cancel_aup else self.elements.EULA_ACCEPT_BUTTON, sleep_time=1, AEFC="Cannot click AUP Decline" if cancel_aup else "Cannot click AUP Accept")
            self.start_time_for_log_search = datetime.now()         
        except Exception as e:
            self.logger.error(f"Error :: login Failed at AUP Page :: {e}")
            raise Exception(f"Error :: login Failed at AUP Page :: {e}")

    def handle_zia_saml(self,               # helper function for zia saml login
                        username: str,
                        password : str,
                        time_for_log_search,        #datetime.now() object
                        cancel_zia_saml_login : bool,
                        browser_based_auth: bool=False,  # Indicates if browser based auth is used for login
                        chrome_browser:bool=False,       # Indicates if chrome browser is used
                        ):
        """
            This function is a helper for ZPA login. It performs the login process and handles exceptions.

            Args:
                username (str): The username for the ZPA login.
                password (str): The password for the ZPA login.
                time_for_log_search: The datetime.now() object for searching logs.
                cancel_zia_saml_login (bool): A flag to indicate if the login should be cancelled.
                browser_based_auth(bool): A flag indicating if browser_based_auth is enabled or not.
                chrome_browser (bool): Indicates if chrome browser is used.
                
            Returns:
                None

            Raises:
                Exception: If the login fails, it raises an exception with the error message.
        """
        if browser_based_auth:
            try:
                self.browser_ops.perform_browser_based_auth(username=username,password=password,chrome=chrome_browser)
                if cancel_zia_saml_login:
                    self.logger.info("login aborted at ZIA SAML")
                    return
            except Exception as e:
                self.logger.error(f"login failed at Browser ZPA OKTA Page :: {e}")
                raise Exception(f"login failed at Browser ZPA OKTA Page :: {e}")
        else:
            try:
                self.do_okta_login(user_id=username,user_password=password,search_log_time=time_for_log_search,partial_login=cancel_zia_saml_login,search_file= "latest")
                if cancel_zia_saml_login:
                    self.logger.info("login aborted at ZIA SAML")
                    return
            except Exception as e:
                self.logger.error(f"login failed at ZIA SAML OKTA Page :: {e}")
                raise Exception(f"login failed at ZIA SAML OKTA Page :: {e}")
        
    def handle_zia_login(self,              # helper function for zia login
                        password: str,
                        time_for_log_search,    #datetime.now() object
                        cancel_zia_login: bool):
        """
            This function is a helper function for ZIA login.
            It handles the login process for ZIA.

            Args:
                password (str): The password for the ZIA login.
                time_for_log_search: The datetime.now() object.
                cancel_zia_login (bool): A boolean indicating whether to cancel the ZIA login.
            """
        
        if cancel_zia_login:
            self.logger.info("Aborting login at ZIA, hitting back button on ZCC")
            self.ui.click(self.elements.ZCC_BACK_BUTTON,sleep_time=1, AEFC="Cannot click ZCC Back Button")
            return
        try:
            is_password_box_up = False
            self.ui.type(self.elements.PASSWORD_BOX, text=password, sleep_time=1, AEFC="Password button not found")
            self.start_time_for_log_search = datetime.now()
            self.ui.click(self.elements.LOGIN_BUTTON_PASSWORD, sleep_time=0, AEFC="Unable to click login Button")
        except Exception as e:
            self.logger.error(f"Error :: login Failed at Form PASSWORD Page :: {e}")
            raise Exception(f"Error :: login Failed at Form PASSWORD Page :: {e}")
        
    def handle_zia_one_id_login(self): # place holder for zia one id login
        pass

    # ------------------------------------------------------------------------------------------------------------------------------------------------------   
    def handle_zpa_login(self,                      # helper function for zpa login
                        username: str,              # Username for ZPA login
                        password : str,             # Password for ZPA login
                        cancel_zpa_login : bool,    # Indicates if login should be cancelled
                        time_for_log_search: int = 0,  # datetime.now() object
                        browser_based_auth: bool = False,   # Indicates if browser auth is enabled
                        chrome_browser : bool = False,      # Indicated if chrome browser is used 
                        use_login_hint:bool=False,  # Indicates if autofill using login_hint
                        zia_saml_login:bool=False,  # Indicates if saml login is used for zia
                        ):
        """
            This function is a helper for ZPA login. It performs the login process and handles exceptions.

            Args:
                username (str): The username for the ZPA login.
                password (str): The password for the ZPA login.
                time_for_log_search: The datetime.now() object for searching logs.
                cancel_zpa_login (bool): A flag to indicate if the login should be cancelled.
                use_login_hint(bool) : A flag to indicate if login_hint is enabled
                browser_based_auth(bool): A flag indicating if browser_based_auth is enabled or not.
                chrome_browser (bool): Indicates if chrome browser is used.
                zia_saml_login (bool): Boolean indicating if saml login is used for zia.
                
            Returns:
                None

            Raises:
                Exception: If the login fails, it raises an exception with the error message.
        """
        if browser_based_auth:
            try:
                self.browser_ops.perform_browser_based_auth(username=username,password=password,chrome=chrome_browser,zia_saml_login=zia_saml_login,use_login_hint=use_login_hint)
                return True, "Browser Auth Successful", 1
            except Exception as e:
                self.logger.error(f"login failed at Browser ZPA OKTA Page :: {e}")
                raise Exception(f"login failed at Browser ZPA OKTA Page :: {e}")
        else:
            try:
                start_time_for_log_search = datetime.now() - timedelta(15)
                self.do_okta_login(user_id=username,user_password=password,search_log_time=time_for_log_search,partial_login=cancel_zpa_login, use_login_hint=use_login_hint)
                if use_login_hint:
                    regex_login_hint = r'url:{https://samlsp(.*)login_hint='
                    self.log_ops.search_log_file(file="ZSATray",words_to_find_in_line=regex_login_hint,search_mode=None,start_timestamp=start_time_for_log_search)
                if cancel_zpa_login:
                    self.logger.info("login aborted at ZPA SAML")
                    return True, "Login aborted", 1
            except Exception as e:
                self.logger.error(f"login failed at ZPA OKTA Page :: {e}")
                return False, f"login failed at ZPA OKTA Page :: {e}", 0

    @allure.step("zcc login")       
    @calculate_func_execute_time
    def login(self,                              # PARENT FUNCTION FOR ZCC LOGIN
              zia_user: str = None,              # zia user id
              zia_password: str = None,          # zia user password
              zpa_user: str = None,              # zpa user id
              zpa_password: str = None,          # zpa user password
              aup: bool= False,                  # Defines AUP enabled or not 
              zia_saml_login: bool= False,       # Defines ZIA SAML enabled or not
              zia_one_id_login: bool= False,     # Defines ZIA SAML enabled or not
              cancel_login_at_zia: bool= False,  # Defines whether to cancel login at ZIA login page 
              cancel_login_at_zpa: bool= False,  # Defines whether to cancel login at ZPA login page
              cancel_aup: bool= False,           # Defines whether to cancel or decline AUP
              use_login_hint: bool = False,      # Defines whether autofill using login_hint is activated or not
              browser_based_auth: bool = False, # Defines wether to authenticate via Browser or not 
              chrome_browser: bool = False,     # Defines wether to authenticate via chrome Browser or not       
              sleep_time: int = 10,              # Defines sleep time after login is done
              oneid_user: str = None,            # One ID User Name
              oneid_password: str = None,        # One ID Password
              cancel_login_at_oneid: bool = False# Cancel AUP at One ID Login Page
              )->tuple[bool,str,str]:   
        """
            Parent function for ZCC login.
                        
            :param zia_user: ZIA user ID. Default is None.
            :param zia_password: ZIA user password. Default is None.
            :param zpa_user: ZPA user ID. Default is None.
            :param zpa_password: ZPA user password. Default is None.
            :param aup: Defines AUP enabled or not. Default is False.
            :param zia_saml_login: Defines ZIA SAML enabled or not. Default is False.
            :param zia_one_id_login: Defines ZIA SAML enabled or not. Default is False.
            :param cancel_login_at_zia: Defines whether to cancel login at ZIA login page. Default is False.
            :param cancel_login_at_zpa: Defines whether to cancel login at ZPA login page. Default is False.
            :param cancel_aup: Defines whether to cancel or decline AUP. Default is False.
            :param sleep_time: Defines sleep time after login is done. Default is 10.
            :param use_login_hint : Defines whether login_hint is enabled or not.
            :param browser_based_auth : Defines whether browser_based_auth is enabled or not.
            :param chrome_browser : Defines whether chrome browser is used or not.

            Returns :
                    A tuple containing a boolean status, a message, and an exception (if any).
        """     
        try:
            if zia_user == zpa_user == oneid_user == None:
                self.logger.error("ZIA, ZPA, One ID users are specified as None, It requires a value")
                return False, "ZIA, ZPA, One ID users are specified as None, It requires a value", None
            if not zia_password and zia_user:
                self.logger.error("ZIA User has been specified, But no password for ZIA User specified")
                return False, "ZIA User has been specified, But no password for ZIA User specified", None
            if not zpa_password and zpa_user:
                self.logger.error("ZPA User has been specified, But no password for ZPA User specified")
                return False, "ZPA User has been specified, But no password for ZPA User specified", None
            if not oneid_password and oneid_user:
                self.logger.error("One ID User has been specified, But no password for One ID User specified")
                return False, "One ID User has been specified, But no password for One ID User specified", None
            
            zia,zpa,oneid = False,False,False
            if oneid_user and oneid_password: oneid=True
            if zia_user and zia_password: zia=True
            if zpa_user and zpa_password: zpa=True

            if oneid:
                self.handle_one_id_login(oneid_password=oneid_password, oneid_user=oneid_user, aup=aup, cancel_aup=cancel_aup, cancel_login_at_oneid=cancel_login_at_oneid)
            
            else:
                if zia_user==zia_password==zpa_user==zpa_password==None:
                    # no zia user is given, check for zpa user
                    self.logger.error("Neither ZIA or ZPA user given to be logged in, cannot login")
                    raise Exception("Neither ZIA or ZPA user given to be logged in, cannot login")
            

                if zia and zpa:self.logger.info("Given user details are ZIA+ZPA")
                if not zia:self.logger.info("Given user is ZPA only user")
        
                # Intial Username field -------------------------
                if zia or zpa:
                    self.logger.info("initial_zcc_login_window")
                    self.initial_zcc_login_window(username=zia_user if zia else zpa_user)
            
                # AUP -------------------------
                if aup:
                    self.logger.info("handle_aup")
                    self.handle_aup(time_for_log_search=self.start_time_for_log_search,zia_saml_login=zia_saml_login,cancel_aup=cancel_aup)
            
            # ZIA -------------------------
            if zia:
                self.logger.info("ZIA")
                if zia_saml_login:
                    self.handle_zia_saml(username=zia_user,password=zia_password,time_for_log_search=self.start_time_for_log_search,cancel_zia_saml_login=cancel_login_at_zia,browser_based_auth=browser_based_auth,chrome_browser=chrome_browser)
                elif zia_one_id_login:
                    self.handle_zia_one_id_login()
                else:
                    self.handle_zia_login(password=zia_password,time_for_log_search=self.start_time_for_log_search,cancel_zia_login=cancel_login_at_zia)
            
            # ZPA -------------------------
            if zpa:
                self.logger.info('waiting')
                time.sleep(10)
                self.logger.info("ZPA")
                self.handle_zpa_login(username=zpa_user,password=zpa_password,time_for_log_search=self.start_time_for_log_search,cancel_zpa_login=cancel_login_at_zpa, use_login_hint=use_login_hint,browser_based_auth=browser_based_auth,chrome_browser=chrome_browser,zia_saml_login=zia_saml_login)
            self.logger.info("Success :: Logged into ZCC Succesfully!")
            
            if sleep_time>0:
                self.logger.info(f'Sleeping for {sleep_time}')
                time.sleep(sleep_time)
            
            zcc_in_focus = self.bring_zcc_to_focus()
            
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
        except Exception as e:
            return (False,f"Error :: Login Failed {e}",None)
        else:
            return (True,f"Success :: Logged in Successfully ",None)

    @calculate_func_execute_time
    @allure.step("logout ZCC")
    def logout( self,
                password: str =None,            # Passowrd/OTP to be used for logout
                sleep_time: int =180,           # number of seconds to sleep when executed
                failure_expected: bool=False,   # Defines whether logout should fail - used with incorrect password
                cancel_logout: bool = False     # Defines whether to cancel logout operation
               ):
        """
            This function is used to logout from the application.
            return: A tuple containing a boolean and a message indicating the success or failure of the operation.
            :rtype: tuple

        """
        
        try:
            if sleep_time==0:sleep_time=30
            try:
                zcc_in_focus = self.bring_zcc_to_focus()
                if not zcc_in_focus[0]:
                    raise Exception(zcc_in_focus[2])
                time.sleep(2)
                self.ui.click(self.elements.LOGOUT_BUTTON, sleep_time=1 * self.sleep_factor,AEFC="Unable to click logout Button")
                start_time_for_log_search = datetime.now()
                
                if cancel_logout:
                    self.logger.info("Aborting logout")
                    self.ui.click(self.elements.LOGOUT_CANCEL_BUTTON, sleep_time=0, AEFC="Unable to confirm logout disable")
                    return (True,f"Success :: Aborted Logged out Successfully ",None)
                if password: 
                    self.ui.type(self.elements.LOGOUT_PASSWORD_BOX, text=password, sleep_time=1*self.sleep_factor, AEFC="Unable to enter password")
                    self.ui.click(self.elements.LOGOUT_ACCEPT_BUTTON, sleep_time=0*self.sleep_factor, AEFC="Unable to confirm logout")
                else:
                    self.ui.click(self.elements.LOGOUT_ACCEPT_BUTTON, sleep_time=0, AEFC="Unable to confirm  ZCC logout")
                if failure_expected:    # mostly used when incorrect password is given intentionally 
                    self.ui.click(self.elements.LOGOUT_CANCEL_BUTTON, sleep_time=0, AEFC="Unable to confirm logout disable")
                    return (True,f"Success :: Not logged out as Failure was expected",None)
                logs_to_be_searched = 'logout success'
                is_logout_validated = False
                try:
                    self.log_ops.search_log_file(file="ZSATray",words_to_find_in_line=[logs_to_be_searched],start_timestamp=start_time_for_log_search)
                    is_logout_validated=True
                except Exception as e: is_logout_validated=False
                if is_logout_validated:
                    zcc_in_focus = self.bring_zcc_to_focus()
                    if not zcc_in_focus[0]:
                        raise Exception(zcc_in_focus[2])
                    self.logger.info("Success :: Logged Out !")
                else: raise Exception(f"logout not validated, max wait time was {sleep_time}")
            except Exception as e: 
                self.logger.error(f"logout Failed at ZSTray Page :: {e}\n".format(e))
                raise Exception(f"Error :: logout Failed at ZSTray Page :: {e}\n".format(e))
            # assert not self.ui.check_if_exists(self.elements.LOGOUT_BUTTON, boolean=True, AESC="logout Button not accepted", AEFC="Logged out well",dynamic_wait_search_element=5)
            self.logger.info("Success :: Logged out!")
        except Exception as e:
            return (False,f"Error :: Logout Failed {e}",None)
        return (True,f"Success :: Logged out Successfully ",None)

    def click_login_error_ok_button(self):
        """
            Click the login error OK button.

            This function attempts to click the login error OK button. If successful, it returns a tuple
            with the first element as True and the second element as a success message. If unsuccessful,
            it returns a tuple with the first element as False and the second element as an error message.

            Returns:
                tuple: A tuple containing a boolean and a string indicating the result of the operation.

        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(self.elements.LOGIN_ERROR_OK_BUTTON, sleep_time=0, AEFC="Unable to click More button on ZCC")
        except Exception as e:
            self.logger.error(f"Error :: Click login Error OK Failed :: {e}\n")
            return (False,f"Click login Error OK Failed :: {e}\n",None)
        return (True,f"Success :: Click login Error OK button Successfully ",None)

    @allure.step("validate zcc logged in")
    def validate_zcc_logged_in(self):
        """
            Validate if ZCC is logged in.

            This function attempts to validate if ZCC is logged in by bringing ZCC to focus,
            checking for the existence of a logout button, and logging relevant information.

            Returns:
                tuple: A tuple containing a boolean value indicating the success or failure of the operation,
                a string message, and an exception object if an exception was raised.

        """
        try:
            zcc_state_validated = False
            try:
                zcc_in_focus = self.bring_zcc_to_focus()
                if not zcc_in_focus[0]:
                    raise Exception(zcc_in_focus[2])
                self.ui.click(
                    path=self.elements.LOGOUT_BUTTON,
                    sleep_time=0,
                    AEFC="Unable to find logout button on ZCC",
                    do_click=False,
                    max_threshold=1,
                    retry_frequency=1,
                    dynamic_wait_search_element=10
                )
                zcc_state_validated=True
            except Exception as e:
                self.logger.error(f"Error while clicking logout button: {e}")
                zcc_state_validated=False
            
            if zcc_state_validated:
                self.ui.click(self.elements.LOGOUT_CANCEL_BUTTON, sleep_time=0, AEFC="Unable to confirm logout disable")
                self.logger.info("logout button exists, zcc logged in")
            else:
                self.logger.info("logout button does not exists, zcc not logged in")
                raise Exception("logout button does not exists, zcc not logged in")

        except Exception as e:
            self.logger.error(f"Zcc is not logged in, Logout button was not found, Exception: {e}")
            return False,f"Zcc is not logged in, Logout button was not found, Exception: {e}",None
        return True, f"Success :: Login Validated Successfully", None
    
    @allure.step("validate zcc logged out")
    def validate_zcc_logged_out(self):
        """
            Validate if ZCC is logged out.

            This function attempts to validate if ZCC is logged out by bringing ZCC to focus,
            checking for the existence of a login button, and logging relevant information.

            Returns:
                tuple: A tuple containing a boolean value indicating the success or failure of the operation,
                a string message, and an exception object if an exception was raised.

        """
        try:
            i=0
            zcc_state_validated = False
            while i<2:
                try:
                    zcc_in_focus = self.bring_zcc_to_focus()
                    if not zcc_in_focus[0]:
                        raise Exception(zcc_in_focus[2])
                    self.ui.click(path=self.elements.LOGIN_BUTTON,sleep_time=0, AEFC="Unable to find login button on ZCC",do_click=False,max_threshold=1,retry_frequency=1)
                    zcc_state_validated=True
                except:
                    i+=1
                    time.sleep(2)
                else:
                    break
            if zcc_state_validated:
                self.logger.info("login button exists, zcc logged out")
            else:
                self.logger.info("login button does not exists, zcc not logged out")
                raise Exception("login button does not exists, zcc not logged out")
        except Exception as e:
            self.logger.error(f"Error :: {e}\n")
            return (False,f"Error ::  {e}\n",None)
        return (True,f"Success :: Logout Validated Successfully ",None)
    
    @calculate_func_execute_time
    @allure.step("Enable/disable Service ZIA/ZPA/ZDX")
    def toggle_service(
        self,
        service: str,                   # Which service to Toggle (ZIA/ZPA/ZDX)
        action: bool =True,             # Turn on if True, otherwise Turn off
        password: str =None,            # Password/OTP for disabling service
        disable_reason: str =None,      # Disable reason to enter while disabling any zscaler service (zia/zpa/zdx)
        sleep_time=10,                  # Number of seconds to sleep after execution
        cancel_toggle: bool =False,     # Determines whether to cancel toggle operation or not
        failure_expected: bool =False   # Expect that password will not be accepted. deal accordingly
    ):     
        """
        Toggle a Zscaler service (ZIA/ZPA/ZDX) on or off.

        Args:
            service (str): The service to toggle (ZIA/ZPA/ZDX).
            action (bool): If True, turn on the service. Otherwise, turn it off. Default is True.
            password (str): The password or OTP for disabling the service. Default is None.
            disable_reason (str): The disable reason to enter when disabling a Zscaler service. Default is None.
            sleep_time (int): The number of seconds to sleep after execution. Default is 10.
            cancel_toggle (bool): If True, cancel the toggle operation. Default is False.
            failure_expected (bool): If True, expect that the password will not be accepted. Default is False.

        Returns:
            tuple: A tuple containing a boolean indicating success or failure and a message describing the result.
        """           
        if service.lower()=="zia":
            TAB = self.elements.INTERNET_SECURITY_TAB
            TOGGLE_ON = self.elements.INTERNET_SECURITY_POWER_BUTTON_ON
            TOGGLE_OFF = self.elements.INTERNET_SECURITY_POWER_BUTTON_OFF
            PASSOWRD_BOX = self.elements.INTERNET_SECURITY_PASSWORD_BOX
            DISABLE_REASON_BOX_WITH_PASSWORD = self.elements.PASSWORD_DISABLE_REASON_BOX
            DISABLE_REASON_BOX = self.elements.DISABLE_REASON_BOX
            PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
            PASSWORD_ACCEPT_BUTTON = self.elements.INTERNET_SECURITY_PASSWORD_ACCEPT_BUTTON
            CONFIRM_ACCEPT_BUTTON = self.elements.CONFIRM_ACCEPT_BUTTON
            FAILED_CONFIRM_BUTTON = self.elements.INTERNET_SECURITY_PASSWORD_FAILED_OK_BUTTON
            CANCEL_BUTTON = self.elements.INTERNET_SECURITY_PASSWORD_CANCEL_BUTTON
        elif service.lower()=="zdp":
            TAB = self.elements.DATA_PREVENTION_TAB
            TOGGLE_ON = self.elements.DATA_PREVENTION_POWER_BUTTON_ON
            TOGGLE_OFF = self.elements.DATA_PREVENTION_POWER_BUTTON_OFF
            PASSOWRD_BOX = self.elements.DATA_PREVENTION_PASSWORD_BOX
            DISABLE_REASON_BOX_WITH_PASSWORD = self.elements.PASSWORD_DISABLE_REASON_BOX
            DISABLE_REASON_BOX = self.elements.DISABLE_REASON_BOX
            PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
            PASSWORD_ACCEPT_BUTTON = self.elements.DATA_PREVENTION_PASSWORD_ACCEPT_BUTTON
            CONFIRM_ACCEPT_BUTTON = self.elements.CONFIRM_ACCEPT_BUTTON
            FAILED_CONFIRM_BUTTON = self.elements.DATA_PREVENTION_PASSWORD_FAILED_OK_BUTTON
            CANCEL_BUTTON = self.elements.DATA_PREVENTION_PASSWORD_CANCEL_BUTTON
        elif service.lower() == "zpa":
            TAB = self.elements.PRIVATE_ACCESS_TAB
            TOGGLE_ON = self.elements.PRIVATE_ACCESS_POWER_BUTTON_ON
            TOGGLE_OFF = self.elements.PRIVATE_ACCESS_POWER_BUTTON_OFF
            PASSOWRD_BOX = self.elements.PRIVATE_ACCESS_PASSWORD_BOX
            DISABLE_REASON_BOX_WITH_PASSWORD = self.elements.PASSWORD_DISABLE_REASON_BOX
            DISABLE_REASON_BOX = self.elements.DISABLE_REASON_BOX
            PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
            PASSWORD_ACCEPT_BUTTON = self.elements.PRIVATE_ACCESS_PASSWORD_ACCEPT_BUTTON
            CONFIRM_ACCEPT_BUTTON = self.elements.CONFIRM_ACCEPT_BUTTON
            FAILED_CONFIRM_BUTTON = self.elements.PRIVATE_ACCESS_PASSWORD_FAILED_OK_BUTTON
            CANCEL_BUTTON = self.elements.PRIVATE_ACCESS_PASSWORD_CANCEL_BUTTON
        elif service.lower() == "zdx":
            TAB = self.elements.DIGITAL_EXPERIENCE_TAB
            TOGGLE_ON = self.elements.DIGITAL_POWER_BUTTON_ON
            TOGGLE_OFF = self.elements.DIGITAL_POWER_BUTTON_OFF
            PASSOWRD_BOX = self.elements.DIGITAL_EXPERIENCE_PASSWORD_BOX
            DISABLE_REASON_BOX_WITH_PASSWORD = self.elements.PASSWORD_DISABLE_REASON_BOX
            DISABLE_REASON_BOX = self.elements.DISABLE_REASON_BOX
            PASSWORD_DISABLE_OK_BUTTON = self.elements.PASSWORD_DISABLE_REASON_OK_BUTTON
            PASSWORD_ACCEPT_BUTTON = self.elements.DIGITAL_EXPERIENCE_PASSWORD_ACCEPT_BUTTON
            CONFIRM_ACCEPT_BUTTON = self.elements.CONFIRM_ACCEPT_BUTTON
            FAILED_CONFIRM_BUTTON = self.elements.DIGITAL_EXPERIENCE_PASSWORD_CANCEL_BUTTON
            CANCEL_BUTTON = self.elements.DIGITAL_EXPERIENCE_PASSWORD_CANCEL_BUTTON
        
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(TAB, sleep_time=1*self.sleep_factor,AEFC=f"Unable to open {service} tab")
            if action:
                self.ui.click(TOGGLE_ON ,sleep_time=1*self.sleep_factor, AEFC=f"Unable to turn on {service}")
            else:
                self.ui.click(TOGGLE_OFF, sleep_time=1*self.sleep_factor, AEFC=f"Unable to turn off {service}")
        
            if password and disable_reason:
                self.ui.type(DISABLE_REASON_BOX_WITH_PASSWORD, text=str(disable_reason),sleep_time=1 * self.sleep_factor, AEFC="Unable to enter disable reason")
                self.ui.type(PASSOWRD_BOX, text=password,sleep_time=1 * self.sleep_factor, AEFC="Unable to enter password")
                # password ok button
                if cancel_toggle:
                    self.logger.info("Aborting Toggle")
                    self.ui.click(CANCEL_BUTTON,sleep_time=1*self.sleep_factor,AEFC=f"Unable to cancel {service} disable")
                else:
                    self.ui.click(PASSWORD_DISABLE_OK_BUTTON,sleep_time=1*self.sleep_factor,AEFC=f"Unable to confirm {service} disable")
            else:
                if disable_reason:
                    self.ui.type(DISABLE_REASON_BOX, text=str(disable_reason),sleep_time=1*self.sleep_factor, AEFC="Unable to enter disable reason")
                    if cancel_toggle:
                        self.logger.info("Aborting Toggle")
                        self.ui.click(self.elements.DISABLE_REASON_CANCEL_BUTTON,sleep_time=1*self.sleep_factor,AEFC=f"Unable to cancel {service} disable")
                if password:
                    self.ui.type(PASSOWRD_BOX, text=password,sleep_time=1 * self.sleep_factor, AEFC="Unable to enter password")
                    # password ok button
                    if cancel_toggle:
                        self.logger.info("Aborting Toggle")
                        self.ui.click(CANCEL_BUTTON,sleep_time=1*self.sleep_factor,AEFC=f"Unable to cancel {service} disable")
                    else:
                        self.ui.click(PASSWORD_ACCEPT_BUTTON,sleep_time=1*self.sleep_factor,AEFC=f"Unable to confirm {service} disable")
                else:
                    if cancel_toggle:
                        self.logger.info("Aborting Toggle")
                        self.ui.click(self.elements.DISABLE_REASON_CANCEL_BUTTON,sleep_time=1*self.sleep_factor,AEFC=f"Unable to cancel {service} disable")
                    else:
                        if not action:
                            self.ui.click(CONFIRM_ACCEPT_BUTTON, sleep_time=1*self.sleep_factor,AEFC=f"Unable to confirm {service} disable")
            if failure_expected:
                self.ui.click(CANCEL_BUTTON, sleep_time=2,AEFC=f"Unable to confirm {service} disable")
            else:
                assert self.ui.check_if_exists(TOGGLE_OFF[1] if action else TOGGLE_ON[1], boolean=True, AESC=f"{service} toggle not accepted properly")

            # assert not self.ui.check_if_exists(CANCEL_BUTTON,boolean=True,AESC=f"{service} toggle not accpeted properly")

            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
        except Exception as e:
            self.logger.error(f"Unable to Toggle Service: {service} {action} :: {e}")
            return (False,f"Error :: Unable to Toggle Service: {service} {action} :: {e}\n",None)
        return (True,f"Success :: Toggle Service: {service} {action} Successfully ",None)

    @calculate_func_execute_time
    @allure.step("Exit ZCC")
    def exit_zcc(
        self,
        relaunch_zcc: bool = False,     # Defines whether to relaunch zcc after doing exit
        exit_password: str = None,      # The password required to exit zcc
        failure_expected: bool = False,
        cancel_exit: bool = False
    ):
        
        # Start performing actions to exit ZCC
        is_zcc_running = False
        try:
            self.ui.click(self.elements.ZCC_TRAY_ICON[1], sleep_time=1*self.sleep_factor, AEFC="Unable to click ZCC icon in the menubar")
            self.ui.click(self.elements.ZCC_EXIT_MENUBAR_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click exit button in menubar")
            if exit_password:
                self.ui.type(self.elements.EXIT_ZCC_PASSWORD_BOX,text=exit_password,sleep_time=1*self.sleep_factor, AEFC="Unable to click exit ok")
        except Exception as e:
            self.logger.error(f"An error occured when trying to exit ZCC :: {e}")
            return (False, f"An error occured when trying to exit ZCC :: {e}", None)
        else:
            if cancel_exit:
                self.ui.click(self.elements.EXIT_ZCC_CANCEL_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click cancel button")
                return True, "ZCC exit is cancelled", None
            else:
                self.ui.click(self.elements.EXIT_ZCC_OK_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click ok button")

            # Fetch the PID of ZCC to check if it is running or not
            self.logger.info("Waiting for 15 seconds to allow ZCC to exit completely")
            time.sleep(15)
            zcc_process_check_result = self.sys_ops.is_application_running()
            if not zcc_process_check_result[0]:
                return False, zcc_process_check_result[1], None
            else:
                is_zcc_running = zcc_process_check_result[2]

            # Validate the expected behaviour
            if failure_expected:
                if (exit_password=="" or exit_password) and not cancel_exit:
                    self.click_error_popup_ok_button()
                    self.ui.click(self.elements.EXIT_ZCC_CANCEL_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click cancel button")

                if is_zcc_running:
                    self.logger.info("ZCC is still running as expected :: Exit failed as expected")
                else:
                    self.logger.error("ZCC has exited :: Exit failure was expected")
                    return False, "ZCC has exited ::  Exit failure was expected", None
            else:
                if is_zcc_running:
                    self.logger.error("ZCC is still running :: Exit was expected")
                    return False, "ZCC is still running :: Exit was expected", None
                else:
                    self.logger.info("ZCC has exited :: Exited as expected")

            if relaunch_zcc:
                self.logger.info("Relaunching ZCC after exit")
                self.ui.start_app(app=self.elements.APP)

        return True, "ZCC exit is completed",None

    @allure.step("Check error notification")
    def check_error_notification(self, notification_text=None):
        """
        This function checks for error notifications.

        :param notification_text: The text of the error notification to be verified.
        :return: A tuple containing a boolean status and a message.
        """
        try:
            if notification_text == None:
                raise Exception("Provide the notification text to be verified")
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.check_if_exists(self.elements.ERROR_DIALOG)
            element = self.ui.click(self.elements.ERROR_VALUE,do_click=False,return_text=True,sleep_time=0)
            assert not self.ui.click(self.elements.LOGIN_ERROR_OK_BUTTON, sleep_time=2,
                                      AEFC="Unable to click on error notification Ok button")
            if element== notification_text:
                self.logger.info(f"Expected error notification exists with message \"{notification_text}\"")
            else:
                self.logger.error(f"Expected error notification with message \"{notification_text}\" does not exist")
                assert not self.ui.click(self.elements.LOGIN_ERROR_OK_BUTTON, sleep_time=2,
                                          AEFC="Unable to click on error notification Ok button")
                raise Exception(
                    f"Error :: Expected error notification with message \"{notification_text}\" does not exist")
        except Exception as e:
            self.logger.error(f"Failed to verify ZCC notification :: {e}\n")
            return (False,f"Error :: Failed to verify ZCC notification :: {e}\n",None)
        return (True,f"Success :: Verified ZCC error notification Successfully ",None)

    @allure.step("Clear All Notification")
    def clear_all_notification(self):
        """
        This function is used to clear all notifications in the ZCC application.

        :return: A tuple containing a boolean status and a string message.
        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(self.elements.NOTIFICATIONS_TAB, sleep_time=2, AEFC="Unable to open Notifications tab")
            self.ui.click(self.elements.CLEAR_ALL_NOTIFICATIONS, sleep_time=2,
                        AEFC="Unable to click on Clear All button in Notifications tab")
            self.ui.click(self.elements.CLEAR_NOTIFICATIONS_CONTINUE, sleep_time=2,
                        AEFC="Unable to click on Continue button to clear all notifications")
            self.logger.info('Cleared All ZCC Notifications')
        except Exception as e:
            self.logger.error(f"Failed to verify ZCC notification :: {e}\n")
            return (False,f"Error :: Failed to Clear ZCC notification :: {e}",None)
        return (True,f"Success :: Cleared ZCC notification Successfully ",None)

    @allure.step("Find Notification")
    def find_notification(self, notification:str, return_notification_details: bool = False):
        """
        Find a specific notification in the ZCC application.

        This function attempts to locate a given notification within the ZCC application.
        It first brings the ZCC to focus, then opens the Notifications tab and searches for
        the specified notification. If found, it logs the success and returns True. If not
        found, it raises an exception.

        Args:
            notification (str): The name of the notification to search for.

        Returns:
            tuple: A tuple containing a boolean indicating success or failure and an error message if applicable.

        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(self.elements.NOTIFICATIONS_TAB, sleep_time=2, AEFC="Unable to open Notifications tab")
            self.ui.click(self.elements.APP_MORE, sleep_time=2, AEFC="Unable to open more tab")
            self.ui.click(self.elements.NOTIFICATIONS_TAB, sleep_time=2, AEFC="Unable to open Notifications tab")
            # Maximum rows to be searched for notification after performing an action
            max_rownumber = 5
            for each_rownumber in range(max_rownumber):
                element_id = self.elements.NOTIFICATIONS_MESSAGE_LABEL.format(each_rownumber)
                print("Sleeping for 5 seconds, Element ID: ",element_id)
                time.sleep(5)
                if self.ui.fetch_attribute(identifier=element_id, fetch_text=True).lower() == notification.lower():
                    self.logger.info(f'Required notification present in ZCC : \'{notification}\'')
                    if return_notification_details:
                        notification_category = self.ui.fetch_attribute(identifier=self.elements.NOTIFICATIONS_CATEGORY_LABEL.format(each_rownumber), fetch_text=True)
                        notification_time = self.ui.fetch_attribute(identifier=self.elements.NOTIFICATIONS_TIME_LABEL.format(each_rownumber), fetch_text=True)
                        return (True,"Succes :: Notification Details Found Successfully", (notification_category, notification_time, notification))
                    return (True,"Succes :: Notification Found Successfully",None)
            raise Exception(f'Required notification not present in ZCC : \'{notification}\'')
        except Exception as e:
            return (False,f"Error :: Failed to find {notification} notification :: {e}",None)
        
    def send_feedback_of_ssit_notification(self,feedbackType= True):
        """
        This function give feedback for a specific message present if it's Learn More text available in ZCC Notification tab.

        :param self: The object itself.
        :feedbackType: True for positive feedback, False for Negative Feedback
        :return: None
        """
        try:
            self.click_notification_learn_more()
            if feedbackType==True:
                self.ui.click(self.elements.NOTIFICATION_LEARN_MORE_POSITIVE_FEEDBACK, sleep_time=2,
                        AEFC="Unable to click on positive feedback on learn more popup")
            else:
                self.ui.click(self.elements.NOTIFICATION_LEARN_MORE_NEGATIVE_FEEDBACK, sleep_time=2,
                        AEFC="Unable to click on negative feedback on learn more popup")
            self.logger.info('SSIT feedback sent')
        except Exception as e:
            self.logger.error(f"Failed to verify ZCC notification :: {e}\n")
            return (False,f"Error :: Failed to Clear ZCC notification :: {e}",None)
        return (True,f"Success :: Cleared ZCC notification Successfully ",None)
    
    @allure.step("Find Internet Access Tab")
    def find_ssit_notification_popup(self, 
                            should_be_present: bool
                            ,action:bool):    # accepts True if tab is expected else False
        """
            Find notification ssit auth status label.

            :param should_be_present: Accepts True if tab is expected else False
            :type should_be_present: bool
            :return: A tuple containing a bool indicating success or failure and a message
            :rtype: tuple
        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(self.elements.APP_MORE, sleep_time=2, AEFC="Unable to open App More tab")
            if action==False:
                self.ui.click(self.elements.SHOW_NOTIFICATIONS_FOR_SSIT_AUTH_OFF_BUTTON, sleep_time=2,AEFC="Unable click on the notification ssit auth status label on",expect_click_failure=not should_be_present)
            else:
                self.ui.click(self.elements.SHOW_NOTIFICATIONS_FOR_SSIT_AUTH_ON_BUTTON, sleep_time=2,AEFC="Unable click on the notification ssit auth status label on",expect_click_failure=not should_be_present)

        except Exception as e:
            return (False,f"Error :: Failed to click on the notification ssit auth status label :: {e}",None)
        return (True,"Success :: Clicked on the notification ssit auth status label ",None)

    @calculate_func_execute_time
    @allure.step("Update Policy")
    def update_policy(self, one_id = False):
        """
        Update policy method for the application.

        This method is responsible for updating the policy of the application. It
        performs a series of actions like getting the current ZCC version, clicking
        on the More button and the Update Policy button, and validating the update.

        Returns:
            tuple: A tuple containing a boolean status and a string message.

        """
        try:
            if self.zcc_build=='':
                self.zcc_build = self.log_ops.get_current_zcc_version()
                self.logger.info(f"ZCC BUILD :: {self.zcc_build}\n\n")
            try:
                zcc_in_focus = self.bring_zcc_to_focus()
                if not zcc_in_focus[0]:
                    raise Exception(zcc_in_focus[2])
                self.ui.click(self.elements.APP_MORE, sleep_time=1*self.sleep_factor, AEFC="Unable to click More button on ZCC")
                start_time_for_log_search = datetime.now()
                self.ui.click(self.elements.APP_UPDATE_POLICY, sleep_time=0, AEFC="Unable to click Update Policy button")
            except Exception as e: 
                self.logger.error(f"Update Policy Failed :: {e}")
                raise Exception(f"Error :: Update Policy Failed :: {e}")
            is_update_policy_validated = False
            # logs_to_be_searched = 'updateConfig:success'

            if int(self.zcc_build[0])<= 4 and int(self.zcc_build[2])<=2:
                strings_list_to_be_searched_in_logs = [["apiMobilePolicyDownloadV2: Finish"], ["updateConfig:success"]]
            else:
                strings_list_to_be_searched_in_logs = [["apiMobilePolicyDownloadV2: Finish"], ["updateConfig:XPC","Result: Success","Error: nil"]]
            
            if one_id:
                strings_list_to_be_searched_in_logs = [["One::ID::ZSOneIdMobileAdminSvc: trigger keep alive"],["One::ID::ZSOneIdMobileAdminSvc: keep alive done"], ["One::ID::ZSOneIdMobileAdminSvc: policy download done"]]
                
            for each_string_list in strings_list_to_be_searched_in_logs:
                try:
                    self.log_ops.search_log_file(file="ZSATray",words_to_find_in_line=each_string_list,search_mode=None,start_timestamp=start_time_for_log_search)
                    is_update_policy_validated=True
                except Exception as e:
                    is_update_policy_validated=False
                    break

            
            if is_update_policy_validated:
                zcc_in_focus = self.bring_zcc_to_focus()
                if not zcc_in_focus[0]:
                    raise Exception(zcc_in_focus[2])
                try:
                    error_log = 'Keepalive request failed'
                    self.log_ops.search_log_file(file="ZSATray",words_to_find_in_line=[error_log],wait_time=0)
                except Exception as e:
                    self.logger.info("Keep Alive Error log not found, all good")
                else:
                    self.logger.error(f"Keep Alive Error log {error_log} found, update policy failed")
                    raise Exception(f"Keep Alive Error log {error_log} found, update policy failed")
                self.logger.info("Success :: Updated Policy!")
            else:
                self.logger.error(f"Update policy not validated")
                raise Exception(f"Update policy not validated")
        except Exception as e:
            return (False,f"Error ::  {e}",None)
        return (True,f"Success :: Updated Policy Successfully ",None)
    
    @calculate_func_execute_time
    @allure.step("Restart Service")
    def restart_service(
        self,
        sleep_time: int=60,                     # Number of seconds to sleep after execution
        validate_zia_connected: bool =False     # Defines whether to check zia connection after restart service
    ):
        """
        Restart the ZIA service.

        This function restarts the ZCC services and optionally checks if the ZIA connection is still active after the restart.

        Args:
            sleep_time (int): Number of seconds to sleep after execution. Default is 60.
            validate_zia_connected (bool): Defines whether to check zia connection after restart service. Default is False.

        Returns:
            tuple: A tuple containing a boolean status and a string message.
        """
        try:
            if self.zcc_build=='':
                fetch_app_version_result=self.sys_ops.fetch_application_version()
                if not fetch_app_version_result[0]:
                    raise Exception(fetch_app_version_result[1])
                self.zcc_build = fetch_app_version_result[2]
            try:
                zcc_in_focus = self.bring_zcc_to_focus()
                if not zcc_in_focus[0]:
                    raise Exception(zcc_in_focus[2])
                self.ui.click(self.elements.APP_MORE, sleep_time=0, AEFC="Unable to click More button on ZCC")
                self.ui.click(self.elements.RESTART_SERVICE, sleep_time=0,AEFC="Unable to click Restart Service button on ZCC")
                self.ui.click(self.elements.RESTART_SERVICE_ACCEPT, sleep_time=0,AEFC="Unable to confirm ZCC Restart")
            except Exception as e:
                self.logger.error(f"Restart Service Failed :: {e}")
                raise Exception(f"Error :: Restart Service Failed :: {e}")
            
            log_directory = self.log_ops.get_log_path_for_current_zcc()
                
            before_files = len([files for files in sorted(os.listdir(log_directory),reverse=True) if "ZSATunnel" in files])
            current_files = before_files

            start_time = int(time.time())
            while(current_files==before_files and int(time.time())-start_time<=sleep_time):
                current_files = len([files for files in sorted(os.listdir(log_directory),reverse=True) if "ZSATunnel" in files])
                print(f"tunnelFiles:{current_files}, Time:{int(time.time())-start_time} s", end='\r')
            if current_files==before_files:
                error = 'Please check manually'
                try:
                    error = self.ui.click(
                        "ErrorValue",
                        do_click=False,
                        return_text=True,
                        AESC="Tunnel restart rate limited pop up encountered",
                        dynamic_wait_search_element=10
                    )
                    self.logger.error(error)
                    self.ui.click(
                        'OkButton', 
                        sleep_time=0, 
                        AEFC="Unable to click Failed Restart service OK Button on ZCC",
                        dynamic_wait_search_element=10
                    )
                except:
                    self.logger.info("NO error box found, might be some different issue")
                finally:
                    raise Exception(f"Tunnel files before restart service :: {before_files}, Tunnel files after restart service :: {current_files}\nError Reason ::{error}")
            self.logger.info(f"Tunnel files before restart service :: {before_files}")
            self.logger.info(f"Tunnel files after restart service :: {current_files}")
            
            if validate_zia_connected:
                is_zia_validated=False
                retry_count=0
                while(is_zia_validated==False and retry_count<3):
                    try:
                        self.validate_zia_server_state('connected')
                        self.logger.info("ZIA Conected validated")
                        is_zia_validated=True
                    except Exception as e:
                        self.logger.error(f"Error while updating zia state :: {e}")
                        retry_count+=1
                        is_zia_validated=False
                if not is_zia_validated:raise Exception("ZIA not in connected state after restart service")
            self.logger.info("Success :: Restarted Service!")
        except Exception as e:
            return (False,f"Error ::  {e}",None)
        return (True,f"Success :: Restarted Service  Successfully ",None)

    @allure.step("reauth from menubar")
    def reauth_from_menubar(self,
                            zpa_user: str = None,
                            zpa_password: str = None,
                            authenticate_early: bool = False,  # true to click, false to not click
                            register: bool = False,  # Register zpa
                            authenticate: bool = False,  # authenticate after zpa expiry
                            cancel_login_at_zpa: bool = False,  # cancel login at saml page
                            browser_based_auth: bool = False,  # True for BBA
                            chrome: bool = False  # use chrome browser if True
                            ):

        """
        Re-authenticates from the menubar button.

        This function handles re-authentication from the menubar by performing the following steps:
        - Brings the ZCC to focus.
        - Optionally clicks on the Authenticate Early, Authenticate, or Register button.
        - Handles ZPA login.
        """

        try:
            res = self.bring_zcc_to_focus()
            if not res[0]:
                raise Exception(res[1])
            self.ui.click(self.elements.ZCC_TRAY_ICON[1],
                          sleep_time=1 * self.sleep_factor,
                          AEFC="Unable to click ZCC icon in the menubar")

            if authenticate_early:
                self.ui.click(
                    self.elements.ZCC_AUTHENTICATE_EARLY_MENUBAR_BUTTON,
                    sleep_time=1 * self.sleep_factor,
                    max_threshold=6,
                    retry_frequency=3,
                    AEFC="Unable to click Authenticate Early button on ZCC")

            elif authenticate:
                self.ui.click(
                    self.elements.ZCC_AUTHENTICATE_MENUBAR_BUTTON,
                    sleep_time=1 * self.sleep_factor,
                    max_threshold=6,
                    retry_frequency=3,
                    AEFC="Unable to click Authenticate Early button on ZCC")

            elif register:
                self.ui.click(
                    self.elements.ZCC_REGISTER_MENUBAR_BUTTON,
                    sleep_time=1 * self.sleep_factor,
                    max_threshold=6,
                    retry_frequency=3,
                    AEFC="Unable to click Authenticate Early button on ZCC")

            time.sleep(10)

            res = self.handle_zpa_login(username=zpa_user, password=zpa_password, cancel_zpa_login=cancel_login_at_zpa,
                                        browser_based_auth=browser_based_auth, chrome_browser=chrome)
            return True, "Login Successful", res

        except Exception as e:
            self.logger.error(e)
            return False, str(e), None

    @allure.step("Restart zdx Service")
    def restart_zdx_service(self,
                        sleep_time: int = 10, # Number of seconds to sleep after execution
                        )->tuple[bool,str]:
        """
        This function restarts the zdx services. It first checks the current ZCC (Zscaler Client Connector) build,
        brings the ZCC to focus, and then clicks on the 'Restart zdx Service' button. 

        Parameters:
            sleep_time (int): Number of seconds to sleep after execution. Default is 10

        Returns:
            tuple[bool,str]: A tuple containing a boolean value and a string. The boolean value indicates the success or failure of
            the operation, and the string provides additional information or error messages.
        """
        # if self.zccBuild == '':
        #     self.zccBuild = self.logOps.get_current_zcc_version()
        #     self.logger.info(f"ZCC BUILD :: {self.zccBuild}\n\n")
        try:
            zcc_in_focus=self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                    raise Exception(zcc_in_focus[2])
            self.ui.click(
                self.elements.DIGITAL_EXPERIENCE_TAB,
                sleep_time=0,
                AEFC="Unable to click ZDX tab button on ZCC")
            self.ui.click(
                self.elements.DIGITAL_EXPERIENCE_RESTART_ZDX,
                sleep_time=0,
                AEFC="Unable to click Restart ZDX Service button on ZCC")
            self.ui.click(
                self.elements.DIGITAL_EXPERIENCE_ACCEPT,
                sleep_time=sleep_time,
                AEFC="Unable to confirm ZDX Restart")
        except Exception as e:
            self.logger.error(f"Restart Service Failed :: {e}")
            return (False,f"Error :: Restart ZDX Service Failed :: {e}","")
        return(True,"Success :: Restart ZDX service","")

    @calculate_func_execute_time
    @allure.step("Export Logs")
    def export_logs(
        self, 
        use_zcc_tray: bool = True, # Defines whether to do export logs from zcc tray or from menubar
        cancel_export_logs: bool = False 
    ):     
        """
            Defines whether to do export logs from zcc tray or from taskbar.

            :param use_zcc_tray: Defines whether to do export logs from zcc tray or from taskbar.
            :type use_zcc_tray: bool
            :return: A tuple containing a boolean status and a string message.
            :rtype: tuple
        """
        try:
            try:
                start_time_for_log_search = datetime.now()
                if use_zcc_tray:
                    zcc_in_focus = self.bring_zcc_to_focus()
                    if not zcc_in_focus[0]:
                        raise Exception(zcc_in_focus[2])
                    self.ui.click(self.elements.APP_MORE, sleep_time=0, AEFC="Unable to click More button on ZCC")
                    self.ui.click(self.elements.EXPORT_LOGS, sleep_time=1, AEFC="Unable to click on export logs")
                    if cancel_export_logs:
                        pyautogui.press('esc') # Hit escape using pyauto gui since cancel button is not accessibe and it cannot be assigned an accessbility ID
                        return True, f"Success :: Cancelled Exported Logs", None
                    self.logger.info("Now hitting enter button")
                    time.sleep(2)
                    pyautogui.press('enter')  # Hit enter using pyauto gui since export button is not accessibe and it cannot be assigned an accessbility ID
                else:
                    self.ui.click(self.elements.ZCC_TRAY_ICON[1], sleep_time=1*self.sleep_factor, AEFC="Unable to click Zcc icon in menubar")
                    self.ui.click(self.elements.ZCC_EXPORT_LOGS_MENUBAR_BUTTON, sleep_time=3*self.sleep_factor, AEFC="Unable to click Export Logs in menubar")
                    if cancel_export_logs:
                        pyautogui.press('esc')
                        return True, f"Success :: Cancelled Exported Logs", None
                    pyautogui.press('enter')  # Hit enter using pyauto gui since export button is not accessibe and it cannot be assigned an accessbility ID
            except Exception as e: 
                self.logger.error(f"Export Logs Failed :: {e}")
                raise Exception(f"Error :: Export Logs Failed :: {e}")

        except Exception as e:
            return False, f"Error ::  {e}", None
        return True, f"Success :: Exported Logs Successfully ", None
    
    @allure.step("Enable/Disable AntiTampering from ZCC")
    def toggle_anti_tampering(self,
                action: bool =True,                 # if True is set, expect "enable_anti_tampering", click on it, else expect "disable_anti_tampering", click on it
                password: str =None,                # password to disable anti tempering
                failure_expected: bool =False,      # expect anti tempering action to be failed
                sleep_time: int =5
        ):
        """
            Toggle anti-tampering feature.

            :param action: If True is set, expect "enable_anti_tampering", click on it, else expect "disable_anti_tampering", click on it.
            :param password: Password to disable anti-tampering.
            :param failure_expected: Expect anti-tampering action to be failed.
            :param sleep_time: Time to wait between operations.
            :return: A tuple containing a boolean status and a string message.
        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(self.elements.APP_MORE, sleep_time=1*self.sleep_factor, AEFC="Unable to click More button on ZCC")
            if action: 
                self.ui.click(self.elements.ENABLE_ANTI_TAMPERING, sleep_time=1*self.sleep_factor, AEFC="Unable to click Enable Anti Tampering button")
                self.ui.click(self.elements.ENABLE_ANTI_TAMPERING_SUCCESS_OK_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click Enable Anti Tampering success ok button")
            else: 
                self.ui.click(self.elements.DISABLE_ANTI_TAMPERING, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable Anti Tampering button")
                if password: 
                    self.ui.type(self.elements.DISABLE_ANTI_TAMPERING_PASSWORD_BOX, text=password, sleep_time=1*self.sleep_factor, AEFC="Unable to enter password")
                self.ui.click(self.elements.DISABLE_ANTI_TAMPERING_PASSWORD_BOX_OK_BUTTON, sleep_time=sleep_time, AEFC="Unable to confirm Anti Tampering disable")
        except Exception as e: 
            self.logger.error(f"Anti Tampering Toggle Failed :: {e}")
            return (False,f"Error :: Anti Tampering Toggle Failed {e}",None)
        self.logger.info("Success :: Anti Tampering Toggle success!")
        return (True,f"Success :: Anti Tampering Toggle success!",None)

    @allure.step("Verify log mode from zcc ui")
    def verify_log_mode_drop_down_state(
        self,
        is_editable:bool=True, # Indicates whether the log mode drop down should be clickable or not
        log_mode:str="Default", # Choose within ["Default", "Debug", "Info", "Warn", "Error"]
    ):  
        """
        This function verifies the log mode drop down state.

        :param is_editable: A boolean value to check if the drop down is editable or not. Default is True.
        :return: A tuple containing a boolean status and a string message.
        """
        try:
            elements_mapping_for_log_mode = {
                "Debug": self.elements.LOG_MODE_DROP_DOWN_DEBUG,
                "Info": self.elements.LOG_MODE_DROP_DOWN_INFO,
                "Warn": self.elements.LOG_MODE_DROP_DOWN_WARN,
                "Error": self.elements.LOG_MODE_DROP_DOWN_ERROR,
            }
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(self.elements.APP_MORE, sleep_time=5, AEFC="Unable to select More button on ZCC")
            try:
                self.ui.click(accessibility_id=self.elements.LOG_MODE_DROP_DOWN[0], sleep_time=5,AEFC="Unable to click log mode drop down on ZCC")
                if not log_mode == "Default" and log_mode in ["Debug", "Info", "Warn", "Error"]:
                    log_mode_element = elements_mapping_for_log_mode[log_mode]
                    self.ui.click(path=log_mode_element, sleep_time=5, AEFC=f"Unable to click {log_mode} on log mode drop down popup on ZCC")
                if not is_editable: 
                    raise Exception("Log mode is not supposed to be editable, but it is editable")
            except:
                if is_editable: 
                    raise Exception("Log mode is supposed to be editable, but it is not editable")
                else: 
                    self.logger.info("Log mode is not editable as expected")
        except Exception as e:
            self.logger.error(f"Log mode drop down expected functionality failed, Error: {e}")
            return False,f"Log mode drop down expected functionality failed, Error: {e}",None
        pyautogui.press('esc')
        self.logger.info("Success, Log mode drop down worked as expected")
        return True,f"Success, Log mode drop down worked as expected",None

    @calculate_func_execute_time
    @allure.step("Report An Issue")
    def report_an_issue(
        self,
        sleep_time:int=120,                     # Number of seconds to sleep after execution
        issue_name:str="Automation User",       # Heading / Name for the issue being reported
        email_cc:list[str]=None,                # List of emails to be added to the CC box, if required
        comments:str="Testing automation code", # Comments to be added
        problem:str="Default",                  # Type of problem being reported, choose within ["Default","Authentication", "User Interface", "Network Connectivity", "Private Access", "Others"], Chooose "Default" to leave it as it is
        priority:str="Default",                  # Priority of problem being reported, choose within ["Default", "Urgent", "High", "Normal", "Low"], Chooose "Default" to leave it as it is
        use_zcc_tray: bool = True,
        cancel_report: bool = False
    ):  
        """
            This function reports an issue.

            Args:
                sleep_time (int): Number of seconds to sleep after execution. Default is 120.

            Returns:
                tuple: A tuple containing a boolean status and a string message.
        """
        try:
            try:
                elements_mapping_for_problem_type = {
                    "Authentication": self.elements.REPORT_AN_ISSUE_PROBLEM_DROPDOWN_AUTHENTICATION,
                    "User Interface": self.elements.REPORT_AN_ISSUE_PROBLEM_DROPDOWN_USER_INTERFACE,
                    "Network Connectivity": self.elements.REPORT_AN_ISSUE_PROBLEM_DROPDOWN_NETWORK_CONNECTIVITY,
                    "Private Access": self.elements.REPORT_AN_ISSUE_PROBLEM_DROPDOWN_PRIVATE_ACCESS,
                    "Others": self.elements.REPORT_AN_ISSUE_PROBLEM_DROPDOWN_OTHERS,
                }
                elements_mapping_for_priotity_type = {
                    "Urgent": self.elements.REPORT_AN_ISSUE_PRIORITY_DROPDOWN_URGENT,
                    "High": self.elements.REPORT_AN_ISSUE_PRIORITY_DROPDOWN_HIGH,
                    "Normal": self.elements.REPORT_AN_ISSUE_PRIORITY_DROPDOWN_NORMAL,
                    "Low": self.elements.REPORT_AN_ISSUE_PRIORITY_DROPDOWN_LOW,
                }
                if use_zcc_tray:
                    zcc_in_focus = self.bring_zcc_to_focus()
                    if not zcc_in_focus[0]:
                        raise Exception(zcc_in_focus[2])
                    
                    # Click on App More
                    self.ui.click(self.elements.APP_MORE, sleep_time=1*self.sleep_factor, AEFC="Unable to click More button on ZCC")
                    # Click on Report An Issue button
                    self.ui.click(self.elements.REPORT_AN_ISSUE_BUTTON, sleep_time=1 * self.sleep_factor,AEFC="Unable to click report an issue button on ZCC")

                    if cancel_report:
                        pyautogui.press('esc')
                        return (True, f"Success :: Cancelled Report an Issue", None)
                else:
                    # Click on Zcc Tray Icon
                    self.ui.click(self.elements.ZCC_TRAY_ICON[1], sleep_time=1*self.sleep_factor, AEFC="Unable to click taskbar")
                    # Click on report an Issue Menu Bar Button
                    self.ui.click(self.elements.ZCC_REPORT_AN_ISSUE_MENUBAR_BUTTON, sleep_time=3*self.sleep_factor, AEFC="Unable to click Report An Issue Button")
                    if cancel_report:
                        pyautogui.press('esc')
                        return (True, f"Success :: Cancelled Report an Issue", None)
                # Type the issue name
                self.ui.type(self.elements.REPORT_AN_ISSUE_NAME_BOX[1], text=issue_name,sleep_time=1 * self.sleep_factor, AEFC="Unable to enter name in report an issue")
                # Type the cc emails
                if not email_cc == None:
                    email_string = ",".join(email_cc)
                    self.ui.type(self.elements.REPORT_AN_ISSUE_EMAIL_BOX, text=email_string,sleep_time=1 * self.sleep_factor, AEFC="Unable to enter CC emails in report an issue")
                # Choose the type of problem from dropdown if problem is not Default
                if not problem == "Default" and problem in ["Authentication", "User Interface", "Network Connectivity", "Private Access", "Others"]:
                    self.ui.click(self.elements.REPORT_AN_ISSUE_PROBLEM_DROPDOWN, sleep_time=1*self.sleep_factor, AEFC="Unable to click type of problem drop down in report an issue window")
                    type_of_problem_element = elements_mapping_for_problem_type[problem]
                    self.ui.click(type_of_problem_element, sleep_time=1*self.sleep_factor, AEFC=f"Unable to click {problem} type from problem drop down in report an issue window")
                # Choose the type of priority from dropdown if priority is not Default
                if not priority == "Default" and priority in ["Urgent", "High", "Normal", "Low"]:
                    self.ui.click(self.elements.REPORT_AN_ISSUE_PRIORITY_DROPDOWN, sleep_time=1*self.sleep_factor, AEFC="Unable to click type of priority drop down in report an issue window")
                    type_of_priority_element = elements_mapping_for_priotity_type[priority]
                    print(f"\n\n\n\n {type_of_priority_element} \n\n\n\n")
                    self.ui.click(type_of_priority_element, sleep_time=1*self.sleep_factor, AEFC=f"Unable to click {priority} type from priority drop down in report an issue window")
                # Enter comments
                self.ui.type(self.elements.REPORT_AN_ISSUE_COMMENTS, text=comments,sleep_time=1 * self.sleep_factor, AEFC="Unable to enter comments in report an issue")
                start_time_for_log_search = datetime.now()
                self.ui.click(self.elements.REPORT_AN_ISSUE_SEND_BUTTON, sleep_time=0,AEFC="Unable to send report from Report an Issue on ZCC")
            except Exception as e:
                self.logger.error(f"Report an Issue Failed :: {e}")
                raise Exception(f"Error :: Report an Issue Failed :: {e}")
            
            is_report_issue_validated = False
            if Version(self.zcc_build) < Version("4.3.0.0"):
                logs_to_be_searched = "executeMobileSupportPostAPI:"
                error_log = "executeMobileSupportPostAPI: Exception:"
            else:
                logs_to_be_searched = "reportIssueV3: Success:"
                error_log = "reportIssueV3: Exception"
            try:
                self.log_ops.search_log_file(file="ZSATray",words_to_find_in_line=[logs_to_be_searched],start_timestamp=start_time_for_log_search,search_mode=None,wait_time=sleep_time)
                is_report_issue_validated=True
            except Exception as e:
                is_report_issue_validated=False

            if is_report_issue_validated:
                try:
                    self.log_ops.search_log_file(file="ZSATray",words_to_find_in_line=[error_log],start_timestamp=start_time_for_log_search,search_mode=None)
                except Exception as e:
                    error = f"executeMobileSupportPostAPI: Exception :: {self.log_ops.recent_log_line}"
                    self.logger.info(f"Report an issue :: {error_log} log not found, all good")
                    self.logger.info(f"Report an Issue log line found :: {self.log_ops.recent_log_line}")
                else:
                    error = f"executeMobileSupportPostAPI: Exception :: {self.log_ops.recent_log_line}"
                    self.logger.error(f"Report an issue Error :: {error} log found, report an issue failed")
                    raise Exception(f"reportIssue Error log {error_log} found, report an issue failed")
                self.logger.info("Success :: Report An Issue!")
            else:
                raise Exception(f"Report Issue not validated from logs, Log line to be searched: {logs_to_be_searched}")
        except Exception as e:
            return False,f"Error ::  {e}",None
        return True,f"Success :: Report an Issue  Successfully ",None
    
    @allure.step("Revert ZCC")
    def revert_zcc(self,  
                   password: str =None,             # password to revert zcc
                   failure_expected: bool=False,    # to verify what happens when wrong password is given
                   sleep_time: int=15,              # number of seconds to sleep after execution
                   expected_version : str=None,     # What zcc version is expected after revert
                   ):            
        """
            This function is used to revert ZCC on ZCC win/mac.
            
            :param password: str, password to revert zcc, defaults to None
            :param failure_expected: bool, to verify what happens when wrong password is given, defaults to False
            :param sleep_time: int, number of seconds to sleep after execution, defaults to 30
            :return: tuple, (bool, str) indicating success or failure and a message
        """
        try:   
            try:
                zcc_in_focus = self.bring_zcc_to_focus()
                if not zcc_in_focus[0]:
                    raise Exception(zcc_in_focus[1])
                
                # Get required variables
                zcc_version_before_revert = self.sys_ops.fetch_application_version()[2]
                zcc_version_after_revert = None
                # Perform UI actions
                self.ui.click(self.elements.APP_MORE, sleep_time=1*self.sleep_factor,AEFC="Unable to click More button on ZCC")
                self.ui.click(self.elements.REVERT_ZCC, sleep_time=1*self.sleep_factor, AEFC="Unable to click Revert ZCC button")
                if password:
                    self.ui.type(self.elements.REVERT_ZCC_PASSWORD_BOX, text=password, sleep_time=1 * self.sleep_factor,AEFC="Unable to enter revert password")
                    self.ui.click(self.elements.REVERT_ZCC_PASSWORD_ACCEPT_BUTTON, sleep_time=1 * self.sleep_factor,AEFC="Unable to click Revert ZCC Password OK button")
                    if failure_expected:
                        self.ui.click(self.elements.REVERT_ZCC_PASSWORD_CANCEL_BUTTON, sleep_time=1 * self.sleep_factor,AEFC="Unable to click Revert ZCC Password Cancel button")
                else:
                    self.ui.click(self.elements.REVERT_ZCC_PASSWORD_ACCEPT_BUTTON, sleep_time=1 * self.sleep_factor,AEFC="Unable to click Revert ZCC Password OK button")
                time.sleep(60)
                start_time = int(time.time())
            except Exception as e:
                self.logger.error(f"Revert ZCC failed :: {e}")
                raise Exception(f"Error :: Revert ZCC Failed :: {e}")
            else:
                while int(time.time() - start_time) <= (70 if failure_expected else 90) and (zcc_version_before_revert==zcc_version_after_revert or zcc_version_after_revert==None):
                    print(f"Waiting for app to revert, Wait time : 90 seconds max, Time elapsed {int(time.time() - start_time)} seconds",end='\r')
                    zcc_version_after_revert = self.sys_ops.fetch_application_version()[2]

                zcc_version_after_revert = self.sys_ops.fetch_application_version()[2]

                if zcc_version_after_revert == zcc_version_before_revert:
                    if failure_expected:
                        self.logger.info(f"Success :: ZCC Revert failed as expected\nZCC version before revert: {zcc_version_before_revert}\nZCC version after revert: {zcc_version_after_revert}")
                        return True,f"Success :: ZCC Revert failed as expected\nZCC version before revert: {zcc_version_before_revert}\nZCC version after revert: {zcc_version_after_revert}",None
                    else:
                        self.logger.error(f"Failure :: ZCC Revert failed, it was not expected\nZCC version before revert: {zcc_version_before_revert}\nZCC version after revert: {zcc_version_after_revert}")
                        return False,f"Failure :: ZCC Revert failed, it was not expected\nZCC version before revert: {zcc_version_before_revert}\nZCC version after revert: {zcc_version_after_revert}",None
                elif Version(zcc_version_after_revert) < Version(zcc_version_before_revert):
                    if expected_version:
                        if zcc_version_after_revert == expected_version:
                            self.logger.info(f"Success :: ZCC Reverted as expected\nZCC version before revert: {zcc_version_before_revert}\nZCC version after revert: {zcc_version_after_revert}\nExpected ZCC version: {expected_version}")
                            return (True,f"Success :: ZCC Reverted as expected\nZCC version before revert: {zcc_version_before_revert}\nZCC version after revert: {zcc_version_after_revert}\nExpected ZCC version: {expected_version}",None)
                        else:
                            self.logger.error(f"Failure :: ZCC did not revert as expected\nZCC version before revert: {zcc_version_before_revert}\nZCC version after revert: {zcc_version_after_revert}\nExpected ZCC version: {expected_version}")
                            return False,f"Failure :: ZCC did not revert as expected\nZCC version before revert: {zcc_version_before_revert}\nZCC version after revert: {zcc_version_after_revert}\nExpected ZCC version: {expected_version}",None
                    else:
                        self.logger.info(f"Success :: ZCC Revert successful\nZCC version before revert: {zcc_version_before_revert}\nZCC version after revert: {zcc_version_after_revert}")
                elif Version(zcc_version_after_revert) > Version(zcc_version_before_revert):
                    self.logger.error(f"Failure :: ZCC revert did not go as expected, ZCC version is higher after revert\nZCC version before revert: {zcc_version_before_revert}\nZCC version after revert: {zcc_version_after_revert}\nExpected ZCC version: {expected_version}")
                    return False,f"Failure :: ZCC revert did not go as expected, ZCC version is higher after revert\nZCC version before revert: {zcc_version_before_revert}\nZCC version after revert: {zcc_version_after_revert}\nExpected ZCC version: {expected_version}",None
                self.logger.debug(f"Sleeping for {sleep_time} to let ZCC load")
                time.sleep(sleep_time)
                self.bring_zcc_to_focus()
                return True,f"Success :: ZCC Revert successful\nZCC version before revert: {zcc_version_before_revert}\nZCC version after revert: {zcc_version_after_revert}",None
        except Exception as e:
            return False,f"Error during reverting ZCC :: {e}",None

    @allure.step("Toggle packet capture")
    def toggle_packet_capture(
        self,
        mode: str = "start",    # accepts "start" or "stop" as value
    ):
        """
        Toggle packet capture on ZCC.

        :param mode: str, optional
            Accepts "start" or "stop" as value, defaults to "start".
        :return: tuple
            Returns a tuple containing a boolean and a string.
        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(self.elements.APP_MORE, sleep_time=1, AEFC="Unable to click More button on ZCC")
            if mode == 'start':
                self.ui.click(self.elements.START_PACKET_CAPTURE, sleep_time=1,AEFC="Unable to click start packet capture  button on ZCC")
            elif mode == 'stop':
                self.ui.click(self.elements.STOP_PACKET_CAPTURE, sleep_time=1,AEFC="Unable to click stop packet capture button on ZCC")
            else:
                raise Exception(f"Error :: Invalid mode given {mode}, please give start or stop")
        except Exception as e:
            self.logger.error(f"{mode} packet capture failed :: {e}")
            return False,f"Error :: {mode} packet capture failed :: {e}",None
        self.logger.info(f"Success :: {mode} packet capture click done !")
        return True,f"Success :: {mode} packet capture click done !",None

    @allure.step("Validate Packet Capture State")
    def validate_packet_capture_state(
        self,
        mode='start',       # accepts "start" or "stop" or "capturing" as value
    ):
        """
        This function validates the packet capture state.

        :param mode: The mode to start or stop packet capture. Default is 'start'.
        :type mode: str
        :return: A tuple containing a boolean status and a string message.
        :rtype: tuple
        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(self.elements.APP_MORE, sleep_time=1, AEFC="Unable to click More button on ZCC")
            if mode == 'start': 
                self.ui.check_if_exists(self.elements.START_PACKET_CAPTURE)
            elif mode == 'stop': 
                self.ui.check_if_exists(self.elements.STOP_PACKET_CAPTURE)
            elif mode == 'capturing': 
                self.ui.check_if_exists(path=self.elements.CAPTURE_STATUS[1])
            else: 
                raise Exception(f"Error :: Invalid Mode Given {mode}, please give start or stop or capturing")
        except Exception as e:
            self.logger.error(f"{mode} packet capture state validation failed :: {e}")
            return False,f"Error :: {mode} packet capture state validation failed :: {e}",None
        self.logger.info(f"SUCCESS :: {mode} packet capture state validation success")
        return True,f"Success :: {mode} packet capture state validation success",None
    
    @allure.step("Clear Logs")
    def clear_logs(self):
        """
        This function clears the logs in the application.

        :return: A tuple containing a boolean status and a string message.
        :rtype: tuple
        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(self.elements.APP_MORE, sleep_time=0, AEFC="Unable to click More button on ZCC")
            self.ui.click(self.elements.CLEAR_LOGS,sleep_time=0, AEFC="Unable to click Clear Logs button on ZCC")
        except Exception as e: 
            self.logger.error(f"Clear logs failed, Error: {e}".format(e))
            return False,f"Clear logs failed, Error: {e}",None
        return True,f"Success, Clear logs done!",None

    def clear_zdx_data(self):
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(self.elements.DIGITAL_EXPERIENCE_TAB,sleep_time=0,AEFC="Unable to click ZDX Tab")
            self.ui.click(self.elements.DIGITAL_EXPERIENCE_CLEAR_DATA,sleep_time=0,AEFC="Unable to click Clear Data button ")
            self.ui.click(self.elements.DIGITAL_EXPERIENCE_ACCEPT,sleep_time=0,AEFC="Unable to click ZDX Clear Accept Button")
            
        except Exception as e: 
            self.logger.error(f"CLEAR ZDX DATA FAILED {e}".format(e))
            return (False,f"Error :: CLEAR ZDX DATA FAILED :: {e}",None)
        return (True,f"Success :: CLEAR ZDX DATA done!",None)
    
    def click_zcc_back(self):
        """
            This function clicks the back button in the ZCC application.

            :return: A tuple containing a boolean status and a string message.
            :rtype: tuple
        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(self.elements.ZCC_BACK_BUTTON, sleep_time=0, AEFC="Unable to click More button on ZCC")
        except Exception as e:
            self.logger.error(f"Click Back Button Failed :: {e}")
            return (False,f"Error :: Click Back Button Failed :: {e}",None)
        return (True,f"Success :: ZCC Back Button Click done!",None)
    
    @allure.step("FInd private access tab")
    def find_private_access_tab(self,
                            should_be_present: bool):    # accepts True if tab is expected else False
        """
            Finds the private access tab.

            :param should_be_present: Accepts True if tab is expected else False
            :type should_be_present: bool
            :return: A tuple containing a bool indicating success or failure and a message
            :rtype: tuple
        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(self.elements.PRIVATE_ACCESS_TAB, sleep_time=0,retry_frequency=1, max_threshold=2,AEFC="Unable to open Private Access tab",expect_click_failure=not should_be_present)
        except Exception as e:
            return (False,f"Error :: Failed to click Private Access Tab :: {e}",None)
        return (True,"Success :: Private Access Tab Found ",None)

    @allure.step("Find Internet Access Tab")
    def find_internet_access_tab(self, 
                            should_be_present: bool):    # accepts True if tab is expected else False
        """
            Finds the Internet Access tab.

            :param should_be_present: Accepts True if tab is expected else False
            :type should_be_present: bool
            :return: A tuple containing a bool indicating success or failure and a message
            :rtype: tuple
        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(self.elements.INTERNET_SECURITY_TAB, sleep_time=0,retry_frequency=1,max_threshold=2,AEFC="Unable to open Internet Access tab",expect_click_failure=not should_be_present)
        except Exception as e:
            return (False,f"Error :: Failed to click Internet Access Tab :: {e}",None)
        return (True,"Success :: Internet Access Tab Found ",None)
    
    @allure.step("Find Digital Experience Tab")
    def find_digital_experience_tab(self,
                                should_be_present: bool):    # accepts True if tab is expected else False
        """
            Finds the Digital Experience tab.

            :param should_be_present: Accepts True if tab is expected else False
            :type should_be_present: bool
            :return: A tuple containing a bool indicating success or failure and a message
            :rtype: tuple
        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(self.elements.DIGITAL_EXPERIENCE_TAB, sleep_time=2,retry_frequency=1,max_threshold=2,AEFC="Unable to open Digital Experience tab",expect_click_failure=not should_be_present)
        except Exception as e:
            return (False,f"Error :: Failed to click Digital Experience Tab :: {e}",None)
        return (True,"Success :: Digital Experience Tab Found ",None)
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Find Data Protection Tab")
    def find_data_protection_tab(self,
                                should_be_present: bool):    # accepts True if tab is expected else False
        """
            Finds the Data Protection tab.

            :param should_be_present: Accepts True if tab is expected else False
            :type should_be_present: bool
            :return: A tuple containing a bool indicating success or failure and a message
            :rtype: tuple
        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            self.ui.click(self.elements.DATA_PREVENTION_TAB, sleep_time=2,retry_frequency=1,max_threshold=2,AEFC="Unable to open Data Prevention tab",expect_click_failure=not should_be_present)
        except Exception as e:
            return (False,f"Error :: Failed to click Data Protection Tab :: {e}", None)
        return (True,"Success :: Data Protection Tab Found ", None)
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("Check Service Connected")
    def check_service_connected(self, 
                            service: str ):   # defines service - ZIA/ZPA
        """
            Checks if a service is connected.

            Args:
                service (str): The service to check, either "ZPA" or "ZIA".

            Returns:
                tuple: A tuple containing a boolean indicating success or failure,
                    a message, and an exception if applicable.
        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            if service == "ZPA":
                self.ui.click(self.elements.PRIVATE_ACCESS_TAB, sleep_time=2, AEFC="Unable to open Private Access tab")
                try:
                    self.ui.check_if_exists(self.elements.PRIVATE_ACCESS_POWER_BUTTON_ON)
                except:
                    time.sleep(30)
                    self.ui.check_if_exists(self.elements.PRIVATE_ACCESS_POWER_BUTTON_ON)
            if service == "ZIA":
                self.ui.click(self.elements.INTERNET_SECURITY_TAB, sleep_time=2, AEFC="Unable to open Internet Security tab")
                try:
                    self.ui.check_if_exists(self.elements.INTERNET_SECURITY_POWER_BUTTON_ON)
                except:
                    time.sleep(30)
                    self.ui.check_if_exists(self.elements.INTERNET_SECURITY_POWER_BUTTON_ON)
            if service == "ZDX":
                self.ui.click(self.elements.DIGITAL_EXPERIENCE_TAB, sleep_time=2, AEFC="Unable to open Digital Experience tab")
                try:
                    self.ui.check_if_exists(self.elements.DIGITAL_POWER_BUTTON_ON)
                except:
                    time.sleep(30)
                    self.ui.check_if_exists(self.elements.DIGITAL_POWER_BUTTON_ON)
        except Exception as e:
            return (False,f"Error :: {service} not connetced :: {e}",None)
        return (True,"Success :: {service} connetced ",None)

    @allure.step("validate zpa reauth state")
    def validate_zpa_reauth_state(self):    #this function checks if zcc is in reauth state or not
        """
            This function checks if ZCC is in reauth state or not.

            :return: A tuple containing a boolean value and a string message.
        """        
        zcc_in_focus = self.bring_zcc_to_focus()
        if not zcc_in_focus[0]:
            raise Exception(zcc_in_focus[2])
        self.ui.click(self.elements.PRIVATE_ACCESS_TAB, sleep_time=1, AEFC="Unable to open Private Access tab")
        try:
            try:
                self.ui.click(self.elements.PRIVATE_ACCESS_REAUTH_BUTTON,max_threshold=3,sleep_time=1, AEFC="Unable to select Private Access button on mac ZCC",do_click=False,dynamic_wait_search_element=10)
            except:
                try:
                    self.ui.click(self.elements.PRIVATE_ACCESS_REAUTH_BUTTON_2,max_threshold=3,sleep_time=1, AEFC="Unable to select Private Access button on ZCC - tried 2 different element id's",do_click=False,dynamic_wait_search_element=10)
                except Exception as e:
                    self.logger.error(e)
                    raise Exception(e)
            self.logger.info("ZPA in reauth required state")
            return (True,"Success :: ZPA in reauth required state",None)
        except:
            self.logger.error("ZPA Reauth button not found")
            return (False,"Error :: ZPA Reauth button not found",None)

    @allure.step("Do ZPA Reauth ")
    def zpa_reauth(self,
                   zpa_username: str ,          # ZPA user for reauth
                   zpa_password: str ,          # ZPA user password for reauth
                   sleep_time: int =10,         # number of seconds to sleep after execution
                   partial_reauth: bool =False, # defines whether do to partial reauth
                   early_auth: bool =False,     # defines whether to do early reauth
                   use_login_hint: bool=False,  # Indicates if login_hint is being used
                   browser_based_auth: bool=False,  # Indicates if browser based auth is enabled or not 
                   chrome_browser: bool=False,   # Indicates if chrome browser is used for auth
                   )->tuple[bool,str,str]:    
        """
            This function performs a ZPA reauthentication.
            
            Args:
                zpa_username (str): ZPA user for reauth.
                zpa_password (str): ZPA user password for reauth.
                sleep_time (int, optional): Number of seconds to sleep after execution. Defaults to 10.
                partial_reauth (bool, optional): Defines whether to do partial reauth. Defaults to False.
                early_auth (bool, optional): Defines whether to do early reauth. Defaults to False.
                browser_based_auth(bool): A flag indicating if browser_based_auth is enabled or not.
                chrome_browser (bool): Indicates if chrome browser is used.
                use_login_hint (bool): Indicates if login_hint is enabled
            Returns:
                tuple: A tuple containing a boolean and a string indicating the success or failure of the operation.
        """        
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            # self.ui.click(self.elements.ZSA_TRAY_FORM, max_threshold=20, sleep_time=1,AEFC="Unable to click on ZCC Tray Form")
            self.ui.click(self.elements.PRIVATE_ACCESS_TAB, sleep_time=1, AEFC="Unable to open Private Access tab")
            try:
                start_time_for_log_search = datetime.now()
                self.ui.click(self.elements.PRIVATE_ACCESS_REAUTH_BUTTON if early_auth==False else self.elements.PRIVATE_ACCESS_EARLY_REAUTH_BUTTON,max_threshold=3,sleep_time=0, AEFC="Unable to select Private Access button on ZCC")
            except Exception as e:
                if (self.operating_system=="Mac" and early_auth==True):
                    self.logger.error(e)
                    raise Exception(e)
                else:
                    try:
                        self.ui.click(self.elements.PRIVATE_ACCESS_REAUTH_BUTTON_2,max_threshold=3,sleep_time=1, AEFC="Unable to select Private Access button on ZCC - tried 2 different element id's")
                    except Exception as e:
                        self.logger.error(e)
                        raise Exception(e)
            if browser_based_auth:
                self.browser_ops.perform_browser_based_auth(username=zpa_username,password=zpa_password,chrome=chrome_browser,use_login_hint=use_login_hint)
            else:
                 self.do_okta_login(zpa_username,zpa_password,search_log_time=start_time_for_log_search,partial_login=partial_reauth,use_login_hint=use_login_hint,zpa_reauth=True)   
        except Exception as e:
            self.logger.error(f"ZPA Reauth Failed :: {e}")
            return (False,f"Error :: ZPA Reauth Failed :: {e}",None)
        self.logger.info("Success :: ZPA Reauth Done!")
        return (True,"Success :: ZPA Reauth Done!",None)

    @allure.step("Get ZPA traffic data from ZCC")
    def get_zpa_traffic_data(self)->tuple[bool, str, tuple]:  # Function to fetch sent bytes data from ZCC UI
        """
            Function to fetch sent bytes data from ZCC UI.

            Returns:
                tuple: A tuple containing a boolean status, a message, and sent/received bytes data.
        """
        try:
            time.sleep(5)
            zcc_ui_data = self.get_zcc_ui_values_mac()
            if not zcc_ui_data[0]:
                raise Exception(zcc_ui_data[2])
            zcc_ui_data = zcc_ui_data[2]
            sent_bytes = zcc_ui_data['zpnTotalBytesSent']
            received_bytes = zcc_ui_data['zpnTotalBytesReceived']
        except Exception as e:
            return False,f"Error :: Unable to get ZPA Traffic:: {e}",None
        return True,"Success :: ZPA Traffic Captured!",(sent_bytes, received_bytes)

    @allure.step("Validate ZPA Tunnel Protocol")
    def validate_zpn_tunnel_protocol(
        self,
        required_tunnel_version: str          # accepted values :: TLS, DTLS, NONE
    ):
        """
            Validate ZPN tunnel protocol.

            This function validates the ZPN tunnel protocol based on the required tunnel version.
            It accepts values like TLS, DTLS, NONE.

            Args:
                required_tunnel_version (str): The required tunnel version.

            Returns:
                tuple: A tuple containing a boolean status and a message.
        """     
        try:
            zcc_ui_data = self.get_zcc_ui_values_mac()
            if not zcc_ui_data[0]:
                raise Exception(zcc_ui_data[2])
            zcc_ui_data = zcc_ui_data[2]
            if required_tunnel_version.upper()=="TLS":
                if str(zcc_ui_data["zpn"])=="4":
                    if str(zcc_ui_data["zpnTunProto"])=="TLS":self.logger.info(f"ZPA ON TLS Validated on UI, zpn value -> {str(zcc_ui_data['zpn'])}")
                    else:raise Exception("ZPA not on TLS : UI")
                else:raise Exception("ZPA not on TLS : UI")
            elif required_tunnel_version.upper()=="DTLS":
                if str(zcc_ui_data["zpn"])=="4":
                    if str(zcc_ui_data["zpnTunProto"])=="DTLS":self.logger.info(f"ZPA ON DTLS Validated on UI, zpn value -> {str(zcc_ui_data['zpn'])}")
                    else:raise Exception("ZPA not on DTLS : UI")
                else:raise Exception("ZPA not on DTLS : UI")
            elif required_tunnel_version.upper()=="NONE":
                if str(zcc_ui_data["zpn"])=="3":
                    self.logger.info(f"ZPA ON NONE Validated on UI, zpn value -> {str(zcc_ui_data['zpn'])}")
                else:raise Exception("ZPA not on NONE : UI")
            else:
                raise Exception(f"Invalid tunel version given -> {required_tunnel_version}, please give TLS DTLS or NONE")
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,"Success :: ZPA Protocol Validated Successfully",None)

    @calculate_func_execute_time
    @allure.step("Add ZPA Partner tenant")
    def add_partner_tenant(self, 
        user_id: str = None,                            # user id for partner tenant
        password: str = None,                           # password for partner tenant
        main_tenant_disable_zpa_password: str = None,   # Add a string here to send disable password if password for disabling ZPA is set for main tenant
        main_tenant_disable_zpa_reason: str = None,     # Add a string here to send disable reason if reason for disabling ZPA is set for main tenant
        failure_expected: bool = False,                 # Expect failures
        expect_error_popups: bool = False,              # Expect error popups if failure occures during any step
        at_partner_tenant_page: bool = False,           # VVIP: The element ID for add partner button at both main tenant page and partner tenant page is different, bug in ZCC, main tenant add partner button exists even though partner tenant has been added. Which causes appium to detect and click on the wrong add partner button when a partner has already been added
        ):
        """
        Target: To Add Partner Tenant to ZCC
        Work Flow
        ----------
            * The function first opens a driver using the start_driver method of the GUI class. It then brings the ZCC to focus using the bring_zcc_to_focus method. Next, it clicks the "Private Access Tab" button and then the "Add Partner" button. It then types the user_id into a box and clicks the "Confirm" button.
            * After a 20-second delay, the function clicks the "Okta Username" button and types the user_id into the username box. It then types the Password into the password box and clicks the "Sign In" button. If the operating system is macOS, the function presses the "Enter" key to sign in.
            * If the main_tenant_disable_zpa_password and main_tenant_disable_zpa_reason parameters are provided, the function types the password and reason into their respective boxes and clicks the "Disable ZPA Main Tenant ok" button. Finally, the function brings the ZCC to focus again and kills the driver using the kill_driver method of the GUI class.
        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            
            if failure_expected:
                wait_time = 20
            else:
                wait_time = 40
            self.ui.click(self.elements.PRIVATE_ACCESS_TAB, sleep_time=1*self.sleep_factor, AEFC="Unable to click Private Access Tab button on ZCC", dynamic_wait_search_element=wait_time)

            # The access id for add partner when only main tenant is present and when a partner tenant is added is different so search what element is present dynamically and click on the available one
            if at_partner_tenant_page==False:
                add_partner_tenant_button = self.elements.ADD_PARTNER_TENANT_BUTTON
            else:
                add_partner_tenant_button = self.elements.ADD_PARTNER_TENANT_AT_ZPA_PARTNER_BUTTON

            self.ui.click(add_partner_tenant_button, sleep_time=0, AEFC="Unable to click Add Partner button", dynamic_wait_search_element=wait_time)
            self.ui.type(self.elements.PARTNER_TENANT_USER_ID_BOX, text=user_id,sleep_time=1, AEFC="Unable to enter User ID in Partner tenant user box", dynamic_wait_search_element=wait_time)
            self.ui.click(self.elements.PARTNER_TENANT_USER_ID_CONFIRM, sleep_time=10, AEFC="Unable to click Confirm button", dynamic_wait_search_element=wait_time)

            # okta page
            self.handle_zpa_login(user_id,password=password,time_for_log_search=self.start_time_for_log_search,cancel_zpa_login=False)
            time.sleep(20)

            if main_tenant_disable_zpa_password and main_tenant_disable_zpa_reason:
                self.ui.type(self.elements.MAIN_TENANT_POWER_PASSWORD_BOX, text=main_tenant_disable_zpa_password, sleep_time=1*self.sleep_factor, AEFC="Unable to enter password")
                self.ui.type(self.elements.MAIN_TENANT_POWER_PASSWORD_DISABLE_REASON_BOX, text= main_tenant_disable_zpa_reason, sleep_time=1*self.sleep_factor, AEFC="Unable to enter disable reason")
                self.ui.click(self.elements.MAIN_TENANT_POWER_DISABLE_PASSWORD_OK_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable ZPA Main Tenant ok button")
            elif main_tenant_disable_zpa_password: 
                self.ui.type(self.elements.MAIN_TENANT_POWER_PASSWORD_BOX, text=main_tenant_disable_zpa_password, sleep_time=1*self.sleep_factor, AEFC="Unable to enter password")
                self.ui.click(self.elements.MAIN_TENANT_POWER_DISABLE_PASSWORD_OK_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable ZPA Main Tenant ok button")
            elif main_tenant_disable_zpa_reason: 
                self.ui.type(self.elements.MAIN_TENANT_POWER_DISABLE_REASON_BOX, text= main_tenant_disable_zpa_reason, sleep_time=1*self.sleep_factor, AEFC="Unable to enter disable reason")
                self.ui.click(self.elements.MAIN_TENANT_POWER_DISABLE_CONFIRM, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable ZPA Main Tenant ok button")
                
        except Exception as e: 

            # Note: In some cases, the exception block is not reached even though failure is expected, in that case, call the click_error_popup_ok_button explicitly, 
            # Example: Adding the main tenant as partner tenant, the error message is popped up after okta login, because of which, the except block will never be reached
            if failure_expected:
                failure_expected_validation_status = True
                failure_expected_message = f"Partner Addition Failed :: {e}"

                if expect_error_popups:

                    result = self.click_error_popup_ok_button()
                    if result[0]:
                        failure_expected_validation_status = True
                        failure_expected_message = f"Partner Addition Failed as expected, {result[1]}"
                    else:
                        failure_expected_validation_status = result[0]
                        failure_expected_message = result[1]+" ,Popup ok button was expected but not found"
                # Handle the case where cancel button exists after entering wrong creds in username field in ZCC and not Okta
                self.logger.info("sleeping for 10 seconds")
                time.sleep(10)
                if self.ui.check_if_exists(path=self.elements.PARTNER_TENANT_USER_ID_CANCEL, dynamic_wait_search_element=wait_time):
                    try:
                        self.ui.click(path=self.elements.PARTNER_TENANT_USER_ID_CANCEL, sleep_time=1*self.sleep_factor, AEFC="Unable to click on CANCEL button at add partner tenant page", dynamic_wait_search_element=10)
                    except Exception as e:
                        failure_expected_validation_status = False
                        failure_expected_message = f"CANCEL button not found, {e}"
                    else:
                        failure_expected_validation_status = True
                        failure_expected_message = f"Partner Addition Failed as expected"


                self.logger.error(failure_expected_message)
                return failure_expected_validation_status, failure_expected_message, None
            
            self.logger.error(f"Partner Addition Failed :: {e}")
            return (False,f"Error :: Partner Addition Failed :: {e}",None)

        self.logger.info("Success :: Partner login success, sleeping for 10 seconds to let the tunnel estabilish")
        time.sleep(10)
        return (True,f"Success :: Partner login success!",None)

    @calculate_func_execute_time
    @allure.step("Edit ZPA Partner tenant")
    def edit_zpa_partner(self,
        main: bool = True,                                  # set this to True for main tenant related actions
        partner: bool = False,                              # set this to True for partner tenant related actions
        action: bool = None,                                # True for disable->enable, False for enable->disable
        main_tenant_disable_zpa_password: str = None,       # send password if it is expected
        main_tenant_disable_zpa_reason: str = None,         # send disable reason if it is expected
        partner_tenant_row_id:int=0,                        # If multiple partners are added, what row to click on
        remove: bool = False,                                # remove partner tenant if set to True
        failure_expected: bool = False                      # expect GUI failure if set to True, for negative scenario testing
        ):
        """
        Target: To Perform actions related to Main and Partner Tenant via GUI
        Work Flow
        ----------
            * The function first starts a GUI driver and then tries to bring the ZCC (ZCC Client Connector) to focus. It then clicks on the "Private Access" tab and, depending on the value of the action parameter, either enables or disables the ZPA main and partner tenants.
            * If the main_tenant_disable_zpa_password and main_tenant_disable_zpa_reason parameters are provided, the function enters the password and disable reason into the corresponding input fields and clicks the "Disable ZPA Main Tenant" button.
            * If the remove parameter is set to True, the function removes the partner tenant by clicking on the "Remove Partner Tenant" button and then confirming the removal.
            * Finally, the function kills the GUI driver and logs a success message if everything went well.
        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            
            self.ui.click(self.elements.PRIVATE_ACCESS_TAB, sleep_time=1*self.sleep_factor, AEFC="Unable to click Private Access Tab button on ZCC")
            if remove == False:
                if action==True: 
                    if main: 
                        self.ui.click(self.elements.MAIN_TENANT_POWER_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click Enable ZPA Main Tenant button")
                    if partner:
                        partner_tenant_power_button = self.elements.PARTNER_TENANT_POWER_BUTTON[0].format(partner_tenant_row_id)
                        self.ui.click(accessibility_id=partner_tenant_power_button, sleep_time=1*self.sleep_factor, AEFC="Unable to click Enable ZPA Partner Tenant button")
                if action==False: 
                    if main: 
                        self.ui.click(self.elements.MAIN_TENANT_POWER_BUTTON, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable ZPA Main Tenant button")
                    if partner:
                        partner_tenant_power_button = self.elements.PARTNER_TENANT_POWER_BUTTON[0].format(partner_tenant_row_id)
                        self.ui.click(accessibility_id=partner_tenant_power_button, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable ZPA Partner Tenant button")
                
                # Check for disable password and disable reason on main tenant
                if main_tenant_disable_zpa_password and  main_tenant_disable_zpa_reason:
                    self.ui.type(self.elements.MAIN_TENANT_POWER_PASSWORD_BOX, text=main_tenant_disable_zpa_password, sleep_time=1*self.sleep_factor, AEFC="Unable to enter password")
                    self.ui.type(self.elements.MAIN_TENANT_POWER_PASSWORD_DISABLE_REASON_BOX, text= main_tenant_disable_zpa_reason, sleep_time=1*self.sleep_factor, AEFC="Unable to enter disable reason")
                elif main_tenant_disable_zpa_password: 
                    self.ui.type(self.elements.MAIN_TENANT_POWER_PASSWORD_BOX, text=main_tenant_disable_zpa_password, sleep_time=1*self.sleep_factor, AEFC="Unable to enter password")
                elif  main_tenant_disable_zpa_reason: 
                    self.ui.type(self.elements.MAIN_TENANT_POWER_DISABLE_REASON_BOX, text=main_tenant_disable_zpa_reason, sleep_time=1*self.sleep_factor, AEFC="Unable to enter disable reason")
                
                # ok button
                if main: 
                    if action==False:
                        self.ui.click(self.elements.MAIN_TENANT_POWER_DISABLE_CONFIRM, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable ZPA Main Tenant ok button")
                if partner: 
                    if main_tenant_disable_zpa_password or main_tenant_disable_zpa_reason:
                        self.ui.click(self.elements.MAIN_TENANT_POWER_DISABLE_CONFIRM, sleep_time=1*self.sleep_factor, AEFC="Unable to click Disable ZPA Main Tenant ok button")
            else:
                if partner:
                    remove_partner_tenant_button = self.elements.REMOVE_PARTNER_TENANT[0].format(partner_tenant_row_id)
                    self.ui.click(accessibility_id=remove_partner_tenant_button, sleep_time=1*self.sleep_factor, AEFC="Unable to click Remove Partner Tenant button")
                    self.ui.click(self.elements.REMOVE_PARTNER_TENANT_CONFIRM, sleep_time=1*self.sleep_factor, AEFC="Unable to click Remove Partner Tenant Confirm button")
                    try:
                        self.log_ops.search_log_file(file = "ZSATray", search_mode=None, words_to_find_in_line= ["removePartnerLogin for user"], start_line=self.log_ops.recent_log_line)
                        return True, f"Partner Tenant removed successfully", None
                    except Exception as e:
                        time.sleep(2)
                        raise Exception(f"Log for partner removal not found - 'removePartnerLogin for user', Error: {e}")
        except Exception as e:
            if partner:
                message = f"Operation specified on partner tenant failed :: {e}"
            else:
                message = f"Operation specified on main tenant failed :: {e}"

            self.logger.error(message)
            return False, message, None

        if partner:
            message = f"Operation specified on partner tenant success, Action: {action}, Remove: {remove}, Partner row id: {partner_tenant_row_id}"
        else:
            message = f"Operation specified on main tenant failed, Action: {action}, Password: {main_tenant_disable_zpa_password}, Reason: {main_tenant_disable_zpa_reason}"

        self.logger.info(message+" Sleeping for 15 seconds to let connection establish")
        time.sleep(15)
        return True, message, None
    
    @allure.step("validate zia tunnel protocol")
    def validate_zia_tunnel_protocol(
        self,
        required_tunnel_version: str,          # TLS or DTLS or V1 or None
        is_connection_error_expected:bool=False,
    ):  
        """
            Validate the ZIA tunnel protocol based on the required version.

            :param self: Self object
            :param required_tunnel_version: Required tunnel version (TLS, DTLS, V1, or None)
            :return: A tuple with boolean true/false based on fn execution along with error/success message
        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            # self.ui.click(self.elements.ZSA_TRAY_FORM, AEFC="App not Found", max_threshold=20, sleep_time=0)
            self.ui.click(self.elements.INTERNET_SECURITY_TAB, sleep_time=0, AEFC="Unable to open Internet Security tab")
            zcc_ui_data = self.get_zcc_ui_values_mac()
            if not zcc_ui_data[0]:
                raise Exception(zcc_ui_data[2])
            zcc_ui_data = zcc_ui_data[2]
            if required_tunnel_version.upper()=="TLS":
                if str(zcc_ui_data["tunVersion"])in ["20:2",'20:2']:
                    if str(zcc_ui_data["websecurity"])=="4":
                        self.logger.info(f"ZIA ON TLS Validated on UI,: tunVersion -> {zcc_ui_data['tunVersion']} webSecuirty -> {zcc_ui_data['websecurity']}")
                    else:raise Exception(f"ZIA not on TLS : tunVersion -> {zcc_ui_data['tunVersion']} webSecuirty -> {zcc_ui_data['websecurity']}")
                else:raise Exception("ZIA not on TLS : UI")

            elif required_tunnel_version.upper()=="DTLS":
                if str(zcc_ui_data["tunVersion"]) in ["20:1",'20:1']:
                    if str(zcc_ui_data["websecurity"])=="4":
                        self.logger.info(f"ZIA ON DTLS Validated on UI,: tunVersion -> {zcc_ui_data['tunVersion']} webSecuirty -> {zcc_ui_data['websecurity']}")
                    else:raise Exception(f"ZIA not on DTLS : tunVersion -> {zcc_ui_data['tunVersion']} webSecuirty -> {zcc_ui_data['websecurity']}")
                else:raise Exception("ZIA not on DTLS : UI")

            elif required_tunnel_version.upper()=="V1":
                if str(zcc_ui_data["tunVersion"]) in ["10:0",'10:0']:
                    if str(zcc_ui_data["websecurity"])=="4" or str(zcc_ui_data["websecurity"])=="5" or str(zcc_ui_data["websecurity"])=="6":
                        self.logger.info(f"ZIA ON T1 Validated on UI: tunVersion -> {zcc_ui_data['tunVersion']} webSecuirty -> {zcc_ui_data['websecurity']}")
                    else:raise Exception(f"ZIA not on T1 : tunVersion -> {zcc_ui_data['tunVersion']} webSecuirty -> {zcc_ui_data['websecurity']}")
                else:raise Exception("ZIA not on T1 : UI")

            elif required_tunnel_version.upper()=="NONE":
                if str(zcc_ui_data["tunVersion"])in ["",'',None]:
                    if str(zcc_ui_data["websecurity"])=="3":
                        self.logger.info(f"ZIA ON NONE MODE Validated on UI, websecurity value -> {str(zcc_ui_data['websecurity'])}")
                    else:raise Exception(f"ZIA not on None mode : tunVersion -> {zcc_ui_data['tunVersion']} webSecuirty -> {zcc_ui_data['websecurity']}")
                else:raise Exception("ZIA not on None mode : UI")
            else:
                self.logger.error("Invalid required state given, please give DTLS,TLS ,v1 or none")
                raise Exception("Invalid required state given, please give DTLS,TLS ,v1 or none")
        except Exception as e:
            if is_connection_error_expected == False:
                return (False,f"Error :: {e}",None)
        return (True,f"Success :: ZIA Protocol Validated Successfully, is Connection error expected: {is_connection_error_expected}",None)
    
    @allure.step("validate zia server state")
    def validate_zia_server_state(self,               # this function checks zia state or fetches the smeIP if required
                                  required_state: str  # accepted values :: gateway,connected error,smeIP,smePort
                                  ):
        """
            This function checks the ZIA state or fetches the SME IP if required.
            
            :param required_state: Accepted values :: gateway, connected error, smeIP, smePort
            :type required_state: str
            :return: A tuple containing a boolean status, a message, and an exception (if any)
            :rtype: tuple
        """
        try:
            if 'gateway' in required_state.lower() or 'smeIP' in required_state.lower() or 'smePort' in required_state.lower() or 'connected' in required_state.lower():
                zcc_in_focus = self.bring_zcc_to_focus()
                if not zcc_in_focus[0]:
                    raise Exception(zcc_in_focus[2])
                self.ui.click(self.elements.INTERNET_SECURITY_TAB, sleep_time=0,AEFC="Unable to open Internet Security tab")
                if self.ui.check_if_exists(self.elements.INTERNET_SECURITY_RETRY_BUTTON,AEFC="Unable to click on zia retry button"):
                    raise Exception("ERROR :: Gateway or smeIP expected, got connection error")
                else:
                    zcc_ui_data = self.get_zcc_ui_values_mac()
                    if not zcc_ui_data[0]:
                        raise Exception(zcc_ui_data[2])
                    zcc_ui_data = zcc_ui_data[2]
                    zia_state = str(zcc_ui_data["serverip"])
                    if 'smePort' in required_state.lower() : 
                        zia_state = str(zcc_ui_data["smePort"])
                        self.logger.info(f"smePort = {zia_state}")
                    else:
                        self.logger.info(f"zia_state = {zia_state} |||| length = {len(zia_state)}")
            else:
                zcc_in_focus = self.bring_zcc_to_focus()
                if not zcc_in_focus[0]:
                    raise Exception(zcc_in_focus[2])
                # self.ui.click(self.elements.ZSA_TRAY_FORM, AEFC="App not Found", max_threshold=20, sleep_time=0)
                self.ui.click(self.elements.INTERNET_SECURITY_TAB, sleep_time=0, AEFC="Unable to open Internet Security tab")
                if self.ui.check_if_exists(self.elements.INTERNET_SECURITY_RETRY_BUTTON, AEFC="Unable to open Internet Security Tunnel Status Text"):
                    self.logger.info("Retry button found")
                    logs_to_be_searched = ['getCurrentNetworkType:NON_TRUSTED getSmeProxyState:SERVER_DOWN_ERROR']
                    self.log_ops.search_log_file(file="ZSATunnel", words_to_find_in_line=logs_to_be_searched,search_mode=None)
                    if 'SERVER_DOWN_ERROR' in self.log_ops.recent_log_line.split(':')[-1]:
                        self.logger.info("SERVER_DOWN_ERROR found in logs !")
                        zia_state = 'connection error'
                    else:
                        self.logger.error("SERVER_DOWN_ERROR not found in logs !")
                        raise Exception("SERVER_DOWN_ERROR not found in logs while testing connection error state")
                else:
                    raise Exception("Retry button not found while testing connection error state")
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,"Success :: ZIA server state Validated Successfully",None)
        
    @allure.step("validate network type on zcc")
    def validate_network_type_on_zcc(self,
                                required_network: str,               # accepted values "trusted" or "off trusted"
                                service: str= "zia",                # service for which netwrok type is to be validated, zia or zpa
                                skip_tray_click: bool =False,         # whether to click on zcc tray or not - as a part of time reduction
                                fail_expected: bool =False           # whether we are expecting case to fail or not
    ):
        """
            This function validates the network type on ZCC.
            
            Args:
                required_network (str): The required network type. Accepted values are "trusted" or "off trusted".
                service (str, optional): The service for which the network type is to be validated. Defaults to "zia".
                skip_tray_click (bool, optional): Whether to click on the ZCC tray or not. Defaults to False.
                fail_expected (bool, optional): Whether the test case is expected to fail or not. Defaults to False.
            
            Returns:
                tuple: A tuple containing a boolean indicating success or failure and a string with a message.
        """
        try:
            zcc_ui_data = self.get_zcc_ui_values_mac()
            if not zcc_ui_data[0]:
                raise Exception(zcc_ui_data[2])
            zcc_ui_data = zcc_ui_data[2]
            if 'on' in required_network.lower() or required_network.lower()=='trusted':
                if str(zcc_ui_data['nwstate'])=="0":self.logger.info(f"ZCC UI on trusted network validated, nwstate value -> {str(zcc_ui_data['nwstate'])}")
                else:
                    if not fail_expected:raise Exception("ZCC UI not on trusted network")
            elif 'off' in required_network.lower() or 'off trusted' in required_network.lower():
                if str(zcc_ui_data['nwstate'])=="2":self.logger.info(f"ZCC UI on off trusted network validated, nwstate value -> {str(zcc_ui_data['nwstate'])}")
                else:
                    if not fail_expected:raise Exception("ZCC UI not on off trusted network")
            elif 'split' in required_network.lower():
                if str(zcc_ui_data['nwstate'])=="3":self.logger.info(f"ZCC UI on split vpn trusted network validated, nwstate value -> {str(zcc_ui_data['nwstate'])}")
                else:
                    if not fail_expected:raise Exception("ZCC UI not on split vpn trusted network")
            elif 'split' not in required_network.lower() and 'vpn' in required_network.lower():
                if str(zcc_ui_data['nwstate'])=="1":self.logger.info(f"ZCC UI on  vpn trusted network validated, nwstate value -> {str(zcc_ui_data['nwstate'])}")
                else:
                    if not fail_expected:raise Exception("ZCC UI not on vpn trusted network")
            else:
                raise Exception(f"Invalid network given -> {required_network}. Please give on,off,Vpn or splitVPN")
        except Exception as e:
            return (False,f"Error :: {e}",None)
        return (True,"Success :: Network type on zcc Validated Successfully",None)

    @allure.step("Validate Strict Enforcement")
    def validate_strict_enforcement(self):
        """
            Validate the presence of Strict Enforcement banner on the GUI.

            This function attempts to bring Zscaler Client Connector (ZCC) to focus,
            then checks for the presence of the Strict Enforcement banner on the GUI.
            If the banner is present and contains the correct message, the function
            returns True and a success message. If the banner is missing or incorrect,
            the function returns False and an error message.

            Returns:
                tuple: A tuple containing a boolean status and a message.
        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            se_banner_header = self.ui.click(self.elements.STRICT_ENFORCEMENT_BANNER_HEADER,return_text=True)
            se_banner_body = self.ui.click(self.elements.STRICT_ENFORCEMENT_BANNER_MESSAGE,return_text=True)
            if (se_banner_header=="Internet Access Blocked" and 
                se_banner_body=="Internet Access is blocked on this computer as per your company's policy unless you sign into Zscaler Client Connector."):self.logger.info("Strict Enforcement banner found on GUI")
            else:raise Exception("Strict Enforcement banner missing on GUI")
        except Exception as e:
            self.logger.error(e)
            return (False,f"Error :: {e}",None)
        return (True,"Success :: Strict Enforcement Validated Successfully",None)

    @allure.step("Update Policy From Side menu")
    def update_policy_from_side_menu(
        self, 
        sleep_time:int=10, # number of seconds to sleep after execution
        ):         
        """
            This function updates the policy from the side menu.

            :param sleep_time: Number of seconds to sleep after execution. Default is 30 seconds.
            :return: A tuple containing a boolean status and a message.
        """
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            zcc_version = self.sys_ops.fetch_application_version()[-1]  # Fetch application version, Default application is Zscaler
            self.ui.click(self.elements.ZCC_SIDE_MENU_ICON, sleep_time=1*self.sleep_factor, AEFC="Unable to click side Menu button on ZCC")
            if Version(zcc_version) < Version("4.5.0.0"):
                self.ui.click(self.elements.ZCC_UPDATE_POLICY_FROM_SIDE_MENU_BUTTON, sleep_time=sleep_time, AEFC="Unable to click Update Policy button from side menu")
            else:
                self.ui.click(self.elements.ZCC_TUNNEL_STATUS_FROM_SIDE_MENU_BUTTON, sleep_time=sleep_time, AEFC="Unable to click Tunnel Status button from side menu")
                self.ui.click(self.elements.ZCC_UPDATE_POLICY_FROM_TUNNEL_STATUS_SIDE_MENU_BUTTON, sleep_time=sleep_time, AEFC="Unable to click update policy via tunnel status button from side menu")
        except Exception as e: 
            self.logger.error(f"Update policy failed from side menu at SE/MT page, Error: {e}")
            return False,f"Update policy failed from side menu at SE/MT page, Error: {e}",None 
        self.logger.info("Success :: Updated Policy from side menu!")
        return True,"Success :: Updated Policy from side menu!",None

    @allure.step("Uninstall ZCC With Password")
    def uninstall_with_password(self,
            password : str,                 # password to be used to Uninstall ZCC
            failure_expected: bool=False    # True/False based on failure expectation
    ):
        """
            This function is used to uninstall ZCC with a password.

            :param password: str, password to be used to Uninstall ZCC
            :param failure_expected: bool, True/False based on failure expectation
            :return: Tuple containing a boolean and a string indicating the success or failure of the operation.
        """
        time.sleep(2)
        self.logger.info(f"Entering uninstall password : {password}")
        try:
            o = InstallUninstall()

            curl_process = Process(
                target=o.uninstall_zcc
            )
            curl_process.start()
            self.ui.type(self.elements.UNINSTALL_PASSWORD_BOX, text=password, sleep_time=1,AEFC="Unable to enter password")
            self.ui.click(self.elements.UNINSTALL_PASSWORD_OK_BUTTON, sleep_time=1, AEFC="Unable to enter password")
            if failure_expected:
                self.ui.click(self.elements.UNINSTALL_PASSWORD_FAILURE_OK_BUTTON, sleep_time=1,AEFC="Unable to click ok after wrong password")
            curl_process.join(30)
        except Exception as e:
            return (False,f"Error :: Unable to enter uninstall password!! ::  {e}",None)
        return (True,"Success :: Uninstall ZCC With Password Sucess",None)

    @allure.step("Validate ZCC build type")
    def validate_zcc_build_type(self, 
                                expected_build: str=None, # "test" or "production", only 2 values accepted
                                failure_expected: bool=False # True if validation should fail, else False
                                )->tuple[bool,str]:
        
        self.logger.info("In Validate ZCC Build type function. Starting to validate ZCC Build type")

        if not expected_build or expected_build not in ["production", "test"]:
            self.logger.error("Please enter the expected_build type ....")
            return (False, "expected_build is missing or invalid, Please enter the correct expected_build type ....", None)

        self.bring_zcc_to_focus()
        build_type_observed = None
        try:
            if expected_build=="production":
                self.logger.info("Checking if the build is PRODUCTION build")
                if(self.ui.check_if_exists(self.elements.ZCC_WINDOW_PRODUCTION_BUILD_LABEL, boolean=True)):
                    build_type_observed = "PRODUCTION"
            elif expected_build=="test":
                self.logger.info("Checking if the build is TEST build")
                if(self.ui.check_if_exists(self.elements.ZCC_WINDOW_TEST_BUILD_LABEL, boolean=True)):
                    build_type_observed = "TEST"
        except Exception as e:
            if build_type_observed and failure_expected:
                self.logger.info(f"Build check failed as expected :: Observed build type is {build_type_observed}, Excpected build type is {expected_build.upper()}")
                return (True, f"Build check failed as expected :: Observed build type is {build_type_observed}, Excpected build type is {expected_build.upper()}, ERROR :: {e}", None)
            else:
                self.logger.info(f"Build check failed:: Observed build type is {build_type_observed}, Excpected build type is {expected_build.upper()}")
                return (False, f"Build check failed :: Observed build type is {build_type_observed}, Excpected build type is {expected_build.upper()}", None)
        else:
            self.logger.info(f"Expected build validated :: Observed build type is {build_type_observed}, Excpected build type is {expected_build.upper()}")
            return (True, f"Expected build validated :: Observed build type is {build_type_observed}, Excpected build type is {expected_build.upper()}", None)


    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    def handle_one_id_login(self,
                            oneid_user: str = None,            # Oneid user id
                            oneid_password: str = None,        # Oneid password
                            aup: bool= True,
                            cancel_aup: bool= False,           # Defines whether to cancel or decline AUP
                            cancel_login_at_oneid: bool = False, #Cancel One ID on Login
                            zia_saml_login: bool= False,        #SAML AUP
                            sleep_time: int = 10)  :             # Defines sleep time after login is done                                                               # Defines AUP enabled or not ): # place holder for zia one id login

            try:
                self.logger.info("One ID Cloud login being attempted....")
            
                self.initial_zcc_login_window(username=oneid_user)

                time.sleep(10)

                self.ui.type(self.elements.ONE_ID_SECOND_LOGIN_BUTTON, text= oneid_user, sleep_time=10, AEFC="Second One ID Login Error")
                self.ui.click(self.elements.ONE_ID_SECOND_LOGIN_BUTTON_NEXT, sleep_time=10, AEFC="Second One ID Next Login Error")
            
                result = self.oneid_ui_login_helper(password=oneid_password, cancel_login_at_oneid =  cancel_login_at_oneid)
                self.logger.info("Logging into ZCC....")
                

                if aup:
                    result = self.ui.click(self.elements.ONEID_AUP_ACCEPT,sleep_time=5, AEFC="AUP Login Failed")
                    self.logger.info("AUP Appears as expected...")
            
            except Exception as e:
                self.logger.error(f"Error :: Login Failed for One ID")
                return False, f"Error :: Login Failed for One ID :: {e}", None
            else:
                return True, f"Success :: Login Succesful", None     
                
                
    # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("One ID UI Login")
    def oneid_ui_login_helper(self,
                        #username: str,              
                        password: str,
                        cancel_login_at_oneid : bool = False):
        
        if cancel_login_at_oneid:
            self.logger.info("Aborting Login at OneId, hitting back button on ZCC")
            try: self.ui.click(self.elements.ONEID_LOGIN_PAGE_BACK_BUTTON,sleep_time=1, AEFC="Cannot click ZCC Back Button")
            except Exception as e: return(False, f"Error :: Login Failed at Form Password pAGE :: {e}", e)
            return (True, "Success : Aborting Login at OneID, hitting back button on ZCC", None)
        
        try:
            time.sleep(10)
            self.ui.type(self.elements.ONEID_LOGIN_PAGE_PASSWORD_BOX, text=password, sleep_time=10, AEFC="Password button not found")
            self.startTimeForLogSearch = datetime.now()
            self.ui.click(self.elements.ONEID_LOGIN_PAGE_SIGNIN_BUTTON, sleep_time=10, AEFC="Unable to click Login Button")
        
        except Exception as e:
            self.logger.error(f"Error :: Login Failed at Form PASSWORD Page :: {e}")
            raise Exception(f"Error :: Login Failed at Form PASSWORD Page :: {e}")      
        
     # ------------------------------------------------------------------------------------------------------------------------------------------------------
    @allure.step("One ID System Test Case: Entitlement Toggle")
    def oneid_system_test_entitlement_toggle(self,              
                        cancel_login_at_oneid : bool = False,
                        input_toggle_count : int = 3,
                        zia_toggle : bool = True,
                        zpa_toggle : bool = True,
                        zdx_toggle : bool = False):
        
        time.sleep(10)
        self.bring_zcc_to_focus()
        time.sleep(10)
        
        ###write block to check if zcc is logged in, else login
        ###expand for 1 id vs non 1 id case
        
        #to check which is ON vs OFF element ID
        
        #ZIA Case
        if zia_toggle:
            self.ui.click(self.elements.INTERNET_SECURITY_TAB, sleep_time=5, AEFC="ZIA Tab not found")
            for i in range(input_toggle_count):
                self.ui.click(self.elements.INTERNET_SECURITY_POWER_BUTTON_OFF, sleep_time=10, AEFC="ZIA Power OFF Button not found")
                self.ui.click(self.elements.INTERNET_SECURITY_POWER_BUTTON_OFF_ACCEPT_BUTTON , sleep_time=30, AEFC="ZIA Power OFF Button not found")            
                self.ui.click(self.elements.INTERNET_SECURITY_POWER_BUTTON_ON, sleep_time=30, AEFC="ZIA Power OFF Button not found")
            
                if self.ui.check_if_exists(self.elements.INTERNET_SECURITY_POWER_BUTTON_OFF, boolean=True, AEFC="ZIA One ID entilement NOT as expected"):
                    print("System Test:: ZIA Entitlement as Expected")
                    time.sleep(10)
                else:
                    print("System Test :: One ID ZIA Entitlement check Failed")
                    

        #ZPA Case
        if zpa_toggle:
            self.ui.click(self.elements.PRIVATE_ACCESS_TAB, sleep_time=5, AEFC="ZPA Tab not found")
            for i in range(input_toggle_count):
                self.ui.click(self.elements.PRIVATE_ACCESS_POWER_BUTTON_OFF, sleep_time=10, AEFC="ZPA Power OFF Button not found")
                self.ui.click(self.elements.PRIVATE_ACCESS_POWER_BUTTON_OFF_ACCEPT_BUTTON , sleep_time=30, AEFC="ZPA Power OFF Button not found")            
                self.ui.click(self.elements.PRIVATE_ACCESS_POWER_BUTTON_ON, sleep_time=30, AEFC="ZPA Power OFF Button not found")
            
                if self.ui.check_if_exists(self.elements.PRIVATE_ACCESS_POWER_BUTTON_OFF, boolean=True, AEFC="ZPA One ID entilement NOT as expected"):
                    print("System Test:: ZPA Entitlement as Expected")
                    time.sleep(10)
                else:
                    print("System Test :: One ID ZPA Entitlement check Failed")
                    
 
    @allure.step("Update ZCC")
    @calculate_func_execute_time
    def update_app(self, failure_expected:bool=False, is_update_on_login_or_keepalive:bool=False, expected_version:str=None, sleep_time:int=20, version_before_update:str=None):
        """
        Validates update app via MA

        1. Clicks on more button
        2. Clicks on update app button
        3. Waits until a new tray file has been found or waits for 180 seconds at max
        4. Validates if the app has updated based on the version of zcc before app update and after app update
        
        """
        self.logger.info("In Update app function")
        # Taking Zcc version before update incase update app needs to be validated on login
        # If update app is being performed manually by clicking on update app button in more, then this method will be called after update without any zcc_version_before_update is specified else zcc_version_before_update needs to be specified 
        zcc_version_before_update = self.sys_ops.fetch_application_version()[2] if version_before_update == None else version_before_update
        logs_to_be_searched_in_old_tray = ["updateApp called"]
        zsa_tray_log_path = constants.Utils.TRAY_LOG_PATH
        number_of_zsa_tray_files_before_update = len([files for files in os.listdir(zsa_tray_log_path) if "ZSATray" in files])
        try:
            if is_update_on_login_or_keepalive == False:
                zcc_in_focus = self.bring_zcc_to_focus()
                if not zcc_in_focus[0]:
                    raise Exception(zcc_in_focus[2])
                self.ui.click(self.elements.APP_MORE, sleep_time=1*self.sleep_factor, AEFC="Unable to click More button on ZCC")
                self.ui.click(self.elements.UPDATE_APP, sleep_time=0, AEFC="Unable to click Update App button")
            start_time = int(time.time())
        except Exception as e:
            self.logger.error(f"There occurred an exception while trying to click on update app :: {e}")
            return False, f"There occurred an exception while trying to click on update app :: {e}", None

        else:
            
            while (len([files for files in os.listdir(zsa_tray_log_path) if "ZSATray" in files])) <= number_of_zsa_tray_files_before_update and int(time.time() - start_time) <= (120 if failure_expected else 180):
                l = len([files for files in os.listdir(zsa_tray_log_path) if "ZSATray" in files])
                print(f"Waiting for app update, Wait time : 3 mins, No. of Tray files : {l} Time elapsed {int(time.time() - start_time)} seconds",end='\r')
            
            self.logger.info("Sleeping for 10 seconds to let ZCC load after update")
            time.sleep(10)
            zcc_version_after_update = self.sys_ops.fetch_application_version()[2]
            
            if len([files for files in os.listdir(zsa_tray_log_path) if "ZSATray" in files]) <= number_of_zsa_tray_files_before_update:
                if failure_expected:
                    if zcc_version_before_update == zcc_version_after_update:
                        self.logger.info(f"No new tray files has been found, The app has not yet updated, Failed as expected\nZCC Version Before Update: {zcc_version_before_update}\nZCC Version After Update: {zcc_version_after_update}\n")
                        return True, f"No new tray files has been found, The app has not yet updated, Failed as expected\nZCC Version Before Update: {zcc_version_before_update}\nZCC Version After Update: {zcc_version_after_update}\n", None
                else:
                    self.logger.error(f"No new tray files has been found, The app has not yet updated, Update App failed\nZCC Version Before Update: {zcc_version_before_update}\nZCC Version After Update: {zcc_version_after_update}\nExpected ZCC Version: {expected_version}")
                    return False, f"No new tray files has been found, The app has not yet updated, Update App failed\nZCC Version Before Update: {zcc_version_before_update}\nZCC Version After Update: {zcc_version_after_update}\nExpected ZCC Version: {expected_version}", None
            else:
                self.logger.info(f"New tray file found : {[files for files in sorted(os.listdir(zsa_tray_log_path), reverse=True) if 'ZSATray' in files][0]}")
                self.logger.info(f"ZCC Version Before Update: {zcc_version_before_update}\nZCC Version After Update: {zcc_version_after_update}")
                if zcc_version_before_update != zcc_version_after_update:
                    self.logger.info("ZCC has updated successfully")
                else:
                    self.logger.error("The ZCC version after & before update is the same")
                if expected_version:
                    if zcc_version_after_update == expected_version:
                        self.logger.info("The expected version & the ZCC version after update are matching, Successfully updated ZCC")
                    else:
                        self.logger.error(f"ZCC Failed to update, please check the app, expected version - {expected_version} & zcc version after update - - {zcc_version_after_update} do not match")
                        return False, f"ZCC Failed to update, please check the app, expected version  - {expected_version} & zcc version after update do not match, ZCC Version Before Update: {zcc_version_before_update}\nZCC Version After Update: {zcc_version_after_update}", None

        if sleep_time > 0:
            self.logger.info(f"Letting ZCC settle down: Sleeping for {sleep_time} seconds")
            time.sleep(sleep_time)

        return True, f"Successfully updated ZCC, \nZCC Version Before Update: {zcc_version_before_update}\nZCC Version After Update: {zcc_version_after_update}", None
    
    @allure.step("Opens partner tenant page")
    def open_partner_tenant_page(self, partner_tenant_row_id:int):
        self.logger.info(f"Trying to open partner tenant page, Partner tenant row id - {partner_tenant_row_id}")        
        partner_tenant_username_button = self.elements.PARTNER_TENANT_USER_NAME_BUTTON[0].format(partner_tenant_row_id)

        try:
            result = self.find_private_access_tab(should_be_present=True)
            if not result[0]:
                return result
            partner_user_name = self.ui.fetch_attribute(identifier=partner_tenant_username_button, fetch_text=False, attribute_to_be_fetched="title")
            self.ui.click(path=None, accessibility_id=partner_tenant_username_button, sleep_time=1*self.sleep_factor, AEFC=f"Unable to click {partner_user_name} Partner tenant at ZPA page")
        except Exception as e:
            self.logger.error(f"Unable to access partner tenant page for {partner_user_name} at row {partner_tenant_row_id}, Error: {e}")
            return False, f"Unable to access partner tenant page for {partner_user_name} at row {partner_tenant_row_id}, Error: {e}", None
        else:
            self.logger.info(f"Opened partner tenant page for {partner_user_name} at row {partner_tenant_row_id}")
            return True, f"Opened partner tenant page for {partner_user_name} at row {partner_tenant_row_id}", None

    @allure.step("Opens main tenant page")
    def open_main_tenant_page(self):
        self.logger.info(f"Trying to open main tenant page")
        
        try:
            result = self.find_private_access_tab(should_be_present=True)
            if not result[0]:
                return result
            main_tenant_user_name = self.ui.fetch_attribute(identifier=self.elements.MAIN_TENANT_USER_NAME_BUTTON[0], fetch_text=False, attribute_to_be_fetched="title")
            self.ui.click(path=None, accessibility_id=self.elements.MAIN_TENANT_USER_NAME_BUTTON[0], sleep_time=1*self.sleep_factor, AEFC=f"Unable to click {main_tenant_user_name} Partner tenant at ZPA page")
        except Exception as e:
            self.logger.error(f"Unable to access main tenant page for {main_tenant_user_name}, Error: {e}")
            return False, f"Unable to access main tenant page for {main_tenant_user_name}, Error: {e}", None
        else:
            self.logger.info(f"Opened main tenant page for {main_tenant_user_name}")
            return True, f"Opened main tenant page for {main_tenant_user_name}", None

    @allure.step("Handle the error OK or Ok popups")
    def click_error_popup_ok_button(self):
        """
        There are 3 different types of OK buttons configured, to handle all the cases, this will handle all 3 cases.
        At max wait time for searching a ok button is just 10 seconds, so totally at max this function will take 30 seconds.
        """
        self.logger.info("Failure must have been expected")
        error=None

        try:
            self.logger.info("Trying to click on the warning Ok button")
            self.ui.click(path=self.elements.WARNING_POPUP_OK_BUTTON, sleep_time=1*self.sleep_factor, AEFC=f"Unable to click 'Ok' button", dynamic_wait_search_element=10)
        except Exception as e:
            error = e
            self.logger.error(f"Unable to  warning 'Ok', trying to click on confirk 'Ok' button, Error: {error}")
        else:
            return True, "Click on warning 'Ok' button successfully performed", None
        
        try:
            self.logger.info("Trying to click on the confirm Ok button")
            self.ui.click(path=self.elements.CONFIRM_POPUP_OK_BUTTON, sleep_time=1*self.sleep_factor, AEFC=f"Unable to click 'Ok' button", dynamic_wait_search_element=10)
        except Exception as e:
            error = e
            self.logger.error(f"Unable to click on warning 'Ok' or confirm 'Ok' button, trying to click on login 'Ok', Error: {error}")
        else:
            return True, "Click on confirm 'Ok' button successfully performed", None

        try:
            self.logger.info("Trying to click on the login Ok button")
            self.ui.click(path=self.elements.LOGIN_ERROR_OK_BUTTON, sleep_time=1*self.sleep_factor, AEFC=f"Unable to click login 'Ok' button", dynamic_wait_search_element=10)
        except Exception as e:
            error = e
            self.logger.info(f"Unable to click on warning 'Ok' or confirm 'Ok' or login 'Ok', trying to click on 'OK', Error: {error}")
        else:
            return True, "Click on login 'Ok' button successfully performed", None
        
        try:
            self.logger.info("Trying to click on the OK button")
            self.ui.click(path=self.elements.ERROR_POPUP_OK_BUTTON, sleep_time=1*self.sleep_factor, AEFC=f"Unable to click 'OK' button", dynamic_wait_search_element=10)
        except Exception as e:
            error = e
            self.logger.info(f"Unable to click on warning 'Ok' or confirm 'Ok' or login 'Ok' or 'OK' button, Error: {error}")
        else:
            return True, "Click on 'OK' button successfully performed", None
        try:
            self.logger.info("Trying to click on the app error OK button")
            self.ui.click(path=self.elements.ERROR_POPUP_OK_BUTTON_2, sleep_time=1*self.sleep_factor, AEFC=f"Unable to click 'OK' button", dynamic_wait_search_element=10)
        except Exception as e:
            error = e
            self.logger.info(f"Unable to click on warning 'Ok' or confirm 'Ok' or login 'Ok' or 'OK' button, Error: {error}")
        else:
            return True, "Click on app error 'OK' button successfully performed", None
        
        try:
            self.logger.info("Trying to click on the SE Ok button")
            self.ui.click(path=self.elements.SE_ERROR_POPUP_OK_BUTTON, sleep_time=1*self.sleep_factor, AEFC=f"Unable to click SE 'OK' button", dynamic_wait_search_element=10)
        except Exception as e:
            error = e
            self.logger.info(f"Unable to click on warning 'Ok' or confirm 'Ok' or login 'Ok' or 'OK' button, Error: {error}")
        else:
            return True, "Click on SE 'OK' button successfully performed", None
        
        return False, f"Unable to click on 'OK' or warning 'Ok' or login 'Ok' or confirm 'Ok' button or SE 'Ok' button, Error: {error}", None
        
    def click_notification_learn_more(self, total_buttons_to_click: int = 1):
        '''
            This function performs click action on Learn More button in Notification tab.
            Searching from the top most notification, goes down till total buttons to click are not completed.

            :param total_buttons_to_click: Total number of Different Learn More Buttons to click
        '''
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            
            self.ui.click(self.elements.NOTIFICATIONS_TAB, sleep_time=2, AEFC="Unable to open Notifications tab")
            self.ui.click(self.elements.APP_MORE, sleep_time=2, AEFC="Unable to open more tab")
            self.ui.click(self.elements.NOTIFICATIONS_TAB, sleep_time=2, AEFC="Unable to open Notifications tab")
            
            max_rownumber = 5

            for each_rownumber in range(max_rownumber):
                if total_buttons_to_click < 1:
                    break
                if self.ui.check_if_exists(accessibility_id=self.elements.NOTIFICATIONS_LEARN_MORE_BUTTON.format(each_rownumber), boolean=True):
                    self.ui.click(accessibility_id=self.elements.NOTIFICATIONS_LEARN_MORE_BUTTON.format(each_rownumber))
                    self.ui.click(self.elements.NOTIFICATION_LEARN_MORE_POPUP_EXIT_BUTTON)
                    total_buttons_to_click -= 1

            if total_buttons_to_click:
                self.logger.error('Error :: Failed to click on Learn More Button for required number of times.')
                return False, f'Error :: Failed to click on Learn More Button for required number of times.', None
            self.logger.info('Success :: Learn More Button Clicked Successfully for required number of times.')
            return True, f'Success :: Learn More Button Clicked Successfully for required number of times.', None
        except Exception as e:
            self.logger.error('Error :: Failed to click on Learn More Button')
            return False, f'Error :: Failed to click on Learn More Button', None

    def count_number_of_notifications(self):
        try:
            zcc_in_focus = self.bring_zcc_to_focus()
            if not zcc_in_focus[0]:
                raise Exception(zcc_in_focus[2])
            
            self.ui.click(self.elements.NOTIFICATIONS_TAB, sleep_time=2, AEFC="Unable to open Notifications tab")
            self.ui.click(self.elements.APP_MORE, sleep_time=2, AEFC="Unable to open more tab")
            self.ui.click(self.elements.NOTIFICATIONS_TAB, sleep_time=2, AEFC="Unable to open Notifications tab")
            
            notification_count = 0

            while True:
                if self.ui.check_if_exists(accessibility_id=self.elements.NOTIFICATIONS_CATEGORY_LABEL.format(notification_count), boolean=True):
                    notification_count += 1
                else:
                    break

            self.logger.info(f'Total number of notifications found: "{notification_count}".')
            return True, f'Total number of notifications found: "{notification_count}".', notification_count
        except Exception as e:
            self.logger.error(f'Error :: Failed to count total number of notifications :: {e}')
            return False, f'Error :: Failed to count total number of notifications :: {e}', None
        
    def set_cloud_name(self,
                            cloud_name: str = None,
                            )->tuple[bool,str,str]:
        """
        This function helps to set adhoc cloud name from zcc ui
        Parameters:
            cloud_name[str]: cloud name to set in zcc
        Returns:
            tuple[bool, str]: A tuple containing a boolean indicating success or failure and a string with a message.
        """
        try:
            self.bring_zcc_to_focus()
            self.logger.info("Clicking on the menu button on ZCC page")
            self.ui.click(self.elements.MENU_BUTTON, sleep_time=1, AEFC="Unable to Click zcc menu button")
            self.logger.info("Clicking on the Cloud name button")
            self.ui.click(self.elements.CLOUD_NAME_BUTTON, sleep_time=1, AEFC="Unable to Click cloud name button")
            self.logger.info("Typing in the cloud name")
            self.ui.type(self.elements.CLOUD_NAME_TEXT, text=cloud_name, sleep_time=1,AEFC="Something wrong with entering cloud name")
            self.logger.info("Clicking on thr save button")
            self.ui.click(self.elements.CLOUD_NAME_SAVE, sleep_time=1,AEFC="Unable to Click cloud name save button")
            self.logger.info("Pressing enter key to click OK on the dialog box")
            pyautogui.press("enter")
            # self.ui.click(self.elements.CLOUD_NAME_OK, sleep_time=1, AEFC="Unable to Click cloud name continue button")
            return (True,"Success ::set adhoc cloud name","")
        except Exception as e:
            self.logger.error(f"Error :: set cloud name failed :: {e}")
            return (False,f"Error :: set cloud name failed :: {e}","")
        
    def scale_zcc_ui(self)->tuple[bool,str,str]:
        """
        This function scales in or scales out the ZCC UI.
        Returns:
        tuple[bool,str]: A tuple indicating if the scaling was success or failure and a string with the message.
        """
        try:
            self.bring_zcc_to_focus()
            self.logger.info("Clicking on scaling button")
            self.ui.click(path=self.elements.SCALE_ZSCALER_UI[1],sleep_time=1,AEFC="Unable to click on scale button")    
            return (True,"Scaling Successfull","")
        except Exception as e:
            self.logger.info(f"Scaling failed::{e}")
            return (False,f"Scaling failed:: {e}","")

    def validate_zia_connected(
        self,
        search_time:int=60
    )->tuple[bool, str, None]:
        """
        Validate if zia is connected by finding the turn off button using xpath
        """
        try:
            is_internet_access_present = self.find_internet_access_tab(should_be_present=True)
            if is_internet_access_present[0]==False:
                return is_internet_access_present

            self.ui.check_if_exists(
                path=self.elements.INTERNET_SECURITY_POWER_BUTTON_OFF[1],
                AEFC="Unable to find Turn Off button",
                AESC="ZIA Turn Off button found",
                dynamic_wait_search_element=search_time
            )
        except Exception as error:
            self.logger.error("Unable to find turn off button, hence unable to validate if zia is connected")
            return False, "Unable to find turn off button, hence unable to validate if zia is connected", None
        else:
            self.logger.info("Zia is in connected state as turn off button was found")
            return True, "Zia is in connected state as turn off button was found", None
    
    def validate_zpa_connected(
        self,
        search_time:int=60
    )->tuple[bool, str, None]:
        """
        Validate if zpa is connected by finding the turn off button using xpath
        """
        try:
            is_private_access_present = self.find_private_access_tab(should_be_present=True)
            if is_private_access_present[0]==False:
                return is_private_access_present

            self.ui.check_if_exists(
                path=self.elements.PRIVATE_ACCESS_POWER_BUTTON_OFF[1],
                AEFC="Unable to find Turn Off button",
                AESC="ZPA Turn Off button found",
                dynamic_wait_search_element=search_time
            )
        except Exception as error:
            self.logger.error("Unable to find turn off button, hence unable to validate if zpa is connected")
            return False, "Unable to find turn off button, hence unable to validate if zpa is connected", None
        else:
            self.logger.info("Zpa is in connected state as turn off button was found")
            return True, "Zpa is in connected state as turn off button was found", None
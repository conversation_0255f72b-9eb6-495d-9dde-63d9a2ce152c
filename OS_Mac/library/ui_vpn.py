from common_lib.common import common_ui
import copy,os
import time
import pyautogui
import allure
from common_lib.common.logger import Logger

class ElementsAnyConnectOld:
    APP_SHORTCUT = r'open /Applications/Cisco/Cisco\ AnyConnect\ Secure\ Mobility\ Client.app/'
    APP_NAME = "/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXIdentifier='_NS:126' and @AXSubrole='AXStandardWindow']/AXStaticText[@AXValue='AnyConnect' and @AXIdentifier='_NS:106']"
    APP = "/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXIdentifier='_NS:126' and @AXSubrole='AXStandardWindow']/AXStaticText[@AXValue='AnyConnect' and @AXIdentifier='_NS:106']"
    ACCEPT_BUTTON = "/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXTitle='Cisco AnyConnect - Banner' and @AXIdentifier='_NS:93' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Accept' and @AXIdentifier='_NS:9']"
    CONNECTED_TEXT = "/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXIdentifier='_NS:126' and @AXSubrole='AXStandardWindow']/AXStaticText[@AXValue='Connected to ************.' and @AXIdentifier='_NS:111']"
    DISCONNECTED_TEXT = "/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXIdentifier='_NS:126' and @AXSubrole='AXStandardWindow']/AXStaticText[@AXValue='Ready to connect.' and @AXIdentifier='_NS:111']"
    IP_FIELD = "/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXIdentifier='_NS:126' and @AXSubrole='AXStandardWindow']/AXComboBox[@AXIdentifier='_NS:46']"
    IP_FIELD_WITH_IP = "/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXIdentifier='_NS:126' and @AXSubrole='AXStandardWindow']/AXComboBox[@AXValue='************' and @AXIdentifier='_NS:46']"
    CONNECT_BUTTON = "/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXIdentifier='_NS:126' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='Connect' and @AXIdentifier='_NS:33']"
    CONNECT_ANYWAY_BUTTON = "/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXIdentifier='_NS:93' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Connect Anyway' and @AXIdentifier='_NS:64']"
    CONNECT_ANYWAY_BUTTON_AT_LAST = "/AXApplication[@AXTitle='vpndownloader']/AXWindow[@AXIdentifier='_NS:9' and @AXSubrole='AXDialog']/AXButton[@AXTitle='Connect Anyway' and @AXIdentifier='_NS:121']"
    OPTION_DROPDOWN = "/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXTitle='Cisco AnyConnect | ************' and @AXIdentifier='_NS:93' and @AXSubrole='AXDialog']/AXPopUpButton[@AXValue='Split_Include']"
    OPTION_DROPDOWN_SPLIT_INCLUDE = "/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXTitle='Cisco AnyConnect | ************' and @AXIdentifier='_NS:93' and @AXSubrole='AXDialog']/AXPopUpButton[@AXValue='Split_Include']/AXMenu[0]/AXMenuItem[@AXTitle='Split_Include' and @AXIdentifier='_popUpItemAction:']"
    USERNAME_FIELD = "/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXTitle='Cisco AnyConnect | ************' and @AXIdentifier='_NS:93' and @AXSubrole='AXDialog']/AXTextField[0]"
    USERNAME_FIELD_WITH_ZSCALER = "/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXTitle='Cisco AnyConnect | ************' and @AXIdentifier='_NS:93' and @AXSubrole='AXDialog']/AXTextField[@AXValue='zscaler']"
    PASSWORD_FIELD = "/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXTitle='Cisco AnyConnect | ************' and @AXIdentifier='_NS:93' and @AXSubrole='AXDialog']/AXTextField[@AXSubrole='AXSecureTextField']"
    LOGIN_OK_BUTTON = "/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXTitle='Cisco AnyConnect | ************' and @AXIdentifier='_NS:93' and @AXSubrole='AXDialog']/AXButton[@AXTitle='OK']"
    DISCONNECT_CONNECTION = "/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXIdentifier='_NS:126' and @AXSubrole='AXStandardWindow']/AXButton[@AXTitle='Disconnect' and @AXIdentifier='_NS:33']"
    CLOSE_BUTTON = 'Close'
    MODE_OPTIONS = {
        "Split_Exclude":"/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXTitle='Cisco AnyConnect | ************' and @AXIdentifier='_NS:93' and @AXSubrole='AXDialog']/AXPopUpButton[@AXValue='Split_Include']/AXMenu[0]/AXMenuItem[@AXTitle='Split_Exclude' and @AXIdentifier='_popUpItemAction:']",
        "Split_Include":"/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXTitle='Cisco AnyConnect | ************' and @AXIdentifier='_NS:93' and @AXSubrole='AXDialog']/AXPopUpButton[@AXValue='Split_Include']/AXMenu[0]/AXMenuItem[@AXTitle='Split_Include' and @AXIdentifier='_popUpItemAction:']",
        "Zscaler_Engineering":"/AXApplication[@AXTitle='Cisco AnyConnect Secure Mobility Client']/AXWindow[@AXTitle='Cisco AnyConnect | ************' and @AXIdentifier='_NS:93' and @AXSubrole='AXDialog']/AXPopUpButton[@AXValue='Split_Include']/AXMenu[0]/AXMenuItem[@AXTitle='Zscaler Engineering' and @AXIdentifier='_popUpItemAction:']"
    }

class ElementsAnyConnect:
    APP_BUNDLE_IDENTIFIER = 'com.cisco.anyconnect.gui'
    APP_NAME = 'Cisco AnyConnect Secure Mobility Client'
    MAIN_PAGE_TITLE_LABEL = '//XCUIElementTypeStaticText[@value="AnyConnect"]'
    MAIN_PAGE_IP_TEXTBOX = '//XCUIElementTypeComboBox'
    MAIN_PAGE_CONNECT_BUTTON = '//XCUIElementTypeButton[@title="Connect"]'
    MAIN_PAGE_VPN_LOCK_IMAGE = '//XCUIElementTypeImage[@label="VPN 72x"]'
    SECURITY_WARNING_CONNECT_ANYWAY_BUTTON = '//XCUIElementTypeButton[@title="Connect Anyway"]'
    SECURITY_WARNING_CANCEL_CONNECTION_BUTTON = '//XCUIElementTypeButton[@title="Cancel Connection"]'
    CREDENTIALS_PAGE_GROUP_POPUP_BUTTON = '//XCUIElementTypePopUpButton'
    CREDENTIALS_PAGE_GROUP_SPLIT_INCLUDE_BUTTON = '//XCUIElementTypeMenuItem[@title="Split_Include"]'
    CREDENTIALS_PAGE_GROUP_SPLIT_INCLUDE_IPV6_BUTTON = '//XCUIElementTypeMenuItem[@title="Split_Include_IPv6"]'
    CREDENTIALS_PAGE_GROUP_FULL_TUNNEL_BUTTON = '//XCUIElementTypeMenuItem[@title="Full_Tunnel"]'
    CREDENTIALS_PAGE_GROUP_SPLIT_EXCLUDE_BUTTON = '//XCUIElementTypeMenuItem[@title="Split_Exclude"]'
    CREDENTIALS_PAGE_GROUP_SPLIT_EXCLUDE_IPV6_BUTTON = '//XCUIElementTypeMenuItem[@title="Split_Exclude_IPv6"]'
    CREDENTIALS_PAGE_GROUP_ZSCALER_ENGINEERING_BUTTON = '//XCUIElementTypeMenuItem[@title="Zscaler Engineering"]'
    CREDENTIALS_PAGE_USERNAME_TEXTBOX = '//XCUIElementTypeTextField[@label="Username:"]'
    CREDENTIALS_PAGE_PASSWORD_TEXTBOX = '//XCUIElementTypeSecureTextField[@label="Password:"]'
    CREDENTIALS_PAGE_OK_BUTTON = '//XCUIElementTypeButton[@title="OK"]'
    CREDENTIALS_PAGE_CANCEL_BUTTON = '//XCUIElementTypeButton[@title="Cancel"]'
    BANNER_ACCEPT_BUTTON = '//XCUIElementTypeButton[@title="Accept"]'
    BANNER_DISCONNECT_BUTTON = '//XCUIElementTypeButton[@title="Disconnect"]'
    MENUBAR_ICON_BUTTON = '//XCUIElementTypeStatusItem'
    MENUBAR_DISCONNECT_BUTTON = '//XCUIElementTypeMenuItem[@title="Disconnect"]'
    MENUBAR_SHOW_ANYCONNECT_BUTTON = '//XCUIElementTypeMenuItem[@title="Show AnyConnect Window"]'
    MENUBAR_SHOW_STATISTICS_BUTTON = '//XCUIElementTypeMenuItem[@title="Show Statistics Window"]'
    MENUBAR_QUIT_BUTTON = '//XCUIElementTypeMenuItem[@title="Quit Cisco AnyConnect"]'
    STATISTICS_PAGE_VPN_DETAILS_BUTTON = '//XCUIElementTypeButton[@title="VPN"]'
    STATISTICS_PAGE_VPN_DETAILS_CONNECTED_LABEL = '//XCUIElementTypeTextField[@value="Connected"]'
    STATISTICS_PAGE_VPN_DETAILS_DISCONNECTED_LABEL = '//XCUIElementTypeTextField[@value="Disconnected"]'

class AnyConnect:
    def __init__(
        self,
        handle="easy",
        log_handle=None
    ):
        self.logger = (log_handle if log_handle else Logger.initialize_logger("AnyConnect.log", log_level="INFO"))
        self.logger.info("Initialized Anyconnect UI")
        self.operating_system = common_ui.operating_system
        self.gui = common_ui.Gui(handle=handle,log_handle=self.logger)
        self.elements = copy.deepcopy(ElementsAnyConnect)
   
    def bring_to_focus(
        self,
        number_of_trails: int =2    # Number of times to try to open anyconnect before raising exception
    )->tuple[bool, str, None]:
        """
        Bring the vpn client to focus
        """
        is_client_launched = False
        for i in range(number_of_trails):
            try:
                self.gui.start_app(
                    app=self.elements.APP_NAME,
                    sleep_time=1,
                    app_bundle=self.elements.APP_BUNDLE_IDENTIFIER
                )
                self.gui.click(path=self.elements.MAIN_PAGE_TITLE_LABEL, AEFC="Anyconnect app not found", max_threshold=5, sleep_time=0)
                self.logger.info("Success :: AnyConnect brought to Focus!")
                is_client_launched = True
            except Exception as e:
                self.logger.error("Warning :: trying to bring AnyConnect to Focus again! ; {}\n".format(e))
                is_client_launched = False
        
        if is_client_launched == False:
            raise Exception("Error :: Failed to bring AnyConnect to Focus")

    def connect(
        self,
        ip: str,            # IP of the vpn server
        username: str,      # username to login into vpn
        password: str,      # password to login into vpn
        mode: str = None,   # mode to login into vpn
    ):      
        """
        Target: to connect to anyconnect service
        Work flow
        ----------
            1. make sure all inputs are provided properly
            2. enter the ip and try connecting
            3. select the mode of connection as specified
            4. validate we are on the right page
        """

        
        try:
            self.bring_to_focus()
            self.gui.type(self.elements.MAIN_PAGE_IP_TEXTBOX, text=ip, sleep_time=1, AEFC="Failed to type on main page IP text box")
            self.gui.click(self.elements.MAIN_PAGE_CONNECT_BUTTON, sleep_time=2,max_threshold=2, retry_frequency=1, AEFC="Failed to click on main page connect button")
            self.gui.click(self.elements.SECURITY_WARNING_CONNECT_ANYWAY_BUTTON, sleep_time=2,max_threshold=2, retry_frequency=1, AEFC="Failed to click on security warning connect anyway button")
            self.gui.click(self.elements.CREDENTIALS_PAGE_GROUP_POPUP_BUTTON, sleep_time=2,max_threshold=2, retry_frequency=1, AEFC="Failed to click on group selection popup")
            
            mode = mode.lower()
            if mode == "split_include":
                group_selection_button = self.elements.CREDENTIALS_PAGE_GROUP_SPLIT_INCLUDE_BUTTON
            elif mode == "split_exclude":
                group_selection_button = self.elements.CREDENTIALS_PAGE_GROUP_SPLIT_EXCLUDE_BUTTON
            elif mode == "full_tunnel":
                group_selection_button = self.elements.CREDENTIALS_PAGE_GROUP_FULL_TUNNEL_BUTTON
            elif mode == "zscaler_engineering":
                group_selection_button = self.elements.CREDENTIALS_PAGE_GROUP_ZSCALER_ENGINEERING_BUTTON
            elif mode == "split_include_ipv6":
                group_selection_button = self.elements.CREDENTIALS_PAGE_GROUP_SPLIT_INCLUDE_IPV6_BUTTON
            elif mode == "split_exclude_ipv6":
                group_selection_button = self.elements.CREDENTIALS_PAGE_GROUP_SPLIT_EXCLUDE_IPV6_BUTTON

            self.gui.click(group_selection_button, sleep_time=2,max_threshold=2, retry_frequency=1, AEFC=f"Failed to click on the specified button for {mode} mode")
            self.gui.type(self.elements.CREDENTIALS_PAGE_USERNAME_TEXTBOX, text=username, sleep_time=1, AEFC="Failed to type on credentials page username textbox")
            self.gui.type(self.elements.CREDENTIALS_PAGE_PASSWORD_TEXTBOX, text=password, sleep_time=1, AEFC="Failed to type on credentials page password textbox")
            self.gui.click(self.elements.CREDENTIALS_PAGE_OK_BUTTON, sleep_time=2,max_threshold=2, retry_frequency=1, AEFC=f"Failed to click on the ok button at credentials page")
            self.gui.click(self.elements.BANNER_ACCEPT_BUTTON, sleep_time=2,max_threshold=2, retry_frequency=1, AEFC=f"Failed to click on the accept button at banner page")
        except Exception as e:
            self.logger.error(f"Unable to connect to AnyConnect, Error: {e}")
            raise Exception(f"Unable to connect to AnyConnect, Error: {e}")

    def validate_connection(
        self,
        mode : str      # accepts connected or disconnected as inputs
    ):
        """
        Validate whether the vpn client is connected to disconnected
        """
        self.bring_to_focus()
        try:
            self.gui.click(self.elements.MENUBAR_ICON_BUTTON, sleep_time=1, retry_frequency=1, max_threshold=2, AEFC="Failed to click on menu bar anyconnect icon")
            self.gui.click(self.elements.MENUBAR_SHOW_STATISTICS_BUTTON, sleep_time=1, retry_frequency=1, max_threshold=2, AEFC="Failed to click on show statistics button")
            self.gui.click(self.elements.STATISTICS_PAGE_VPN_DETAILS_BUTTON, sleep_time=1, retry_frequency=1, max_threshold=2, AEFC="Failed to click on vpn details tab")
            if mode.lower()=='connected':
                try:
                    self.gui.click(self.elements.STATISTICS_PAGE_VPN_DETAILS_CONNECTED_LABEL, sleep_time=1, retry_frequency=1, max_threshold=2, AEFC="Failed to click on connected status label")
                except Exception as e:
                    self.logger.error(f"Issue with validating connected status: {e}")
                    raise Exception(f"Issue with validating connected status: {e}")
            elif mode.lower()=='disconnected':
                try:
                    self.gui.click(self.elements.STATISTICS_PAGE_VPN_DETAILS_DISCONNECTED_LABEL, sleep_time=1, retry_frequency=1, max_threshold=2, AEFC="Failed to click on disconnected status label")
                except Exception as e:
                    self.logger.error(f"Issue with validating disconnected status: {e}")
                    raise Exception(f"Issue with validating disconnected status: {e}")
            else:
                self.logger.error("Invalid mode given")
                raise Exception("Invalid mode given")
        except Exception as e:
            self.logger.error(f"Issue with validating anyconnect status: {e}")
            raise Exception(f"Issue with validating anyconnect status: {e}")

    def disconnect(
        self,
        sleep_time: int =5,                     # number of seconds to sleep after disconnect
        validate_connected_state: bool = True   # True for connected and False for disconnected
    ):
        """
        Disconnect the VPN connection, Do not quit
        """
        try:
            self.logger.info("Trying to disconnect anyconnect VPN")
            self.bring_to_focus()
            self.gui.click(self.elements.MENUBAR_ICON_BUTTON, sleep_time=1, retry_frequency=1, max_threshold=2, AEFC="Failed to click on menu bar anyconnect icon")
            self.gui.click(self.elements.MENUBAR_DISCONNECT_BUTTON,sleep_time=sleep_time,retry_frequency=1,max_threshold=2,AEFC="Unable to click on menubar disconnect button")
        except Exception as e:
            self.logger.error(f'Unable to disconnect anyconnect from menubar, Error: {e}')
            raise Exception(f'Unable to disconnect anyconnect from menubar, Error: {e}')
        else:
            self.logger.info("Successfully disconnected from anyconnect VPN Service")

    def quit_vpn(
        self,
    ):
        """
        Quit anyconnect vpn client by clicking on quit in the menubar
        """
        try:
            self.logger.info("Trying to quit anyconnect VPN")
            self.bring_to_focus()
            self.gui.click(self.elements.MENUBAR_ICON_BUTTON, sleep_time=1, retry_frequency=1, max_threshold=2, AEFC="Failed to click on menu bar anyconnect icon")
            self.gui.click(self.elements.MENUBAR_QUIT_BUTTON, sleep_time=1, retry_frequency=1, max_threshold=2, AEFC="Failed to click on menu bar quit anyconnect button")
        except Exception as e:
            self.logger.error(f"Issue with quitting anyconnect VPN, Error: {e}")
            raise Exception(f"Issue with quitting anyconnect VPN, Error: {e}")
        else:
            self.logger.info("Successfully quit anyconnect VPN")
        
class ElementsPulse :
    APP_SHORTCUT = r'open /Applications/Pulse\ Secure.app/'
    APP = '''/AXApplication[@AXTitle='Pulse Secure']/AXWindow[@AXTitle='Pulse Secure' and @AXIdentifier='_NS:163' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='_NS:9']/AXScrollArea[@AXIdentifier='_NS:16']/AXOutline[@AXIdentifier='_NS:20']/AXRow[@AXSubrole='AXOutlineRow']/AXGroup[0]/AXStaticText[@AXValue='Pulse Disconnected']'''
    CONNECT_BUTTON = "/AXApplication[@AXTitle='Pulse Secure']/AXWindow[@AXTitle='Pulse Secure' and @AXIdentifier='_NS:163' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='_NS:9']/AXScrollArea[@AXIdentifier='_NS:16']/AXOutline[@AXIdentifier='_NS:20']/AXRow[@AXSubrole='AXOutlineRow']/AXButton[@AXTitle='Connect']"
    CONNECTED_STATUS = '''/AXApplication[@AXTitle='Pulse Secure']/AXWindow[@AXTitle='Pulse Secure' and @AXIdentifier='_NS:163' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='_NS:9']/AXScrollArea[@AXIdentifier='_NS:16']/AXOutline[@AXIdentifier='_NS:20']/AXRow[@AXSubrole='AXOutlineRow']/AXGroup[0]/AXStaticText[@AXValue='Pulse Connected']'''
    DISCONNECTED_STATUS = '''/AXApplication[@AXTitle='Pulse Secure']/AXWindow[@AXTitle='Pulse Secure' and @AXIdentifier='_NS:163' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='_NS:9']/AXScrollArea[@AXIdentifier='_NS:16']/AXOutline[@AXIdentifier='_NS:20']/AXRow[@AXSubrole='AXOutlineRow']/AXGroup[0]/AXStaticText[@AXValue='
Pulse
Disconnected']'''
    DISCONNECT_BUTTON = "/AXApplication[@AXTitle='Pulse Secure']/AXWindow[@AXTitle='Pulse Secure' and @AXIdentifier='_NS:163' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='_NS:9']/AXScrollArea[@AXIdentifier='_NS:16']/AXOutline[@AXIdentifier='_NS:20']/AXRow[@AXSubrole='AXOutlineRow']/AXButton[@AXTitle='Disconnect']"
    FAILED_STATUS = '''/AXApplication[@AXTitle='Pulse Secure']/AXWindow[@AXTitle='Pulse Secure' and @AXIdentifier='_NS:163' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='_NS:9']/AXScrollArea[@AXIdentifier='_NS:16']/AXOutline[@AXIdentifier='_NS:20']/AXRow[@AXSubrole='AXOutlineRow']/AXGroup[0]/AXStaticText[@AXValue='
Pulse
Failed']'''
    CANCEL_BUTTON = "/AXApplication[@AXTitle='Pulse Secure']/AXWindow[@AXTitle='Pulse Secure' and @AXIdentifier='_NS:163' and @AXSubrole='AXStandardWindow']/AXGroup[@AXIdentifier='_NS:9']/AXScrollArea[@AXIdentifier='_NS:16']/AXOutline[@AXIdentifier='_NS:20']/AXRow[@AXSubrole='AXOutlineRow']/AXButton[@AXTitle='Cancel']"
    #VALIDATE_TERMINATE_SESSION = "/AXApplication[@AXTitle='Pulse Secure']/AXWindow[@AXTitle='Pulse' and @AXIdentifier='_NS:623' and @AXSubrole='AXStandardWindow']/AXSheet[@AXIdentifier='_NS:990']/AXStaticText[@AXValue='Sessions' and @AXIdentifier='_NS:1039']"
    #CONFIRM_TERMINATE_SESSION =  "/AXApplication[@AXTitle='Pulse Secure']/AXWindow[@AXTitle='Pulse' and @AXIdentifier='_NS:623' and @AXSubrole='AXStandardWindow']/AXSheet[@AXIdentifier='_NS:990']/AXButton[@AXTitle='Connect' and @AXIdentifier='_NS:994']"

class Pulse:
    def __init__(self,
                 handle="easy",
                 log_handle=None):
        self.logger = (log_handle if log_handle else Logger.initialize_logger("Pulse.log", log_level="DEBUG"))
        self.logger.info("Initialized Pulse Gui")
        self.operating_system = common_ui.operating_system
        self.gui = common_ui.Gui(handle=handle, log_handle=self.logger)
        self.elements = copy.deepcopy(ElementsPulse)
        self.do_element_check()
    
    # ---------------------------------------------------------------------------------------------
    def do_element_check(self):
        self.gui.start_app(app=self.elements.APP_SHORTCUT,sleep_time=0)
        try:
            self.gui.click(name=self.elements.APP,sleep_time=0,max_threshold=2,retry_frequency=1)
        except:
            self.elements.APP = self.elements.APP.replace('Disconnected','Disconnected - manual override')
            self.elements.DISCONNECTED_STATUS = self.elements.DISCONNECTED_STATUS.replace('Disconnected','Disconnected - manual override')
            self.elements.CONNECTED_STATUS = self.elements.CONNECTED_STATUS.replace('Connected','Connected - manual override')
            self.elements.FAILED_STATUS = self.elements.FAILED_STATUS.replace('Failed','Failed - manual override')
            self.logger.info(f"\n\n\n{'*'*50}\nElements updated - manual overide added\n{'*'*50}\n\n\n")
            self.gui.click(name=self.elements.APP,sleep_time=0,max_threshold=2,retry_frequency=1)
    
    # ---------------------------------------------------------------------------------------------
    def bring_to_focus(self,  # Function to bring Pulse to focus win/mac
                       number_of_trails=2):  # number of times to try to open Pulse before raising exception
        """
        Work Flow
        ----------
            1. start Pulse app, sleep for sometime
            2. try verifying Pulse is clickable for 'number_of_trails' times with 'max_threshold' time gap between tries
        """
        for i in range(number_of_trails):
            try:
                self.gui.start_app(app=self.elements.APP_SHORTCUT,sleep_time=0)
                self.logger.info("Success :: Pulse brought to Focus!")
                return
            except Exception as e:
                self.logger.error("Warning :: trying to bring Pulse to Focus again! ; {}\n".format(e))
        raise Exception("Error :: Failed to bring Pulse to Focus")

    # ---------------------------------------------------------------------------------------------
    def connect(self,
    mode=None,
    sleep_time=30
    ):
        if mode==None:
            raise Exception("No VPN Mode given, please give full or split as mode")
        try:  # validating if vpn is in disconnected state or not
            self.validate_connection('disconnected')
        except Exception as e:
            self.logger.error('Pulse not in disconnected state !!!!')
            self.disconnect()
        self.bring_to_focus()
        self.gui.click(name=self.elements.APP, AEFC="App not Found", max_threshold=20, sleep_time=0)
        self.gui.click(self.elements.CONNECT_BUTTON, AESC=" - connect button", AEFC="Connect button not Found",max_threshold=20, sleep_time=5)
        self.logger.info('Pressing shift+tab to select vpn mode')
        pyautogui.keyDown('shift')
        pyautogui.press('tab')
        pyautogui.keyUp('shift')
        time.sleep(3)
        if 'full' in mode:
            self.logger.info('Pressing enter to select vpn mode : full tunnel')
            pyautogui.press('enter')
            time.sleep(sleep_time)
        elif 'split' in mode:
            self.logger.info('Pressing down key to select vpn mode : split tunnel')
            pyautogui.press('down')
            time.sleep(1)
            self.logger.info('Pressing enter to select vpn mode : split tunnel')
            pyautogui.press('enter')
        else:
            raise Exception(f'Invalid VPN mode given : {mode}, give full or split')
            #if self.gui.click(self.elements.VALIDATE_TERMINATE_SESSION,max_threshold=2,expect_click_failure=True): self.gui.click(self.elements.CONFIRM_TERMINATE_SESSION,max_threshold=10)

    # ---------------------------------------------------------------------------------------------
    def validate_connection(self,
                            mode
                            ):
        try:
            self.bring_to_focus()
            if mode.lower() == 'connected':
                self.gui.click(name=self.elements.CONNECTED_STATUS, AEFC="Unable to get connection status",AESC="Clicked on VPN status - Connected", max_threshold=3, sleep_time=0,retry_frequency=1)
                self.logger.info("VPN Connected Validated")
            elif mode.lower() == 'disconnected':
                self.gui.click(name=self.elements.DISCONNECTED_STATUS, AEFC="Unable to get connection status",AESC="Clicked on VPN status - Disconnected", max_threshold=3, sleep_time=0,retry_frequency=1)
                self.logger.info("VPN Disconnected Validated")
        except Exception as e:
            self.logger.error(f"ERROR :: {e}")
            try:
                # now checking if vpn is in failed state, if yes then we click on cancel button and raise exception
                self.gui.click(name=self.elements.FAILED_STATUS,AEFC="Unable to get connection status for faield state",AESC="Clicked on VPN status - Failed status", max_threshold=3, sleep_time=0,retry_frequency=1)
                self.logger.error(f"VPN in failed state,clicking cancel")
                self.gui.click(self.elements.CANCEL_BUTTON, AEFC="Unable to click on cancel button for faield state",AESC="Clicked on cancel button - Failed status", max_threshold=3, sleep_time=0,retry_frequency=1)
                raise Exception("ERROR :: VPN was in failed state, clicked on cancel and will kill the VPN now")
            except Exception as e:
                self.kill()
                raise Exception(e)

    # ---------------------------------------------------------------------------------------------
    def disconnect(self,
    sleep_time=5,
    validate_connected_state = True
    ):
        try:
            if validate_connected_state: self.validate_connection('connected')
            else: self.logger.info("Skipping vpn connected state validation")
        except Exception as e:
            raise Exception('Pulse not in connected state, cannot disconnect')
        else:
            self.bring_to_focus()
            self.gui.click(self.elements.DISCONNECT_BUTTON, AEFC="Disconnect button not Found", max_threshold=20, sleep_time=sleep_time)
            self.logger.info("Successfully disconnected from VPN Service")
    
    # ------------------------------------------------------------------------------------------
    def kill(self):
        """
        Target: terminate the existing anyconnect instance
        Work flow
        ----------
            1. check if any nstance exists.
            2. if it doesnt, just return
            3. if it does, kill it and check no instance exists anymore
        """
        os.system('osascript -e "tell application \"Pulse\ Secure.app\" to quit"')

    def quit_vpn(self):
        pass
    # ------------------------------------------------------------------------------------------

class ElementsF5:
    APP_SHORTCUT = r'open /Applications/BIG-IP\ Edge\ Client.app'
    APP = "BIG-IP Edge Client"
    APP_BUNDLE = "com.f5networks.EdgeClient"
    APP_TRAY_ICON = "//XCUIElementTypeStatusItem"
    CONTINUE = '//XCUIElementTypeWindow/XCUIElementTypeGroup/XCUIElementTypeButton[2]'
    TURN_OFF_BUTTON = ('act_SwitchClientMode:', '//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[2]')
    TURN_ON_BUTTON = ('act_SwitchClientMode:', '//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[2]')
    MANAGE_VPN_SERVER_BUTTON = ('act_ShowManageServers:', '//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[6]')
    PREFERENCES_BUTTON = ('act_ShowPreferences:', '//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[8]')
    VIEW_DETAILS_BUTTON = ('act_ShowDetails:','//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[9]')
    ABOUT_BUTTON = ('act_ShowAbout:','//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[10]')
    QUIT_APP_BUTTON = ('act_Quit:', '//XCUIElementTypeStatusItem/XCUIElementTypeMenu/XCUIElementTypeMenuItem[12]')
    LOGIN_OK_BUTTON = ("Logon",'//XCUIElementTypeWebView[@label="3.21.12.45"]/XCUIElementTypeGroup[4]/XCUIElementTypeButton')
    DETAILS_STATUS_VPN_ON_LABEL = ('_NS:16', '//XCUIElementTypeStaticText[@value="VPN On"]')
    DETAILS_STATUS_VPN_OFF_LOGGED_IN_LABEL = ('_NS:16', '//XCUIElementTypeStaticText[@value="VPN Off, User Logged In"]')
    DETAILS_STATUS_VPN_CONNECTING_LABEL = ('_NS:16', '//XCUIElementTypeStaticText[@value="Connecting"]')
    DETAILS_STATUS_VPN_OFF_LABEL = ('_NS:16', '//XCUIElementTypeStaticText[@value="VPN Off"]')
    # USERNAME_FIELD =  '//XCUIElementTypeWebView/XCUIElementTypeTable/XCUIElementTypeTableColumn/XCUIElementTypeCell/XCUIElementTypeTable/XCUIElementTypeTableRow[3]/XCUIElementTypeCell/XCUIElementTypeTextField'
    USERNAME_FIELD = '//XCUIElementTypeTextField'
    PASSWORD_FIELD = '//XCUIElementTypeSecureTextField'
    SPLIT_TUNNEL = '//XCUIElementTypeStaticText[@value="Split Tunnel"]'
    FULL_TUNNEL = '//XCUIElementTypeStaticText[@value="Full Tunnel"]'

    CONNECT_BUTTON = ("Connect" ,'//XCUIElementTypeRadioButton[@value="0"])[2]')
    CLOSE_ERROR_BOX = ""
    CONNECTED_STATUS ='//XCUIElementTypeStaticText[@value="Connected"]'
    DISCONNECT_CONNECTION = ("Disconnect",'//XCUIElementTypeRadioButton[@value="1"]')
    DISCONNECTED_STATUS = '//XCUIElementTypeStaticText[@value="Disconnected"]'
    MAIN_WINDOW = ""

    
    CANCEL_BUTTON = '//XCUIElementTypeGroup/XCUIElementTypeButton[1]'
    LOGON_STATUS= '//XCUIElementTypeStaticText[@value="Secure Logon "]' 
    SECURITY_TAB = '''//XCUIElementTypeStaticText[@value="Server's identity is untrusted"]'''
    SECURITY_CONNECT = ("Continue",'//XCUIElementTypeGroup/XCUIElementTypeButton[2]')
    TUNNEL_SELECTION = '//XCUIElementTypeStaticText[@value="Please choose one of the following two options below."]'

class VpnF5:
    def __init__(self, 
    handle: str="easy", 
    log_handle: Logger=None):
        self.logger = (log_handle if log_handle else Logger.initialize_logger("F5.log", log_level="DEBUG"))
        self.logger.info("Initialized F5 Gui")
        self.count = 0
        self.operating_system= "Mac"
        self.gui = common_ui.Gui(handle=handle,log_handle=self.logger)
        self.elements = copy.deepcopy(ElementsF5)
    
    # ---------------------------------------------------------------------------------------------
    def bring_to_focus(self,    # Function to bring F5 to focus win/mac
    number_of_trails: int=2):            # number of times to try to open Pulse before raising exception
        """
        Work Flow
        ----------
            1. start F5 app, sleep for sometime
            2. try verifying Pulse is clickable for 'number_of_trails' times with 'max_threshold' time gap between tries
        """
        for i in range(number_of_trails):
            try:
                import subprocess
                subprocess.Popen(['open', '/Applications/BIG-IP Edge Client.app'])
                self.logger.info("Success :: F5 brought to Focus!")
                return
            except Exception as e:
                self.logger.error("Warning :: trying to bring F5 to Focus again! ; {}\n".format(e))
        raise Exception("Error :: Failed to bring F5 to Focus!")
    
    def connect(self,
    username: str=None,
    password: str=None,
    mode: str=None,
    sleep_time: int=30,
    validate_turned_off_state: bool=True
    ):  
        mode = mode.lower()
        if mode not in ['split','full']:
            self.logger.error(f"Invalid mode given - {mode} , please give either split or full")
        # self.bring_to_focus()

        try:
            if validate_turned_off_state: self.validate_connection('turned_off')
            else: self.logger.info("Skipping vpn turned off state validation")
        except Exception as e:
            self.logger.error(f'F5 not in turned off state, cannot connect - Error :: {e}')
            raise Exception(f'F5 not in turned off state, cannot connect - Error :: {e}')
        
        try:
            try:
                self.gui.click(self.elements.CONTINUE, AESC=" - Continue button",AEFC="Continue button not Found", max_threshold=2, sleep_time=5)
            except:
                self.logger.info("Continue button is not present, Proceeding ahead to check if Username & Password fields are available")
            else:
                time.sleep(10)
                self.gui.type(path=self.elements.USERNAME_FIELD, text=username, sleep_time=1)
                self.gui.type(path=self.elements.PASSWORD_FIELD, text=password, sleep_time=1) 
                self.gui.click(path=self.elements.LOGIN_OK_BUTTON, sleep_time=5,max_threshold=3, retry_frequency=2, AEFC="Login OK button not found")
            if mode=="split":
                self.gui.click(path=self.elements.SPLIT_TUNNEL, sleep_time=30,max_threshold=3, retry_frequency=2, AEFC="Split tunnel button not found")
            elif mode=="full":
                self.gui.click(path=self.elements.FULL_TUNNEL, sleep_time=30,max_threshold=3, retry_frequency=2, AEFC="Full tunnel button not found")
            # self.bring_to_focus()
            
        except Exception as e:
            self.logger.error(f"Unable to connect to F5 VPN :: {e}")
            raise Exception(f"Unable to connect to F5 VPN :: {e}")

    def validate_connection(self,
    mode: str # 'turned_off_user_logged_in' - validate VPN is in logged in state but turned off, 'connected' - validate vpn is in VPN on state, 'disconnected' - validate vpn is quit, 'turned_off' - validate vpn is off)
    ): 
        try:
            # self.bring_to_focus()
            if mode.lower()=='connected':
                self.gui.click(self.elements.APP_TRAY_ICON,AEFC="Unable to click F5 App tray icon in menu bar",AESC="Clicked on app tray icon",max_threshold=3, sleep_time=0,retry_frequency=1)
                self.gui.click(self.elements.VIEW_DETAILS_BUTTON,AEFC="Unable to click View Details in drop down menu",AESC="Clicked on View Details button",max_threshold=3, sleep_time=0,retry_frequency=1)
                self.gui.click(self.elements.DETAILS_STATUS_VPN_ON_LABEL[1],AEFC="Unable to click on VPN On Label",AESC="Clicked on VPN status - VPN On",max_threshold=3, sleep_time=0,retry_frequency=1)
                self.logger.info("VPN Connection Validated")

            elif mode.lower()=='disconnected':
                # Confirm the tray icon does not exist.
                try:
                    self.gui.click(self.elements.APP_TRAY_ICON,AEFC="Unable to click F5 App tray icon in menu bar",AESC="Clicked on app tray icon",max_threshold=3, sleep_time=0,retry_frequency=1)
                except:
                    self.logger.info("VPN Disconnection Validated")
            
            elif mode.lower()=="turned_off_user_logged_in":
                self.gui.click(self.elements.APP_TRAY_ICON,AEFC="Unable to click F5 App tray icon in menu bar",AESC="Clicked on app tray icon",max_threshold=3, sleep_time=0,retry_frequency=1)
                self.gui.click(self.elements.VIEW_DETAILS_BUTTON,AEFC="Unable to click View Details in drop down menu",AESC="Clicked on View Details button",max_threshold=3, sleep_time=0,retry_frequency=1)
                self.gui.click(self.elements.DETAILS_STATUS_VPN_OFF_LOGGED_IN_LABEL[1],AEFC="Unable to click on VPN Off, User Logged In Label",AESC="Clicked on VPN status - VPN Off, User Logged In",max_threshold=3, sleep_time=0,retry_frequency=1)
                self.logger.info("VPN turned off, user logged in validated")

            elif mode.lower()=="turned_off":
                self.gui.click(self.elements.APP_TRAY_ICON,AEFC="Unable to click F5 App tray icon in menu bar",AESC="Clicked on app tray icon",max_threshold=3, sleep_time=0,retry_frequency=1)
                self.gui.click(self.elements.VIEW_DETAILS_BUTTON,AEFC="Unable to click View Details in drop down menu",AESC="Clicked on View Details button",max_threshold=3, sleep_time=0,retry_frequency=1)
                self.gui.click(self.elements.DETAILS_STATUS_VPN_OFF_LABEL[1],AEFC="Unable to click on VPN Off Label",AESC="Clicked on VPN status - VPN Off",max_threshold=3, sleep_time=0,retry_frequency=1)
                self.logger.info("VPN Off state validated")


        except Exception as e:
            self.logger.error(f"ERROR :: {e}")
            raise Exception(f"Connection validation failed - Mode :: {mode},  Error :: {e}")

    def disconnect(self,
    sleep_time: int=5,
    validate_connected_state : bool=True
    ): 
        try:
            if validate_connected_state: self.validate_connection('connected')
            else: self.logger.info("Skipping vpn connected state validation")
        except Exception as e:
            self.logger.error(f'F5 not in connected state, cannot disconnect - Error :: {e}')
            raise Exception(f'F5 not in connected state, cannot disconnect - Error :: {e}')
        else:
            try:
                # self.bring_to_focus()
                self.gui.click(self.elements.APP_TRAY_ICON,AEFC="Unable to click F5 App tray icon in menu bar",AESC="Clicked on app tray icon",max_threshold=3, sleep_time=0,retry_frequency=1)
                self.gui.click(self.elements.TURN_OFF_BUTTON,AEFC="Unable to click on Turn VPN Off button",AESC="Clicked on Turn VPN Off button",max_threshold=3, sleep_time=0,retry_frequency=1)
            except Exception as e:
                self.logger.error(f'Unable to turn off F5 VPN :: {e}')
                raise Exception(f'Unable to turn off F5 VPN :: {e}')
    
    def quit_vpn(self):
        try:
            # self.bring_to_focus()
            self.gui.click(self.elements.APP_TRAY_ICON,AEFC="Unable to click F5 App tray icon in menu bar",AESC="Clicked on app tray icon",max_threshold=3, sleep_time=0,retry_frequency=1)
            self.gui.click(self.elements.QUIT_APP_BUTTON,AEFC="Unable to click on Quit BIG-IP Client button",AESC="Clicked on Quit BIG-IP Client button",max_threshold=3, sleep_time=0,retry_frequency=1)
                
        except Exception as e:
            self.logger.error(f"Unable to quit the VPN Client application - Please check manually ! Error - {e}")
            raise Exception(f"Unable to quit the VPN Client application - Please check manually ! Error - {e}")
        else:    
            # Confirm the tray icon does not exist.
            try:
                self.gui.click(self.elements.APP_TRAY_ICON,AEFC="Unable to click F5 App tray icon in menu bar",AESC="Clicked on app tray icon",max_threshold=3, sleep_time=0,retry_frequency=1)
            except:
                self.logger.info("VPN Disconnection Validated")

    def turn_on_vpn(self):
        try:
            # self.bring_to_focus()
            self.logger.info("Trying to turn on VPN ...")
            self.gui.click(self.elements.APP_TRAY_ICON,AEFC="Unable to click F5 App tray icon in menu bar",AESC="Clicked on app tray icon",max_threshold=3, sleep_time=0,retry_frequency=1)
            self.gui.click(self.elements.TURN_ON_BUTTON,AEFC="Unable to click on Turn VPN On button",AESC="Clicked on Turn VPN On button",max_threshold=3, sleep_time=0,retry_frequency=1)
        except Exception as e:
            self.logger.error(f"Unable to turn on VPN :: {e}")
            raise Exception(f"Unable to turn on VPN :: {e}")
        else:
            self.validate_connection("connected")

    def turn_off_vpn(self):
        try:
            # self.bring_to_focus()
            self.logger.info("Trying to turn OFF VPN ...")
            self.gui.click(self.elements.APP_TRAY_ICON,AEFC="Unable to click F5 App tray icon in menu bar",AESC="Clicked on app tray icon",max_threshold=3, sleep_time=0,retry_frequency=1)
            self.gui.click(self.elements.TURN_OFF_BUTTON,AEFC="Unable to click on Turn VPN Off button",AESC="Clicked on Turn VPN Off button",max_threshold=3, sleep_time=0,retry_frequency=1)
        except Exception as e:
            self.logger.error(f"Unable to turn off VPN :: {e}")
            raise Exception(f"Unable to turn off VPN :: {e}")
        else:
            self.validate_connection("turned_off_user_logged_in")

class ElementsGlobalProtect:
    ICON_MENUBAR_BUTTON = "//XCUIElementTypeStatusItem"
    

class GlobalProtect:
    def __init__(
        self, 
        handle: str="easy", 
        log_handle: Logger=None
    ):
        pass
import time, base64, requests, subprocess, pyautogui
from common_lib.common.logger import Logger
from OS_Mac.library import ops_system


class ZoomOps:
    def __init__(self, cloud, log_handle=None):
        self.logger = (log_handle if log_handle != None else Logger.initialize_logger("App_Bypass.log", log_level="DEBUG"))
        self.sys_ops = ops_system.SysOps(cloud=cloud, log_handle=self.logger)

    def start_zoom_meeting(self):
        '''
        Creates a Zoom meeting using Zoom API and join the Meeting using Zoom 
        Refer to readme.md for details of setup
        '''
        account_id = "LPDRyVZ8TRiHfUHYdmmD8g"
        client_id = "g4nrUSojTJSdJNNGxbEXDA"
        client_secret = "h4sXxqDxz5ZQhYbUOgefGTbNj6DuRai6"

        token_url = "https://zoom.us/oauth/token"
        create_meeting_url = "https://api.zoom.us/v2/users/me/meetings"

        auth_str = f"{client_id}:{client_secret}"
        auth_bytes = auth_str.encode('utf-8')
        auth_base64 = base64.b64encode(auth_bytes).decode('utf-8')

        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Authorization": f"Basic {auth_base64}"
        }
        data = {
            "grant_type": "account_credentials",
            "account_id": account_id
        }

        try:
            response = requests.post(token_url, headers=headers, data=data, verify=False)
        except Exception as e:
            self.logger.error(f"Error :: While creating Zoom API Access token : {e}")
            return False, f'Error :: While creating Zoom API Access token : {e}', e
        else:
            self.logger.info(f"Zoom API Access Token Generated: {response.text}")

        token = response.json()["access_token"]

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        body = {
            "topic": "Automation Meeting",
            "type": 2,
            "waiting_room": False,
            "duration": 30,
            "settings": 
                { "join_before_host": True }
        }
        
        try:
            response = requests.post(create_meeting_url, headers=headers, json=body, verify=False)
        except Exception as e:
            self.logger.error(f'Error :: While creating Zoom Meeting : {e}')
            return False, f'Error :: While creating Zoom Meeting : {e}', e
        else:
            self.logger.info(f"Zoom Meeting Created: {response.text}")

        join_url = response.json()["join_url"]

        self.logger.info(f"Launching Zoom Client")

        # Launching Zoom Meeting from different terminal
        cmd = f"open -a zoom.us '{join_url}'"
        subprocess.Popen(['sh','OS_Mac/resource/launch_zoom_client.sh',f'{join_url}'])
        # subprocess.run(['osascript', '-e', f'tell application "Terminal" to do script "{cmd}"'])

        time.sleep(15)

        pyautogui.typewrite('automation_participant')
        pyautogui.press('enter', presses=2,  interval=10)

        self.logger.info('Zoom Meeting Created and Joined Successfully')
        return True, f'Zoom Meeting Created and Joined Successfully', None
    

    def end_zoom_meeting(self):
        '''
        Closes Zoom Meeting by exiting Zoom Client
        '''

        return self.sys_ops.kill_process_mac(process_name='zoom.us')
        